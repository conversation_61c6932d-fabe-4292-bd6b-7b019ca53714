## 项目名称

uta_engine

## BUILD && INSTALL

0. 保证系统中有如下库  
`gcc8.5以上(linux要求, 其他平台请自行保证编译器支持c++20)`  
`安装cmake(3.17版本及以上)`  
`openssl-devel glibc-headers autoconf(v2.72e for linux) autoconf-archive automake libtool bison gcovr m4`  
`cppcheck cpplink clang-format(18.1.5)`  
`pkgconf(2.3.0 for linux https://github.com/pkgconf/pkgconf)`  

1. 编译  
* run_cmake.sh方案  
  `brew install jq`  
  `brew install gnu-getopt`(**for mac os, 需要确保使用的是gnu-getopt而不是系统自带的getopt, 可以通过`which getopt`检查实际使用的getopt**)  
  `./run_cmake.sh -r -j8`(**第一次运行时会自动安装vcpkg依赖, 并编译本项目, 花的时间比较长, 请耐心等待**)  
* cmake presets命令行方案   
     `cmake --list-presets=all .`  
     `cmake --preset MD`  
     `cmake --build --preset MD`

## 本地跑UT
0. 设置动态库加载的路径:
  * mac: export DYLD_LIBRARY_PATH="${PATH_TO_YOUR_VCPKG_LIB}"
  * linux: export LD_LIBRARY_PATH="${PATH_TO_YOUR_VCPKG_LIB}"
  * example for **debug**: export DYLD_LIBRARY_PATH="/Users/<USER>/Git/engine/trading/uta_engine/deps/installed/arm64-osx/**debug**/lib:/Users/<USER>/Git/engine/trading/uta_engine/lib"
  * example for **release**: export DYLD_LIBRARY_PATH="/Users/<USER>/Git/engine/trading/uta_engine/deps/installed/arm64-osx/lib:/Users/<USER>/Git/engine/trading/uta_engine/lib"
1. 安装etcd和kafka, 并按默认配置启动这两个服务
2. 配置etcd和配置环境变量
```bash
etcdctl put /config/kafka_456_server/broker/0 127.0.0.1:9092
etcdctl put /config/kafka_abc_server/broker/0 127.0.0.1:9092
etcdctl put /config/kafka_server/broker/0 127.0.0.1:9092
etcdctl put /config/kafka_t123_server/broker/0 127.0.0.1:9092
etcdctl put /config/kafka_test_ut/broker/0 127.0.0.1:x
etcdctl put /config/kafka_test_ut/broker/1 127.0.0.1:y
etcdctl put /config/kafka_test_ut/broker/2 127.0.0.1:z
```

# 集成测试
详情见[README.md](test/biz/trade/README.md)
