diff --git a/util/misc/uuid.cc b/util/misc/uuid.cc
index 3013d7b..4cc135f 100644
--- a/util/misc/uuid.cc
+++ b/util/misc/uuid.cc
@@ -41,7 +41,8 @@
 namespace crashpad {

 static_assert(sizeof(UUID) == 16, "UUID must be 16 bytes");
-static_assert(std::is_pod<UUID>::value, "UUID must be POD");
+static_assert(std::is_standard_layout<UUID>::value, "UUID must be a standard-layout type");
+static_assert(std::is_trivial<UUID>::value, "UUID must be a trivial type");

 bool UUID::operator==(const UUID& that) const {
   return memcmp(this, &that, sizeof(*this)) == 0;
