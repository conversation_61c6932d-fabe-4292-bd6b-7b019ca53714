
get_filename_component(_d3dx_root "${CMAKE_CURRENT_LIST_DIR}" PATH)
get_filename_component(_d3dx_root "${_d3dx_root}" PATH)

set(_d3dx_root_lib "${_d3dx_root}/lib/d3dx9.lib")
if (EXISTS "${_d3dx_root_lib}")

   add_library(Microsoft::D3DX9 SHARED IMPORTED)
   set_target_properties(Microsoft::D3DX9 PROPERTIES
      IMPORTED_LOCATION_RELEASE            "${_d3dx_root}/bin/D3DX9_43.dll"
      IMPORTED_IMPLIB_RELEASE              "${_d3dx_root_lib}"
      IMPORTED_LOCATION_DEBUG              "${_d3dx_root}/debug/bin/D3DX9d_43.dll"
      IMPORTED_IMPLIB_DEBUG                "${_d3dx_root}/debug/lib/d3dx9d.lib"
      INTERFACE_INCLUDE_DIRECTORIES        "${_d3dx_root}/include/dxsdk-d3dx"
      IMPORTED_CONFIGURATIONS              "Debug;Release"
      IMPORTED_LINK_INTERFACE_LANGUAGES    "C")

   add_library(Microsoft::D3DX10 SHARED IMPORTED)
   set_target_properties(Microsoft::D3DX10 PROPERTIES
      IMPORTED_LOCATION_RELEASE            "${_d3dx_root}/bin/d3dx10_43.dll"
      IMPORTED_IMPLIB_RELEASE              "${_d3dx_root}/lib/d3dx10.lib"
      IMPORTED_LOCATION_DEBUG              "${_d3dx_root}/debug/bin/D3DX10d_43.dll"
      IMPORTED_IMPLIB_DEBUG                "${_d3dx_root}/debug/lib/d3dx10d.lib"
      INTERFACE_INCLUDE_DIRECTORIES        "${_d3dx_root}/include/dxsdk-d3dx"
      IMPORTED_CONFIGURATIONS              "Debug;Release"
      IMPORTED_LINK_INTERFACE_LANGUAGES    "C")

   add_library(Microsoft::D3DX11 SHARED IMPORTED)
   set_target_properties(Microsoft::D3DX11 PROPERTIES
      IMPORTED_LOCATION_RELEASE            "${_d3dx_root}/bin/d3dx11_43.dll"
      IMPORTED_IMPLIB_RELEASE              "${_d3dx_root}/lib/d3dx11.lib"
      IMPORTED_LOCATION_DEBUG              "${_d3dx_root}/debug/bin/D3DX11d_43.dll"
      IMPORTED_IMPLIB_DEBUG                "${_d3dx_root}/debug/lib/d3dx11d.lib"
      INTERFACE_INCLUDE_DIRECTORIES        "${_d3dx_root}/include/dxsdk-d3dx"
      IMPORTED_CONFIGURATIONS              "Debug;Release"
      IMPORTED_LINK_INTERFACE_LANGUAGES    "C")

    set(dxsdk-d3dx_FOUND TRUE)

else()

    set(dxsdk-d3dx_FOUND FALSE)

endif()

unset(_d3dx_root_lib)
unset(_d3dx_root)
