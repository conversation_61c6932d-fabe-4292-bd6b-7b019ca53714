diff --git a/c/blake3_dispatch.c b/c/blake3_dispatch.c
index af6c3da..dce85b4 100644
--- a/c/blake3_dispatch.c
+++ b/c/blake3_dispatch.c
@@ -31,7 +31,7 @@
 #define ATOMIC_INT _Atomic int
 #define ATOMIC_LOAD(x) x
 #define ATOMIC_STORE(x, y) x = y
-#elif defined(_MSC_VER)
+#elif defined(IS_X86) && defined(_MSC_VER)
 #define ATOMIC_INT LONG
 #define ATOMIC_LOAD(x) InterlockedOr(&x, 0)
 #define ATOMIC_STORE(x, y) InterlockedExchange(&x, y)
