diff --git a/Foundation/CMakeLists.txt b/Foundation/CMakeLists.txt
index f504bc1..226dadb 100644
--- a/Foundation/CMakeLists.txt
+++ b/Foundation/CMakeLists.txt
@@ -109,6 +109,23 @@ set_target_properties(Foundation
 if(POCO_UNBUNDLED)
 	target_link_libraries(Foundation PUBLIC Pcre2::Pcre2 ZLIB::ZLIB)
 	target_compile_definitions(Foundation PUBLIC POCO_UNBUNDLED)
+	add_definitions(
+		-D_pcre2_utf8_table1=_poco_pcre2_utf8_table1
+		-D_pcre2_utf8_table1_size=_poco_pcre2_utf8_table1_size
+		-D_pcre2_utf8_table2=_poco_pcre2_utf8_table2
+		-D_pcre2_utf8_table3=_poco_pcre2_utf8_table3
+		-D_pcre2_utf8_table4=_poco_pcre2_utf8_table4
+		-D_pcre2_OP_lengths_8=_poco_pcre2_OP_lengths_8
+		-D_pcre2_callout_end_delims_8=_poco_pcre2_callout_end_delims_8
+		-D_pcre2_callout_start_delims_8=_poco_pcre2_callout_start_delims_8
+		-D_pcre2_hspace_list_8=_poco_pcre2_hspace_list_8
+		-D_pcre2_vspace_list_8=_poco_pcre2_vspace_list_8
+		-D_pcre2_ucp_gbtable_8=_poco_pcre2_ucp_gbtable_8
+		-D_pcre2_ucp_gentype_8=_poco_pcre2_ucp_gentype_8
+		-D_pcre2_utt_8=_poco_pcre2_utt_8
+		-D_pcre2_utt_names_8=_poco_pcre2_utt_names_8
+		-D_pcre2_utt_size_8=_poco_pcre2_utt_size_8
+	)
 endif(POCO_UNBUNDLED)
 
 target_include_directories(Foundation
