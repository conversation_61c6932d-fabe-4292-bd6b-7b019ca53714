diff --git a/CMakeLists.txt b/CMakeLists.txt
index 9c1b150..1adbfed 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -105,10 +105,10 @@ add_definitions(-DTHEIA_DATA_DIR="${CMAKE_SOURCE_DIR}/data")
 
 # Eigen
 set(MIN_EIGEN_VERSION 3.2.0)
-find_package(Eigen ${MIN_EIGEN_VERSION} REQUIRED)
-if (EIGEN_FOUND)
-  message("-- Found Eigen version ${EIGEN_VERSION}: ${EIGEN_INCLUDE_DIRS}")
-endif (EIGEN_FOUND)
+find_package(Eigen3 ${MIN_EIGEN_VERSION} REQUIRED)
+if (EIGEN3_FOUND)
+  message("-- Found Eigen version ${EIGEN3_VERSION}: ${EIGEN3_INCLUDE_DIRS}")
+endif (EIGEN3_FOUND)
 
 # Use a larger inlining threshold for Clang, since it hobbles Eigen,
 # resulting in an unreasonably slow version of the blas routines. The
@@ -129,70 +129,27 @@ endif ()
 
 # GFlags. The namespace patch is borrow from Ceres Solver (see license in
 # FindGflags.cmake)
+message("-- Check for Google Flags")
 find_package(Gflags REQUIRED)
-if (GFLAGS_FOUND)
-  message("-- Found Google Flags: ${GFLAGS_INCLUDE_DIRS} in namespace: ${GFLAGS_NAMESPACE}")
-  add_definitions(-DTHEIA_GFLAGS_NAMESPACE=${GFLAGS_NAMESPACE})
-else (GFLAGS_FOUND)
-  message(FATAL_ERROR "Can't find Google FLAGS.")
-endif (GFLAGS_FOUND)
 
 # Google Logging
 message("-- Check for Google Log")
 find_package(Glog REQUIRED)
-if (GLOG_FOUND)
-  message("-- Found Google Logging: ${GLOG_INCLUDE_DIRS}")
-else (GLOG_FOUND)
-  message(FATAL_ERROR "Can't find Google Logging. Please set GLOG_INCLUDE_DIR & "
-    "GLOG_LIBRARY")
-endif (GLOG_FOUND)
+
+# Flann
+message("-- Check for Flann")
+find_package(Flann REQUIRED)
 
 # Ceres
 message("-- Check for Ceres")
 find_package(Ceres REQUIRED SuiteSparse)
-if (CERES_FOUND)
-  message("-- Found Ceres: ${CERES_INCLUDE_DIRS}")
-else (CERES_FOUND)
-  message(FATAL_ERROR "Can't find Ceres. Please set CERES_INCLUDE_DIR & "
-    "CERES_LIBRARY")
-endif (CERES_FOUND)
 
 # OpenImageIO
 message("-- Check for OpenImageIO")
 find_package(OpenImageIO REQUIRED)
-if (OPENIMAGEIO_FOUND)
-  message("-- Found OpenImageIO: ${OPENIMAGEIO_INCLUDE_DIRS}")
-else (OPENIMAGEIO_FOUND)
-  message(FATAL_ERROR "Can't find OpenImageIO. Please set OPENIMAGEIO_INCLUDE_DIR & "
-    "OPENIMAGEIO_LIBRARY")
-endif (OPENIMAGEIO_FOUND)
 
 # Suitesparse
 find_package(SuiteSparse REQUIRED)
-if (SUITESPARSE_FOUND)
-  # On Ubuntu the system install of SuiteSparse (v3.4.0) up to at least
-  # Ubuntu 13.10 cannot be used to link shared libraries.
-  if (BUILD_SHARED_LIBS AND
-      SUITESPARSE_IS_BROKEN_SHARED_LINKING_UBUNTU_SYSTEM_VERSION)
-    message(FATAL_ERROR "You are attempting to build Theia as a shared "
-      "library on Ubuntu using a system package install of SuiteSparse "
-      "3.4.0. This package is broken and does not support the "
-      "construction of shared libraries (you can still build Theia as "
-      "a static library).  If you wish to build a shared version of Theia "
-      "you should uninstall the system install of SuiteSparse "
-      "(libsuitesparse-dev) and perform a source install of SuiteSparse "
-      "(we recommend that you use the latest version), "
-      "see http://theia-solver.org/building.html for more information.")
-  endif (BUILD_SHARED_LIBS AND
-    SUITESPARSE_IS_BROKEN_SHARED_LINKING_UBUNTU_SYSTEM_VERSION)
-  message("-- Found SuiteSparse ${SUITESPARSE_VERSION}")
-  add_definitions(-DTHEIA_SUITESPARSE_VERSION="${SUITESPARSE_VERSION}")
-else (SUITESPARSE_FOUND)
-  # Disable use of SuiteSparse if it cannot be found and continue.
-  message(FATAL ERROR "Can't find SuiteSparse. This library is required "
-    "for bundle adjustment and for solving convex optimization problems. "
-    "Please set SUITESPARSE_INCLUDE_DIR & SUITESPARSE_LIBRARY")
-endif (SUITESPARSE_FOUND)
 
 include_directories(
   include
@@ -225,24 +182,19 @@ include_directories(
   libraries/spectra
   ${akaze_SOURCE_DIR}
   ${akaze_INCLUDE_DIR}
-  ${cereal_SOURCE_DIR}
-  ${cereal_SOURCE_DIR}/include
-  ${flann_SOURCE_DIR}
-  ${flann_SOURCE_DIR}/src/cpp
-  ${gtest_SOURCE_DIR}/include
-  ${gtest_SOURCE_DIR}
   ${optimo_SOURCE_DIR}
   ${statx_SOURCE_DIR}
   ${stlplus3_SOURCE_DIR}
   ${vlfeat_SOURCE_DIR}
-  ${visual_sfm_SOURCE_DIR})
+  ${visual_sfm_SOURCE_DIR}
+)
 
 list(APPEND CMAKE_MODULE_PATH "${CMAKE_SOURCE_DIR}/cmake")
 include(OptimizeTheiaCompilerFlags)
 optimizetheiacompilerflags()
 
 add_subdirectory(src/theia)
-add_subdirectory(applications)
+#add_subdirectory(applications)
 
 if (BUILD_DOCUMENTATION)
   message("-- Documentation building is enabled")
@@ -260,7 +212,13 @@ install(FILES ${THEIA_HDRS} DESTINATION include/theia)
 file(GLOB_RECURSE THEIA_INTERNAL_HDRS ${CMAKE_SOURCE_DIR}/src/theia/*.h)
 install(DIRECTORY src/theia/ DESTINATION include/theia FILES_MATCHING PATTERN "*.h")
 
-install(DIRECTORY libraries/ DESTINATION include/theia/libraries FILES_MATCHING PATTERN "*.h*")
+install(DIRECTORY libraries/akaze/ DESTINATION include/theia/libraries/akaze FILES_MATCHING PATTERN "*.h*")
+install(DIRECTORY libraries/optimo/ DESTINATION include/theia/libraries/optimo FILES_MATCHING PATTERN "*.h*")
+install(DIRECTORY libraries/spectra/ DESTINATION include/theia/libraries/spectra FILES_MATCHING PATTERN "*.h*")
+install(DIRECTORY libraries/statx/ DESTINATION include/theia/libraries/statx FILES_MATCHING PATTERN "*.h*")
+install(DIRECTORY libraries/stlplus3/ DESTINATION include/theia/libraries/stlplus3 FILES_MATCHING PATTERN "*.h*")
+install(DIRECTORY libraries/visual_sfm/ DESTINATION include/theia/libraries/visual_sfm FILES_MATCHING PATTERN "*.h*")
+install(DIRECTORY libraries/vlfeat/ DESTINATION include/theia/libraries/vlfeat FILES_MATCHING PATTERN "*.h*")
 
 # Add an uninstall target to remove all installed files.
 configure_file("${CMAKE_SOURCE_DIR}/cmake/uninstall.cmake.in"
@@ -272,17 +230,10 @@ add_custom_target(uninstall
 
 # Set up install directories. INCLUDE_INSTALL_DIR, LIB_INSTALL_DIR and
 # CMAKECONFIG_INSTALL_DIR must not be absolute paths.
-if (WIN32)
-  set(INCLUDE_INSTALL_DIR Include)
-  set(LIB_INSTALL_DIR Lib)
-  set(CMAKECONFIG_INSTALL_DIR CMake)
-  set(RELATIVE_CMAKECONFIG_INSTALL_DIR CMake)
-else ()
-  set(INCLUDE_INSTALL_DIR include)
-  set(LIB_INSTALL_DIR lib)
-  set(CMAKECONFIG_INSTALL_DIR share/Theia)
-  set(RELATIVE_CMAKECONFIG_INSTALL_DIR share/Theia)
-endif ()
+set(INCLUDE_INSTALL_DIR include)
+set(LIB_INSTALL_DIR lib)
+set(CMAKECONFIG_INSTALL_DIR share/theia)
+set(RELATIVE_CMAKECONFIG_INSTALL_DIR share/theia)
 
 # This "exports" all targets which have been put into the export set
 # "TheiaExport". This means that CMake generates a file with the given
@@ -319,9 +270,4 @@ configure_file("${CMAKE_SOURCE_DIR}/cmake/TheiaConfigVersion.cmake.in"
 # in the public API of Theia and should thus be present in THEIA_INCLUDE_DIRS.
 install(FILES "${CMAKE_CURRENT_BINARY_DIR}/TheiaConfig.cmake"
               "${CMAKE_CURRENT_BINARY_DIR}/TheiaConfigVersion.cmake"
-              "${CMAKE_SOURCE_DIR}/cmake/FindEigen.cmake"
-              "${CMAKE_SOURCE_DIR}/cmake/FindGlog.cmake"
-              "${CMAKE_SOURCE_DIR}/cmake/FindGflags.cmake"
-	      "${CMAKE_SOURCE_DIR}/cmake/FindOpenImageIO.cmake"
-              "${CMAKE_SOURCE_DIR}/cmake/FindSuiteSparse.cmake"
               DESTINATION ${CMAKECONFIG_INSTALL_DIR})
diff --git a/cmake/TheiaConfig.cmake.in b/cmake/TheiaConfig.cmake.in
index a3ea187..7e9d912 100644
--- a/cmake/TheiaConfig.cmake.in
+++ b/cmake/TheiaConfig.cmake.in
@@ -124,14 +124,7 @@ list(APPEND CERES_INCLUDE_DIR_HINTS @CERES_INCLUDE_DIR@)
 get_filename_component(THEIA_BUILD_CERES_LIBRARY_DIR @CERES_LIBRARIES@ PATH)
 list(APPEND CERES_LIBRARY_DIR_HINTS ${THEIA_BUILD_CERES_LIBRARY_DIR})
 # Search quietly s/t we control the timing of the error message if not found.
-find_package(Ceres QUIET)
-if (CERES_FOUND)
-  message(STATUS "Found required Theia dependency: "
-    "Ceres in ${CERES_INCLUDE_DIRS}")
-else (CERES_FOUND)
-  theia_report_not_found("Missing required Theia "
-    "dependency: Ceres, please set CERES_INCLUDE_DIR.")
-endif (CERES_FOUND)
+find_package(Ceres REQUIRED)
 list(APPEND THEIA_INCLUDE_DIRS ${CERES_INCLUDE_DIRS})
 
 # Glog.
@@ -140,64 +133,19 @@ list(APPEND GLOG_INCLUDE_DIR_HINTS @GLOG_INCLUDE_DIR@)
 get_filename_component(THEIA_BUILD_GLOG_LIBRARY_DIR @GLOG_LIBRARY@ PATH)
 list(APPEND GLOG_LIBRARY_DIR_HINTS ${THEIA_BUILD_GLOG_LIBRARY_DIR})
 # Search quietly s/t we control the timing of the error message if not found.
-find_package(Glog QUIET)
-if (GLOG_FOUND)
-  message(STATUS "Found required Theia dependency: "
-    "Glog in ${GLOG_INCLUDE_DIRS}")
-else (GLOG_FOUND)
-  theia_report_not_found("Missing required Theia "
-    "dependency: Glog, please set GLOG_INCLUDE_DIR.")
-endif (GLOG_FOUND)
+find_package(Glog REQUIRED)
 list(APPEND THEIA_INCLUDE_DIRS ${GLOG_INCLUDE_DIRS})
 
 # GFlags. The namespace patch is borrow from Ceres Solver (see license in
 # FindGflags.cmake)
-find_package(Gflags QUIET)
-if (GFLAGS_FOUND)
-  message(STATUS "Found required Theia dependency: Google Flags in "
-    "${GFLAGS_INCLUDE_DIRS} in namespace: ${GFLAGS_NAMESPACE}")
-  add_definitions(-DTHEIA_GFLAGS_NAMESPACE=${GFLAGS_NAMESPACE})
-else (GFLAGS_FOUND)
-  theia_report_not_found("Missing required Theia dependency: Google Flags, please set "
-    "GFLAGS_INCLUDE_DIR.")
-endif (GFLAGS_FOUND)
+find_package(Gflags REQUIRED)
 list(APPEND THEIA_INCLUDE_DIRS ${GFLAGS_INCLUDE_DIRS})
 
 # OpenImageIO
-find_package(OpenImageIO QUIET)
-if (OPENIMAGEIO_FOUND)
-  message(STATUS "Found Theia dependency: OpenImageIO in ${OPENIMAGEIO_INCLUDE_DIRS}")
-else (OPENIMAGEIO_FOUND)
-  theia_report_not_found("Missing required Theia dependency: OpenImageIO. Please set "
-    "OPENIMAGEIO_INCLUDE_DIR & OPENIMAGEIO_LIBRARY")
-endif (OPENIMAGEIO_FOUND)
+find_package(OpenImageIO REQUIRED)
 list(APPEND THEIA_INCLUDE_DIRS ${OPENIMAGEIO_INCLUDE_DIRS})
 
-find_package(SuiteSparse QUIET)
-if (SUITESPARSE_FOUND)
-  # On Ubuntu the system install of SuiteSparse (v3.4.0) up to at least
-  # Ubuntu 13.10 cannot be used to link shared libraries.
-  if (BUILD_SHARED_LIBS AND
-      SUITESPARSE_IS_BROKEN_SHARED_LINKING_UBUNTU_SYSTEM_VERSION)
-    theia_report_not_found("You are attempting to build Theia as a shared "
-      "library on Ubuntu using a system package install of SuiteSparse "
-      "3.4.0. This package is broken and does not support the "
-      "construction of shared libraries (you can still build Theia as "
-      "a static library).  If you wish to build a shared version of Theia "
-      "you should uninstall the system install of SuiteSparse "
-      "(libsuitesparse-dev) and perform a source install of SuiteSparse "
-      "(we recommend that you use the latest version), "
-      "see http://theia-solver.org/building.html for more information.")
-  endif (BUILD_SHARED_LIBS AND
-    SUITESPARSE_IS_BROKEN_SHARED_LINKING_UBUNTU_SYSTEM_VERSION)
-  message("-- Found SuiteSparse ${SUITESPARSE_VERSION}")
-  add_definitions(-DTHEIA_SUITESPARSE_VERSION="${SUITESPARSE_VERSION}")
-else (SUITESPARSE_FOUND)
-  # Disable use of SuiteSparse if it cannot be found and continue.
-  theia_report_not_found("Can't find SuiteSparse. This library is required "
-    "for bundle adjustment and for solving convex optimization problems. "
-    "Please set SUITESPARSE_INCLUDE_DIR & SUITESPARSE_LIBRARY")
-endif (SUITESPARSE_FOUND)
+find_package(SuiteSparse REQUIRED)
 list(APPEND THEIA_INCLUDE_DIRS ${SUITESPARSE_INCLUDE_DIRS})
 
 # Import exported Theia targets.
@@ -207,22 +155,24 @@ endif (NOT TARGET theia AND NOT Theia_BINARY_DIR)
 # Set the expected XX_LIBRARIES variable for FindPackage().
 set(THEIA_LIBRARIES theia)
 
+find_package(cereal REQUIRED)
+list(APPEND THEIA_LIBRARIES cereal)
+
+find_package(Flann REQUIRED)
+list(APPEND THEIA_INCLUDE_DIRS ${FLANN_INCLUDE_DIR})
+list(APPEND THEIA_LIBRARIES ${FLANN_LIBRARY})
+
 # Add the libraries included with the distribution.
 set(THEIA_INTERNAL_LIB_PREFIX ${THEIA_INCLUDE_DIR}/theia/libraries)
 set(THEIA_INTERNAL_LIBS_INCLUDES
   ${THEIA_INTERNAL_LIB_PREFIX}
   ${THEIA_INTERNAL_LIB_PREFIX}/akaze
-  ${THEIA_INTERNAL_LIB_PREFIX}/cereal
-  ${THEIA_INTERNAL_LIB_PREFIX}/cereal/include
-  ${THEIA_INTERNAL_LIB_PREFIX}/flann
-  ${THEIA_INTERNAL_LIB_PREFIX}/gtest
-  ${THEIA_INTERNAL_LIB_PREFIX}/gtest/include
   ${THEIA_INTERNAL_LIB_PREFIX}/optimo
   ${THEIA_INTERNAL_LIB_PREFIX}/spectra
   ${THEIA_INTERNAL_LIB_PREFIX}/statx
   ${THEIA_INTERNAL_LIB_PREFIX}/stlplus3
-  ${THEIA_INTERNAL_LIB_PREFIX}/vlfeat
-  ${THEIA_INTERNAL_LIB_PREFIX}/visual_sfm)
+  ${THEIA_INTERNAL_LIB_PREFIX}/visual_sfm
+  ${THEIA_INTERNAL_LIB_PREFIX}/vlfeat)
 
 list(APPEND THEIA_INCLUDE_DIRS ${THEIA_INTERNAL_LIBS_INCLUDES})
 
diff --git a/libraries/CMakeLists.txt b/libraries/CMakeLists.txt
index 84f3829..f88d75a 100644
--- a/libraries/CMakeLists.txt
+++ b/libraries/CMakeLists.txt
@@ -5,12 +5,6 @@ endif (${BUILD_TESTING})
 # AKAZE feature extractor.
 add_subdirectory(akaze)
 
-# Cereal for portable IO.
-add_subdirectory(cereal)
-
-# Flann for fast approximate nearest neighbor searches.
-add_subdirectory(flann)
-
 # Add Optimo.
 add_subdirectory(optimo)
 
@@ -24,4 +18,4 @@ add_subdirectory(stlplus3)
 add_subdirectory(vlfeat)
 
 # Add VisualSfM files.
-add_subdirectory(visual_sfm)
\ No newline at end of file
+add_subdirectory(visual_sfm)
diff --git a/libraries/optimo/CMakeLists.txt b/libraries/optimo/CMakeLists.txt
index 80f5e5f..ea57f1b 100644
--- a/libraries/optimo/CMakeLists.txt
+++ b/libraries/optimo/CMakeLists.txt
@@ -67,11 +67,11 @@ set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
 
 # Locations to search for Eigen
 # Eigen
-find_package(Eigen REQUIRED)
-if (EIGEN_FOUND)
-  message("-- Found Eigen version ${EIGEN_VERSION}: ${EIGEN_INCLUDE_DIRS}")
-endif (EIGEN_FOUND)
-include_directories(${EIGEN_INCLUDE_DIRS})
+find_package(Eigen3 REQUIRED)
+if (EIGEN3_FOUND)
+  message("-- Found Eigen version ${EIGEN3_VERSION_STRING}: ${EIGEN3_INCLUDE_DIRS}")
+endif (EIGEN3_FOUND)
+include_directories(${EIGEN3_INCLUDE_DIRS})
 
 # Setting CXX FLAGS appropriately. The code below was inspired from
 # Google CERES and modified for this library.
diff --git a/libraries/statx/CMakeLists.txt b/libraries/statx/CMakeLists.txt
index 23b9e49..c58a550 100644
--- a/libraries/statx/CMakeLists.txt
+++ b/libraries/statx/CMakeLists.txt
@@ -72,11 +72,11 @@ message("-- Default Install prefix: ${CMAKE_INSTALL_PREFIX}")
 option(STATX_WITH_CERES "Enables GEV parameter estimation using Ceres" ON)
 
 # Eigen
-find_package(Eigen REQUIRED)
-if (EIGEN_FOUND)
-  message("-- Found Eigen version ${EIGEN_VERSION}: ${EIGEN_INCLUDE_DIRS}")
-endif (EIGEN_FOUND)
-include_directories(${EIGEN_INCLUDE_DIRS})
+find_package(Eigen3 REQUIRED)
+if (EIGEN3_FOUND)
+  message("-- Found Eigen version ${EIGEN3_VERSION_STRING}: ${EIGEN3_INCLUDE_DIRS}")
+endif (EIGEN3_FOUND)
+include_directories(${EIGEN3_INCLUDE_DIRS})
 
 # Google Flags
 find_package(Gflags REQUIRED)
@@ -103,7 +103,11 @@ if(STATX_WITH_CERES)
   endif(CERES_FOUND)
 
   # BLAS
-  find_package(BLAS REQUIRED)
+  if(UNIX AND NOT APPLE)
+    find_package(OpenBLAS REQUIRED)
+  else()
+    find_package(BLAS REQUIRED)
+  endif()
 
   # LAPACK
   find_package(LAPACK REQUIRED)
@@ -129,7 +133,6 @@ set(XGAMMA_FILES ${XGAMMA_FUNCS}/asa121.cpp ${XGAMMA_FUNCS}/asa103.cpp)
 
 # Setting CXX FLAGS appropriately. The code below was inspired from
 # Google CERES and modified for this library.
-set (CMAKE_BUILD_TYPE Release)
 set (STATX_CXX_FLAGS)
 
 if (CMAKE_BUILD_TYPE STREQUAL "Release")
diff --git a/libraries/vlfeat/CMakeLists.txt b/libraries/vlfeat/CMakeLists.txt
index 7f4ffc7..15bca12 100644
--- a/libraries/vlfeat/CMakeLists.txt
+++ b/libraries/vlfeat/CMakeLists.txt
@@ -18,14 +18,19 @@ set(vl_sources
   vl/random.c
   vl/sift.c
   vl/vlad.c)
+
 set_source_files_properties(${vl_sources} PROPERTIES LANGUAGE C)
 
+add_library(vlfeat ${vl_sources})
+
 if (MSVC)
-  add_definitions(-DVL_BUILD_DLL)
-  add_definitions(-DVL_DISABLE_SSE2)
+  if(BUILD_SHARED_LIBS)
+    target_compile_definitions(vlfeat PRIVATE VL_BUILD_DLL)
+    target_compile_definitions(vlfeat INTERFACE BUILD_DLL)
+  endif()
+  target_compile_definitions(vlfeat PUBLIC VL_DISABLE_SSE2)
 endif (MSVC)
 
-add_library(vlfeat SHARED ${vl_sources})
 install(TARGETS vlfeat
   EXPORT  TheiaExport
   RUNTIME DESTINATION bin
diff --git a/libraries/vlfeat/vl/host.h b/libraries/vlfeat/vl/host.h
index 293fe1f..0ea3d42 100644
--- a/libraries/vlfeat/vl/host.h
+++ b/libraries/vlfeat/vl/host.h
@@ -312,27 +312,31 @@ defined(__DOXYGEN__)
 #if defined(VL_COMPILER_MSC) & ! defined(__DOXYGEN__)
 #  define VL_UNUSED
 #  define VL_INLINE static __inline
-#  define snprintf _snprintf
 #  define isnan _isnan
-#  ifdef VL_BUILD_DLL
+#  if defined(VL_BUILD_DLL)
 #    ifdef __cplusplus
 #      define VL_EXPORT extern "C" __declspec(dllexport)
 #    else
 #      define VL_EXPORT extern __declspec(dllexport)
 #    endif
-#  else
+#  elif defined(VL_DLL)
 #    ifdef __cplusplus
 #      define VL_EXPORT extern "C" __declspec(dllimport)
 #    else
 #      define VL_EXPORT extern __declspec(dllimport)
 #    endif
+#  else
+#    ifdef __cplusplus
+#      define VL_EXPORT extern "C"
+#    else
+#      define VL_EXPORT extern
+#    endif
 #  endif
 #endif
 
 #if defined(VL_COMPILER_LCC) & ! defined(__DOXYGEN__)
 #  define VL_UNUSED
 #  define VL_INLINE static __inline
-#  define snprintf _snprintf
 #  define isnan _isnan
 VL_INLINE float fabsf(float x) { return (float) fabs((double) x) ; }
 #  ifdef VL_BUILD_DLL
diff --git a/src/theia/CMakeLists.txt b/src/theia/CMakeLists.txt
index 6830f71..768586d 100644
--- a/src/theia/CMakeLists.txt
+++ b/src/theia/CMakeLists.txt
@@ -209,7 +209,6 @@ set(THEIA_LIBRARY_DEPENDENCIES
   ${SUITESPARSE_LIBRARIES}
   ${OPENIMAGEIO_LIBRARIES}
   akaze
-  flann_cpp
   statx
   stlplus3
   vlfeat
