diff --git a/configure b/configure
index e242e73c..e0d1df76 100755
--- a/configure
+++ b/configure
@@ -1,4 +1,7 @@
-#!/bin/bash
+#!/usr/bin/env bash
+
+export CFLAGS="${CPPFLAGS} ${CFLAGS}"
+test "${AS:-:}" = ":" && unset AS
 
 if test x"$1" = x"-h" -o x"$1" = x"--help" ; then
 cat <<EOF
@@ -837,6 +840,7 @@ case $host_cpu in
             AS="${AS-${SRCPATH}/tools/gas-preprocessor.pl -arch aarch64 -as-type armasm -- armasm64 -nologo}"
         else
             AS="${AS-${CC}}"
+            test "${AS}" = "${CC}" && ASFLAGS="${CPPFLAGS} ${ASFLAGS}"
         fi
         ;;
     arm*)
@@ -855,6 +859,7 @@ case $host_cpu in
             AS="${AS-${SRCPATH}/tools/gas-preprocessor.pl -arch arm -as-type clang -force-thumb -- ${CC} -mimplicit-it=always}"
         else
             AS="${AS-${CC}}"
+            test "${AS}" = "${CC}" && ASFLAGS="${CPPFLAGS} ${ASFLAGS}"
         fi
         ;;
     s390|s390x)
@@ -1354,8 +1359,10 @@ if [ $SYS = WINDOWS -a $ARCH = X86 -a $compiler = GNU ] ; then
 fi
 
 if cc_check "stdio.h" "" "fseeko(stdin,0,0);" ; then
+  if cc_check "" "" "#if defined(__ANDROID_API__) && __ANDROID_API__ < 24\n#error\n#endif\n" ; then
     define fseek fseeko
     define ftell ftello
+  fi
 elif cc_check "stdio.h" "" "fseeko64(stdin,0,0);" ; then
     define fseek fseeko64
     define ftell ftello64
