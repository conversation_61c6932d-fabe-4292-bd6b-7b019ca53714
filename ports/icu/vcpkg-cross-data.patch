diff --git a/source/configure.ac b/source/configure.ac
index 1bd5871..c508f48 100644
--- a/source/configure.ac
+++ b/source/configure.ac
@@ -1151,7 +1151,7 @@ AC_ARG_ENABLE(fuzzer,
 	fuzzer=false)
 ICU_CONDITIONAL(FUZZER, test "$fuzzer" = true)
 
-ICU_CONDITIONAL(DATA, test "$tools" = true || test "$cross_compiling" = "yes")
+ICU_CONDITIONAL(DATA, test "$tools" = true || test "$cross_compiling" = "yes" || test -n "$cross_buildroot")
 
 AC_ARG_WITH(data-packaging,
 	[  --with-data-packaging     specify how to package ICU data. Possible values:
