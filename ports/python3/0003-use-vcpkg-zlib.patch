From 196555707236f4387875749f66620fb519166963 Mon Sep 17 00:00:00 2001
From: <PERSON> <<PERSON><PERSON>oh<PERSON>@gmail.com>
Date: Wed, 9 Sep 2020 15:20:36 -0400
Subject: [PATCH 2/6] use vcpkg zlib

building without zlib is not a supported configuration, per the warning
messages.
---
 PCbuild/pythoncore.vcxproj | 6 ++++--
 1 file changed, 4 insertions(+), 2 deletions(-)

diff --git a/PCbuild/pythoncore.vcxproj b/PCbuild/pythoncore.vcxproj
index dbe236829a..5f30a35eb3 100644
--- a/PCbuild/pythoncore.vcxproj
+++ b/PCbuild/pythoncore.vcxproj
@@ -540,8 +540,10 @@
     <ClCompile Include="..\Python\deepfreeze\deepfreeze.c" />
     <!-- END deepfreeze -->
   </ItemGroup>
-  <ItemGroup Condition="$(IncludeExternals)">
+  <ItemGroup>
     <ClCompile Include="..\Modules\zlibmodule.c" />
+  </ItemGroup>
+  <ItemGroup Condition="false">
     <ClCompile Include="$(zlibDir)\adler32.c" />
     <ClCompile Include="$(zlibDir)\compress.c" />
     <ClCompile Include="$(zlibDir)\crc32.c" />
@@ -593,7 +595,7 @@
   <Target Name="_WarnAboutToolset" BeforeTargets="PrepareForBuild" Condition="$(PlatformToolset) != 'v140' and $(PlatformToolset) != 'v141' and $(PlatformToolset) != 'v142' and $(PlatformToolset) != 'v143'">
     <Warning Text="Toolset $(PlatformToolset) is not used for official builds. Your build may have errors or incompatibilities." />
   </Target>
-  <Target Name="_WarnAboutZlib" BeforeTargets="PrepareForBuild" Condition="!$(IncludeExternals)">
+  <Target Name="_WarnAboutZlib" BeforeTargets="PrepareForBuild" Condition="false">
     <Warning Text="Not including zlib is not a supported configuration." />
   </Target>
 
-- 
2.28.0.windows.1

