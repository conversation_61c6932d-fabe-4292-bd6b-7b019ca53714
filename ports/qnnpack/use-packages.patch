diff --git a/CMakeLists.txt b/CMakeLists.txt
index a5ddc49..05e1279 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -13,8 +13,6 @@ PROJECT(QNNPACK C CXX ASM)
 
 # ---[ Options.
 OPTION(QNNPACK_CUSTOM_THREADPOOL "Build QNNPACK for custom thread pool" OFF)
-SET(QNNPACK_LIBRARY_TYPE "default" CACHE STRING "Type of library (shared, static, or default) to build")
-SET_PROPERTY(CACHE QNNPACK_LIBRARY_TYPE PROPERTY STRINGS default static shared)
 OPTION(QN<PERSON><PERSON>K_BUILD_TESTS "Build QNNPACK unit tests" ON)
 OPTION(QNNPACK_BUILD_BENCHMARKS "Build QNNPACK benchmarks" ON)
 
@@ -53,11 +51,11 @@ SET(CONFU_DEPENDENCIES_SOURCE_DIR "${CMAKE_SOURCE_DIR}/deps"
 SET(CONFU_DEPENDENCIES_BINARY_DIR "${CMAKE_BINARY_DIR}/deps"
   CACHE PATH "Confu-style dependencies binary directory")
 
-IF(NOT DEFINED CLOG_SOURCE_DIR)
+IF(FALSE)
   SET(CLOG_SOURCE_DIR "${PROJECT_SOURCE_DIR}/deps/clog")
 ENDIF()
 
-IF(NOT DEFINED CPUINFO_SOURCE_DIR)
+IF(FALSE)
   MESSAGE(STATUS "Downloading cpuinfo to ${CONFU_DEPENDENCIES_SOURCE_DIR}/cpuinfo (define CPUINFO_SOURCE_DIR to avoid it)")
   CONFIGURE_FILE(cmake/DownloadCpuinfo.cmake "${CONFU_DEPENDENCIES_BINARY_DIR}/cpuinfo-download/CMakeLists.txt")
   EXECUTE_PROCESS(COMMAND "${CMAKE_COMMAND}" -G "${CMAKE_GENERATOR}" .
@@ -67,7 +65,7 @@ IF(NOT DEFINED CPUINFO_SOURCE_DIR)
   SET(CPUINFO_SOURCE_DIR "${CONFU_DEPENDENCIES_SOURCE_DIR}/cpuinfo" CACHE STRING "cpuinfo source directory")
 ENDIF()
 
-IF(NOT DEFINED FP16_SOURCE_DIR)
+IF(FALSE)
   MESSAGE(STATUS "Downloading FP16 to ${CONFU_DEPENDENCIES_SOURCE_DIR}/fp16 (define FP16_SOURCE_DIR to avoid it)")
   CONFIGURE_FILE(cmake/DownloadFP16.cmake "${CONFU_DEPENDENCIES_BINARY_DIR}/fp16-download/CMakeLists.txt")
   EXECUTE_PROCESS(COMMAND "${CMAKE_COMMAND}" -G "${CMAKE_GENERATOR}" .
@@ -77,7 +75,7 @@ IF(NOT DEFINED FP16_SOURCE_DIR)
   SET(FP16_SOURCE_DIR "${CONFU_DEPENDENCIES_SOURCE_DIR}/fp16" CACHE STRING "FP16 source directory")
 ENDIF()
 
-IF(NOT DEFINED FXDIV_SOURCE_DIR)
+IF(FALSE)
   MESSAGE(STATUS "Downloading FXdiv to ${CONFU_DEPENDENCIES_SOURCE_DIR}/fxdiv (define FXDIV_SOURCE_DIR to avoid it)")
   CONFIGURE_FILE(cmake/DownloadFXdiv.cmake "${CONFU_DEPENDENCIES_BINARY_DIR}/fxdiv-download/CMakeLists.txt")
   EXECUTE_PROCESS(COMMAND "${CMAKE_COMMAND}" -G "${CMAKE_GENERATOR}" .
@@ -87,7 +85,7 @@ IF(NOT DEFINED FXDIV_SOURCE_DIR)
   SET(FXDIV_SOURCE_DIR "${CONFU_DEPENDENCIES_SOURCE_DIR}/fxdiv" CACHE STRING "FXdiv source directory")
 ENDIF()
 
-IF(NOT DEFINED PSIMD_SOURCE_DIR)
+IF(FALSE)
   MESSAGE(STATUS "Downloading PSimd to ${CONFU_DEPENDENCIES_SOURCE_DIR}/psimd (define PSIMD_SOURCE_DIR to avoid it)")
   CONFIGURE_FILE(cmake/DownloadPSimd.cmake "${CONFU_DEPENDENCIES_BINARY_DIR}/psimd-download/CMakeLists.txt")
   EXECUTE_PROCESS(COMMAND "${CMAKE_COMMAND}" -G "${CMAKE_GENERATOR}" .
@@ -97,7 +95,7 @@ IF(NOT DEFINED PSIMD_SOURCE_DIR)
   SET(PSIMD_SOURCE_DIR "${CONFU_DEPENDENCIES_SOURCE_DIR}/psimd" CACHE STRING "PSimd source directory")
 ENDIF()
 
-IF(NOT DEFINED PTHREADPOOL_SOURCE_DIR)
+IF(FALSE)
   MESSAGE(STATUS "Downloading pthreadpool to ${CONFU_DEPENDENCIES_SOURCE_DIR}/pthreadpool (define PTHREADPOOL_SOURCE_DIR to avoid it)")
   CONFIGURE_FILE(cmake/DownloadPThreadPool.cmake "${CONFU_DEPENDENCIES_BINARY_DIR}/pthreadpool-download/CMakeLists.txt")
   EXECUTE_PROCESS(COMMAND "${CMAKE_COMMAND}" -G "${CMAKE_GENERATOR}" .
@@ -107,7 +105,7 @@ IF(NOT DEFINED PTHREADPOOL_SOURCE_DIR)
   SET(PTHREADPOOL_SOURCE_DIR "${CONFU_DEPENDENCIES_SOURCE_DIR}/pthreadpool" CACHE STRING "pthreadpool source directory")
 ENDIF()
 
-IF(QNNPACK_BUILD_TESTS AND NOT DEFINED GOOGLETEST_SOURCE_DIR)
+IF(FALSE)
   MESSAGE(STATUS "Downloading Google Test to ${CONFU_DEPENDENCIES_SOURCE_DIR}/googletest (define GOOGLETEST_SOURCE_DIR to avoid it)")
   CONFIGURE_FILE(cmake/DownloadGoogleTest.cmake "${CONFU_DEPENDENCIES_BINARY_DIR}/googletest-download/CMakeLists.txt")
   EXECUTE_PROCESS(COMMAND "${CMAKE_COMMAND}" -G "${CMAKE_GENERATOR}" .
@@ -117,7 +115,7 @@ IF(QNNPACK_BUILD_TESTS AND NOT DEFINED GOOGLETEST_SOURCE_DIR)
   SET(GOOGLETEST_SOURCE_DIR "${CONFU_DEPENDENCIES_SOURCE_DIR}/googletest" CACHE STRING "Google Test source directory")
 ENDIF()
 
-IF(QNNPACK_BUILD_BENCHMARKS AND NOT DEFINED GOOGLEBENCHMARK_SOURCE_DIR)
+IF(FALSE)
   MESSAGE(STATUS "Downloading Google Benchmark to ${CONFU_DEPENDENCIES_SOURCE_DIR}/googlebenchmark (define GOOGLEBENCHMARK_SOURCE_DIR to avoid it)")
   CONFIGURE_FILE(cmake/DownloadGoogleBenchmark.cmake "${CONFU_DEPENDENCIES_BINARY_DIR}/googlebenchmark-download/CMakeLists.txt")
   EXECUTE_PROCESS(COMMAND "${CMAKE_COMMAND}" -G "${CMAKE_GENERATOR}" .
@@ -229,15 +227,7 @@ IF(CMAKE_SYSTEM_PROCESSOR MATCHES "^(i[3-6]86|x86_64)$" OR IOS_ARCH MATCHES "^(i
   LIST(APPEND QNNPACK_UKERNELS ${QNNPACK_X86_SSE2_UKERNELS})
 ENDIF()
 
-IF(QNNPACK_LIBRARY_TYPE STREQUAL "default")
-  ADD_LIBRARY(qnnpack ${QNNPACK_INIT_SRCS} ${QNNPACK_EXEC_SRCS} ${QNNPACK_UKERNELS})
-ELSEIF(QNNPACK_LIBRARY_TYPE STREQUAL "shared")
-  ADD_LIBRARY(qnnpack SHARED ${QNNPACK_INIT_SRCS} ${QNNPACK_EXEC_SRCS} ${QNNPACK_UKERNELS})
-ELSEIF(QNNPACK_LIBRARY_TYPE STREQUAL "static")
-  ADD_LIBRARY(qnnpack STATIC ${QNNPACK_INIT_SRCS} ${QNNPACK_EXEC_SRCS} ${QNNPACK_UKERNELS})
-ELSE()
-  MESSAGE(FATAL_ERROR "Unsupported QNNPACK library type \"${QNNPACK_LIBRARY_TYPE}\". Must be \"static\", \"shared\", or \"default\"")
-ENDIF()
+ADD_LIBRARY(qnnpack ${QNNPACK_INIT_SRCS} ${QNNPACK_EXEC_SRCS} ${QNNPACK_UKERNELS})
 SET_TARGET_PROPERTIES(qnnpack PROPERTIES
   C_STANDARD 99
   C_EXTENSIONS YES)
@@ -272,7 +262,7 @@ TARGET_INCLUDE_DIRECTORIES(qnnpack PRIVATE src)
 SET_TARGET_PROPERTIES(qnnpack PROPERTIES PUBLIC_HEADER include/qnnpack.h)
 
 # ---[ Configure clog
-IF(NOT TARGET clog)
+IF(FALSE)
   SET(CLOG_BUILD_TESTS OFF CACHE BOOL "")
   SET(CLOG_RUNTIME_TYPE "${CPUINFO_RUNTIME_TYPE}" CACHE STRING "")
   ADD_SUBDIRECTORY(
@@ -281,10 +271,9 @@ IF(NOT TARGET clog)
   # We build static version of clog but a dynamic library may indirectly depend on it
   SET_PROPERTY(TARGET clog PROPERTY POSITION_INDEPENDENT_CODE ON)
 ENDIF()
-TARGET_LINK_LIBRARIES(qnnpack PRIVATE clog)
 
 # ---[ Configure cpuinfo
-IF(NOT TARGET cpuinfo)
+IF(FALSE)
   SET(CPUINFO_BUILD_TOOLS OFF CACHE BOOL "")
   SET(CPUINFO_BUILD_UNIT_TESTS OFF CACHE BOOL "")
   SET(CPUINFO_BUILD_MOCK_TESTS OFF CACHE BOOL "")
@@ -293,17 +282,19 @@ IF(NOT TARGET cpuinfo)
     "${CPUINFO_SOURCE_DIR}"
     "${CONFU_DEPENDENCIES_BINARY_DIR}/cpuinfo")
 ENDIF()
-TARGET_LINK_LIBRARIES(qnnpack PRIVATE cpuinfo)
+
+find_package(cpuinfo CONFIG REQUIRED)
+target_link_libraries(qnnpack PUBLIC cpuinfo::clog cpuinfo::cpuinfo)
 
 # ---[ Configure pthreadpool
-IF(NOT TARGET pthreadpool)
+IF(FALSE)
   SET(PTHREADPOOL_BUILD_TESTS OFF CACHE BOOL "")
   SET(PTHREADPOOL_BUILD_BENCHMARKS OFF CACHE BOOL "")
   ADD_SUBDIRECTORY(
     "${PTHREADPOOL_SOURCE_DIR}"
     "${CONFU_DEPENDENCIES_BINARY_DIR}/pthreadpool")
 ENDIF()
-IF(QNNPACK_CUSTOM_THREADPOOL)
+IF(FALSE)
   # Depend on pthreadpool interface, but not on implementation.
   # This is used when QNNPACK user (e.g. Caffe2) provides its own threadpool implementation.
   TARGET_LINK_LIBRARIES(qnnpack PUBLIC pthreadpool_interface)
@@ -312,34 +303,38 @@ ELSE()
 ENDIF()
 
 # ---[ Configure FXdiv
-IF(NOT TARGET fxdiv)
+IF(FALSE)
   SET(FXDIV_BUILD_TESTS OFF CACHE BOOL "")
   SET(FXDIV_BUILD_BENCHMARKS OFF CACHE BOOL "")
   ADD_SUBDIRECTORY(
     "${FXDIV_SOURCE_DIR}"
     "${CONFU_DEPENDENCIES_BINARY_DIR}/fxdiv")
 ENDIF()
-TARGET_LINK_LIBRARIES(qnnpack PRIVATE fxdiv)
+find_path(FXDIV_INCLUDE_DIRS "fxdiv.h")
+target_include_directories(qnnpack PRIVATE ${FXDIV_INCLUDE_DIRS})
 
 # ---[ Configure psimd
-IF(NOT TARGET psimd)
+IF(FALSE)
   ADD_SUBDIRECTORY(
     "${PSIMD_SOURCE_DIR}"
     "${CONFU_DEPENDENCIES_BINARY_DIR}/psimd")
 ENDIF()
-TARGET_LINK_LIBRARIES(qnnpack PRIVATE psimd)
+find_path(PSIMD_INCLUDE_DIRS "psimd.h")
+target_include_directories(qnnpack PRIVATE ${PSIMD_INCLUDE_DIRS})
 
 # ---[ Configure FP16
-IF(NOT TARGET fp16)
+IF(FALSE)
   SET(FP16_BUILD_TESTS OFF CACHE BOOL "")
   SET(FP16_BUILD_BENCHMARKS OFF CACHE BOOL "")
   ADD_SUBDIRECTORY(
     "${FP16_SOURCE_DIR}"
     "${CONFU_DEPENDENCIES_BINARY_DIR}/fp16")
 ENDIF()
-TARGET_LINK_LIBRARIES(qnnpack PRIVATE fp16)
+find_path(FP16_INCLUDE_DIRS "fp16.h")
+target_include_directories(qnnpack PRIVATE ${FP16_INCLUDE_DIRS})
 
 INSTALL(TARGETS qnnpack
+    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
     LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
     ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
     PUBLIC_HEADER DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})
