diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 62ce99e..61f941e 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -194,13 +194,13 @@ endif (WIN32)
 
 if (WITH_WEBSOCKETS)
 	if (STATIC_WEBSOCKETS)
-		set (MOSQ_LIBS ${MOSQ_LIBS} websockets_static)
+		set (MOSQ_LIBS ${MOSQ_LIBS} websockets)
 		if (WIN32)
 			set (MOSQ_LIBS ${MOSQ_LIBS} iphlpapi)
 			link_directories(${mosquitto_SOURCE_DIR})
 		endif (WIN32)
 	else (STATIC_WEBSOCKETS)
-		set (MOSQ_LIBS ${MOSQ_LIBS} websockets)
+		set (MOSQ_LIBS ${MOSQ_LIBS} websockets_shared)
 	endif (STATIC_WEBSOCKETS)
 endif (WITH_WEBSOCKETS)
 
