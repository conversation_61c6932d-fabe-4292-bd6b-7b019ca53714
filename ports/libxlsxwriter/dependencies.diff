diff --git a/CMakeLists.txt b/CMakeLists.txt
index 0e855c77..36ea4195 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -230,8 +230,8 @@ message("zlib version: " ${ZLIB_VERSION})
 
 # MINIZIP
 if (USE_SYSTEM_MINIZIP)
-    find_package(MINIZIP "1.0" REQUIRED)
-    list(APPEND LXW_PRIVATE_INCLUDE_DIRS ${MINIZIP_INCLUDE_DIRS})
+    find_package(MINIZIP NAMES unofficial-minizip REQUIRED)
+    set(MINIZIP_LIBRARIES unofficial::minizip::minizip)
 endif()
 
 # LIBRARY
diff --git a/dev/release/pkg-config.txt b/dev/release/pkg-config.txt
index a87d080..fa58047 100644
--- a/dev/release/pkg-config.txt
+++ b/dev/release/pkg-config.txt
@@ -7,4 +7,5 @@ Name: libxlsxwriter
 Description: A C library for creating Excel XLSX files
 Version: @VERSION@
 Cflags: -I${includedir}
-Libs: -L${libdir} -lxlsxwriter -lz
+Libs: -L${libdir} -lxlsxwriter
+Requires.private: minizip zlib
