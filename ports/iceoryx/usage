iceoryx provides CMake targets:

    find_package(iceoryx_binding_c CONFIG REQUIRED)
    target_link_libraries(main PRIVATE iceoryx_binding_c::iceoryx_binding_c)

    find_package(iceoryx_hoofs CONFIG REQUIRED)
    target_link_libraries(main PRIVATE iceoryx_hoofs::iceoryx_hoofs
                                       iceoryx_hoofs::iceoryx_platform)

    find_package(iceoryx_posh CONFIG REQUIRED)
    target_link_libraries(main PRIVATE iceoryx_posh::iceoryx_posh
                                       iceoryx_posh::iceoryx_posh_roudi
                                       iceoryx_posh::iceoryx_posh_config
                                       iceoryx_posh::iceoryx_posh_gateway)
