diff --git a/rkcommon/CMakeLists.txt b/rkcommon/CMakeLists.txt
index 2bfb993..9d61267 100644
--- a/rkcommon/CMakeLists.txt
+++ b/rkcommon/CMakeLists.txt
@@ -55,6 +55,10 @@ if (RKCOMMON_TASKING_INTERNAL)
   target_compile_definitions(${PROJECT_NAME} PRIVATE -DENKITS_BUILD_DLL)
 endif()
 
+if(BUILD_SHARED_LIBS)
+    target_compile_definitions(${PROJECT_NAME} PUBLIC rkcommon_SHARED)
+endif()
+
 if (RKCOMMON_ADDRSAN)
   target_compile_definitions(${PROJECT_NAME} PUBLIC -DRKCOMMON_ADDRSAN)
 endif()
diff --git a/rkcommon/common.h b/rkcommon/common.h
index a205770..07bf930 100644
--- a/rkcommon/common.h
+++ b/rkcommon/common.h
@@ -26,7 +26,7 @@ typedef int ssize_t;
 #include "unistd.h"
 #endif
 
-#ifdef _WIN32
+#if defined(_WIN32) && defined(rkcommon_SHARED)
 #ifdef rkcommon_EXPORTS
 #define RKCOMMON_INTERFACE __declspec(dllexport)
 #else
