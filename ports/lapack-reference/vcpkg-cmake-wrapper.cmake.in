message(STATUS "Using VCPKG FindLAPACK from package 'lapack-reference'")
set(LAPACK_PREV_MODULE_PATH "${CMAKE_MODULE_PATH}")
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}")

list(REMOVE_ITEM ARGS "NO_MODULE")
list(REMOVE_ITEM ARGS "CONFIG")
list(REMOVE_ITEM ARGS "MODULE")

if(@USE_OPTIMIZED_BLAS@)
    find_package(BLAS)
endif()

# BLA_VENDOR and BLA_STATIC are documented at:
# * https://cmake.org/cmake/help/latest/module/FindBLAS.html
# * https://cmake.org/cmake/help/latest/module/FindLAPACK.html

set(BLA_VENDOR @BLA_VENDOR@)
set(BLA_STATIC @BLA_STATIC@)
_find_package(${ARGS})
unset(BLA_VENDOR)
unset(BLA_STATIC)

if(@CBLAS@)
  include(SelectLibraryConfigurations)
  find_library(CB<PERSON><PERSON>_LIBRARY_RELEASE NAMES libcblas cblas PATHS "${CURRENT_PACKAGES_DIR}/lib" NO_DEFAULT_PATH)
  find_library(CBLAS_LIBRARY_DEBUG NAMES libcblas cblas PATHS "${CURRENT_PACKAGES_DIR}/debug/lib" NO_DEFAULT_PATH)
  select_library_configurations(CBLAS)
  set(LAPACK_LIBRARIES ${LAPACK_LIBRARIES} ${CBLAS_LIBRARIES})
endif()

set(CMAKE_MODULE_PATH "${LAPACK_PREV_MODULE_PATH}")
