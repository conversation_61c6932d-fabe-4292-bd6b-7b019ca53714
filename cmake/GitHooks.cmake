if (ENABLE_CREATE_GIT_HOOKS)
    set(PRE_COMMIT_HOOK ${CMAKE_CURRENT_SOURCE_DIR}/.git/hooks/pre-commit)
    if (EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/.git/hooks AND NOT EXISTS "${PRE_COMMIT_HOOK}")
        message(STATUS "Create the pre-commit hook")
        set(PRE_COMMIT_HOOK ${CMAKE_CURRENT_SOURCE_DIR}/.git/hooks/pre-commit)
        execute_process(
                COMMAND "ln" "-sf" ${CMAKE_CURRENT_SOURCE_DIR}/.git_hooks/pre-commit ${PRE_COMMIT_HOOK}
                RESULT_VARIABLE ret_code
        )
        if (${ret_code} EQUAL 0)
            MESSAGE(STATUS "Creating pre-commit hook done")
        else ()
            MESSAGE(FATAL_ERROR "Creating pre-commit hook failed: ${ret_code}")
        endif ()
    endif ()
endif ()
