# - Try to find Google performance tools (gperftools)
# Input variables:
#   GPERFTOOLS_ROOT_DIR    - The gperftools install directory;
#                            if not set the GPERFTOOLS_DIR environment variable will be used
#   GPERFTOOLS_INCLUDE_DIR - The gperftools include directory
#   GPERFTOOLS_LIBRARY     - The gperftools library directory
# Components: profiler, and tcmalloc or tcmalloc_minimal
# Output variables:
#   gperftools_FOUND        - System has gperftools
#   gperftools_INCLUDE_DIRS - The gperftools include directories
#   gperftools_LIBRARIES    - The libraries needed to use gperftools
#   gperftools_VERSION      - The version string for gperftools

include(FindPackageHandleStandardArgs)

if (NOT DEFINED gperftools_FOUND)

    # If not set already, set GPERFTOOLS_ROOT_DIR from environment
    if (DEFINED ENV{GPERFTOOLS_DIR} AND NOT DEFINED GPERFTOOLS_ROOT_DIR)
        set(GPERFTOOLS_ROOT_DIR $ENV{GPERFTOOLS_DIR})
    endif ()

    # Set default search paths for gperftools
    if (GPERFTOOLS_ROOT_DIR)
        set(GPERFTOOLS_INCLUDE_DIR ${GPERFTOOLS_ROOT_DIR}/include CACHE PATH "The include directory for gperftools")
        if (CMAKE_SIZEOF_VOID_P EQUAL 8 AND CMAKE_SYSTEM_NAME STREQUAL "Linux")
            set(GPERFTOOLS_LIBRARY ${GPERFTOOLS_ROOT_DIR}/lib64;${GPERFTOOLS_ROOT_DIR}/lib CACHE PATH "The library directory for gperftools")
        else ()
            set(GPERFTOOLS_LIBRARY ${GPERFTOOLS_ROOT_DIR}/lib CACHE PATH "The library directory for gperftools")
        endif ()
    else (GPERFTOOLS_ROOT_DIR)
        set(GPERFTOOLS_INCLUDE_DIR /usr/include;/usr/local/include;/usr/local/homebrew/include;/opt/local/include;/opt/homebrew/include CACHE PATH "The default include directory for gperftools")
        if (CMAKE_SIZEOF_VOID_P EQUAL 8 AND CMAKE_SYSTEM_NAME STREQUAL "Linux")
            set(GPERFTOOLS_LIBRARY /usr/lib64;/usr/lib;/usr/local/lib64;/usr/local/lib;/opt/local/lib64;/opt/local/lib;/opt/homebrew/lib CACHE PATH "The default library directory for gperftools")
        else ()
            set(GPERFTOOLS_LIBRARY /usr/lib;/usr/local/lib;/opt/local/lib64;/opt/local/lib;/opt/homebrew/lib CACHE PATH "The default library directory for gperftools")
        endif ()
    endif ()

    # Check to see if libunwind is required
    set(GPERFTOOLS_DISABLE_PROFILER FALSE)
    if ((";${gperftools_FIND_COMPONENTS};" MATCHES ";profiler;") AND
    (CMAKE_SYSTEM_NAME MATCHES "Linux" OR
            CMAKE_SYSTEM_NAME MATCHES "BlueGeneQ" OR
            CMAKE_SYSTEM_NAME MATCHES "BlueGeneP") AND
    (CMAKE_SIZEOF_VOID_P EQUAL 8))

        # Libunwind is required by profiler on this platform
        if (gperftools_FIND_REQUIRED_profiler OR gperftools_FIND_REQUIRED_tcmalloc_and_profiler)
            find_package(libunwind 0.99 REQUIRED)
        else ()
            find_package(libunwind)
            if (NOT libunwind_FOUND OR LIBUNWIND_VERSION VERSION_LESS 0.99)
                set(GPERFTOOLS_DISABLE_PROFILER TRUE)
            endif ()
        endif ()
    endif ()

    # Check for invalid components
    foreach (_comp ${gperftools_FIND_COMPONENTS})
        if ((NOT _comp STREQUAL "tcmalloc_and_profiler") AND
        (NOT _comp STREQUAL "tcmalloc") AND
        (NOT _comp STREQUAL "tcmalloc_minimal") AND
        (NOT _comp STREQUAL "profiler"))
            message(FATAL_ERROR "Invalid component specified for Gperftools: ${_comp}")
        endif ()
    endforeach ()

    # Check for valid component combinations
    if (";${gperftools_FIND_COMPONENTS};" MATCHES ";tcmalloc_and_profiler;" AND
    (";${gperftools_FIND_COMPONENTS};" MATCHES ";tcmalloc;" OR
            ";${gperftools_FIND_COMPONENTS};" MATCHES ";tcmalloc_minimal;" OR
            ";${gperftools_FIND_COMPONENTS};" MATCHES ";profiler;"))
        message("ERROR: Invalid component selection for Gperftools: ${gperftools_FIND_COMPONENTS}")
        message("ERROR: Gperftools cannot link both tcmalloc_and_profiler with the tcmalloc, tcmalloc_minimal, or profiler libraries")
        message(FATAL_ERROR "Gperftools component list is invalid")
    endif ()
    if (";${gperftools_FIND_COMPONENTS};" MATCHES ";tcmalloc;" AND ";${gperftools_FIND_COMPONENTS};" MATCHES ";tcmalloc_minimal;")
        message("ERROR: Invalid component selection for Gperftools: ${gperftools_FIND_COMPONENTS}")
        message("ERROR: Gperftools cannot link both tcmalloc and tcmalloc_minimal")
        message(FATAL_ERROR "Gperftools component list is invalid")
    endif ()

    # Search for component libraries
    foreach (_comp ${gperftools_FIND_COMPONENTS})
        find_library(GPERFTOOLS_${_comp}_LIBRARY ${_comp}
                HINTS ENV LD_LIBRARY_PATH
                HINTS ENV DYLD_LIBRARY_PATH
                HINTS ${GPERFTOOLS_LIBRARY})
        if (GPERFTOOLS_${_comp}_LIBRARY)
            set(gperftools_${_comp}_FOUND TRUE)
        else ()
            set(gperftools_${_comp}_FOUND FALSE)
        endif ()

        # Exclude profiler from the found list if libunwind is required but not found
        if (gperftools_${_comp}_FOUND AND ${_comp} MATCHES "profiler" AND GPERFTOOLS_DISABLE_PROFILER)
            set(gperftools_${_comp}_FOUND FALSE)
            set(GPERFTOOLS_${_comp}_LIBRARY "GPERFTOOLS_${_comp}_LIBRARY-NOTFOUND")
            message("WARNING: Gperftools '${_comp}' requires libunwind 0.99 or later.")
            message("WARNING: Gperftools '${_comp}' will be disabled.")
        endif ()

        if (";${gperftools_FIND_COMPONENTS};" MATCHES ";${_comp};" AND gperftools_${_comp}_FOUND)
            list(APPEND gperftools_LIBRARIES "${GPERFTOOLS_${_comp}_LIBRARY}")
        endif ()
    endforeach ()

    # Search for component include
    find_path(gperftools_INCLUDE_DIRS NAMES gperftools/malloc_extension.h
            HINTS ${GPERFTOOLS_INCLUDE_DIR})


    # handle the QUIETLY and REQUIRED arguments and set gperftools_FOUND to TRUE
    # if all listed variables are TRUE
    find_package_handle_standard_args(gperftools
            FOUND_VAR gperftools_FOUND
            REQUIRED_VARS gperftools_LIBRARIES gperftools_INCLUDE_DIRS
            HANDLE_COMPONENTS)

    mark_as_advanced(GPERFTOOLS_INCLUDE_DIR GPERFTOOLS_LIBRARY
            gperftools_INCLUDE_DIRS gperftools_LIBRARIES)

    # Add linker flags that instruct the compiler to exclude built in memory
    # allocation functions. This works for GNU, Intel, and Clang. Other compilers
    # may need to be added in the future.
    if (gperftools_LIBRARIES MATCHES "tcmalloc")
        if ((CMAKE_CXX_COMPILER_ID MATCHES "GNU") OR
        (CMAKE_CXX_COMPILER_ID MATCHES "AppleClang") OR
        (CMAKE_CXX_COMPILER_ID MATCHES "Clang") OR
        ((CMAKE_CXX_COMPILER_ID MATCHES "Intel") AND (NOT CMAKE_CXX_PLATFORM_ID MATCHES "Windows")))
            list(APPEND gperftools_LIBRARIES "-fno-builtin-malloc"
                    "-fno-builtin-calloc" "-fno-builtin-realloc" "-fno-builtin-free")
        endif ()
    endif ()

    # Add libunwind flags to gperftools if the profiler is being used
    if (gperftools_LIBRARIES MATCHES "profiler" AND libunwind_FOUND)
        list(APPEND gperftools_INCLUDE_DIRS "${libunwind_INCLUDE_DIR}")
        list(APPEND gperftools_LIBRARIES "${libunwind_LIBRARIES}")
    endif ()

    unset(GPERFTOOLS_DISABLE_PROFILER)

endif ()