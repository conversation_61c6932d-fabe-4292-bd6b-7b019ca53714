find_package(Git)

if (GIT_FOUND AND EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/.git")
    execute_process(
            COMMAND ${GIT_EXECUTABLE} rev-parse --short HEAD
            WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
            OUTPUT_VARIABLE GIT_COMMIT
    )
    execute_process(
            COMMAND ${GIT_EXECUTABLE} name-rev --name-only HEAD
            WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
            OUTPUT_VARIABLE GIT_BRANCH
    )
endif()


if (GIT_COMMIT)
    string(REGEX REPLACE "[^0-9a-f]+" "" GIT_COMMIT "${GIT_COMMIT}")
endif()

if (GIT_BRANCH)
    string(REGEX REPLACE "\n" "" GIT_BRANCH "${GIT_BRANCH}")
endif()
