print_config(PROJECT_NAME)

get_cmake_property(variable_list VARIABLES)
foreach (_varname ${variable_list})
    string(REGEX MATCH "^(ENABLE|BUILD_TESTING)" matched ${_varname})
    if (matched)
        print_option(${_varname})
    endif ()
endforeach ()

print_config(CMAKE_TOOLCHAIN_FILE)
print_config(CMAKE_BUILD_TYPE)
print_config(CMAKE_CXX_FLAGS)
print_config(CMAKE_CXX_FLAGS_DEBUG)
print_config(CMAKE_CXX_FLAGS_RELEASE)
print_config(CMAKE_HOST_SYSTEM_NAME)
print_config(CMAKE_HOST_SYSTEM_PROCESSOR)
