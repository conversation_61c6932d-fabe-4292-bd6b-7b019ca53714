#ifndef SRC_CROSS_WORKER_CROSS_RECEIVER_CROSS_MANAGER_CROSS_CONSUME_HPP_
#define SRC_CROSS_WORKER_CROSS_RECEIVER_CROSS_MANAGER_CROSS_CONSUME_HPP_

#include <bbase/common/hdts/hdts.hpp>
#include <memory>

#include "src/cross_worker/cross_receiver/cross_define.hpp"
#include "src/cross_worker/cross_receiver/message_handle/message_handle_base.hpp"

namespace cross {

struct CrossConsumeParam {
 public:
  using Ptr = std::shared_ptr<CrossConsumeParam>;

 public:
  consume_task_id_t consume_task_id{0};
  bbase::hdts::Topic topic_name{""};
  bbase::hdts::Offset start_offset{0};
  bbase::hdts::Offset end_offset{-1};
  bbase::hdts::Offset pause_offset{0};
  BusinessType business_type{BusinessType::kUnkown};
  bool alone_cb_thread{false};
  bool is_reliable{true};
};

class CrossConsume final {
 public:
  using Ptr = std::shared_ptr<CrossConsume>;

 public:
  CrossConsume(CrossConsumeParam::Ptr consume_param_ptr);
  ~CrossConsume();

  bbase::hdts::Topic& GetTopic() { return topic_; }

  /**
   * @brief 启动消费者
   * @param 消费者处理回调
   * @return 启动成功返回true
   */
  bool StartConsume(MessageHandleBase::Ptr&& handler);

  /**
   * @brief 停止消费者
   */
  bool StopConsume();
  /**
   * @brief消费者的回调线程中需要将停止处理抛到其他线程处理，否则会导致dtssdk内部死锁
   */
  void StopConsumeUseThread();

  /**
   * @brief 调整传入的起始offset
   */
  void AdjustOffset();

 private:
  bbase::hdts::Topic topic_;
  std::atomic_bool running_{false};
  MessageHandleBase::Ptr msg_handler_{nullptr};
  bbase::hdts::Manager::ClientId hdts_client_id_;
  CrossConsumeParam::Ptr cross_consume_param_ptr_{nullptr};
  bbase::hdts::Offset final_start_offset_{0};       // 最终决断的起始offset
  bbase::hdts::Offset final_end_offset_{-1};        // 最终决断的结束offset， -1表示没有结束
  bbase::hdts::Offset current_begin_inclusive_{0};  // 当前可消费offset区间的左闭边界
  bbase::hdts::Offset current_end_exclusive_{0};    // 当前可消费offset区间的右开边界
};

}  // namespace cross

#endif  // SRC_CROSS_WORKER_CROSS_RECEIVER_CROSS_MANAGER_CROSS_CONSUME_HPP_
