#ifndef SRC_CROSS_WORKER_CROSS_PRODUCER_XREQ_BUILD_SPOT_REQUEST_MANAGER_HPP_
#define SRC_CROSS_WORKER_CROSS_PRODUCER_XREQ_BUILD_SPOT_REQUEST_MANAGER_HPP_

#include <bbase/common/hdts/hdts.hpp>
#include <bbase/common/utils/utils.hpp>
#include <memory>
#include <string>
#include <vector>

#include "src/biz_worker/service/trade/store/order/raw_order.hpp"
#include "src/biz_worker/service/trade/store/passthrough/raw_pass_through.hpp"
#include "src/biz_worker/service/trade/store/passthrough/raw_recover_pass.hpp"
#include "src/biz_worker/service/trade/store/spot_order/raw_spot_order.hpp"
#include "src/common/utils.hpp"
#include "src/config/config_manager.hpp"
#include "src/config/symbol_config/symbol_config_manager.hpp"
#include "src/cross_worker/cross_common/error/cross_error.hpp"
#include "src/cross_worker/cross_common/protocol/binary_spot/spot_header.hpp"
#include "src/cross_worker/cross_common/protocol/binary_spot/spot_request.hpp"
#include "src/cross_worker/cross_common/protocol/binary_spot/spot_response.hpp"
#include "src/cross_worker/cross_producer/xreq_build/spot_request_manager.hpp"
#include "src/data/event/event.hpp"
#include "src/data/type/biz_type.hpp"

namespace biz::cross::spot {

class SpotRequestManager {
 public:
  SpotRequestManager() = default;
  ~SpotRequestManager() = default;

 public:
  biz::CrossError BuildProduceMsg(const config::SpotClient* spot_client, bbase::hdts::Producer::Message& msg,
                                  biz::cross_idx_t cross_id);
  // biz::CrossError BuildProduceParadigmMsg(const config::SpotClient* spot_client,
  // bbase::hdts::Producer::Message& msg,
  //                                        biz::cross_idx_t cross_id);

 public:
  /// payload内容
  std::shared_ptr<spot::RawXHeader> header_obj_ = nullptr;
  std::shared_ptr<spot::request::XRequest> request_obj_ = nullptr;

  /// 透传内容
  // 用pb序列化结果存储透传信息
  // 此字段序列化之前需要转成pb
  // std::optional<store::PassThrough> x_pass_through_;
  std::optional<::models::passthroughdto::PassThroughDTO> x_pass_through_;
  /// for spot
  std::optional<store::SpotOrder> x_spot_recovery_item_;
};
}  // namespace biz::cross::spot
#endif  // SRC_CROSS_WORKER_CROSS_PRODUCER_XREQ_BUILD_SPOT_REQUEST_MANAGER_HPP_
