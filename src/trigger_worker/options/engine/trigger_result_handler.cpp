#include "src/trigger_worker/options/engine/trigger_result_handler.hpp"

#include <bbase/common/utils/assert.hpp>

namespace trigger_worker::options {

std::int32_t TriggerResultHandler::OnEvent(const event::Event::Ptr& event_p) {
  // 1.过滤不需要同步给其他worker的事件
  if (!this->NeedHandle(event_p)) {
    return 0;
  }

  ASSERT_RETVAL(writers_[worker::kPreWorker] != nullptr, error::ErrorCode::kErrorCodeDefault);

  int32_t ret = 0;
  switch (event_p->type()) {
    default: {
      ret = writers_[worker::kPreWorker]->Write(event_p);
    }
  }

  return ret;
}

bool TriggerResultHandler::NeedHandle(const event::Event::Ptr& event_p) {
  LOG_DEBUG("[Option] NeedHandle event_p == null ? {}", event_p == nullptr);
  return true;
}

}  // namespace trigger_worker::options
