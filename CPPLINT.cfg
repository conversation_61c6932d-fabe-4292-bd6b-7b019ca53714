# Stop searching for additional config files.
set noparent

# Disable a warning about C++ features that were not in the original
# C++11 specification (and so might not be well-supported).
filter=-build/c++11

# Use `#ifndef FOO_H` guard.
#filter=-build/header_guard
#filter=+build/pragma_once

# Disable cpplint's include order.
filter=-build/include_order

# Ignore code that isn't ours.
# exclude_files=(docs|vcpkg|cmake-.*|bld|build|gen|protocol|proto)

# others
filter=-legal/copyright
filter=-whitespace/braces
filter=-readability/braces
filter=-readability/check
filter=-runtime/arrays
filter=-runtime/string
#filter=-runtime/printf
#filter=-whitespace/indent
filter=-runtime/references
#filter=-build/include_subdir
#filter=-whitespace/todo
filter=-readability/fn_size
linelength=120
