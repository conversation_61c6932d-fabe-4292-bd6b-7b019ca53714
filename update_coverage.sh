#!/bin/bash

cd `dirname "${BASH_SOURCE[0]}"`

rm -rf build
rm -rf coverage

proc_num=1
current_path=${PWD}
vcpkg_dir=${current_path}/deps/vcpkg
vcpkg_installed_dir=${current_path}/deps/vcpkg/installed
vcpkg_manifest=${current_path}/deps
ctest_proc_num=1
enable_ccache='ON'

#please `brew install gnu-getopt` on mac
OPTIONS=`getopt -o j:D:d:M::J:C: -- $@`

eval set -- "${OPTIONS}"

while true ; do
    case "$1" in
        -j) proc_num=$2 ; shift 2;;
        -D) vcpkg_dir="$2" ; shift 2;;
        -d) vcpkg_installed_dir="$2" ; shift 2;;
        -M) vcpkg_manifest="$2" ; shift 2;;
        -J) ctest_proc_num="$2" ; shift 2;;
        -C) enable_ccache="$2" ; shift 2;;
        --) shift ; break ;;
    esac
done

./run_cmake.sh -c -j${proc_num} -D${vcpkg_dir} -d${vcpkg_installed_dir} -M${vcpkg_manifest} -C${enable_ccache}

exit_code=$?

if [ ${exit_code} -ne 0 ]; then
    exit ${exit_code}
fi

mkdir -p coverage/html

cd build
ctest -j${ctest_proc_num} --progress --timeout 30 --test-timeout 30 --rerun-failed --output-on-failure  --repeat until-pass:5 --output-junit ../coverage/ctest_report.xml
cd ..

gcovr -r . --exclude-branches-by-pattern '.*(LOG|ASSERT|PANIC).*' --gcov-ignore-errors --gcov-ignore-parse-errors --exclude-noncode-lines --exclude-throw-branches --exclude-unreachable-branches --calls --decisions --html --html-nested -e 'bbase/proto/' -e 'deps/' -e 'third_party/' -e 'test/' -e 'tools/' -e 'build/' -e 'proto/' -o coverage/html/index.html --json coverage/coverage.json --json-pretty --json-summary coverage/coverage-summary.json --json-summary-pretty --cobertura coverage/coverage.xml --cobertura-pretty -j 8 --merge-mode-functions=separate
