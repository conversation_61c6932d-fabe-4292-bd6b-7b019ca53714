#!/bin/bash

cd `dirname "${BASH_SOURCE[0]}"`

EXPECT_VER="clang-format version 18.1.5"

if [ "$(clang-format --version)" != "${EXPECT_VER}" ]; then
  echo
  echo "Please use ${EXPECT_VER} !!!"
  echo
  exit 1
fi

# clang-format -i --style='file:./.clang-format' --files=<( find  ./bbase ./src ./test ./tools  -type f | grep -E '/[^/]*\.(h|c|hpp|cpp|hh|cc|hxx|cxx)$' | grep -v -E '/[^/]*\.pb\.(h|cc)$' )

clang-format -i --style='file:./.clang-format' --files=<( git diff --name-only --merge-base origin/master -- ./bbase ./src ./test ./tools  | grep -E '/[^/]*\.(h|c|hpp|cpp|hh|cc|hxx|cxx)$' | grep -v -E '/[^/]*\.pb\.(h|cc)$' )
