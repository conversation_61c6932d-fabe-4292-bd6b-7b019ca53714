syntax = "proto3";

package enums.etriggerby;
option go_package = "code.bydev.io/trading/idl/pb/enums/etriggerby";


enum TriggerBy {
    option allow_alias = true;
    UNKNOWN = 0;
    LastPriceOld = 0;

    LastPrice = 1;
    MarkPrice = 2;
    IndexPrice = 3;
    ExpectSettlePrice = 4; //预期结算价格
    Ask1Price = 5;
    Bid1Price = 6;
}

enum OCOTriggerBy {
    OCO_TRIGGER_BY_UNKNOWN = 0; // 未知类型
    OCO_TRIGGER_BY_TP = 1; // 止盈触发
    OCO_TRIGGER_BY_SL = 2; // 止损触发
}