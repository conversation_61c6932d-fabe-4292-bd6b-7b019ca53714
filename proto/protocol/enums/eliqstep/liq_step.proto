syntax = "proto3";

package enums.eliqstep;
option go_package = "code.bydev.io/trading/idl/pb/enums/eliqstep";


enum LiqStep {
  WaitTrigger = 0;               // 等待触发
  TryAdjustRiskWithoutLoss = 1;  // 无损降档
  TryAdjustWithLoss = 2;         // 有损降档
  CancelAllOpenOrders = 3;       // 取消开仓单
  CancelAllOrders = 4;           // 取消订单
  PartialLiq = 5;                // 期权期货阶梯强平
  FutureTakeOver = 6;            // 期货托管
  SystemTakeOver = 7;            // 期权期货全部托管
  RiskClosePositions = 8;        // 人工全部托管
  SpotExchange = 9;              // 现货兑币
  DelayLiqWait = 10;             // 延迟强平等待中
  LiqTest = 11;                  // 强平测试
  LiqTrigger = 12;               // 强制检查
  LiqSelfTrade = 13;             // 强平双仓自成交
  FinalSpotExchange = 14;        // 衍生品强平完成后再次进行兑币
  CancelAllForDelay = 15;        // 延迟强平前置撤单
  HedgedStepLiq = 16;            // 对冲拆腿
  SpotCollateralConvert = 17;    // 现货资产价值率转换
}
