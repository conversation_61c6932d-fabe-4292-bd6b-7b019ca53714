syntax = "proto3";

package enums.epzchtype;
option go_package = "code.bydev.io/trading/idl/pb/enums/epzchtype";


enum PositionChangeType {
    UNKNOWN = 0;

    BalanceChange = 1; // 仓位余额变化 (或者没有变化也是这个值)
    SizeChange = 2; // 仓位的size,size,entry_price变化
    LeverageChange = 3; // 仓位的leverage变化

    /// 跟 强平/减仓 流程相关的撤单操作, 不论是否撤单成功
    /// 都要给一个position_change数据包

    CancelAllBeforeLiq = 4; // 逻辑上跟 BalanceChange 一样
    LiqOrderRetry = 5; // 强平系统的5秒撤单行为的 成交回报 (reject也需要丢这个事件)

    CancelAllBeforeAdl = 6; // 逻辑上跟 BalanceChange 一样
}
