syntax = "proto3";

package enums.ecpbindrecordcreatetype;
option go_package = "code.bydev.io/trading/idl/pb/enums/ecptradefollowertype";


enum CopyTradeBindRecordCreateType {
  option allow_alias = true;
  UNKNOWN = 0;
  CreateByFollower = 0; // follower 发起绑定/解绑

  CreateByLeaderUnbind = 1; // leader主动发起解绑
  CreateByConsecutiveFailSystemUnbind = 2; // 因为连续跟单失败系统发起解绑
  CreateByManyFailSystemUnbind = 3; // 因为多次跟单失败系统发起解绑
  CreateByCSLTrigger = 4; // 由于触发CSL机制而进行解绑
  CreateByInternalUnbind = 5; // 由于系统内部解绑触发
}
