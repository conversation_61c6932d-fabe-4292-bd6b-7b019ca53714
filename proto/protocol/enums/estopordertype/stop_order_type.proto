syntax = "proto3";

package enums.estopordertype;
option go_package = "code.bydev.io/trading/idl/pb/enums/estopordertype";


enum StopOrderType {
    UNKNOWN = 0;

    Stop = 1;
    TakeProfit = 2;
    StopLoss = 3;
    TrailingStop = 4;
    TrailingProfit = 5;

    PartialTakeProfit = 6; //部分止盈
    PartialStopLoss = 7;   // 部分止损
    TakeProfitStopLoss = 8;   // 止盈止损（现货独有）
    MmRateClose = 9;
    TakeProfitStopLossOCO = 10;   // 止盈止损OCO（现货独有）
    BidirectionalTpSl = 11; // 现货双向止盈止损
}
