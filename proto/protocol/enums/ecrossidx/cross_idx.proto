syntax = "proto3";

package enums.ecrossidx;
option go_package = "code.bydev.io/trading/idl/pb/enums/ecrossidx";


//I: inverse  F: future L: linear
enum CrossIdx {
    UNKNOWN = 0;

    //BTC 反向永续
    BTCIP = 1;
    //BTC反向交割
    BTCIF = 11;

    ETHIP = 2;
    //ETH交割
    ETHIF = 12;
    EOSIP = 3;
    XRPIP = 4;

    DOTIP2 = 10;
    BITIP = 13;
    ADAIP = 14;
    SOLIP = 15;
    LUNAIP = 16;
    MANAIP = 17;
    LTCIP = 18;

    //正向永续
    BTCLP = 5;
    ETHLP = 6;
    // (eos etc matic bnb fil sol)/usdt
    EMBFSLP = 7;

    BCHLP = 23;
    LTCLP = 24;
    XTZLP = 25;
    LINKLP = 26;
    ADULP = 27;
    // xrpusdt xemusdt sushiusdt aaveusdt
    XXSALP = 28;
    C30LP = 30;
    C31LP = 31;
    C32LP = 32;

    //正向交割
    //USDT_LF = ?

    TESTIP = 9;

    // 组合保证金永续
    BTCPLP = 29;

    C33LP = 33;
    C34LP = 34;
    C35LP = 35;
    C36LP = 36;
    C37LP = 37;
    C38LP = 38;
}