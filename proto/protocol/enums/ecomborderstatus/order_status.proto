syntax = "proto3";

package enums.ecomborderstatus;
option go_package = "code.bydev.io/trading/idl/pb/enums/ecmborderstatus";


// 将stop_order的状态`StopOrderStatus`组合到一起
enum OrderStatus {
    option allow_alias = true;
    UNKNOWN = 0;

    NotActive = 1; // 条件单未触发时 orders表的状态 (二进制值兼容)
    /* >>>>> begin NotActive >>>>> */

    Untriggered = 2; // 等待被触发 `Anchoring`
    // Cancelled 条件单撤单 跟活动订单撤单复用
    Deactivated = 10; // 条件单激活前被撤单 or 锚定持仓变化后被撤单

    Triggered = 3; // 正常情况下 只会很短暂地停留在这个状态
    // PendingCreate // 条件单激活后转化过程中
    // Rejected --> 复用order的拒绝状态: 条件单激活后转化为活动订单被拒绝

    /* <<<<< end NotActive <<<<< */

    Active = 3; // 条件单激活成功后 stop_orders表的状态 `Fulfilled`
    /* >>>>> begin Active >>>>> */

    Created = 4; // 这个状态一般不会输出, 只会停留在内存中
    Rejected = 5;
    New = 6;
    WaitToRecvPassthrough = 12; // partiallyfill, fill, cancel comb撮合回来，等单腿撮合透传包期间的状态
    PartiallyFilled = 8;
    Filled = 9;
    Cancelled = 7;
    PartiallyFilledCanceled = 11;
    /* <<<<< end Active <<<<< */
}
