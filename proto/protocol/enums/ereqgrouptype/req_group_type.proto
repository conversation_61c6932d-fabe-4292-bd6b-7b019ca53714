syntax = "proto3";

package enums.ereqgrouptype;
option go_package = "code.bydev.io/trading/idl/pb/enums/ereqgrouptype";


//请求业务组类别，和UnifiedV2RequestDTO里面第一层req_group类别对应
//用作请求区分时第一层过滤
enum ReqGroupType {
  UNKNOWN = 0;
  CHECK = 14; //核对相关
  CROSS = 1; //核对相关
  LENDING = 2; //借贷相关
//  LIQ = 3; //强平
  MAINT = 4; //维护+洗数据相关
  MARGIN = 5; // 保证金相关
  MARKET_MAKER = 6;//做市商
  OPTION = 7;//期权（暂时）
  ORDER = 8;//订单
  POSITION = 9;//订单
  QUERY = 10;//查询
  RECOVERY = 11;//永续数据恢复
  SETTING = 12; //设置
  WALLET = 13; //钱包相关
}