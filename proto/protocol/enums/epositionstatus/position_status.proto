syntax = "proto3";

package enums.epositionstatus;
option go_package = "code.bydev.io/trading/idl/pb/enums/epositionstatus";


enum PositionStatus {
    option allow_alias = true;
    Normal = 0; // 这个用0值 是为了方便zero初始化后就是`Normal`状态
    // 只有在Normal状态下, 用户才能 下单/修改订单

    Liq = 1; // 作为LiqReady的别名
    Adl = 2; // 作为AdlReady的别名

    // Banned = 3; 交易封禁
    // Frozen = 3; 冻结持仓

    PendingLiq = 3;
    LiqPreparing = 3; // 前置撤单
    LiqReady = 1; // 已撤单完成; 等待执行SellOff 或 等待被TakeOver
    LiqInProcessing = 4; // 已经发起 SellOff 或 TakeOver请求; 等待处理结果

    PendingAdl = 5;
    AdlPreparing = 5; // 前置撤单
    AdlReady = 2; // 已撤单完成; 等待被执行自动减仓
    AdlInProcessing = 6; // 已经发起 自动减仓透传请求; 等待处理结果
    Inactive = 7; // 单双向切换后，原仓位状态置为Inactive，新仓位状态置为Normal
}
