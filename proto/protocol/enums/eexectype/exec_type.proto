syntax = "proto3";

package enums.eexectype;
option go_package = "code.bydev.io/trading/idl/pb/enums/eexectype";


enum ExecType {
    // Next:18
    UNKNOWN = 0;

    Trade = 1; // 成交
    BustTrade = 3; // 仓位被ZeroOut (被强平的普通用户 看到这个类型)
    AdlTrade = 6; // 减仓maker (`被减仓用户` 看到这个类型)
    BlockTrade = 13; // 大宗交易
    BlockTradeRollback = 14; // 大宗交易回滚
    BlockTradeMovePosition = 17; // 大宗交易移仓
    FutureSpread = 18;  // FutureSpread成交

    DealingDesk = 16; // DealingDesk 正常成交
    Funding = 2; // 资金费用

    TakeOver = 4; // 仓位被take over (强平系统 看到这个类型)
    LiqTrade = 5; // 强平系统去挂单后; 被普通用户买走 (强平系统 看到这个类型)

    AdlTaker = 7; // 强平系统作为taker发起减仓 (强平系统 看到这个类型)

    Force = 11; //强增强减

    Settle = 12; // 交割

    SessionSettlePnL = 15; // 定期结算盈亏

    // --- 特殊类型 ---
    Amend = 8; //发送new|replace给撮合前;修改数量|价格; 用execQty|execPrice保存原始值
    Cancel = 9; //撮合撤单; 用execQty保存实际撤掉的数量
    Replace = 10; //撮合修改订单数量|价格; 用execQty|execPrice保存原始值

}
