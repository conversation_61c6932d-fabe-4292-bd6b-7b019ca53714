syntax = "proto3";

package enums.ecombcrossstatus;
option go_package = "code.bydev.io/trading/idl/pb/enums/ecombcrossstatus";

enum CrossStatus {
    option allow_alias = true;
    Init = 0; // 这个用0值 是为了方便zero初始化后就是`Init`状态
    Deactivated = 20; // 因为关联条件变得不合法或不合理被废除的条件单
    WaitToSendNew = 13;
    Aborted = 1; // 发cross前被裁剪到0, 直接放弃掉新建订单
    PendingNew = 2; // 对新创建订单, 发起`x_create`挂单请求
    NewRejected = 3; // 新创建订单, 被撮合拒绝 (用reject_reason区分经过cross拒绝和条件单激活拒绝)
    NewAccepted = 4; // New_WaitFill=4, PF_WaitFill=5
    MakerFill = 14;
    TakerFill = 15;
    Expired = 16;
    ForceFill = 21; //强增强减
    PendingCancel = 6; // 对挂单成功订单, 发起`x_cancel`撤单请求
    CancelRejected = 7; // 撤单请求被拒绝
    Canceled = 8; // 撤单成功 (用reject_reason区分经过cross撤单和条件单激活前撤单)
    SmpCanceled = 22; // 防自撮合cancel taker处理(区别于expired), cancel maker直接走canceled逻辑
    MmpCanceled = 23; // 期权做市保护导致的批量撤单,多symbol的撤单
    WaitToSendReplace = 17;
    PrepareReplace = 17; // 作为WaitToSendReplace的别名. todo 后续去掉
    ReplaceNotModified = 18;
    PendingReplace = 9; // 对已部分成交的订单, 发起`x_replace`减qty请求
    ReplaceRejected = 10;
    ReplaceToCanceled = 19;
    Replaced = 11; // replace成功1: 减少qty, 订单在order_book中优先级不变
    ReAdded = 12; // replace成功2: 增加qty or 修改price, 订单在order_book中`重新排队挂单`
    // 被replace后的 `Replaced` 和 `JustReAdded` 状态是短暂的
    // 被撮合成交 or 被用户撤单/修改后. 这两个状态直接被覆盖掉
    WaitToSetFill = 24; // 组合订单成交之后等待单腿的撮合透传包，收到后改为 MakerFill/TakerFill
    WaitToSetCanceled = 25; // 组合订单撤单，还没收到单腿透传包的状态，收到后改为 Canceled
    WaitToSetReplaced = 26; // 组合改单的撮合回报，有在途单腿透传包时，设置成这个状态，然后再单腿收完透传包后改 Replaced
    WaitToSetReAdded = 27; // 组合改单的撮合回报，有在途单腿透传包时，设置成这个状态，然后再单腿收完透传包后改 ReAdded
}
