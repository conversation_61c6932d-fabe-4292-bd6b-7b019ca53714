syntax = "proto3";

package enums.ehdtsmessageheaderkey;
option go_package = "code.bydev.io/trading/idl/pb/enums/ehdtsmessageheaderkey";

/**
 * hdts result 下发消息的header常量定义
 */
 enum HdtsMessageHeaderKey {
    uid = 0; //uid
    aid = 1; //aid
    action = 2; //对应serviceAction
    ctype = 3; //对应业务类型（enums.eliqresult.MarginResultSendType）
    flushmode = 4; //
    accountVersion = 5; //账户版本号
    grpcUriType = 6; //请求uri type
    root_uid = 7; //请求root uid lending系统使用，hdts头传递下去
    parent_uid = 8; //请求母uid lending系统使用，hdts头传递下去
    member_relation_type = 9; //lending系统使用，hdts头传递下去
}
