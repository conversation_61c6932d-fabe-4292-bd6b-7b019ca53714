syntax = "proto3";

package enums.eopplatform;
option go_package = "code.bydev.io/trading/idl/pb/enums/eopplatform";


enum OpPlatform {
    option allow_alias = true;
    unknown = 0;
    UNKNOWN = 0;

    pc = 1; // 用户从PC站发起请求
    FromPC = 1;

    h5 = 2; // 用户从M站发起请求
    FromH5 = 2;

    api = 3; // 用户使用机构api发起请求
    // todo 如果要细分 api的各种来源的话, 考虑用 `api_xxx_yyy` 格式
    FromApi = 3;

    ios = 4; // 用户从苹果app发起请求
    FromIos = 4;

    android = 5; // 用户从安卓app发起请求
    FromAndroid = 5;

    fix = 6; // 通过fix协议发起请求
    FromFix = 6;

    sys = 7;
    FromSys = 7;

    // NEXT: 8
}
