syntax = "proto3";

package enums.ecoin;
option go_package = "code.bydev.io/trading/idl/pb/enums/ecoin";


enum Coin {
    UNKNOWN = 0;

    BTC = 1;
    ETH = 2;
    EOS = 3;
    XRP = 4;
    USDT = 5;
    DOT = 6;
    DOGE = 7;
    LTC = 8;
    TEST = 9;
    XLM = 10;
    USD = 11;
    BIT = 12;
    USDC = 16;

    DYDX = 13;
    MATIC = 14;
    DASH = 15;
    UNI = 17;
    SOL = 18;
    BCH = 19;
    ADA = 20;
    LINK = 21;
    ICP = 22;
    ETC = 23;
    LUNA = 24;
    THETA = 25;
    FIL = 26;
    AAVE = 27;
    XMR = 28;
    AXS = 29;
    GRT = 30;
    MKR = 31;
    KLAY = 32;
    ATOM = 33;
    AVAX = 34;
    XTZ = 35;
    COMP = 36;
    KSM = 37;
    CHZ = 38;
    WAVES = 39;
    XEM = 40;
    ZEC = 41;
    FLOW = 42;
    MANA = 43;
    SUSHI = 44;
    ENJ = 45;
    YFI = 46;
    SNX = 47;
    ZIL = 48;
    ZRX = 49;
    CRV = 50;
    ANKR = 51;
    PERP = 52;
    TLM = 53;
    PLA = 54;
    // 2021-08-24 - fast_online_currency add - end
    AGLD = 55;
    BAT = 56;
    OMG = 57;
    TRIBE = 58;
    QNT = 59;
    SRM = 60;
    REN = 61;
    AUDIO = 62;
    BNT = 63;
    SAND = 64;
    UMA = 65;
    COTI = 66;
    SXP = 67;
    DENT = 68;
    XYO = 69;
    INJ = 70;
    GALA = 71;
    FTM = 72;
    MINA = 73;
    HBAR = 74;
    NEAR = 75;
    ALGO = 76;
    CRO = 77;
    HOT = 78;
    CELR = 79;
    TEL = 80;
    // 2021-10-29 新增
    SHIB = 81;
    SPELL = 82;
    NU = 83;
    IMX = 84;
    GODS = 85;
    CBX = 86;
    FTT = 87;
    WOO = 88;
    C98 = 89;
    GAL = 90;
    CAKE = 91;
    ONE = 92;
    GENE = 93;
    XYM = 94;
    BICO = 95;
    PTU = 96;
    AMP = 97;
    Dtravel = 98;
    CEL = 99;
    NEXO = 100;
    ALPACA = 101;
    UST = 102;
    SLP = 103;
    SUPER = 104;
    GTC = 105;
    PSP = 106;
    KDA = 107;
    MOVR = 108;
    RUNE = 109;
    XDC = 110;
    STX = 111;
    AR =112;
    ICX = 113;
    EGLD = 114;
    PENDLE = 115;
    KMA = 116;
    // 2021-10-29 新增
    TRVL = 117;
    AVA = 118;
    CWAR = 119;
    // 2021-10-29 又新增
    ENS = 120 ;
    GM = 121 ;
    BNB = 122 ;
    CELO = 123 ;
    HT = 124 ;
    STETH = 125 ;
    LFW = 126 ;
}

//// https://github.com/yonilevy/crypto-currency-symbols
//func (c Coin) DescStr() string {
//	switch c {
//	case BTC:
//		return "₿"
//	case ETH:
//		return "Ξ"
//	case EOS:
//		return "EOS"
//	case XRP:
//		return "Ʀ"
//	default:
//		return ""
//	}
//}