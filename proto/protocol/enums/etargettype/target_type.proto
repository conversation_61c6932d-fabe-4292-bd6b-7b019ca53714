syntax = "proto3";

package enums.etargettype;
option go_package = "code.bydev.io/trading/idl/pb/enums/etargettype";


enum TargetType {
    UNKNOWN = 0;

    Price = 1; // Price
    MA20 = 2; // MA20
    MA50 = 3; // MA50
    MA200 = 4; // MA100
    EMA8 = 5; // EMA8
    EMA21 = 6; // EMA21
    EMA34 = 7; // EMA34
    Candles5 = 8; // Candles #5
    Candles10 = 9; // Candles #10
    Candles20 = 10; // Candles #20
    Value = 11; // Value
}

enum TargetIndex {
    PriceInd = 0;
    MaxPriceInd = 1;
    MinPriceInd = 2;
    MA20Ind = 3;
    MA50Ind = 4;
    MA200Ind  = 5;
    EMA8Ind = 6;
    EMA21Ind = 7;
    EMA34Ind = 8;
    Candles5HighInd = 9;
    Candles5LowInd = 10;
    Candles10HighInd = 11;
    Candles10LowInd = 12;
    Candles20HighInd = 13;
    Candles20LowInd = 14;
}
