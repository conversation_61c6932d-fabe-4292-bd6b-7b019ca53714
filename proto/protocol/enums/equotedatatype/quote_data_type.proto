syntax = "proto3";

package enums.equotedatatype;
option go_package = "code.bydev.io/trading/idl/pb/enums/equotedatatype";


enum QuoteDataType {
    UnKnown = 0;

    //instrument
    Instrument = 1;

    //recent_trade
    RecentTrade = 2;

    //OrderBook20
    OrderBook20 = 3;

    //OrderBook25
    OrderBook25 = 4;

    //OrderBook200
    OrderBook200 = 5;

    //index_quote_20
    IndexQuote20 = 6;

    //index_quote_200
    IndexQuote200 = 7;

    //Kline
    Candles = 8;
}

