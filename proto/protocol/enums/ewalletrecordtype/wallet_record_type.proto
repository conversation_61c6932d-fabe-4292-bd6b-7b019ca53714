syntax = "proto3";

package enums.ewalletrecordtype;
option go_package = "code.bydev.io/trading/idl/pb/enums/ewalletrecordtype";


enum WalletRecordType {
    UNKNOWN = 0;

    Deposit = 1; // 充值 (amount正数,加钱)
    Withdraw = 2; // 提现 (amount正数,减钱)
    RealisedPNL = 4; // 已结盈亏 (amount正数:赚钱 amount负数:亏钱)
    FundingFee = 5; // (无记录) 资金费用
    Commission = 6; // 佣金 (amount正数,加钱)
    Refund = 7; // 退款 (amount正数,加钱)
    GivenCash = 8; // 赠金 (amount负数:减钱) // 很早以前这个类型是赠金逻辑 amount有正有负
    GivenCashByInvited = 9; // 邀请逻辑的赠金 (amount正数,加钱)
    AffiliateWithdraw = 10; // 代理商返佣提现 (amount正数,加钱)
    GivenCashByFirstDeposit = 11; // 首冲赠金 (amount正数,加钱)
    GivenCashByRegiste = 12; // 注册赠金 (amount正数,加钱)
    GivenCashByOperation = 13; // 运营赠金 (amount正数,加钱)
    ExchangeOrderWithdraw = 14; // 资产兑换 （amount正数,减钱）
    ExchangeOrderDeposit = 15; // 资产兑换 （amount正数,加钱）
    CompensationDeposit = 16; // 赔偿支出
    TransferOrderDeposit = 17; // 合约云内部转账（amount正数,加钱）
    ReturnServiceCash = 18; // 返还手续费赠金
    GivenCashExpired = 19; //交易赠金过期（原赠金更名为体验金，即：交易赠金
    GivenCashByTakeProfitStopLoss = 20; // 体验止盈/止损单赠金
    TransferOrderWithdraw = 21; // 合约云内部转账（amount正数,减钱）
    GivenCashByTradingCompensation = 22; // 交易赔偿赠金
    TransferTradeToBXBAccount = 24; // 交易账户转到保险账户
    TransferBXBToTradeAccount = 25; // 保险账户转到交易账户
    GivenCashByInsurancePurchasing = 26; // 保险购买体验金
    SubMemberTransferWithdraw = 27; // 母子账号内部转账出金（amount正数，减钱）
    SubMemberTransferDeposits = 28; // 母子账号内部转账入金（amount正数，加钱）
    CreditCardECheckDeposit = 29; // 法币入金-ECheck入金（amount正数，加钱）
    CouponInterest = 30 ; //生息券收益
    AccountTransferDeposits = 31; // 账户互转转入到合约账户
    AccountTransferWithdraw = 32; // 账户互转从合约账户转出
    CashBack = 33; // 返现活动
    Mt4AccountTransferDeposits = 34; // 账户互转转入到合约账户
    Mt4AccountTransferWithdraw = 35; // 账户互转从合约账户转出
    ETPPreSaleProvisions = 36; // etp申购认购备付金 （加钱 转入到合约账户）
    ETPIpoOpenPosition = 37; // etp 申赎开仓 （加钱 转入到合约账户）
    ETPIpoSupplementaryMargin = 38; // etp 申购补充保证金  （加钱 转入到合约账户）
    ETPIpoRefundMargin = 39 ; // etp 申购退还保证金  （减钱 转出到合约账户）
    ETPExit = 40 ; // etp  USDT从机构合约账户划转到申赎系统账户  （减钱 转出到合约账户）
    ETPCarryFee = 41; // etp管理费收取   （减钱 转出到合约账户）
    ETPPreSaleOpenOption = 42; // etp认购开仓 （加钱 转入到合约账户）
    ETPPreSaleOpenOptionRefund = 43; // etp 认购开仓失败退回 （减钱 转出到合约账户）
    CopyTradeWithdraw = 44; // 跟单交易扣钱
    CopyTradeDeposits = 45; // 跟单交易加钱
    CopyTradePreDuctWithdraw = 46; // 跟单交易预扣除 follower扣钱
    CopyTradePreDuctDeposits = 47; // 跟单交易预扣除 中间户加钱
    CopyTradeShardsWithdraw = 48; //跟单交易分润 中间户扣钱
    CopyTradeShardsDeposits = 49; //跟单交易分润 交易员加钱
    CopyTradeRefundWithdraw = 50; //跟单交易返还 中间户扣钱
    CopyTradeRefundDeposits = 51; //跟单交易返还 跟随者加钱
    ChainRollbackDeduct = 52; //链回滚 追回用户资产 扣钱
    OffChainDeposit = 53; //本地站：线下入金 加钱
    ExchangeMiranaDeposit = 54; //兑换mirana 加钱
    ExchangeMiranaWithdraw = 55; //兑换mirana 扣钱
    FireBlocksS2C = 56;  // FireBlocks资金托管S2C划转 合约加钱
    FireBlocksC2S = 57;  // FireBlocks资金托管C2S 合约减钱

    // 网格交易使用
    BotTransferDeposit  = 61; // 合约网格划入
    BotTransferWithdraw = 62; // 合约网格划出

    AirDrop = 120;    // 空投加钱
    AirDropOut = 121; // 空投减钱

    // 机构借贷入账
    INSTITUTION_LOAN_IN = 1010;
    // 机构还款出账(本金)
    INSTITUTION_PAYBACK_PRINCIPAL_OUT = 1011;
    // 机构还款出账（利息）
    INSTITUTION_PAYBACK_INTEREST_OUT = 1012;
    // 机构OTC兑换卖出
    INSTITUTION_EXCHANGE_SELL = 1013;
    // 机构OTC兑换买入
    INSTITUTION_EXCHANGE_BUY = 1014;
    // 机构强平还款出账（本金）
    INSTITUTION_LIQ_PRINCIPAL_OUT=1015;
    // 机构强平还款出账（利息）
    INSTITUTION_LIQ_INTEREST_OUT=1016;
    // 大MM业务
    BIG_MM_AIRDROP = 1017;  //(1017, "BIG_MM_AIRDROP", "财务大MM空投加钱"),
    BIG_MM_AIRDROP_OUT = 1018;  //(1018, "BIG_MM_AIRDROP_OUT", "财务大MM空投减钱"),
    // 闪兑兑入
    EXCHANGE_IN=1019;
    // 闪兑兑出
    EXCHANGE_OUT=1020;
    // 机构借贷转入（LTV平仓后的系统转入）
    INSTITUTION_LOAN_TRANSFER_IN=1021;
    // 机构借贷转出（LTV平仓后的系统转出）
    INSTITUTION_LOAN_TRANSFER_OUT=1022;
    // 一键兑币买入
    SPOT_REPAYMENT_BUY=1023;
    // 一键兑币卖出
    SPOT_REPAYMENT_SELL=1024;
    // 机器人划转入
    BOT_TRANSFER_IN=1027;
    // 机器人划转出
    BOT_TRANSFER_OUT=1028;
    // 托管网络费划出
    CUSTODY_NETWORK_FEE_OUT = 1031;
    // 托管结算费划出
    CUSTODY_SETTLE_FEE_OUT = 1032;
    // 活期储蓄申购
    FLEXIBLE_STAKING_SUBSCRIPTION = 1033;
    // 活期储蓄赎回
    FLEXIBLE_STAKING_REDEMPTION = 1034;
    // 定期储蓄申购
    FIXED_STAKING_SUBSCRIPTION = 1035;

    PREMARKET_TRANSFER_IN = 1036;       //盘前交易质押金退还
    PREMARKET_TRANSFER_OUT = 1037;      //盘前交易质押金上账
    PREMARKET_DELIVERY_SELL_NEW_COIN = 1038; //交割新币(c2s)卖方新币划扣（-）
    PREMARKET_DELIVERY_BUY_NEW_COIN = 1039; //新币种划给买家(s2c)买方新币上账（+）
    PREMARKET_DELIVERY_PLEDGE_PAY_SELLER = 1040; //交割履约 -- 质押款（货款）交付给卖家(s2c)
    PREMARKET_DELIVERY_PLEDGE_BACK = 1041; //交割履约 -- 退回卖家质押币(s2c)
    PREMARKET_ROLLBACK_PLEDGE_BACK = 1042; //交割违约 -- 退还买家质押款(s2c)
    PREMARKET_ROLLBACK_PLEDGE_PENALTY_TO_BUYER = 1043; //交割违约 -- 违约金划给买家(s2c)
    PREMARKET_TAKER_FEE_TO_POOL = 1044; //吃单成功，吃单手续费上账(s2s)
    PREMARKET_MAKER_FEE_TO_POOL = 1045; //吃单成功，挂单手续费上账(s2s)
    PREMARKET_ROLLBACK_PLEDGE_PENALTY_TO_FUNDPOOL = 1046; //交割违约 -- 违约金划入资金池(s2s)

    LOANS_PLEDGE_TRANSFER_IN = 1047; //抵押品下账
    LOANS_PLEDGE_TRANSFER_OUT = 1048; //抵押品上账
    LOANS_BORROW_TRANSFER_IN = 1049; //借贷上账
    LOANS_REPAY_TRANSFER_OUT = 1050; //借贷下账

    BONUS_TRANSFER_IN = 1051; //体验金划入
    BONUS_TRANSFER_OUT = 1052; //体验金划出
    // 该枚举与proto3不一致， 如果业务方有要用到，需要去code.bydev.io/dbu:svc/proto3查看最新的枚举， 与资产同学确认，手动同步过来
}
