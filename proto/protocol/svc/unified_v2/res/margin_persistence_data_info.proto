syntax = "proto3";
package svc.unified_v2.res;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/res";

//保证金持久化消费对象
message MarginPersistenceDataInfo {
  optional int64 acceptTime = 1;// grpc请求受理时间（归档时时间判断）
  optional string topic = 2;// kafka/永续的topic
  optional int32 partition = 3;// 用户分片
  optional int64 minOffset = 4;// 期权kafka 重发offset
  optional int64 currentOffset = 5;// 当前请求offset
  optional int64 symbolId = 6;// symbol
  optional int64 corsSeqId = 7;// corsSeq
  optional int32 corsSeqIndex = 8;// corsSeq相同时，index
  optional int64 perpSequenceMark = 9; // 永续请求序列号，用于永续重发
  optional int64 crossIdx = 10; // crossIdx
  optional string walId = 11; // walId
  optional int32 settleCoinId = 12;// 结算币Id USDC/USDT/BTC/ETH
  optional string serviceName = 13;// 服务类型

  // Next: 13
}
