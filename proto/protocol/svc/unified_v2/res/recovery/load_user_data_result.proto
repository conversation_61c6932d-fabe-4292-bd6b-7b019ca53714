syntax = "proto3";

package svc.unified_v2.res.recovery;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/res/recovery";

import "models/dump/userdata.proto";
import "models/dump/summary.proto";

message LoadUserDataResult{
  models.dump.UserData user_data = 2;
  // 当dump由于容灾重启后数据后退至早于请求中提供的offset值时，此处填充dump此时的summary状态用于业务修正恢复状态
  models.dump.Summary  summary = 3;
  // 若UserData的size超大则需要分包，需要顺序把chunk_finished从false到true的所有包数据拼接起来才是完整的
  bytes user_data_chunk = 4;
  // 配合user_data_chunk，当true时表示UserData数据已发送完整
  bool chunk_finished = 5;
  // 用户是否存在于请求的node里, 仅请求参数skip_data是true时需要判断
  bool exist = 6;
}

message BatchLoadUserDataResult{
  // marshal from UserDataChunk and then zstd encoded;需要顺序把chunk_finished从false到true的所有包数据拼接起来解析
  bytes partial_userdata_chunk_zstd = 1;
  
  // repeated models.dump.UserData user_data_list = 2;

  // 当dump由于容灾重启后数据后退至早于请求中提供的offset值时，此处填充dump此时的summary状态用于业务修正恢复状态
  models.dump.Summary  summary = 3;

  // 配合partial_userdata_chunk_zstd，false表示当前包数据不完整，true时表示包数据已发送完整
  bool chunk_finished = 4;
  
  // 当前chunk序号 从1开始
  int32 chunk_index = 5;
  // 当前批次chunk总数
  int32 chunk_total_num = 6;
  // 当前batch数据总大小
  int64 batch_size = 7;
  // 当前批次序号 从1开始
  int32 batch_index = 8;
  // 批次总数
  int32 batch_total_num = 9;
}

message UserDataChunk {
  repeated models.dump.UserData user_data_list = 1;
}
