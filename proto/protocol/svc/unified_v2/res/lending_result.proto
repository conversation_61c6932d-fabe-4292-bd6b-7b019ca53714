syntax = "proto3";
package svc.unified_v2.res;
option go_package = "code.bydev.io/trading/idl/pb/svc/lending/res";

// 支持借贷（兑币 利率结算） lending
message LendingResult {

  // 借贷兑币请求相应ack
  UserExchangeCoinResult userExchangeCoinResult = 1;


};

// 借贷兑币请求相应ack
message UserExchangeCoinResult {

  // 兑币请求流水
  string requestId = 1;

  // 兑币处理结果状态
  string handleResult = 2;

  // 保证金处理发送时间
  int64 marginSendTime = 3;

  // 处理兑币的目标币种
  string lendingCoin = 4;

  /*兑换订单结果明细*/
  repeated ExchangeOrder exchangeOrder = 5;

}


// 借贷用户兑换订单结果信息
message ExchangeOrder {
  /*兑换流水号-exchangeId*/
  string exchangeId = 1;

  /**订单号*/
  string orderId = 2;

  /*原始币种*/
  string sellCoin = 3;

  /*卖币资金*/
  string sellMoney = 4;

  /*目标币种币种*/
  string buyCoin = 5;

  /*卖币资金*/
  string buyMoney = 6;

  /*兑换单价*/
  string exchangePrice = 7;
  /*兑换结果通知 - 未知兑换结果类型NOTICE_RESULT_UNKNOW 全部成功-SUCCESS_NOTICE 部分成功-PARTIAL_SUCCESS_NOTICE 全部失败-ALL_FAIL_NOTICE */
  string exchangeNoticeResult = 8;

  /*手续费*/
  string feeMoney = 9;

  /*是否刚兑*/
  bool isRigid = 10;

  /*手续费率*/
  string feeRate = 11;

  /*触发源*/
  string source = 12;

  /*卖币资金*/
  string sellMoneyUsd = 13;

  /*卖币资金*/
  string buyMoneyUsd = 14;

  /*兑换单类型 参考枚举：ExchangeOrderType*/
  ExchangeOrderType exchangeOrderType = 15;
}

enum ExchangeOrderType {
  // 所有类型
  DEFAULT = 0;

  // 自动兑换
  AUTO_EXCHANGE = 1;

  // 主动兑换
  MANUAL_EXCHANGE = 2;

  // OTC自动兑换
  OTC_AUTO_EXCHANGE = 3;

  // 一键还款
  ACTIVE_REPAYMENT_EXCHANGE = 4;
}