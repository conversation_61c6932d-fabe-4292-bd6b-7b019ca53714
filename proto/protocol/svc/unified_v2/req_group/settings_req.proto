syntax = "proto3";
package svc.unified_v2.req.group;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/group";

import "svc/unified_v2/req/settings/set_spot_collateral_coin.proto";
import "svc/unified_v2/req/settings/set_spot_leverage.proto";
import "svc/unified_v2/req/settings/switch_spot_margin_mode.proto";
import "svc/unified_v2/req/settings/label_code_sync.proto";
import "svc/unified_v2/req/settings/spot_set_fee_rate.proto";
import "svc/unified_v2/req/settings/spot_open_innovation.proto";
import "svc/unified_v2/req/settings/set_customer_trigger_liq.proto";
import "svc/unified_v2/req/settings/set_collateral_coin_mode.proto";
import "svc/unified_v2/req/settings/update_site_info.proto";
import "svc/unified_v2/req/settings/update_user_ban_status.proto";
import "svc/unified_v2/req/settings/set_pwm_trade_status.proto";

// ----- 设置相关action -----
message SettingsReqGroupBody {
  // 字段tag值跟`enums/eaction/action.proto`枚举定义保持一致
  // 需要先增加Action枚举定义, 再添加tag值对应的reqBody
  oneof req_group_body {
    // 现货保证金参数设置相关配置
    svc.unified_v2.req.settings.SetSpotCollateralCoinReq set_spot_collateral_coin = 150;
    svc.unified_v2.req.settings.SetSpotLeverageReq set_spot_leverage = 151;
    svc.unified_v2.req.settings.SwitchSpotMarginModeReq switch_spot_margin_mode = 152;

    //标签信息同步：labelCodeSync
    svc.unified_v2.req.settings.LabelCodeSyncReq labelCodeSyncReq = 153;

    // 现货设置费率请求
    svc.unified_v2.req.settings.SpotSetFeeRateReq spot_set_fee_rate_req = 154;

    svc.unified_v2.req.settings.SpotOpenInnovationReq spot_open_innovation_req = 155;

    svc.unified_v2.req.settings.SetCustomerTriggerLiqReq set_customer_trigger_liq = 98;
    // 设置抵押币模式
    svc.unified_v2.req.settings.SetCollateralCoinModeReq set_collateral_coin_mode_req = 157;
    // 更新站点
    svc.unified_v2.req.settings.UpdateSiteInfoReq  update_site_info_req = 229;
    // 更新用户封禁状态
    svc.unified_v2.req.settings.UpdateUserBanStatusReq update_user_ban_status_req = 243;
    // 设置PWM用户交易状态
    svc.unified_v2.req.settings.SetPWMTradeStatusReq set_pwm_trade_status_req = 245;
  }
}

