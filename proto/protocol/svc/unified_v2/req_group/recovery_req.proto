syntax = "proto3";
package svc.unified_v2.req.group;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/group";

import "svc/unified_v2/req/recovery/batch_user_inject.proto";
import "svc/unified_v2/req/recovery/connect_stream.proto";
// import "svc/unified_v2/req/recovery/load_user_data.proto";
import "svc/unified_v2/req/recovery/load_shard_data.proto";
// import "svc/unified_v2/req/recovery/load_summary.proto";

// ----- 主备数据同步相关action -----
message RecoveryReqGroupBody {
  // 字段tag值跟`enums/eaction/action.proto`枚举定义保持一致
  // 需要先增加Action枚举定义, 再添加tag值对应的reqBody
  oneof req_group_body {
    // svc.unified_v2.req.recovery.LoadUserDataReq load_user_data_req = 81;
    //
//    svc.unified_v2.UnifiedV2ResultDTO on_recv_trading_result = 46;
    //
    //svc.unified_v2.req.recovery.MarginResult on_recv_margin_result = 103;
    //svc.unified_v2.req.recovery.UnifiedMarginResult on_recv_unified_margin_result = 135;
    // svc.unified_v2.req.recovery.LoadSummaryReq load_summary_req = 173;
    svc.unified_v2.req.recovery.LoadShardDataReq load_shard_data_req = 1024;
    svc.unified_v2.req.recovery.ConnectStreamReq connect_stream = 1026;
    svc.unified_v2.req.recovery.BatchUserInjectFinishedReq batch_user_inject_finished_req = 1089;
  }
}

