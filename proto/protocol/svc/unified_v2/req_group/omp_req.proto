syntax = "proto3";
option java_multiple_files = true;
package svc.unified_v2.req.group;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/group";
option java_package = "svc.unifiedv2.req_group";
option java_outer_classname = "OmpReqGroupBodyProto";

import "svc/unified_v2/req/margin/sync_omp_data_to_margin.proto";

// ----- 运营相关action -----
message OmpReqGroupBody{
  // 字段tag值跟`enums/eaction/action.proto`枚举定义保持一致
  // 需要先增加Action枚举定义, 再添加tag值对应的reqBody
  oneof req_group_body {
    // 同步持仓数据和订单到保证金
    svc.unified_v2.req.margin.SyncOmpDataToMargin sync_omp_data_to_margin = 104;
  }
}

