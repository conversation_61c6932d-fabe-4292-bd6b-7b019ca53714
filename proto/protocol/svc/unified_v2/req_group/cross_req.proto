syntax = "proto3";
package svc.unified_v2.req.group;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/group";

import "models/matchingdto/matching_result_dto.proto";
//import "svc/unified_v2/req/cross/settle_funding_fee.proto";
//import "svc/unified_v2/req/cross/rotate_realised_pnl.proto";
//import "svc/unified_v2/req/cross/mark_price_update.proto";
import "svc/unified_v2/res/x_seq_mark_info.proto";

// ----- 撮合成交回报+透传包相关action -----
message CrossReqGroupBody {
  // 字段tag值跟`enums/eaction/action.proto`枚举定义保持一致
  // 需要先增加Action枚举定义, 再添加tag值对应的reqBody
  oneof req_group_body {
    models.matchingdto.MatchingResultDTO on_recv_matching_result = 57;
    // 输出action:
    // => execution_report = 32; // 将撮合结果(new/cancel/replace)更新到持仓
    // => liq_take_over_pz = 12; // 强平将持仓全量接管走
    // => adl_reduce_pz = 44; // 被adl导致持仓降低
    // => settle_funding_fee = 28; // 每8小时结算funding_fee
    // `-> rotate_realised_pnl = 19; // 每天00:00做完funding后做当日盈亏归档
    // => settle_future_contract = 45; // 到期合约统一清算
//    svc.unified_v2.req.cross.SettleFundingFeeReq settle_funding_fee = 28; // 先兼容存在, 从撮合转一圈结算funding后去掉
//    svc.unified_v2.req.cross.RotateRealisedPnlReq rotate_realised_pnl = 19; // 先兼容存在, 改成00:00的funding后自动归档后去掉
    // svc.trading.req.ReconciliationReq reconciliation = 71;
    //
//    svc.unified_v2.req.cross.MarkPriceUpdateReq on_mark_price_update = 35;


    // xSeqMark透传包
    svc.unified_v2.res.XSeqMarkInfo x_seq_mark_info = 48;
  }
}

