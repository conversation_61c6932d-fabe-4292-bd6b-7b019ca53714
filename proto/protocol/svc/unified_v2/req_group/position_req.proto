syntax = "proto3";
package svc.unified_v2.req.group;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/group";

//import "svc/unified_v2/req/position/add_margin.proto";
//import "svc/unified_v2/req/position/force_switch_position_mode.proto";
//import "svc/unified_v2/req/position/set_auto_add_margin.proto";
//import "svc/unified_v2/req/position/set_fee_rate.proto";
import "svc/unified_v2/req/position/set_leverage.proto";
//import "svc/unified_v2/req/position/set_leverage_with_symbols.proto";
//import "svc/unified_v2/req/position/set_margin.proto";
//import "svc/unified_v2/req/position/set_open_limit.proto";
import "svc/unified_v2/req/position/set_risk_id.proto";
//import "svc/unified_v2/req/position/switch_isolated.proto";
//import "svc/unified_v2/req/position/switch_isolated_with_symbols.proto";
//import "svc/unified_v2/req/position/switch_position_mode.proto";
//import "svc/unified_v2/req/position/switch_position_mode_with_coin.proto";
//import "svc/unified_v2/req/position/switch_position_mode_with_symbols.proto";
import "svc/unified_v2/req/position/switch_tp_sl_mode.proto";
//import "svc/unified_v2/req/position/update_adl_rank_indicator.proto";
import "svc/unified_v2/req/position/position_migration.proto";

// ----- 持仓相关action -----
message PositionReqGroupBody {
  // 字段tag值跟`enums/eaction/action.proto`枚举定义保持一致
  // 需要先增加Action枚举定义, 再添加tag值对应的reqBody
  oneof req_group_body {
    svc.unified_v2.req.position.SetLeverageReq set_leverage = 20;
//    svc.unified_v2.req.position.SetMarginReq set_margin = 21;
//    svc.unified_v2.req.position.AddMarginReq add_margin = 22;
    svc.unified_v2.req.position.SetRiskIdReq set_risk_id = 23;
//    svc.unified_v2.req.position.SetAutoAddMarginReq set_auto_add_margin = 24;
//    svc.unified_v2.req.position.UpdateAdlRankIndicatorReq update_adl_rank_indicator = 26; // 原命名: deleverage_indicator
    // update_position_status = 27;
//    svc.unified_v2.req.position.SwitchIsolatedReq switch_isolated = 31;
//    svc.unified_v2.req.position.SetOpenLimitReq set_open_limit = 33;
//    svc.unified_v2.req.position.SwitchPositionModeReq switch_position_mode = 54;
//    svc.unified_v2.req.position.SwitchPositionModeWithCoinReq switch_position_mode_with_coin = 140;
//    svc.unified_v2.req.position.ForceSwitchPositionModeReq force_switch_position_mode = 137;
//    svc.unified_v2.req.position.SetLeverageWithSymbolsReq set_leverage_with_symbols = 123;
//    svc.unified_v2.req.position.SwitchIsolatedWithSymbolsReq switch_isolated_with_symbols = 124;
//    svc.unified_v2.req.position.SwitchPositionModeWithSymbolsReq switch_position_mode_with_symbols = 125;
    svc.unified_v2.req.position.SwitchTpSlModeReq switch_tp_sl_mode = 49;
//    svc.unified_v2.req.position.SetFeeRateReq set_fee_rate = 25;
    svc.unified_v2.req.position.PositionMigrationReq position_migration = 27;

  }
}

