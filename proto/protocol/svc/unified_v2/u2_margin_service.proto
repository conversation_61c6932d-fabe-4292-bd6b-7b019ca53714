syntax = "proto3";

package svc.unified_v2;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2";

import "google/protobuf/any.proto";
import "svc/req_resp_header.proto";

import "svc/unified_v2/unified_v2_request_dto.proto";
import "svc/unified_v2/unified_v2_result_dto.proto";

service U2MarginService {
  // req:resp一比一处理
  rpc Process (UnifiedV2RequestDTO) returns (UnifiedV2ResultDTO);

  // req:resp是n:m对应
  rpc Subscribe (stream UnifiedV2RequestDTO) returns (stream UnifiedV2ResultDTO);
}
