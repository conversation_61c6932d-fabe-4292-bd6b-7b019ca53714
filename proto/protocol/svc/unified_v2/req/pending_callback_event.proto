syntax = "proto3";

package svc.unified_v2.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req";

import "svc/req_resp_header.proto";

import "svc/unified_v2/req/liq/liq_request.proto";


// 对于 期货+期权 业务, 可能会有margin服务触发强平等相关回调操作
message PendingCallbackEvent {
  svc.ReqRespHeader req_header = 2001;

  // 字段tag值跟Action枚举定义保持一致
  // 需要先增加Action枚举定义, 再添加req
  oneof req_body {
    svc.unified_v2.req.liq.LiqRequestReq liq_request = 114;
  }
};
