syntax = "proto3";

package svc.unified_v2.req.order;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/order";
option java_package = "com.bybit.option.margin.unifiedv2.req.order";

// import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";
import "enums/ecreatetype/create_type.proto";
import "enums/etriggerby/trigger_by.proto";
import "enums/eside/side.proto";
import "enums/eordertype/order_type.proto";
import "enums/etimeinforce/time_in_force.proto";
import "enums/eqtytype/qty_type.proto";
import "enums/estopordertype/stop_order_type.proto";
import "enums/epricedirection/price_direction.proto";
// import "enums/epegpricetype/peg_price_type.proto";

message PreCreateOrderReq {
    // int64 user_id = 1;
    // enums.ecoin.Coin coin = 2;
    enums.esymbol.Symbol symbol = 3;
    int64 position_idx = 4; // 0:单持仓, 1:双向的多仓, 2:双向的空仓

    enums.ecreatetype.CreateType create_type = 5;
    string remark = 6;
    string order_link_id = 7;
    enums.etriggerby.TriggerBy tp_trigger_by = 8;
    int64 take_profit_x = 9;
    enums.etriggerby.TriggerBy sl_trigger_by = 10;
    int64 stop_loss_x = 11;

    string order_id = 12; // UUIDv4: char[16]
    enums.eside.Side side = 13;
    enums.eordertype.OrderType order_type = 14;
    enums.etimeinforce.TimeInForce time_in_force = 15;

    int64 price_x = 16;
    int64 qty_x = 17;

    enums.eqtytype.QtyType qty_type = 30; // 下单类型 2百分比 1新版本市价单 0老版本市价单（包括市价100%下单）
    int64 qty_type_v = 31; // 依据下单类型的数量
    int64 designated_margin_e8 = 32; // 依据下单类型的指定保证金的数量
    
    bool reduce_only = 18; // execInst:0x1
    bool close_on_trigger = 19; // execInst:0x10
    bool skip_validate_risk_limit = 20; // execInst:0x100
    bool trading_banned = 21; // execInst:0x1000

    enums.estopordertype.StopOrderType stop_order_type = 22;
    enums.etriggerby.TriggerBy trigger_by = 23;
    enums.epricedirection.PriceDirection expected_direction = 24;
    // enums.epegpricetype.PegPriceType peg_price_type = 25;
    int64 base_price_x = 26;
    int64 trail_value_x = 27; // peg_offset_value_x
    int64 trigger_price_x = 28;

    string pre_create_id = 29; // 预下单session_id, 过期失效, 避免预计算结果和实际下单结果差异过大

    int32 price_scale = 33;

    // NEXT: 34
}
