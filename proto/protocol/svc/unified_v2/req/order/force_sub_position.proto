syntax = "proto3";

package svc.unified_v2.req.order;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/order";
option java_package = "com.bybit.option.margin.unifiedv2.req.order";

import "enums/esymbol/symbol.proto";
import "enums/eside/side.proto";
import "enums/elastliquidityind/last_liquidity_ind.proto";

message ForceSubPositionReq {
    //NEXT： 8
    //int64 user_id = 1;
    //enums.ecoin.Coin coin = 2;
    enums.esymbol.Symbol symbol = 3;
    int64 position_idx = 4; // 0:单持仓, 1:双向的多仓, 2:双向的空仓
    enums.eside.Side subed_side = 5; //仓位减少的方向
    int64 subed_size_x = 6;   //减少的仓位大小 必须大于0
    int64 exec_price_x = 7;   //减少的仓位入场价
    enums.elastliquidityind.LastLiquidityInd last_liquidity_ind = 8; //强增强减时需要根据缺少的交易来去确定是taker还是maker
    bool reduce_only = 9; //只减仓，带了该标志，就不允许出现反向持仓

    int32 price_scale = 10;
}