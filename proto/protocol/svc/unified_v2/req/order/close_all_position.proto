syntax = "proto3";

package svc.unified_v2.req.order;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/order";
option java_package = "com.bybit.option.margin.unifiedv2.req.order";

import "enums/esymbol/symbol.proto";
import "enums/ecreatetype/create_type.proto";

message CloseAllPositionReq {
  // int64 user_id = 1;
  // enums.ecoin.Coin coin = 2;
  enums.esymbol.Symbol symbol = 3; // 不传symbol，平coin下所有仓位，传symbol，根据position_idx进行平仓
  int64 position_idx = 4; // -1:双仓全部仓位, 0:单持仓, 1:双向的多仓, 2:双向的空仓

  enums.ecreatetype.CreateType create_type = 5;
  // NEXT: 6
}
