syntax = "proto3";

package svc.unified_v2.req.order;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/order";
option java_package = "com.bybit.option.margin.unifiedv2.req.order";

// import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";
import "enums/etriggerby/trigger_by.proto";

message SetTpSlTsReq {
    // int64 user_id = 1;
    // enums.ecoin.Coin coin = 2;
    enums.esymbol.Symbol symbol = 3;
    int64 position_idx = 4; // 0:单持仓, 1:双向的多仓, 2:双向的空仓

    // 用wrappers的Int64Value来区分`is_set`:
    // nil ~ 不做任何修改
    // 0 ~ 清除 锚定仓位的止盈/止损/追踪止损
    // x ~ 新设置or更新 锚定仓位的止盈/止损/追踪止损

    enums.etriggerby.TriggerBy new_tp_trigger_by = 5; // 默认按LastPrice
    oneof optional_new_tp {
        int64 new_take_profit_x = 6;
    }

    enums.etriggerby.TriggerBy new_sl_trigger_by = 7; // 默认按LastPrice
    oneof optional_new_sl {
        int64 new_stop_loss_x = 8;
    }

    enums.etriggerby.TriggerBy new_ts_trigger_by = 9; // 默认按LastPrice
    oneof optional_new_ts {
        int64 new_trailing_stop_x = 10;
    }
    int64 new_activation_price_x = 11; // 传0:按追踪止损处理, 传x:按追踪止盈处理

    oneof optional_tp_size {
        int64 new_tp_size_x  = 12; //设置止盈的数量 只用于部分止盈止损模式下
    }

    oneof optional_sl_size {
        int64 new_sl_size_x  = 13;//设置止损的数量 只用于部分止盈止损模式下
    }

    string tp_stop_order_id = 14; //修改止盈条件单订单ID 只用于部分止盈止损模式下
    string sl_stop_order_id = 15;//设置止损条件单订单ID 只用于部分止盈止损模式下

    int32 price_scale = 16;
    // NEXT: 17
}
