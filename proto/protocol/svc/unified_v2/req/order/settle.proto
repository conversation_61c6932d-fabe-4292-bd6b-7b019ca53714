syntax = "proto3";

package svc.unified_v2.req.order;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/order";
option java_package = "com.bybit.option.margin.unifiedv2.req.order";

// import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";

//交割
message SettleReq {
    //int64 user_id = 1;
    //enums.ecoin.Coin coin = 2;
    enums.esymbol.Symbol symbol = 3;
    int64 settle_price_x = 4; // 交割价格
    int64 settle_time_e9 = 5; // 交割时间
    int64 settle_fee_rate_e8 = 6; // 交割手续费率
    int64 cross_seq = 7; // 交割请求对应撮合定序号

    int32 price_scale = 8;
}

