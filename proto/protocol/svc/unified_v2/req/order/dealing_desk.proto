syntax = "proto3";

package svc.unified_v2.req.order;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/order";
option java_package = "com.bybit.option.margin.unifiedv2.req.order";

import "enums/esymbol/symbol.proto";
import "enums/eside/side.proto";
import "enums/eordertype/order_type.proto";

// 用户只为taker
message DealingDeskReq {
    //int64 user_id = 1;
    //enums.ecoin.Coin coin = 2;
    enums.esymbol.Symbol symbol = 3;

    int64 maker_user_id = 4;
    string maker_order_id = 5;
    bool need_fill = 6;
    bool need_new = 7;
    int64 fill_price_x = 8;
    int32 price_scale = 9;
    //NEXT： 10
}