syntax = "proto3";

package svc.unified_v2.req.recovery;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/recovery";

message DumpStreamReq {
  SystemInfo system_info = 1;
}

message SystemInfo {
  enum UserDataPackage {
    Single = 0;
    Batch = 1;
    BatchZstd = 2;
  }
  string session_id = 1;
  UserDataPackage user_data_package = 2;
  bool is_recovering = 3;
}

message SystemBootReq{}

message RecoveryFinishReq{}
