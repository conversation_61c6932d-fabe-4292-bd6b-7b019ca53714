syntax = "proto3";

package svc.unified_v2.req.position;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/position";

import "enums/esymbol/symbol.proto";
import "enums/etpslmode/tp_sl_mode.proto";

message SwitchTpSlModeReq {
    // 用户ID
    int64 user_id = 1;

    // 合约
    enums.esymbol.Symbol symbol = 2;

    // 止盈止损模式（0，1全量 ,2 部分）
    enums.etpslmode.TpSlMode tp_sl_mode = 3;
};