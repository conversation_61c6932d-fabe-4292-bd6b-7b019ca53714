syntax = "proto3";

package svc.unified_v2.req.position;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/position";

// import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";

message SetRiskIdReq {
    // int64 user_id = 1;
    // enums.ecoin.Coin coin = 2;
    enums.esymbol.Symbol symbol = 3;
    int64 position_idx = 4; // 0:单持仓, 1:双向的多仓, 2:双向的空仓

    int64 new_risk_id = 5;

    // NEXT: 6
}
