syntax = "proto3";
package svc.unified_v2.req.lending;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/lending";


message LendingRequest {

  // 借贷兑币请求
  repeated UserExchangeCoinRequest repaymentUserExchangeRequest = 1;

  // 借贷利息
  repeated InterestUserRequest interestUserRequest = 2;

  // 机构清算专用-是否变卖非保证金币种（默认不变卖非保证金币种）
  bool sellNonMarginCoin = 3;

  // 机构清算专用-是否刚兑（true，会变卖非保证金币种，资产不足自动刚兑，最后直接将负债抹零，sell：0USDT，buy：XXcoin）
  bool rigid = 4;

  // 机构清算专用-借贷产品线
  int32 loanProductLine = 5;

  // 触发规则
  string riskRule = 6;
}

message UserExchangeCoinRequest {

  /**
   * 请求幂等
   */
  string requestId = 1;

  /**
   * 需要兑币目标金额
   */
  string amount = 2;

  /**
   * 兑币目标币种
   */
  int32 coin = 3;

  /**
   * 发送时间
   */
  int64 sendTime = 4;

  /**
   * 风控规则计算请求参数
   */
  string originCalRequest = 5;

  /**
   * 触发风控规则信息
   */
  string riskRuleInfo = 6;

  /*兑换订单order请求明细*/
  repeated ExchangeOrder exchangeOrder = 7;

  /**
   * 扩展信息
   */
  string extInfo = 8;
}


// 借贷用户兑换流水
message ExchangeOrder {
  /**
 * 兑换流水号-exchangeId
 */
  string exchangeId = 1;

  /** */
  string orderId = 2;

  /*原始币种*/
  int32 sellCoin = 3;

  /*卖币资金*/
  string sellMoney = 4;

  /*目标币种币种*/
  string buyCoin = 5;

  /*卖币资金*/
  string buyMoney = 6;

  /*兑换单价*/
  string exchangePrice = 7;

  /*手续费*/
  string feeMoney = 8;

  /*兑换结果通知 - INIT(1, "初始化"),
PARTIALLY_FILLED(2, "部分成交"),
FILLED(3, "成交"),
FAILED(4, "失败"),
CANCELED(5, "取消"); */
  string exchangeNoticeResult = 9;

}

/**
 * 利息账单模型
 */
message InterestUserRequest {
  /**
   * 请求ID
   */
  string requestId = 1;

  /**
   * 利息类型
   * exemptedInterest - 豁免利息； actualInterest - 扣减利息
   */
  string interestType = 2;

  /**
   * 利息金额
   */
  string amount = 3;

  /**
   * 利息结息币种
   */
  int32 settleCoin = 4;

  /**
   * 利息结算equity快照
   */
  string equitySnapshot = 5;

  /**
   * 免息额快照
   */
  string interestFreeSnapshot = 6;

  /**
   * 负债快照
   */
  string liabilitySnapshot = 7;

  /**
   * 借币上限
   */
  string borrowingLimit = 8;

  /**
   * 处理时间
   */
  int64 handleTime = 9;

  /**
   * 计息base
   */
  string interestBase = 10;

  /**
   * 小时级别利率
   */
  string interestRateByHour = 11;

  /**
   * 切片展示利率时间
   */
  int64 interestViewTime = 12;

  /**
   * 保留扩展字段
   */
  string extInfo = 13;

  /**
   * 免除的利息
   */
  string exemptedInterestAmount = 14;

  /**
   * 借贷的浮亏
   */
  string lendingUnrealisedLoss = 15;

  /**
   * 免息借款额
   */
  string freeBorrowingAmount = 16;

  string fixedBorrowUsed = 17; // 固定额度使用

}


// user summary
message SharedGroupLoanLimit {
  // uid:UserSharedLoans
  map<int64, PerUserSharedLimit> per_user_remaining_limits = 1;
  // 源分片信息
  string source_partition = 2;
}

// PerUserSharedLimit=groupSharingLimit-groupOtherUserUsed
message PerUserSharedLimit {
  // message time
  int64 message_time = 1;
  // group user's parent uid
  int64 group_id = 2;
  // coin:remaining group loan limit (exclude current user)，scale E8
  map<int32, int64> per_coin_remaining_limit_e8 = 3;
  map<int32, string> per_coin_fix_borrow_all_liability = 4;
}
