syntax = "proto3";

package svc.unified_v2.req.wallet;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/wallet";


// 钱包请求参数
message UnifyAccountRequest {
    int64 transTime = 2;// 业务受理时间戳，UTC毫秒级时间戳即可，划转日志记录使用，如不传使用本地受理时间
    string transId = 3;// 资产系统流水号
    int64 transAmountX = 4;// 划转金额，trans_amount为18位小数精度的字符串
    string freezeSeq = 5;// 冻结流水号，请求冻结时生成，解冻或转出时必传
    int32 quoteCoin   = 6;// 标价货币 USD/USDC/...
    repeated int32  settleCoins = 7; // 创建账户时传入(USDC，USDT，BTC，ETH)-统保
    //  com.bybit.PerUserTradingData userTradingData = 8; // 仓位维度需要保留的设置,结算币维度需要保留的设置-统保创建后一次性传入
    int64 banStatus = 10;// 封禁模式 BAN_ACCOUNT时需要传入 参考RiskLevelEnum
    int32 transSubType = 11; // 子类型
    string bonus = 12;// 体验金最终值，进同步用
    int64 version = 13;
    int32 priceScale = 14;// 价格缩放倍数

}
