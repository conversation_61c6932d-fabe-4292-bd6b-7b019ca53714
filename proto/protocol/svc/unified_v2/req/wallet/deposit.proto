syntax = "proto3";

package svc.unified_v2.req.wallet;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/wallet";

// import "enums/ecoin/coin.proto";

import "models/walletrecorddto/wallet_record_dto.proto";

message DepositReq {
    // int64 user_id = 1;
    // enums.ecoin.Coin coin = 2;
    // enums.esymbol.Symbol symbol = 3;
    // int64 position_idx = 4; // 0:单持仓, 1:双向的多仓, 2:双向的空仓

    string transId = 1;// 资产系统流水号
    int32 coin = 2; //币种
    int32 wallet_record_type = 3;// 参考wallet_record_type.proto，后续新增类型由资产侧维护
    string amount = 4;// 划转金额
    string bonusChange = 5; // 赠金变化
    bool upgradeWithPosition = 6;// true是Uta升级划转，不校验金额参数
    string transfer_extra_info = 7; // 划转专用扩展信息
}
