syntax = "proto3";

package svc.unified_v2.req.wallet;

import "models/tradingdto/position_dto.proto";
import "models/usersettingdto/per_coin_user_setting_dto.proto";
import "models/walletdto/unify_wallet.proto";
import "enums/eaccountmode/account_mode.proto";

option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/wallet";

// 请求参数
message MergeSettingPositionReq {
    map<int32 /*coin*/, models.usersettingdto.PerCoinUserSettingDTO> future_per_coin_setting = 1;
    repeated models.tradingdto.PositionDTO future_positions = 2;
}


message utaAccountUpgradeTrailReq {
    map<int32 /*coin*/, models.usersettingdto.PerCoinUserSettingDTO> future_per_coin_setting = 1;
    repeated models.tradingdto.PositionDTO future_positions = 2;
    repeated models.walletdto.UnifyWallet wallet_list = 3;
    enums.eaccountmode.AccountMode accountMode = 4;
}




