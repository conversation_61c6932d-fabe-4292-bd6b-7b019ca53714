syntax = "proto3";

package svc.unified_v2.req.wallet;

option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/wallet";

message UpdateAccountInfoReq {
    int64 root_uid = 1;                                 // lending系统使用，hdts头传递下去
    int64 parent_uid = 2;                               // lending系统使用，hdts头传递下去
    int64 member_relation_type = 3;                     // lending系统使用，hdts头传递下去
}
