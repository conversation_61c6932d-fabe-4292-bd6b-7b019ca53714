syntax = "proto3";
package svc.unified_v2.req.settings;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/settings";

//单币种可抵押配置
message CollateralCoin {
  int32 coin = 1;  //coin id
  bool enable = 2; //true表示打开，false表示关闭
}
// 设置用户现货抵押币种
message SetSpotCollateralCoinReq {
  bool forceRemoveAllUserConfig = 1; // 传true的时候强制失效所有用户配置, 忽略coinList参数。传false时则处理coinList配置
  repeated CollateralCoin coinList = 2;
}