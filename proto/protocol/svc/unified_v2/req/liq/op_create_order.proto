syntax = "proto3";

package svc.unified_v2.req.liq;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/liq";

message OpCreateOrderReq {
    int32 symbol = 1;
    int64 position_idx = 2; // 0:单持仓, 1:双向的多仓, 2:双向的空仓
    int32 create_type = 3;
    string remark = 4;
    string order_link_id = 5;
    int32 tp_trigger_by = 6;
    int64 take_profit_x = 7;
    int32 sl_trigger_by = 8;
    int64 stop_loss_x = 9;
    string order_id = 10; // UUIDv4: char[16]
    int32 side = 11; // 0:None; 1:Buy; 2:Sell;
    int32 order_type = 12;
    int32 time_in_force = 13;
    int64 price_x = 14;
    int64 qty_x = 15;
    int32 qty_type = 16; // 下单类型 2百分比 1新版本市价单 0老版本市价单（包括市价100%下单）
    int64 qty_type_value = 17; // 依据下单类型的数量
    int64 designated_margin_e8 = 18; // 依据下单类型的指定保证金的数量
    bool reduce_only = 19; // execInst:0x1
    bool close_on_trigger = 20; // execInst:0x10
    bool skip_validate_risk_limit = 21; // execInst:0x100
    bool trading_banned = 22; // execInst:0x1000
    bool skip_revise_price = 23; // execInst:0x10000 即使超出限价范围也不做修正
    int32 stop_order_type = 24;
    int32 trigger_by = 25;
    int32 expected_direction = 26;
    int64 base_price_x = 27;
    int64 trail_value_x = 28; // peg_offset_value_x
    int64 trigger_price_x = 29; // 是否需要试算 true表示需要试算 默认false
    bool pm_trial = 30;
    bool mmp_enabled = 31; // MMP开关, true表示开启做市商保护 默认false
    // NEXT: 32
}
