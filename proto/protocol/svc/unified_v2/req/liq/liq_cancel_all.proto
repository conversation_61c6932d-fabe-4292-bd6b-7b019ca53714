syntax = "proto3";
package svc.unified_v2.req.liq;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/liq";

//message LiqCancelAllOrdReq {
//  int32 symbol_id = 1; // 0 表示取消该coin下的所有订单
//  int32 cancel_all_type = 2; // 取消订单的类型 0:取消所有活动单(兼容stop_order_type) 1:取消全部条件单(兼容stop_order_type)  2:取消全部止盈止损条件单  3：取消全部普通条件单与追踪止损条件单  4：取消所有开仓单(非RO|COT的条件单和活动单)  5：取消所有开启MMP的订单
//  int32 cancel_type = 3; // // 9: 普通用户被强平前, 统一撤单 <前序撤单> // 2: 普通用户被强平前, 统一撤单 <最后一笔>
//  string remark = 4;
//  // NEXT: 4
//};
