syntax = "proto3";

package svc.unified_v2.req.liq;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/liq";

message LiqSetRiskId {
    string request_id = 1; // 请求ID，用于请求幂等
    int64 user_id = 2; // 用户ID
    int64 symbol_id = 3; // 合约id
    int64 position_idx = 4; // 仓位编号 0-单向持仓模式 1-双仓buy 2-双仓sell
    int64 risk_id = 5; // 风险等级

    // NEXT: 6
}

message LiqBatchSetRiskId {
    int64 user_id = 1;

    repeated LiqSetRiskId set_risk_id_list = 2;
    // NEXT: 3
}