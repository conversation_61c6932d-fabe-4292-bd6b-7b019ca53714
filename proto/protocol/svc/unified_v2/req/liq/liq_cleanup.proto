syntax = "proto3";

package svc.unified_v2.req.liq;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/liq";

// import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";

message LiqCleanupReq {
    // int64 user_id = 1;
    // enums.ecoin.Coin coin = 2;
    enums.esymbol.Symbol symbol = 3;
    int64 position_idx = 4; // 0:单持仓, 1:双向的多仓, 2:双向的空仓

    // 查找到持仓后, 用最新持仓状态再次验证是否还需要执行liq_cleanup
    int64 orig_cross_seq = 5;
    int64 orig_data_ver = 6;

    string cleanup_remark = 7;

    // NEXT: 8
}