syntax = "proto3";

package svc.unified_v2.req.liq;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/liq";

// import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";
import "enums/eside/side.proto";

message AdlExecuteReq {
    // int64 user_id = 1;
    // enums.ecoin.Coin coin = 2;
    enums.esymbol.Symbol symbol = 3;
    int64 position_idx = 4; // 0:单持仓, 1:双向的多仓, 2:双向的空仓

    // 查找到持仓后, 用最新持仓状态再次验证是否真的执行adl
    int64 orig_cross_seq = 5;
    int64 orig_data_ver = 6;

    bool skip_cancel_all = 7; // 特殊用户被adl时,强制执行无需前置cancel_all. 直接下一个CloseOnTrigger的adl订单
    bool fulfill_want_qty = 8; // 保证满足全量want_qty, 即使会导致反向持仓 (但如果会出现反向持仓后立即强平则放弃本次执行)

    string adl_order_id = 9;
    int64 taken_over_position_idx = 10;
    enums.eside.Side taken_over_position_side = 11;
    int64 want_price_x = 12; // 如果被选中持仓会被减穿仓, 则放弃本次执行 重新选持仓
    int64 want_qty_x = 13; // 取min(want_qty_x, pz.size_x), 另外考虑特殊用户的remain_qty_x
    string adl_remark = 14;

    int32 price_scale = 15;
    // NEXT: 16
}
