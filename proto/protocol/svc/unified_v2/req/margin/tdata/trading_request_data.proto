syntax = "proto3";

package svc.unified_v2.req.margin.tdata;
option go_package = "code.bydev.io/trading/idl/pb/svc/unifiedv2/req/margin/tdata";
option java_package = "svc.unifiedv2.req.margin.tdata";

//import "models/tradingdto/position_dto.proto";
import "models/tradingdto/transact_dto.proto";
import "models/tradingdto/closed_pnl_dto.proto";
import "models/tradingdto/position_dto.proto";
import "models/liqadldtov2/liq_record_dto.proto";
import "models/liqadldtov2/adl_record_dto.proto";
//import "models/contractdto/contract_dto.proto";
import "models/assetdto/reconciliation_dto.proto";
import "models/usersettingdto/per_coin_user_setting_dto.proto";

import "svc/unified_v2/req/margin/tdata/attached_action.proto";


// 交易结果数据
message TradingRequestData {
  // Next: 18
  // 持仓信息同步（目前用于期货有关止盈止损相关信息同步）
  repeated models.tradingdto.PositionDTO related_positions = 14;

  // 订单维度的相关数据:
  repeated models.tradingdto.TransactDTO related_orders = 2;
  // repeated models.tradingdto.TransactDTO related_floating_orders = 17;
  repeated models.tradingdto.ClosedPnlDTO related_closed_pnl_records = 3; // 平仓盈亏
  repeated models.tradingdto.TransactDTO related_dealing_desk_orders = 12; // DealingDesk

  // 成交维度相关的数据:
  repeated models.tradingdto.TransactDTO related_fills = 4; // 撮合成交记录 or 强平接管 or 被自动减仓
  repeated models.tradingdto.TransactDTO related_funding_records = 5; // 当双向开仓时,funding记录可能会有2条

  repeated models.liqadldtov2.LiqRecordDTO liq_records = 6; // 强平过程记录
  repeated models.liqadldtov2.AdlRecordDTO adl_records = 7; // 减仓记录

  repeated models.assetdto.ReconciliationDTO reconciliation_infos = 10; // 资金对账信息
  repeated models.usersettingdto.PerCoinUserSettingDTO user_settings = 11; // 用户级配置信息

  // ========== 回调事件 ==========
  repeated AttachedAction f_attached_actions = 13;
};
