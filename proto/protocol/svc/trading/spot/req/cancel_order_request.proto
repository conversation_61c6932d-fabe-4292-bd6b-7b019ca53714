syntax = "proto3";

package svc.trading.spot.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/spot/req";

import "enums/estopordertype/stop_order_type.proto";

message CancelOrderRequest {
    //  int32 broker_id = 1;
    //  int64 exchange_id = 2;
    string order_link_id = 3; // 客户端订单ID，可选，无订单ID时必传
    string order_id = 4; // 订单ID，可选，无客户端订单ID时必传
    enums.estopordertype.StopOrderType stop_order_type = 5;

}