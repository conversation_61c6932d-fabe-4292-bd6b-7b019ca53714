syntax = "proto3";

package svc.trading.spot.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/spot/req";


import "enums/epricedirection/price_direction.proto";

message TriggeredToActiveReq {
    int32 symbol = 3;

    int64 trigger_src_seq = 7;
    string trigger_src_price = 8;
    int64 trigger_src_time_e9 = 9;
    string trigger_remark = 10;

    string triggered_order_id = 11; // 用于查找到被触发的条件单
    enums.epricedirection.PriceDirection orig_expected_direction = 13; // 被触发条件单的原预期价格方向
    string orig_trigger_price = 14; // 被触发条件单的原触发价格

    // NEXT: 15
}
