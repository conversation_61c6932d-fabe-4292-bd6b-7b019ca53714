syntax = "proto3";

package svc.trading.spot.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/spot/req";

import "enums/eplantype/plan_type.proto";

message GetOpenTriggerOrdersRequest {
  int32 broker_id = 1;
  int64 broker_user_id = 2;
  int64 exchange_id = 3;
  int64 shard_id = 4;
  int32 symbol = 5;
  string cursor_order_id = 6;
  int32 limit = 7;
  enums.eplantype.PlanType plan_type = 8;
}