syntax = "proto3";

package svc.trading.spot.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/spot/req";

import "enums/eordertype/order_type.proto";
import "enums/eside/side.proto";
import "enums/estopordertype/stop_order_type.proto";


message BatchCancelOrderRequest {
  int32 broker_id = 1;
  repeated int32 symbols = 4; // 若为空则撤销所有币对
  enums.eside.Side side = 5; // 指定买单或卖单，默认是买卖都撤销
  enums.eordertype.OrderType order_type = 6;

  int32 base_token_id = 7; // baseTokenId
  int32 quote_token_id = 8; // quoteTokenId

  enums.estopordertype.StopOrderType stop_order_type = 9;
}