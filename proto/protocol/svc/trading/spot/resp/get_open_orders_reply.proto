syntax = "proto3";

package svc.trading.spot.resp;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/spot/resp";

message GetOpenOrdersReply {

  message LightOrderInfo {
    int64 account_id = 1;
    int64 order_id = 2;
  }

  repeated LightOrderInfo light_order_infos = 1;
  // 回传给服务端，该位点继续查询，返回-1表示查询结束
  int64 next_account_id = 2;
  // 回传给服务端，该位点继续查询，返回-1表示查询结束
  int64 next_order_id = 3;
}
