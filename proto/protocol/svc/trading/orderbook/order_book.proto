syntax = "proto3";

package svc.trading.orderbook;

import "enums/ecrossidx/cross_idx.proto";
import "enums/esymbol/symbol.proto";
import "enums/ecrossstatus/cross_status.proto";

option go_package = "code.bydev.io/trading/idl/pb/svc/trading/orderbook";
option php_namespace = "Bybit\\Engine\\Idl\\Svc\\Trading\\OrderBook";

message OrderBookMetaData {
    enums.ecrossidx.CrossIdx cross_idx = 1;
    enums.esymbol.Symbol symbol = 2;
    int64 cross_seq = 3;
    int64 timestamp = 4;
    string symbol_name = 5;
}

message OrderBookData {
    OrderBookMetaData meta_data = 1;
    bytes data = 2;
    bool data_compressed = 3;
    uint64 data_original_size = 4;
}

message OrderBookEntryItem {
    int64 price = 1;
    int64 qty = 2;
    int64 user_id = 3;
    int32 price_scale = 4;
}

message OrderBookEntry {
    int64 price = 1;
    int64 qty = 2;
    repeated OrderBookEntryItem orders = 3;
    int32 price_scale = 4;
}

message OrderBookProfile {
    repeated OrderBookEntry asks = 1;
    repeated OrderBookEntry bids = 2;
}

message OrderBookTradeItem {
    enums.ecrossstatus.CrossStatus cross_status = 1;
    int64 price = 2;
    int64 qty = 3;
    int64 user_id = 4;
    int32 price_scale = 5;
    int32 smp_group = 6;
}

message OrderBookTrade {
    repeated OrderBookTradeItem items = 1;
}

message OrderBook {
    enums.esymbol.Symbol symbol = 1;
    int64 cross_seq = 2;
    int64 timestamp = 3;
    // repeated OrderBookEntry asks = 4;
    // repeated OrderBookEntry bids = 5;
    reserved 4, 5;
    oneof data {
        OrderBookProfile profile = 6;
        OrderBookTrade trade = 7;
    }
}

message OrderBookDataBatchDump {
    repeated OrderBookData data = 1;
}
