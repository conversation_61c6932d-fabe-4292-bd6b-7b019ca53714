syntax = "proto3";

package svc.trading.option.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/option/req";

import "enums/etimeinforce/time_in_force.proto";
import "enums/eside/side.proto";
import "enums/eordertype/order_type.proto";

// 内部模型：批量下单请求对象
message BatchOrderRequest {
  repeated OrderRequest orderRequest = 1;
}

// 内部模型：单笔订单相关请求对象
message OrderRequest {

  // 请求ID
  string outRequestId = 1;
  // 下单时必填 仅支持请求期权symbol名称
  string symbol = 2;
  // 下单时必填 默认：限价单 Limit；仅支持请求Limit
  enums.eordertype.OrderType orderType = 3;
  // 描述:买卖方向 Buy; Sell
  enums.eside.Side  side = 4;
  // 描述:委托数量
  string orderQty = 5;
  // Option时，price 可为 false，若 false，不传参，若 true，需为tick_size的整数倍; category = Option 且 iv 为 true，此字段传参请求无效，最终以iv反推的价格作为订单价格
  string orderPrice = 6;
  // iv 可为 false，默认不传参; 若 true：IV必须大于0
  string iv = 7;
  // 描述:GoodTillCancel 一直有效至取消; ImmediateOrCancel 立即成交或取消; FillOrKill 完全成交或取消; PostOnly 被动委托
  enums.etimeinforce.TimeInForce timeInForce = 8;
  // 机构自定义订单ID, 最大长度36位，且同一机构下在期权交易（还是期权+永续）自定义ID不可重复
  string orderLinkId = 9;
  // 是否为mmp单
  bool mmp = 10;
  // reduceOnly
  bool reduceOnly = 11;
  // placeMode:basic/advanced
  int32 placeMode = 12;
  // placeType:iv/price
  int32 placeType = 13;
  // 是否需要试算
  bool pmTrial = 14;
}