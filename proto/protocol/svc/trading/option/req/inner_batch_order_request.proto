syntax = "proto3";

package svc.trading.option.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/option/req";

import "enums/esymbol/symbol.proto";
import "svc/trading/option/req/inner_order_request.proto";

/**
 内部模型：内部系统批量下单请求对象
*/
message IBatchOrderRequest {

  // [必填]
  int64 userId = 1;
  // [必填]
  int64 accountId = 2;
  // 请求来源 [必填] 例如:margin-core
  string tradeSource = 3;
  // 下单请求对象列表
  repeated OrderRequest createOrderRequest = 4;
  // [选填] 订单tag
  int32 orderTag = 5;
}