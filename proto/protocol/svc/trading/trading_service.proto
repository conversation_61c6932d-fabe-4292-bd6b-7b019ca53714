syntax = "proto3";

package svc.trading;

option go_package = "code.bydev.io/trading/idl/pb/svc/trading";

import "google/protobuf/any.proto";
import "svc/req_resp_header.proto";

// 订单相关请求:
import "svc/trading/req/margin_cancel_all.proto";
import "svc/trading/req/margin_cancel_order.proto";
import "svc/trading/req/pre_create_order.proto";
import "svc/trading/req/create_order.proto";
import "svc/trading/req/create_order_with_leverage.proto";
import "svc/trading/req/cancel_order.proto";
import "svc/trading/req/replace_order.proto";
import "svc/trading/req/cancel_all.proto";
import "svc/trading/req/cancel_all_order_by_coin.proto";
import "svc/trading/req/triggered_to_active.proto";
import "svc/trading/req/swap_position.proto";
import "svc/trading/req/query_order.proto";
import "svc/trading/req/liq_cleanup.proto";
import "svc/trading/req/liq_execute.proto";
import "svc/trading/req/adl_cleanup.proto";
import "svc/trading/req/adl_execute.proto";
import "svc/trading/req/evict_history_order.proto";
import "svc/trading/req/query_symbol_contents.proto";
// 现货订单请求
import "svc/trading/spot/req/new_order_request.proto";
import "svc/trading/spot/req/batch_new_order_request.proto";
import "svc/trading/spot/req/cancel_order_request.proto";
import "svc/trading/spot/req/batch_cancel_order_request.proto";
import "svc/trading/spot/req/batch_cancel_order_by_ids_request.proto";
import "svc/trading/spot/req/place_block_trade_order_request.proto";
import "svc/trading/spot/req/batch_cancel_block_trade_order_request.proto";
import "svc/trading/spot/req/cancel_order_status_by_symbol_request.proto";
import "svc/trading/spot/req/get_open_orders_request.proto";
import "svc/trading/spot/req/get_open_trigger_orders_request.proto";
import "svc/trading/spot/req/get_trigger_order_page_request.proto";
import "svc/trading/spot/req/get_trigger_orders_request.proto";
import "models/usersettingdto/user_setting.proto";

// 撮合透传请求:
import "models/tradingdto/transact_dto.proto";
import "models/matchingdto/matching_result_dto.proto";
// 持仓相关请求:
import "svc/trading/req/deposit.proto";
import "svc/trading/req/withdraw.proto";
import "svc/trading/req/rotate_realised_pnl.proto";
import "svc/trading/req/set_leverage.proto";
import "svc/trading/req/switch_isolated.proto";
import "svc/trading/req/set_margin.proto";
import "svc/trading/req/add_margin.proto";
import "svc/trading/req/set_open_limit.proto";
import "svc/trading/req/set_risk_id.proto";
import "svc/trading/req/set_auto_add_margin.proto";
import "svc/trading/req/set_fee_rate.proto";
import "svc/trading/req/update_adl_rank_indicator.proto";
import "svc/trading/req/settle_funding_fee.proto";
import "svc/trading/req/set_tp_sl_ts.proto";
import "svc/trading/req/query_position.proto";
import "svc/trading/req/mark_price_update.proto";
import "svc/trading/req/query_asset.proto";
import "svc/trading/req/update_subscription.proto";
import "svc/trading/req/force_add_position.proto";
import "svc/trading/req/force_sub_position.proto";
import "svc/trading/req/switch_position_mode.proto";
import "svc/trading/req/switch_position_mode_with_coin.proto";
import "svc/trading/req/force_switch_position_mode.proto";
import "svc/trading/req/self_fill.proto";
import "svc/trading/req/settle.proto";
import "svc/trading/req/query_active_future_symbol.proto";
import "svc/trading/req/load_user_data.proto";
import "svc/trading/req/on_symbol_config_update.proto";
import "svc/trading/req/query_re_calc_params.proto";
import "svc/trading/req/switch_tp_sl_mode.proto";
import "svc/trading/req/query_wallet_op.proto";
import "svc/trading/req/query_stop_order_list.proto";
import "svc/trading/req/reconciliation.proto";
import "svc/trading/req/block_trade.proto";
import "svc/trading/req/dealing_desk.proto";
import "svc/trading/req/check_orders_detail.proto";
import "svc/trading/req/check_inactiveuser.proto";
import "svc/trading/req/unload_userdata.proto";
import "svc/trading/req/close_all_position.proto";
import "svc/trading/req/on_risk_limit_update.proto";
import "svc/trading/req/set_single_fee_rate.proto";
import "svc/trading/req/revoke_single_fee_rate.proto";
import "svc/trading/req/switch_isolated_with_symbols.proto";
import "svc/trading/req/switch_position_mode_with_symbols.proto";
import "svc/trading/req/set_leverage_with_symbols.proto";
import "svc/trading/req/pre_check_orders.proto";
import "svc/trading/req/transfer_postion.proto";
import "svc/trading/req/fix_position.proto";
import "svc/trading/req/fix_user_setting.proto";
import "svc/trading/req/close_position_by_symbol_and_percentage.proto";
// 用户设置相关请求
import "svc/trading/req/set_customer_default_max_leverage.proto";
import "svc/trading/req/set_customer_fee_rate.proto";
import "svc/trading/req/set_customer_trigger_liq.proto";
import "svc/trading/req/set_customer_symbol_list.proto";
import "svc/trading/req/set_customer_uniform_margin.proto";
import "svc/trading/req/set_customer_symbol_max_leverage.proto";
import "svc/trading/req/query_user_setting.proto";
import "svc/trading/req/sync_user_data_to_pm.proto";
import "svc/trading/req/set_mt4_account.proto";
import "svc/trading/req/revoke_customer_fee_rate.proto";
import "svc/trading/req/set_customer_dealer_user_id.proto";
import "svc/trading/req/health_check.proto";
import "svc/trading/req/update_ws_user_status.proto";
import "svc/trading/req/set_risk_limit_config_type.proto";
import "svc/trading/req/set_dealing_desk_shard_config.proto";
import "svc/trading/req/recover_pending_order.proto";
import "svc/trading/req/set_pre_off_loaded.proto";
import "svc/trading/req/set_mm_rate_close.proto";
import "svc/trading/req/confirm_pending_mmr.proto";
import "svc/trading/req/set_fee_rate_user_rule.proto";
// 请求的响应内部包含一份result
import "models/tradingdto/trading_result_dto.proto";
import "models/tradingdump/per_coin_user_trading_data.proto";
import "models/unifiedtradingdump/per_user_trading_data.proto";

// PM响应
import "svc/trading/req/margin_result.proto";
import "svc/trading/req/unified_margin_result.proto";

// PM强平交互
import "svc/trading/req/liq_set_riskid.proto";
import "svc/trading/req/liq_taken_over.proto";
import "svc/trading/req/liq_set_status.proto";
import "svc/trading/req/liq_cancel_active_order.proto";
import "svc/trading/req/liq_sub_position.proto";
import "svc/trading/req/manual_taken_over.proto";

// trigger rebalance
import "svc/trading/req/trigger_rebalance.proto";

// 统保强平交互
import "svc/trading/req/liq_coin_execute.proto";
import "svc/trading/req/liq_request.proto";
import "svc/trading/req/liq_create_twap_order.proto";
import "svc/trading/req/liq_create_twap_order_v2.proto";
import "svc/trading/req/adl_execute_v2.proto";

//用户Symbol信息
import "svc/trading/req/query_user_symbol_data.proto";

// 设置copy trade account
import "svc/trading/req/set_copy_trade_account.proto";

import "svc/unified_v2/unified_v2_result_dto.proto";

// 期权订单请求
import "svc/uta_engine/option/option_trade_common_message.proto";
import "svc/uta_engine/option/option_block_trade_message.proto";
import "svc/uta_engine/option/option_mmp_label_message.proto";
import "svc/trading/option/req/inner_order_request.proto";
import "svc/trading/option/req/inner_batch_cancel_request.proto";
import "svc/trading/option/req/inner_batch_order_request.proto";
import "svc/trading/option/req/inner_common_cancel_request.proto";
import "svc/trading/option/req/inner_block_order_request.proto";

import "svc/trading/req/agg_create_orders.proto";
import "svc/trading/req/update_user_tag.proto";
import "svc/trading/req/update_common_bot_tag.proto";
import "svc/trading/req/set_tpsl_price_protect.proto";
import "svc/trading/req/update_verify_user_info.proto";
import "svc/trading/req/set_equity_tpsl.proto";
import "svc/trading/req/set_equity_tpsl_ts.proto";

import "svc/trading/req/fund_take_over_calc.proto";

import "svc/trading/req/pre_calc_liq_price.proto";
import "svc/trading/req/set_customer_symbol_max_value.proto";
import "enums/eslippagetype/slippage_type.proto";
import "models/tradingdto/per_user_trading_dataset.proto";
import "svc/trading/req/set_symbol_group_fee_rate.proto";
import "svc/trading/req/revoke_symbol_group_fee_rate.proto";

// 用oneof将多种请求定义成统一的req
message TradingReq {
  svc.ReqRespHeader req_header = 2001;

  // 字段tag值跟Action枚举定义保持一致
  // 需要先增加Action枚举定义, 再添加req
  oneof req_body {
    // Action.NEXT=231
    // ----- 订单相关action -----
    //
    svc.trading.req.PreCreateOrderReq pre_create_order = 34; // 预下单: 只计算,不更新内存,也不发撮合
    svc.trading.req.CreateOrderReq create_order = 1; // 新创建条件单or活动单, 符合条件的发撮合
    svc.trading.req.CancelOrderReq cancel_order = 2; // 待触发的条件单直接撤单, 活动单向撮合发起cancel
    svc.trading.req.ReplaceOrderReq replace_order = 3; // 待触发的条件单直接修改, 活动单向撮合发起replace
    svc.trading.req.CancelAllReq cancel_all = 4; // 批量撤单 (默认撤掉所有活动单, 指定stop_order_type的话撤掉所有条件单)
    svc.trading.req.CancelAllOrderByCoinReq cancel_all_order_by_coin = 156; // 批量撤单 撤销某个coin的全部订单
    svc.trading.req.TriggeredToActiveReq triggered_to_active = 5; // 条件单触发后执行新创建活动单, 符合条件的发撮合
    //6 7 reserved 被占用了 别定义
    svc.trading.req.SwapPositionReq swap_position = 70;
    // query_order = 8;
    // web_query_order = 9;
    // liq_prepare = 10;
    svc.trading.req.LiqCleanupReq liq_cleanup = 11;
    // liq_take_over_pz = 12;
    svc.trading.req.LiqExecuteReq liq_execute = 13;
    // adl_prepare = 14;
    svc.trading.req.AdlCleanupReq adl_cleanup = 15;
    svc.trading.req.AdlExecuteReq adl_execute = 16;
    svc.trading.req.EvictHistoryOrderReq evict_history_order = 93;

    //
    // ----- 持仓相关action -----
    //
    svc.trading.req.DepositReq deposit = 17;
    svc.trading.req.WithdrawReq withdraw = 18;
    svc.trading.req.RotateRealisedPnlReq rotate_realised_pnl = 19; // 先兼容存在, 改成00:00的funding后自动归档后去掉
    svc.trading.req.SetLeverageReq set_leverage = 20;
    svc.trading.req.SetMarginReq set_margin = 21;
    svc.trading.req.AddMarginReq add_margin = 22;
    svc.trading.req.SetRiskIdReq set_risk_id = 23;
    svc.trading.req.SetAutoAddMarginReq set_auto_add_margin = 24;
    svc.trading.req.SetFeeRateReq set_fee_rate = 25;
    svc.trading.req.UpdateAdlRankIndicatorReq update_adl_rank_indicator = 26; // 原命名: deleverage_indicator
    // update_position_status = 27;
    svc.trading.req.SettleFundingFeeReq settle_funding_fee = 28; // 先兼容存在, 从撮合转一圈结算funding后去掉
    svc.trading.req.SetTpSlTsReq set_tp_sl_ts = 29;
    // web_query_position = 30;
    svc.trading.req.SwitchIsolatedReq switch_isolated = 31;
    // execution_report = 32;
    svc.trading.req.SetOpenLimitReq set_open_limit = 33;
    svc.trading.req.ForceAddPositionReq force_add_position = 52;
    svc.trading.req.ForceSubPositionReq force_sub_position = 53;
    svc.trading.req.SwitchPositionModeReq switch_position_mode = 54;
    svc.trading.req.SwitchPositionModeWithCoinReq switch_position_mode_with_coin = 140;
    svc.trading.req.ForceSwitchPositionModeReq force_switch_position_mode = 137;
    svc.trading.req.SelfFillReq self_fill = 55;
    svc.trading.req.SettleReq settle = 56;
    svc.trading.req.ReconciliationReq reconciliation = 71;
    svc.trading.req.BlockTradeReq block_trade = 72;
    svc.trading.req.CheckOrdersDetail check_orders_detail = 73;
    svc.trading.req.CloseAllPositionReq close_all_position = 102;
    svc.trading.req.SetLeverageWithSymbolsReq set_leverage_with_symbols = 123;
    svc.trading.req.SwitchIsolatedWithSymbolsReq switch_isolated_with_symbols = 124;
    svc.trading.req.SwitchPositionModeWithSymbolsReq switch_position_mode_with_symbols = 125;

    //
    // ----- 撮合相关action -----
    //
    models.tradingdto.TransactDTO cross_req = 43; // 从request_of_N读取出定序后的撮合请求
    // {oneof内部不支持repeated} repeated models.tradingdto.TransactDTO cross_resp_pkg = ?;
    models.matchingdto.MatchingResultDTO on_recv_matching_result = 57;
    // 输出action:
    // => execution_report = 32; // 将撮合结果(new/cancel/replace)更新到持仓
    // => liq_take_over_pz = 12; // 强平将持仓全量接管走
    // => adl_reduce_pz = 44; // 被adl导致持仓降低
    // => settle_funding_fee = 28; // 每8小时结算funding_fee
    // `-> rotate_realised_pnl = 19; // 每天00:00做完funding后做当日盈亏归档
    // => settle_future_contract = 45; // 到期合约统一清算

    //
    // ----- 其他数据同步 -----
    //
    svc.trading.req.MarkPriceUpdateReq on_mark_price_update = 35;
    // calculate_position_pnl = 36;
    models.tradingdto.TradingResultDTO on_recv_trading_result = 46;

    //
    // ----- 查询请求 -----
    //
    svc.trading.req.QueryAssetReq query_asset = 37;
    svc.trading.req.QueryPositionReq query_position = 42;
    svc.trading.req.QueryOrderReq query_order = 8;
    svc.trading.req.QueryActiveFutureSymbolReq query_active_future_symbol = 58;
    svc.trading.req.QueryReCalcParamsReq query_re_calc_params = 65; // fixme: action=67
    svc.trading.req.QueryWalletOpReq query_wallet_op = 68;
    // load_aopz_from_db = 38;
    // process_aopz_result = 39;
    // debug_show_aopz = 40;
    // cancel_recovery_order = 41;

    //
    // ----- 订阅请求 -----
    //
    svc.trading.req.UpdateSubscriptionReq update_subscription = 47;

    // 加载用户持仓订单等数据
    svc.trading.req.LoadUserDataReq load_user_data = 81;

    // 重启容灾时恢复已发给 margin 但是没有发给撮合的订单
    svc.trading.req.RecoverPendingOrderReq recover_pending_order = 1035;

    svc.trading.req.OnSymbolConfigUpdate on_symbol_config_update = 80;

    // 切换止盈止损模式
    svc.trading.req.SwitchTpSlModeReq switch_tp_sl_mode = 49;

    // 查询条件单列表
    svc.trading.req.QueryStopOrderListReq query_stop_order_list = 50;

    // 检查非活跃用户数据
    svc.trading.req.CheckInactiveUserReq check_inactive_user = 94;

    // 持久化用户数据
    svc.trading.req.UnloadUserCacheReq unload_user_data = 95;

    // 设置用户费率
    svc.trading.req.SetCustomerFeeRateReq set_customer_fee_rate = 97;

    // 设置用户强平是否触发
    svc.trading.req.SetCustomerTriggerLiqReq set_customer_trigger_liq = 98;

    // 设置用户强平成交用户id
    svc.trading.req.SetCustomerDealerUserIdReq set_customer_dealer_user_id = 160;

    // 设置用户可交易币对
    svc.trading.req.SetCustomerSymbolListReq set_customer_symbol_list = 99;

    // 设置用户是否开启统一保证金
    svc.trading.req.SetCustomerUnifiedMarginReq set_customer_unified_margin = 127;

    // 设置用户不同symbol最大杠杆
    svc.trading.req.SetCustomerSymbolMaxLeverageReq set_customer_symbol_max_leverage = 171;

    // 查询用户配置
    svc.trading.req.QueryUserSettingReq query_user_setting = 100;

    // 查询用户coin下的symbol目录
    svc.trading.req.QuerySymbolContentsReq query_symbol_contents = 101;

    // 组合保证金的response
    svc.trading.req.MarginResult on_recv_margin_result = 103;
    svc.trading.req.UnifiedMarginResult on_recv_unified_margin_result = 135;

    // 同步持仓数据和订单到保证金
    svc.trading.req.SyncUserDataToPM sync_user_data_to_pm = 104;

    // 保证金强平请求
    svc.trading.req.LiqSetStatus liq_set_status = 105;
    svc.trading.req.LiqSetRiskId liq_set_riskid = 106;
    svc.trading.req.LiqBatchSetRiskId liq_batch_set_riskid = 152;
    svc.trading.req.LiqCancelActiveOrd liq_cancel_ord = 107;
    svc.trading.req.LiqSubPosition liq_sub_position = 108;
    svc.trading.req.LiqBatchSubPosition liq_batch_sub_position = 153;
    svc.trading.req.LiqTakenOver liq_taken_over = 109;
    svc.trading.req.LiqBatchTakenOver liq_batch_taken_over = 151;
    svc.trading.req.ManualTakenOver manual_taken_over = 182;

    // 风险限额发生变化
    svc.trading.req.OnRiskLimitUpdate on_risk_limit_update = 110;

    // 健康检查
    svc.trading.req.HealthCheckReq health_check = 111;

    // 统一设置mt4的交易配置。目前有：持仓方向，杠杆，强平豁免，risklimit（使用postionDTO里的OpenValueLimitE8字段）
    svc.trading.req.SetMT4AccountReq set_mt4_account = 112;

    // ws 用户信息更新
    svc.trading.req.UpdateWsUserStatusReq  update_ws_user_status = 131;

    // 设置用户symbol费率
    svc.trading.req.SetSingleFeeRateReq set_single_fee_rate = 132;

    // 重置用户symbol费率
    svc.trading.req.RevokeSingleFeeRateReq revoke_single_fee_rate = 133;

    // 重置用户setting费率
    svc.trading.req.RevokeCustomerFeeRateReq revoke_customer_fee_rate = 134;

    // 触发rebalane
    svc.trading.req.TriggerReBalanceReq on_trigger_rebalance = 136;
    //查询用户某个符号的所有信息
    svc.trading.req.QueryUserSymbolDataReq query_user_symbol_data = 138;

    //设置用户某个符号的所有信息
    models.tradingdto.TradingResultDTO on_set_trading_result = 139;

    // -------------------------统保强平-----------------------
    // 强平执行(强平撤销开仓单|强平撤销所有订单|无损降挡|有损降挡|仓位托管)
    svc.trading.req.LiqCoinExecuteReq on_liq_coin_execute = 141;
    // --------------------------------------------------------

    // 预先检查订单
    svc.trading.req.PreCheckOrders pre_check_orders = 147;

    // 风险限额提供多套配置，通过接口设置用户使用哪套配置
    svc.trading.req.SetRiskLimitConfigTypeReq set_risk_limit_config_type = 149;

    // 转移仓位
    svc.trading.req.TransferPositionReq transfer_position = 150;

    // Dealing Desk
    svc.trading.req.DealingDeskReq dealing_desk = 161;
    svc.trading.req.SetDealingDeskShardConfigReq set_dealing_desk_shard_config = 163;

    svc.trading.req.CreateOrderWithLeverageReq create_order_with_leverage = 164;

    // 设置copy trade account
    svc.trading.req.SetCopyTradeAccountReq set_copy_trade_account = 168;

    // 保证金调用trading取消订单
    svc.trading.req.MarginCancelAllReq margin_cancel_all = 173;
    svc.trading.req.MarginCancelOrderReq margin_cancel_order = 174;

    svc.trading.req.LiqRequest liq_request = 178;

    // 设置用户默认可开最大杠杆
    svc.trading.req.SetCustomerDefaultMaxLeverageReq set_customer_default_max_leverage = 193;
    svc.trading.req.AggCreateOrdersReq agg_create_orders_req = 199;

    svc.trading.req.SetPreOffLoadedReq set_pre_off_loaded = 202;

    svc.trading.req.SetMmRateCloseReq set_mm_rate_close = 204;
    svc.trading.req.ConfirmPendingMMR confirm_pending_mmr = 205;
    // 设置或者更新用户的user_tag值
    svc.trading.req.UpdateUserTagReq update_user_tag = 206;

    svc.trading.req.FixPositionReq fix_position = 218;
    svc.trading.req.FixUserSettingReq fix_user_setting = 219;
    svc.trading.req.SetTpSlPriceProtectReq set_tpsl_price_protect_req = 223;
    svc.trading.req.LiqCreateTWapOrderReq liq_create_twap_order_req = 228;
    svc.trading.req.UpdateVerifyUserInfoReq update_verify_user_info_req = 230;        // 更新用户人群包信息
    svc.trading.req.LiqCreateTWapOrderV2Req liq_create_twap_order_v2_req = 231;
    svc.trading.req.AdlExecuteReqV2 adl_execute_req_v2 = 232;
    // svc.trading.req.UpdateTradeBanStatusReq update_trade_ban_status = 233; 已占用
    // reserved 234; 已占用
    svc.trading.req.FundTakeOverCalcReq fund_take_over_calc_req = 235;
    svc.trading.req.SetEquityTpslReq set_equity_tpsl_req = 236;
    // 设置用户费率标签
    svc.trading.req.SetFeeRateUserRuleReq set_fee_rate_user_rule_req = 237;

    svc.trading.req.PreCalcLiqPriceReq pre_calc_liq_price_req = 240;



    // 更新用户的userTag(commonBot bit位)、commonBotTag
    svc.trading.req.UpdateCommonBotTagReq update_common_bot_tag_req = 241;
    svc.trading.req.SetCustomerSymbolMaxValueReq set_customer_symbol_max_Value_req = 242;

    // 为了和action对齐，跳过243-246
    svc.trading.req.SetEquityTpsltsReq set_equity_tpsl_ts_req = 247;
    // 为了和action对齐，跳过248-250
    svc.trading.req.ClosePositionBySymbolAndPercentageReq close_position_by_symbol_and_percentage_req = 251;
    svc.trading.req.SetSymbolGroupFeeRateReq set_symbol_group_fee_rate_req = 252;
    svc.trading.req.RevokeSymbolGroupFeeRateReq revoke_symbol_group_fee_rate_req = 253;

    // todo spot 从 2000开始;
    // 下单请求（包括币币、条件单、止盈止损订单）
    //        svc.trading.spot.req.NewOrderRequest new_order_request = 2000;

    // 撤单
    //        svc.trading.spot.req.CancelOrderRequest cancel_order_request = 2002;

    // 批量下单或大宗交易批量预下单
    //        svc.trading.spot.req.BatchNewOrderRequest batch_new_order_request = 2003;

    // 批量撤单
    //        svc.trading.spot.req.BatchCancelOrderRequest batch_cancel_order_request = 2004;

    // 按订单id批量撤单
    //        svc.trading.spot.req.BatchCancelOrderByIdsRequest batch_cancel_order_by_ids_request = 2005;

    // todo option 从 3000开始;
    // 期权-内部风控服务
    svc.trading.option.req.ICommonCancelRequest inner_common_cancel_request = 3000;
    svc.trading.option.req.IBatchCancelRequest inner_batch_cancel_request = 3001;
    svc.trading.option.req.IBatchOrderRequest inner_batch_order_request = 3002;
    svc.uta_engine.option.InnerMmpInactiveRequest inner_mmp_inactive_request = 3003;

    // 期权-siteApi服务
    svc.trading.option.req.OrderRequest site_order_request = 3010;
    svc.trading.option.req.BatchOrderRequest site_batch_order_request = 3011;
    svc.uta_engine.option.SiteReplaceOrderRequest site_replace_order_request = 3012;
    svc.uta_engine.option.SiteCancelOrderRequest site_cancel_order_request = 3013;
    svc.uta_engine.option.SiteBatchCancelOrderRequest site_batch_cancel_order_request = 3014;
    svc.uta_engine.option.SiteCancelAllRequest site_cancel_all_request = 3015;

    // 期权-大宗交易服务
    svc.trading.option.req.IBlockOrderRequest block_order_request = 3020;
    svc.uta_engine.option.BlockTradeOrderRequest block_trade_order_request = 3021;

    // 期权-做市商MMP信息服务
    svc.uta_engine.option.MmpQueryUserMmpRequest mmp_query_user_mmp_request = 3030;
  }
  // alt: google.protobuf.Any req_body = ?;
};

// 包含对req的同步响应结果 (一般是等result持久化成功后再回复resp)
message TradingResp {
  svc.ReqRespHeader resp_header = 1;

  int32 ret_code = 2; // 错误码, 0表示处理成功
  string ret_msg = 3; // 错误消息
  string ext_code = 4; // 扩展错误码
  google.protobuf.Any ext_info = 5; // 扩展信息?

  models.tradingdto.TradingResultDTO result = 6; // 对于ret_code=0的响应, 会将result持久化到MQ中

  svc.unified_v2.UnifiedV2ResultDTO u_result = 13; // 统一交易账户下的result

  // oneof result {
  //     models.tradingdto.TradingResultDTO delta = 6;
  // models.tradingdump.PerCoinUserTradingData snapshot = 7; // 对外
  bytes snapshot = 7; // 对内
  // }

  message PreCreateOrderResult {
    bool has_position = 1;
    int64 order_value_e8 = 2;
    int64 take_up_margin_e8 = 3;
    int64 available_margin_e8 = 4;
    int64 trigger_price_x = 5;
    int64 mark_price_x = 6;
    int64 liq_price_x = 7;
    int64 difference_x = 8;
    int64 position_size_x = 9;
    int64 actual_price_x = 10;
    int64 orig_qty_x = 11;
    int64 actual_qty_x = 12;
    int64 take_profit_x = 13;
    int64 stop_loss_x = 14;

    int32 price_scale = 15;
    int32 pre_imr_e4 = 16;
    int32 pre_mmr_e4 = 17;
    int32 post_imr_e4 = 18;
    int32 post_mmr_e4 = 19;
    bool has_imr_mmr = 20;
    enums.eslippagetype.SlippageType max_slippage_type = 21;
    int64 max_slippage_e2 = 22;
    // NEXT: 21
  }
  PreCreateOrderResult pre_create_order_result = 8;

  repeated svc.trading.req.ReCalcParamsResult re_calc_params_result = 9;

  svc.trading.req.SymbolContentsResult symbol_contents_result = 10;

  message PmLiqResult {
    int32 pending_cancel_ord_count = 1; // 待取消的订单数量
    string order_id = 2; // 订单号
    repeated string order_id_list = 3; // 订单号列表
  }
  PmLiqResult pm_liq_result = 11;

  models.unifiedtradingdump.PerCoinUserTradingData per_coin_user_trading_data = 12; // 统一保证金升级时，返回仓位配置和用户设置信息

  svc.trading.req.LiqTWapResponse liq_twap_response = 15; // liq_create_twap_order_v2_req请求会返回此结构

  svc.trading.req.FundTakeOverCalcResponse fund_take_over_cacl_response = 16;
  oneof buy_pre_calc_liq_result {
    int64 buy_liq_price_x = 17; // 买成交预算liq price结果
  }

  oneof sell_pre_calc_liq_result {
    int64 sell_liq_price_x = 18; // 卖成交预算liq price结果
  }

  int32 pre_calc_liq_price_scale = 19;
  models.usersettingdto.PerUserSettingData user_setting = 20;
  models.tradingdto.EquityTpslData per_user_equity_tpsl_data = 21;
  // next 22
};

service TradingService {
  // req:resp一比一处理
  rpc Process (TradingReq) returns (TradingResp);

  // req:resp是n:m对应
  rpc Subscribe (stream TradingReq) returns (stream TradingResp);
}
