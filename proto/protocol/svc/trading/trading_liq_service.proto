syntax = "proto3";

package svc.trading;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading";

import "svc/req_resp_header.proto";
import "svc/trading/req/liq_set_riskid.proto";
import "svc/trading/req/liq_taken_over.proto";
import "svc/trading/req/liq_set_status.proto";
import "svc/trading/req/liq_cancel_active_order.proto";
import "svc/trading/req/liq_sub_position.proto";
import "svc/trading/req/pm_cancel_order.proto";
import "svc/trading/req/pm_cancel_all.proto";
import "svc/trading/req/op_create_order.proto";

message TradingLiqSetStatusReq {
  svc.ReqRespHeader req_header = 1;
  svc.trading.req.LiqSetStatus set_status = 2;
};

message TradingLiqSetRiskIdReq {
  svc.ReqRespHeader req_header = 1;
  svc.trading.req.LiqSetRiskId set_risk_id = 2;
};

message TradingLiqBatchSetRiskIdReq {
  svc.ReqRespHeader req_header = 1;
  svc.trading.req.LiqBatchSetRiskId batch_set_risk_id = 2;
};

message TradingLiqCancelOrdReq {
  svc.ReqRespHeader req_header = 1;
  svc.trading.req.LiqCancelActiveOrd cancel_ord = 2;
};

message TradingLiqSubPositionReq {
  svc.ReqRespHeader req_header = 1;
  svc.trading.req.LiqSubPosition sub_position = 2;
};

message TradingLiqBatchSubPositionReq {
  svc.ReqRespHeader req_header = 1;
  svc.trading.req.LiqBatchSubPosition batch_sub_position = 2;
};

message TradingLiqTakenOverReq {
  svc.ReqRespHeader req_header = 1;
  svc.trading.req.LiqTakenOver taken_over = 2;
};

message TradingLiqBatchTakenOverReq {
  svc.ReqRespHeader req_header = 1;
  svc.trading.req.LiqBatchTakenOver batch_taken_over = 2;
};

message TradingCancelOrderReq {
  svc.ReqRespHeader req_header = 1;
  svc.trading.req.PMCancelOrderReq pm_cancel_order = 2;
};

message TradingCancelAllReq {
  svc.ReqRespHeader req_header = 1;
  svc.trading.req.PMCancelAllReq pm_cancel_all = 2;
}

message TradingCreateOrderReq {
  svc.ReqRespHeader req_header = 1;
  svc.trading.req.OpCreateOrderReq op_create_order = 2;
}

// 包含对req的同步响应结果 (一般是等result持久化成功后再回复resp)
message TradingLiqResp {
  svc.ReqRespHeader resp_header = 1;
  int32 code = 2; // 错误码, 0表示处理成功
  string msg = 3; // 错误消息
  int32 pending_cancel_ord_count = 4; // 待取消的订单数量
  string order_id = 5; // 订单号
  repeated string order_id_list = 6; // 订单号列表
};

service TradingLiqService {
  rpc ProcessLiqStatus(TradingLiqSetStatusReq) returns (TradingLiqResp);
  rpc ProcessSetRiskId(TradingLiqSetRiskIdReq) returns (TradingLiqResp);
  rpc ProcessBatchSetRiskId(TradingLiqBatchSetRiskIdReq) returns (TradingLiqResp);
  rpc ProcessCancelOrd(TradingLiqCancelOrdReq) returns (TradingLiqResp);
  rpc ProcessSubPosition(TradingLiqSubPositionReq) returns (TradingLiqResp);
  rpc ProcessBatchSubPosition(TradingLiqBatchSubPositionReq) returns (TradingLiqResp);
  rpc ProcessTakenOver(TradingLiqTakenOverReq) returns (TradingLiqResp);
  rpc ProcessBatchTakenOver(TradingLiqBatchTakenOverReq) returns (TradingLiqResp);
  rpc CancelOrder(TradingCancelOrderReq) returns (TradingLiqResp);
  rpc CancelAll(TradingCancelAllReq) returns (TradingLiqResp);
  rpc CreateOrder(TradingCreateOrderReq) returns (TradingLiqResp);
}
