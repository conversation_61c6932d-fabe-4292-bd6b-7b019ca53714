syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


import "enums/ecanceltype/cancel_type.proto";
import "enums/ecancelalltype/cancel_all_type.proto";
import "enums/econtracttype/contract_type.proto";
import "enums/ecoin/coin.proto";

message CancelAllOrderByCoinReq {
  repeated enums.ecancelalltype.CancelAllType cancel_all_types = 1; // 取消订单的类型 0:取消所有活动单(兼容stop_order_type) 1:取消全部条件单(兼容stop_order_type)  2:取消全部止盈止损条件单  3：取消全部普通条件单与追踪止损条件单  4：取消所有开仓单(非RO|COT的条件单和活动单)  5：取消所有开启MMP的订单
  enums.ecanceltype.CancelType cancel_type = 2;
  string remark = 3;
  enums.econtracttype.ContractType contract_type = 5;
  repeated enums.ecoin.Coin coin_list = 6;
  bool cancel_all_coins = 7;
  // NEXT: 8
}