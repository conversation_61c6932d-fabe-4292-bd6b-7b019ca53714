syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


// import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";
import "enums/esystemsource/system_source.proto";

message SetLeverageWithSymbolsReq {
  // int64 user_id = 1;
  // enums.ecoin.Coin coin = 2;
  repeated SetLeverageWithSymbol new_leverage_setting = 3;

  message SetLeverageWithSymbol {
    enums.esymbol.Symbol symbol = 1;

    int64 position_idx = 2; // 0:单持仓, 1:双向的多仓, 2:双向的空仓

    // 目前需要保持多空杠杆一致，后续支持扩展
    int64 new_buy_leverage_e2 = 3;
    int64 new_sell_leverage_e2 = 4;

  }

  enums.esystemsource.SystemSource system_source = 6;

  //兼容旧版本接口，允许旧版本通过调杠杆接口切换全逐仓
  //默认false表示旧版本 需要判断是否是切全逐仓请求
  //新版本传true  一定是调杠杆
  bool skip_switch_isolated = 4;

  // true表示跳过最大杠杆检查
  bool skip_max_leverage_limit = 5;
  // NEXT: 7
}
