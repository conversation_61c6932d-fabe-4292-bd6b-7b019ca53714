syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


import "enums/eliqaction/liq_action.proto";

message LiqCancelOrders {
  int32 symbol_id = 1;              // 0 表示取消该coin下的所有订单
  int32 cancel_type = 2;            // 9: 普通用户被强平前, 统一撤单 <前序撤单>  2: 普通用户被强平前, 统一撤单 <最后一笔>
  string remark = 3;
  repeated LiqSpotOrder to_cancel_order_ids = 4; // 如果该字段非空,就取消指定的订单

  enums.eliqaction.LiqAction liq_action = 99; // 强平action
  // NEXT: 5
};

message LiqSpotOrder {
  string order_id = 1;
  int32 stop_order_type = 2; // 0普通订单 1普通条件单  8止盈止损
};
