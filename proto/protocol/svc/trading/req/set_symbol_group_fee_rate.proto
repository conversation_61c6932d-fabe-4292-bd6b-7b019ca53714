syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


import "enums/esymbol/symbol.proto";

message SetSymbolGroupFeeRateReq {
    int64 fee_rate_tag_id = 1;   //  费率板块值
    oneof optional_new_tkfr {
        int64 new_taker_fee_rate_e8 = 2;
    }
    oneof optional_new_mkfr {
        int64 new_maker_fee_rate_e8 = 3;
    }
    // taker费率描述
    oneof optional_tkfr_desc {
        string taker_fee_rate_desc = 4;
    }
    // maker费率描述
    oneof optional_mkfr_desc {
        string maker_fee_rate_desc = 5;
    }
    // NEXT: 6
}
