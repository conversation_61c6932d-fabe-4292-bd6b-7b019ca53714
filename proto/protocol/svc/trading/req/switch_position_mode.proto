syntax="proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


//import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";
import "enums/epositionmode/position_mode.proto";

//单向持仓双向持仓切换
message SwitchPositionModeReq {
    //int64 user_id = 1;
    //enums.ecoin.Coin coin = 2;
    enums.esymbol.Symbol symbol = 3;
    enums.epositionmode.PositionMode position_mode = 4; //单向持仓or双向持仓
}