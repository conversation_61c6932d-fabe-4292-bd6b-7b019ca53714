syntax = "proto3";

package svc.trading.req;

option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


// CommonBot类型的userTag专用
message UpdateCommonBotTagReq {
    // 需要设置的common_bot_tag
    int64 common_bot_tag = 1;

    // 如果clear_tag=true，则清除setting.user_tag common_bot bit位, common_bot_tag设置为0，不需要关注common_bot_tag入参
    // 如果clear_tag=false，则common_bot_tag入参一定大于0
    // 如果clear_tag=false，common_bot_tag入参只能从0变成设置值（如果用户setting.common_bot_tag==0，才能设置，否则拦截）
    // 如果clear_tag=false, 则设置common_bot user_tag bit位， common_bot_tag设置为common_bot_tag入参值
    // 如果clear_tag=false且common_bot_tag入参值和用户setting.comm_bot_tag相同，则返回幂等错误码(不下发dump)
    bool clear_tag = 2;
    // NEXT 3
}
