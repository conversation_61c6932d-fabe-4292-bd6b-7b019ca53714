syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";

message SetCustomerSymbolListReq {
  bool clear_all = 1;                       // 为true则以下参数都不生效
  bool usdt_symbol_enabled = 2;
  repeated int64 usdt_symbol_list = 3;
  bool usdc_symbol_enabled = 4;
  repeated int64 usdc_symbol_list = 5;
  bool usdc_future_symbol_enabled = 6;
  repeated string support_usdc_future = 7; // usdc交割按照base_currency进行白名单设置
  bool inverse_perp_symbol_enabled = 8;
  repeated int64 support_inverse_perp_symbol_list = 9;
  bool inverse_future_coin_enabled = 10;
  repeated string support_inverse_future_coin_list = 11;
  bool usdt_future_symbol_enabled = 12;
  repeated string support_usdt_future = 13; // usdt交割按照base_currency进行白名单设置
  // next = 14;
}
