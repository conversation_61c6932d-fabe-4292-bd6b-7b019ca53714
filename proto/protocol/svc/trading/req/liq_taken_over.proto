syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


import "option/common/money.proto";

message LiqTakenOver {
    string request_id = 1; // 请求ID，用于请求幂等
    int64 user_id = 2;     // 用户ID
    int64 symbol_id = 3;   // 合约id
    int64 position_idx = 4; // 仓位编号 0-弹仓模式持仓 1-双仓buy 2-双仓sell
    int64 side = 5; // 买卖方向
    com.bybit.option.common.Money bust_price = 6; // 破产价
    com.bybit.option.common.Money position_balance = 7; // 持仓价值
    com.bybit.option.common.PrecisionDecimal position_size = 8;    //
    int64 cross_seq = 9; // cross_seq
    int64 liq_system_user_id = 10; // 强平接管用户
    bool is_manual_taken_over = 11; // 手动全仓接管标识
    string view_id = 12; // 视图id，用来关联相关交易

   // NEXT: 13
}

message LiqBatchTakenOver {
    int64 user_id = 1;
    repeated LiqTakenOver taken_over_pz_list = 2;

    // NEXT 3
}