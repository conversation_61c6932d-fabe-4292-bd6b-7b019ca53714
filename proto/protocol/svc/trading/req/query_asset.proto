syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


// import "enums/ecoin/coin.proto";

import "svc/trading/req/query_position.proto";
import "svc/trading/req/query_order.proto";

message QueryAssetReq {
    // int64 user_id = 1;
    // enums.ecoin.Coin coin = 2;

    svc.trading.req.QueryPositionReq query_position = 3;
    svc.trading.req.QueryOrderReq query_order = 4;

    // NEXT: 5
}
