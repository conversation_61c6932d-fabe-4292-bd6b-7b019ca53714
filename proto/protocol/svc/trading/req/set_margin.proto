syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


// import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";

message SetMarginReq {
    // int64 user_id = 1;
    // enums.ecoin.Coin coin = 2;
    enums.esymbol.Symbol symbol = 3;
    int64 position_idx = 4; // 0:单持仓, 1:双向的多仓, 2:双向的空仓

    // 两者传入其中一个即可 (建议传position_balance), 内部逻辑会用公式反推出另外一个
    int64 new_position_balance_e8 = 5;

    int64 new_position_margin_e8 = 6;

    // NEXT: 7
}
