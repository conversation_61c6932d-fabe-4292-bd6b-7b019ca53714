syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


//import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";

message QueryReCalcParamsReq {
}

message ReCalcParamsResult {
    enums.esymbol.Symbol symbol = 1;

    int64 mark_price_x = 2;
    int64 last_price_x = 3;
    int64 expect_settle_price_x = 4;

    int32 price_scale = 9;

    repeated int64 buy_grade_qty = 5;
    map<int64, int64> buy_grade_qty_per_price = 6;

    repeated int64 sell_grade_qty = 7;
    map<int64, int64> sell_grade_qty_per_price = 8;
}
