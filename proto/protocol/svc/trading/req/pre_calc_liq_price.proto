syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


// import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";
import "enums/eordertype/order_type.proto";
import "enums/etimeinforce/time_in_force.proto";
import "enums/eqtytype/qty_type.proto";

message CalcParams {
    int64 price_x = 1; 
    int64 qty_x = 2;
    int64 qty_type_v = 3; // 依据下单类型的数量
    int64 designated_margin_e8 = 4; // 依据下单类型的指定保证金的数量
}

message PreCalcLiqPriceReq {
    enums.esymbol.Symbol symbol = 1;
    int64 position_idx = 2; // 0:单持仓, 非0:双仓
    enums.eqtytype.QtyType qty_type = 3; // 数量类型
    enums.eordertype.OrderType order_type = 4;
    enums.etimeinforce.TimeInForce time_in_force = 5;
    bool reduce_only = 6;
    int32 price_scale = 7;
    CalcParams buy_param = 8;
    CalcParams sell_param = 9;
}