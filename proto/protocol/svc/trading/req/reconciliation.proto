syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


// import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";

message ReconciliationReq {
    // int64 user_id = 1;
    // enums.ecoin.Coin coin = 2;
    enums.esymbol.Symbol symbol = 3;

    string reconciliation_id = 4;

    int64 reconciliation_time_e9 = 5;

    int64 cross_seq = 6;

    // NEXT: 7
}
