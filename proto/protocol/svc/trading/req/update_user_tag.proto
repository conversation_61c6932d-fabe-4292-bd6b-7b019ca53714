syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";

import "enums/esourcebusinessline/source_business_line.proto";


message UpdateUserTagReq {
    // 需要设置或者清除的tag 位，可以多个位一起设置或者清除，取值参考enum UserTag 定义
    int64 user_tag = 1;
    // = true 表示清除bit位，= false 表示设置bit位
    bool clear_tag = 2;
    // 请求来源的业务线，该字段不再使用，可以不用输入
    enums.esourcebusinessline.SourceBusinessLine source_business_line = 3;
    // NEXT 4
}
