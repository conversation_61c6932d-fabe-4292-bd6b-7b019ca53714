syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


// import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";

message MarkPriceUpdateReq {
    // int64 user_id = 1;
    // enums.ecoin.Coin coin = 2;
    enums.esymbol.Symbol symbol = 3;
    // int64 position_idx = 4; // 0:单持仓, 1:双向的多仓, 2:双向的空仓

    int64 mark_price_x = 5;
    int64 index_price_x = 6;
    int64 mark_price_seq = 7;
    int64 mark_price_time_e9 = 8;

    int32 price_scale = 9;
    // NEXT: 10
}
