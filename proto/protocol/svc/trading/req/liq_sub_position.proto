syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


import "option/common/money.proto";

message LiqSubPosition {
    string request_id = 1; // 请求ID，用于请求幂等
    int64 user_id = 2; // 用户ID
    int64 symbol_id = 3; // 合约id
    int64 position_idx = 4; // 仓位编号 0-弹仓模式持仓 1-双仓buy 2-双仓sell
    int64 side = 5; // 买卖方向
    com.bybit.option.common.Money bust_price = 6; // 破产价
    com.bybit.option.common.PrecisionDecimal sub_qty = 7; // 削减持仓量
    int64 cross_seq = 8; // cross_seq

    // NEXT: 9
}

message LiqBatchSubPosition {
    int64 user_id = 1;

    repeated LiqSubPosition sub_position_list = 2;
    // NEXT: 3
}