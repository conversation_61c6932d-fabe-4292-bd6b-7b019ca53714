syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


// import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";
import "enums/etriggerby/trigger_by.proto";
import "enums/epricedirection/price_direction.proto";

message TriggeredToActiveReq {
    // int64 user_id = 1;
    // enums.ecoin.Coin coin = 2;
    enums.esymbol.Symbol symbol = 3;
    // [不需要用到这个参数] int64 position_idx = 4; // 0:单持仓, 1:双向的多仓, 2:双向的空仓

    enums.etriggerby.TriggerBy trigger_by = 5;
    int64 trigger_src_seq = 7;
    int64 trigger_src_price_x = 8;
    int64 trigger_src_time_e9 = 9;
    string trigger_remark = 10;

    string triggered_order_id = 11; // 用于查找到被触发的条件单
    enums.etriggerby.TriggerBy orig_trigger_by = 12; // 被触发条件单的原触发来源
    enums.epricedirection.PriceDirection orig_expected_direction = 13; // 被触发条件单的原预期价格方向
    int64 orig_trigger_price_x = 14; // 被触发条件单的原触发价格

    int32 price_scale = 15;
    // NEXT: 16
}
