syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


//import "enums/ecoin/coin.proto";
//import "enums/esymbol/symbol.proto";

message UpdateSubscriptionReq {
    // last_asset_req_num=-1 首次订阅,发送init_dump
    // last_asset_req_num=0 首次订阅,不发送init_dump
    // last_asset_req_num=n 断点续传or内部重连:
    // * last_asset_req_num=cur_asset_req_num 不发送init_dump,等待下次更新
    // * last_asset_req_num<cur_asset_req_num 发送init_dump并重置asset_req_num
    // * last_asset_req_num>cur_asset_req_num 打印warn日志,发送init_dump并重置asset_req_num

    // 平时只传输增量变化数据
    map<int64, int64> added_online_users = 1; // user_id -> last_asset_req_num
    map<int64, int64> removed_online_users = 2; // user_id -> last_asset_req_num

    // 定期传输当前全量在线用户数据
    map<int64, int64> all_online_users = 3; // user_id -> last_asset_req_num

    // NEXT: 4
}
