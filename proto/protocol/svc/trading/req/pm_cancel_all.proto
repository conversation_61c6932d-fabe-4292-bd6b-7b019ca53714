syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


message PMCancelAllReq {
    int32 symbol = 1;
    int64 position_idx = 2; // -1:双仓全部仓位, 0:单持仓, 1:双向的多仓, 2:双向的空仓
    int32 cancel_type = 3;
    int32 last_cancel_type = 4;
    string remark = 5;
    int32 stop_order_type = 6; // 默认撤掉所有的活动订单, 传`Stop`则会将所有条件单解除
    int32 cancel_all_type = 7; // 取消订单的类型 0:取消所有活动单(兼容stop_order_type) 1:取消全部条件单(兼容stop_order_type)  2:取消全部止盈止损条件单  3：取消全部普通条件单与追踪止损条件单  4：取消所有开仓单(非RO|COT的条件单和活动单)  5：取消所有开启MMP的订单
    int64 mmp_end_time_e9 = 8; // 本次冻结结束时间: MMP触发时刻+冻结期

// NEXT: 9
}
