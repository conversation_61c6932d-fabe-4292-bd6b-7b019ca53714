syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


// import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";
import "enums/ecptradeuserrole/copy_trade_user_role.proto";
import "svc/trading/req/set_leverage_with_symbols.proto";
import "svc/trading/req/switch_isolated_with_symbols.proto";
import "svc/trading/req/switch_position_mode_with_symbols.proto";

message SetCopyTradeAccountReq {
    // int64 user_id = 1;
    // enums.ecoin.Coin coin = 2;

    // 设置user tag
    oneof optional_new_user_tag {
        int64 user_tag = 3;
    }

    // 用于是否强制修改 user tag; true:强制修改（跳过存在不能修改的错误; 默认false:不强制
    bool force_set_new_user_tag = 7;

    // 批量设置单双仓模式 会吃掉没有修改的错误
    repeated svc.trading.req.SwitchPositionModeWithSymbolsReq.SwitchPositionModeWithSymbol new_position_mode_setting = 4;

    // 批量设置全逐仓模式 会吃掉没有修改的错误
    repeated svc.trading.req.SwitchIsolatedWithSymbolsReq.SwitchIsolatedWithSymbol new_isolated_setting = 5;

    // 批量设置杠杆 有报错就报错
    repeated svc.trading.req.SetLeverageWithSymbolsReq.SetLeverageWithSymbol new_leverage_setting = 6;

    // NEXT: 8
}
