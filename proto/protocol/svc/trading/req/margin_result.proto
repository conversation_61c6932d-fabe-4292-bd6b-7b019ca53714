syntax = "proto3";

package svc.trading.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/trading/req";


import "svc/req_resp_header.proto";
import  "option/margin/margin_service.proto";

message MarginResult {
    svc.ReqRespHeader header = 1;
    bool biz_success = 2; // 业务处理是否成功
    string biz_code = 3; // 业务状态码
    string biz_message = 4; // 业务处理信息
    bool pm_mode = 5; // 是否开启组合保证金模式
    string ori_req_id = 6; // 交易发送到margin服务的请求id
    WalletSnapshot wallet_snapshot =7; // 用户钱包快照
    com.bybit.option.margin.trade.PassThroughInfo path_thru_info = 8;

    // NEXT: 9
}

message WalletSnapshot {
    int64 user_id = 1; // 用户ID
    int64 account_id = 2; // 账户ID
    int64 wallet_balance_e8 = 3;  // 钱包余额真钱(WB): 出入金,手续费,盈亏,返佣等
    int64 total_position_balance_e8 = 4; // 所有持仓的预占用真钱(PB): 逐:固定分配后盈亏自负; 全:IM%+loss浮动 (如果支持浮盈开仓的话,这个值最终可能是负数)
    int64 available_balance_e8 = 5;  // 可用余额(AB): WB-PB-OB => AB (保证AB>=0)
    bool pm_mode = 6;

  // NEXT: 7
}



