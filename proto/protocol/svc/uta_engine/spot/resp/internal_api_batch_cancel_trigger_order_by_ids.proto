syntax = "proto3";

package svc.uta_engine.spot.resp;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/spot/resp";

import "svc/uta_engine/spot/common/common.proto";

message BatchCancelTriggerOrderByIdsResponse {
    repeated CancelTriggerOrderResult results = 1;
}

message CancelTriggerOrderResult {
    svc.uta_engine.spot.common.OrderStatusEnum status = 1; // 订单状态
    string order_id = 2; // 订单ID

    // 取消单业务码
    svc.uta_engine.spot.common.ErrorCode code = 3;
    string client_order_id = 4; // 客户端订单ID
}