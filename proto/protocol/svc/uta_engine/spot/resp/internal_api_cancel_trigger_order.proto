syntax = "proto3";

package svc.uta_engine.spot.resp;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/spot/resp";

import "models/tradingdto/spot_transact_dto.proto";

message CancelTriggerOrderResponse {
    string order_id = 1; // 订单ID，必须字段
    string client_order_id = 2; // 客户端订单ID
    int32 err_code = 3;
    string err_msg = 4;

    models.tradingdto.UnifySpotTransactDTO order = 5;
}