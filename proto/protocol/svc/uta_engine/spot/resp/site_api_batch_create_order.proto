syntax = "proto3";

package svc.uta_engine.spot.resp;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/spot/resp";
import "svc/uta_engine/spot/resp/site_api_create_order.proto";

message BatchCreateOrderRespSiteApiItem {
    int64 code = 1;
    string msg = 2;
    ClientCreateOrderResponse rsp = 3;
}

message BatchCreateOrderRespSiteApiResult {
    repeated BatchCreateOrderRespSiteApiItem list = 1;
}