syntax = "proto3";

package svc.uta_engine.spot.resp;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/spot/resp";

message PlanSpotCreateOrderResponse{
  string                time				 	= 1;
  string                orderId					= 2;
  string                accountId				= 3;
  string                accountType				= 4;
  string                brokerUserId			= 5;
  string                clientOrderId			= 6;
  string                symbolId				= 7;
  string                symbolName				= 8;
  string                baseTokenId				= 9;
  string                baseTokenName			= 10;
  string                quoteTokenId			= 11;
  string                quoteTokenName			= 12;
  string                price					= 13;
  string                origQty					= 14;
  string                type					= 15;
  string                side					= 16;
  string                status					= 17;
  string                statusDesc				= 18;
  string                exchangeId				= 19;
  string                orgId					= 20;
  string                triggerPrice			= 21;
  string                triggerTime				= 22;
  string                quotePrice				= 23;
  string                executedOrderId			= 24;
  string                executedPrice			= 25;
  string                executedQty				= 26;
  string                executedAmount			= 27;
  string                updateTime				= 28;
  string                timeInForce				= 29;
  string                pushTime				= 30;
  string                tp_trigger_price = 31; // 止盈触发价格
  string                sl_trigger_price = 32;   // 止损触发价格 
  string                tp_limit_price = 33; // 限价止盈单价格
  string                sl_limit_price = 34; // 限价止损单价格
  string                tp_order_type = 35; // 止盈订单类型
  string                sl_order_type = 36; // 止损订单类型 
  string                market_unit = 37;
}