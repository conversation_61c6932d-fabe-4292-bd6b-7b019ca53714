syntax = "proto3";

package svc.uta_engine.spot.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/spot/req";

import "svc/uta_engine/spot/common/common.proto";

message CancelTriggerOrderRequest {
    int64 user_id = 1; // 交易账户ID，必须字段
    string client_order_id = 2; // 客户端订单ID，可选，无订单ID时必传
    string order_id = 3; // 订单ID，可选，无客户端订单ID时必传
    int32 plan_type = 4; // 计划类型
    bool need_reply_after_cross = 5; // 是否需要设置发撮合后回复grpc
    // next 6
}