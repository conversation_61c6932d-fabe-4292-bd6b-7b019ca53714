syntax = "proto3";

package svc.uta_engine.spot.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/spot/req";

import "svc/req_resp_header.proto";

message WalletFrozenRequest {
    svc.ReqRespHeader header = 1;

    string coin = 2;
    string amount = 3;

    bool is_frozen = 4; // true 冻结  false 解冻
    bool un_frozen_and_transfer = 5; // 解冻时是否划转
    int32 trans_type = 6;
    string transfer_amount = 7; // 划转金额

    string remark = 8;
    bool frozen_record_exist_db = 9;
}