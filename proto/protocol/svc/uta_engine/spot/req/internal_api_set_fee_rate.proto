syntax = "proto3";

package svc.uta_engine.spot.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/spot/req";

message SetFeeRateRequest {
    int64 user_id = 1;

    int32 type = 2; // 0-新增 1-更新 2-删除

    string symbol_name = 3; // 为空代表操作的是用户级别费率

    string taker_fee_rate = 4;
    string maker_fee_rate = 5;
    string taker_zipper_id = 6;
    string maker_zipper_id = 7;

    int32 is_final = 8; // 0=false 1=true

    string coin_name = 9;
    bool is_fiat = 10; // 是否是法币
    // NEXT: 11
}