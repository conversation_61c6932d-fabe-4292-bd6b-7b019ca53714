syntax = "proto3";

package svc.uta_engine.spot.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/spot/req";

import "svc/uta_engine/spot/common/common.proto";
import "enums/estopordertype/stop_order_type.proto";
import "enums/eside/side.proto";
import "enums/eordertype/order_type.proto";

message BatchCancelOrderRequest {
    int64 user_id = 1;
    string symbol = 2; // 有symbol 则忽略 base_token和quote_token 否则根据base_token和quote_token找对应的symbol列表
    string base_token = 3;
    string quote_token = 4;
    repeated enums.estopordertype.StopOrderType stop_order_types = 5;
    enums.eside.Side side = 6;
    bool limit = 7;
    bool limit_maker = 8;
    bool market = 9;
}