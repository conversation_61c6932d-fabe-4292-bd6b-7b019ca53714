syntax = "proto3";

package svc.uta_engine.dmm.resp;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/dmm/resp";

message CoinItemV5 {
    string coin = 1;
    string equity = 2;
    string usd_value = 3;
    string wallet_balance = 4;
    string free = 5;
    string locked = 6;
    string spot_hedging_qty = 7;
    string borrow_amount = 8;
    string available_to_borrow = 9;
    string available_to_withdraw = 10;
    string accrued_interest = 11;
    string total_order_i_m = 12;
    string total_position_i_m = 13;
    string total_position_m_m = 14;
    string unrealised_pnl = 15;
    string cum_realised_pnl = 16;
    string bonus = 17;
    string margin_collateral = 18;
    string collateral_switch = 19;
}

message WalletItemV5 {
    string account_type = 1;
    string account_l_t_v = 2;
    string account_i_m_rate = 3;
    string account_m_m_rate = 4;
    string total_equity = 5;
    string total_wallet_balance = 6;
    string total_margin_balance = 7;
    string total_available_balance = 8;
    string total_perp_u_p_l = 9;
    string total_initial_margin = 10;
    string total_maintenance_margin = 11;
    repeated CoinItemV5 coin = 12;
}

message QueryWalletV5 {
    repeated WalletItemV5 list = 1;
}
