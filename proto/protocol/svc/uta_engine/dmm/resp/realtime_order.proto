syntax = "proto3";

package svc.uta_engine.dmm.resp;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/dmm/resp";

message OrderItemV5 {
    string order_id = 1;
    string order_link_id = 2;
    string symbol = 3;
    string price = 4;
    string qty = 5;
    string side = 6;
    string is_leverage = 7;
    int64 position_idx = 8;
    string order_status = 9;
    string create_type = 10;
    string cancel_type = 11;
    string reject_reason = 12;
    string avg_price = 13;
    string leaves_qty = 14;
    string leaves_value = 15;
    string cum_exec_qty = 16;
    string cum_exec_value = 17;
    string cum_exec_fee = 18;
    string time_in_force = 19;
    string order_type = 20;
    string stop_order_type = 21;
    string order_iv = 22;
    string market_unit = 23;
    string trigger_price = 24;
    string take_profit = 25;
    string stop_loss = 26;
    string tpsl_mode = 27;
    string oco_trigger_by = 28;
    string tp_limit_price = 29;
    string sl_limit_price = 30;
    string tp_trigger_by = 31;
    string sl_trigger_by = 32;
    int32 trigger_direction = 33;
    string trigger_by = 34;
    string last_price_on_created = 35;
    bool reduce_only = 36;
    bool close_on_trigger = 37;
    string place_type = 38;
    string smp_type = 39;
    string smp_group = 40;
    string smp_order_id = 41;
    string created_time = 42;
    string updated_time = 43;
}

message RealtimeOrderV5 {
    string category = 1; // spot, linear, option
    repeated OrderItemV5 list = 2;
    string next_page_cursor = 3;
}
