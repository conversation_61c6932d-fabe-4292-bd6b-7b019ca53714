syntax = "proto3";

package svc.uta_engine.dmm.resp;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/dmm/resp";


message PositionItemV5 {
    int64 position_idx = 1;
    int64 risk_id = 2;
    string risk_limit_value = 3;
    string symbol = 4;
    string side = 5;
    string size = 6;
    string avg_price = 7;
    string position_value = 8;
    int64 trade_mode = 9;
    int64 auto_add_margin = 10;
    string position_status = 11;
    string leverage = 12;
    string mark_price = 13;
    string liq_price = 14;
    string bust_price = 15;
    string position_i_m = 16;
    string position_m_m = 17;
    string position_balance = 18;
    string tpsl_mode = 19;
    string take_profit = 20;
    string stop_loss = 21;
    string trailing_stop = 22;
    string session_avg_price = 23;
    string delta = 24;
    string gamma = 25;
    string vega = 26;
    string theta = 27;
    string unrealised_pnl = 28;
    string cur_realised_pnl = 29;
    string cum_realised_pnl = 30;
    int64 adl_rank_indicator = 31;
    bool is_reduce_only = 32;
    string mmr_sys_updated_time = 33;
    string leverage_sys_updated_time = 34;
    string created_time = 35;
    string updated_time = 36;
    int64 seq = 37;
}

message QueryPositionV5 {
    string category = 1;
    repeated PositionItemV5 list = 2;
    string next_page_cursor = 3;
}
