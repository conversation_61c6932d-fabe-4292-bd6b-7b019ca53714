syntax = "proto3";

package svc.uta_engine.dmm.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/dmm/req";


message RealtimeOrderV5 {
    string category = 1; // spot, linear, option
    string symbol = 2;
    string base_coin = 3;
    string settle_coin = 4;
    string order_id = 5;
    string order_link_id = 6;
    string open_only = 7;
    string order_filter = 8;
    int32 limit = 9;
    string cursor = 10;
}
