syntax = "proto3";

package svc.uta_engine.dmm;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/spot";

import "svc/req_resp_header.proto";

message WalletAbstract {
  int64   user_id = 1;
  string  coin_name = 2;
  string  wallet_balance = 3;
  string  order_balance = 4;
  string  available_balance = 5;
  int64   wallet_version = 6;
  int64   updated_at_e9 = 7;
}

message QueryUserWallet{
  string symbol_name = 1; // 查询base_coin和settle_coin的钱包
  string coin_name = 2; // 查询coin
}

message SpotMmRequest {
  svc.ReqRespHeader req_header = 1;

  oneof req_body {
    QueryUserWallet query_wallet = 3; //查询钱包;
  }
}

message SpotMmResponse {
  svc.ReqRespHeader resp_header = 1;

  int32 ret_code = 2; // 错误码, 0表示处理成功
  string ret_msg = 3; // 错误消息
  string ext_code = 4; // 扩展错误码

  repeated WalletAbstract wallets = 5; //钱包摘要
}