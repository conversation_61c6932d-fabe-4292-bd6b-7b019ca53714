syntax = "proto3";

package filter_helper;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/crossreceiver";

message OffsetReqInfo {
  int64 cross_seq = 1; // 撮合序号
}

message GetOffsetRelRequest {
  int32 shard_id = 1;   // shard_id   分片编号 0-9
  map<int32, OffsetReqInfo> MapCrossOffsetReq = 2; // key:撮合编号， value: offset请求信息
}


enum OffsetRespStatus {
  SUCCESS = 0; // 成功
  PARAM_ERR = 1; // 参数错误
  NOT_FOUND_MAX = 2; // 没有找到，比内存中最大的还大, 返回的crossSeq和offset为内存中最大的
  NOT_FOUND_MIN = 3; // 没有找到，比内存中最小的还小，返回的crossSeq和offset为内存中最小的
  NOT_FOUND_NODATA = 4; // 没有找到，新上撮合或者filter topic中还没有数据，返回的crossSeq为-1，offset为-2
  NOT_FOUND_CROSS_ID = 5; // 内存中不存在该撮合id
}

// offset信息
message OffsetRespInfo {
  OffsetRespStatus resp_status = 1;  // 状态码
  int64 req_cross_seq = 2; // 请求的crossSeq
  int64 resp_cross_seq = 3; // 找到的crossSeq
  int64 resp_offset = 4; // 找到的offset
  int64 resp_cross_seq_HDTS_ts = 5; // crossSeq写入HDTS的时间戳
}

message GetOffsetRelResponse {
  GetOffsetRelRequest reqInfo = 1; // 请求信息
  map<int32, OffsetRespInfo> MapCrossOffsetResp = 2; // key:撮合编号， value: offset回复信息
  int64 req_ts_e9 = 3; // 收到请求的时间戳 ns
  int64 resp_ts_e9 = 4; // 回复的时间戳 ns
}


service FilterHelperAPI {
  rpc GetOffsetRel(GetOffsetRelRequest) returns (GetOffsetRelResponse);
}