syntax = "proto3";

package svc.uta_engine;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine";


import "svc/uta_engine/spot/req/open_api_v3_batch_cancel_order.proto";
import "svc/uta_engine/spot/req/open_api_v3_cancel_order.proto";
import "svc/uta_engine/spot/req/open_api_v3_cancel_order_by_ids.proto";
import "svc/uta_engine/spot/req/open_api_v3_create_order.proto";

import "svc/uta_engine/spot/resp/open_api_v3_batch_cancel_order.proto";
import "svc/uta_engine/spot/resp/open_api_v3_cancel_order.proto";
import "svc/uta_engine/spot/resp/open_api_v3_cancel_order_by_ids.proto";
import "svc/uta_engine/spot/resp/open_api_v3_create_order.proto";

service UTAEngineSpotOpenApiV3Service {

    // 内部下单接口
    rpc CreatePrivateOrder(spot.req.CreateOrderV3Request) returns (spot.resp.CreateOrderV3Response);

    // 撤单
    rpc CancelPrivateOrder(spot.req.CancelOrderV3Request) returns (spot.resp.CancelOrderV3Response);

    // 批量撤销订单
    rpc CancelOrders(spot.req.BatchCancelOrderV3Request) returns (spot.resp.BatchCancelOrderV3Response);

    // 根据订单号批量撤销订单
    rpc CancelOrdersByIds(spot.req.CancelOrdersByIdsV3Request) returns (spot.resp.CancelOrdersByIdsV3Response);
}



