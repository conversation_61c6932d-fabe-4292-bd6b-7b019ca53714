syntax = "proto3";

package svc.uta_engine;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine";

import "svc/uta_engine/strategy_trade/req/pre_check.proto";
import "svc/uta_engine/strategy_trade/resp/pre_check.proto";
import "svc/uta_engine/strategy_trade/resp/ddh_strategy_create_order_resp.proto";
import "svc/uta_engine/strategy_trade/resp/ddh_strategy_pre_check_resp.proto";
import "svc/uta_engine/strategy_trade/req/ddh_strategy_create_order_req.proto";
import "svc/uta_engine/strategy_trade/req/ddh_strategy_pre_check_req.proto";

service UTAEngineStrategyTradeInnerService {

  rpc PreCheck(strategy_trade.req.PreCheckReq) returns (strategy_trade.resp.PreCheckResp);

  rpc DDHStrategyPreCheck(strategy_trade.req.DDHStrategyPreCheckReq) returns (strategy_trade.resp.DDHStrategyPreCheckResp);

  rpc DDHStrategyCreateOrder(strategy_trade.req.DDHStrategyCreateOrderReq) returns (strategy_trade.resp.DDHStrategyCreateOrderResp);
}