syntax = "proto3";

package svc.uta_engine;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine";

import "svc/uta_engine/block_trade/req/pre_occupy.proto";
import "svc/uta_engine/block_trade/resp/pre_occupy.proto";
import "svc/uta_engine/block_trade/req/create_block_trade.proto";
import "svc/uta_engine/block_trade/resp/create_block_trade.proto";
import "svc/uta_engine/block_trade/req/query_order.proto";
import "svc/uta_engine/block_trade/resp/query_order.proto";

service UTAEngineBlockTradeService {
    rpc PreOccupy(svc.uta_engine.block_trade.req.PreOccupyReq) returns (svc.uta_engine.block_trade.resp.PreOccupyResp);
    rpc CreateBlockTrade(svc.uta_engine.block_trade.req.CreateBlockTradeReq) returns (svc.uta_engine.block_trade.resp.CreateBlockTradeResp);
    rpc QueryOrder(svc.uta_engine.block_trade.req.QueryOrderReq) returns (svc.uta_engine.block_trade.resp.QueryOrderResp);
}
