syntax = "proto3";

package svc.uta_engine.block_trade.resp;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/blocktrade/resp";

message PreOccupyResp {
    // retCode 0:表示受理成功 非0都是失败
    int64 ret_code = 1;
    // ret_msg
    string ret_msg = 2;

    repeated PreOrderRespItem pre_order_resp_items = 3;
}

message PreOrderRespItem {
    // 【必填】
    string order_id = 1;
    string order_link_id = 2;
    int64 fee_rate_e8 = 3;
    string fee_rate_desc = 4;
    int64 exec_fee_e8 = 5;
    int64 per_hand_fee_e8 = 6;
}
