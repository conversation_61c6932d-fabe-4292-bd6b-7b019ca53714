syntax = "proto3";

package svc.uta_engine.block_trade.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/blocktrade/req";

import "enums/eside/side.proto";
import "enums/eopplatform/op_platform.proto";

message CreateBlockTradeReq {
    // 【必填】blockTradeId,一次请求中的所有OrderItem下面的blockTradeId都一样
    string block_trade_id = 1;
    // 请求来源的ip值
    string ip = 2;
    // op_from
    string op_from = 3; // = 网关的 x-referer
    // op_platform
    enums.eopplatform.OpPlatform op_platform = 4; // = 网关的 platform
    // 【必填】
    int64 taker_user_id = 5;
    // 【必填】
    int64 taker_account_id = 6;
    // 【必填】
    int64 maker_user_id = 7;
    bool maker_is_unified = 9;
    // 【必填】OrderItem 这里是list结构,每个对象放了具体的请求参数
    repeated OrderItem order_items = 10;

    bool no_spot_fee = 11;
    bool no_future_fee = 12;
    bool no_option_fee = 13;
    // 是否是移仓请求
    bool move_position = 14;
}

// 下单请求请求对象单笔订单数据模型
message OrderItem {
    // 【必填】产品类型: linear:正向合约;inverse:反向合约;option:期权;spot:现货;以及其他暂未定义的类型
    string category = 1;
    // 【必填】symbol
    string symbol = 2;
    // 【必填】price
    string price = 3;
    // 【必填】qty
    string qty = 4;
    // 【必填】taker_side
    enums.eside.Side taker_side = 6;
    // 【必填】
    string taker_order_id = 7;
    string taker_order_link_id = 8;
    int64 taker_fee_rate_e8 = 9;
    string taker_fee_rate_desc = 10;
    int64 taker_exec_fee_e8 = 11;
    int64 taker_per_hand_fee_e8 = 12;
    // 【必填】
    int64 taker_position_idx = 19;

    // 【必填】
    string maker_order_id = 13;
    string maker_order_link_id = 14;
    int64 maker_fee_rate_e8 = 15;
    string maker_fee_rate_desc = 16;
    int64 maker_exec_fee_e8 = 17;
    int64 maker_per_hand_fee_e8 = 18;

    // 【必填】
    int64 maker_position_idx = 20;

    // 【必填】
    int64 maker_account_id = 21;
}