// proto语言本本，使用proto3
syntax = "proto3";

package svc.uta_engine.option;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/option";

// 全量标签关系查询请求模型(默认mmp)
message MmpQueryUserMmpRequest{

  // 币种
  string baseCoin = 1;
}

// 限权咨询请求模型
message MmpQueryUserMmpResponse{
  // 标签体
  repeated UserMmpLabelModel result = 3;
}

message UserMmpLabelModel{

  // 币种
  string baseCoin = 1;

  // mmp是否开启
  bool mmpEnabled = 2;

  // mmp滚动时间窗口
  string window         = 4;

  // 冻结时间窗口
  string frozenPeriod   = 5;

  // 数量上限
  string qtyLimit = 6;

  // delta 上限
  string deltaLimit = 7;

  // MMP冻结时间戳
  string mmpFrozenUntil = 8;

  // mmp是否冻结
  bool mmpFrozen = 9;

}
