syntax = "proto3";

package svc.uta_engine.resp;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/resp";

message BatchCreateOrderRespV5ResultListItem {
    string category = 1;
    string symbol = 2;
    string order_id = 3;
    string order_link_id = 4;
    string create_at = 5;
}

message BatchCreateOrderRespV5Result {
    repeated BatchCreateOrderRespV5ResultListItem list = 1;
}

message BatchCreateOrderRespV5ExtInfoItem {
    int64 code = 1;
    string msg = 2;
}

message BatchCreateOrderRespV5ExtInfo {
    repeated BatchCreateOrderRespV5ExtInfoItem list = 1;
}

// BatchCreateOrderRespV5ExtInfoItem 结构是bgw组装的 这里无需处理
message BatchCreateOrderRespV5 {
    repeated BatchCreateOrderRespV5ResultListItem list = 1;
}
