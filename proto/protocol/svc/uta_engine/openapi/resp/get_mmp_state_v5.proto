syntax = "proto3";

package svc.uta_engine.resp;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/resp";

message GetMmpStateRespV5 {
    repeated MmpLabelModel result = 1;
}

message MmpLabelModel {
    string baseCoin = 1;        // 币种
    bool mmpEnabled = 2;        // mmp是否开启
    string window = 4;          // mmp滚动时间窗口
    string frozenPeriod = 5;    // 冻结时间窗口
    string qtyLimit = 6;        // 数量上限
    string deltaLimit = 7;      // delta 上限
    string mmpFrozenUntil = 8;  // MMP冻结时间戳
    bool mmpFrozen = 9;         // mmp是否冻结
}