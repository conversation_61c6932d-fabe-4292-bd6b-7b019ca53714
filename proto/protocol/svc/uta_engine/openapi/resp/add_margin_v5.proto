syntax = "proto3";

package svc.uta_engine.resp;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/resp";


message AddMarginResponseV5 {
    string category = 1;
    int32 position_idx = 2;
    int32 risk_id = 3;
    string risk_limit_value = 4;
    string symbol = 5;
    string size = 6;
    string position_value = 7;
    string avg_price = 8;
    string liq_price = 9;
    string bust_price = 10;
    string mark_price = 11;
    string leverage = 12;
    int32 auto_add_margin = 13;
    string position_status = 14;
    string position_im = 15 [json_name = "positionIM"];
    string position_mm = 16 [json_name = "positionMM"];
    string unrealised_pnl = 17;
    string cum_realised_pnl = 18;
    string take_profit = 19;
    string stop_loss = 20;
    string trailing_stop = 21;
    string created_time = 22;
    string updated_time = 23;
}