syntax = "proto3";

package svc.uta_engine.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/req";


message CreateOrderReqV5 {
    string category = 1; // spot, linear, option, inverse
    string symbol = 2;
    int64 position_idx = 3;
    string side = 4; // Buy Sell
    string order_type = 5; // Market Limit
    string qty = 6; // 若category=spot, 且是Market Buy單，則qty表示為報價幣種金額
    string price = 7;
    string order_link_id = 8;
    string time_in_force = 14;
    string order_filter = 9; // 僅對現貨有效. Order: 普通單,tpslOrder: 止盈止損單. 若不傳, 默認Order
    int32 is_leverage = 10; // 是否借貸. 僅現貨有效. 0(default): 否, 1: 是
    /**
        對於期貨, 是條件單觸發價格參數. 若您希望市場價是要上升後觸發, 確保:
        triggerPrice > 市場價格
        否則, triggerPrice < 市場價格
        對於現貨, 這是下止盈止損單的觸發價格參數
     */
    string trigger_price = 11;
    /**
        條件單參數. 用於辨別期望的方向.
        1: 當市場價上漲到了triggerPrice時觸發條件單
        2: 當市場價下跌到了triggerPrice時觸發條件單
     */
    int32 trigger_direction = 12;
    string trigger_by = 13; // 條件單參數. 觸發價格類型. LastPrice, IndexPrice, MarkPrice
    string take_profit = 15;
    string stop_loss = 16;
    string tp_trigger_by = 17;
    string sl_trigger_by = 18;
    bool reduce_only = 19;
    bool close_on_trigger = 20;
    string order_iv = 21;
    bool mmp = 22;
    /**
        防自成交模式
        CancelMaker
        CancelTaker
        CancelBoth
        None
     */
    string smp_type = 23;

    /**
        下单带tpsl参数 (升级版tpsl)
     */
    string tpsl_mode = 24;
    string tp_limit_price = 25;
    string sl_limit_price = 26;
    string tp_order_type = 27;
    string sl_order_type = 28;
    string market_unit = 29; // 交易单位. BaseCoin：基础币种，QuoteCoin：计价币种，限价单不传该字段，市价单不传买单默认QuoteCoin，卖单默认BaseCoin
    string slippage_tolerance      = 30;      // 市价单滑点值， - 百分比（例如1%）或者价差（50 USDT）
    string slippage_tolerance_type = 31;      // 市价单滑点类型, tick_size, percent
}
