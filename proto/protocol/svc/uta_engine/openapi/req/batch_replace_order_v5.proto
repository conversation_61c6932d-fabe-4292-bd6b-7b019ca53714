syntax = "proto3";

package svc.uta_engine.req;
option go_package = "code.bydev.io/trading/idl/pb/svc/utaengine/req";

message BatchReplaceOrderReqV5Item {
    // 保持和replace_order接口二进制兼容？
    string symbol = 2;
    string order_id = 3;
    string order_link_id = 4;
    string qty = 5; // 若category=spot, 且是Market Buy單，則qty表示為報價幣種金額
    string price = 6;
    string order_iv = 7;
    string trigger_price = 8;
    string trigger_by = 9; // 條件單參數. 觸發價格類型. LastPrice, IndexPrice, MarkPrice
    string take_profit = 10;
    string stop_loss = 11;
    string tp_trigger_by = 12;
    string sl_trigger_by = 13;
    string tp_limit_price = 14;
    string sl_limit_price = 15;
    string tpsl_mode = 16;
}

message BatchReplaceOrderReqV5 {
    string category = 1; // spot, linear, option, inverse

    repeated BatchReplaceOrderReqV5Item request = 2;
}
