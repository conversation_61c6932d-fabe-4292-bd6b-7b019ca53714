syntax = "proto3";

package models.mdpdto;
option go_package = "code.bydev.io/trading/idl/pb/models/mdpdto";


import "enums/esymbol/symbol.proto";
import "enums/eside/side.proto";
import "enums/etickdirection/tick_direction.proto";
import "svc/req_resp_header.proto";

message InstrumentDTO {
    int32 id = 1;
    enums.esymbol.Symbol symbol = 2;
    int64 last_price_x = 3;
    enums.etickdirection.TickDirection last_tick_direction = 4;
    int64 prev_price_24h_x = 5;
    int64 price_24h_pcnt_e6 = 6;
    int64 high_price_24h_x = 7;
    int64 low_price_24h_x = 8;
    int64 prev_price_1h_x = 9;
    int64 price_1h_pcnt_e6 = 10;
    int64 mark_price_x = 11;
    int64 index_price_x = 12;
    int64 open_interest_e8 = 13;
    int64 open_value_e8 = 14;
    int64 total_turnover_e8 = 15;
    int64 turnover_24h_e8 = 16;
    int64 total_volume_e8 = 17;
    int64 volume_24h_e8 = 18;
    int64 funding_rate_e6 = 19;
    int64 predicted_funding_rate_e6 = 20;
    int64 cross_seq = 21;
    //string created_at = 22;
    //string updated_at = 23;
    string next_funding_time = 24;
    int64 count_down_hour = 25;
    // price_x的放大倍数
    int32 price_scale = 26;
}

message TradeItem {
    enums.esymbol.Symbol symbol = 1;
    enums.etickdirection.TickDirection last_tick_direction = 2;
    int64 exec_price_x = 3;
    int64 exec_qty_x = 4;
    int64 transact_time_e9 = 5; // 精度弄低一点?
    enums.eside.Side side = 6;
    string exec_id = 7;
    // price_x的放大倍数
    int32 price_scale = 8;
}

message CandleDTO {
    int64 start_timestamp = 1; // UNIX timestamp, 精确到秒
    int64 end_ex_timestamp = 2; // UNIX timestamp, 精确到秒
    int64 open_x = 3;
    int64 close_x = 4;
    int64 high_x = 5;
    int64 low_x = 6;
    int64 turnover_e8 = 7;
    int64 volume_x = 8;
    bool confirm = 9;
    //int64 cross_seq = 10;
    //int64 transact_time_e9 = 11;
    // price_x的放大倍数
    int32 price_scale = 12;
}

message PerPeriodKlineData {
    // Symbol name or ticker
    string symbol = 1;

    // Symbol resolution, Possible resolutions are:
    // daily (`1D`, `2D` ... )
    // weekly (`1W`, `2W` ...)
    // monthly (`1M`, `2M`...)
    // intra-day resolution - minutes(`1`, `2` ...)
    string resolution = 2; // 原名: period

    repeated CandleDTO candles = 3;
}

message MdpResultDTO {
    // 将请求相关的header信息dump一份
    svc.ReqRespHeader header = 1;

    enums.esymbol.Symbol symbol = 2;
    int64 applied_cross_seq = 3; // 最后一个已经applied的撮合包seq
    int64 applied_transact_time_e9 = 4; // 对外推送置0
    int64 prev_cross_seq = 5; // 上一次已经输出mdp_result的撮合包seq
    int64 prev_transact_time_e9 = 6; // 对外推送置0

    InstrumentDTO instrument = 7; // 每次保留最新完整object

    map<int64, int64> sell_book = 8; // price_x => qty_x (qty<=0表示删除; qty>0表示upsert)
    int64 ask1_price_x = 9; // 0表示sell_book为空
    int64 bid1_price_x = 10; // 0表示buy_book为空
    map<int64, int64> buy_book = 11; // price_x => qty_x (qty<=0表示删除; qty>0表示upsert)

    repeated TradeItem recent_trades = 12; // 尽量保证(prev,applied]之间的成交都完整增量输出, init_dump只保留最新200个

    // mdp只保留最近1440根蜡烛1min精度的K线数据 (正好24小时)
    // 其他更大精度的K线由下游自己aggregate算出来
    // K线考虑转换成float64?  在行情cache层  同时支持rest查询 和 ws推送
    map<string, PerPeriodKlineData> kline_data = 13; // period => kline_data

    map<int32, InstrumentDTO> other_instruments = 14; // other_symbol => instrument

    // price_x的放大倍数
    int32 price_scale = 15;
    // NEXT: 16
}
