syntax = "proto3";

package models.quote;
option go_package = "code.bydev.io/trading/idl/pb/models/quote";

message EtpNetWorth {
    string symbol = 1;
    string nav = 2;
    int64  nav_time = 3;
    string basket = 4;
    string leverage = 5;
    string loan = 6;
    string token_issued = 7;
    string notional_position = 8;
    int32 lt_status = 9;
    string params = 10;
}

message EtpNetWorthResult {
    int64 timestamp = 1;
    repeated EtpNetWorth symbol_etp_net_worth = 2;
}