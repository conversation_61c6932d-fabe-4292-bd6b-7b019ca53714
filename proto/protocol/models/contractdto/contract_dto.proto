syntax = "proto3";

package models.contractdto;
option go_package = "code.bydev.io/trading/idl/pb/models/contractdto";

import "enums/esymbol/symbol.proto";
import "enums/econtracttype/contract_type.proto";
import "enums/econtractstatus/contract_status.proto";
import "enums/ecoin/coin.proto";
import "enums/epositionmode/position_mode.proto";
import "enums/equartertype/quarter_type.proto";
import "option/model/passthrough_dto.proto";

message ContractDTO {
    //NEXT:21
    //合约唯一标识
    enums.esymbol.Symbol symbol = 1;

    //合约名称 例如BTCUSD0626
    string symbol_name = 2;

    //定价symbol 比如BTCUSD 表示取BTCUSD的指数价格
    enums.esymbol.Symbol quote_symbol = 3;

    //合约类型 交割 永续
    enums.econtracttype.ContractType contract_type = 4;

    //合约状态
    enums.econtractstatus.ContractStatus contract_status = 5;

    //合约所属年份
    int32 year = 6;

    //合约对应的交易币种
    enums.ecoin.Coin coin = 7;

    //支持单向开仓还是双向开仓
    enums.epositionmode.PositionMode mode = 8;

    //是否支持浮盈开仓
    bool is_unrealised_profit_borrowable = 9;

    //计算基差时吃满对向的深度
    int64 ob_depth_e8 = 10;

    //最高买价
    int64 max_price_x = 11;

    //最低卖价
    int64 min_price_x = 12;

    //预期结算价格
    int64 expect_settle_price_x = 13;

    //预期结算价格对应的时间
    int64 expect_settle_price_time_e9 = 14;

    //开始交易时间
    int64 start_trading_time_e9 = 15;

    //交割时间
    int64 settle_time_e9 = 16;

    //关联的symbol,依赖于此计算开盘10分钟内的限价
    repeated enums.esymbol.Symbol related_symbol = 17;

    // 交割手续费率
    int64 settle_fee_rate_e8 = 18;

    //距离到期时间 精度：秒级
    int64 time_to_settle = 19;

    //当季 次季还是次次季
    enums.equartertype.QuarterType quarter_type = 20;

    // price_x的放大倍数
    int32 price_scale = 21;

    // 兼容期权
    com.bybit.option.cross.model.pb.QuotePrice price = 101 ;
}