syntax = "proto3";

package models.liqadldtov2;
option go_package = "code.bydev.io/trading/idl/pb/models/liqadldtov2";


import "enums/esymbol/symbol.proto";
import "enums/eside/side.proto";
import "enums/eliqmethod/liq_method.proto";
import "enums/eliqresult/liq_result.proto";

message LiqRecordDTO {
    // next: 35

    //数据库自增主键
    int64 id = 1;

    //仓位id
    int64 position_id = 2;

    //用户ID
    int64 user_id = 3;

    // 正向合约symbol
    enums.esymbol.Symbol symbol = 4;

    //仓位大小
    int64 size_x = 5;

    //仓位方向
    enums.eside.Side side = 6;

    //开仓价格
    int64 entry_price_x = 7;

    //强平价格
    int64 liq_price_x = 8;

    //破产价格
    int64 bust_price_x = 9;

    //最低仓位保证金
    int64 min_position_cost_e8 = 10;

    //仓位杠杆
    int64 leverage_e2 = 11;

    //触发时的强平价
    int64 trigger_fair_price_x = 12;

    //强平触发时间
    int64 trigger_time_e9 = 13;

    // 仓位模式，全仓或者逐仓
    bool is_isolated = 14;

    // 0:关 1:开
    bool is_auto_add_margin = 15;

    //追加的保证金
    int64 added_margin_e8 = 16;

    //强平结果
    enums.eliqresult.LiqResult liq_result = 17;

    //position_version
    int64 position_version = 18;

    //强平方法
    enums.eliqmethod.LiqMethod liq_method = 19;

    //取消的活动委托数量
    int32 canceled_order_num = 20;

    //原风险等级
    int64 old_risk_id = 21;

    //调整之后的风险等级
    int64 new_risk_id = 22;

    //部分平仓的平仓数量
    int64 sub_qty_x = 23;

    int64 created_at_e9 = 24;
    int64 updated_at_e9 = 25;

    //原风险等级对应的风险限额
    int64 old_risk_limit_e8 = 26;

    //新的风险等级对应的风险限额
    int64 new_risk_limit_e8 = 27;

    //阶梯强平时的订单执行价格
    int64 exec_price_x = 28;

    //仓位保证金
    int64 position_balance_e8 = 29;

    //原风险等级对应的维持保证金 便于发送邮件
    int64 old_maintenance_e4 = 30;

    //新的风险等级对应的维持保证金
    int64 new_maintenance_e4 = 31;

    //触发强平时的指数价格 api那边需要用
    int64 index_price_x = 32;

    //触发强平时的可用余额
    int64 available_balance_e8 = 33;

    // price_x的放大倍数
    int32 price_scale = 34;
}