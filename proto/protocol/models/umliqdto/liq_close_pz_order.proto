syntax="proto3";

package models.umliqdto;
option go_package = "code.bydev.io/trading/idl/pb/models/umliqdto";

import "enums/eside/side.proto";

// 保证金PM下阶梯强平，创建平仓单
message LiqCreateOrderReq {
  string order_id = 1; // 订单id
  int32 symbol_id = 2;
  int64 position_idx = 3; // 0:单持仓, 1:双向的多仓, 2:双向的空仓
  enums.eside.Side side = 4;
  int64 price_x = 5; // 平仓价格
  int64 qty_x = 6; // 平仓数量
  int32 price_scale = 7; // 价格精度
  int64 cross_seq = 8; // 用来做幂等

  // NEXT 9
}