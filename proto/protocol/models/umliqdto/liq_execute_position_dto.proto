syntax="proto3";

package models.umliqdto;
option go_package = "code.bydev.io/trading/idl/pb/models/umliqdto";

import "enums/eside/side.proto";

message LiqPosition {
  int64 position_idx = 1;     // 0:单持仓, 1:双向的多仓, 2:双向的空仓
  int64 risk_id = 2;          // 风险等级
  enums.eside.Side side = 3;
  int64 size_x = 4;           // 仓位大小 注意精度对齐
  int64 cross_seq = 5;        // 撮合消息序号
  int64 version = 6;          // 仓位版本号
  int64 split_balance_e8 = 7; // PM模式下，强平时仓位上分到的钱

  // NEXT: 8
}

message LiqExecutePositionDTO {
  int32 symbolId = 1;
  repeated LiqPosition positions = 2;

  //NEXT 3
}
