syntax = "proto3";

package models.accountdto;

import "enums/eaccountstatus/account_status.proto";
import "enums/einststatus/inst_status.proto";

option go_package = "code.bydev.io/trading/idl/pb/models/assetdto";

message AccountInfoDTO {
  enums.eaccountstatus.AccountStatus account_status = 1; // enums.eaccountstatus.AccountStatus
  int64 account_version = 2; //账户版本
  int64 last_trigger_time = 3; // 延迟强平上次触发时间
  int64 op_seq = 4;
  bool im_cancel_flag = 5;
  int32 liq_step = 6;       // 强平执行过程 enums.eliqstep.liq_step
  enums.einststatus.InstStatus inst_status = 7; // 机构借贷执行状态
  bool need_load = 8;                           // true:移入; false: 移出
  int64 load_status_update_time = 9;            // 标记load状态更新时间
  int64 upgrading_with_position_start_time = 10; // 标记持仓升级开始时间
  int64 inverse_upgrading_with_position_start_time = 11; // 标记反向升级开始时间
  int64 root_uid = 12;                                 // lending系统使用，hdts头传递下去
  int64 parent_uid = 13;                               // lending系统使用，hdts头传递下去
  int64 member_relation_type = 14;                     // lending系统使用，hdts头传递下去
  //NEXT: 14
}