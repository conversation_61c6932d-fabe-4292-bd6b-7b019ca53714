syntax = "proto3";

package models.dump;
option go_package = "code.bydev.io/trading/idl/pb/models/dump";

message EjectUserPolicy {
  oneof policy {
    EjectUserList eject_user_list = 1;
    EjectBySuffix eject_by_suffix = 2;
  }
}

message EjectUserList {
  // 需要迁出产生多个不同的node组织为map
  map<string/*node_name*/, UserList> node_to_user_list = 1;
  message UserList {
    repeated int64 user_ids = 1;
  }
}

message EjectBySuffix {
  // 需要迁出产生多个不同的node组织为map
  map<string/*node_name*/, UserSuffix> node_to_suffix_list = 1;
  message UserSuffix {
    repeated int32 uid_suffix4 = 1;
  }
}
