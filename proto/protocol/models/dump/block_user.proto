syntax = "proto3";

package models.dump;
option go_package = "code.bydev.io/trading/idl/pb/models/dump";

message BlockUser{
  int64 margin_seq = 1;    //用户被block住的margin_seq
};

// margin修复工具修复后,发出的dump消息, 往前追赶next_block_margin_seq
message DumpNextBlockMarginSeq{
  int64 next_block_margin_seq = 1; // 修复工具修复后的next_block_margin_seq, 已经修复的margin_seq + 1， 所以命名next
};

// 正常服务的margin启动的时候, 检测到用户已经解除block状态, 发起unblock
// dump服务需要清理block列表, 重置next_block_margin_seq为0
message UnBlockUser{
  int64 next_block_margin_seq = 1; // 修复工具修复后的next_block_margin_seq
  repeated int64 uids = 2;  //解封的用户uid列表
}

// AIO 使用
message BlockInfo {
  int64 blocked_seq = 1; //必须单调递增,被block的Seq, fix trading 修复的过程中会更新这个字段
  int64 lastest_seq = 2; //必须单调递增, 拉黑的时候初始化为blockSeq,  normal engine 在处理的过程中,发现有用户后续的cross消息(假定seq是X),会更新这个字段为X
}

// AIO summary 保存拉黑用户信息 使用
message UserBlockInfo {
  map<int32/*cross_idx*/, BlockInfo/*block_info*/> cross_block_info = 1;
  BlockInfo margin_block_info = 2;
}

// AIO 第一次拉黑使用
message AddUserBlockInfo {
  map<int32/*cross_idx*/, BlockInfo/*block_info*/> cross_block_info = 1;
  BlockInfo margin_block_info = 2;
}

// AIO 更新seq使用
message UpdateUserBlockInfo {
  map<int32/*cross_idx*/, BlockInfo/*block_info*/> cross_block_info = 1;
  BlockInfo margin_block_info = 2;
  // 告知dump更新最新seq或者修复seq暂告一个段落
  bool is_end = 3;
  string trace_id = 5;
}

// AIO 解拉黑使用
message UnblockUserBlockInfo {
  map<int32/*cross_idx*/, BlockInfo/*block_info*/> cross_block_info = 1;
  BlockInfo margin_block_info = 2;
  string trace_id = 3;
}
