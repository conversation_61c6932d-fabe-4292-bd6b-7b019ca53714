syntax = "proto3";

package models.tradingdto;
option go_package = "code.bydev.io/trading/idl/pb/models/tradingdto";


import "enums/ecoin/coin.proto";
import "enums/eaction/action.proto";
import "enums/ecreatetype/create_type.proto";
import "enums/ecanceltype/cancel_type.proto";
import "enums/eopplatform/op_platform.proto";
import "enums/eside/side.proto";
import "enums/eordertype/order_type.proto";
import "enums/etimeinforce/time_in_force.proto";
import "enums/ecomborderstatus/order_status.proto";
import "enums/ecombcrossstatus/cross_status.proto";
import "enums/ecxlrejreason/cxl_rej_reason.proto";
import "enums/elastliquidityind/last_liquidity_ind.proto";
import "enums/eproducttype/product_type.proto";
import "enums/eexectype/exec_type.proto";
import "enums/econtracttype/contract_type.proto";

message ExecDigest {
    string exec_id = 1;  // 组合订单的exec_id
    int64 leg1_exec_price = 2; 
    string leg2_exec_price = 3;
    int64 leg1_mark_price = 4;
    string leg2_mark_price = 5;
    int64 exec_qty = 6;  // 单腿的成交数量
    bool recv_leg1_passthrough_succ = 7;
    bool recv_leg2_passthrough_succ = 8;
    bool recv_combination_succ = 9;
    enums.elastliquidityind.LastLiquidityInd last_liquidity_ind = 10; // taker or maker
    int64 transact_time_e9 = 11;  // 成交的时间
    int64 opponent_user_id = 12;
    string opponent_order_id = 13;
    int64 opponent_account_id = 14; // 成交对手account_id（现货单腿使用）
    int64 opponent_fee_rate_e8 = 15; // 成交对手费率 (现货单腿使用)
}

message CombinationTransactDTO {
    int64 id = 1; // 自增id
    int64 op_req_num = 2; // 请求被accept时关联的i+=1e5编号 (后5位数字表示进程pid)
    int64 op_time_e9 = 3; // 单个uid内每次操作i++对应的当时时间戳
    string op_from = 4; // 发起请求的来源平台 (用户请求取platform, 内部请求取模块缩写:类似req_from)
    enums.eopplatform.OpPlatform op_platform = 5; // platform: pc/h5/api/ios/android/fix
    int64 op_seq = 6; // 单个uid内每次操作i++版本号 (只有成功commit内存时,op_seq才会i++; 灾备恢复时需要从xreq队列恢复出最大op_seq)
    int64 user_id = 7;
    string user_ip = 8; // 用户ip地址
    int64 symbol = 9;
    string base_coin_name = 10;
    int64 added_op_seq = 11; // 订单create时分配一个初始op_seq, replace-price或replace-up-qty时更新为当前op_seq, 其他时候不变
    int64 position_idx = 12;
    enums.eaction.Action action = 13;
    enums.ecreatetype.CreateType create_type = 14;
    enums.ecanceltype.CancelType cancel_type = 15;
    string remark = 16;
    string order_link_id = 17; // char[45]  原命名: cl_ord_id
    string order_id = 18; // UUIDv4: char[16]
    enums.eside.Side side = 19;
    enums.eordertype.OrderType order_type = 20;
    enums.etimeinforce.TimeInForce time_in_force = 21;
    int64 price_x = 22; // 订单价格
    int64 qty_x = 23; // 订单数量
    int64 created_at_e9 = 24;
    int64 updated_at_e9 = 25;
    int64 last_price_x = 26; // 下单时的最新成交价
    int32 price_scale = 27;
    int32 qty_scale = 28; // 数量精度
    int64 added_cross_seq = 29; // 订单成功挂单进撮合ob时的cross_seq
    int64 transact_time_e9 = 30; // 订单最后一次被撮合更新时的cross_transact_time
    enums.ecomborderstatus.OrderStatus order_status = 31; // 订单最新`稳定`状态
    enums.ecombcrossstatus.CrossStatus cross_status = 32; // 订单执行`Pending`状态 (可以推算出last_liquidity_ind)
    int64 leaves_qty_x = 33; // 订单剩余数量
    int64 cum_qty_x = 34; // 撮合累计已成交数量
    string exec_id = 35; // UUIDv4: char[16] 撮合成交对id
    int64 exec_price_x = 36; // 成交价格
    int64 exec_qty_x = 37; // 成交数量
    enums.eexectype.ExecType exec_type = 38; // 成交类型
    int64 cross_seq = 39; // 订单最后一次被撮合更新时的cross_seq
    bool is_last_item = 40; // 是否为撮合成交回报最后一个item
    enums.ecxlrejreason.CxlRejReason cxl_rej_reason = 41; // 撮合拒单原因
    enums.elastliquidityind.LastLiquidityInd last_liquidity_ind = 42; // taker or maker
    int64 opponent_user_id = 43; // 成交对手user_id
    int32 nth_item = 44; // 当前撮合成交回报item是第几个
    enums.eordertype.OrderType orig_order_type = 45; // 用于记录被修改前的原 order_type
    int64 orig_price_x = 46; // 用于记录被修改前的原 订单价格
    int64 orig_qty_x = 47; // 用于记录被修改前的原 订单数量
    enums.eproducttype.ProductType leg1_product_type = 48;
    int64 leg1_symbol = 49;
    int64 leg1_mark_price = 50;
    string leg1_order_id = 51;
    enums.eproducttype.ProductType leg2_product_type = 52;
    int64 leg2_symbol = 53;
    string leg2_mark_price = 54;
    string leg2_order_id = 55;
    int32 contract_type = 56;
    int32 biz_error = 57; // svc.trading的错误码
    // 恢复现货单腿订单需要使用 开始
    int64 user_taker_fee_rate_e8 = 58;
    int64 user_maker_fee_rate_e8 = 59;
    int64 maker_bonus_fee_rate_e8 = 60;
    string kyc_country = 61; // 下单时的kyc/kyb国家
    string user_site_id = 62; // 用户下单时的site-id'
    string user_maker_zipper_id = 63;
    string user_taker_zipper_id = 64;
    int64 account_id = 65;
    // 恢复现货单腿订单需要使用 结束
    map<string, ExecDigest> exec_digest_map = 66; // exec_id => exec_digest
    string opponent_order_id = 67;  // 对手方的orderid,maker透传包关联原始订单使用
    enums.eside.Side leg1_side = 68;
    enums.eside.Side leg2_side = 69;
    int64 opponent_account_id = 70; // 成交对手account_id（现货单腿使用）
    int64 opponent_fee_rate_e8 = 71; // 成交对手费率 (现货单腿使用)
    enums.econtracttype.ContractType leg1_contract_type = 72;
    enums.econtracttype.ContractType leg2_contract_type = 73;
    int32 leverage_place_type = 74;  //现货单腿杠杆下单类型
    int32 leverage_order_type = 75; // 现货单腿杠杆订单类型
};
