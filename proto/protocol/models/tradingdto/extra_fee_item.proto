syntax = "proto3";

package models.tradingdto;
option go_package = "code.bydev.io/trading/idl/pb/models/extrafeedto";

import "enums/eextrafeetype/extra_fee_type.proto";
import "enums/eextrafeesubtype/extra_fee_sub_type.proto";

message ExtraFeeItem {
  int32 fee_coin = 1;                                      // ExtraFee coin
  enums.eextrafeetype.ExtraFeeType fee_type = 2;           // 费用类型
  enums.eextrafeesubtype.ExtraFeeSubType sub_fee_type = 3; // 费用子类型
  int64 extra_fee_rate_e8 = 4;                             // 执行费率
  string extra_fee = 5;                                    // 执行费用
}