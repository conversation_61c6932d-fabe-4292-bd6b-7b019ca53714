syntax = "proto3";

package models.tradingdto;
option go_package = "code.bydev.io/trading/idl/pb/models/tradingdto";

import "enums/eside/side.proto";
import "enums/eordertype/order_type.proto";
import "enums/etimeinforce/time_in_force.proto";
import "enums/estopordertype/stop_order_type.proto";
import "enums/ecreatetype/create_type.proto";
import "enums/ecanceltype/cancel_type.proto";
import "enums/eopplatform/op_platform.proto";
import "enums/eorderstatus/order_status.proto";
import "enums/ecrossstatus/cross_status.proto";
import "enums/ecxlrejreason/cxl_rej_reason.proto";
import "enums/eexectype/exec_type.proto";
import "enums/elastliquidityind/last_liquidity_ind.proto";
import "enums/epricedirection/price_direction.proto";
import "enums/esmptype/smp_type.proto";
import "enums/etriggerby/trigger_by.proto";
import "enums/emarketunit/market_unit.proto";
import "enums/eslippagetype/slippage_type.proto";
import "enums/eordersubmittype/order_submit_type.proto";
import "models/tradingdto/extra_fee_item.proto";

message UnifySpotTransactDTO {
    // NEXT: 112
    int64 user_id = 1; // 用户id => broker_user_id
    int64 account_id = 2; // 账户ID

    int32 base_coin = 74;
    int32 settle_coin = 75;
    int32 symbol = 3; // 合约ID
    int32 exchange_symbol = 81; // 合约ID(与撮合交互的id)
    string symbol_name = 4; // 对应现货原symbolId,适配symbol改名情况，真实业务根据symbolId处理

    string order_id = 5; // UUIDv4: char[16]
    string order_link_id = 6; // char[45]  原命名: cl_ord_id
    enums.eside.Side side = 7; //enums.eside.Side
    enums.eordertype.OrderType order_type = 8; //enums.eordertype.OrderType
    enums.estopordertype.StopOrderType stop_order_type = 9; // so|tp|sl|ts|ttp  => plan_type

    string price = 10; // 订单价格（现货市价单破产价-要不就报错  要不一定会有价格）
    string qty = 11; // 订单数量
    string amount = 12; // 下单金额
    string loan_qty = 13; // 条件单触发时借贷数量

    enums.eorderstatus.OrderStatus order_status = 14; // 订单最新`稳定`状态 enums.eorderstatus.OrderStatus
    enums.ecrossstatus.CrossStatus cross_status = 80; // 订单最新`稳定`状态 enums.eorderstatus.OrderStatus
    enums.etimeinforce.TimeInForce time_in_force = 15; //enums.etimeinforce.TimeInForce

    string trigger_price = 16; // 条件单触发价格
    int64 trigger_src_time_e9 = 17; // 条件单触发时间
    enums.epricedirection.PriceDirection expected_direction = 76; // 条件单预期涨|跌方向

    string user_ip = 18; // 用户ip地址
    enums.eopplatform.OpPlatform op_platform = 19; // platform: pc/h5/api/ios/android/fix => order_source_new
    string remark = 20;

    enums.ecreatetype.CreateType create_type = 78;
    enums.ecanceltype.CancelType cancel_type = 68;

    int32 leverage_place_type = 21;
    int32 leverage_order_type = 22; // 全仓杠杆交易类型
    string leverage_related_order_id = 23;

    string executed_order_id = 24; // 止盈止损条件单 -> 子级订单ID
    string parent_order_id = 25; // 1.止盈止损条件单 → 父级订单ID => trigger_order_id 2. future spread订单 现货单腿订单的成交透传包对应的组合订单ID

    // ----- 下面字段主要用于表示成交记录相关信息 -----
    int32 cross_id = 26;
    string exec_id = 27; // UUIDv4: char[16] 撮合成交对id => ticket_id
    int64 cross_seq = 28; // 订单最后一次被撮合更新时的cross_seq
    int64 transact_time_e9 = 29; // 订单最后一次被撮合更新时的cross_transact_time => match_time
    enums.eexectype.ExecType exec_type = 79; // 成交类型
    enums.elastliquidityind.LastLiquidityInd last_liquidity_ind = 30; // taker or maker enums.elastliquidityind.LastLiquidityInd
    int32 nth_item = 72; // 当前撮合成交回报item是第几个
    bool is_last_item = 73; // 是否为撮合成交回报最后一个item

    string exec_price = 31; // 成交价格
    string exec_qty = 32; // 成交数量
    string exec_amount = 33; // 成交价值 (根据closed_size切割出 exit_value+entry_value)

    int64 fee_rate_e8 = 34; // 执行费率
    string exec_fee = 35; // 执行手续费

    string leaves_qty = 36; // 订单剩余数量
    string leaves_amount = 37; // 订单折算剩余金额

    string cum_qty = 38; // 撮合累计已成交数量
    string cum_amount = 39; // 撮合累计已成交金额
    string cum_exec_fee = 40; // 本订单累计交易手续费(taker,maker抵消累加)
    int64 opponent_order_id = 41;
    int64 opponent_account_id = 42; // 成交对手account_id
    int64 opponent_user_id = 43; // 成交对手user_id
    int64 opponent_broker_id = 44; // 成交对手broker_id
    int64 opponent_fee_rate_e8 = 77; // 成交对手费率

    int64 created_at_e9 = 45; // => 纳秒
    int64 updated_at_e9 = 46; // => 纳秒

    string last_price = 47; // 下单时的最新成交价

    int64 user_taker_fee_rate_e8 = 48;
    int64 user_maker_fee_rate_e8 = 49;
    string user_maker_zipper_id = 50;
    string user_taker_zipper_id = 51;

    int64 broker_id = 52; //券商ID
    string pack_id = 53; // mirana批量下单批次ID,非真实订单
    string block_order_id = 54; // => sign_nonce
    int32 business_line = 55; // BL_UNKNOWN = 0;BL_SPOT = 1; // 币币交易BL_CROSS_MARGIN = 2; // 全仓杠杆BL_ETP = 3; // ETP业务 BL_STOP_LIMIT = 4; // 止盈止损
    int32 market_order_price_limit_percentage_e6 = 56; //限价上下限比例
    int32 trade_nature = 57; //0: 非测试交易币对; 1:  测试交易币对
    int64 trade_detail_id = 58; //现货对账把成交和对账匹配，透传
    int64 maker_bonus_fee_rate_e8 = 59; // 返佣fee_rate
    string agent_source = 60; // 代理商信息
    //    int32 plan_type = 61;// 触发单类型：0条件单，1止盈止损单
    int32 max_order_count = 62; // 止盈止损条件单最大限制数量

    enums.ecxlrejreason.CxlRejReason cxl_rej_reason = 63; // 撮合拒单原因

    bool is_leverage = 64;

    int32 parent_stop_order_type = 65; // so|tp|sl|ts|ttp  => plan_type

    int64 default_taker_fee_rate_e8 = 66; // 默认taker fee rate
    int64 default_maker_fee_rate_e8 = 67; // 默认maker fee rate

    enums.esmptype.SmpType smp_type = 69; // 订单防自撮合模式
    int32  smp_group = 70; // 当前订单携带的防自撮合交易组ID
    string smp_order_id = 71; // 防止自撮合策略触发导致该笔订单被取消的对手单ID

    string tp_trigger_price = 82; // 止盈触发价格
    string sl_trigger_price = 83;   // 止损触发价格
    string tp_limit_price = 84; // 限价止盈单价格
    string sl_limit_price = 85; // 限价止损单价格
    enums.eordertype.OrderType tp_order_type = 86; // 止盈订单类型
    enums.eordertype.OrderType sl_order_type = 87; // 止损订单类型
    enums.etriggerby.OCOTriggerBy oco_trigger_by = 88; // OCO触发类型1.tp 2.sl

    bool is_new_rule_stop_order = 89; // 是否是新规则下的条件单（去除子母订单逻辑）
    enums.emarketunit.MarketUnit market_unit = 90; // 市价单交易单位

    string orig_price = 91; // 改单前订单价格
    string orig_qty = 92; // 改单前订单价格
    string orig_amount = 93; // 改单前订单价格
    string orig_trigger_price = 94; // 改单前订单触发价格
    bool show_limit_active_order_tpsl = 95; // 是否需要展示限价单止盈止损参数

    // 追踪止损单相关参数
    string activation_price = 96; // 激活价格
    string trailing_percentage = 97; // 回撤比例
    string trailing_value = 98; // 回撤价差
    string base_price = 99; // 基准价格

    string kyc_country = 100; // 下单时的kyc/kyb国家
    string user_site_id = 101; // 用户下单时的site-id
    enums.eordersubmittype.OrderSubmitType order_submit_type = 102;  // 欧洲站使用 1:real order; 2:mirror order
    int32 forbid_match_rpi = 103;   // 1.禁止和RPI订单成交 0.可以和RPI订单成交
    string max_slippage = 104;  // 市价单最大滑点值
    enums.eslippagetype.SlippageType max_slippage_type = 105; // 市价单滑点类型, tick_size, percent
    string parent_exec_id = 106;  // 组合合约成交之后拆成两条腿的成交，资产需要把这三条成交记录关联起来进行查询展示

    int32 extra_fee_rule_id = 107;      // 交易Extra费，对应的规则Id
    repeated models.tradingdto.ExtraFeeItem trade_extra_fee_item = 108; // Extra费明细，仅fill改字段有值，IDN场景下：如果是币币交易(ETHUSDT), 会4笔费用，会按照ExtraFeeType分组，合并成2笔
    repeated models.tradingdto.ExtraFeeItem cum_order_extra_fee_item = 109; // 订单累计的Extra交易费用，IDN场景下：如果是币币交易(ETHUSDT), 会4笔费用，按照ExtraFeeType分组，合并成2笔
    string opponent_site_id = 110;    // 对手方 site_id
    map<int32, string> cum_exec_fee_map = 111; // fee_coin_id => cum_exec_fee
};
