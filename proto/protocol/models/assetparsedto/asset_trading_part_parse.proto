syntax = "proto3";

package models.assetparsedto;

option go_package = "code.bydev.io/trading/idl/pb/models/assetparsedto";

import "svc/req_resp_header.proto";
import "google/protobuf/any.proto";

import "models/dump/m_seq_mark_info.proto";
import "svc/unified_v2/res/x_seq_mark_info.proto";
import "models/assetparsedto/order_parse.proto";
import "models/assetparsedto/trade_translog_parse.proto";
import "svc/unified_v2/res/routeinfo_result.proto";
import "svc/unified_v2/req_group/recovery_req.proto";
import "models/dump/block_user.proto";
import "svc/unified_v2/res/asset_margin_result.proto";

// 资产部分解析proto
message AssetTradingPartParseResultDTO {
  // NEXT: 28

  // 将请求相关的header信息dump一份
  svc.ReqRespHeader header = 1;

  // 如果处理结果是失败, 如下部分会承载错误码(非0表示错误)和错误信息
  int64 ret_code = 2; // 错误码, 0表示处理成功
  string ret_msg = 3; // 错误消息
  string ext_code = 4; // 扩展错误码
  google.protobuf.Any ext_info = 5; // 扩展信息?

  // bytes result = 6;
  bytes snapshot = 7; // 统保下发灰度迁移里面保存原BatchNotify信息，用来做kafka信息发送

  // ========== 以下部分是偏结果数据 ==========
  svc.unified_v2.res.AssetMarginResult asset_margin_result = 11;
  models.dump.MSeqMarkInfo m_seq_mark_info = 12;
  repeated svc.unified_v2.res.XSeqMarkInfo x_seq_mark_infos = 23;
  //现货
  models.assetparsedto.TradingParseDTO spot_margin_result = 13;

  //恢复数据透传信号包
  svc.unified_v2.req.group.RecoveryReqGroupBody recoveryReqGroupBody = 18;

  models.dump.AddUserBlockInfo add_block_user = 24; // add user block或者update user block信息使用
  models.dump.UpdateUserBlockInfo update_block_user = 25;
  int64 user_tag = 27; // bitmap of UserTag: CopyTradeLeader|CopyTradeFollower
  models.dump.UnblockUserBlockInfo unblock_user_info = 28; // unblock user时同步信息使用
}