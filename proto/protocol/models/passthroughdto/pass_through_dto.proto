syntax = "proto3";

package models.passthroughdto;
option go_package = "code.bydev.io/trading/idl/pb/models/passthroughdto";


//import "enums/esymbol/symbol.proto";
import "models/tradingdto/position_dto.proto";
import "models/contractdto/contract_dto.proto";
import "models/mdpdto/mark_price_dto.proto";
import "models/fundingfeedto/funding_fee_dto.proto";
import "svc/trading/req/swap_position.proto";
import "svc/trading/req/block_trade.proto";
import "svc/trading/req/recover_pass.proto";
import "models/assetdto/reconciliation_dto.proto";
import "svc/trading/req/dealing_desk.proto";
import "enums/ecrossidx/cross_idx.proto";
import "option/model/passthrough_dto.proto";
import "models/passthroughdto/spot_block_trade_dto.proto";
import "models/passthroughdto/spot_taken_over_dto.proto";
import "models/passthroughdto/spot_delisting_dto.proto";
import "models/premarketpassthroughdto/pre_market_pass_through_dto.proto";
import "models/futurespreadpassthroughdto/future_spread_future_pass_through_dto.proto";
import "models/futurespreadpassthroughdto/future_spread_spot_pass_through_dto.proto";

message PassThroughDTO {
  // NEXT: 12

  // 强平托管仓位
  models.tradingdto.PositionDTO orig_position = 1;

  // 合约信息
  models.contractdto.ContractDTO contract_info = 2;

  // 交割合约的标记价格
  models.mdpdto.MarkPriceDTO mark_price = 3;

  // 资金费用
  models.fundingfeedto.FundingFeeDTO funding_fee = 4;

  // 仓位互导
  svc.trading.req.SwapPositionReq swap_position = 5;

  // 资金对账
  models.assetdto.ReconciliationDTO reconciliation = 6;

  // 大宗成交
  svc.trading.req.BlockTradeReq block_trade = 7;

  // 恢复穿透包
  svc.trading.req.RecoverPassDTO recover_pass = 8;

  // 协议同步问题,recover_pass在go版本用的id是9, 在c++用的id是8,
  // 因为两个版本都上mainnet了,为了避免解码错误,两个version号都占用了
  svc.trading.req.RecoverPassDTO recover_pass_place_holder = 9;
  // DealingDesk成交
  svc.trading.req.DealingDeskReq dealing_desk = 10;
  models.fundingfeedto.FundingFeeDTO session_settle = 11;

  // 期货盘前永续合约 盘前各个阶段
  models.premarketpassthroughdto.PreMarketPassThroughDto pre_market_pass_through_dto = 12;

  // FutureSpread 期货单腿的透传包
  models.futurespreadpassthroughdto.FutureSpreadFuturePassThroughDTO future_spread_future_pass_through_dto = 13;

  // 期权的PassThroughPBDTO
  com.bybit.option.cross.model.pb.PassThroughPBDTO  option_passthrough_dto = 101;

  // 现货block-trade
  models.passthroughdto.SpotBlockTradeDTO spot_block_trade_dto = 102;
  // 现货接管
  models.passthroughdto.SpotTakenOverDTO spot_taken_over_dto = 103;
  // 现货下币
  models.passthroughdto.SpotDelistingDTO spot_delisting_dto = 104;
  // 现货future spread现货单腿成交透传包
  models.futurespreadpassthroughdto.SpotFutureSpreadTradeDTO spot_future_spread_trade_dto = 105;
}
