syntax = "proto3";

package models.premarketpassthroughdto;
option go_package = "code.bydev.io/trading/idl/pb/models/premarketpassthroughdto";

message PreMarketOpenListingS0 {
  int32 symbol_id = 1;
  int32 price_scale = 2;  // 初始开盘价精度
  int64 price_x = 3;  // 初始开盘价
}

message PreMarketCallAuctionS11 {
  int32 symbol_id = 1;
  int32 price_scale = 2;  // 初始开盘价精度
  int64 price_x = 3;  // 初始开盘价
  int32 price_freq = 4;  // 计算开盘价周期时间，单位秒。若不带，默认60s
}

message PreMarketCallAuctionS12 {
  int32 symbol_id = 1;
  int32 price_scale = 2;  // 初始开盘价精度
  int64 price_x = 3;  // 初始开盘价
  int32 price_freq = 4;  // 计算开盘价周期时间，单位秒。若不带，默认5s
}

message PreMarketCallAuctionNotCancelS2 {
  int32 symbol_id = 1;
  int32 price_scale = 2;  // 初始开盘价精度
  int64 price_x = 3;  // 初始开盘价
  int32 price_freq = 4;  // 计算开盘价周期时间，单位秒。若不带，默认5s
}

message PreMarketCallAuctionCrossMatchS3 {
  int32 symbol_id = 1;
  int32 price_scale = 2;  // 初始开盘价精度
  int64 price_x = 3;  // 初始开盘价
  int64 open_condition = 4; // 开盘条件/订单数量
}

message PreMarketContinuousTradingS4 {
  int32 symbol_id = 1;
}

message PreMarketCallAuctionExecCrossMatch {
  int32 symbol_id = 1;
}

message PreMarketOpenStatus {
  int32 open_status = 1; // 1 开盘失败, 2 开盘成功, 0 忽略信号,不用管
}

message PreMarketEstimateOpenPriceDto {
  int32 symbol_id = 1;
  int32 price_scale = 2;  // 预估开盘价精度
  int64 price_x = 3;  // 预估开盘价
  int64 timestamp = 4;  // 开盘价时间戳
  int64 qty_x = 5;  // 累积可成交qty量。正向已放大10的8次方倍，反向不放大
  bool  last_estimate_price_flag = 6; // 最终开盘价标识,true为最终开盘价
}

message PreMarketPassThroughDto{
  oneof pass_through_body{
    //盘前激活撮合不进入撮合状态，第0阶段
    PreMarketOpenListingS0  pre_market_open_listing_s0 = 1;
    //盘前开放竞价交易状态，第1.1阶段
    PreMarketCallAuctionS11 pre_market_call_auction_s11 = 2;
    //盘前开放竞价交易状态，第1.2阶段
    PreMarketCallAuctionS12 pre_market_call_auction_s12 = 3;
    //盘前竞价交易禁止撤单状态，第2阶段
    PreMarketCallAuctionNotCancelS2 pre_market_call_auction_not_cancel_s2 = 4;
    //盘前交易进入撮合匹配状态，第3阶段
    PreMarketCallAuctionCrossMatchS3  pre_market_call_auction_cross_match_s3 = 5;
    //盘前交易多次执行撮合成交
    PreMarketCallAuctionExecCrossMatch premarket_call_auction_mut_exec_cross_match = 6;
    //盘前交易进入到连续竞价交易状态，第4阶段
    PreMarketContinuousTradingS4 pre_market_continuous_trading_s4 = 7;
    //盘前交易最新预估开盘价&量
    PreMarketEstimateOpenPriceDto pre_market_estimate_open_price_dto = 8;
    //开盘状态相关状态
    PreMarketOpenStatus pre_market_open_status = 9;
  }
}