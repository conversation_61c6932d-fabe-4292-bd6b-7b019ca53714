syntax = "proto3";

package models.symbolconfigdto;
option go_package = "code.bydev.io/trading/idl/pb/models/symbolconfigdto";


import "enums/ecrossidx/cross_idx.proto";
import "enums/ecoin/coin.proto";
import "enums/esymbol/symbol.proto";
import "enums/epositionmode/position_mode.proto";
import "enums/econtracttype/contract_type.proto";
import "enums/econtractstatus/contract_status.proto";
import "enums/ebrokersupporttype/broker_support_type.proto";

message SymbolConfigDTO {
    // 合约
    int32 symbol = 1;

    // 名称
    string symbol_name = 2;

    // 基础货币 交易标的
    string base_currency = 54;

    // 计价货币
    string quote_currency = 55;

    // 币种
    int32 coin = 3;

    // 币种名称
    string coin_name = 51;

    // 别名 对外展示
    string symbol_alias = 4;

    // 简述 内部打印使用
    string symbol_desc = 5;

    // 对应的基础合约（交割合约对应永续合约，永续合约对应本身）
    int32 quote_symbol = 6;

    // 类型 交割 永续
    enums.econtracttype.ContractType contract_type = 7;

    // 导入时间
    int64 import_time_e9 = 8;

    // 开始交易时间
    int64 start_trading_time_e9 = 9;

    // 交割时间
    int64 settle_time_e9 = 10;
    // 计算预计交割价格时间
    int64 start_calc_settle_price_time_e9 = 11;

    // 状态
    enums.econtractstatus.ContractStatus contract_status = 12;

    // 对应的撮合序号
    int32 cross_idx = 13;

    // 对应的撮合名称
    string cross_name = 52;

    // 是否支持浮盈开仓
    bool is_unrealised_profit_borrowable = 14;

    // 支持的仓位模式
    enums.epositionmode.PositionMode mode = 15;

    // ----- 缩放相关配置 -----
    // 价格缩放倍数
    int32 price_scale = 16;
    // 当此字段>0时，client端的sdk需要将所有价格相关的缩放至new_price_scale
    int32 new_price_scale = 78;
    // 价值缩放倍数
    int32 value_scale = 17;
    // price相关一般都是放大10^4倍
    int64 one_e4 = 18;
    // value,rate相关一般都是放大10^8倍
    int64 one_e8 = 19;
    // qty,size的放大倍数 (根据symbol自定义)
    int64 one_x = 20;

    // 价格小数位数（对外展示）
    int32 price_fraction = 21;
    // 价格最小增量
    int64 tick_size_x = 22;
    // 价格最小增量小数位数（对外展示）
    int32 tick_size_fraction = 58;
    // 最小价格=1 * tickSizeX
    int64 min_price_x = 23;
    // 最大价格
    int64 max_price_x = 24;

    // 数量小数位数
    int32 lot_fraction = 25;
    // 数量最小增量
    int64 lot_size_x = 26;
    // 最小数量=1 * lotSizeX
    int64 min_qty_x = 27;
    // 最大新下单数量
    int64 max_new_order_qty_x = 28;
    // 最大持仓数量
    int64 max_position_size_x = 29;
    // 最大ob允许单笔订单数量
    int64 max_order_book_qty_x = 30;

    // 钱包资产余额展示小数位数
    int32 wallet_balance_fraction = 53;

    // 最小价值=1 Satoshi
    int64 min_value_x = 31;
    // 最大价值=<int64_t max>
    int64 max_value_x = 32;

    // 默认交割费率
    int64 default_settle_fee_rate_e8 = 33;

    // 默认taker费率
    int64 default_taker_fee_rate_e8 = 34;   // 该字段交易不在使用，替换成由费率中心维护的配置，从nacos获取
    // 最大taker费率
    int64 max_taker_fee_rate_e8 = 35;
    // 最小taker费率
    int64 min_taker_fee_rate_e8 = 36;

    // 默认maker费率
    int64 default_maker_fee_rate_e8 = 37;   // 该字段交易不在使用，替换成由费率中心维护的配置，从nacos获取
    // 最大maker费率
    int64 max_maker_fee_rate_e8 = 38;
    // 最小maker费率
    int64 min_maker_fee_rate_e8 = 39;
    // 默认限仓额度
    int64 open_interest_limit_x = 40;
    // 标记价格是否走撮合透传
    bool mark_price_passthrough_cross = 41;
    // 是否有资金费用
    bool has_funding_fee = 42;

    // 风险限额
    repeated RiskLimitDTO risk_limits = 43;
    message RiskLimitDTO {
        int32 symbol = 1;
        int64 risk_id = 2;
        bool is_lowest_risk = 3;
        int64 max_ord_pz_value_x = 4;
        int64 max_leverage_e2 = 5;
        int64 maintenance_margin_rate_e4 = 6;
        int64 initial_margin_rate_e4 = 7;
        string symbol_str = 8;
        repeated string section = 9;
        int64 old_maintenance_margin_rate_e4 = 10;
        int64 maintenance_margin_deduction_e8 = 11;
        int64 old_maintenance_margin_deduction_e8 = 12;
    }

    int64 risk_limit_count = 69;
    int64 start_risk_id = 70;
    int64 base_max_ord_pz_value_x = 59;
    int64 step_max_ord_pz_value_x = 60;
    int64 base_maintenance_margin_rate_e4 = 61;
    int64 step_maintenance_margin_rate_e4 = 62;
    int64 base_initial_margin_rate_e4 = 63;
    int64 step_initial_margin_rate_e4 = 64;

    // 风险限额增加多套配置，key的取值参考RiskLimitConfigType
    map<int32, MultiRiskLimit> multi_risk_limits = 80;
    message MultiRiskLimit {
        repeated RiskLimitDTO risk_limits = 1;
    }
    map<int32, RiskLimitConfigDTO> multi_risk_limits_config = 79;
    message RiskLimitConfigDTO {
        int64 risk_limit_count = 69;
        int64 start_risk_id = 70;
        int64 base_max_ord_pz_value_x = 59;
        int64 step_max_ord_pz_value_x = 60;
        int64 base_maintenance_margin_rate_e4 = 61;
        int64 step_maintenance_margin_rate_e4 = 62;
        int64 base_initial_margin_rate_e4 = 63;
        int64 step_initial_margin_rate_e4 = 64;
    }

    // adl相关
    int64 buy_adl_user_id = 44;
    int64 sell_adl_user_id = 45;
    int64 daily_adl_qty_x = 46;

    // 交易所成分
    repeated ExchangeDetailDTO exchange_detail = 47;
    message ExchangeDetailDTO {
        string exchange = 1;
        int64 weight_e4 = 2;
        string req_channel = 3;
        string rsp_channel = 4;
        string original_pair_to = 5;
    }

    // OB深度基准 交割合约标记价格在深度不足该值1%时，自动用市价补齐至该值1%的深度
    int64 order_book_depth_value_x = 48;

    // 计算溢价指数时所需的探底量
    int64 impact_margin_notional_x = 49;

    //funding_rate范围限制
    int64 funding_rate_clamp_e8 =71;

    //资金费率间隔
    int64 funding_rate_interval_min =72;

    // 当前资金费率收取模式:false延期收 true当期收
    bool settle_funding_immediately = 76;

    //合约下架交割价格计算起始位置
    int64 index_price_offset=73;

    //合约下架交割价格总和
    int64 index_price_sum=74;

    //合约下架交割价格数量
    int64 index_price_count=75;

    // 首页展示排序
    double index_sort = 56;

    // 生效配置
    string enable_config = 57;

    int64 version = 50;

    // ob深度合并倍数
    string ob_depth_merge_times = 65;

    // 未结盈亏小数点展示位数
    int32 upnl_fraction = 66;

    // qtyX,sizeX 复原到不带X 应除的小数位
    int32 qty_scale = 67;

    // 限价百分比 x
    int64 price_limit_pnt_e6 = 68;

    // PostOnly对应的max_new_order_qty_x放大因子
    int32 post_only_factor = 77;

    // symbol展示站点列表
    string exhibit_site_list = 82;

    // OI 上限比例值
    int64 open_interest_limit_range_e2 = 83;

  // "1000FLOKIUSDT": "1000", 1000 -> 3 同price_scale逻辑相同
    int32 index_price_scale = 84;

    // 用来把现货和本地站symbol映射起来
    string base_spot_coin = 85;

    // 是否立刻生效新的资金费率周期
    bool effect_funding_interval_immediately = 86;
    reserved 87;   // 废弃，不在使用
    int64 risk_limit_created_at = 88;   // risk limit 创建的时间
    int64 risk_limit_version = 89;   // risk limit版本号
    int64 gray_risk_limit_end_at = 90;   // 灰度结束的时间
    bool is_renew_symbol = 91;   // 重新上架灰度: true; 其他情况: false
    int64 max_new_market_order_qty_x = 92;     // 最大市价单新下单数量
    int64 tpsl_protect_ratio_e4 = 93;   // 价差保护阈值
    // 计算【价格冲击本金】（溢价指数探底量）的金额
    int64 impact_margin_notional_amount_e8 = 94;
    // 绑定保险池id
    int32 riskpool_id = 95;
    // 最小名义价值
    int64 min_notional_value_x = 96;

    // 标记价格计算模式：经典-0/兜底逻辑-1，默认经典模式
    int32 mark_price_mode = 97;

    // 是否盘前交易：正式交易-false/盘前交易-true，默认正式交易
    bool is_pre_market = 98;
    // 盘前交易配置
    PreMarketDTO pre_market_dto = 99;
    message PreMarketDTO {
        // 做市商预估开盘价格
        int64 mm_estimated_open_price_x = 1;
        // 第1.1阶段集合竞价(可撤单，1min计算一次)开始时间
        int64 s1_call_auction_start_time_e9 = 2;
        // 第1.2阶段集合竞价(可撤单，5s计算一次)开始时间
        int64 s1_2_call_auction_start_time_e9 = 3;
        // 第2阶段集合竞价(不可撤单)开始时间
        int64 s2_call_auction_not_cancel_start_time_e9 = 4;
        // 第3阶交易暂停,撮合开始消化时间
        int64 s3_call_auction_cross_match_time_e9 = 5;
        // 第4阶连续竞价开盘时间
        int64 s4_continuous_trading_start_time_e9 = 6;
    }
    int32 fee_rate_tag_id = 100;     // 该字段交易不在使用，替换成由费率中心维护的配置，从nacos获取
    // 限价百分比 y，新增
    int64 price_limit_pnt_y_e6 = 101;
    // 合约是否只支持UTA链路
    bool uta_support_only = 102;
    // 合约是否不配置shardingConfig
    bool ignore_shard_config = 103;
    // 合约展示用别名
    string display_name = 104;
    // 合约broker支持类型
    enums.ebrokersupporttype.BrokerSupportType broker_support_type = 105;
    // next: 106
}

