syntax = "proto3";

package models.symbolconfigdto;
option go_package = "code.bydev.io/trading/idl/pb/models/symbolconfigdto";


import "models/symbolconfigdto/symbol_config_dto.proto";
import "enums/ecoin/coin.proto";

message SymbolConfigResultDTO {
    map<int32, SymbolConfigDTO> prev_stable_symbol_config = 1;
    map<int32, SymbolConfigDTO> cur_symbol_config = 2;

    repeated string enable_service = 3;

    int64 version = 4;

    // kafka 队列offset
    int64 offset = 5;

    message WaitingSignalCrossInfo {
        int32 cross_idx = 1;
        string cross_name = 2;
        enums.ecoin.Coin coin = 3;
    }
    repeated WaitingSignalCrossInfo WaitingCrossLists = 6;
}

