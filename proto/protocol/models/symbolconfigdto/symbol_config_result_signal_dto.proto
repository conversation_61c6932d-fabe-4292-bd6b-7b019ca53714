syntax = "proto3";

package models.symbolconfigdto;
option go_package = "code.bydev.io/trading/idl/pb/models/symbolconfigdto";


import "models/symbolconfigdto/symbol_config_dto.proto";

message SymbolConfigResultSignalDto {
    // next: 7

    // prev_stable_symbol_config cur_symbol_config 里面的 SymbolConfigDTO
    // 除了 version 外其他信息都为零值
    map<int32, SymbolConfigDTO> prev_stable_symbol_config = 1;
    map<int32, SymbolConfigDTO> cur_symbol_config = 2;

    repeated string enable_service = 3;

    int64 version = 4;

    // kafka 队列 offset
    int64 offset = 5;

    // symbol_config_result 消息发送成功的 kafka offset
    int64 result_offset = 6;
}

