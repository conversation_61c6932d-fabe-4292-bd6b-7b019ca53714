syntax = "proto3";

package models.openinterestdto;
option go_package = "code.bydev.io/trading/idl/pb/models/openinterestdto";

import "enums/esymbol/symbol.proto";
import "enums/ecoin/coin.proto";

message PerGroupExceedConfig {
  string group_name = 1; // "_default_" 表示默认规则
  // 关联uid列表
  repeated int64 user_id_list = 2;
  // 关联uid的所有buy方向size累计值
  int64 total_buy_size_x = 3;
  // 关联uid的所有sell方向size累计值
  int64 total_sell_size_x = 4;
  // 起步限仓额度
  int64 pz_size_limit_start_x = 5;
  // 限仓比例
  int64 pz_size_limit_rate_e8 = 6;
}

message OpenInterestResultDTO {
  // ----- 第一段就是单纯的open_interest统计容灾信息 -----
  //
  // 当前合约的symbol
  enums.esymbol.Symbol symbol = 1;
  // 当前symbol的最后一条已处理的aopz_pre的offset
  // map<string, int64> aopz_pre_offsets = 2; // "$topic_name" => $offset
  // 当前symbol的最后一条已处理的aopz_result的offset
  // int64 aopz_result_offset = 3;

  // 保存所有限仓组
  map<string, models.openinterestdto.GroupStatisticalDTO> all_groups = 4; // groupName => GroupStatisticalDTO

  // buy方向所有仓位size的累加值
  int64 buy_open_interest_x = 5;
  // 散户超过起步额度, 缓存一份统计值 只有这些uid会可能被限制
  map<int64, int64> buy_candidate_map = 10; // $user_id => $buy_size_x
  // sell方向所有仓位size的累加值
  int64 sell_open_interest_x = 7;
  // 散户超过起步额度, 缓存一份统计值 只有这些uid会可能被限制
  map<int64, int64> sell_candidate_map = 11; // $user_id => $sell_size_x

  int64 buy_pz_num = 21; // buy 方向仓位的个数(size >0 )
  int64 sell_pz_num = 22; // sell 方向仓位的个数(size >0 )

  // ----- 第二段就是比较静态的配置, 一般是手工调整 -----
  //
  // 配置的自增版本号, 每次变更配置需要对应变更版本号, 方便下游做重新解析
  int64 exceed_config_ver = 8;
  // 读取到内存后, 转换成 $uid => $PerGroupLimitConfig 方便处理
  map<string, models.openinterestdto.PerGroupExceedConfig> exceed_config_map = 9; // "$group_name" => $PerGroupLimitConfig

  // ----- 第三段就是持仓限制的排序结果 -----
  //
  // 结果版本号 (每次输出exceeded_result_map实际变更时, i++)
  int64 exceeded_result_ver = 12;
  // 最终结果展开成一维, 方便上游判断
  map<int64, int64> buy_exceeded_result_map = 13; // $user_id => $v
  // $v定义: +n:自己uid超限, 0:group整体超限, -n:group被限制,但自己还剩xxx平仓额度
  map<int64, int64> sell_exceeded_result_map = 14; // $user_id => $v

  int64 non_normal_total_buy_size_x = 15; // 非普通用户 buy size total
  int64 non_normal_total_sell_size_x = 16; // 非普通用户 sell size total
  int64 extra_to_add_oi_x = 17; //非普通用户 额外增加的oi

  models.openinterestdto.PerGroupExceedConfig default_cfg = 18; // 默认配置
  map<int64, string> white_list_user_ids = 19; // 不做限制的uid   uid -> cfgGroupName
  map<int64, string> user_id2_group_name = 20; // 将config解析成方便处理的map  uid -> cfgGroupName
}

message OpenInterestDump {
  enums.ecoin.Coin  coin = 1;

  // shardName -> newestOffset 已经处理的trading_result 的 offset
  map<string, int64> all_shard_offset = 2;

  // shardName -> lastOffset 已经写入dump的trading_result 的 offset
  map<string, int64> all_shard_dump_offset = 3;

  // symbolIdx -> OpenInterestResultDTO 全部shard的symbol的限仓数据
  map<int32, models.openinterestdto.OpenInterestResultDTO> all_shard_open_interest_dto_dump = 4;
}

// 更新open_interest中的guidlist --新版本已经废弃
// 非删除情况下，更新整个group_name下的所有uid名单
message UpdateOpenInterestGroupUidList {
  // 当前合约的symbol
  enums.esymbol.Symbol symbol = 1;
  // 是否删除
  int64 is_delete = 2;
  // "_default_" 表示默认规则 _default_不能被修改
  string group_name = 3;
  // 关联uid列表
  repeated int64 user_id_list = 4;
}

// 一个限仓组
// 限仓以组为级别
// 内容包括所有的用户的简易仓位以及一些统计值
message GroupStatisticalDTO {
  int64 sell_total_pz_size_x = 1; // sell方向所有用户仓位的累加
  int64 buy_total_pz_size_x = 2; // buy方向所有用户仓位的累加
  int64 sell_total_size_x = 3; // sell方向所有用户挂单仓位的累加的和
  int64 buy_total_size_x = 4; // buy方向所有用户挂单仓位的累加的和
  int64 buy_pz_num = 11; // buy 方向仓位的个数(size >0 )
  int64 sell_pz_num = 12; // sell 方向仓位的个数(size >0 )

  int64 limit_rate_e2 = 8; // 限仓比例，默认是100，如果不设置则默认为（0 = 100）
  int64 weighted_buy_total_size_x = 9; // buy方向所有用户挂单仓位的累加的和的加权值 weighted_buy_total_size_x = buy_total_size_x * 1e2 / limit_rate_e2
  int64 weighted_sell_total_size_x = 10; // sell方向所有用户挂单仓位的累加的和的加权值 weighted_sell_total_size_x = sell_total_size_x * 1e2 / limit_rate_e2

  map<int64, models.openinterestdto.UserStatisticalDTO > all_users = 5; // uid -> user all positions
  enums.esymbol.Symbol symbol = 6;
  string group_name = 7;
  int64 symbol_limit_x = 13; // 机构设置oi至少可开值
  string limit_rate_type = 14; // rate 生效类型
  bool is_default_group = 15; // 是否是默认组
  bool buy_limit = 16; // 组买方向被限制
  bool sell_limit = 17; // 组买方向被限制
  int64 buy_limit_check_time_ver = 18; // 组被限制时的版本
  int64 sell_limit_check_time_ver = 19; // 组被限制时的版本
  bool not_limit_order = 20; //订单是否占用OI 
}

// 用户所有的简单的仓位以及一些统计值
message UserStatisticalDTO {
  int64 sell_total_pz_size_x = 1; // sell方向所有仓位的累加
  int64 buy_total_pz_size_x = 2; // buy方向所有仓位的累加
  int64 sell_total_size_x = 3; // sell方向所有挂单仓位的累加和
  int64 buy_total_size_x = 4; // buy方向所有挂单仓位的累加和
  int64 buy_pz_num = 7; // buy 方向仓位的个数(size >0 )
  int64 sell_pz_num = 8; // sell 方向仓位的个数(size >0 )
  repeated models.openinterestdto.SimpleStatisticalPositionDTO all_positions = 5; // positions_idx -> 仓位信息（两个方向的挂单与仓位大小）
  int64 uid = 6;
}

// 简单的仓位
// 对于双仓来说，可以通过position idx来区分 对应挂单开平仓属性，
// 所以统计的:单边仓位 + 单边仓位的开仓单的和
// 对于单仓，无法通过 side 或 position idx 区分开平仓属性，本身trading在仓位上的统计值也是在单仓时没有区分开平订单
// 所以统计的是:单边仓位 + 单边仓位的开仓单的和  或 单边反方向订单之和 = 反方向开仓单之和 + 当前仓位的平仓单之和
message SimpleStatisticalPositionDTO {
  int64 buy_pz_size_x = 1; // buy方向的仓位大小
  int64 sell_pz_size_x = 2; // sell方向的仓位大小
  int64 sell_leaves_qty_x = 3; // buy方向的挂单
  int64 buy_leaves_qty_x = 4; // sell方向的挂单
  int64 buy_stop_order_total_qty_x = 5; // buy方向条件单挂单总和
  int64 sell_stop_order_total_qty_x = 6; // sell方向条件单挂单总和
  map<string, int64> buy_stop_order_list = 7; // buy方向条件单挂单  order_id -> qtyX
  map<string, int64> sell_stop_order_list = 8; // sell方向条件单挂单  order_id -> qtyX
  int64 position_idx = 9;
}

// 用于open_interest_cfg dump 与自身容灾 和 open_interest 服务容灾
message OpenInterestGroupDump {
  map<int64, string> uid_to_group_map = 1; // uid -> group name
  // 每个组支持配置限仓的比例，例如当前1000w的限仓额度，配置rate_e2=120，则该组的限仓额度为1200w
  map<string/*group name*/,int64/*rate_e2*/> group_to_rate = 3;  // group name -> rate_e2
  int64 next_open_interest_change_group_uid_list_offset = 2; //启动时需要从open_interest_change_group_uid_list_offset队列恢复的offset

  map<string,SymbolRate> symbol_to_rate = 5; // symbol_rate2 配置
  map<string,CoinRate> coin_to_rate = 6; // coin_rate2 配置
  map<string,SymbolLimit> group_to_default_limit =7; // 白名单默认限仓额度
  map<string, bool> group_order_not_limit_oi = 8; // 订单不占OI的配置
}

// ChangeGroupData 修改group关系的内容
// 更新open_interest中的group
// uid ——> group name 覆盖内存
// 也可以是全量的group uid list
message ChangeGroupData {
  bool internal = 1;
  int64 change_group_offset = 2;
  map<int64, string> uid_to_group_map = 3; // uid -> group name
  // 每个组支持配置限仓的比例，例如当前1000w的限仓额度，配置rate_e2=120，则该组的限仓额度为1200w
  map<string/*group name*/,int64/*rate_e2*/> group_to_rate = 4;  // group name -> rate_e2

  map<string,SymbolRate> symbol_to_rate = 5; // group name -> symbol -> rate_e2
  map<string,CoinRate> coin_to_rate = 6; // group name -> coin -> rate_e2
  map<string,SymbolLimit> group_to_default_limit =7; // 白名单默认限仓额度
  map<string,bool> group_order_not_limit_oi = 8; //机构挂单不占OI的配置 group_name->true 订单不限制,false订单限制
}

message SymbolRate {
  map<int32,int64> symbol_rate = 1;
}

message CoinRate {
  map<int32,int64> coin_rate = 1;
}

message SymbolLimit {
  map<int32,int64> symbol_limit = 1;
}

message OpenInterestExceededResultDTO {
  //int64 id = 1;
  enums.esymbol.Symbol symbol = 2;
  int64  exceeded_result_ver = 3;
  // uid -> 仓位大小（X:反向没有E8 正向E8值）
  map<int64, int64> buy_exceeded_result_map = 4;
  // uid -> 仓位大小（X:反向没有E8 正向E8值）
  map<int64, int64> sell_exceeded_result_map = 5;

  // uid -> 仓位大小（X:反向没有E8 正向E8值）
  map<int64,int64> extra_buy_exceeded_result_map = 7;
  map<int64,int64> extra_sell_exceeded_result_map = 8;

  int64 time_stamp_e9 = 6;
}

message OpenInterestSnapshotDTO {
  enums.ecoin.Coin coin = 1;
  map<int32,OpenInterestExceededResultDTO> all_symbol_limit_result_map = 2;
}

message OpenInterestPzDTO {
  int64 uid = 1;    // 用户ID
  int64 pz_oi_size_x = 2; // 持仓OISize
}

message OpenInterestBaseInfoDTO {
  int64 cur_open_interest_x = 1;
  int64 buy_open_interest_x = 2;
  int64 sell_open_interest_x = 3;
  int64 non_normal_total_buy_size_x = 4;
  int64 non_normal_total_sell_size_x = 5;
  int64 extra_to_add_oix = 6;
  int64 default_limit = 7;
  int64 oi_limit = 8;
  int64 sc_limit = 9;
}