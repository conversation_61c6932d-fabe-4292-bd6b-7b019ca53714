syntax = "proto3";

package com.bybit.option.common.service.facade.grpc.spot;

option java_multiple_files = true;
option java_package = "com.bybit.option.common.service.facade.grpc.spot";
option java_outer_classname = "SpotWhiteListServiceProto";
option go_package = "code.bydev.io/trading/idl/pb/option/omp";

service SpotWhiteListService{

  rpc query(SpotWhiteListRequest) returns(SpotWhiteListResponse);

}

message SpotWhiteListRequest {
  int32      page         = 1;
  int64      orgId        = 2;
}

message SpotWhiteListResponse {
  repeated WhiteListInfo checkInfo = 1;
  int32 ret_code =2;
  string ret_msg =3;
  string updated =4;
}
message WhiteListInfo {
  int64 id = 1;
  int64 org_id = 2;
  int64 user_id = 3;
  string check_id = 4;
  UserBlackWhiteListType list_type = 5;
  int64 valid_judge =6;
  int64 record_type = 7;
  string symbol_id = 8;
  string start_time = 9;
  string end_time = 10;
  string status = 11;
  string extra_info = 12;
  int64 scene_id =13;
  int64 created =14;
  int64 updated =15;
}

enum UserBlackWhiteListType {
  UNKNOWN_BLACK_WHITE_LIST_TYPE = 0;
  WITHDRAW_BLACK_WHITE_LIST_TYPE = 1; //提现 黑白名单
  SYMBOL_BAN_SELL_WHITE_LIST_TYPE = 2; //禁卖白名单
  SYMBOL_BAN_BUY_WHITE_LIST_TYPE = 3; //禁买白名单
  OPTION_BAN_SELL_WHITE_LIST_TYPE = 4; //禁卖白名单
  OPTION_BAN_BUY_WHITE_LIST_TYPE = 5; //禁买白名单
  SPECIAL_ORDER_TYPE_WHITE_LIST_TYPE = 6; // 特殊订单类型白名单
  SPOT_IN_WHITE_LIST_TYPE = 7; // 灰度用户白名单
  SPOT_IN_BLACK_LIST_TYPE = 8; // 风控用户黑名单
  SYMBOL_PAGE_VISIBLE_WHITE_LIST_TYPE = 9; //页面可见白名单
  SYMBOL_GLOBAL_WHITE_LIST_TYPE = 10; //全局白名单
  SYMBOL_ALLOW_DEAL_WHITE_LIST_TYPE = 11; //允许成交白名单
  SYMBOL_WHITE_LIST_CONF_MODEL_LIST_TYPE = 12; // 白名单配置模式：0全局；1单独
  API_TRADE_LIST_CONF_MODEL_LIST_TYPE = 13; // api交易白名单
}