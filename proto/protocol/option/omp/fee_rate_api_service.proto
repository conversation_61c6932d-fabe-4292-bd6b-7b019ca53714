syntax = "proto3";

package com.bybit.option.common.service.facade.grpc.feerate;

option java_multiple_files = true;
option java_package = "com.bybit.option.common.service.facade.grpc.feerate";
option java_outer_classname = "OmpFeeRateApiProto";
option go_package = "code.bydev.io/trading/idl/pb/option/omp";

service OmpFeeRateService{
  /**
    设置uid coin纬度费率
   */
  rpc setUidCoinFeeRate(SetCoinFeeRequest) returns(FeeResponse);

  /**
    删除uid coin纬度费率
   */
  rpc rmUidCoinFeeRate(RevokeCoinFeeRequest) returns(FeeResponse);

  /**
    设置coin纬度默认费率
   */
  rpc setCoinDefaultFeeRate(SetDefaultCoinFeeRequest) returns(FeeResponse);
}

message SetCoinFeeRequest {
  repeated UidCoinFeeRate coinFeeList = 1;  // uid coin 费率
  string                  reqId       = 2;
}

message RevokeCoinFeeRequest {
  repeated RevokeCoinFee revokeCoinFeeList = 1;  // uid coin 费率
  string                 reqId             = 2;
}

message SetDefaultCoinFeeRequest {
  string coin     = 1;
  string fee_type = 2;
  string fee_rate = 3;
}

message UidCoinFeeRate {
  string  uid            = 1;
  // 如果是 UID纬度 coin传空
  string  coin           = 2;
  // MAKER TAKER
  FeeType fee_type       = 3;
  string  fee_rate       = 4;
  // 透传财务ID
  int64   zipperId       = 5;
  // 费率创建时间
  int64   create_time_e9 = 6;
  //  配置来源
  string  op_from        = 7;
}

message RevokeCoinFee {
  string  uid            = 1;
  string  coin           = 2;
  FeeType fee_type       = 3;
  int64   zipperId       = 4;
  int64   create_time_e9 = 5;
}


message FeeResponse {
  int32  retCode = 1; // grpc 返回代码 0 成功 非0失败
  string retMsg  = 2; // message
  string reqId   = 3;
}

enum FeeType {
  MAKER = 0;
  TAKER = 1;
}

