syntax = "proto3";


option go_package = "code.bydev.io/trading/idl/pb/option/model";
package com.bybit.option.margin;
import "option/common/money.proto";

// 成交信息
message Trade {
    int64 id = 1;
    int64 userId = 2;// 用户ID
    int64 accountId = 3;// 账户ID
    string execId = 4; //交易单号
    string orderId = 5;//订单号
    int32 marketRole = 6;// 当前订单 maker，taker信息 MarketLiquidityEnum
    int32 orderType = 7;// 订单类型
    int64 orderTime = 8;// 下单时间
    int32 symbolId = 9;// 标的ID
    string symbol = 10;// 标的
    int32 side = 11;// 当前持仓方向 BUY、SELL enum
    com.bybit.option.common.Money execPrice = 12;// 订单价格
    com.bybit.option.common.PrecisionDecimal execQty = 13;// 订单成交数量（BTC）
    com.bybit.option.common.Money execFee = 14;// 手续费
    com.bybit.option.common.Money cashFlow = 15;//现金流
    com.bybit.option.common.Money change = 16;//所有的变动
    int32 contractType = 18;
    string baseCoin = 19;// 标的币种，兼容期货
    string quoteCoin = 20;// 结算货币，兼容期货
    int64 crossSeq = 21;// 撮合序号
    int32 timeInForce = 22;
    int64 transTime = 23;// 交易时间 上游透传字段
    int64 marginDealTime = 24;// 保证金处理时间 内部时间 毫秒级
    int64 offset = 25;
    int32 marketLiquidity = 26;// taker/maker enum
    string settleTerm = 27;
    int32 tradeType = 28; // TransLogTypeEnum
    string requestId = 29;// 原始请求ID
    com.bybit.option.common.Money cashBalance = 30; //余额
    string execFeeRate = 31;// 手续费率

    com.bybit.option.common.Money markPrice = 32; //

    com.bybit.option.common.Money underlyingPrice = 33; //交易时刻现货价格

    com.bybit.option.common.Money indexPrice = 34; //交易时刻指数价格

    int32 action = 35;//open/close

    com.bybit.option.common.Money execValue = 36;

    int32 execType = 37;
  
    int32 index = 38;//排序字段
}