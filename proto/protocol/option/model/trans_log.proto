syntax = "proto3";

import "option/common/money.proto";

option go_package = "code.bydev.io/trading/idl/pb/option/model";
package com.bybit.option.margin;

message TransLog {
    string requestId = 1;// 原始请求ID
    string transId = 2;// tranglog ID
    string orderId = 3;// 订单号
    int64 transTime = 4;// 交易系统透传时间戳（毫秒级）
    int64 marginDealTime = 5;// 保证金处理时间 内部时间 毫秒级 System.currentTimeMillis()
    int64 crossSeq = 6;// 撮合回报单号
    int32 contractType = 7;// 标的产品类型 永续/Prepetual：BTCUSD 期权/Option：BTC-29JUL21-30000-C
    string symbol = 8;// 交易标的
    int32 symbolId = 9;// 标的id
    int32 transType = 10;// 转入/转出/交易/结算/交割/强平
    int32 side = 11;// enum buy/sell
    int32 action = 12;// enum  open /close
    string execId = 13;
    com.bybit.option.common.Money execPrice = 14;// 成交价格
    com.bybit.option.common.PrecisionDecimal execQty = 15;// 成交数量
    com.bybit.option.common.Money execFee = 16;// 手续费
    com.bybit.option.common.PrecisionDecimal execFeeRate = 17;// 手续费率
    com.bybit.option.common.Money Funding = 18;// 资金费用针对永续
    com.bybit.option.common.Money cashFlow = 19;// 现金流
    com.bybit.option.common.Money change = 20;// 余额变更 cashFlow+fee
    com.bybit.option.common.Money cashBalance = 21;//
    com.bybit.option.common.Money equity = 22;//
    string feeRate = 23;//
    com.bybit.option.common.Money insurancePoolFee = 24;//
    com.bybit.option.common.Money sessionRpl = 25;// 平仓结算
    string settleTerm = 26;
    string info = 27;//
    com.bybit.option.common.PrecisionDecimal currSize = 28;// size
    string baseCoin = 29;
    string quoteCoin = 30;
    int32 marketRole = 31;// 当前订单 maker，taker信息  MarketLiquidityEnum
    bool takeOverAcount=32;//是否是接管户，true:是接管户；
    int32 marginMode = 33; //0-常规保证金模式 1-组合保证金模式 默认常规保证金
    int32 positionIdx = 34; //0-单向持仓
    com.bybit.option.common.Money preCashBalance = 35;//
    int32 index = 36;//排序字段
    int32 transSubType =37;
    int32 liqTransFlag=38;
}