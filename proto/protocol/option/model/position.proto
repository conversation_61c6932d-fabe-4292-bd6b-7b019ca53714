syntax = "proto3";

import "option/common/money.proto";


option go_package = "code.bydev.io/trading/idl/pb/option/model";

package com.bybit.option.margin;

// 持仓信息
message Position {
    int64 id = 1;
    int64 userId = 2;// 用户ID
    int64 accountId = 3;// 账户ID
    string positionId = 4;// 仓位ID
    int32 symbolId = 5;// 标的id
    string symbol = 6;// 标的
    int32 side = 7;// 当前持仓方向 BUY、SELL
    com.bybit.option.common.PrecisionDecimal size = 8;// 仓位数量
    com.bybit.option.common.Money value = 9;// 永续持仓价值
    int32 positionStatus = 10;// 持仓状态
    int32 positionIdx = 11;// 0 单向buy sell, 1 双向 buy, 2 双向 sell
    int32 symbolType = 16;// 标的合约类型 call put
    string settleTerm = 12;//账期
    int32 contractType = 13;// 标的产品类型
    //以下字段后期或许删除，期权业务在运营平台可获取。暂时用于兼容期货
    string baseCoin = 14;// 标的货币
    string quoteCoin = 15;// 结算货币
    int64 expireDate = 17;// 到期日
    string riskId = 18; // 仓om位风险限额等级
    int64 leverage = 19; //仓位杠杆
    com.bybit.option.common.Money positionIM = 20;// 总初始保证金
    com.bybit.option.common.Money positionMM = 21;// 总维持保证金
    com.bybit.option.common.Money positionAvgPrice = 22;   // 持仓均价
    com.bybit.option.common.Money sessionAvgPrice = 23;   // 结算周期均价
    com.bybit.option.common.Money positionPnl = 24;   // 持仓损益
    com.bybit.option.common.Money sessionUpl = 25;   // 结算周期未结损益
    com.bybit.option.common.Money sessionRpl = 26;   // 结算周期已结损益
    com.bybit.option.common.Money accumulatedRpl = 27;   // 持仓总已结损益
    com.bybit.option.common.Money closingFee = 28;   // 平仓手续费
    com.bybit.option.common.Money positionAvailableBalance = 29;   //仓位可用余额
    com.bybit.option.common.Money totalTradingFee = 30;   //总付出手续费
    int64 sentDeliveryTime = 31;// 发起交割时间
    com.bybit.option.common.Money premium = 32;   // 付出权益金
    com.bybit.option.common.Money strikePrice = 33;   // 行权价
    int64 transDateTime = 34;//业务处理时间
    int32 optionType = 35; //期权类型 看涨/看跌
    string expiryDateRepresentation = 36; // 到期时间展示维度（24SEP21）
    int64 marginDealTime = 37; //保证金处理时间
    com.bybit.option.common.PrecisionDecimal roi = 38;// 仓位ROI
    com.bybit.option.common.Money deliveryRpl = 39;// 交割实现盈亏
    bool liqMark = 40;//强平圈定标识
    com.bybit.option.common.Money markPrice = 41;//标记价格
    string iv = 42;// 仓位数量
}