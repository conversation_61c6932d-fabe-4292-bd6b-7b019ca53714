syntax = "proto3";

import "option/common/money.proto";
import "option/model/position.proto";


option go_package = "code.bydev.io/trading/idl/pb/option/model";

package com.bybit.option.margin;

// 永续和期货持仓信息
message PerpetualPosition {
    int64 id = 1;
    int64 userId = 2;// 用户ID
    int64 accountId = 3;// 账户ID
    string positionId = 4;// 仓位ID
    int32 symbolId = 5;// 标的id
    string symbol = 6;// 标的
    int32 side = 7;// 当前持仓方向 BUY、SELL
    com.bybit.option.common.PrecisionDecimal size = 8;// 仓位数量
    com.bybit.option.common.Money value = 9;// 永续持仓价值
    int32 positionStatus = 10;// 持仓状态
    int32 positionIdx = 11;// 0 单向buy sell, 1 双向 buy, 2 双向 sell
    int32 symbolType = 16;// 标的合约类型 call put
    string settleTerm = 12;//账期
    int32 contractType = 13;// 标的产品类型
    //以下字段后期或许删除，期权业务在运营平台可获取。暂时用于兼容期货
    string baseCoin = 14;// 标的货币
    string quoteCoin = 15;// 结算货币
    string riskId = 18; // 仓om位风险限额等级
    com.bybit.option.common.PrecisionDecimal leverage = 19; //杠杆
    //com.bybit.option.common.PrecisionDecimal leverage=19;//
    com.bybit.option.common.Money positionIM = 20;// 总初始保证金
    com.bybit.option.common.Money positionMM = 21;// 总维持保证金
    com.bybit.option.common.Money positionAvgPrice = 22;   // 持仓均价
    com.bybit.option.common.Money sessionAvgPrice = 23;   // 结算周期均价
    com.bybit.option.common.Money positionPnl = 24;   // 持仓损益
    com.bybit.option.common.Money sessionUpl = 25;   // 结算周期未结损益
    com.bybit.option.common.Money sessionRpl = 26;   // 结算周期已结损益
    com.bybit.option.common.Money accumulatedRpl = 27;   // 持仓总已结损益
    com.bybit.option.common.Money closingFee = 28;   // 平仓手续费
    com.bybit.option.common.Money positionAvailableBalance = 29;   //仓位可用余额
    com.bybit.option.common.Money totalTradingFee = 30;   //总付出手续费
    int64 sentDeliveryTime = 31;// 发起交割时间
    com.bybit.option.common.Money premium = 32;   // 付出权益金
    com.bybit.option.common.Money strikePrice = 33;   // 行权价
    int64 transDateTime = 34;//业务处理时间
    int32 optionType = 35; //期权类型 看涨/看跌
    string expiryDateRepresentation = 36; // 到期时间展示维度（24SEP21）
    int64 marginDealTime = 37; //保证金处理时间
    com.bybit.option.common.PrecisionDecimal roi = 38;// 仓位ROI


    int32 tpSlMode = 43; //止盈止损模式
    int64 created_at = 44;
    int64 updated_at = 45;

    com.bybit.option.common.Money curValue =46; //
    com.bybit.option.common.Money liqPrice = 47;// 强平价格(passthru)
    com.bybit.option.common.Money bustPrice = 48;// 破产价格(passthru)
    com.bybit.option.common.Money trailingStopPrice = 49; //追踪止损(passthru)
    com.bybit.option.common.Money occFundingFee = 50;// 仓位size和当前资金费率预占用资金费用(passthru)
    com.bybit.option.common.Money takeProfitPrice = 51; //止盈价格(passthru)
    com.bybit.option.common.Money stopLossPrice = 52; //止损价格(passthru)
    com.bybit.option.common.Money orderMargin = 53;//订单保证金(passthru)

    com.bybit.option.common.Money markPrice = 54;//标记价格
    com.bybit.option.common.Money occClosingFee =55;//仓位占用的平仓手续费
    int32 deleverageIndicator=56;//风险指示灯等级

    com.bybit.option.common.Money unrealisedPnl =57;//未结盈亏
    com.bybit.option.common.Money cumRealisedPnl=58;//累计已结盈亏
    com.bybit.option.common.Money cumCommission =59;//累计佣金
    com.bybit.option.common.Money entryPrice=60;//平均入场价
    // Buy方向下单成本转化系数
    int64 buyValueToCostE8 = 61;
    // Sell方向下单成本转化系数
    int64 sellValueToCostE8 = 62;

    // 持仓模式: 0~单持仓 3~双边持仓
    int32 positionMode = 63;

    // 锚定仓位的追踪止盈激活价 0:按追踪止损处理, x:按追踪止盈处理
    com.bybit.option.common.Money activationPrice = 64;//  交易要传递
    // 止盈已设置数量（订单数量)
    com.bybit.option.common.PrecisionDecimal tpOrderQty = 65;//  交易要传递
    // 止损已设置数量（订单数量)
    com.bybit.option.common.PrecisionDecimal slOrderQty = 67;//  交易要传递
    // 止盈剩余可设置仓位大小
    com.bybit.option.common.PrecisionDecimal tpFreeSize = 68;//  交易要传递
    // 止损剩余可设置仓位大小
    com.bybit.option.common.PrecisionDecimal slFreeSize = 69;//  交易要传递

    //免成本手数 - 透传
    com.bybit.option.common.PrecisionDecimal freeCostQty = 70;
    //免成本部分保证金 - 透传
    com.bybit.option.common.PrecisionDecimal freeCost = 71;
    //原始position status
    int32 originalPositionStatus = 72;
}