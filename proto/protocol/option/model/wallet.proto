syntax = "proto3";

import "option/common/money.proto";

option go_package = "code.bydev.io/trading/idl/pb/option/model";

package com.bybit.option.margin;

// 钱包信息
message Wallet {
    string quoteCoin = 1;// 结算货币
    int64 userId = 2;// 用户ID
    int64 accountId = 3;// 账户ID
    bool pmMode = 4;// 保证金类型 false RM 常规保证金，true PM 组合保证金
    com.bybit.option.common.Money cashBalance = 5;// 现金余额
    com.bybit.option.common.Money availableBalance = 6;// 可用余额
    com.bybit.option.common.Money marginBalance = 7;// 保证金余额
    com.bybit.option.common.Money equity = 8;// 资产净值-Margin Balance + Options Value
    com.bybit.option.common.Money optionSessionUpl = 9;// 期权结算周期内总未结盈亏
    com.bybit.option.common.Money optionSessionRpl = 10;// 期权结算周期内总已结盈亏
    com.bybit.option.common.Money perpSessionUpl = 11;// 永续结算周期内总未结盈亏
    com.bybit.option.common.Money perpSessionRpl = 12;// 永续结算周期内总已结盈亏
    com.bybit.option.common.Money futureSessionUpl = 13;// 交割结算周期内总未结盈亏
    com.bybit.option.common.Money futureSessionRpl = 14;// 交割结算周期内总已结盈亏
    com.bybit.option.common.Money accountSessionUpl = 15;// 结算周期内总未结盈亏
    com.bybit.option.common.Money accountSessionRpl = 16;// 结算周期内总已结盈亏
    com.bybit.option.common.Money accountRpl = 17;// 账户总已实现盈亏（累计值不清空）
    com.bybit.option.common.Money accountIM = 18;// 总初始保证金
    com.bybit.option.common.Money accountMM = 19;// 总维持保证金
    com.bybit.option.common.Money givenCash = 22;// 体验金
    com.bybit.option.common.Money serviceCash = 23;// 抵扣金
    com.bybit.option.common.Money freezeBalance = 24;// 冻结金额
    int64 createTime = 25;
    int32 riskLevel = 26;
    string liqAccountStatus = 27;
    bool liqMark = 28;
    string LiqRiskLevel = 29 ;
    com.bybit.option.common.Money optionTotalIM = 30;// 期权总计起始保证金
    com.bybit.option.common.Money optionTotalMM = 31;// 期权总计维持保证金
    com.bybit.option.common.Money perpTotalIM = 32;// 永续总计起始保证金
    com.bybit.option.common.Money perpTotalMM = 33;// 永续总计维持保证金
    com.bybit.option.common.Money futureTotalIM = 34;// 交割总计起始保证金
    com.bybit.option.common.Money futureTotalMM = 35;// 交割总计维持保证金
}