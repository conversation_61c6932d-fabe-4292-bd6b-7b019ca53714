syntax = "proto3";

package bgw.v1;

import "google/protobuf/descriptor.proto";

enum HttpMethod {
  HTTP_METHOD_UNSPECIFIED = 0;
  HTTP_METHOD_ANY = 1;
  HTTP_METHOD_GET = 2;
  HTTP_METHOD_POST = 3;
  HTTP_METHOD_PUT = 4;
  HTTP_METHOD_DELETE = 5;
}

enum Resource {
  RESOURCE_GROUP_UNSPECIFIED = 0;
  // Deprecated
  RESOURCE_GROUP_ALL = 1;
  RESOURCE_GROUP_ORDER = 2;
  RESOURCE_GROUP_POSITION = 3;
  RESOURCE_GROUP_SPOT_TRADE = 4;
  RESOURCE_GROUP_CLOUD_CONTRACT = 5;
  RESOURCE_GROUP_BLOCK_TRADE = 6;
  RESOURCE_GROUP_OPTIONS_TRADE = 7;
  RESOURCE_GROUP_DERIVATIVES_TRADE = 8;
  RESOURCE_GROUP_COPY_TRADE = 9;
  RESOURCE_GROUP_EXCHANGE_HISTORY = 10;
  RESOURCE_GROUP_NFT_QUERY_PRODUCT_LIST = 11;
  RESOURCE_GROUP_ACCOUNT_TRANSFER = 12;
  RESOURCE_GROUP_SUB_MEMBER_TRANSFER = 13;
  RESOURCE_GROUP_WITHDRAW = 14;
  RESOURCE_GROUP_SUB_MEMBER_TRANSFER_LIST = 15;
  RESOURCE_GROUP_AFFILIATE = 16;
}

enum Permission {
  PERMISSION_UNSPECIFIED = 0;
  PERMISSION_READ = 1;
  PERMISSION_WRITE = 2;
  PERMISSION_READ_WRITE = 3;
}

enum Protocol {
  PROTOCOL_UNSPECIFIED = 0;
  PROTOCOL_GRPC = 1; // grpc协议
}

enum Selector {
  SELECTOR_UNSPECIFIED = 0;
  SELECTOR_ROUND_ROBIN = 1; // 轮询
  SELECTOR_ZONE_RAFT = 2; // zone raft  ps: 当负载均衡带zone时，service级别必须填写FILTER_AUTH或者FILTER_OPENAPI过滤器获取memberID
  SELECTOR_RANDOM = 3; // 随机
  SELECTOR_ZONE_ROUND_ROBIN = 6; // zone 轮询   ps: 当负载均衡带zone时，service级别必须填写FILTER_AUTH或者FILTER_OPENAPI过滤器获取memberID
  SELECTOR_RAFT = 7; // raft
  SELECTOR_ZONE_VIP_RAFT = 8; // zone vip raft    ps: 当负载均衡带zone时，service级别必须填写FILTER_AUTH或者FILTER_OPENAPI过滤器获取memberID
  SELECTOR_SYMBOLS_ROUND_ROBIN = 9; // symbols selector 可以有多个symbol，按逗号分割
  SELECTOR_METAS_ROUND_RANDOM = 10; // 指定header或者query参数 selector 可以有多个参数，按逗号分割
  SELECTOR_CONSISTENT_HASH = 11; // 一致性hash负载均衡，和AZ亲和冲突，可按照uid，ip，uid/ip，token等维度进行一致性hash
  SELECTOR_MULTI_REGISTRY = 12; // 多服务负载均衡，for 期权
}

enum Filter {
  FILTER_UNSPECIFIED = 0;
  FILTER_QPS_LIMITER = 1; // 单机qps限流
  FILTER_AUTH = 2; // 网站token鉴权         ps：auth和openapi鉴权只能二选一
  FILTER_OPENAPI = 3; // openapi使用apikey鉴权 ps：auth和openapi鉴权只能二选一
  FILTER_GEO_IP = 4; // ip归属地
  FILTER_BIZ_LIMITER = 5; // 业务集群限流
  FILTER_CONTEXT = 6; // header透传,cookie处理等
  FILTER_RESPONSE = 7; // response body处理    ps：--version=v1代表lowerSnakeCase，v2代表lowerCamelCase
  FILTER_REQUEST = 8; // request body处理    ps：不支持传参数，并且请求类型必须是bytes，键为request
  FILTER_PLUGIN = 9; // 网关提供插件扩展标识
  FILTER_BIZ_LIMITER_V2 = 10; // 业务集群限流V2
  FILTER_SIGNATURE = 11; //安全身份认证过滤器，https://c1ey4wdv9g.larksuite.com/wiki/wikusZr7rHgxDrFfz0bNVWsFfrg
  FILTER_ANTI_REPLAY = 12; // 请求防重放
  FILTER_IP_LIMITER = 13; // ip限频和拦截
  FILTER_API_LIMITER = 14; // openapi limiter
  FILTER_COMPLIANCE_WALL = 15; //compliance wall
  FILTER_GRAY = 16; // gray filter
  FILTER_OPEN_INTEREST = 17; // oi check
  FILTER_BIZ_LIMITER_MEMO = 18; // 内存限频
  FILTER_BIZ_CRYPTION = 19; // 加签验签
  FILTER_BSP = 20; // bsp
  FILTER_BIZ_BAN = 21; // 通用封禁
  FILTER_RISK_SIGNATURE = 22; // 风控验签
}

extend google.protobuf.ServiceOptions {
  Service service_options = 60001;
}

extend google.protobuf.MethodOptions {
  Method method_options = 60002;
}

extend google.protobuf.FieldOptions {
  Field field_options = 60003;
}

extend google.protobuf.FieldOptions {
  bool forbidden_export = 60004;
}

message FilterConfig {
  Filter name = 1 [(field_options) = {
    required: true
  }];
  string args = 2 [(field_options) = {
    placeholder: true
  }];
  // plugin name
  string plugin_name = 3 [(field_options) = {
    placeholder: true
  }];
  // 不启用该filter，针对于某个过滤器，定义在service级别时，但method有少量接口不需要定义该过滤器，例如鉴权
  bool disable = 4 [(field_options) = {
    placeholder: true
  }];
  // filter扩展参数
  repeated FilterOptions options = 5 [(field_options) = {
    placeholder: true
  }];
}

message FilterOptions {
  //  限流器配置选项
  string category = 1; // futures,option,spot
  int32 rate = 2; // 额度
  int32 burst = 3; // 桶大小，默认和rate相同
  int32 step = 4; // 限流步长
  int32 period_sec = 5; // 限流周期，默认1，单位秒
  bool uid = 6; // uid维度
  bool path = 7; // path维度
  bool method = 8; // method维度
  bool symbol = 9; // symbol维度
  bool enable_custom_rate = 10; // 是否启用自定义提频
  string group = 11; // group维度名称
  string data_provider = 12; // 限频数据源，futures，etcd，默认为网关etcd
  string limit_type = 13; // 限流方式，counter,bucket,默认为令牌桶
}

// access control list;
message ACL {
  Resource group = 1 [(field_options) = {
    placeholder: true
  }]; // 组;

  Permission permission = 2 [(field_options) = {
    required: true
  }]; // 权限;

  bool all_group = 3 [(field_options) = {
    placeholder: true
  }]; // 所有组;

  repeated Resource groups = 4 [(field_options) = {
    placeholder: true
  }]; // 多个组;
}

// true表示需要执行特定的操作
message Field {
  bool inheritable = 1; // 允许缺失时继承,对method无效
  bool required = 2; // 必填字段
  bool forbidden = 3; // 禁止填写，由插件填写
  bool placeholder = 4; // 仅用于占位
}

// 路由配置
message Method {
  // grpc方法名.由插件填写。
  string name = 1 [(field_options) = {
    forbidden: true
  }];

  // http路径
  string path = 2 [(field_options) = {
    placeholder: true
  }];

  // http方法
  HttpMethod http_method = 3 [(field_options) = {
    placeholder: true
  }];

  // 过滤器
  repeated FilterConfig filters = 4 [(field_options) = {
    placeholder: true
  }];

  // access control list;
  ACL acl = 5 [(field_options) = {
    placeholder: true
  }];

  bool offline = 6 [(field_options) = {
    placeholder: true
  }];

  Selector selector = 7 [(field_options) = {
    placeholder: true
  }];

  string group = 8 [(field_options) = {
    placeholder: true
  }];

  // http路径列表
  repeated string paths = 9 [(field_options) = {
    placeholder: true
  }];

  // method超时时间(秒)
  int32 timeout = 10 [(field_options) = {
    placeholder: true
  }];

  // selector_meta用于标记服务选取的key和value，以及(路由一对多服务时)
  // groups:         {"groups":{"category":"option"}}  // 静态分组，解析body
  // composite:      {"composite":{"type":"default","group":"option-asset","order":[""]}} // 组合类型
  // dynamic_groups: {"dynamic_groups":[{"key":"_sp_category","value":"fbu"}]}  // 动态分组，只取header或者query参数
  // select_keys:    {"select_keys":["_sp_business"]} // 动态选取nacos的服务列表元数据key做负载均衡，只取header或者query参数
  // groups，dynamic_groups和composite必须三选一，不能同时存在
  string selector_meta = 11 [(field_options) = {
    placeholder: true
  }];

  // http方法, 支持自定义方法集合
  repeated HttpMethod http_methods = 12 [(field_options) = {
    placeholder: true
  }];

  // 是否允许wss调用
  bool allow_wss = 13 [(field_options) = {
    placeholder: true
  }];

  // 负载均衡元信息
  string load_balance_meta = 14 [(field_options) = {
    placeholder: true
  }];

  // 是否禁用该接口
  bool disable = 15 [(field_options) = {
    placeholder: true
  }];

  // category信息
  string category = 16 [(field_options) = {
    placeholder: true
  }];

  // group route mode,可选,strict为默认值
  string group_route_mode = 17 [(field_options) = {
    placeholder: true
  }];

  // breaker 熔断功能开关
  optional bool breaker = 18 [(field_options) = {
    placeholder: true
  }];

  // risk_sign 加签功能开关
  optional bool risk_sign = 19 [(field_options) = {
    placeholder: true
  }];
}

// 服务配置
message Service {
  // package.service,继承的service的full_name
  string reference = 1 [
    (forbidden_export) = true,
    (field_options) = {
      placeholder: true
    }
  ];

  // nacos service name,nacos服务注册使用名
  string registry = 2 [(field_options) = {
    required: true
  }];

  // nacos namespace，可继承,可选
  string namespace = 3 [(field_options) = {
    inheritable: true;
    forbidden: true;
  }];

  // nacos group,可继承,可选
  string group = 4 [(field_options) = {
    inheritable: true;
    forbidden: true;
  }];

  // 可继承,必须
  Protocol protocol = 5 [(field_options) = {
    inheritable: true;
    required: true;
  }];

  // java, go package name ,不可继承
  string package = 7 [(field_options) = {
    forbidden: true
  }];

  // service名, 由插件赋值,不可配置
  string name = 8 [(field_options) = {
    forbidden: true
  }];

  // 过滤器
  repeated FilterConfig filters = 9 [(field_options) = {
    placeholder: true
  }];

  // 负载均衡方式
  Selector selector = 10 [(field_options) = {
    required: true
  }];

  // 由插件使用,不可配置
  repeated Method methods = 11 [(field_options) = {
    forbidden: true
  }];

  // method超时时间(秒)
  int32 timeout = 12 [(field_options) = {
    placeholder: true
  }];

  // selector_meta用于标记服务选取的key和value，以及(路由一对多服务时)
  // groups:         {"groups":{"category":"option"}}  // 静态分组，解析body
  // composite:      {"composite":{"type":"default","group":"option-asset","order":[""]}} // 组合类型
  // dynamic_groups: {"dynamic_groups":[{"key":"_sp_category","value":"fbu"}]}  // 动态分组，只取header或者query参数
  // select_keys:    {"select_keys":["_sp_business"]} // 动态选取nacos的服务列表元数据key做负载均衡，只取header或者query参数
  // groups，dynamic_groups和composite必须三选一，不能同时存在
  string selector_meta = 13 [(field_options) = {
    placeholder: true
  }];

  // 是否允许wss调用
  bool allow_wss = 14 [(field_options) = {
    placeholder: true
  }];

  // 负载均衡元信息
  string load_balance_meta = 15 [(field_options) = {
    placeholder: true
  }];

  // category信息
  string category = 16 [(field_options) = {
    placeholder: true
  }];

  // demo group可选
  bool with_demo_account = 17 [(field_options) = {
    placeholder: true
  }];

  // group route mode,可选,strict为默认值
  string group_route_mode = 18 [(field_options) = {
    placeholder: true
  }];

  // breaker 熔断功能开关
  bool breaker = 19 [(field_options) = {
    placeholder: true
  }];

  // risk_sign 加签功能开关
  bool risk_sign = 20 [(field_options) = {
    placeholder: true
  }];
}

message Module {
  // 所属app
  string app = 1 [(field_options) = {
    required: true
  }];

  // module名称,全局唯一
  string module = 2 [(field_options) = {
    required: true
  }];

  // 由插件填写和使用
  repeated Service services = 3 [(field_options) = {
    forbidden: true
  }];

  AppCfg app_cfg = 4;
}

message AppCfg {
  bool mapping = 1; // 是否需要映射
  string key = 2; // 映射的key
  map<string, string> value = 3; // map映射表
}

message Out {
  repeated Module modules = 1;
}
