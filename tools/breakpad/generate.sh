#!/bin/bash

cd `dirname "${BASH_SOURCE[0]}"`

if [ "$(uname -s)" != "Linux" -o "$(uname -m)" != "x86_64" ] ; then
    echo "only support x64-linux"
    exit 1
fi

current_path=${PWD}
tools_directory="${current_path}/"
output_directory="/corefile/output/"
minidump_filepath=""
minidump_filename=""
exe_filepath=""
exe_filename=""


help_function() {
    echo "        -h, print help information"
    echo "        -t tools_directory, breakpad tools directory, default: ${tools_directory}"
    echo "        -o output_directory, output directory, default: ${output_directory}"
    echo "        -f minidump_filepath, minidump filepath, default: "
    echo "        -e exe_filepath, exe filepath, default: "
}

#please `brew install gnu-getopt` on mac
OPTIONS=`getopt -o ht:o:f:e: -- $@`

eval set -- "${OPTIONS}"

while true ; do
    case "$1" in
        -h) help_function ; exit 0;;
        -t) tools_directory="$2" ; shift 2;;
        -o) output_directory="$2" ; shift 2;;
        -f) minidump_filepath="$2" ; shift 2;;
        -e) exe_filepath="$2" ; shift 2;;
        --) shift ; break ;;
    esac
done

minidump_filename=${minidump_filepath##*/}
exe_filename=${exe_filepath##*/}

echo tools_directory: ${tools_directory}
echo output_directory: ${output_directory}
echo minidump_filepath: ${minidump_filepath}
echo minidump_filename: ${minidump_filename}
echo exe_filepath: ${exe_filepath}
echo exe_filename: ${exe_filename}


# copy minidump
mkdir -p ${output_directory}
output_minidump_filepath=${output_directory}/${exe_filename}.${minidump_filename}
output_minidump_filename=${output_minidump_filepath##*/}
echo output_minidump_filepath: ${output_minidump_filepath}
echo output_minidump_filename: ${output_minidump_filename}
cp -f ${minidump_filepath} ${output_minidump_filepath}

# extra symbol
temp_output_exe_symbol_filepath=${output_directory}/${exe_filename}.sym
${tools_directory}/dump_syms ${exe_filepath} > ${temp_output_exe_symbol_filepath}
output_exe_symbol_directory=${output_directory}/symbols/$(head -n1 ${temp_output_exe_symbol_filepath} | awk '{print $5}')/$(head -n1 ${temp_output_exe_symbol_filepath} | awk '{print $4}')
output_exe_symbol_filepath=${output_exe_symbol_directory}/${exe_filename}.sym
echo output_exe_symbol_directory: ${output_exe_symbol_directory}
echo output_exe_symbol_filepath: ${output_exe_symbol_filepath}
mkdir -p ${output_exe_symbol_directory}
mv -f ${temp_output_exe_symbol_filepath} ${output_exe_symbol_filepath}

# text minidump
output_text_minidump_filepath=${output_directory}/${exe_filename}.${minidump_filename}.text
echo output_text_minidump_filepath: ${output_text_minidump_filepath}
${tools_directory}/minidump_stackwalk ${output_minidump_filepath} ${output_directory}/symbols -s &> ${output_text_minidump_filepath}

# generate coredump
output_coredump_filepath=${output_directory}/core.${exe_filename}.${minidump_filename}.coredump
echo output_coredump_filepath: ${output_coredump_filepath}
${tools_directory}/minidump-2-core ${output_minidump_filepath} -o ${output_coredump_filepath}
