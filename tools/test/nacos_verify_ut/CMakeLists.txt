find_package(GTest CONFIG REQUIRED)

add_compile_options(-fno-access-control -g)

set(nacos_verify_path ${CMAKE_SOURCE_DIR}/tools/nacos_verify)
add_custom_command(
    OUTPUT ${nacos_verify_path}/proto/uta_engine_nacos_verify_service.pb.h ${nacos_verify_path}/proto/uta_engine_nacos_verify_service.pb.cc
    COMMAND protobuf::protoc -I${nacos_verify_path}/proto  --cpp_out=${nacos_verify_path}/proto ${nacos_verify_path}/proto/uta_engine_nacos_verify_service.proto
    MAIN_DEPENDENCY ${nacos_verify_path}/proto/uta_engine_nacos_verify_service.proto
)

get_target_property(grpc_cpp_plugin_bin gRPC::grpc_cpp_plugin IMPORTED_LOCATION_RELEASE)
add_custom_command(
    OUTPUT ${nacos_verify_path}/proto/uta_engine_nacos_verify_service.grpc.pb.h ${nacos_verify_path}/proto/uta_engine_nacos_verify_service.grpc.pb.cc
    COMMAND protobuf::protoc -I${nacos_verify_path}/proto --plugin=protoc-gen-grpc=${grpc_cpp_plugin_bin} --grpc_out=${nacos_verify_path}/proto ${nacos_verify_path}/proto/uta_engine_nacos_verify_service.proto
    MAIN_DEPENDENCY ${nacos_verify_path}/proto/uta_engine_nacos_verify_service.proto
)

file(GLOB_RECURSE PROJECT_TEST_SRC_FILES
                    ${CMAKE_CURRENT_SOURCE_DIR}/*.cpp
                    ${nacos_verify_path}/*.cpp)
list(REMOVE_ITEM PROJECT_TEST_SRC_FILES ${nacos_verify_path}/main.cpp)
set(PROJECT_TEST_SRC_FILES ${PROJECT_TEST_SRC_FILES}
                      ${nacos_verify_path}/proto/uta_engine_nacos_verify_service.pb.cc
                      ${nacos_verify_path}/proto/uta_engine_nacos_verify_service.grpc.pb.cc)

set(nacos_verify_ut_bin nacos_verify_ut.bin)

add_executable(${nacos_verify_ut_bin} ${PROJECT_TEST_SRC_FILES})
target_include_directories(${nacos_verify_ut_bin} PRIVATE ${googletest_INCLUDE_DIR})
target_link_libraries(${nacos_verify_ut_bin} PRIVATE
    GTest::gtest
    GTest::gmock
    bbase::bcommon_ut
    bbase::bshare
    proto_gen
    ${LIB_WHITEBOX}
    $<TARGET_OBJECTS:application_obj>
    $<TARGET_OBJECTS:application_version_obj>
    $<TARGET_OBJECTS:biz_worker_obj>
    $<TARGET_OBJECTS:adl_worker_obj>
    $<TARGET_OBJECTS:common_obj>
    $<TARGET_OBJECTS:config_obj>
    $<TARGET_OBJECTS:cross_worker_obj>
    $<TARGET_OBJECTS:data_obj>
    $<TARGET_OBJECTS:grpc_worker_obj>
    $<TARGET_OBJECTS:lending_risk_notify_obj>
    $<TARGET_OBJECTS:margin_request_obj>
    $<TARGET_OBJECTS:monitor_obj>
    $<TARGET_OBJECTS:post_worker_obj>
    $<TARGET_OBJECTS:seq_mark_worker_obj>
    $<TARGET_OBJECTS:trading_dump_worker_obj>
    $<TARGET_OBJECTS:trigger_worker_obj>
    $<TARGET_OBJECTS:quote_merge_worker_obj>
    $<TARGET_OBJECTS:persist_worker_obj>
    $<TARGET_OBJECTS:open_interest_obj>)

install(TARGETS ${nacos_verify_ut_bin}
    COMPONENT ${nacos_verify_ut_bin}
    PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
    RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/test/nacos_verify_ut
)

# # make test
include(GoogleTest)
gtest_add_tests(TARGET ${nacos_verify_ut_bin})
