{"name": "uta-engine", "version-string": "2021.10.15", "builtin-baseline": "d8c3b79708bed1b08523bb36f0f3c09c8997fdd7", "vcpkg-configuration": {"registries": [{"kind": "git", "repository": "*****************:public-lib/forks/vcpkg.git", "reference": "bybit-stable", "baseline": "33997e701a1b8037d0dad32699df25146cd55547", "packages": ["bbase", "bblog", "boost-multiprecision", "breakpad", "dtssdk", "etcd-cpp-apiv3", "folly", "fswatch", "grpc", "libback<PERSON>ce", "nacos-sdk-cppv2", "opentelemetry-cpp", "prometheus-cpp", "workflow"]}]}, "dependencies": ["bbase", "benchmark", "boost-uuid", "gtest", "catch2", "cxxopts", "gperftools", {"name": "<PERSON><PERSON><PERSON><PERSON>", "platform": "!(osx)"}], "overrides": [{"name": "bbase", "version": "1.2.8#0"}, {"name": "abseil", "version": "20240722.0#0"}, {"name": "benchmark", "version": "1.8.2"}, {"name": "boost-algorithm", "version": "1.79.0"}, {"name": "boost-align", "version": "1.79.0"}, {"name": "boost-any", "version": "1.79.0"}, {"name": "boost-array", "version": "1.79.0"}, {"name": "boost-asio", "version": "1.79.0"}, {"name": "boost-assert", "version": "1.79.0"}, {"name": "boost-atomic", "version": "1.79.0"}, {"name": "boost-bind", "version": "1.79.0"}, {"name": "boost-build", "version": "1.79.0"}, {"name": "boost-chrono", "version": "1.79.0"}, {"name": "boost-concept-check", "version": "1.79.0"}, {"name": "boost-config", "version": "1.79.0"}, {"name": "boost-container-hash", "version": "1.79.0"}, {"name": "boost-container", "version": "1.79.0"}, {"name": "boost-context", "version": "1.79.0"}, {"name": "boost-conversion", "version": "1.79.0"}, {"name": "boost-core", "version": "1.79.0"}, {"name": "boost-coroutine", "version": "1.79.0"}, {"name": "boost-crc", "version": "1.79.0"}, {"name": "boost-date-time", "version": "1.79.0"}, {"name": "boost-detail", "version": "1.79.0"}, {"name": "boost-dynamic-bitset", "version": "1.79.0"}, {"name": "boost-endian", "version": "1.79.0"}, {"name": "boost-exception", "version": "1.79.0"}, {"name": "boost-filesystem", "version": "1.79.0"}, {"name": "boost-foreach", "version": "1.79.0"}, {"name": "boost-function-types", "version": "1.79.0"}, {"name": "boost-function", "version": "1.79.0"}, {"name": "boost-fusion", "version": "1.79.0"}, {"name": "boost-integer", "version": "1.79.0"}, {"name": "boost-intrusive", "version": "1.79.0"}, {"name": "boost-io", "version": "1.79.0"}, {"name": "boost-iterator", "version": "1.79.0"}, {"name": "boost-lexical-cast", "version": "1.79.0"}, {"name": "boost-locale", "version": "1.79.0"}, {"name": "boost-lockfree", "version": "1.79.0"}, {"name": "boost-math", "version": "1.79.0"}, {"name": "boost-modular-build-helper", "version": "1.79.0#1"}, {"name": "boost-move", "version": "1.79.0"}, {"name": "boost-mp11", "version": "1.79.0"}, {"name": "boost-mpl", "version": "1.79.0"}, {"name": "boost-multi-index", "version": "1.79.0"}, {"name": "boost-multiprecision", "version": "********"}, {"name": "boost-numeric-conversion", "version": "1.79.0"}, {"name": "boost-optional", "version": "1.79.0"}, {"name": "boost-parameter", "version": "1.79.0"}, {"name": "boost-phoenix", "version": "1.79.0"}, {"name": "boost-pool", "version": "1.79.0"}, {"name": "boost-predef", "version": "1.79.0"}, {"name": "boost-preprocessor", "version": "1.79.0"}, {"name": "boost-program-options", "version": "1.79.0"}, {"name": "boost-proto", "version": "1.79.0"}, {"name": "boost-random", "version": "1.79.0"}, {"name": "boost-range", "version": "1.79.0"}, {"name": "boost-ratio", "version": "1.79.0"}, {"name": "boost-rational", "version": "1.79.0"}, {"name": "boost-regex", "version": "1.79.0"}, {"name": "boost-scope-exit", "version": "1.79.0"}, {"name": "boost-serialization", "version": "1.79.0"}, {"name": "boost-smart-ptr", "version": "1.79.0"}, {"name": "boost-spirit", "version": "1.79.0"}, {"name": "boost-static-assert", "version": "1.79.0"}, {"name": "boost-system", "version": "1.79.0"}, {"name": "boost-thread", "version": "1.79.0"}, {"name": "boost-throw-exception", "version": "1.79.0"}, {"name": "boost-tokenizer", "version": "1.79.0"}, {"name": "boost-tti", "version": "1.79.0"}, {"name": "boost-tuple", "version": "1.79.0"}, {"name": "boost-type-index", "version": "1.79.0"}, {"name": "boost-type-traits", "version": "1.79.0"}, {"name": "boost-typeof", "version": "1.79.0"}, {"name": "boost-uninstall", "version": "1.79.0"}, {"name": "boost-unordered", "version": "1.79.0"}, {"name": "boost-utility", "version": "1.79.0"}, {"name": "boost-uuid", "version": "1.79.0"}, {"name": "boost-variant2", "version": "1.79.0"}, {"name": "boost-variant", "version": "1.79.0"}, {"name": "boost-vcpkg-helpers", "version": "1.79.0"}, {"name": "boost-winapi", "version": "1.79.0"}, {"name": "breakpad", "version": "2022-07-12-3#0"}, {"name": "c-ares", "version": "1.33.1#0"}, {"name": "civetweb", "version": "1.15#1"}, {"name": "cpprestsdk", "version": "2.10.18#1"}, {"name": "curl", "version": "7.87.0#1"}, {"name": "cxxopts", "version": "3.0.0"}, {"name": "double-conversion", "version": "3.2.0"}, {"name": "dtssdk", "version": "3.2.6.7#0"}, {"name": "etcd-cpp-apiv3", "version": "0.2.6.4#0"}, {"name": "fmt", "version": "9.1.0#1"}, {"name": "folly", "version-string": "2022.10.31.00.1"}, {"name": "fswatch", "version": "1.17.1.1#0"}, {"name": "gflags", "version": "2.2.2#5"}, {"name": "glog", "version": "0.5.0#3"}, {"name": "gmp", "version": "6.2.1#9"}, {"name": "gperftools", "version": "2.10"}, {"name": "grpc", "version": "1.65.5.1#0"}, {"name": "gtest", "version": "1.13.0"}, {"name": "<PERSON><PERSON>", "version": "1.0.2#3"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "5.3.0#0"}, {"name": "libdisasm", "version": "0.23#11"}, {"name": "libevent", "version": "2.1.12#6"}, {"name": "libiconv", "version": "1.16#13"}, {"name": "lz4", "version": "1.9.3#3"}, {"name": "mpfr", "version": "4.1.0#4"}, {"name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "version": "3.10.5#2"}, {"name": "openssl", "version": "3.0.3"}, {"name": "opentelemetry-cpp", "version": "1.3.0.2#2"}, {"name": "prometheus-cpp", "version": "1.0.1.1"}, {"name": "protobuf", "version": "5.26.1#0"}, {"name": "re2", "version": "2024-07-02"}, {"name": "redis-plus-plus", "version": "1.3.2"}, {"name": "spdlog", "version": "1.10.0"}, {"name": "thrift", "version": "0.13.0#2"}, {"name": "utf8-range", "version": "5.26.1#0"}, {"name": "upb", "version": "5.26.1#0"}, {"name": "vcpkg-cmake-config", "version": "2022-02-06"}, {"name": "vcpkg-cmake", "version": "2022-05-06"}, {"name": "vcpkg-cmake-get-vars", "version": "2023-12-31"}, {"name": "zlib", "version": "1.3.1#0"}, {"name": "libback<PERSON>ce", "version": "2022-12-21#1"}, {"name": "bblog", "version": "4.0.2#0"}, {"name": "zstd", "version": "1.5.2#1"}, {"name": "snappy", "version": "1.1.9#2"}, {"name": "workflow", "version": "0.10.5.3#0"}, {"name": "yaml-cpp", "version": "0.8.0#1"}, {"name": "nacos-sdk-cppv2", "version": "2.1.2#0"}, {"name": "<PERSON><PERSON><PERSON>", "version": "2023-07-17#1"}, {"name": "catch2", "version": "3.5.2#0"}]}