{"version": 3, "cmakeMinimumRequired": {"major": 3, "minor": 23, "patch": 0}, "configurePresets": [{"name": "common", "hidden": true, "binaryDir": "${sourceDir}/build", "cacheVariables": {"ENABLE_UNITY_BUILD": "OFF", "ENABLE_UT_UNITY_BUILD": "OFF", "CMAKE_EXPORT_COMPILE_COMMANDS": "ON", "ENABLE_BUILD_WITH_TIME_TRACE": "OFF", "ENABLE_BUILD_TOOLS": "OFF", "TRADING_BUILD_VERSION": "$env{TRADING_BUILD_VERSION}", "BUILD_TESTING": "ON", "ENABLE_CCACHE": "ON"}}, {"name": "vcpkg-ci", "hidden": true, "cacheVariables": {"CMAKE_TOOLCHAIN_FILE": {"type": "FILEPATH", "value": "/usr/local/ci_vcpkg/scripts/buildsystems/vcpkg.cmake"}, "VCPKG_INSTALLED_DIR": "/usr/local/ci_vcpkg/vcpkg_installed"}}, {"name": "vcpkg-local", "hidden": true, "cacheVariables": {"CMAKE_TOOLCHAIN_FILE": {"type": "FILEPATH", "value": "${sourceDir}/deps/vcpkg/scripts/buildsystems/vcpkg.cmake"}, "VCPKG_INSTALLED_DIR": "${sourceDir}/deps/installed", "VCPKG_MANIFEST_DIR": "${sourceDir}/deps"}}, {"name": "vcpkg-env", "hidden": true, "cacheVariables": {"CMAKE_TOOLCHAIN_FILE": {"type": "FILEPATH", "value": "$env{VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake"}}}, {"name": "Linux-CI", "generator": "Ninja Multi-Config", "inherits": ["common", "vcpkg-ci"], "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Linux"}}, {"name": "MacOS-M1", "hidden": true, "generator": "Ninja Multi-Config", "inherits": ["common", "vcpkg-local"], "cacheVariables": {"ENABLE_BUILD_TOOLS": "ON", "ENABLE_UNITY_BUILD": "OFF", "ENABLE_UT_UNITY_BUILD": "OFF", "VCPKG_TARGET_TRIPLET": "arm64-osx", "VCPKG_HOST_TRIPLET": "arm64-osx"}, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "<PERSON>"}}, {"name": "MD", "generator": "Ninja", "inherits": ["MacOS-M1"], "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "ENABLE_UNITY_BUILD": "OFF", "ENABLE_UT_UNITY_BUILD": "OFF"}}, {"name": "MDU", "generator": "Ninja", "inherits": ["MacOS-M1"], "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "ENABLE_UNITY_BUILD": "ON", "ENABLE_UT_UNITY_BUILD": "ON"}}, {"name": "MR", "generator": "Ninja", "inherits": ["MacOS-M1"], "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "ENABLE_UNITY_BUILD": "OFF", "ENABLE_UT_UNITY_BUILD": "OFF"}}], "buildPresets": [{"name": "common", "hidden": true, "jobs": 8, "nativeToolOptions": ["-j8"]}, {"name": "Linux-CI-Release", "configurePreset": "Linux-CI", "inherits": ["common"], "configuration": "Release"}, {"name": "Linux-CI-Debug", "configurePreset": "Linux-CI", "inherits": ["common"], "configuration": "Debug"}, {"name": "MR", "configurePreset": "MR", "inherits": ["common"], "configuration": "Release"}, {"name": "MD", "configurePreset": "MD", "inherits": ["common"], "configuration": "Debug"}, {"name": "MDU", "configurePreset": "MDU", "inherits": ["common"], "configuration": "Debug"}], "testPresets": [{"name": "common", "hidden": true, "execution": {"jobs": 1, "timeout": 180, "stopOnFailure": true}}, {"name": "Linux-CI-Release", "configurePreset": "Linux-CI", "inherits": ["common"], "configuration": "Release"}, {"name": "Linux-CI-Debug", "configurePreset": "Linux-CI", "inherits": ["common"], "configuration": "Debug"}, {"name": "MR", "configurePreset": "MacOS-M1", "inherits": ["common"], "configuration": "Release", "output": {"outputOnFailure": true}, "execution": {"noTestsAction": "error", "stopOnFailure": true}}, {"name": "MD", "configurePreset": "MacOS-M1", "inherits": ["common"], "configuration": "Debug", "output": {"outputOnFailure": true}, "execution": {"noTestsAction": "error", "stopOnFailure": true}}]}