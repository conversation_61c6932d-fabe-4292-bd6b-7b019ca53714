#include "test/biz/fast_dump_recovery/fast_dump_service_impl.hpp"

#include <bbase/common/hdts/hdts.hpp>
#include <boost/uuid/random_generator.hpp>
#include <boost/uuid/uuid_io.hpp>

FastDumpServiceImpl::~FastDumpServiceImpl() {
  bbase::hdts::Hdts::Fini();
  bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::ObjectType::kObjectConfigCenter);

  if (grpc_client_ != nullptr) {
    grpc_client_->StopProcess();
  }
}

void FastDumpServiceImpl::set_session_id() {
  session_id_ = boost::uuids::to_string(boost::uuids::random_generator()());
  return;
}

void FastDumpServiceImpl::Init(std::string zone) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  // 初始化etcd
  etcd_client_ = std::make_shared<bbase::etcd_client::EtcdClient>();
  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::ObjectType::kObjectConfigCenter,
                                                       etcd_client_);
  etcd_client_->InitManually();

  zone_ = zone;

  // 获取grpc地址
  std::string trading_dump_grpc_key;
  char buf[256] = {0};
  snprintf(buf, sizeof(buf) - 1, "u2fastdump.instances.rpc/%s", zone.c_str());
  trading_dump_grpc_key.assign(buf);
  address_ = etcd_client_->GetStringVar(trading_dump_grpc_key, "");

  // 初始化grpc client
  grpc::ChannelArguments channel_arguments;
  channel_arguments.SetInt(GRPC_ARG_MAX_CONNECTION_IDLE_MS, 600000);
  channel_arguments.SetInt(GRPC_ARG_KEEPALIVE_TIME_MS, 5000);
  channel_arguments.SetInt(GRPC_ARG_KEEPALIVE_TIMEOUT_MS, 1000);
  channel_arguments.SetInt(GRPC_ARG_INITIAL_RECONNECT_BACKOFF_MS, 1000);
  channel_arguments.SetInt(GRPC_ARG_MIN_RECONNECT_BACKOFF_MS, 1000);
  channel_arguments.SetInt(GRPC_ARG_MAX_RECONNECT_BACKOFF_MS, 5000);
  channel_arguments.SetInt(GRPC_ARG_KEEPALIVE_PERMIT_WITHOUT_CALLS, 1);

  if (grpc_client_ != nullptr) {
    grpc_client_->StopProcess();
  }

  set_session_id();

  grpc_client_ = std::make_shared<bbase::bgrpc::AsyncClient<>>();
  grpc_client_->Build(1, 1, address_, channel_arguments);
  grpc_stub_ = svc::unified_v2::U2DumpService::NewStub(grpc_client_->channel());
  std::cout << fmt::format("trading dump service address is [{}]", address_) << std::endl;

  grpc_client_->StartProcess();
}
