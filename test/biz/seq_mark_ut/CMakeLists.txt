find_package(benchmark CONFIG REQUIRED)
find_package(GTest CONFIG REQUIRED)

add_compile_options(-fno-access-control -g)

file(GLOB_RECURSE PROJECT_TEST_SRC_FILES
    "${CMAKE_CURRENT_SOURCE_DIR}/mock/*.cpp"
    "${CMAKE_SOURCE_DIR}/test/mocks/nacos_config/nacos_config_mock.cpp"
    "${CMAKE_SOURCE_DIR}/test/mocks/hdts/*.cpp")

set(seq_mark_ut_bin seq_mark_ut.bin)

add_executable(${seq_mark_ut_bin} ${PROJECT_TEST_SRC_FILES} "${CMAKE_CURRENT_SOURCE_DIR}/seq_mark_ut.cpp")
target_include_directories(${seq_mark_ut_bin} PRIVATE ${googletest_INCLUDE_DIR})
target_link_libraries(${seq_mark_ut_bin} PRIVATE
    GTest::gtest
    GTest::gmock
    bbase::bcommon_ut
    bbase::bshare
    proto_gen
    ${LIB_WHITEBOX}
    $<TARGET_OBJECTS:application_obj>
    $<TARGET_OBJECTS:application_version_obj>
    $<TARGET_OBJECTS:biz_worker_obj>
    $<TARGET_OBJECTS:adl_worker_obj>
    $<TARGET_OBJECTS:common_obj>
    $<TARGET_OBJECTS:config_obj>
    $<TARGET_OBJECTS:cross_worker_obj>
    $<TARGET_OBJECTS:data_obj>
    $<TARGET_OBJECTS:grpc_worker_obj>
    $<TARGET_OBJECTS:lending_risk_notify_obj>
    $<TARGET_OBJECTS:margin_request_obj>
    $<TARGET_OBJECTS:monitor_obj>
    $<TARGET_OBJECTS:post_worker_obj>
    $<TARGET_OBJECTS:seq_mark_worker_obj>
    $<TARGET_OBJECTS:trading_dump_worker_obj>
    $<TARGET_OBJECTS:trigger_worker_obj>
    $<TARGET_OBJECTS:quote_merge_worker_obj>
    $<TARGET_OBJECTS:persist_worker_obj>
    $<TARGET_OBJECTS:open_interest_obj>)

install(TARGETS ${seq_mark_ut_bin}
    COMPONENT ${seq_mark_ut_bin}
    PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
    RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/test/seq_mark_ut
)

set(seq_mark_benchmark_bin seq_mark_benchmark.bin)

add_executable(${seq_mark_benchmark_bin} ${PROJECT_TEST_SRC_FILES} "${CMAKE_CURRENT_SOURCE_DIR}/seq_mark_benchmark.cpp")
target_include_directories(${seq_mark_benchmark_bin} PRIVATE ${googletest_INCLUDE_DIR})
target_link_libraries(${seq_mark_benchmark_bin} PRIVATE
    benchmark::benchmark
    benchmark::benchmark_main
    GTest::gtest
    GTest::gmock
    bbase::bcommon_ut
    bbase::bshare
    proto_gen
    ${LIB_WHITEBOX}
    $<TARGET_OBJECTS:application_obj>
    $<TARGET_OBJECTS:application_version_obj>
    $<TARGET_OBJECTS:biz_worker_obj>
    $<TARGET_OBJECTS:adl_worker_obj>
    $<TARGET_OBJECTS:common_obj>
    $<TARGET_OBJECTS:config_obj>
    $<TARGET_OBJECTS:cross_worker_obj>
    $<TARGET_OBJECTS:data_obj>
    $<TARGET_OBJECTS:grpc_worker_obj>
    $<TARGET_OBJECTS:lending_risk_notify_obj>
    $<TARGET_OBJECTS:margin_request_obj>
    $<TARGET_OBJECTS:monitor_obj>
    $<TARGET_OBJECTS:post_worker_obj>
    $<TARGET_OBJECTS:seq_mark_worker_obj>
    $<TARGET_OBJECTS:trading_dump_worker_obj>
    $<TARGET_OBJECTS:trigger_worker_obj>
    $<TARGET_OBJECTS:quote_merge_worker_obj>
    $<TARGET_OBJECTS:persist_worker_obj>
    $<TARGET_OBJECTS:open_interest_obj>)

install(TARGETS ${seq_mark_benchmark_bin}
    COMPONENT ${seq_mark_benchmark_bin}
    PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
    RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/test/seq_mark_benchmark
)

# # make test
include(GoogleTest)
gtest_add_tests(TARGET ${seq_mark_ut_bin})
