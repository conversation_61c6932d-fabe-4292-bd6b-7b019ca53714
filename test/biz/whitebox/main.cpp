#include <third_party/include/whitebox/wbsk_skb.h>

#include <bbase/common/utils/codec.hpp>
#include <iostream>
#include <string>

int main() {
  std::string input{"Hello World"};
  unsigned char output[256];
  int out_len = sizeof(output);

  auto result =
      wbsk_LAES_ecb_encrypt(reinterpret_cast<const unsigned char*>(input.data()), input.length(), output, &out_len);

  if (result != 0) {
    printf("Encryption failed with error code: %d\n", result);

    return 0;
  }

  std::string encrypted_str(reinterpret_cast<char*>(output), out_len);
  std::string encrypted_base64_str;
  bbase::utils::base64_encode(encrypted_str, encrypted_base64_str);

  std::cout << encrypted_str << std::endl;
  std::cout << encrypted_base64_str << std::endl;

  std::string encrypted_base64_decode_str;
  bbase::utils::base64_decode(encrypted_base64_str, encrypted_base64_decode_str);
  std::cout << encrypted_base64_decode_str << std::endl;

  unsigned char decrypted[256];
  int decrypted_len = sizeof(decrypted);

  result = wbsk_LAES_ecb_decrypt(reinterpret_cast<const unsigned char*>(encrypted_base64_decode_str.data()),
                                 encrypted_base64_decode_str.length(), decrypted, &decrypted_len);

  if (result != 0) {
    printf("Decryption failed with error code: %d\n", result);

    return 0;
  }

  std::string decrypted_str(reinterpret_cast<char*>(decrypted), decrypted_len);

  std::cout << decrypted_str << std::endl;

  return 0;
}
