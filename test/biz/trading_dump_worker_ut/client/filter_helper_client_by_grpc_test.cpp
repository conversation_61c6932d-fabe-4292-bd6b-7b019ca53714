#include "trading_dump_worker/client/filter_helper_client_by_grpc.hpp"

#include <gtest/gtest.h>

#include <bbase/common/hdts/hdts.hpp>
#include <bbase/common/utils/zstd.hpp>

#include "src/data/error/error.hpp"
#include "trading_dump_worker/trading_dump_engine.hpp"

extern std::int32_t StartServer(bbase::bgrpc::AsyncServer<>& server_p);
extern std::int32_t ServerPort();

class TradingDumpFilterHelperClientByGrpcTest : public testing::Test {
 public:
  void SetUp() {
    r_handler = std::make_shared<worker::ResultHandler>();
    application::App app;
    engine = new trading_dump_worker::TradingDumpEngine(0, r_handler, &app);
    client = new trading_dump_worker::FilterHelperClientByGrpc(engine);
    StartServer(server);
  }

  void TearDown() {
    server.StopProcess();

    // if (engine) delete engine;
    // if (client) delete client;
  }

  static void SetUpTestSuite() {}
  static void TearDownTestSuite() {}

 protected:
  std::shared_ptr<worker::ResultHandler> r_handler;
  trading_dump_worker::TradingDumpEngine* engine = nullptr;
  trading_dump_worker::FilterHelperClientByGrpc* client = nullptr;
  bbase::bgrpc::AsyncServer<> server;
};

TEST_F(TradingDumpFilterHelperClientByGrpcTest, InvokeMethodTestSuccess) {
  grpc::Status status(grpc::StatusCode::OK, "ok");
  auto response_p = std::make_shared<filter_helper::GetOffsetRelResponse>();
  auto res_map = response_p->mutable_mapcrossoffsetresp();
  filter_helper::OffsetRespInfo resp_info;
  resp_info.set_resp_status(filter_helper::OffsetRespStatus::SUCCESS);
  resp_info.set_resp_offset(10000);
  resp_info.set_req_cross_seq(10000);
  resp_info.set_resp_cross_seq(10000);
  (*res_map)[5] = resp_info;
  client->InvokeMethod(nullptr, response_p);
}

TEST_F(TradingDumpFilterHelperClientByGrpcTest, InvokeMethodTestNodata) {
  grpc::Status status(grpc::StatusCode::OK, "ok");
  auto response_p = std::make_shared<filter_helper::GetOffsetRelResponse>();
  auto res_map = response_p->mutable_mapcrossoffsetresp();
  filter_helper::OffsetRespInfo resp_info;
  resp_info.set_resp_status(filter_helper::OffsetRespStatus::NOT_FOUND_NODATA);
  resp_info.set_resp_offset(-2);
  resp_info.set_req_cross_seq(-1);
  resp_info.set_resp_cross_seq(-1);
  (*res_map)[5] = resp_info;
  client->InvokeMethod(nullptr, response_p);
}

TEST_F(TradingDumpFilterHelperClientByGrpcTest, InvokeMethodTestFailed) {
  grpc::Status status(grpc::StatusCode::OK, "ok");
  auto response_p = std::make_shared<filter_helper::GetOffsetRelResponse>();
  auto res_map = response_p->mutable_mapcrossoffsetresp();
  filter_helper::OffsetRespInfo resp_info;
  resp_info.set_resp_status(filter_helper::OffsetRespStatus::NOT_FOUND_CROSS_ID);
  (*res_map)[5] = resp_info;
  ASSERT_DEATH(client->InvokeMethod(nullptr, response_p), "");
}

TEST_F(TradingDumpFilterHelperClientByGrpcTest, InvokeMethodTestInvalidResponse) {
  grpc::Status status(grpc::StatusCode::OK, "ok");
  ASSERT_DEATH(client->InvokeMethod(nullptr, nullptr), "");
}
