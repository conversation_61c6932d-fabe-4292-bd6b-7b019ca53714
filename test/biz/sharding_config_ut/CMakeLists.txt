add_compile_options(-fno-access-control)

find_package(GTest CONFIG REQUIRED)

set(sharding_config_ut_src
    ${CMAKE_CURRENT_SOURCE_DIR}/main.cpp
    ${CMAKE_SOURCE_DIR}/test/mocks/nacos_config/nacos_config_mock.cpp
)

set(sharding_config_ut_bin sharding_config_ut.bin)

add_executable(${sharding_config_ut_bin} ${sharding_config_ut_src})
target_link_libraries(${sharding_config_ut_bin} PRIVATE GTest::gtest)
target_link_libraries(${sharding_config_ut_bin} PRIVATE GTest::gmock)
target_link_libraries(${sharding_config_ut_bin} PRIVATE bbase::bcommon_ut bbase::bshare)
target_link_libraries(${sharding_config_ut_bin} PRIVATE proto_gen)
target_link_libraries(${sharding_config_ut_bin} PRIVATE 
    ${LIB_WHITEBOX}
    $<TARGET_OBJECTS:application_obj>
                                                        $<TARGET_OBJECTS:application_version_obj>
                                                        $<TARGET_OBJECTS:biz_worker_obj>
                                                        $<TARGET_OBJECTS:adl_worker_obj>
                                                        $<TARGET_OBJECTS:common_obj>
                                                        $<TARGET_OBJECTS:config_obj>
                                                        $<TARGET_OBJECTS:cross_worker_obj>
                                                        $<TARGET_OBJECTS:data_obj>
                                                        $<TARGET_OBJECTS:grpc_worker_obj>
                                                        $<TARGET_OBJECTS:lending_risk_notify_obj>
                                                        $<TARGET_OBJECTS:margin_request_obj>
                                                        $<TARGET_OBJECTS:monitor_obj>
                                                        $<TARGET_OBJECTS:post_worker_obj>
                                                        $<TARGET_OBJECTS:seq_mark_worker_obj>
                                                        $<TARGET_OBJECTS:trading_dump_worker_obj>
                                                        $<TARGET_OBJECTS:trigger_worker_obj>
                                                        $<TARGET_OBJECTS:quote_merge_worker_obj>
                                                        $<TARGET_OBJECTS:persist_worker_obj>
                                                        $<TARGET_OBJECTS:open_interest_obj>
)

install(TARGETS ${sharding_config_ut_bin}
    COMPONENT ${sharding_config_ut_bin}
    PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
    RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/test/sharding_config_ut
)


## make test
include(GoogleTest)
gtest_add_tests(TARGET ${sharding_config_ut_bin})
