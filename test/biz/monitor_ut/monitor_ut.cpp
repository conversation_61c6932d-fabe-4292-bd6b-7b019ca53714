#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <bbase/common/nacos_client/nacos_client_impl.hpp>
#include <chrono>
#include <memory>

#include "monitor/monitor.hpp"
#include "src/application/application.hpp"
#include "src/application/global_var_manager.hpp"
#include "src/config/config_proxy.hpp"

class MonitorTest : public testing::Test {
 public:
  void SetUp() override {}

  void TearDown() override {}
};

TEST_F(MonitorTest, monitor) {
  GTEST_SKIP() << "该case 临时不过，uford会fix";

  LOG_INFO("monitor wallet ut start");

  auto app = std::make_shared<application::App>();
  app->pipeline()->Init();

  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, app);
  monitor::Monitor::Instance().Init();
  monitor::Monitor::Instance().Start("localhost:19090");

  monitor::Monitor::Instance().IncWalletCounter("panic", "error", "kCoinNotSupport");
  monitor::Monitor::Instance().IncWalletCounter("panic", "error", "kCoinNotSupport");
  monitor::Monitor::Instance().IncWalletCounter("panic", "error", "kRequestCheckException");
  monitor::Monitor::Instance().IncWalletCounter("panic", "error", "kRequestCheckException");
  monitor::Monitor::Instance().IncWalletCounter("panic", "error", "kRequestCheckException");
  monitor::Monitor::Instance().IncWalletCounter("panic", "error", "kRequestCheckException");
  monitor::Monitor::Instance().IncWalletCounter("panic", "error", "kRequestCheckException");
  monitor::Monitor::Instance().IncWalletCounter("panic", "error", "kRequestCheckException");
  monitor::Monitor::Instance().IncWalletCounter("panic", "error", "kRequestCheckException");
  monitor::Monitor::Instance().IncWalletCounter("panic", "error", "kRequestCheckException");
  monitor::Monitor::Instance().IncWalletCounter("panic", "error", "kRequestCheckException");
  monitor::Monitor::Instance().IncWalletCounter("panic", "error", "kRequestCheckException");
  monitor::Monitor::Instance().IncWalletCounter("panic", "error", "kRequestCheckException");
  monitor::Monitor::Instance().IncWalletCounter("panic", "error", "kRequestCheckException");

  monitor::Monitor::Instance().OptionCounterIncrement("trading_option_error_code_metric", "5", "0", "inst_name");

  monitor::Monitor::Instance().OptionCounterIncrement("trading_option_error_code_metric", "5", "0", "inst_name");

  //  /**
  //   * 所有错误异常
  //   * 当type、subType，不是很清楚的时候，可以填写"-"
  //   *
  //   * type   种类：deposit、withdraw、wallet,system
  //   * subType种类：根据业务来定义，比如deposit业务，kCoinNotSupport,kRequestCheckException
  //   */
  //  auto errorCount_1 = monitor::Monitor::Instance().GetCounter("errorCount", "error_code", "sub_error_code");
  //  errorCount_1->Increment();
  //
  //  auto errorCount_2 = monitor::Monitor::Instance().GetCounter("errorCount", "error_code", "sub_error_code");
  //  errorCount_2->Increment(20);

  // case1
  monitor::Monitor::Instance().SetWalletGauge("queueSizeGauge", "threadthools", "accountShard_N");

  auto start = std::chrono::high_resolution_clock::now();
  std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  auto end = std::chrono::high_resolution_clock::now();
  auto nano_seconds = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);

  EXPECT_GT(nano_seconds.count(), **********);
  EXPECT_GT(**********, nano_seconds.count());

  LOG_INFO("monitor wallet ut end {}", nano_seconds.count());

  monitor::Monitor::Instance().ObserveWalletLatencyHistogram(EAction::create_order, "create_order",
                                                             nano_seconds.count());

  //  auto replace_order_tps_histogram = monitor::Monitor::Instance().GetHistogram("tps", "options", "replace_order");
  //  replace_order_tps_histogram->Observe(5);

  EXPECT_TRUE(monitor::checkMarginRatio_histogram != nullptr);
  EXPECT_TRUE(monitor::checkAssetAvailableBalance_histogram != nullptr);
  EXPECT_TRUE(monitor::syncMarginCost_histogram != nullptr);
  EXPECT_TRUE(monitor::liq_histogram != nullptr);

  for (int i = 0; i < 11; ++i) {
    EXPECT_TRUE(monitor::wallet_tradingResult_latency_h[i] != nullptr);
  }

  monitor::batch_size_c[monitor::ProductLabelType::kLabelProductFutures][monitor::BatchActionType::kLabelBatchCreate]
      ->Increment(1);
  monitor::batch_size_c[monitor::ProductLabelType::kLabelProductFutures][monitor::BatchActionType::kLabelBatchCancel]
      ->Increment(1);
  monitor::batch_size_c[monitor::ProductLabelType::kLabelProductFutures][monitor::BatchActionType::kLabelBatchReplace]
      ->Increment(1);
  monitor::batch_size_c[monitor::ProductLabelType::kLabelProductOptions][monitor::BatchActionType::kLabelBatchCreate]
      ->Increment(1);
  monitor::batch_size_c[monitor::ProductLabelType::kLabelProductOptions][monitor::BatchActionType::kLabelBatchCancel]
      ->Increment(1);
  monitor::batch_size_c[monitor::ProductLabelType::kLabelProductOptions][monitor::BatchActionType::kLabelBatchReplace]
      ->Increment(1);
  monitor::batch_size_c[monitor::ProductLabelType::kLabelProductSpot][monitor::BatchActionType::kLabelBatchCreate]
      ->Increment(1);
  monitor::batch_size_c[monitor::ProductLabelType::kLabelProductSpot][monitor::BatchActionType::kLabelBatchCancel]
      ->Increment(1);
  monitor::batch_size_c[monitor::ProductLabelType::kLabelProductSpot][monitor::BatchActionType::kLabelBatchReplace]
      ->Increment(1);

  monitor::Monitor::Instance().Stop();
  monitor::Monitor::Instance().Fini();

  bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
  app.reset();

  LOG_INFO("monitor wallet ut end");
}

TEST_F(MonitorTest, monitor_cross) {
  LOG_INFO("monitor wallet ut start");

  auto app = std::make_shared<application::App>();
  app->pipeline()->Init();

  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, app);
  monitor::Monitor::Instance().Init();
  monitor::Monitor::Instance().Start("localhost:19091");

  // GetCrossLatencyHistogram参数1字符串,必须是字母开头,不能数字开头
  auto cross_filter_l = monitor::Monitor::Instance().GetCrossLatencyHistogram("cross", "6", "6");
  if (cross_filter_l != nullptr) {
    cross_filter_l->Observe(static_cast<double>(1));
  }

  //  auto replace_order_tps_histogram = monitor::Monitor::Instance().GetHistogram("tps", "options", "replace_order");
  //  replace_order_tps_histogram->Observe(5);

  monitor::Monitor::Instance().Stop();
  monitor::Monitor::Instance().Fini();

  bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
  app.reset();

  LOG_INFO("monitor wallet ut end");
}

TEST_F(MonitorTest, monitor_limit_frequency) {
  LOG_INFO("monitor limit frequency ut start");

  auto app = std::make_shared<application::App>();
  app->pipeline()->Init();

  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, app);
  monitor::Monitor::Instance().Init();
  monitor::Monitor::Instance().Start("localhost:19092");

  auto counter = monitor::Monitor::Instance().GetLFTimeoutCounter("LimitFrequencyTimerUnexpected", std::to_string(1));
  counter->Increment();

  auto histogram_pending_queue =
      monitor::Monitor::Instance().GetLFPendingHistogram("LimitFrequencyPendingSize", std::to_string(0), "BTCUSDT");
  histogram_pending_queue->Observe(static_cast<double>(1));

  auto histogram_mark_set =
      monitor::Monitor::Instance().GetLFMarkSetHistogram("LimitFrequencyUidMarkSet", std::to_string(0), "BTCUSDT");
  histogram_mark_set->Observe(static_cast<double>(1));

  monitor::Monitor::Instance().Stop();
  monitor::Monitor::Instance().Fini();

  bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
  app.reset();

  LOG_INFO("monitor wallet ut end");
}

int main(int argc, char* argv[]) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  auto client = std::make_shared<bbase::nacos_client::NacosClientImpl>("FUTURE-CONFIG", "uta", "");
  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::ObjectType::kObjectConfigCenter, client);
  // cache kv dataids
  config::ConfigProxy::Instance().InitConfigCenter();

  testing::InitGoogleTest(&argc, argv);
  testing::InitGoogleMock(&argc, argv);

  std::int32_t ret = RUN_ALL_TESTS();

  bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::ObjectType::kObjectConfigCenter);
  config::ConfigProxy::Instance().Fini();
  bbase::hdts::Hdts::Fini();

  return ret;
}
