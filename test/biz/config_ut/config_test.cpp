#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <bbase/common/bkafka/bkafka.hpp>
#include <bbase/common/blogger/blogger.hpp>
#include <bbase/common/decimal/decimal.hpp>
#include <bbase/common/hdts/hdts.hpp>
#include <bbase/common/nacos_client/nacos_client_impl.hpp>
#include <bbase/common/object_manager/object_manager.hpp>

#include "src/application/app.hpp"
#include "src/application/application.hpp"
#include "src/application/global_config.hpp"
#include "src/application/global_var_manager.hpp"
#include "src/config/config_proxy.hpp"
#include "src/config/data_type.hpp"
#include "src/data/event/biz_event.hpp"
#include "src/data/event/event.hpp"
#include "src/monitor/monitor.hpp"

class ConfigTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::ObjectType::kObjectKafka,
                                                         std::make_shared<bbase::bkafka::Bkafka>());
    application::GlobalVarManager::Instance().set_group_name("g1");
  }
  static void TearDownTestSuite() {
    config::ConfigProxy::Instance().Fini();
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::ObjectType::kObjectKafka);
  }
  static void waitRecvHandler() {
    auto& io_service = config::ConfigProxy::Instance().io_service_;
    bool io_service_arrived = false;
    int i = 0;
    io_service.post([&] { io_service_arrived = true; });
    usleep(1000);
    while (++i < 1000) {
      if (io_service_arrived) {
        break;
      }
      usleep(10000);
    }
    if (i >= 1000) {
      LOG_ERROR("io service timeout, recv handler may not take effect");
    }
  }

  static constexpr const char* kUtaGroupId = "uta";
  static constexpr const char* kEmptyContent = "";
  static constexpr const char* kInvalidContent =
      "{\"data\":\"InvalidContent\",\"version\":\"b54eeafb-0a91-4d16-aa74-442d2c3e104f\"}";
  static constexpr const char* kClientConfigContent =
      "{\"data\":{\"feeMidSwitch\":true,\"ompGrpcHost\":\"***********\",\"ompGrpcPort\":9090,\"vipConfigSwitch\":false,"
      "\"ackGrpcSwitch\":false,\"defaultBrokerId\":9001,\"defaultEtpRiskPotionLimit\":20000,\"shardNum\":1},"
      "\"version\":\"1\"}";
  static constexpr const char* kClientConfigContentNew =
      "{\"data\":{\"feeMidSwitch\":true,\"ompGrpcHost\":\"***********\",\"ompGrpcPort\":9090,\"vipConfigSwitch\":false,"
      "\"ackGrpcSwitch\":false,\"defaultBrokerId\":9001,\"defaultEtpRiskPotionLimit\":20000,\"shardNum\":1},"
      "\"version\":\"1\"}";
  static constexpr const char* kClientConfigContentFake =
      "{\"data\":{\"feeMidSwitch\":true,\"ompGrpcHost\":\"**********\",\"ompGrpcPort\":9090,\"vipConfigSwitch\":false,"
      "\"ackGrpcSwitch\":false,\"defaultBrokerId\":9001,\"defaultEtpRiskPotionLimit\":20000,\"shardNum\":1},"
      "\"version\":\"1\"}";
  static constexpr const char* kClientConfigContentEmptyHost =
      "{\"data\":{\"feeMidSwitch\":true,\"ompGrpcHost\":\"\",\"ompGrpcPort\":9090,\"vipConfigSwitch\":false,"
      "\"ackGrpcSwitch\":false,\"defaultBrokerId\":9001,\"defaultEtpRiskPotionLimit\":20000,\"shardNum\":1},"
      "\"version\":\"1\"}";
  static constexpr const char* kMpPrecisionContent =
      "{\"data\":{\"BTC\":{\"orderSize\":\"4\",\"fee\":\"8\",\"delta\":\"8\",\"positionSize\":\"4\",\"theta\":\"8\","
      "\"feeRate\":\"8\",\"equity\":\"12\",\"PNL\":\"4\",\"RPL\":\"8\",\"EDP\":\"8\",\"markPrice\":\"8\","
      "\"markPriceIV\":\"4\",\"MB\":\"12\",\"positionMM\":\"8\",\"orderPrice\":\"8\",\"positionIM\":\"8\",\"baseCoin\":"
      "\"BTC\",\"bidAskPrice\":\"8\",\"CB\":\"8\",\"AB\":\"12\",\"funding\":\"8\",\"indexPrice\":\"8\",\"bidAskIV\":"
      "\"4\",\"optionValue\":\"12\",\"p24Change\":\"8\",\"positionAvgPrice\":\"8\",\"underlying\":\"8\",\"ROI\":\"4\","
      "\"sessionRPL\":\"8\",\"sessionUPL\":\"8\",\"orderIV\":\"4\",\"p24Low\":\"8\",\"orderIM\":\"8\","
      "\"sessionAvgPrice\":\"8\",\"gamma\":\"8\",\"vega\":\"8\",\"lastPrice\":\"8\",\"p24High\":\"8\"}},\"version\":"
      "\"1\"}";
  static constexpr const char* kMpRLPContent =
      R"({"data":[{"uids":[12345678,1002],"symbolIds":[1,279] }, {"uids":[1003,1004],"symbolIds": [279,278] }]})";
  static constexpr const char* kRLPWhiteListContent = R"({"uids":[11050842, 22236272]})";

  static constexpr const char* kMpFutureRLPContent =
      R"({"data":[{"uids":[12345678,1002],"symbolIds":[5,45] }, {"uids":[1003,1004],"symbolIds": [640] }]})";
  static constexpr const char* kSoftTakerWhiteList =
      R"({"data":{"whitelistUids":[{"instId":"BRK001","parentUids":[6,7],"escrowUids":[]}],"apiBrokers":[{"brokerId":"Vq000495","brokerName": "eazybot"}]}})";

  static constexpr const char* kOptionalGlobalContent = R"({"data":{"STRIKE":{"changeReason":"123","otpCode":"123",
   "gears":"4,45","defaultGear":"45"},"WARNLINE":{"liquidationTarget":"0.9","minOrderSizeIncrement":"0.02",
   "priceDeviationPercentage":"0.02","uniRmTakeoverTrigger":"1.6","usdcRmTakeoverTrigger":"1.6","maxOrderSize":"0.03",
   "uniPmTakeoverTrigger":"1.5","imWarnLine":"0.82","mmWarnLine":"0.8","usdcPmTakeoverTrigger":"1.6","maxOrderPrice":"0.02"}},
   "version":"669216e7-6b89-46a8-bee6-d582e725e9e2"})";
  static constexpr const char* kPmContent =
      R"({"data":{"BTC":{"priceRangeInterval":"0.013","volatilityRangeUp":"0.5","optionInterestRate":"0","netShortOptionsFactor":"0.1",
      "usdtUsdcFactor":"0.15","deltaContingencyFactor":"0.1","volatilityRangeDown":"0.5","vegaContingencyFactor":"0.1","initialMarginFactor":"2","baseCoin":"BTC"},
      "SOL":{"priceRangeInterval":"0.001","volatilityRangeUp":"0.001","optionInterestRate":"0.001","netShortOptionsFactor":"0.1",
      "usdtUsdcFactor":"0.1","volatilityRangeDown":"0.001","deltaContingencyFactor":"0.1","vegaContingencyFactor":"0.1","initialMarginFactor":"1","baseCoin":"SOL"}},
      "version":"7287fbf7-21ea-4e73-9295-84c8ea96d4d5"})";

  static constexpr const char* kTradeContent =
      R"({"data":{"BTC":{"orderPriceFromLastPrice":"0.03","liquidationFeeRate":"0.0035","bidAskMinIv":"0",
      "maxProportionOfDeliveryInOptionValue":"0.125","insuranceFeeRate":"0.000175","tickSize":"0.5","deviationOfMarkIV":"0.02",
      "entryId":"47","markMinIv":"0.1","bidAskMaxIv":"7.5","makerFee":"0.00025","minSellBasis":"0.0005","minImFactor":"0.1",
      "markMaxIv":"4.5","liquidationFeeRatePerp":"0.0006","baseCoin":"BTC","maxBuyFactor":"10","minSellFactor":"10",
      "liquidationTarget":"0.1","stepLiqSheet":"0-20:5;20-100:10;100-200:20;200-:1000","takerFee":"0.00073","minOrderSize":"0.01",
      "liquidationFeeRateFutures":"9.998","minOrderSizeIncrement":"0.01","liquidationFeeRateOption":"9.999","orderPriceFromMarkPrice":"0.1",
      "priceDeviationPercentage":"99.99","maxImFactor":"0.15","maxProportionOfTransactionInOrderPrice":"0.1251","mmFactor":"0.03",
      "minOrderPrice":"0.5","basicDeliveryFeeRate":"0.00015","maxOrderSize":"50000","takeoverTrigger":"1.6","stepLiqMaxOrderSize":"5",
      "maxOrderPrice":"10000000"}},"version":"32798883-f752-4222-b171-71a8ff43531f"})";
  static constexpr const char* kTradeUSDTContent =
      R"({"data":{"DEFAULT_COIN":{"liquidationFeeRateUsdt":"0.0006","insuranceFeeRateUsdt":"0.000513"}},
      "version":"437eebfe-91dd-4e03-a173-6a215d2d0863"})";
  static constexpr const char* kAcctLimitContent =
      "{\"data\":{\"BTC\":{\"SMALL_INSTITUTION\":{\"maxTotalAmountOptionOrders\":\"100\",\"maxValueFutureOrders\":"
      "\"500000\",\"maxOpenOrders\":\"20\",\"maxOpenOptionOrders\":\"50\",\"maxNetShortOptionPositionSize\":\"1000\","
      "\"maxValueFuturePosition\":\"5000000\",\"maxNumberOfContractsHeldForOneInstrument\":\"5000\",\"aidWhiteList\":"
      "\"19481899\",\"whiteList\":\"7111221\",\"subType\":\"SMALL_INSTITUTION\",\"baseCoin\":\"BTC\","
      "\"maxOpenFutureOrders\":\"20\"},\"USER_DEFAULT\":{\"maxValueFutureOrders\":\"500000\","
      "\"maxTotalAmountOptionOrders\":\"50\",\"maxOpenOrders\":\"20\",\"maxOpenOptionOrders\":\"20\","
      "\"maxNetShortOptionPositionSize\":\"100\",\"maxValueFuturePosition\":\"5000000\","
      "\"maxNumberOfContractsHeldForOneInstrument\":\"1000\",\"subType\":\"USER_DEFAULT\",\"maxOpenFutureOrders\":"
      "\"20\",\"baseCoin\":\"BTC\"},\"MARKET_MAKER\":{\"maxTotalAmountOptionOrders\":\"8000\",\"maxValueFutureOrders\":"
      "\"5000000\",\"maxOpenOrders\":\"5000\",\"maxOpenOptionOrders\":\"5000\",\"maxNetShortOptionPositionSize\":"
      "\"1000\",\"maxValueFuturePosition\":\"50000000\",\"maxNumberOfContractsHeldForOneInstrument\":\"5000\","
      "\"aidWhiteList\":\"\",\"whiteList\":\"\",\"subType\":\"MARKET_MAKER\",\"baseCoin\":\"BTC\","
      "\"maxOpenFutureOrders\":\"5000\"}}},\"version\":\"0e10c577-e944-49e3-bf01-bebbd7207eb6\"}";
  static constexpr const char* kOptionSymbolContent =
      "{\"data\":[{\"assetType\":\"OPTION\",\"baseCoin\":\"BTC\",\"coinpairId\":1,\"contractSize\":1,\"contractType\":"
      "\"LinearOption\",\"crossId\":10005,\"deliveryTime\":1841937600,\"expiry\":\"CURRENT_WEEK\",\"id\":102311,"
      "\"multiplier\":1,\"onlineTime\":1640687236,\"quoteCoin\":\"USD\",\"settleCoin\":\"USDC\",\"standard\":\"U\","
      "\"status\":\"OFFLINE\",\"strikePrice\":40000.000000002,\"symbolName\":\"BTC-31DEC21-40000-C\",\"symbolType\":"
      "\"C\"}, {\"assetType\":\"OPTION\",\"baseCoin\":\"BTC\",\"coinpairId\":1,\"contractSize\":1,\"contractType\":"
      "\"LinearOption\",\"crossId\":10005,\"deliveryTime\":1841937600,\"expiry\":\"CURRENT_WEEK\",\"id\":102311,"
      "\"multiplier\":1,\"onlineTime\":1640687236,\"quoteCoin\":\"USD\",\"settleCoin\":\"USDC\",\"standard\":\"U\","
      "\"status\":\"ONLINE\",\"strikePrice\":40000.000000002,\"symbolName\":\"BTC-31DEC21-40000-C\",\"symbolType\":"
      "\"C\"}, {\"assetType\":\"OPTION\",\"baseCoin\":\"BTC\",\"coinpairId\":1,\"contractSize\":1,\"contractType\":"
      "\"LinearOption\",\"crossId\":10005,\"deliveryTime\":1841937600,\"expiry\":\"THREE_DAY\",\"id\":102313,"
      "\"multiplier\":1,\"onlineTime\":1640687236,\"quoteCoin\":\"USD\",\"settleCoin\":\"USDC\",\"standard\":\"U\","
      "\"status\":\"ONLINE\",\"strikePrice\":40000.000000002,\"symbolName\":\"BTC-31DEC21-40000-D\",\"symbolType\":"
      "\"C\"}],\"version\":\"aa6d74a6-77af-4409-b09c-fe902f72683a\"}";
  static constexpr const char* kDeliveryTimeContent =
      "{\"data\":[{\"baseCoin\":\"BTC\",\"deliveryTime\":1655020800,\"quoteCoin\":\"USD\",\"settleCoin\":\"USDC\"},{"
      "\"baseCoin\":\"BTC\",\"deliveryTime\":1655107200,\"quoteCoin\":\"USD\",\"settleCoin\":\"USDC\"}],\"version\":"
      "\"e3cc39a5-07fd-47d8-9a38-bdd1dcdb3dd9\"}";
  static constexpr const char* kUmGeneralConfigContent =
      "{\"data\":{\"creditUtilizationRate\":0.85,\"defaultLever\":5,\"maxLever\":5,\"mmRateThreshold\":0.8,"
      "\"settleRate\":0.02,\"supportLever\":[2,3,4,5]},\"version\":\"d9c0febc-99f5-4e1a-8b1d-260b01282868\"}";
  static constexpr const char* kUmCoinContent =
      R"({"data":{"buyCoin":["USDC","USDT"],"sellCoin":["BTC", "ETH", "USDC", "USDT", "BIT", "SOL", "XRP"]},
      "version":"6f3ed28b-2619-4c4e-abde-2d30813390a5"})";
  static constexpr const char* kUmCoinConfigContent =
      "{\"data\":[{\"baseCoin\":\"SELENA14\",\"collateralValueRatio\":1,\"shortSpotImRate\":1,\"shortSpotMmRate\":1,"
      "\"uniMarginBalance\":1},{\"baseCoin\":\"BTC\",\"collateralValueRatio\":0.994,\"creditLimit\":0.0001,"
      "\"hourlyBorrowRate\":1E-12,\"interestFreeCreditAmount\":2,\"shortSpotImRate\":0.0001,\"shortSpotMmRate\":1E-8,"
      "\"uniMarginBalance\":999998}],\"version\":\"3a1baf36-46db-4ec0-8bb6-cbd3fafc5c2a\"}";
  static constexpr const char* kExchangeRateContent =
      "{\"data\":[{\"buyCoin\":\"USDT\",\"orderSizeMax\":5,\"orderSizeMin\":0,\"priceScalar\":0.9969,\"sellCoin\":"
      "\"BTC\"},{\"buyCoin\":\"USDT\",\"orderSizeMax\":10,\"orderSizeMin\":5,\"priceScalar\":0.9969,\"sellCoin\":"
      "\"BTC\"},{\"buyCoin\":\"USDT\",\"orderSizeMax\":15,\"orderSizeMin\":10,\"priceScalar\":0.9969,\"sellCoin\":"
      "\"BTC\"},{\"buyCoin\":\"USDT\",\"orderSizeMax\":20,\"orderSizeMin\":15,\"priceScalar\":0.9969,\"sellCoin\":"
      "\"BTC\"}],\"version\":\"3b718b1c-3108-435e-9cf1-bc572e1fdbb9\"}";
  static constexpr const char* kCoinPairContent =
      "{\"data\":[{\"quoteCoin\":\"USD\",\"baseCoin\":\"BTC\",\"settleCoin\":\"USDC\",\"contractSize\":1,"
      "\"multiplier\":1}],\"version\":\"1\"}";
  static constexpr const char* kUserZoneContent =
      "{\"data\":[{\"gmtCreate\":\"2022-02-16T08:03:13.385\",\"gmtCreator\":\"15\",\"gmtModified\":\"2022-02-16T08:03:"
      "13.385\",\"gmtModifier\":\"15\",\"id\":1,\"isDeleted\":\"N\",\"uid\":\"13774489\",\"zoneId\":\"27\","
      "\"zoneName\":\"RZVIP00\",\"zoneType\":\"VIP\"}],\"version\":\"dbddf475-16fd-4a5b-8b2a-db076f85fca1\"}";
  static constexpr const char* kRiskInstContent =
      "{\"data\":[{\"gmtCreate\":\"2022-03-08T07:52:10.305\",\"gmtCreator\":\"bill.wang\",\"gmtModified\":\"2022-03-"
      "08T07:52:11.305\",\"gmtModifier\":\"bill.wang\",\"id\":1,\"instId\":\"13774489\",\"instName\":\"Options "
      "Internalgrey\",\"liquidationDelayInfo\":{\"liquidationDelaySwitch\":\"OFF\"},\"marginTrialSwitch\":\"ON\","
      "\"status\":\"ACTIVE\",\"uid\":\"13774489\"}],\"version\":\"dbe946b4-9102-496b-a5b8-f084dafc6ac2\"}";
  static constexpr const char* kInstUserRelContent =
      "{\"data\":[{\"gmtCreate\":\"2022-02-16T07:39:10.773\",\"gmtCreator\":\"15\",\"gmtModified\":\"2022-02-16T07:39:"
      "10.773\",\"gmtModifier\":\"15\",\"id\":1,\"instId\":\"13774489\",\"instName\":\"Options "
      "Internalgrey\",\"isDeleted\":\"N\",\"uid\":\"13774489\",\"usdcAid\":\"17624981\"}],\"version\":\"a542bc6f-4ea0-"
      "4cba-a5ca-60e511369e47\"}";
  static constexpr const char* kInstConfigContent =
      "{\"data\":[{\"acctCoinTypes\":\"[\\\"usdc\\\"]\",\"acctParamType\":\"大型做市商\",\"apiQuota\":5000,"
      "\"dcpStatus\":\"OFF\",\"gmtCreate\":\"2022-02-16T09:14:08.227\",\"gmtCreator\":\"15\",\"gmtModified\":\"2022-05-"
      "17T08:09:21.125\",\"gmtModifier\":\"15\",\"id\":1,\"instId\":\"13774489\",\"instLevel\":\"Premium\","
      "\"instName\":\"Options Internalgrey\",\"instType\":\"Market "
      "Maker\",\"isDeleted\":\"N\",\"status\":\"ACTIVE\"}],\"version\":\"7f8087ff-be8c-42dc-be6a-5474e8a68ff0\"}";
  static constexpr const char* kInstUserConfigContent =
      R"({"data":[{"acctCoinTypes":"[\"usdc\"]","apiQuota":400,"dcpStatus":"ON","gmtCreate":"2021-12-14T13:20:54.753",
      "gmtCreator":"100034","gmtModified":"2021-12-14T13:20:54.753","gmtModifier":"100034","id":7,"instId":"461637",
      "instName":"cyber","isDeleted":"N","status":"ACTIVE","uid":"7817372"},{"acctCoinTypes":"[\"usdc\"]","apiQuota":40000,
      "dcpStatus":"ON","gmtCreate":"2021-12-15T06:16:44.165","gmtCreator":"100034","gmtModified":"2021-12-22T03:54:02.720",
      "gmtModifier":"100034","id":8,"instId":"461637","instName":"cyber","isDeleted":"N","status":"ACTIVE","uid":"7798738"}],"version":"1"})";

  // ?? decimal 0.019820/0.00015 will crash in mac
  // ??
  static constexpr const char* kInstFeeContent =
      R"({"data":[{"assetType":"OPTION","baseCoin":"BTC","coinpairId":"1","gmtCreate":"2021-12-02T13:11:31.934",
      "gmtCreator":"100034","gmtModified":"2021-12-02T13:11:31.934","gmtModifier":"100035","id":70,"isDeleted":"N",
      "makerFee":0.010001,"ownerId":"461712","ownerType":"USER","takerFee":0.010002},{"assetType":"OPTION","baseCoin":"BTC",
      "coinpairId":"1","gmtCreate":"2021-12-07T09:55:57.622","gmtCreator":"100034","gmtModified":"2021-12-07T09:55:57.622",
      "gmtModifier":"100034","id":227,"isDeleted":"N","makerFee":0.000100,"ownerId":"7805539","ownerType":"USER","takerFee":0.000101}],
      "version":"153f04c2-cec7-4fb6-afac-a17b96cfb6bc"})";
  static constexpr const char* kInstUserFeeContent =
      "{\"data\":{\"25984454_OPTION_BTC\":{\"assetType\":\"OPTION\",\"baseCoin\":\"BTC\",\"coinpairId\":\"1\","
      "\"makerFee\":0.000001,\"ownerId\":\"15961849\",\"ownerType\":\"INST\",\"takerFee\":0.000002}},\"version\":"
      "\"6fa034bb-2b14-4fa7-b9c0-197839974b54\"}";
  static constexpr const char* kInstKpiConfigContent =
      "{\"data\":[{\"baseCoin\":\"BTC\",\"instLevel\":\"Premium\",\"instType\":\"Market "
      "Maker\",\"instTypeId\":1,\"kpiParams\":\"{\\\"spread\\\":true,\\\"spreadDeltaMax\\\":0.01,"
      "\\\"spreadDeltaFactor\\\":0.02,\\\"spreadKpi1\\\":true,\\\"spreadKpi1ExpireLessThanDays\\\":2,"
      "\\\"spreadKpi1ExpireOverDays\\\":60,\\\"spreadKpi1TimesOfRequiredMaxSpread\\\":1.5,\\\"spreadKpi2\\\":true,"
      "\\\"spreadKpi2DaysAfterIntroduction\\\":2,\\\"spreadKpi2TimesOfRequiredMaxSpread\\\":1.5,\\\"spreadKpi3\\\":"
      "true,\\\"spreadKpi3TimesOfRequiredMaxSpread\\\":3,\\\"quotingSize\\\":true,\\\"quotingSizeForEachStrike\\\":"
      "true,\\\"minQuotingSizeForEachStrike\\\":25,\\\"maturityCoverageFlag\\\":true,\\\"maturityCoverage\\\":"
      "\\\"CURRENT_WEEK,NEXT_WEEK,CURRENT_MONTH,NEXT_MONTH,CURRENT_QUARTER\\\",\\\"strikeCoverage\\\":true,"
      "\\\"strikeCoveragePercent\\\":80,\\\"strikeCoverageDeltaMin\\\":0.1,\\\"strikeCoverageDeltaMax\\\":0.9,"
      "\\\"upTime\\\":true,\\\"upTimePercent\\\":90,\\\"r1\\\":true,\\\"r2\\\":true,\\\"r3\\\":true,\\\"r4\\\":true,"
      "\\\"r5\\\":true,\\\"r6\\\":true,\\\"r7\\\":true,\\\"r8\\\":true,\\\"r9\\\":true,\\\"r10\\\":true}\"}],"
      "\"version\":\"1\"}";
  static constexpr const char* kMmProtectContent =
      "{\"data\":{\"BTC_21826590\":{\"baseCoin\":\"BTC\",\"coinpairId\":\"1\",\"configurable\":\"Y\",\"deltaLimit\":"
      "\"100\",\"frozenPeriodMs\":100,\"gmtCreate\":\"2022-05-11T07:23:58.728\",\"gmtCreator\":\"SYSTEM\","
      "\"gmtModified\":\"2022-05-11T07:59:15.998\",\"gmtModifier\":\"SYSTEM\",\"id\":54,\"instId\":\"21541143\","
      "\"instName\":\"Babel\",\"isDeleted\":\"N\",\"qtyLimit\":\"100\",\"status\":\"ON\",\"uid\":\"21826590\","
      "\"windowMs\":5000}},\"version\":\"efad9a9d-8bc6-4340-a1a4-bcaf1f9c8ce2\"}";
  static constexpr const char* kCoinInfoContent =
      R"({"data":[{"accuracyLength":8,"bizType":1,"brokerId":2,"chain":"BTC","coin":"BTC","coinId":3,"coinLabel":4,"coinPlate":5,"type":6},
      {"accuracyLength":8,"bizType":1,"brokerId":0,"chain":"ETH,ARBI,BSC,ZKSYNC,AURORA,OP","coin":"ETH","coinId":2,"coinLabel":2,"coinPlate":1,"type":1},
      {"accuracyLength":8,"bizType":1,"brokerId":2,"chain":"USDC","coin":"USDC","coinId":16,"coinLabel":2,"coinPlate":1,"type":6}],
      "version":"197cddb8-1ea9-4e74-9d1f-f727abbc0a1b"})";
  static constexpr const char* kCoinInfoContentTmp =
      R"({"data":[{"accuracyLength":8,"bizType":1,"brokerId":2,"chain":"BTC","coin":"BTC","coinId":3,"coinLabel":4,"coinPlate":6,"type":7},
      {"accuracyLength":8,"bizType":1,"brokerId":0,"chain":"ETH,ARBI,BSC,ZKSYNC,AURORA,OP","coin":"ETH","coinId":2,"coinLabel":2,"coinPlate":1,"type":1},
      {"accuracyLength":8,"bizType":1,"brokerId":2,"chain":"USDC","coin":"USDC","coinId":16,"coinLabel":2,"coinPlate":1,"type":6}],
      "version":"197cddb8-1ea9-4e74-9d1f-f727abbc0a1b"})";
  static constexpr const char* kMarginCoinCfgContent =
      R"({"data":[{"clearingOrder":1,"coin":"USDT","coinId":5,"isLoan":"Y","isMargin":"N","marginValueRate":0.990000000000000000,
      "shortSpotImRate":0.211100000000000000,"shortSpotMmRate":0.410000000000000000,"status":1},
      {"clearingOrder":4,"coin":"USDT","coinId":1,"isLoan":"N","isMargin":"Y","marginValueRate":0.990000000000000000,
      "shortSpotImRate":0.211100000000000000,"shortSpotMmRate":0.410000000000000000,"status":1},
      {"clearingOrder":3,"coin":"BTC","coinId":1,"isLoan":"Y","isMargin":"Y","marginValueRate":1.000000000000000000,
      "shortSpotImRate":0.110000000000000000,"shortSpotMmRate":0.220000000000000000,"status":1}],
      "version":"c65f2f5f-88e1-4c4e-90cb-57c0ba8ea9c4"})";
  static constexpr const char* kSpotSymbolContent =
      R"({"data":[{"allowBargain":0,"allowPlan":1,"allowTrade":1,"banBuyStatus":0,"banSellStatus":0,"baseCoin":"BTC","baseCoinId":1,
    "baseCoinName":"BTC","baseCoinType":1,"basePrecision":3,"brokerId":9001,"crossId":22009,"direction":"","etpNavDeviation":"0","etpRiskSwitchStatus":0,
    "exchangeSymbolId":10,"forbidOpenapiTrade":0,"inleverage":0E-18,"isTest":0,"limitOrderPriceLimitPercentage":"10","limitY":"15","limitStatus":1,"makerBuyFee":"0.00100000",
    "makerSellFee":"0.00100000","marginLoanOpen":1,"marketOrderPriceLimitPercentage":"10","minPricePrecision":"0.001000000000000000","needPreviewCheck":0,
    "openPrice":3500.000000000000000000,"openTime":1679973300000,"settleCoin":"USDT","settleCoinId":5,"settleCoinName":"USDT","settleCoinType":1,
    "settlePrecision":8,"showStatus":1,"showStatusOpenTime":1679973300000,"status":3,"symbolFullName":"BTCUSDT","symbolId":10,"symbolName":"BTCUSDT",
    "symbolType":1,"takerBuyFee":"0.00100000","takerSellFee":"0.00100000", "maxTradeQuantity":"100", "minTradeQuantity":"0.00001", "maxTradeAmount":"1000000",
    "minTradeAmount":"0.00001"},  {"allowBargain":1,"allowPlan":0,"allowTrade":0,"banBuyStatus":1,"banSellStatus":1,"baseCoin":"SPOTTEST3","baseCoinId":162,
    "baseCoinName":"SPOTTEST3","baseCoinType":1,"basePrecision":7,"brokerId":9001,"crossId":20004,"direction":"","etpNavDeviation":"","exchangeSymbolId":275,
    "forbidOpenapiTrade":1,"inleverage":0E-18,"isTest":0,"kycRule":"","limitOrderPriceLimitPercentage":"0","limitY":"15","limitStatus":0,"makerBuyFee":"0.00100000","makerSellFee":"0.00100000",
    "marginLoanOpen":0,"marketOrderPriceLimitPercentage":"0","maxTradeAmount":"10000000.000000000000000000","maxTradeQuantity":"10000000.000000000000000000",
    "minPricePrecision":"0.000000100000000000","minTradeAmount":"1.00000000000E-7","minTradeQuantity":"1.00000000000E-7","needPreviewCheck":0,"onlineTime":0,
    "openPrice":1.000000000000000000,"openTime":0,"settleCoin":"SPOTTEST1","settleCoinId":160,"settleCoinName":"SPOTTEST1","settleCoinType":1,"settlePrecision":7,
    "showStatus":0,"showStatusOpenTime":0,"status":1,"symbolFullName":"SPOTTEST3SPOTTEST1","symbolId":278,"symbolName":"SPOTTEST3SPOTTEST1","symbolType":1,
    "takerBuyFee":"0.00100000","takerSellFee":"0.00100000","showPlatforms":[]},{"allowBargain":0,"allowPlan":1,"allowTrade":1,"banBuyStatus":0,"banSellStatus":0,"baseCoin":"BTG",
    "baseCoinId":174,"baseCoinName":"BTG","baseCoinType":1,"basePrecision":2,"brokerId":9001,"crossId":20001,"direction":"","etpNavDeviation":"","exchangeSymbolId":276,
    "forbidOpenapiTrade":0,"inleverage":0E-18,"isTest":1,"kycRule":"","limitOrderPriceLimitPercentage":"0","limitY":"15","limitStatus":0,"makerBuyFee":"0.00100000","makerSellFee":"0.00100000",
    "marginLoanOpen":0,"marketOrderPriceLimitPercentage":"0","maxTradeAmount":"10000.000000000000000000","maxTradeQuantity":"10.000000000000000000",
    "minPricePrecision":"0.010000000000000000","minTradeAmount":"0.100000000000000000","minTradeQuantity":"0.010000000000000000","needPreviewCheck":0,"onlineTime":0,
    "openPrice":1.000000000000000000,"openTime":1657008631000,"settleCoin":"ETH","settleCoinId":2,"settleCoinName":"ETH","settleCoinType":1,"settlePrecision":4,
    "showStatus":1,"showStatusOpenTime":1657013273351,"status":3,"symbolFullName":"BTGETH","symbolId":279,"symbolName":"BTGETH","symbolType":1,
    "takerBuyFee":"0.00100000","takerSellFee":"0.00100000","showPlatforms":["Bybit","TUR"]},{"allowBargain":0,"allowPlan":1,"allowTrade":1,"banBuyStatus":0,"banSellStatus":0,"baseCoin":"BTC","baseCoinId":1,
    "baseCoinName":"BTC","baseCoinType":1,"basePrecision":3,"brokerId":9001,"crossId":22009,"direction":"","etpNavDeviation":"0","etpRiskSwitchStatus":0,
    "exchangeSymbolId":10,"forbidOpenapiTrade":0,"inleverage":0E-18,"isTest":0,"limitOrderPriceLimitPercentage":"10","limitY":"15","limitStatus":1,"makerBuyFee":"0.00100000",
    "makerSellFee":"0.00100000","marginLoanOpen":1,"marketOrderPriceLimitPercentage":"10","minPricePrecision":"0.001000000000000000","needPreviewCheck":0,
    "openPrice":3500.000000000000000000,"openTime":1679973300000,"settleCoin":"USDC","settleCoinId":6,"settleCoinName":"USDC","settleCoinType":1,
    "settlePrecision":8,"showStatus":1,"showStatusOpenTime":1679973300000,"status":3,"symbolFullName":"BTCUSDC","symbolId":10,"symbolName":"BTCUSDC",
    "symbolType":1,"takerBuyFee":"0.00100000","takerSellFee":"0.00100000", "maxTradeQuantity":"100", "minTradeQuantity":"0.00001", "maxTradeAmount":"1000000",
    "minTradeAmount":"0.00001"}],"version":"f56ff996-a578-4f2f-9050-95f1b59465cf"})";
  static constexpr const char* kSpotLoanSymbolContent =
      R"({"data":[{"brokerId":9001,"status":1,"symbolId":227,"symbolName":"ADAUSDT"},
      {"brokerId":9001,"status":1,"symbolId":222,"symbolName":"USDCUSDT"}],
      "version":"f642026f-7658-4c8c-a212-7ba89f30ed73"})";
  static constexpr const char* kTrusteeContent =
      R"({"data":[{"uid":4224048434,"aid":4224048436,"label":"AIO_SYSTEM_TO_USER_PM"},
      {"uid":4224048430,"aid":4224048432,"label":"AIO_SYSTEM_TO_USER_RM"},
      {"uid":4229696,"aid":4229763,"label":"RM-BAN_LIQUIDATION"}],
      "version":"d33778aa-a222-42f4-94f1-eacd836a5552"})";
  static constexpr const char* kMpSpecialUserContent =
      R"({"data":{"3799951":{"PM_LIMIT":{"pmMaintenanceEquity":"3005","pmInitialEquity":"41"}},
      "3799717":{"PM_LIMIT":{"pmMaintenanceEquity":"200","pmInitialEquity":"200"}}},"version":"1"})";
  static constexpr const char* kCoinRiskPosLimitContent =
      R"({"data":[{"coin":"CGG","positionLimit":1230},
      {"coin":"WWY","positionLimit":-1},
      {"coin":"SOLO","positionLimit":20000.000000000000000000},
      {"coin":"ETHF","positionLimit":30000.000000000000000000},
      {"coin":"XRP","positionLimit":5000.000000000000000000},
      {"coin":"EOS","positionLimit":-1.000000000000000000}],
      "version":"a75aab00-f361-4b55-b578-54717bc34as5"})";
  static constexpr const char* kCoinRiskUserPosLimitContent =
      R"({"data":[{"coin":"CGG","positionLimit":111111111,"uid":1},
      {"coin":"CGG","positionLimit":-1,"uid":2},
      {"coin":"XRP","positionLimit":300000.000000000000000000,"uid":123456},
      {"coin":"XRP","positionLimit":100000.000000000000000000,"uid":654321}],
      "version":"f678a2ff-8a6b-441e-9ca2-c173009112fa"})";
  static constexpr const char* kEtpRiskPosLimitContent =
      R"({"data":[{"coin":"USDT","positionLimit":2200.000000000000000000,"symbolName":"ETH3SUSDT"},
      {"coin":"USDT","positionLimit":100.000000000000000000,"symbolName":"EOSUSDT"}],
      "version":"2388dbce-c234-4fc7-ac47-98ec6042ce1a"})";
  static constexpr const char* kEtpRiskUserPosLimitContent =
      R"({"data":[{"coin":"USDT","positionLimit":0E-18,"symbolName":"ETH3SUSDT","uid":4224474},
      {"coin":"USDT","positionLimit":0E-18,"symbolName":"ETH3SUSDT","uid":4224475}],
      "version":"6016df6c-1e50-40f4-9730-7dd9d9298986"})";

  static constexpr const char* kVipUserContent =
      R"({"data":{"100801":1001,"102161":1003},"lastUpdate":0,"version":"1d4c37d1d529f083def4698f064b3e39"})";
  static constexpr const char* kUmLadderContent =
      R"({"data":{"BTC":{"0":{"hourlyBorrowRate":"0.00000114200000000000","interestFreeCreditAmount":"2222","creditLimit":"22223","baseCoin":"BTC"},
      "1003":{"hourlyBorrowRate":"0.00000114200000000000","interestFreeCreditAmount":"30000000","creditLimit":"30000000"}},
      "SOL":{"2001":{"interestFreeCreditAmount":"2000","creditLimit":"5000000"}}},"version":"e8b8e218-7897-4e30-aea4-2f4ab7ee5758"})";

  static constexpr const char* kSpotPlatformConfigContent =
      R"({"data":{"openPriceMultiple":"10","placeLimitOrderLimit":"1","placeMarketOrderLimit":"1","riskTime":"1"},"version":"7c3f04ec-8d37-4ef9-9b3b-6af242e8f2d7"})";
  static constexpr const char* kSpotCountryConfigContent =
      R"([{"alpha3":"AND","alpha2":"AD","name_en":"Andorra","name_zh":"安道尔共和国"},
  {"alpha3":"ARE","alpha2":"AE","name_en":"United Arab Emirates","name_zh":"阿拉伯联合酋长国"}])";
  static constexpr const char* kFastDumpConfigContent = R"({"addr":"***********:8080"})";
  static constexpr const char* kFilterHelperConfigContent = R"({"addr":"***********:8090"})";
  // \"SHIB\":{\"availableBalance\":\"3000\",\"usedBorrowSizeBalance\":\"0\",\"totalDepositBalance\":\"3000\",\"minAvailableBalance\":\"0\"},
  static constexpr const char* kFundPoolBalanceContent =
      R"({"content":"{\"SUSHI\":{\"availableBalance\":\"600\",\"usedBorrowSizeBalance\":\"0\",\"totalDepositBalance\":\"600\",\"minAvailableBalance\":\"0\"},\"QNT\":{\"availableBalance\":\"300\",\"usedBorrowSizeBalance\":\"0\",\"totalDepositBalance\":\"0\",\"minAvailableBalance\":\"100\"}}"})";
  static constexpr const char* kMarginServerProductContent =
      R"({"dataId":"margin-server-product-config","data":{"128":{"leverage":10.00000000, "convertRates":[],"marketType":1},
  "1":{"leverage":0.10000000,"convertRates":[{"token":"USDT,USDC","configs":[{"min":0,"max":99999999999,"rate":1}]},
  {"token":"BTC,ETH,BUSD","configs":[{"min":0,"max":20000000,"rate":1},{"min":20000000,"max":50000000,"rate":0.975},{"min":50000000,"max":100000000,"rate":0.95},
  {"min":100000000,"max":200000000,"rate":0.9},{"min":200000000,"max":300000000,"rate":0.8},{"min":300000000,"max":400000000,"rate":0.7},
  {"min":400000000,"max":800000000,"rate":0.6},{"min":800000000,"max":1500000000,"rate":0.5},{"min":1500000000,"max":3000000000,"rate":0.3},
  {"min":3000000000,"max":99999999999,"rate":0}]}],"marketType":1},
  "129":{"leverage":10.00000000,"convertRates":[],"marketType":1},"2":{"leverage":1.00000000,"convertRates":[],"marketType":2},"130":{"leverage":1.00000000,"convertRates":[],"marketType":1},
  "3":{"leverage":2.00000000,"convertRates":[],"marketType":2},"131":{"leverage":18.00000000,"convertRates":[],"marketType":2},"132":{"leverage":3.00000000,"convertRates":[],"marketType":2},
  "127":{"leverage":11.00000000,"convertRates":[],"marketType":2}},"version":"61851f60e12ac7dd34378bb7a74bbc0f","lastUpdate":1686552073949,"ompGrpcPort":0})";
  static constexpr const char* kMarginServerCommonContent = R"({"dataId":"margin-server-common-config",
  "data":{"marginServerGrpcHost":"margin-server-unify-test-1.test2.efficiency.ww5sawfyut0k.bitsvc.io","clearingTokenOrder":"USDT,BTC,ETH,USDC,XRP,EOS,LTC,ERTHA",
  "marginServerGrpcPort":9090,"ackGrpcSwitch":true},"version":"ff0d4d92e40e40da6a7936805426bdfa","lastUpdate":1686564030773,"ompGrpcPort":0})";
  static constexpr const char* kMosConfigContent =
      R"({"data":[{"bizType":"FUTURE","maxOrderSize":"400.000000","minOrderSize":"100.000000","mosKey":"ETHUSD","uid":"3799717"},
      {"bizType":"OPTIONS","maxOrderSize":"600.000000","minOrderSize":"200.000000","mosKey":"BTC","uid":"3799717"},
      {"bizType":"OPTIONS","maxOrderSize":"1","minOrderSize":"1","mosKey":"SOL","uid":"3799717"},
      {"bizType":"OPTIONS","maxOrderSize":"600000.000000","minOrderSize":"0.120000","mosKey":"ETH","uid":"3800356"},
      {"bizType":"OPTIONS","maxOrderSize":"150000.000000","minOrderSize":"0.020000","mosKey":"ETH","uid":"3799720"},
      {"bizType":"FUTURE","maxOrderSize":"4","minOrderSize":"1","mosKey":"BTCUSD","uid":"3799720"}],"version":"69564186-46ed-466e-9841-54b450bd34d8"})";
  static constexpr const char* kUmUserConfigContent =
      R"({"data":[{"coinLimitMap":{"USDT":10000},"gmtModified":"2023-09-07T03:49:43.710","status":"ACTIVE","uid":"6353623","umCoinWhiteListDefaultModel":"DEFAULT_VIP_LEVEL"},
      {"coinLimitMap":{"USDT":20000,"USDC":10000},"gmtModified":"2023-08-31T04:18:34.065","status":"ACTIVE","uid":"6355032","umCoinWhiteListDefaultModel":"DEFAULT_VIP_LEVEL"}],
      "version":"113886a0-7ee6-4109-a668-376fc6ccaa60"})";

  static constexpr const char* kHedgedUidListConfig = R"(123456,122122,12313)";
  static constexpr const char* kHedgedRatioConfig =
      R"({"coinConfig":[{"hedgePercent":"0.95","coin":"BTC"},{"hedgePercent":"0.9","coin":"ETH"}]})";
  static constexpr const char* kBanTradeConfig =
      R"({"data":[{"symbolId":9,"symbolName":"BTCUSDT","category":"SPOT","side":"BUY","banTradeType":["Market","StopLoss_Market", "Oco_Limit"],"priceRange":"12.9933"}],
      "version":"d6af24bd-f7f9-4e80-996f-5bdfb6b1e475"})";
  static constexpr const char* kBanTradeConfigInvalid =
      R"({"data":[{"symbolId":9,"symbolName":"BTCUSDT","category":"SPOT","side":"SELL","banTradeType":["RLLimit","TakeProfit_Market", "Oco_RLLimit","Oco_RLLimit_3"],"priceRange":"12.9933"},
      {"symbolId":9,"symbolName":"BTCUSDT","category":"SPOT","side":"SELL","banTradeType":"","priceRange":"12.9933"}],
      "version":"d6af24bd-f7f9-4e80-996f-5bdfb6b1e475"})";
  static constexpr const char* kPlatformBanTradeConfig = R"({"allBannedType":"all","isAllTradingBanned":false})";
  static constexpr const char* kInstLoanUidListConfig =
      R"(44073632, 75992198, 77350040, 13828310, 56083281, 16814342, 56830821, 73630827, 2662432, 29403596, 54844233, 56742773,
  26956174, 75988113, 20314403, 16812311, 73630843, 75986078, 76536981, 29403612, 71834710, 77030537, 13824204, 34253333, 75990168, 77030582, 56083318, 16814370, 39328379, 75984036,
  75920544, 29403624, 32217539, 13828346, 26956208, 73630799, 26954167, 75988138, 75992232, 71834745, 6611024, 77030562, 75990194, 13635818, 40843860, 29403646, 73630813, 40841775,
  75920582, 76537039, 75984069, 74023983, 13824148, 75986113, 46674563, 75992268, 44073709, 29403533, 13828253, 77030599, 26945994, 44088050, 75988180, 75990228, 73276426, 73630778,
  22950148, 56083213, 46674579, 75990236, 56550146, 75984091, 26954234, 16814433, 73630721, 13828277, 75986144, 71834656, 6846466, 56083259, 73276467, 74023943, 50985831, 29403567,
  73630739, 36489821, 56742674, 75992308, 26946031, 56083235, 75990259, 39488045, 50985840, 75988221, 6846482, 34253430, 71834677, 13824175, 39510564, 72811561, 77030422, 6469859,
  16814467, 75983875, 75992077, 40841956, 36489916, 75985942, 75987988, 13824069, 77347845, 75990046, 26956033, 44073532, 75983910, 16814499, 75992098, 29403496, 13824122, 75985967,
  12863599, 39328502, 44073487, 77030456, 18307490, 16814519, 43981350, 77030444, 46674547, 18307498, 10035236, 75988026, 75990074, 26956068, 103481066, 20822524, 75992134, 75985988,
  2607328, 13824026, 75983951, 75988047, 44073578, 26956113, 3561723, 16814545, 34253522, 56083341, 39488134, 75990107, 20314601, 46111330, 76536914, 77030518, 75983972, 73630854, 75992162,
  26954109, 13824052, 20314578, 39328436, 71834788, 26956138, 16814577, 75986035, 75988082, 75983998, 75990136, 20502585, 75990404, 26955935, 103481135, 53383718, 34253568, 56083034, 53383715,
  4936005, 53383712, 75986312, 75990423, 75992466, 75984273, 44088251, 75988378, 7450991, 56083063, 53383694, 75992486, 34253608, 53383691, 53383688, 75984303, 75988398, 53383685, 44088204,
  53383682, 56846931, 75990453, 30413027, 53383709, 53383703, 75986366, 53383700, 75984317, 103481109, 53383697, 44088290, 20314231, 40842023, 103618428, 75990486, 26955983, 75992530, 75988433,
  103481213, 76021215, 75986399, 76525012, 40846095, 12868005, 46273441, 23678991, 57575945, 75984352, 20502609, 75990508, 20312152, 75992563, 12859836, 43863013, 56080941, 75984381, 75986428,
  103481175, 103618386, 65919634, 103481258, 71917001, 44088103, 103618469, 20314300, 56550101, 56083163, 75990282, 75984137, 76537089, 75986184, 75992328, 71917021, 74026495, 36490170, 32088191,
  34253715, 20312236, 30412876, 3021232, 75984166, 40846282, 30412917, 75988258, 32088143, 56083184, 56550137, 75992352, 75990315, 56550124, 75986228, 74026461, 75988286, 103481232, 103618449,
  74026454, 75992390, 32088107, 103618543, 16814274, 75984196, 30412823, 44088169, 75990347, 65608231, 77055320, 56550026, 75986258, 77348165, 75992414, 103618551, 102500294, 74026420, 75988313,
  65608246, 103618508, 56083125, 20314326, 44088132, 75984227, 56550072, 16814312, 13834554, 7451035, 3021253, 75986281, 75990376, 7451011, 75992433, 75988350, 16814330, 72811948, 53383471, 39487577,
  102726684, 46180548, 75986563, 16370428, 53383467, 29402054, 75992705, 20173621, 20820789, 76152449, 53383460, 75988621, 34252801, 12868296, 46160077, 53383457, 15559421, 53383485, 102726666, 76152476,
  26954626, 53383479, 20820774, 53383474, 75988635, 75984538, 71917143, 75992729, 102726656, 75990680, 53383438, 75990695, 71429728, 26954687, 53383435, 102726713, 53383432, 29402087, 44073095, 102726709,
  53383428, 49682652, 75986605, 102726704, 53383425, 46127247, 53383454, 23339861, 75992757, 71917178, 53383448, 20314893, 102726694, 75988670, 20820743, 53383444, 39510113, 74026580, 75984570, 76152503,
  53383441, 53383534, 34252873, 53383531, 20036468, 43860187, 53383528, 74026528, 75990734, 22575890, 51497306, 53383525, 51497309, 17550149, 12868235, 76152537, 17605469, 75984598, 40843324, 53383549,
  53383546, 75986642, 74026545, 53383543, 53383540, 56580356, 75992795, 17161048, 53383537, 75988711, 51497328, 53383500, 18353020, 71917101, 53383497, 53383494, 53383491, 14611120, 53383488, 75986679,
  51497312, 53383519, 44073170, 51497315, 53383516, 75984629, 75992819, 20820811, 51497318, 17161085, 51497321, 53383510, 29402041, 20314956, 74026514, 51497324, 53383507, 53383504, 75990777, 76152566,
  46127164, 53383597, 75988484, 53383594, 73746147, 75990528, 53383590, 39487696, 53383585, 53383615, 75992598, 75450093, 53383612, 12868183, 29403989, 53383609, 71917265, 26954496, 34252945, 75986460,
  53383603, 23340026, 53383600, 29401953, 53383567, 53383564, 53383561, 75984416, 75988512, 53383558, 18353073, 75990572, 53383555, 53383552, 26954548, 71429873, 73746133, 53383582, 75992631, 6847194,
  73746128, 53383576, 12864125, 71917296, 53383573, 53383570, 23339995, 46159872, 53383663, 53383660, 75986500, 75984448, 76152384, 29401868, 75988555, 26954582, 20315128, 71429775, 53383648, 53383679,
  53383676, 46159892, 75992658, 75990609, 53051902, 53383673, 53383669, 53383666, 75280034, 34253013, 53383630, 75986533, 75992677, 53383627, 53383624, 75984481, 44011640, 74026625, 75450004, 53383621,
  53383618, 71917222, 53383645, 75988596, 53383642, 67501808, 75984511, 53383639, 53383636, 74026642, 75990651, 76152436, 20315083, 53383633, 75990925, 75984778, 12868555, 29401808, 56580172, 76152733,
  76152721, 75450215, 75986845, 20314671, 34253078, 20822563, 70455085, 75984805, 75988901, 56580217, 76152750, 12868588, 39510384, 56580212, 75450190, 26954409, 29401843, 20314630, 104836363, 75990963,
  44073365, 75988927, 56580198, 56580194, 76152759, 75984824, 75986872, 46111231, 43981278, 71429893, 56580122, 75427643, 26954451, 75990986, 71429903, 71917319, 56580106, 76152784, 56580098, 13833101,
  75986904, 76152808, 48796042, 725871, 75988961, 12868515, 65941638, 75450134, 75991022, 75427604, 75984876, 76152805, 56580145, 56580141, 22164007, 68667234, 46111176, 75988988, 34253172, 36489643,
  75988736, 65814629, 75984652, 39487959, 29403725, 26954260, 56580300, 75988767, 39328195, 29403740, 75992859, 56580290, 43981118, 29401697, 39328249, 75990822, 101316011, 75986721, 76152613, 75984682,
  75992887, 46272886, 56834262, 53792898, 73752541, 26946082, 75984699, 461754, 75988806, 12868358, 16370493, 43856217, 34253263, 26954333, 56580247, 57575591, 46111093, 76152642, 75450291, 75984715, 20820722,
  75990856, 53383422, 76152664, 53383419, 75992913, 53383414, 75988830, 75992926, 39487873, 56580227, 53383411, 26946116, 75986776, 20314837, 75984742, 75990884, 39510460, 9948017, 43981176, 75992940, 26946167,
  7449501, 29403694, 56580263, 12868412, 70993857, 44073307, 75450244, 67688436, 63105087, 75988858, 75986809, 76152694, 76152970, 75985028, 17284354, 49133291, 26955164, 44177061, 73752693, 75989140, 76152990,
  44177082, 75987101, 75991194, 36286007, 43859640, 14654707, 76898491, 20178204, 14654703, 75985077, 76898470, 75991217, 32087504, 59353010, 75989179, 14654688, 69609497, 742475, 44177120, 79794310, 26955216,
  50370382, 75987145, 76898522, 73752629, 44177137, 75989204, 63104918, 102726218, 75985106, 75991249, 26195415, 103479920, 50183846, 39327239, 44177149, 75987172, 44177094, 75985121, 32087427, 44011252, 76898553,
  75991275, 43859699, 102726244, 73752600, 75989242, 75991290, 56580063, 74971383, 75991041, 75986944, 56580050, 73752811, 73752823, 20309415, 75991060, 75984912, 56580039, 75989022, 76152852, 39278326, 76152873,
  65378113, 36488842, 75986991, 73752780, 56580086, 53325700, 68792500, 75984950, 56580077, 102726317, 56580073, 76152892, 12860531, 75991101, 2571418, 26955099, 75989063, 56579998, 72984732, 44177002, 75987023,
  55035788, 75991126, 56579981, 56579975, 43972165, 75984988, 13636619, 20823523, 44177020, 29402399, 76152917, 75989094, 56580028, 70473941, 102726392, 13636663, 75985005, 65378063, 100924147, 75987063, 75991158,
  56580010, 73752733, 76152946, 29402427, 56580003, 20821442, 75151736, 102726425, 102902565, 75987328, 44087208, 39278429, 75991437, 12576180, 75985290, 29402318, 102726417, 75989385, 43972493, 102726409, 73752956,
  74023280, 100374754, 6845810, 75151713, 37005103, 75991449, 36488972, 65377989, 100924192, 6845772, 73752896, 53050892, 29402342, 102726455, 75985326, 23340110, 44087181, 75987370, 46126992, 75991475, 100924213, 46159863,
  75151693, 102726440, 102153005, 75987387, 73752923, 29402366, 75989446, 102726493, 20565114, 40843048, 44011479, 102726487, 79907211, 79907210, 79907209, 75991501, 73752878, 79907214, 72235264, 79907212, 100924236,
  102726479, 79907219, 79907218, 75989462, 79907217, 23340087, 79907216, 79907223, 79907222, 46127081, 79907221, 75987409, 79907220, 75985360, 79907227, 79907226, 79907225, 79907224, 79907230, 29402270, 102726465,
  79907229, 53050999, 36489075, 20565094, 79907228, 17263704, 102726527, 75985383, 79907233, 75987429, 79907232, 44087240, 75991532, 79907247, 79907246, 102726513, 79907251, 79907250, 75151624, 79907249, 54527539,
  79907248, 79907255, 26266852, 79907254, 79907253, 79907252, 39278382, 34252656, 20327500, 29402299, 79907256, 75987204, 13307214, 75989261, 43972373, 39278552, 102726544, 44177197, 20823212, 80750916, 75985170,
  79907248, 79907255, 26266852, 79907254, 79907253, 79907252, 39278382, 34252656, 20327500, 29402299, 79907256, 75987204, 13307214, 75989261, 43972373, 39278552, 102726544, 44177197, 20823212, 80750916, 75985170,
  65377904, 75991312, 75991335, 20309141, 102726590, 75985187, 74971602, 75987233, 29402215, 44177163, 55332607, 102726571, 75989297, 53051027, 75987261, 13307254, 102726562, 57575123, 102726623, 14654749, 44087140,
  75991361, 20821236, 34252739, 73753007, 75989324, 43859793, 75985226, 39278489, 14654736, 55918213, 17284306, 14654729, 76900674, 20565218, 70265326, 75987302, 75991397, 44087107, 14654778, 102726649, 20565203,
  55316135, 44011378, 75985258, 102726641, 29402159, 73752982, 20565194, 102726636, 14654762, 39509932, 75989360, 80750889, 75991419, 57575057, 19396387, 75991694, 32088035, 26292122, 75270775, 75987596, 44088493,
  20827955, 26945422, 36490299, 75270752, 34362412, 12576415, 30412786, 7480912, 44088451, 19396353, 102500410, 75989664, 24975189, 71760492, 75985579, 75987627, 102500400, 19396374, 54781298, 39277669, 75991733,
  28043160, 54781301, 75270724, 54781307, 65377759, 54781304, 54781310, 103333993, 69354083, 75989701, 75991749, 19396452, 46120187, 75987650, 46169222, 30412695, 75985615, 100925519, 102500439, 20502391, 75991767,
  77347533, 43863244, 26945487, 103334015, 12576488, 75985631, 102500423, 75987675, 75270691, 26945533, 20502367, 77347573, 44088523, 102500471, 75989736, 75987701, 7450114, 71889469, 19396433, 17362815, 75989759,
  49687695, 12576454, 102500453, 75985661, 46120132, 102500448, 44088542, 75991800, 53382574, 63108419, 46119997, 75989507, 53382571, 75985410, 26951453, 36490410, 75987456, 18671509, 43863062, 75991574, 75989523,
  63108436, 74023678, 73276105, 43863050, 102500485, 26945286, 102500481, 46169183, 75987480, 73276158, 7481043, 44088322, 12576281, 74023617, 39509232, 19396490, 75985449, 3507866, 75989558, 75987508, 26945326,
  53050780, 19396509, 75991613, 54781313, 74023594, 35863776, 54781319, 44088421, 103334126, 69354212, 54781316, 103334112, 54781322, 20502514, 54781326, 53050873, 26945354, 103340282, 54781329, 20309991, 12576364,
  7480993, 54781335, 75987539, 69354230, 75989585, 54781332, 54781341, 75985497, 43863106, 75991640, 44586059, 54781344, 73757313, 54781349, 54781355, 75987564, 53050820, 54781358, 103334086, 75985513, 75989623,
  75991670, 54781361, 20821966, 54781364, 63108405, 75985535, 44088410, 43863139, 14155735, 75983751, 13823952, 75989892, 77347739, 75991936, 103334204, 75985810, 75991954, 73276230, 77347716, 20823588, 43971972,
  46120357, 75989917, 75987868, 26955397, 80141273, 75983783, 77347773, 75991972, 69354246, 36490503, 77030332, 103334147, 13823999, 77347762, 103334171, 75987892, 75985842, 73276264, 20823558, 26945191, 75989947,
  3669879, 26945242, 72231695, 52552807, 77347803, 103334240, 76379077, 72123136, 75992011, 77347793, 100923725, 26951380, 20309626, 75985878, 7481127, 75983824, 20823652, 29402777, 46087653, 46181787, 26955456,
  103334259, 75989980, 43972033, 51076180, 75987928, 68307825, 20309587, 59469036, 75992046, 77347824, 75987947, 36285786, 77347816, 75990000, 74023697, 75981820, 77030379, 75983867, 20823618, 103334315, 43971862,
  77347604, 20821681, 73630703, 17249931, 103334332, 75987743, 74023920, 73276359, 75985694, 39327171, 26955264, 71318492, 75983654, 77347647, 75989796, 103334285, 74023887, 26951222, 77347630, 44088595, 75991860,
  73630681, 26945057, 75987771, 77347617, 75985721, 75991879, 46181637, 75983681, 46120311, 75989838, 77347670, 19396332, 77347664, 51109063, 75987786, 73276305, 19396341, 75989850, 74023863, 26955332, 33893859,
  26945148, 75985760, 75985773, 75983723, 75991913, 75987831, 73276323)";
  static constexpr const char* kSpotCopperConfig = R"({"uids":["123","456"]})";
  static constexpr const char* kSpotAdventureConfig = R"({"uids":[123,456]})";

  static constexpr const char* kListingOpenTradeTimeConfig =
      R"({"data":{"XXXUSDT":{"symbolName":"XXXUSDT","coinName":"USDT","openTradeTime":************},"YYYUSDT":{"symbolName":"YYYUSDT","coinName":"USDT","openTradeTime":4070908800000}}})";
  static constexpr const char* kSpotFeeRuleConfig =
      R"({"biz_type":1,
    "data":[{"rule_type":1,"rule_priority":0,"rule_config":{"symbols":["BTCUSDT","ETHUSDT"],"config":[{"user_rule":"VIP1","taker":"0.0001","maker":"0.0001"},{"user_rule":"VIP2","taker":"0.0002","maker":"0.0002"}]}},
    {"rule_type":2,"rule_priority":0,"rule_config":{"coins":["USDT","USDC","ETH"],"config":[{"user_rule":"VIP1","taker":"0.0001","maker":"0.0001"},{"user_rule":"VIP2","taker":"0.0002","maker":"0.0002"}]}},
    {"rule_type":4,"rule_priority":0,"rule_config":{"tag_ids":[1],"config":[{"user_rule":"VIP1","taker":"0.0001111","maker":"0.000111777"},{"user_rule":"VIP2","taker":"0.0002222","maker":"0.0002222"}]}},
    {"rule_type":4,"rule_priority":0,"rule_config":{"tag_ids":[2,3,4,5,6],"config":[{"user_rule":"VIP1","taker":"0.0001111","maker":"0.000111"},{"user_rule":"VIP2","taker":"0.0002222","maker":"0.0002222"}]}}],
    "version":1712830447967,"hash":"b991a4dab4adf61a581b17d2530f4169"})";
  static constexpr const char* kSpotSiteFeeRuleConfig =
      R"({"data":[
      {"siteId":"ARE",
      "siteFee":{"siteId":"ARE","makerBuyFee":"0.001","makerSellFee":"0.0011","takerBuyFee":"0.0011","takerSellFee":"0.0011"},
      "symbolFeeList":[{"siteId":"ARE","symbolId":"KARUSDC","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
      {"siteId":"ARE","symbolId":"OPUSDT","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
      {"siteId":"ARE","symbolId":"WLDUSDT","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
      {"siteId":"ARE","symbolId":"OPETH","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
      {"siteId":"ARE","symbolId":"DASHUSDT","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
      {"siteId":"ARE","symbolId":"OMGBTC","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
      {"siteId":"ARE","symbolId":"OMGETH","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
      {"siteId":"ARE","symbolId":"DEFYBTC","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
{"siteId":"ARE","symbolId":"DEFYUSDT","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"}]}],
"version":"********************************","lastUpdate":1713968011791})";
  static constexpr const char* kSpotKycFeeRuleConfig =
      R"({"data":[
      {"kycCountry":"ARE",
      "kycFee":{"kycCountry":"ARE","makerBuyFee":"0.001","makerSellFee":"0.0011","takerBuyFee":"0.0011","takerSellFee":"0.0011"},
      "symbolFeeList":[{"kycCountry":"ARE","symbolId":"KARUSDC","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
      {"kycCountry":"ARE","symbolId":"OPUSDT","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
      {"kycCountry":"ARE","symbolId":"WLDUSDT","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
      {"kycCountry":"ARE","symbolId":"OPETH","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
      {"kycCountry":"ARE","symbolId":"DASHUSDT","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
      {"kycCountry":"ARE","symbolId":"OMGBTC","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
      {"kycCountry":"ARE","symbolId":"OMGETH","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
      {"kycCountry":"ARE","symbolId":"DEFYBTC","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"},
{"kycCountry":"ARE","symbolId":"DEFYUSDT","makerBuyFee":"0.0001","makerSellFee":"0","takerBuyFee":"0.0001","takerSellFee":"0.0002"}]}],
"version":"********************************","lastUpdate":1713968011791})";
  static constexpr const char* kExternalMarketMakersConfig =
      R"({"BTCUSDT": [1234,5677,1345],"ETHUSDT": [1234,5677,1345]})";

  static constexpr const char* kBrokerAdminLadderRateConfig =
      R"({
      "data":[
    {
        "tokenId": "BTC",
        "coinId": 1,
        "showOrder": 1,
        "siteId": "BYBIT",
        "rateList": [{"min":"0", "max":"100", "rate":"1"}, {"min":"100", "max":"200", "rate":"0.9"}]
    },
    {
        "tokenId": "ETH",
        "coinId": 2,
        "siteId": "BYBIT",
        "showOrder": 2,
        "rateList": [{"min":"0", "max":"100", "rate":"1"},{"min":"100", "max":"", "rate":"0.9"}]
    },
    {
        "tokenId": "ETH",
        "coinId": 2,
        "siteId": "EU",
        "showOrder": 2,
        "rateList": [{"min":"0", "max":"100", "rate":"1"},{"min":"100", "max":"", "rate":"0.9"}]
    }
  ],
  "version":"e501677014428b827167027c6184a6f5",
  "lastUpdate":1713843440294
})";

  static constexpr const char* kBrokerAdminLadderRateConfigInValid =
      R"({
      "data":[
    {
        "tokenId": "BTC",
        "showOrder": 1,
        "coinId": 1,
        "siteId": "BYBIT",
        "rateList": [{"min":"0", "max":"100", "rate":"1"}, {"min":"101", "max":"200", "rate":"0.9"}]
    },
    {
        "tokenId": "ETH",
        "showOrder": 2,
        "coinId": 2,
        "siteId": "BYBIT",
        "rateList": [{"min":"0", "max":"100", "rate":"1"},{"min":"100", "max":"", "rate":"0.9"}]
    }
  ],
  "version":"e501677014428b827167027c6184a6f5",
  "lastUpdate":1713843440294
})";

  static constexpr const char* kTaxRuleConfig =
      R"({
      "tax_rule_list":[
    {
        "tax_site": "IDN",
        "fiat_coin_id": 32,
        "tax_rule_id": 1,
        "tax_ppn_e8": 110000,
        "tax_pph_e8": 100000,
        "cfx_fee_e8": 22200
    }
  ],
  "tax_rule_map": {
        "IDN": 1
    }
})";
};

static constexpr const char* kFutureSpreadPContent = R"({
  "data" : [
    {
      "baseCoin" : "BTC",
      "contractType" : 7,
      "crossIdx" : 40000,
      "grayUids" : [ "123", "1234", "99981" ],
      "leg1ContractType" : 4,
      "leg1ProductType" : 1,
      "leg1SymbolId" : 724,
      "leg2ContractType" : 6,
      "leg2ProductType" : 2,
      "leg2SymbolId" : 10,
      "quoteCoin" : "USDT",
      "settleCoin" : "USDT",
      "settleTimeE9" : 1740726000000000000,
      "startFullTradingTimeE9" : 0,
      "startGrayTradingTimeE9" : 1740384000000000000,
      "status" : 1,
      "symbolAlias" : "",
      "symbolId" : 100010,
      "symbolName" : "BTCUSDT-28FEB25_BTC/USDT"
    },
    {
      "baseCoin" : "BTC",
      "contractType" : 10,
      "crossIdx" : 40000,
      "grayUids" : [ "123", "456", "789" ],
      "leg1ContractType" : 2,
      "leg1ProductType" : 1,
      "leg1SymbolId" : 5,
      "leg2ContractType" : 6,
      "leg2ProductType" : 2,
      "leg2SymbolId" : 10,
      "quoteCoin" : "USDT",
      "settleCoin" : "USDT",
      "settleTimeE9" : 0,
      "startFullTradingTimeE9" : 1740364800000000000,
      "startGrayTradingTimeE9" : 1740364500000000000,
      "status" : 1,
      "symbolAlias" : "",
      "symbolId" : 100009,
      "symbolName" : "BTCUSDT_BTC/USDT"
    }
  ],
  "version" : "1740383656001|2c85942d-f939-4ff4-9f45-67194d817544"
})";

TEST_F(ConfigTest, parseJson) {
  std::string msg;

  // MP_INST_SPOT_RLP_DATA
  config::SpotRPIUserConfig spot_rpi_config;
  ASSERT_FALSE(spot_rpi_config.parse(kInvalidContent, msg));
  std::string invalidJson =
      R"({"data":[{"uids":[12345678,1002],"symbolIds":[1,279], {"uids":[1003,1004],"symbolIds": [279,278] }]})";
  ASSERT_FALSE(spot_rpi_config.parse(invalidJson, msg));

  config::FutureRPIUserConfig future_rpi_config;
  ASSERT_FALSE(future_rpi_config.parse(kInvalidContent, msg));
  ASSERT_FALSE(future_rpi_config.parse(invalidJson, msg));

  // parse OPTION_CLIENT_CONFIG
  config::ClientConfig client_config;
  ASSERT_FALSE(client_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(client_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(client_config.parse(kClientConfigContent, msg));
  ASSERT_EQ(client_config.fee_mid_switch, true);
  ASSERT_STREQ(client_config.omp_grpc_host.c_str(), "***********");
  ASSERT_EQ(client_config.omp_grpc_port, 9090);
  ASSERT_EQ(client_config.ack_grpc_switch, false);

  // parse OPTION_MP_PRECISION
  config::CommonConfigData precision_config;
  ASSERT_FALSE(precision_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(precision_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(precision_config.parse(kMpPrecisionContent, msg));
  ASSERT_EQ(precision_config.data.size(), 1);
  auto iter = precision_config.data.find("BTC");
  ASSERT_TRUE(iter != precision_config.data.end());
  auto btc_iter = iter->second.find("orderSize");
  ASSERT_TRUE(btc_iter != iter->second.end());
  std::string order_size = btc_iter->second;
  int64_t size = strtoll(order_size.c_str(), nullptr, 10);
  ASSERT_EQ(size, 4);

  // OPTION_MP_GLOBAL
  config::CommonConfigData global_config;
  ASSERT_FALSE(global_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(global_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(global_config.parse(kOptionalGlobalContent, msg));
  ASSERT_EQ(global_config.data.size(), 2);
  ASSERT_STREQ(global_config.data["STRIKE"]["changeReason"].c_str(), "123");

  // OPTION_MP_PORTFOLIO_MARGIN
  config::CommonConfigData pm_config;
  ASSERT_FALSE(pm_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(pm_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(pm_config.parse(kPmContent, msg));
  ASSERT_EQ(pm_config.data.size(), 2);
  ASSERT_STREQ(pm_config.data["BTC"]["priceRangeInterval"].c_str(), "0.013");

  // OPTION_MP_TRADE
  config::CommonConfigData trade_data;
  ASSERT_FALSE(trade_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(trade_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(trade_data.parse(kTradeContent, msg));
  ASSERT_EQ(trade_data.data.size(), 1);
  ASSERT_STREQ(trade_data.data["BTC"]["orderPriceFromLastPrice"].c_str(), "0.03");

  // OPTION_MP_TRADE_USDT
  config::CommonConfigData trade_usdt_data;
  ASSERT_FALSE(trade_usdt_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(trade_usdt_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(trade_usdt_data.parse(kTradeUSDTContent, msg));
  ASSERT_EQ(trade_usdt_data.data.size(), 1);
  ASSERT_STREQ(trade_usdt_data.data["DEFAULT_COIN"]["liquidationFeeRateUsdt"].c_str(), "0.0006");
  ASSERT_STREQ(trade_usdt_data.data["DEFAULT_COIN"]["insuranceFeeRateUsdt"].c_str(), "0.000513");

  // OPTION_MP_ACCT_LIMIT
  config::AcctLimitData acct_limit_data;
  ASSERT_FALSE(acct_limit_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(acct_limit_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(acct_limit_data.parse(kAcctLimitContent, msg));
  ASSERT_EQ(acct_limit_data.rule_data.size(), 1);
  ASSERT_EQ(acct_limit_data.rule_data["BTC"].size(), 3);
  ASSERT_STREQ(acct_limit_data.rule_data["BTC"]["SMALL_INSTITUTION"]["maxTotalAmountOptionOrders"].c_str(), "100");
  ASSERT_STREQ(acct_limit_data.rule_data["BTC"]["SMALL_INSTITUTION"]["whiteList"].c_str(), "7111221");

  ASSERT_EQ(acct_limit_data.user_data.size(), 1);
  ASSERT_EQ(acct_limit_data.user_data["BTC"].size(), 1);
  ASSERT_STREQ(acct_limit_data.user_data["BTC"]["7111221"].c_str(), "SMALL_INSTITUTION");

  // parse SYMBOL_FULL_DATA_ID
  config::SymbolFullRawData symbol_data;
  ASSERT_FALSE(symbol_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(symbol_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(symbol_data.parse(kOptionSymbolContent, msg));
  ASSERT_EQ(symbol_data.data.size(), 3);
  std::shared_ptr<config::SymbolDTO> symbol_dto = symbol_data.data[0];
  ASSERT_STREQ(symbol_dto->asset_type.c_str(), "OPTION");
  ASSERT_STREQ(symbol_dto->base_coin.c_str(), "BTC");
  ASSERT_EQ(symbol_dto->coin_pair_id, 1);
  ASSERT_EQ(symbol_dto->contract_size, 1);
  ASSERT_STREQ(symbol_dto->contract_type.c_str(), "LinearOption");
  ASSERT_EQ(symbol_dto->cross_id, 10005);
  ASSERT_EQ(symbol_dto->delivery_time, 1841937600);
  ASSERT_STREQ(symbol_dto->expiry.c_str(), "CURRENT_WEEK");
  ASSERT_EQ(symbol_dto->id, 102311);
  ASSERT_EQ(symbol_dto->multiplier, 1);
  ASSERT_EQ(symbol_dto->online_time, 1640687236);
  ASSERT_STREQ(symbol_dto->quote_coin.c_str(), "USD");
  ASSERT_STREQ(symbol_dto->settle_coin.c_str(), "USDC");
  ASSERT_STREQ(symbol_dto->standard.c_str(), "U");
  ASSERT_STREQ(symbol_dto->status.c_str(), "OFFLINE");
  ASSERT_TRUE(symbol_dto->strike_price.Equal(bbase::decimal::Decimal<>("40000.000000002")));
  ASSERT_STREQ(symbol_dto->symbol_name.c_str(), "BTC-31DEC21-40000-C");
  ASSERT_STREQ(symbol_dto->symbol_type.c_str(), "C");
  ASSERT_STREQ(symbol_data.data[1]->status.c_str(), "ONLINE");
  ASSERT_STREQ(symbol_data.data[2]->expiry.c_str(), "THREE_DAY");

  // parse OPTION_MP_DELIVERYTIME
  config::DeliveryTimeData delivery_time_data;
  ASSERT_FALSE(delivery_time_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(delivery_time_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(delivery_time_data.parse(kDeliveryTimeContent, msg));
  ASSERT_EQ(delivery_time_data.data.size(), 2);
  ASSERT_STREQ(delivery_time_data.data[0]->base_coin.c_str(), "BTC");
  ASSERT_EQ(delivery_time_data.data[0]->delivery_time, 1655020800);
  ASSERT_STREQ(delivery_time_data.data[0]->quote_coin.c_str(), "USD");
  ASSERT_STREQ(delivery_time_data.data[0]->settle_coin.c_str(), "USDC");
  ASSERT_EQ(delivery_time_data.data[1]->delivery_time, 1655107200);
  ASSERT_STREQ(delivery_time_data.data[1]->settle_coin.c_str(), "USDC");

  // parse OPTION_MP_UM_GENERAL_CONFIG
  config::UmGeneralConfigDetail um_general_config;
  ASSERT_FALSE(um_general_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(um_general_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(um_general_config.parse(kUmGeneralConfigContent, msg));
  ASSERT_EQ(um_general_config.credit_utilization_rate, bbase::decimal::Decimal<>("0.85"));
  ASSERT_EQ(um_general_config.default_lever, 5);
  ASSERT_EQ(um_general_config.max_lever, 5);
  ASSERT_EQ(um_general_config.mm_rate_threshold, bbase::decimal::Decimal<>("0.8"));
  ASSERT_EQ(um_general_config.settle_rate, bbase::decimal::Decimal<>("0.02"));
  ASSERT_EQ(um_general_config.support_lever.size(), 4);
  ASSERT_EQ(um_general_config.support_lever[0], 2);
  ASSERT_EQ(um_general_config.support_lever[3], 5);

  // OPTION_MP_UM_COIN
  config::UmCoinData um_coin_data;
  ASSERT_FALSE(um_coin_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(um_coin_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(um_coin_data.parse(kUmCoinContent, msg));
  ASSERT_EQ(um_coin_data.data.size(), 2);
  ASSERT_STREQ(um_coin_data.data["buyCoin"][0].c_str(), "USDC");
  ASSERT_STREQ(um_coin_data.data["buyCoin"][1].c_str(), "USDT");

  // parse OPTION_MP_UM_COIN_CONFIG
  config::UmCoinConfigData um_coin_config_data;
  ASSERT_FALSE(um_coin_config_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(um_coin_config_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(um_coin_config_data.parse(kUmCoinConfigContent, msg));
  ASSERT_EQ(um_coin_config_data.config_list.size(), 2);
  ASSERT_STREQ(um_coin_config_data.config_list[0]->base_coin.c_str(), "SELENA14");
  ASSERT_EQ(um_coin_config_data.config_list[0]->collateral_value_ratio, bbase::decimal::Decimal<>("1"));

  // parse OPTION_MP_EXCHANGE_FEE_RATE
  config::ExchangeRateRawData exchange_rate_raw_data;
  ASSERT_FALSE(exchange_rate_raw_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(exchange_rate_raw_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(exchange_rate_raw_data.parse(kExchangeRateContent, msg));
  ASSERT_EQ(exchange_rate_raw_data.data.size(), 4);
  ASSERT_STREQ(exchange_rate_raw_data.data[3]->buy_coin.c_str(), "USDT");
  ASSERT_EQ(exchange_rate_raw_data.data[3]->order_size_max, bbase::decimal::Decimal<>("20"));
  ASSERT_EQ(exchange_rate_raw_data.data[3]->price_scalar, bbase::decimal::Decimal<>("0.9969"));

  // COIN_PAIR_CONFIG_DATA_ID
  config::CoinPairData coin_pair_data;
  ASSERT_FALSE(coin_pair_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(coin_pair_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(coin_pair_data.parse(kCoinPairContent, msg));
  ASSERT_EQ(coin_pair_data.data.size(), 1);
  ASSERT_STREQ(coin_pair_data.data[0]->quote_coin.c_str(), "USD");
  ASSERT_EQ(coin_pair_data.data[0]->contract_size, 1);

  // OPTION_MP_USER_ZONE
  config::UserZoneConfigRawData user_zone_raw_config;
  ASSERT_FALSE(user_zone_raw_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(user_zone_raw_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(user_zone_raw_config.parse(kUserZoneContent, msg));
  ASSERT_EQ(user_zone_raw_config.data.size(), 1);
  ASSERT_STREQ(user_zone_raw_config.data[0]->gmt_creator.c_str(), "15");
  ASSERT_STREQ(user_zone_raw_config.data[0]->gmt_create.c_str(), "2022-02-16T08:03:13.385");
  ASSERT_STREQ(user_zone_raw_config.data[0]->gmt_modifier.c_str(), "15");
  ASSERT_STREQ(user_zone_raw_config.data[0]->gmt_modified.c_str(), "2022-02-16T08:03:13.385");
  ASSERT_EQ(user_zone_raw_config.data[0]->id, 1);
  ASSERT_STREQ(user_zone_raw_config.data[0]->is_deleted.c_str(), "N");
  ASSERT_STREQ(user_zone_raw_config.data[0]->uid.c_str(), "13774489");
  ASSERT_STREQ(user_zone_raw_config.data[0]->zone_id.c_str(), "27");
  ASSERT_STREQ(user_zone_raw_config.data[0]->zone_name.c_str(), "RZVIP00");
  ASSERT_STREQ(user_zone_raw_config.data[0]->zone_type.c_str(), "VIP");

  // OPTION_MP_RISK_INST
  config::RiskInstConfigRawData risk_inst_raw_data;
  ASSERT_FALSE(risk_inst_raw_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(risk_inst_raw_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(risk_inst_raw_data.parse(kRiskInstContent, msg));
  ASSERT_EQ(risk_inst_raw_data.data.size(), 1);
  ASSERT_STREQ(risk_inst_raw_data.data[0]->gmt_create.c_str(), "2022-03-08T07:52:10.305");
  ASSERT_STREQ(risk_inst_raw_data.data[0]->gmt_creator.c_str(), "bill.wang");
  ASSERT_STREQ(risk_inst_raw_data.data[0]->gmt_modified.c_str(), "2022-03-08T07:52:11.305");
  ASSERT_STREQ(risk_inst_raw_data.data[0]->gmt_modifier.c_str(), "bill.wang");
  ASSERT_EQ(risk_inst_raw_data.data[0]->id, 1);
  ASSERT_STREQ(risk_inst_raw_data.data[0]->inst_id.c_str(), "13774489");
  ASSERT_STREQ(risk_inst_raw_data.data[0]->inst_name.c_str(), "Options Internalgrey");
  ASSERT_EQ(risk_inst_raw_data.data[0]->liquidation_delay_info.liquidation_delay_switch, config::SwitchType::kOff);
  ASSERT_EQ(risk_inst_raw_data.data[0]->margin_trial_switch, "ON");
  ASSERT_STREQ(risk_inst_raw_data.data[0]->status.c_str(), "ACTIVE");
  ASSERT_STREQ(risk_inst_raw_data.data[0]->uid.c_str(), "13774489");

  // OPTION_MP_INST_USER_REL
  config::InstUserRelRawData inst_user_rel_raw_data;
  ASSERT_FALSE(inst_user_rel_raw_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(inst_user_rel_raw_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(inst_user_rel_raw_data.parse(kInstUserRelContent, msg));
  ASSERT_EQ(inst_user_rel_raw_data.data.size(), 1);
  ASSERT_STREQ(inst_user_rel_raw_data.data[0]->gmt_creator.c_str(), "15");
  ASSERT_STREQ(inst_user_rel_raw_data.data[0]->gmt_create.c_str(), "2022-02-16T07:39:10.773");
  ASSERT_STREQ(inst_user_rel_raw_data.data[0]->gmt_modifier.c_str(), "15");
  ASSERT_STREQ(inst_user_rel_raw_data.data[0]->gmt_modified.c_str(), "2022-02-16T07:39:10.773");
  ASSERT_EQ(inst_user_rel_raw_data.data[0]->id, 1);
  ASSERT_STREQ(inst_user_rel_raw_data.data[0]->inst_id.c_str(), "13774489");
  ASSERT_STREQ(inst_user_rel_raw_data.data[0]->inst_name.c_str(), "Options Internalgrey");
  ASSERT_STREQ(inst_user_rel_raw_data.data[0]->is_deleted.c_str(), "N");
  ASSERT_STREQ(inst_user_rel_raw_data.data[0]->uid.c_str(), "13774489");
  ASSERT_STREQ(inst_user_rel_raw_data.data[0]->usdc_aid.c_str(), "17624981");

  // OPTION_MP_INST_CONFIG
  config::InstConfigRawData inst_config_raw_data;
  ASSERT_FALSE(inst_config_raw_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(inst_config_raw_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(inst_config_raw_data.parse(kInstConfigContent, msg));
  ASSERT_EQ(inst_config_raw_data.data.size(), 1);
  ASSERT_STREQ(inst_config_raw_data.data[0]->acct_coin_types.c_str(), "[\"usdc\"]");
  ASSERT_STREQ(inst_config_raw_data.data[0]->acct_param_type.c_str(), "大型做市商");
  ASSERT_EQ(inst_config_raw_data.data[0]->api_quota, 5000);
  ASSERT_STREQ(inst_config_raw_data.data[0]->dcp_status.c_str(), "OFF");
  ASSERT_STREQ(inst_config_raw_data.data[0]->gmt_create.c_str(), "2022-02-16T09:14:08.227");
  ASSERT_STREQ(inst_config_raw_data.data[0]->gmt_creator.c_str(), "15");
  ASSERT_STREQ(inst_config_raw_data.data[0]->gmt_modified.c_str(), "2022-05-17T08:09:21.125");
  ASSERT_STREQ(inst_config_raw_data.data[0]->gmt_modifier.c_str(), "15");
  ASSERT_EQ(inst_config_raw_data.data[0]->id, 1);
  ASSERT_STREQ(inst_config_raw_data.data[0]->inst_id.c_str(), "13774489");
  ASSERT_STREQ(inst_config_raw_data.data[0]->inst_level.c_str(), "Premium");
  ASSERT_STREQ(inst_config_raw_data.data[0]->inst_name.c_str(), "Options Internalgrey");
  ASSERT_STREQ(inst_config_raw_data.data[0]->inst_type.c_str(), "Market Maker");
  ASSERT_STREQ(inst_config_raw_data.data[0]->is_deleted.c_str(), "N");
  ASSERT_STREQ(inst_config_raw_data.data[0]->status.c_str(), "ACTIVE");

  // OPTION_MP_INST_USER_CONFIG
  config::InstUserConfigRawData inst_user_config_data;
  ASSERT_FALSE(inst_user_config_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(inst_user_config_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(inst_user_config_data.parse(kInstUserConfigContent, msg));
  ASSERT_EQ(inst_user_config_data.data.size(), 2);
  ASSERT_STREQ(inst_user_config_data.data[0]->acct_coin_types.c_str(), "[\"usdc\"]");
  ASSERT_EQ(inst_user_config_data.data[0]->api_quota, 400);
  ASSERT_STREQ(inst_user_config_data.data[0]->dcp_status.c_str(), "ON");
  ASSERT_STREQ(inst_user_config_data.data[0]->gmt_create.c_str(), "2021-12-14T13:20:54.753");
  ASSERT_STREQ(inst_user_config_data.data[0]->gmt_creator.c_str(), "100034");
  ASSERT_STREQ(inst_user_config_data.data[0]->gmt_modified.c_str(), "2021-12-14T13:20:54.753");
  ASSERT_STREQ(inst_user_config_data.data[0]->gmt_modifier.c_str(), "100034");
  ASSERT_EQ(inst_user_config_data.data[0]->id, 7);
  // no such fields in DTO
  // ASSERT_STREQ(inst_user_config_data.data[0]->inst_id.c_str(), "461637");
  // ASSERT_STREQ(inst_user_config_data.data[0]->inst_name.c_str(), "cyber");
  ASSERT_STREQ(inst_user_config_data.data[0]->is_deleted.c_str(), "N");
  ASSERT_STREQ(inst_user_config_data.data[0]->status.c_str(), "ACTIVE");
  ASSERT_STREQ(inst_user_config_data.data[0]->uid.c_str(), "7817372");
  ASSERT_STREQ(inst_user_config_data.data[1]->uid.c_str(), "7798738");

  // OPTION_MP_INST_FEE
  config::InstFeeRawData inst_fee_raw_data;
  ASSERT_FALSE(inst_fee_raw_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(inst_fee_raw_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(inst_fee_raw_data.parse(kInstFeeContent, msg));
  ASSERT_EQ(inst_fee_raw_data.data.size(), 2);
  ASSERT_STREQ(inst_fee_raw_data.data[0]->asset_type.c_str(), "OPTION");
  ASSERT_STREQ(inst_fee_raw_data.data[0]->base_coin.c_str(), "BTC");
  ASSERT_STREQ(inst_fee_raw_data.data[0]->coin_pair_id.c_str(), "1");
  ASSERT_STREQ(inst_fee_raw_data.data[0]->gmt_create.c_str(), "2021-12-02T13:11:31.934");
  ASSERT_STREQ(inst_fee_raw_data.data[0]->gmt_creator.c_str(), "100034");
  ASSERT_STREQ(inst_fee_raw_data.data[0]->gmt_modified.c_str(), "2021-12-02T13:11:31.934");
  ASSERT_STREQ(inst_fee_raw_data.data[0]->gmt_modifier.c_str(), "100035");
  ASSERT_STREQ(inst_fee_raw_data.data[0]->is_deleted.c_str(), "N");
  ASSERT_EQ(inst_fee_raw_data.data[0]->maker_fee, bbase::decimal::Decimal<>("0.010001"));
  ASSERT_EQ(inst_fee_raw_data.data[0]->taker_fee, bbase::decimal::Decimal<>("0.010002"));
  ASSERT_STREQ(inst_fee_raw_data.data[0]->owner_id.c_str(), "461712");
  ASSERT_STREQ(inst_fee_raw_data.data[0]->owner_type.c_str(), "USER");

  // OPTION_MP_ALL_INST_USER_FEE
  config::InstUserFeeData inst_user_fee_data;
  ASSERT_FALSE(inst_user_fee_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(inst_user_fee_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(inst_user_fee_data.parse(kInstUserFeeContent, msg));
  ASSERT_EQ(inst_user_fee_data.data.size(), 1);
  ASSERT_STREQ(inst_user_fee_data.data["25984454_OPTION_BTC"]->asset_type.c_str(), "OPTION");
  ASSERT_STREQ(inst_user_fee_data.data["25984454_OPTION_BTC"]->base_coin.c_str(), "BTC");
  ASSERT_STREQ(inst_user_fee_data.data["25984454_OPTION_BTC"]->owner_id.c_str(), "15961849");
  ASSERT_EQ(inst_user_fee_data.data["25984454_OPTION_BTC"]->maker_fee, bbase::decimal::Decimal<>("0.000001"));

  // OPTION_MP_INST_KPI_CONFIG
  config::InstTypeKpiConfigRawData inst_kpi_config_data;
  ASSERT_FALSE(inst_kpi_config_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(inst_kpi_config_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(inst_kpi_config_data.parse(kInstKpiConfigContent, msg));
  ASSERT_EQ(inst_kpi_config_data.data.size(), 1);
  ASSERT_STREQ(inst_kpi_config_data.data[0]->base_coin.c_str(), "BTC");
  ASSERT_STREQ(inst_kpi_config_data.data[0]->inst_level.c_str(), "Premium");
  ASSERT_STREQ(inst_kpi_config_data.data[0]->inst_level.c_str(), "Premium");

  // OPTION_MP_MM_PROTECTION
  config::MarketMakerProtectData mm_protect_config;
  ASSERT_FALSE(mm_protect_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(mm_protect_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(mm_protect_config.parse(kMmProtectContent, msg));
  ASSERT_EQ(mm_protect_config.data.size(), 1);
  ASSERT_STREQ(mm_protect_config.data["BTC_21826590"]->base_coin.c_str(), "BTC");
  ASSERT_STREQ(mm_protect_config.data["BTC_21826590"]->coin_pair_id.c_str(), "1");
  ASSERT_STREQ(mm_protect_config.data["BTC_21826590"]->configurable.c_str(), "Y");
  ASSERT_STREQ(mm_protect_config.data["BTC_21826590"]->delta_limit.c_str(), "100");
  ASSERT_EQ(mm_protect_config.data["BTC_21826590"]->frozen_period_ms, 100);
  ASSERT_STREQ(mm_protect_config.data["BTC_21826590"]->gmt_create.c_str(), "2022-05-11T07:23:58.728");
  ASSERT_STREQ(mm_protect_config.data["BTC_21826590"]->gmt_creator.c_str(), "SYSTEM");
  ASSERT_STREQ(mm_protect_config.data["BTC_21826590"]->gmt_modified.c_str(), "2022-05-11T07:59:15.998");
  ASSERT_STREQ(mm_protect_config.data["BTC_21826590"]->gmt_modifier.c_str(), "SYSTEM");
  ASSERT_EQ(mm_protect_config.data["BTC_21826590"]->id, 54);
  ASSERT_STREQ(mm_protect_config.data["BTC_21826590"]->inst_id.c_str(), "21541143");
  ASSERT_STREQ(mm_protect_config.data["BTC_21826590"]->inst_name.c_str(), "Babel");
  ASSERT_STREQ(mm_protect_config.data["BTC_21826590"]->is_deleted.c_str(), "N");
  ASSERT_STREQ(mm_protect_config.data["BTC_21826590"]->qty_limit.c_str(), "100");
  ASSERT_STREQ(mm_protect_config.data["BTC_21826590"]->status.c_str(), "ON");
  ASSERT_STREQ(mm_protect_config.data["BTC_21826590"]->uid.c_str(), "21826590");
  ASSERT_EQ(mm_protect_config.data["BTC_21826590"]->window_ms, 5000);

  // MP_COIN_INFO
  config::CoinInfoRawData coin_info_raw_data;
  ASSERT_FALSE(coin_info_raw_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(coin_info_raw_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(coin_info_raw_data.parse(kCoinInfoContent, msg));
  ASSERT_EQ(coin_info_raw_data.data.size(), 3);
  ASSERT_EQ(coin_info_raw_data.data[0]->accuracy_length, 8);
  ASSERT_EQ(coin_info_raw_data.data[0]->biz_type, 1);
  ASSERT_EQ(coin_info_raw_data.data[0]->broker_id, 2);
  ASSERT_STREQ(coin_info_raw_data.data[0]->chain.c_str(), "BTC");
  ASSERT_STREQ(coin_info_raw_data.data[0]->coin.c_str(), "BTC");
  ASSERT_EQ(coin_info_raw_data.data[0]->coin_id, 3);
  ASSERT_EQ(coin_info_raw_data.data[0]->coin_label, 4);
  ASSERT_EQ(coin_info_raw_data.data[0]->coin_plate, 5);
  ASSERT_EQ(coin_info_raw_data.data[0]->type, 6);
  ASSERT_STREQ(coin_info_raw_data.data[1]->coin.c_str(), "ETH");
  ASSERT_STREQ(coin_info_raw_data.data[1]->chain.c_str(), "ETH,ARBI,BSC,ZKSYNC,AURORA,OP");

  // MP_MARGIN_COIN_CONFIG
  config::MarginCoinConfigRawData margin_coin_raw_data;
  ASSERT_FALSE(margin_coin_raw_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(margin_coin_raw_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(margin_coin_raw_data.parse(kMarginCoinCfgContent, msg));
  ASSERT_EQ(margin_coin_raw_data.data.size(), 3);
  ASSERT_EQ(margin_coin_raw_data.data[0]->clearing_order, 1);
  ASSERT_STREQ(margin_coin_raw_data.data[0]->coin.c_str(), "USDT");
  ASSERT_EQ(margin_coin_raw_data.data[0]->coin_id, 5);
  ASSERT_STREQ(margin_coin_raw_data.data[0]->is_loan.c_str(), "Y");
  ASSERT_STREQ(margin_coin_raw_data.data[0]->is_margin.c_str(), "N");
  ASSERT_EQ(margin_coin_raw_data.data[0]->margin_value_rate, bbase::decimal::Decimal<>("0.990000000000000000"));
  ASSERT_EQ(margin_coin_raw_data.data[0]->short_spot_im_rate, bbase::decimal::Decimal<>("0.211100000000000000"));
  ASSERT_EQ(margin_coin_raw_data.data[0]->short_spot_mm_rate, bbase::decimal::Decimal<>("0.410000000000000000"));
  ASSERT_EQ(margin_coin_raw_data.data[0]->status, 1);
  ASSERT_EQ(margin_coin_raw_data.data[2]->short_spot_im_rate, bbase::decimal::Decimal<>("0.110000000000000000"));
  ASSERT_EQ(margin_coin_raw_data.data[2]->short_spot_mm_rate, bbase::decimal::Decimal<>("0.220000000000000000"));

  // MARGIN-CONFIG

  // MP_SPOT_SYMBOL
  config::SpotSymbolRawData spot_symbol_raw_data;
  ASSERT_FALSE(spot_symbol_raw_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(spot_symbol_raw_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(spot_symbol_raw_data.parse(kSpotSymbolContent, msg));
  ASSERT_EQ(spot_symbol_raw_data.data.size(), 4);
  ASSERT_STREQ(spot_symbol_raw_data.data[0]->base_coin.c_str(), "BTC");
  ASSERT_EQ(spot_symbol_raw_data.data[0]->base_coin_id, 1);
  ASSERT_EQ(spot_symbol_raw_data.data[0]->base_coin_type, 1);
  ASSERT_EQ(spot_symbol_raw_data.data[0]->base_precision, 3);
  ASSERT_EQ(spot_symbol_raw_data.data[0]->broker_id, 9001);
  ASSERT_TRUE(spot_symbol_raw_data.data[0]->direction.empty());
  ASSERT_EQ(spot_symbol_raw_data.data[0]->inleverage, bbase::decimal::Decimal<>("0E-18"));
  ASSERT_EQ(spot_symbol_raw_data.data[0]->maker_buy_fee_rate_e8, 100000);
  ASSERT_EQ(spot_symbol_raw_data.data[0]->maker_sell_fee_rate_e8, 100000);
  ASSERT_EQ(spot_symbol_raw_data.data[0]->margin_loan_open, 1);
  ASSERT_EQ(spot_symbol_raw_data.data[0]->min_price_precision, bbase::decimal::Decimal<>("0.001000000000000000"));
  ASSERT_EQ(spot_symbol_raw_data.data[0]->min_price_precision_int, 3);
  ASSERT_EQ(spot_symbol_raw_data.data[0]->open_price, bbase::decimal::Decimal<>("3500.000000000000000000"));
  ASSERT_STREQ(spot_symbol_raw_data.data[0]->settle_coin.c_str(), "USDT");
  ASSERT_EQ(spot_symbol_raw_data.data[0]->settle_coin_type, 1);
  ASSERT_EQ(spot_symbol_raw_data.data[0]->show_status_open_time_ms, 1679973300000);
  ASSERT_EQ(spot_symbol_raw_data.data[0]->etp_risk_switch_status, 0);
  ASSERT_EQ(spot_symbol_raw_data.data[0]->cross_id, 22009);
  ASSERT_EQ(spot_symbol_raw_data.data[0]->min_trade_quantity, bbase::decimal::Decimal<>("0.00001"));
  ASSERT_EQ(spot_symbol_raw_data.data[0]->max_trade_quantity, bbase::decimal::Decimal<>("100"));
  ASSERT_EQ(spot_symbol_raw_data.data[0]->min_trade_amount, bbase::decimal::Decimal<>("0.00001"));
  ASSERT_EQ(spot_symbol_raw_data.data[0]->max_trade_amount, bbase::decimal::Decimal<>("1000000"));
  ASSERT_EQ(spot_symbol_raw_data.data[0]->limit_order_price_limit_percentage, bbase::decimal::Decimal<>("10"));
  ASSERT_EQ(spot_symbol_raw_data.data[0]->market_order_price_limit_percentage, bbase::decimal::Decimal<>("10"));
  ASSERT_EQ(spot_symbol_raw_data.data[0]->limit_y_percentage, bbase::decimal::Decimal<>("15"));
  ASSERT_EQ(spot_symbol_raw_data.data[0]->status, 3);
  ASSERT_EQ(spot_symbol_raw_data.data[1]->status, 1);
  ASSERT_EQ(spot_symbol_raw_data.data[1]->is_test, 0);
  ASSERT_EQ(spot_symbol_raw_data.data[2]->is_test, 1);

  std::string min_precision_str = "0.001";
  ASSERT_EQ(config::SpotSymbolRawData::convertStringToPrecision(min_precision_str), 3);
  min_precision_str = "0.00100000000";
  ASSERT_EQ(config::SpotSymbolRawData::convertStringToPrecision(min_precision_str), 3);
  min_precision_str = "0.100000000";
  ASSERT_EQ(config::SpotSymbolRawData::convertStringToPrecision(min_precision_str), 1);
  min_precision_str = "1.0";
  ASSERT_EQ(config::SpotSymbolRawData::convertStringToPrecision(min_precision_str), 0);
  min_precision_str = "1";
  ASSERT_EQ(config::SpotSymbolRawData::convertStringToPrecision(min_precision_str), 0);

  // MP_SPOT_LOAN_SYMBOL
  config::SpotLoanSymbolRawData spot_loan_symbol_raw_data;
  ASSERT_FALSE(spot_loan_symbol_raw_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(spot_loan_symbol_raw_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(spot_loan_symbol_raw_data.parse(kSpotLoanSymbolContent, msg));
  ASSERT_EQ(spot_loan_symbol_raw_data.data.size(), 2);
  ASSERT_EQ(spot_loan_symbol_raw_data.data[0]->symbol_id, 227);
  ASSERT_STREQ(spot_loan_symbol_raw_data.data[0]->symbol_name.c_str(), "ADAUSDT");
  ASSERT_EQ(spot_loan_symbol_raw_data.data[0]->broker_id, 9001);
  ASSERT_EQ(spot_loan_symbol_raw_data.data[0]->status, 1);
  ASSERT_EQ(spot_loan_symbol_raw_data.data[1]->symbol_id, 222);
  ASSERT_STREQ(spot_loan_symbol_raw_data.data[1]->symbol_name.c_str(), "USDCUSDT");

  // MP_TRUSTEESHIP_UID_CONFIG
  config::TrusteeshipRawData truestee_raw_data;
  ASSERT_FALSE(truestee_raw_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(truestee_raw_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(truestee_raw_data.parse(kTrusteeContent, msg));
  ASSERT_EQ(truestee_raw_data.data.size(), 3);
  ASSERT_EQ(truestee_raw_data.data[0]->uid, 4224048434);
  ASSERT_EQ(truestee_raw_data.data[0]->aid, 4224048436);
  ASSERT_STREQ(truestee_raw_data.data[0]->label.c_str(), "AIO_SYSTEM_TO_USER_PM");

  // OPTION_MP_USER_SPECIAL
  config::UserSpecialConfigData user_special_data;
  ASSERT_FALSE(user_special_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(user_special_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(user_special_data.parse(kMpSpecialUserContent, msg));
  ASSERT_EQ(user_special_data.data.size(), 2);
  ASSERT_TRUE(user_special_data.data.find("3799951") != user_special_data.data.end());
  ASSERT_EQ(user_special_data.data["3799951"].size(), 1);
  ASSERT_STREQ(user_special_data.data["3799951"]["PM_LIMIT"]["pmMaintenanceEquity"].c_str(), "3005");
  ASSERT_STREQ(user_special_data.data["3799951"]["PM_LIMIT"]["pmInitialEquity"].c_str(), "41");
  ASSERT_STREQ(user_special_data.data["3799717"]["PM_LIMIT"]["pmMaintenanceEquity"].c_str(), "200");
  ASSERT_STREQ(user_special_data.data["3799717"]["PM_LIMIT"]["pmInitialEquity"].c_str(), "200");

  // MP_COIN_RISK_POSITION_LIMIT
  config::CoinRiskPositionLimitRawData coin_risk_pos_limit_data;
  ASSERT_FALSE(coin_risk_pos_limit_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(coin_risk_pos_limit_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(coin_risk_pos_limit_data.parse(kCoinRiskPosLimitContent, msg));
  ASSERT_EQ(coin_risk_pos_limit_data.data.size(), 6);
  ASSERT_STREQ(coin_risk_pos_limit_data.data[0]->coin.c_str(), "CGG");
  ASSERT_EQ(coin_risk_pos_limit_data.data[0]->position_limit, bbase::decimal::Decimal<>("1230"));

  // MP_COIN_RISK_USER_POSITION_LIMIT
  config::CoinRiskUserPositionLimitRawData coin_risk_user_pos_limit_data;
  ASSERT_FALSE(coin_risk_user_pos_limit_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(coin_risk_user_pos_limit_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(coin_risk_user_pos_limit_data.parse(kCoinRiskUserPosLimitContent, msg));
  ASSERT_EQ(coin_risk_user_pos_limit_data.data.size(), 4);
  ASSERT_STREQ(coin_risk_user_pos_limit_data.data[0]->coin.c_str(), "CGG");
  ASSERT_EQ(coin_risk_user_pos_limit_data.data[0]->position_limit, bbase::decimal::Decimal<>("111111111"));
  ASSERT_EQ(coin_risk_user_pos_limit_data.data[0]->uid, 1);
  ASSERT_EQ(coin_risk_user_pos_limit_data.data[1]->position_limit, bbase::decimal::Decimal<>("-1"));
  ASSERT_EQ(coin_risk_user_pos_limit_data.data[1]->uid, 2);
  ASSERT_EQ(coin_risk_user_pos_limit_data.data[2]->position_limit,
            bbase::decimal::Decimal<>("300000.000000000000000000"));
  ASSERT_EQ(coin_risk_user_pos_limit_data.data[2]->uid, 123456);

  // MP_ETP_RISK_POSITION_LIMIT
  config::EtpRiskPositionLimitRawData etp_risk_pos_limit_data;
  ASSERT_FALSE(etp_risk_pos_limit_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(etp_risk_pos_limit_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(etp_risk_pos_limit_data.parse(kEtpRiskPosLimitContent, msg));
  ASSERT_EQ(etp_risk_pos_limit_data.data.size(), 2);
  ASSERT_STREQ(etp_risk_pos_limit_data.data[0]->coin.c_str(), "USDT");
  ASSERT_EQ(etp_risk_pos_limit_data.data[0]->position_limit, bbase::decimal::Decimal<>("2200.000000000000000000"));
  ASSERT_STREQ(etp_risk_pos_limit_data.data[0]->symbol_name.c_str(), "ETH3SUSDT");

  // MP_ETP_RISK_USER_POSITION_LIMIT
  config::EtpRiskUserPositionLimitRawData etp_risk_user_pos_limit_data;
  ASSERT_FALSE(etp_risk_user_pos_limit_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(etp_risk_user_pos_limit_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(etp_risk_user_pos_limit_data.parse(kEtpRiskUserPosLimitContent, msg));
  ASSERT_EQ(etp_risk_user_pos_limit_data.data.size(), 2);
  ASSERT_STREQ(etp_risk_user_pos_limit_data.data[0]->coin.c_str(), "USDT");
  ASSERT_EQ(etp_risk_user_pos_limit_data.data[0]->position_limit, bbase::decimal::Decimal<>("0E-18"));
  ASSERT_STREQ(etp_risk_user_pos_limit_data.data[0]->symbol_name.c_str(), "ETH3SUSDT");
  ASSERT_EQ(etp_risk_user_pos_limit_data.data[0]->uid, 4224474);

  // OPTION_MP_UM_LADDER_CONFIG
  config::UmLadderRawConfig um_ladder_raw_config;
  ASSERT_FALSE(um_ladder_raw_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(um_ladder_raw_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(um_ladder_raw_config.parse(kUmLadderContent, msg));
  ASSERT_EQ(um_ladder_raw_config.data.size(), 2);
  ASSERT_EQ(um_ladder_raw_config.data["BTC"].size(), 2);
  ASSERT_EQ(um_ladder_raw_config.data["BTC"]["0"].size(), 4);
  ASSERT_STREQ(um_ladder_raw_config.data["BTC"]["0"]["hourlyBorrowRate"].c_str(), "0.00000114200000000000");
  ASSERT_EQ(um_ladder_raw_config.data["BTC"]["1003"].size(), 3);
  ASSERT_EQ(um_ladder_raw_config.data["SOL"].size(), 1);
  ASSERT_STREQ(um_ladder_raw_config.data["SOL"]["2001"]["interestFreeCreditAmount"].c_str(), "2000");

  // MP_SPOT_PLATFORM_CONFIG
  config::SpotPlatformConfigDTO spot_platform_config;
  ASSERT_FALSE(spot_platform_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(spot_platform_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(spot_platform_config.parse(kSpotPlatformConfigContent, msg));
  ASSERT_EQ(spot_platform_config.risk_time_min, bbase::decimal::Decimal<>("1"));
  ASSERT_EQ(spot_platform_config.open_price_multiple, bbase::decimal::Decimal<>("10"));
  ASSERT_EQ(spot_platform_config.place_limit_order_limit, bbase::decimal::Decimal<>("1"));
  ASSERT_EQ(spot_platform_config.place_market_order_limit, bbase::decimal::Decimal<>("1"));

  // MP_SPOT_COUNTRY_CONFIG
  // todo

  // FAST_DUMP_CONFIG
  config::FastDumpConfig fast_dump_config;
  ASSERT_FALSE(fast_dump_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(fast_dump_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(fast_dump_config.parse(kFastDumpConfigContent, msg));
  ASSERT_STREQ(fast_dump_config.addr.c_str(), "***********:8080");

  // FILTER_HELPER_INFO
  config::FilterHelperConfig filter_helper_config;
  ASSERT_FALSE(filter_helper_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(filter_helper_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(filter_helper_config.parse(kFilterHelperConfigContent, msg));
  ASSERT_STREQ(filter_helper_config.addr.c_str(), "***********:8090");

  // FUND_POOL_BALANCE_DATA_ID
  config::FundPoolAssetData fund_pool_data;
  ASSERT_FALSE(fund_pool_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(fund_pool_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(fund_pool_data.parse(kFundPoolBalanceContent, msg));
  ASSERT_EQ(fund_pool_data.data.size(), 2);
  ASSERT_NE(fund_pool_data.data.find("SUSHI"), fund_pool_data.data.end());
  ASSERT_EQ(fund_pool_data.data["SUSHI"]->min_available_balance, bbase::decimal::Decimal<>("0"));
  ASSERT_EQ(fund_pool_data.data["SUSHI"]->available_balance, bbase::decimal::Decimal<>("600"));

  // MARGIN_SERVER_PRODUCT_DATA_ID
  config::MarginServerProductConfig margin_server_product_config;
  ASSERT_FALSE(margin_server_product_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(margin_server_product_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(margin_server_product_config.parse(kMarginServerProductContent, msg));
  ASSERT_EQ(margin_server_product_config.product_token_map.size(), 1);
  ASSERT_EQ(margin_server_product_config.product_token_map[1].size(), 5);
  ASSERT_NE(margin_server_product_config.product_token_map[1].find("BUSD"),
            margin_server_product_config.product_token_map[1].end());

  // MARGIN_SERVER_COMMON_DATA_ID
  config::MarginServerCommonConfig margin_server_common_config;
  ASSERT_FALSE(margin_server_common_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(margin_server_common_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(margin_server_common_config.parse(kMarginServerCommonContent, msg));
  ASSERT_EQ(margin_server_common_config.clearing_order_map.size(), 8);
  ASSERT_EQ(margin_server_common_config.clearing_order_map["ETH"], 2);
  ASSERT_EQ(margin_server_common_config.clearing_order_map["ERTHA"], 7);

  // UNIFY_MP_TRADE
  config::MosConfigData mos_config_data;
  ASSERT_FALSE(mos_config_data.parse(kEmptyContent, msg));
  ASSERT_FALSE(mos_config_data.parse(kInvalidContent, msg));
  ASSERT_TRUE(mos_config_data.parse(kMosConfigContent, msg));
  ASSERT_NE(mos_config_data.future_data.find("3799717"), mos_config_data.future_data.end());
  ASSERT_NE(mos_config_data.future_data["3799717"].find("ETHUSD"), mos_config_data.future_data["3799717"].end());
  ASSERT_NE(mos_config_data.option_data.find("3799717"), mos_config_data.option_data.end());
  ASSERT_NE(mos_config_data.option_data["3799717"].find("BTC"), mos_config_data.option_data["3799717"].end());

  // OPTION_MP_UM_USER_WHITE_CONFIG
  config::UmUserWhiteConfig um_user_config;
  ASSERT_FALSE(um_user_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(um_user_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(um_user_config.parse(kUmUserConfigContent, msg));
  ASSERT_EQ(um_user_config.uid_white_config_map.size(), 2);
  ASSERT_STREQ(um_user_config.uid_white_config_map["6353623"]->status.c_str(), "ACTIVE");
  ASSERT_STREQ(um_user_config.uid_white_config_map["6353623"]->gmt_modified.c_str(), "2023-09-07T03:49:43.710");
  ASSERT_STREQ(um_user_config.uid_white_config_map["6353623"]->um_coin_white_list_default_model.c_str(),
               "DEFAULT_VIP_LEVEL");
  ASSERT_EQ(um_user_config.uid_white_config_map["6353623"]->coin_limit_map.size(), 1);
  ASSERT_EQ(um_user_config.uid_white_config_map["6353623"]->coin_limit_map["USDT"], bbase::decimal::Decimal<>("10000"));
  ASSERT_EQ(um_user_config.uid_white_config_map["6355032"]->coin_limit_map.size(), 2);
  ASSERT_EQ(um_user_config.uid_white_config_map["6355032"]->coin_limit_map["USDT"], bbase::decimal::Decimal<>("20000"));
  ASSERT_EQ(um_user_config.uid_white_config_map["6355032"]->coin_limit_map["USDC"], bbase::decimal::Decimal<>("10000"));

  config::BasisHedgeUidConfig basis_hedge_uid_config;
  ASSERT_TRUE(basis_hedge_uid_config.parse("123456,122122,12313", msg));
  ASSERT_TRUE(basis_hedge_uid_config.data.find(123456) != basis_hedge_uid_config.data.end());
  ASSERT_TRUE(basis_hedge_uid_config.data.find(122122) != basis_hedge_uid_config.data.end());
  ASSERT_TRUE(basis_hedge_uid_config.data.find(122122) != basis_hedge_uid_config.data.end());
  ASSERT_TRUE(basis_hedge_uid_config.data.find(0) == basis_hedge_uid_config.data.end());

  const char* hedge_upl_coin_config_json =
      R"({"coinConfig":[{"hedgePercent":"0.95","coin":"BTC"},{"hedgePercent":"0.9","coin":"ETH"}]})";
  config::BasisHedgeCoinConfig basis_hedge_coin_config;
  ASSERT_FALSE(basis_hedge_coin_config.parse("ds", msg));
  ASSERT_TRUE(basis_hedge_coin_config.parse(hedge_upl_coin_config_json, msg));
  ASSERT_TRUE(basis_hedge_coin_config.data.find("BTC") != basis_hedge_coin_config.data.end());
  ASSERT_EQ(basis_hedge_coin_config.data.find("BTC")->second->hedge_percent, bbase::decimal::Decimal<>("0.95"));
  ASSERT_TRUE(basis_hedge_coin_config.data.find("ETH") != basis_hedge_coin_config.data.end());
  ASSERT_EQ(basis_hedge_coin_config.data.find("ETH")->second->hedge_percent, bbase::decimal::Decimal<>("0.9"));
  ASSERT_TRUE(basis_hedge_coin_config.data.find("btc") == basis_hedge_coin_config.data.end());

  // OPTION_MP_BAN_TRADE_CONFIG
  config::BanTradeConfig ban_trade_config;
  ASSERT_FALSE(ban_trade_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(ban_trade_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(ban_trade_config.parse(kBanTradeConfig, msg));
  auto ban_trade_config_iter = ban_trade_config.data.find("BTCUSDT-2-10-1");
  ASSERT_TRUE(ban_trade_config_iter != ban_trade_config.data.end());
  ASSERT_EQ(ban_trade_config_iter->second->symbol_id, 9);
  ASSERT_EQ(ban_trade_config_iter->second->side, ESide::Buy);
  ASSERT_STREQ(ban_trade_config_iter->second->symbol_name.c_str(), "BTCUSDT");
  ASSERT_STREQ(ban_trade_config_iter->second->category.c_str(), "SPOT");
  ASSERT_STREQ(ban_trade_config_iter->second->price_range.c_str(), "12.9933");
  ASSERT_EQ(ban_trade_config_iter->second->ban_trade_type.size(), 3);
  ASSERT_EQ(ban_trade_config_iter->second->ban_trade_type[0].stop_order_type, EStopOrderType::UNKNOWN);
  ASSERT_EQ(ban_trade_config_iter->second->ban_trade_type[0].order_type, EOrderType::Market);
  ASSERT_EQ(ban_trade_config_iter->second->ban_trade_type[1].stop_order_type, EStopOrderType::TakeProfitStopLoss);
  ASSERT_EQ(ban_trade_config_iter->second->ban_trade_type[1].order_type, EOrderType::Market);
  ASSERT_EQ(ban_trade_config_iter->second->ban_trade_type[2].stop_order_type, EStopOrderType::TakeProfitStopLossOCO);
  ASSERT_EQ(ban_trade_config_iter->second->ban_trade_type[2].order_type, EOrderType::Limit);

  config::BanTradeConfig ban_trade_config_invalid;
  ASSERT_TRUE(ban_trade_config_invalid.parse(kBanTradeConfigInvalid, msg));
  ASSERT_EQ(ban_trade_config_invalid.data.size(), 0);

  config::InstLoanUidConfig inst_loan_uid_config;
  ASSERT_TRUE(inst_loan_uid_config.parse(kInstLoanUidListConfig, msg));
  ASSERT_EQ(inst_loan_uid_config.data.size(), 1248);

  config::PlatformBanTradeConfig plartform_ban_trade_config;
  ASSERT_TRUE(inst_loan_uid_config.parse(kPlatformBanTradeConfig, msg));

  // SPOT_COPPER_DATA_ID
  config::SpotCopperConfig copper_config;
  ASSERT_FALSE(copper_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(copper_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(copper_config.parse(kSpotCopperConfig, msg));
  ASSERT_EQ(copper_config.data.size(), 2);
  ASSERT_EQ(copper_config.data.count(123), 1);
  ASSERT_EQ(copper_config.data.count(456), 1);

  // LISTING_OPEN_TRADE_TIME_CONFIG
  config::ListingOpenTradeConfigData listing_config;
  ASSERT_FALSE(listing_config.parse(kEmptyContent));
  ASSERT_FALSE(listing_config.parse(kInvalidContent));
  ASSERT_TRUE(listing_config.parse(kListingOpenTradeTimeConfig));
  ASSERT_EQ(listing_config.data.find("XXXUSDT"), listing_config.data.end());
  ASSERT_NE(listing_config.data.find("YYYUSDT"), listing_config.data.end());

  // EXTERNAL_MARKET_MAKERS_CONFIG
  config::ExternalMarketMakersConfig external_market_makers_config;
  ASSERT_TRUE(external_market_makers_config.parse(kEmptyContent));
  ASSERT_FALSE(external_market_makers_config.parse(kInvalidContent));
  ASSERT_TRUE(external_market_makers_config.parse(kExternalMarketMakersConfig));
  ASSERT_EQ(external_market_makers_config.data.find("XXXUSDT"), external_market_makers_config.data.end());
  ASSERT_NE(external_market_makers_config.data.find("BTCUSDT"), external_market_makers_config.data.end());

  // FUTURE_SPREAD_SYMBOL_CONFIG
  config::FutureSpreadSymbolFullRawData future_spread_symbol_config;
  ASSERT_FALSE(future_spread_symbol_config.parse(kEmptyContent, msg));
  ASSERT_FALSE(future_spread_symbol_config.parse(kInvalidContent, msg));
  ASSERT_TRUE(future_spread_symbol_config.parse(kFutureSpreadPContent, msg));
  ASSERT_EQ(future_spread_symbol_config.data.size(), 2);
  ASSERT_STREQ(future_spread_symbol_config.data[0]->base_coin.c_str(), "BTC");
  ASSERT_EQ(future_spread_symbol_config.data[0]->id, 100010);
  ASSERT_STREQ(future_spread_symbol_config.data[0]->symbol_name.c_str(), "BTCUSDT-28FEB25_BTC/USDT");
  ASSERT_TRUE(future_spread_symbol_config.data[0]->symbol_alias.empty());
  ASSERT_STREQ(future_spread_symbol_config.data[0]->quote_coin.c_str(), "USDT");
  ASSERT_EQ(future_spread_symbol_config.data[0]->contract_type, EContractType::CarryTrade);
  ASSERT_EQ(future_spread_symbol_config.data[0]->leg1_product_type, EProductType::Futures);
  ASSERT_EQ(future_spread_symbol_config.data[0]->leg1_contract_type, EContractType::LinearFutures);
  ASSERT_EQ(future_spread_symbol_config.data[0]->leg1_symbol_id, 724);
  ASSERT_EQ(future_spread_symbol_config.data[0]->leg2_product_type, EProductType::Spot);
  ASSERT_EQ(future_spread_symbol_config.data[0]->leg2_contract_type, EContractType::Spot);
  ASSERT_EQ(future_spread_symbol_config.data[0]->leg2_symbol_id, 10);
  ASSERT_EQ(future_spread_symbol_config.data[0]->cross_id, 40000);
  ASSERT_EQ(future_spread_symbol_config.data[0]->settle_time_ns, 1740726000000000000);
  ASSERT_EQ(future_spread_symbol_config.data[0]->start_full_trading_time_ns, 0);
  ASSERT_EQ(future_spread_symbol_config.data[0]->start_gray_trading_time_ns, 1740384000000000000);
  ASSERT_EQ(future_spread_symbol_config.data[0]->contract_status, EContractStatus::Trading);
  ASSERT_EQ(future_spread_symbol_config.data[0]->gray_uids.size(), 3);

  config::MarginCoinStepValueRatioConfigData margin_coin_step_ratio_config;
  margin_coin_step_ratio_config.parse(kBrokerAdminLadderRateConfig);
  ASSERT_EQ(margin_coin_step_ratio_config.coin_map.size(), 2);

  application::GlobalVarManager::Instance().site_id_ = "EU";
  config::MarginCoinStepValueRatioConfigData margin_coin_step_ratio_config1;
  margin_coin_step_ratio_config1.parse(kBrokerAdminLadderRateConfig);
  ASSERT_EQ(margin_coin_step_ratio_config1.coin_map.size(), 1);
  application::GlobalVarManager::Instance().site_id_ = "";

  config::MarginCoinStepValueRatioConfigData margin_coin_step_ratio_config2;
  ASSERT_TRUE(margin_coin_step_ratio_config2.parse(config::DEFAULT_MARGIN_COIN_STEP_VALUE_CONFIG));
  ASSERT_EQ(margin_coin_step_ratio_config2.coin_map.size(), 179);

  auto btc_step_value_cfg = margin_coin_step_ratio_config.coin_map["BTC"];
  ASSERT_EQ(btc_step_value_cfg->all_level_count, 3);
  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list.size(), 3);
  ASSERT_EQ(btc_step_value_cfg->ordered_value_ratio_map.size(), 3);
  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[0]->level, 1);
  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[0]->length, biz::BigDecimal(100));
  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[0]->length_with_rate, biz::BigDecimal(100));
  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[0]->start, biz::BigDecimal(0));
  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[0]->cum_pre_length_with_rate, biz::BigDecimal(0));

  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[1]->level, 2);
  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[1]->length, biz::BigDecimal(100));
  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[1]->length_with_rate, biz::BigDecimal(90));
  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[1]->start, biz::BigDecimal(100));
  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[1]->cum_pre_length_with_rate, biz::BigDecimal(100));

  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[2]->level, 3);
  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[2]->length, biz::BigDecimal(0));
  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[2]->length_with_rate, biz::BigDecimal(0));
  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[2]->start, biz::BigDecimal(200));
  ASSERT_EQ(btc_step_value_cfg->level_value_ratio_list[2]->cum_pre_length_with_rate, biz::BigDecimal(190));

  ASSERT_EQ(btc_step_value_cfg->GetCurrentBalanceTopLevel(biz::BigDecimal(-1)), nullptr);
  ASSERT_EQ(btc_step_value_cfg->GetCurrentBalanceTopLevel(biz::BigDecimal(0))->level, 1);
  ASSERT_EQ(btc_step_value_cfg->GetCurrentBalanceTopLevel(biz::BigDecimal(1))->level, 1);
  ASSERT_EQ(btc_step_value_cfg->GetCurrentBalanceTopLevel(biz::BigDecimal(100))->level, 2);
  ASSERT_EQ(btc_step_value_cfg->GetCurrentBalanceTopLevel(biz::BigDecimal(101))->level, 2);
  ASSERT_EQ(btc_step_value_cfg->GetCurrentBalanceTopLevel(biz::BigDecimal(200))->level, 3);
  ASSERT_EQ(btc_step_value_cfg->GetCurrentBalanceTopLevel(biz::BigDecimal(300))->level, 3);

  ASSERT_EQ(btc_step_value_cfg->CalcBalanceWithStepRate(biz::BigDecimal(50)), biz::BigDecimal(50));
  ASSERT_EQ(btc_step_value_cfg->CalcBalanceWithStepRate(biz::BigDecimal(200)), biz::BigDecimal(190));
  ASSERT_EQ(btc_step_value_cfg->CalcBalanceWithStepRate(biz::BigDecimal(300)), biz::BigDecimal(190));
  ASSERT_EQ(btc_step_value_cfg->CalcBalanceWithStepRate(biz::BigDecimal(-200)), biz::BigDecimal(-200));

  ASSERT_EQ(btc_step_value_cfg->CalcSpotQtyAvgMarginValueRate(biz::BigDecimal(-10), biz::BigDecimal(-5)),
            biz::BigDecimal(0));
  ASSERT_EQ(btc_step_value_cfg->CalcSpotQtyAvgMarginValueRate(biz::BigDecimal(-10), biz::BigDecimal(5)),
            biz::BigDecimal(1));
  ASSERT_EQ(btc_step_value_cfg->CalcSpotQtyAvgMarginValueRate(biz::BigDecimal(-10), biz::BigDecimal(10)),
            biz::BigDecimal(1));
  ASSERT_EQ(btc_step_value_cfg->CalcSpotQtyAvgMarginValueRate(biz::BigDecimal(-10), biz::BigDecimal(220)),
            biz::BigDecimal("0.909090909090909090"));
  ASSERT_EQ(btc_step_value_cfg->CalcSpotQtyAvgMarginValueRate(biz::BigDecimal(0), biz::BigDecimal(380)),
            biz::BigDecimal("0.5"));
  ASSERT_EQ(btc_step_value_cfg->CalcSpotQtyAvgMarginValueRate(biz::BigDecimal(100), biz::BigDecimal(5)),
            biz::BigDecimal("0.9"));
  ASSERT_EQ(btc_step_value_cfg->CalcSpotQtyAvgMarginValueRate(biz::BigDecimal(200), biz::BigDecimal(300)),
            biz::BigDecimal("0"));

  auto eth_step_value_cfg = margin_coin_step_ratio_config.coin_map["ETH"];
  ASSERT_EQ(eth_step_value_cfg->all_level_count, 2);
  ASSERT_EQ(eth_step_value_cfg->level_value_ratio_list.size(), 2);
  ASSERT_EQ(eth_step_value_cfg->ordered_value_ratio_map.size(), 2);
  ASSERT_EQ(eth_step_value_cfg->level_value_ratio_list[0]->level, 1);
  ASSERT_EQ(eth_step_value_cfg->level_value_ratio_list[1]->level, 2);

  ASSERT_EQ(eth_step_value_cfg->GetCurrentBalanceTopLevel(biz::BigDecimal(101))->level, 2);

  // 连续性校验失败
  config::MarginCoinStepValueRatioConfigData margin_coin_step_ratio_config_error;
  ASSERT_FALSE(margin_coin_step_ratio_config.parse(kBrokerAdminLadderRateConfigInValid));
}

TEST_F(ConfigTest, beforeInit) {
  std::string value;

  ASSERT_EQ(config::getTlsCfgMgrRaw()->fee_config_mgr()->GetAllUserFee("123", "OPTION", "BTC"), nullptr);

  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_config_svc()->pm_data(), nullptr);
  value = config::getTlsCfgMgrRaw()->margin_config_svc()->GetPmConfigkeyValue("BTC", "priceRangeInterval");

  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_config_svc()->basis_hedge_coin_config(), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_config_svc()->basis_hedge_uid_config(), nullptr);

  ASSERT_TRUE(value.empty());
  value = config::getTlsCfgMgrRaw()->margin_config_svc()->GetPmLimitKeyValue("pmMaintenanceEquity");
  ASSERT_TRUE(value.empty());
  value = config::getTlsCfgMgrRaw()->margin_config_svc()->GetPmTimeskeyValue("GetPmTimeskeyValue");
  ASSERT_TRUE(value.empty());
  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_config_svc()->margin_server_product_config(), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_config_svc()->margin_server_common_config(), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_config_svc()->GetClearingTokenOrder("token"),
            std::numeric_limits<int32_t>::max());

  ASSERT_EQ(config::getTlsCfgMgrRaw()->common_config_svc()->option_trade_data(), nullptr);
  value = config::getTlsCfgMgrRaw()->common_config_svc()->GetTradekeyValue("BTC", "orderPriceFromLastPrice");
  ASSERT_TRUE(value.empty());
  ASSERT_EQ(config::getTlsCfgMgrRaw()->common_config_svc()->GetFutureMosConfig("1111", "BTCUSDT"), nullptr);
  biz::BigDecimal min_order_size;
  biz::BigDecimal max_order_size;
  ASSERT_EQ(config::getTlsCfgMgrRaw()->common_config_svc()->GetOptionMosConfig("1111", "BTCUSDT", min_order_size,
                                                                               max_order_size),
            false);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->common_config_svc()->precision_data(), nullptr);
  value = config::getTlsCfgMgrRaw()->common_config_svc()->GetPrecisionkeyValue("BTC", "orderSize");
  ASSERT_TRUE(value.empty());
  value = config::getTlsCfgMgrRaw()->common_config_svc()->GetGlobalkeyValue("BTC", "orderSize");
  ASSERT_TRUE(value.empty());

  ASSERT_EQ(config::getTlsCfgMgrRaw()->inst_client()->acct_limit_data(), nullptr);
  value = config::getTlsCfgMgrRaw()->inst_client()->GetAcctLimitConfigUid("7111221", "maxTotalAmountOptionOrders");
  ASSERT_TRUE(value.empty());

  ASSERT_EQ(config::getTlsCfgMgrRaw()->option_symbol_svc()->symbol_full_data(), nullptr);
  value = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetDeliveryFeeRateBySymbolId(-1);
  ASSERT_TRUE(value.empty());

  ASSERT_EQ(config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName("symbol_name"), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolById(1234), nullptr);

  ASSERT_EQ(config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolList(
                std::vector<config::SymbolStatus>{config::SymbolStatus::kOffline}),
            nullptr);

  ASSERT_EQ(config::getTlsCfgMgrRaw()->user_zone_config_client()->user_zone_config_data(), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->user_zone_config_client()->GetZoneInfoByUid("123"), nullptr);

  ASSERT_EQ(config::getTlsCfgMgrRaw()->inst_client()->inst_user_rel_data(), nullptr);
  value = config::getTlsCfgMgrRaw()->inst_client()->GetUidByAid("11");
  ASSERT_TRUE(value.empty());
  value = config::getTlsCfgMgrRaw()->inst_client()->GetAidByUid("11");
  ASSERT_TRUE(value.empty());

  ASSERT_TRUE(config::getTlsCfgMgrRaw()->inst_client()->GetAcctLimitConfigUid("123", "key").empty());
  ASSERT_EQ(config::getTlsCfgMgrRaw()->inst_client()->GetAcctLimitConfig("BTC", "123"), nullptr);
  ASSERT_STREQ(config::getTlsCfgMgrRaw()->inst_client()->GetAcctLimitConfig("BTC", "123", "key").c_str(), "");

  ASSERT_EQ(config::getTlsCfgMgrRaw()->inst_client()->mm_protect_data(), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->inst_client()->GetMmpConfigByUid("BTC", "1"), nullptr);

  ASSERT_EQ(config::getTlsCfgMgrRaw()->inst_client()->risk_inst_config_data(), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->inst_client()->GetRiskInstConfigByUid("15961896"), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->inst_client()->GetInstConfigByUid("15961896"), nullptr);
  ASSERT_STREQ(config::getTlsCfgMgrRaw()->inst_client()->GetInstIdByUid("15961896").c_str(), "");
  ASSERT_EQ(config::getTlsCfgMgrRaw()->inst_client()->GetInstConfigByInstId("1234"), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->inst_client()->GetLiquidationDelayUidList(), nullptr);

  ASSERT_EQ(config::getTlsCfgMgrRaw()->fee_config_mgr()->GetAllUserFee("25984454", "FUTURE", "BTC"), nullptr);

  std::vector<std::shared_ptr<config::MarginCoinConfigDTO>> coin_config_list;
  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_coin_config_client()->QueryAllMarginCoinConfigs(1, coin_config_list),
            false);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_coin_config_client()->QueryMarginCoinConfigByCoin("BTC"), nullptr);

  ASSERT_EQ(config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolId(1, 1), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolName("Symbol", 1), nullptr);

  ASSERT_EQ(config::getTlsCfgMgrRaw()->risk_position_limit_client()->GetUserCoinRiskPositionLimit(1234, "BTC"),
            nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->risk_position_limit_client()->GetUserEtpRiskPositionLimit(1234, "BTC", "symbol"),
            nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->trustee_client()->QueryTrusteeshipByLabel("label"), nullptr);
  ASSERT_FALSE(config::getTlsCfgMgrRaw()->spot_client()->IsInCopperUidList(123));

  ASSERT_EQ(config::getTlsCfgMgrRaw()->common_config_svc()->GetListingOpenTradeConfig("UNKNOWNUSDT"), nullptr);
}

TEST_F(ConfigTest, initConfig) {
  auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  nacos_client->SetContent(config::ConstConfig::OPTION_CLIENT_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kClientConfigContentNew);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_PRECISION, config::ConstConfig::MP_DATA_GROUP,
                           kMpPrecisionContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_GLOBAL, config::ConstConfig::MP_DATA_GROUP,
                           kMpPrecisionContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_PORTFOLIO_MARGIN, config::ConstConfig::MP_DATA_GROUP,
                           kPmContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_TRADE, config::ConstConfig::MP_DATA_GROUP, kTradeContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_TRADE_USDT, config::ConstConfig::MP_DATA_GROUP,
                           kTradeUSDTContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_ACCT_LIMIT, config::ConstConfig::MP_DATA_GROUP,
                           kAcctLimitContent);
  nacos_client->SetContent(config::ConstConfig::SYMBOL_FULL_DATA_ID, config::ConstConfig::MP_DATA_GROUP,
                           kOptionSymbolContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_DELIVERYTIME, config::ConstConfig::MP_DATA_GROUP,
                           kDeliveryTimeContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_UM_GENERAL_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kUmGeneralConfigContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_UM_COIN_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kUmCoinConfigContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_UM_COIN, config::ConstConfig::MP_DATA_GROUP, kUmCoinContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_EXCHANGE_FEE_RATE, config::ConstConfig::MP_DATA_GROUP,
                           kExchangeRateContent);
  nacos_client->SetContent(config::ConstConfig::COIN_PAIR_CONFIG_DATA_ID, config::ConstConfig::MP_DATA_GROUP,
                           kCoinPairContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_USER_ZONE, config::ConstConfig::MP_DATA_GROUP,
                           kUserZoneContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_RISK_INST, config::ConstConfig::MP_DATA_GROUP,
                           kRiskInstContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_INST_USER_REL, config::ConstConfig::MP_DATA_GROUP,
                           kInstUserRelContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_INST_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kInstConfigContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_INST_USER_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kInstUserConfigContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_INST_FEE, config::ConstConfig::MP_DATA_GROUP,
                           kInstFeeContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_ALL_INST_USER_FEE, config::ConstConfig::MP_DATA_GROUP,
                           kInstUserFeeContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_INST_KPI_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kInstKpiConfigContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_MM_PROTECTION, config::ConstConfig::MP_DATA_GROUP,
                           kMmProtectContent);
  nacos_client->SetContent(config::ConstConfig::MP_COIN_INFO, config::ConstConfig::MP_DATA_GROUP, kCoinInfoContent);
  nacos_client->SetContent(config::ConstConfig::MP_MARGIN_COIN_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kMarginCoinCfgContent);
  nacos_client->SetContent(config::ConstConfig::MP_SPOT_SYMBOL, config::ConstConfig::MP_DATA_GROUP, kSpotSymbolContent);
  nacos_client->SetContent(config::ConstConfig::MP_SPOT_LOAN_SYMBOL, config::ConstConfig::MP_DATA_GROUP,
                           kSpotLoanSymbolContent);
  nacos_client->SetContent(config::ConstConfig::MP_TRUSTEESHIP_UID_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kTrusteeContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_USER_SPECIAL, config::ConstConfig::MP_DATA_GROUP,
                           kMpSpecialUserContent);
  nacos_client->SetContent(config::ConstConfig::MP_COIN_RISK_POSITION_LIMIT, config::ConstConfig::MP_DATA_GROUP,
                           kCoinRiskPosLimitContent);
  nacos_client->SetContent(config::ConstConfig::MP_COIN_RISK_USER_POSITION_LIMIT, config::ConstConfig::MP_DATA_GROUP,
                           kCoinRiskUserPosLimitContent);
  nacos_client->SetContent(config::ConstConfig::MP_ETP_RISK_POSITION_LIMIT, config::ConstConfig::MP_DATA_GROUP,
                           kEtpRiskPosLimitContent);
  nacos_client->SetContent(config::ConstConfig::MP_ETP_RISK_USER_POSITION_LIMIT, config::ConstConfig::MP_DATA_GROUP,
                           kEtpRiskUserPosLimitContent);
  nacos_client->SetContent(config::ConstConfig::ConstConfig::VIP_USER_LEVEL_PREFIX + std::string("0"),
                           config::ConstConfig::INTEREST_RATE_DATA_GROUP, kVipUserContent);
  nacos_client->SetContent(config::ConstConfig::ConstConfig::VIP_USER_LEVEL_PREFIX + std::string("1"),
                           config::ConstConfig::INTEREST_RATE_DATA_GROUP, kVipUserContent);
  nacos_client->SetContent(config::ConstConfig::ConstConfig::VIP_USER_LEVEL_PREFIX + std::string("2"),
                           config::ConstConfig::INTEREST_RATE_DATA_GROUP, kVipUserContent);
  nacos_client->SetContent(config::ConstConfig::ConstConfig::VIP_USER_LEVEL_PREFIX + std::string("3"),
                           config::ConstConfig::INTEREST_RATE_DATA_GROUP, kVipUserContent);
  nacos_client->SetContent(config::ConstConfig::ConstConfig::VIP_USER_LEVEL_PREFIX + std::string("4"),
                           config::ConstConfig::INTEREST_RATE_DATA_GROUP, kVipUserContent);
  nacos_client->SetContent(config::ConstConfig::ConstConfig::VIP_USER_LEVEL_PREFIX + std::string("5"),
                           config::ConstConfig::INTEREST_RATE_DATA_GROUP, kVipUserContent);
  nacos_client->SetContent(config::ConstConfig::ConstConfig::VIP_USER_LEVEL_PREFIX + std::string("6"),
                           config::ConstConfig::INTEREST_RATE_DATA_GROUP, kVipUserContent);
  nacos_client->SetContent(config::ConstConfig::ConstConfig::VIP_USER_LEVEL_PREFIX + std::string("7"),
                           config::ConstConfig::INTEREST_RATE_DATA_GROUP, kVipUserContent);
  nacos_client->SetContent(config::ConstConfig::ConstConfig::VIP_USER_LEVEL_PREFIX + std::string("8"),
                           config::ConstConfig::INTEREST_RATE_DATA_GROUP, kVipUserContent);
  nacos_client->SetContent(config::ConstConfig::ConstConfig::VIP_USER_LEVEL_PREFIX + std::string("9"),
                           config::ConstConfig::INTEREST_RATE_DATA_GROUP, kVipUserContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_UM_LADDER_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kUmLadderContent);
  nacos_client->SetContent(config::ConstConfig::MP_SPOT_PLATFORM_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kSpotPlatformConfigContent);
  nacos_client->SetContent(config::ConstConfig::MP_SPOT_COUNTRY_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kSpotCountryConfigContent);
  std::string fast_dump_dataid =
      config::ConstConfig::FAST_DUMP_CONFIG_PREFIX + application::GlobalVarManager::Instance().node_name() + ".rpc";
  nacos_client->SetContent(fast_dump_dataid, kUtaGroupId, kFastDumpConfigContent);
  std::string filter_helper_dataid =
      config::ConstConfig::FILTER_HELPER_INFO_PREFIX + application::GlobalVarManager::Instance().group_name();
  nacos_client->SetContent(filter_helper_dataid, kUtaGroupId, kFilterHelperConfigContent);
  nacos_client->SetContent(config::ConstConfig::FUND_POOL_BALANCE_DATA_ID, config::ConstConfig::FUND_POOL_GROUP,
                           kFundPoolBalanceContent);
  nacos_client->SetContent(config::ConstConfig::MARGIN_SERVER_PRODUCT_DATA_ID, config::ConstConfig::MARGIN_SERVER_GROUP,
                           kMarginServerProductContent);
  nacos_client->SetContent(config::ConstConfig::MARGIN_SERVER_COMMON_DATA_ID, config::ConstConfig::MARGIN_SERVER_GROUP,
                           kMarginServerCommonContent);
  nacos_client->SetContent(config::ConstConfig::UNIFY_MP_TRADE, config::ConstConfig::MP_DATA_GROUP, kMosConfigContent);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_UM_USER_WHITE_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kUmUserConfigContent);
  nacos_client->SetContent(config::ConstConfig::BASIS_HEDGE_COIN_CONFIG, config::ConstConfig::LENDING_GROUP,
                           kHedgedRatioConfig);
  nacos_client->SetContent(config::ConstConfig::BASIS_HEDGE_UID_LIST, config::ConstConfig::LENDING_GROUP,
                           kHedgedUidListConfig);
  nacos_client->SetContent(config::ConstConfig::OPTION_MP_BAN_TRADE_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kBanTradeConfig);
  nacos_client->SetContent(config::ConstConfig::INST_LOAN_UID_LIST, kUtaGroupId, kInstLoanUidListConfig);
  nacos_client->SetContent(config::ConstConfig::SPOT_COPPER_DATA_ID, config::ConstConfig::BROKER_GROUP,
                           kSpotCopperConfig);
  nacos_client->SetContent(config::ConstConfig::LISTING_OPEN_TRADE_TIME_CONFIG, config::ConstConfig::FOP_GROUP,
                           kListingOpenTradeTimeConfig);
  nacos_client->SetContent(config::ConstConfig::SPOT_FEE_RULE_CONFIG_DATA_ID,
                           config::ConstConfig::FEE_RULE_CONFIG_GROUP, kSpotFeeRuleConfig);
  nacos_client->SetContent(config::ConstConfig::BAN_TRADE_WHITE_LIST_UID_CONFIG, kUtaGroupId, "1");
  nacos_client->SetContent(config::ConstConfig::SPOT_ADVENTURE_DATA_ID, config::ConstConfig::BROKER_GROUP,
                           kSpotAdventureConfig);
  nacos_client->SetContent(config::ConstConfig::SPOT_SITE_FEE_RATE_CONFIG,
                           config::ConstConfig::BROKER_ADMIN_SERVER_GROUP, kSpotSiteFeeRuleConfig);
  nacos_client->SetContent(config::ConstConfig::SPOT_KYC_FEE_RATE_CONFIG,
                           config::ConstConfig::BROKER_ADMIN_SERVER_GROUP, kSpotKycFeeRuleConfig);
  nacos_client->SetContent(config::ConstConfig::EXTERNAL_MARKET_MAKERS_CONFIG_DATA_ID,
                           config::ConstConfig::MARKET_MAKER_GROUP, kExternalMarketMakersConfig);
  nacos_client->SetContent(config::ConstConfig::BROKER_ADMIN_LADDER_RATE,
                           config::ConstConfig::BROKER_ADMIN_SERVER_GROUP, kBrokerAdminLadderRateConfig);
  nacos_client->SetContent(config::ConstConfig::MP_INST_SPOT_RLP_DATA, config::ConstConfig::MP_DATA_GROUP,
                           kMpRLPContent);
  nacos_client->SetContent(config::ConstConfig::MP_INST_DERIVATIVES_RLP_DATA, config::ConstConfig::MP_DATA_GROUP,
                           kMpFutureRLPContent);

  nacos_client->SetContent(config::ConstConfig::MP_INST_SPOT_RLP_WHITE_LIST, config::ConstConfig::UTA_GROUP,
                           kRLPWhiteListContent);
  nacos_client->SetContent(config::ConstConfig::FUTURE_SPREAD_SYMBOL_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kFutureSpreadPContent);

  nacos_client->SetContent(config::ConstConfig::MP_SPOT_RLP_SOFT_TAKER_WHITE_DATA, config::ConstConfig::MP_DATA_GROUP,
                           kSoftTakerWhiteList);
  nacos_client->SetContent(config::ConstConfig::MP_DERIVATIVES_RLP_SOFT_TAKER_WHITE_DATA,
                           config::ConstConfig::MP_DATA_GROUP, kSoftTakerWhiteList);

  nacos_client->SetContent(config::ConstConfig::TAX_RULE_CONFIG, config::ConstConfig::TAX_CENTER_CONFIG,
                           kTaxRuleConfig);

  ASSERT_EQ(config::ConfigProxy::Instance().InitConfigCenter(), 0);
  ASSERT_EQ(config::ConfigProxy::Instance().InitConfigManager(), 0);
  bool all_config_ready = false;
  config::ConfigProxy::Instance().Start(
      [&](const event::Event::Ptr& ev) {
        LOG_INFO("pre broadcast callback. ev:{}", ev->type());
        all_config_ready = true;
      },
      []() { LOG_INFO("dummy notify app callback"); },
      [](std::vector<biz::cross_idx_t>) { LOG_INFO("dummy notify add new cross list callabck"); },
      [](std::unordered_map<biz::coin_t, biz::coin_name_t>) { LOG_INFO("dummy notify add new coin map callabck"); });
  int i = 0;
  while (++i < 1000) {
    if (all_config_ready) {
      break;
    }
    usleep(10000);
  }
  if (i >= 1000) {
    LOG_ERROR("wait config ready timeout");
  }

  // check global cross mgr after init
  const auto& global_cross_mgr = config::getTlsCfgMgrRaw()->global_cross_mgr();
  ASSERT_NE(global_cross_mgr->cross_list(), nullptr);
  ASSERT_GT(global_cross_mgr->cross_list()->size(), 0);
  ASSERT_NE(global_cross_mgr->cross_prod_map(), nullptr);
  ASSERT_GT(global_cross_mgr->cross_prod_map()->size(), 0);
  ASSERT_NE(global_cross_mgr->future_cross_symbol_map(), nullptr);
  ASSERT_GT(global_cross_mgr->future_cross_symbol_map()->size(), 0);
  ASSERT_NE(global_cross_mgr->option_cross_symbol_map(), nullptr);
  ASSERT_GT(global_cross_mgr->option_cross_symbol_map()->size(), 0);
  ASSERT_NE(global_cross_mgr->spot_cross_symbol_map(), nullptr);
  ASSERT_GT(global_cross_mgr->spot_cross_symbol_map()->size(), 0);

  auto cross_map = global_cross_mgr->GetAllCrossSymbolData();
  ASSERT_GT(cross_map->size(), 0);
  ASSERT_NE(global_cross_mgr->GetSymbolMapByProductType(EProductType::Futures), nullptr);
  ASSERT_GT(global_cross_mgr->GetSymbolMapByProductType(EProductType::Futures)->size(), 0);
  ASSERT_NE(global_cross_mgr->GetSymbolMapByProductType(EProductType::Options), nullptr);
  ASSERT_GT(global_cross_mgr->GetSymbolMapByProductType(EProductType::Futures)->size(), 0);
  ASSERT_NE(global_cross_mgr->GetSymbolMapByProductType(EProductType::Spot), nullptr);
  ASSERT_GT(global_cross_mgr->GetSymbolMapByProductType(EProductType::Spot)->size(), 0);

  ASSERT_TRUE(config::getTlsCfgMgrRaw()->margin_config_svc()->basis_hedge_coin_config() != nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_config_svc()->basis_hedge_coin_config()->data.at("BTC")->hedge_percent,
            bbase::decimal::Decimal<>("0.95"));
  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_config_svc()->basis_hedge_coin_config()->data.at("ETH")->hedge_percent,
            bbase::decimal::Decimal<>("0.9"));

  ASSERT_TRUE(config::getTlsCfgMgrRaw()->margin_config_svc()->basis_hedge_uid_config() != nullptr);
  ASSERT_TRUE(config::getTlsCfgMgrRaw()->margin_config_svc()->basis_hedge_uid_config()->data.contains(123456));
  config::ConfigProxy::Instance().initSkipTrialUidList();
  ASSERT_GT(config::ConfigProxy::Instance().skip_trial_uid_list_.size(), 0);
  ASSERT_FALSE(config::ConfigProxy::Instance().IsUserInSkipTrialList(0));
  ASSERT_TRUE(config::ConfigProxy::Instance().IsUserInSkipTrialList(13774489));

  nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             config::ConstConfig::FUTURE_SPREAD_WHITE_LIST, "1111 , 2222 ,aaa");
  config::ConfigProxy::Instance().initFutureSpreadUidList();
  ASSERT_EQ(config::ConfigProxy::Instance().future_spread_uid_list_.size(), 2);
  ASSERT_TRUE(config::ConfigProxy::Instance().IsUserInFutureSpreadList(1111));
  ASSERT_TRUE(config::ConfigProxy::Instance().IsUserInFutureSpreadList(2222));

  ASSERT_TRUE(
      config::ConfigProxy::Instance().config_mgr()->common_config_svc()->external_market_makers_config()->data.contains(
          "BTCUSDT"));
  ASSERT_TRUE(
      config::ConfigProxy::Instance().config_mgr()->common_config_svc()->external_market_makers_config()->data.contains(
          "ETHUSDT"));

  ASSERT_EQ(config::ConfigProxy::Instance()
                .config_mgr()
                ->margin_coin_config_client()
                ->QueryMarginCoinStepValueRatioConfigByCoin("BTC")
                ->all_level_count,
            3);
}

TEST_F(ConfigTest, storeClientConfig) {
  ASSERT_FALSE(config::ConfigProxy::Instance().StoreClientConfig(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().StoreClientConfig(kClientConfigContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().StoreClientConfig(kClientConfigContent));
}

TEST_F(ConfigTest, stepValueConfigReceive) {
  config::ConfigProxy::Instance().MarginCoinStepValueConfigReceiveHandler(R"({"data":[]})");
  usleep(10000);
  auto d = config::ConfigProxy::Instance()
               .config_mgr()
               ->margin_coin_config_client()
               ->margin_coin_step_value_ratio_config_data();
  ASSERT_EQ(d->coin_map.size(), 179);

  config::ConfigProxy::Instance().MarginCoinStepValueConfigReceiveHandler(R"({"data":[]})");
  d = config::ConfigProxy::Instance()
          .config_mgr()
          ->margin_coin_config_client()
          ->margin_coin_step_value_ratio_config_data();
  ASSERT_EQ(d->coin_map.size(), 179);
}

TEST_F(ConfigTest, pmConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().PmConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().PmConfigReceiveHandler(kPmContent));
  waitRecvHandler();
  std::string value = config::getTlsCfgMgrRaw()->margin_config_svc()->GetPmConfigkeyValue("BTC", "priceRangeInterval");
  ASSERT_STREQ(value.c_str(), "0.013");

  std::string pm_content =
      "{\"data\":{\"BTC\":{\"priceRangeInterval\":\"0.06\",\"volatilityRangeUp\":\"0.33\",\"optionInterestRate\":\"0\","
      "\"netShortOptionsFactor\":\"0.009\",\"volatilityRangeDown\":\"0.28\",\"deltaContingencyFactor\":\"0.005\","
      "\"vegaContingencyFactor\":\"0.005\",\"initialMarginFactor\":\"1.2\",\"baseCoin\":\"BTC\"},"
      "\"DEFAULT_COIN\":{\"priceRangeInterval\":\"0.05\",\"volatilityRangeUp\":\"0.34\",\"optionInterestRate\":\"0\","
      "\"netShortOptionsFactor\":\"0.009\",\"volatilityRangeDown\":\"0.28\",\"deltaContingencyFactor\":\"0.005\","
      "\"vegaContingencyFactor\":\"0.005\",\"initialMarginFactor\":\"1.2\",\"baseCoin\":\"BTC\"},\"PM_LIMIT\":{"
      "\"pmMaintenanceEquity\":\"500\",\"pmInitialEquity\":\"1000\"},\"PM_TIMES\":{\"pmTimesNormal\":\"10\","
      "\"extremeRange\":\"0.02\",\"extremeTime\":\"1\",\"halfHourIn\":\"1\"}},\"version\":\"727d7ff6-57aa-4e1e-893a-"
      "7265d6973cef\"}";
  ASSERT_TRUE(config::ConfigProxy::Instance().PmConfigReceiveHandler(pm_content));
  waitRecvHandler();
  value = config::getTlsCfgMgrRaw()->margin_config_svc()->GetPmConfigkeyValue("BTC", "priceRangeInterval");
  ASSERT_STREQ(value.c_str(), "0.06");
  value = config::getTlsCfgMgrRaw()->margin_config_svc()->GetPmConfigkeyValue("unknown_COIN", "priceRangeInterval");
  ASSERT_STREQ(value.c_str(), "0.05");
  value = config::getTlsCfgMgrRaw()->margin_config_svc()->GetPmConfigkeyValue("BTC", "notexist");
  ASSERT_TRUE(value.empty());
  value = config::getTlsCfgMgrRaw()->margin_config_svc()->GetPmConfigkeyValue("unknown_COIN", "notexist");
  ASSERT_TRUE(value.empty());

  value = config::getTlsCfgMgrRaw()->margin_config_svc()->GetPmLimitKeyValue("pmMaintenanceEquity");
  ASSERT_STREQ(value.c_str(), "500");
  value = config::getTlsCfgMgrRaw()->margin_config_svc()->GetPmLimitKeyValue("notexist");
  ASSERT_TRUE(value.empty());
  value = config::getTlsCfgMgrRaw()->margin_config_svc()->GetPmTimeskeyValue("pmTimesNormal");
  ASSERT_STREQ(value.c_str(), "10");
  value = config::getTlsCfgMgrRaw()->margin_config_svc()->GetPmTimeskeyValue("notexist");
  ASSERT_TRUE(value.empty());
  ASSERT_TRUE(config::ConfigProxy::Instance().PmConfigReceiveHandler(kPmContent));
}

TEST_F(ConfigTest, optionTradeConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().OptionTradeConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().OptionTradeConfigReceiveHandler(kTradeContent));
  waitRecvHandler();
  std::string value =
      config::getTlsCfgMgrRaw()->common_config_svc()->GetTradekeyValue("BTC", "orderPriceFromLastPrice");
  ASSERT_STREQ(value.c_str(), "0.03");

  std::string option_trade_content =
      "{\"data\":{\"BTC\":{\"orderPriceFromLastPrice\":\"0.05\",\"liquidationFeeRate\":\"0.002\",\"bidAskMinIv\":\"0\","
      "\"maxProportionOfDeliveryInOptionValue\":\"0.125\",\"insuranceFeeRate\":\"0.00175\",\"deviationOfMarkIV\":\"0."
      "1\",\"tickSize\":\"5\",\"entryId\":\"47\",\"markMinIv\":\"0.2\",\"bidAskMaxIv\":\"5\",\"makerFee\":\"0.0003\","
      "\"minImFactor\":\"0.1\",\"minSellBasis\":\"0.0005\",\"markMaxIv\":\"3.5\",\"liquidationFeeRatePerp\":\"0.0006\","
      "\"baseCoin\":\"BTC\",\"liquidationTarget\":\"0.9\",\"maxBuyFactor\":\"0.05\",\"minSellFactor\":\"0.01\","
      "\"takerFee\":\"0.0003\",\"minOrderSize\":\"0.01\",\"minOrderSizeIncrement\":\"0.01\","
      "\"orderPriceFromMarkPrice\":\"0.1\",\"priceDeviationPercentage\":\"0.3\",\"maxImFactor\":\"0.15\","
      "\"maxProportionOfTransactionInOrderPrice\":\"0.125\",\"mmFactor\":\"0.03\",\"minOrderPrice\":\"5\","
      "\"basicDeliveryFeeRate\":\"0.00015\",\"takeoverTrigger\":\"1.6\",\"maxOrderSize\":\"100\",\"maxOrderPrice\":"
      "\"10000000\"}},\"version\":\"b2708766-4340-4252-ad56-6cffc93339e6\"}";
  config::ConfigProxy::Instance().OptionTradeConfigReceiveHandler(option_trade_content);
  waitRecvHandler();
  value = config::getTlsCfgMgrRaw()->common_config_svc()->GetTradekeyValue("BTC", "orderPriceFromLastPrice");
  ASSERT_STREQ(value.c_str(), "0.05");
  ASSERT_TRUE(config::ConfigProxy::Instance().OptionTradeConfigReceiveHandler(kTradeContent));
}

TEST_F(ConfigTest, optionTradeUSDTConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().OptionTradeUSDTConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().OptionTradeUSDTConfigReceiveHandler(kTradeUSDTContent));
  waitRecvHandler();
  std::string value =
      config::getTlsCfgMgrRaw()->common_config_svc()->GetTradeUSDTKeyValue("DEFAULT_COIN", "liquidationFeeRateUsdt");
  ASSERT_STREQ(value.c_str(), "0.0006");
  value = config::getTlsCfgMgrRaw()->common_config_svc()->GetTradeUSDTKeyValue("DEFAULT_COIN", "insuranceFeeRateUsdt");
  ASSERT_STREQ(value.c_str(), "0.000513");
  ASSERT_TRUE(config::ConfigProxy::Instance().OptionTradeUSDTConfigReceiveHandler(kTradeUSDTContent));
}

TEST_F(ConfigTest, precisionConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().PrecisionConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().PrecisionConfigReceiveHandler(kMpPrecisionContent));
  waitRecvHandler();
  std::string value = config::getTlsCfgMgrRaw()->common_config_svc()->GetPrecisionkeyValue("BTC", "orderSize");
  ASSERT_STREQ(value.c_str(), "4");

  std::string precision_config_content =
      "{\"data\":{\"BTC\":{\"orderSize\":\"8\",\"fee\":\"8\",\"delta\":\"8\",\"positionSize\":\"4\",\"theta\":\"8\","
      "\"feeRate\":\"8\",\"equity\":\"12\",\"PNL\":\"4\",\"RPL\":\"8\",\"EDP\":\"8\",\"markPrice\":\"8\","
      "\"markPriceIV\":\"4\",\"MB\":\"12\",\"positionMM\":\"8\",\"orderPrice\":\"8\",\"positionIM\":\"8\",\"baseCoin\":"
      "\"BTC\",\"bidAskPrice\":\"8\",\"CB\":\"8\",\"AB\":\"12\",\"funding\":\"8\",\"indexPrice\":\"8\",\"bidAskIV\":"
      "\"4\",\"optionValue\":\"12\",\"p24Change\":\"8\",\"positionAvgPrice\":\"8\",\"underlying\":\"8\",\"ROI\":\"4\","
      "\"sessionRPL\":\"8\",\"sessionUPL\":\"8\",\"orderIV\":\"4\",\"p24Low\":\"8\",\"orderIM\":\"8\","
      "\"sessionAvgPrice\":\"8\",\"gamma\":\"8\",\"vega\":\"8\",\"lastPrice\":\"8\",\"p24High\":\"8\"}},\"version\":"
      "\"1\"}";
  config::ConfigProxy::Instance().PrecisionConfigReceiveHandler(precision_config_content);
  waitRecvHandler();
  value = config::getTlsCfgMgrRaw()->common_config_svc()->GetPrecisionkeyValue("BTC", "orderSize");
  ASSERT_STREQ(value.c_str(), "8");
  ASSERT_TRUE(config::ConfigProxy::Instance().PrecisionConfigReceiveHandler(kMpPrecisionContent));
}

TEST_F(ConfigTest, optionGlobalConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().OptionGlobalConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().OptionGlobalConfigReceiveHandler(kOptionalGlobalContent));
  waitRecvHandler();
  std::string value = config::getTlsCfgMgrRaw()->common_config_svc()->GetGlobalkeyValue("STRIKE", "changeReason");
  ASSERT_STREQ(value.c_str(), "123");
  value = config::getTlsCfgMgrRaw()->common_config_svc()->GetWarnLineValue("liquidationTarget");
  ASSERT_STREQ(value.c_str(), "0.9");
  ASSERT_TRUE(config::ConfigProxy::Instance().OptionGlobalConfigReceiveHandler(kOptionalGlobalContent));
}
TEST_F(ConfigTest, acctLimitReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().AcctLimitReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().AcctLimitReceiveHandler(kAcctLimitContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().InstConfigReceiveHandler(kInstConfigContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().InstUserRelReceiveHandler(kInstUserRelContent));
  waitRecvHandler();
  std::string value =
      config::getTlsCfgMgrRaw()->inst_client()->GetAcctLimitConfigUid("7111221", "maxTotalAmountOptionOrders");
  ASSERT_STREQ(value.c_str(), "100");
  std::string new_content =
      R"({"data":{"BTC":{"SMALL_INSTITUTION":{"maxTotalAmountOptionOrders":"50000","maxValueFutureOrders":"22","maxOpenOrders":"50000","maxOpenOptionOrders":"50000","maxNetShortOptionPositionSize":"500001","maxValueFuturePosition":"22","maxNumberOfContractsHeldForOneInstrument":"987123456","aidWhiteList":"3709087/3748038/13774489","whiteList":"3709083/3748036","subType":"SMALL_INSTITUTION","maxOpenFutureOrders":"12","baseCoin":"BTC"}},
      "DEFAULT_COIN":{"SMALL_INSTITUTION":{"maxValueFutureOrders":"9999","maxTotalAmountOptionOrders":"9999","maxOpenOrders":"9999","maxOpenOptionOrders":"9999","maxNetShortOptionPositionSize":"9999","maxValueFuturePosition":"9999","maxNumberOfContractsHeldForOneInstrument":"9999","aidWhiteList":"4230224","whiteList":"13774489","subType":"SMALL_INSTITUTION","baseCoin":"DEFAULT_COIN","maxOpenFutureOrders":"9999"},
      "USER_DEFAULT":{"maxValueFutureOrders":"9999","maxTotalAmountOptionOrders":"9999","maxOpenOrders":"9999","maxOpenOptionOrders":"9999","maxNetShortOptionPositionSize":"9999","maxValueFuturePosition":"9999","maxNumberOfContractsHeldForOneInstrument":"9999","subType":"USER_DEFAULT","baseCoin":"DEFAULT_COIN","maxOpenFutureOrders":"9999"}}},"version":"83921483-b444-4849-beea-6ec9da1dd574"})";
  config::ConfigProxy::Instance().AcctLimitReceiveHandler(new_content);
  waitRecvHandler();
  auto rule_map = config::getTlsCfgMgrRaw()->inst_client()->GetAcctLimitConfig("BTC", "13774489");
  ASSERT_NE(rule_map, nullptr);
  ASSERT_STREQ((*rule_map)["maxTotalAmountOptionOrders"].c_str(), "50000");
  rule_map = config::getTlsCfgMgrRaw()->inst_client()->GetAcctLimitConfig("DEFAULT_COIN", "3709087");
  ASSERT_NE(rule_map, nullptr);
  ASSERT_STREQ((*rule_map)["maxTotalAmountOptionOrders"].c_str(), "9999");
  ASSERT_STREQ(config::getTlsCfgMgrRaw()
                   ->inst_client()
                   ->GetAcctLimitConfig("DEFAULT_COIN", "3709087", "maxTotalAmountOptionOrders")
                   .c_str(),
               "9999");
  rule_map = config::getTlsCfgMgrRaw()->inst_client()->GetAcctLimitConfig("ETH", "3709087");
  ASSERT_EQ(rule_map, nullptr);
  std::string result = config::getTlsCfgMgrRaw()->inst_client()->GetAcctLimitConfig(
      "BTC", "3709083", "maxNumberOfContractsHeldForOneInstrument");
  ASSERT_STREQ(result.c_str(), "987123456");
  result = config::getTlsCfgMgrRaw()->inst_client()->GetAcctLimitConfig("BTC", "3709087",
                                                                        "maxNumberOfContractsHeldForOneInstrument");
  ASSERT_STREQ(result.c_str(), "");
  ASSERT_TRUE(config::ConfigProxy::Instance().AcctLimitReceiveHandler(kAcctLimitContent));
}

TEST_F(ConfigTest, symbolFullReceive) {
  ASSERT_TRUE(config::ConfigProxy::Instance().StoreClientConfig(kClientConfigContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().OptionTradeConfigReceiveHandler(kTradeContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().SymbolFullReceiveHandler(kOptionSymbolContent));
  waitRecvHandler();
  std::string value = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetDeliveryFeeRateBySymbolId(102311);
  ASSERT_STREQ(value.c_str(), "0.00015");
  auto dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolById(102311);
  ASSERT_NE(dto, nullptr);
  ASSERT_STREQ(dto->asset_type.c_str(), "OPTION");
  ASSERT_STREQ(dto->contract_type.c_str(), "LinearOption");
  dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName("BTC-31DEC21-40000-C");
  ASSERT_NE(dto, nullptr);
  ASSERT_STREQ(dto->asset_type.c_str(), "OPTION");
  ASSERT_STREQ(dto->contract_type.c_str(), "LinearOption");

  value = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetDeliveryFeeRateBySymbolId(102313);
  ASSERT_STREQ(value.c_str(), "0");

  dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName("BTC-12FEB23-2000-C");
  ASSERT_EQ(dto, nullptr);

  dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolById(-1);
  ASSERT_EQ(dto, nullptr);
  dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolById(-1);
  ASSERT_EQ(dto, nullptr);
  dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName("not_exist_symbol");
  ASSERT_EQ(dto, nullptr);
  dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName("not_exist_symbol");
  ASSERT_EQ(dto, nullptr);

  auto list = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolList(
      std::vector<config::SymbolStatus>{config::SymbolStatus::kOnline});
  ASSERT_EQ(list->size(), 1);
  list = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolList(
      std::vector<config::SymbolStatus>{config::SymbolStatus::kOffline});
  ASSERT_EQ(list->size(), 1);
  config::ConfigProxy::Instance().StoreClientConfig(kClientConfigContentFake);
  dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName("not_exist_symbol2");
  ASSERT_EQ(dto, nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().SymbolFullReceiveHandler(kOptionSymbolContent));
}

TEST_F(ConfigTest, deliveryTimeReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().DeliveryTimeReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().DeliveryTimeReceiveHandler(kDeliveryTimeContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->option_symbol_svc()->delivery_time_data(), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().DeliveryTimeReceiveHandler(kDeliveryTimeContent));
}

TEST_F(ConfigTest, userZoneReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().UserZoneConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().UserZoneConfigReceiveHandler(kUserZoneContent));
  waitRecvHandler();
  auto user_zone_config = config::getTlsCfgMgrRaw()->user_zone_config_client()->GetZoneInfoByUid("13774489");
  ASSERT_NE(user_zone_config, nullptr);
  ASSERT_STREQ(user_zone_config->gmt_create.c_str(), "2022-02-16T08:03:13.385");
  ASSERT_STREQ(user_zone_config->gmt_creator.c_str(), "15");
  ASSERT_TRUE(config::ConfigProxy::Instance().UserZoneConfigReceiveHandler(kUserZoneContent));
}

TEST_F(ConfigTest, umGeneralConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().UmGeneralConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().UmGeneralConfigReceiveHandler(kUmGeneralConfigContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->unified_margin_svc()->um_general_config(), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().UmGeneralConfigReceiveHandler(kUmGeneralConfigContent));
}

TEST_F(ConfigTest, umCoinReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().UmCoinReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().UmCoinReceiveHandler(kUmCoinContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->unified_margin_svc()->um_coin_data(), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().UmCoinReceiveHandler(kUmCoinContent));
}

TEST_F(ConfigTest, umCoinConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().UmCoinConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().UmCoinConfigReceiveHandler(kUmCoinConfigContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->unified_margin_svc()->um_coin_config(), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().UmCoinConfigReceiveHandler(kUmCoinConfigContent));
}

TEST_F(ConfigTest, exchangeRateReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().ExchangeRateReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().ExchangeRateReceiveHandler(kExchangeRateContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->unified_margin_svc()->exchange_rate_data(), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().ExchangeRateReceiveHandler(kExchangeRateContent));
}

TEST_F(ConfigTest, coinPairReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().CoinPairReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().CoinPairReceiveHandler(kCoinPairContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->coin_client()->coin_pair_data(), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().CoinPairReceiveHandler(kCoinPairContent));
}

TEST_F(ConfigTest, riskInstConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().RiskInstConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().RiskInstConfigReceiveHandler(kRiskInstContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().InstConfigReceiveHandler(kInstConfigContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().InstUserRelReceiveHandler(kInstUserRelContent));
  waitRecvHandler();

  auto risk_inst_config = config::getTlsCfgMgrRaw()->inst_client()->GetRiskInstConfigByUid("13774489");
  ASSERT_NE(risk_inst_config, nullptr);
  ASSERT_STREQ(risk_inst_config->gmt_create.c_str(), "2022-03-08T07:52:10.305");
  ASSERT_STREQ(risk_inst_config->gmt_creator.c_str(), "bill.wang");
  ASSERT_STREQ(risk_inst_config->inst_id.c_str(), "13774489");
  ASSERT_STREQ(config::getTlsCfgMgrRaw()->inst_client()->GetAidByUid("13774489").c_str(), "17624981");
  ASSERT_STREQ(config::getTlsCfgMgrRaw()->inst_client()->GetUidByAid("17624981").c_str(), "13774489");
  auto inst_config = config::getTlsCfgMgrRaw()->inst_client()->GetInstConfigByAid("17624981");
  // ASSERT_STREQ(inst_config->inst);
  ASSERT_TRUE(config::ConfigProxy::Instance().RiskInstConfigReceiveHandler(kRiskInstContent));
}

TEST_F(ConfigTest, instUserRelReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().InstUserRelReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().InstUserRelReceiveHandler(kInstUserRelContent));
  waitRecvHandler();

  ASSERT_STREQ(config::getTlsCfgMgrRaw()->inst_client()->GetUidByAid("17624981").c_str(), "13774489");
  ASSERT_STREQ(config::getTlsCfgMgrRaw()->inst_client()->GetAidByUid("13774489").c_str(), "17624981");
  ASSERT_TRUE(config::ConfigProxy::Instance().InstUserRelReceiveHandler(kInstUserRelContent));
}

TEST_F(ConfigTest, instConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().InstConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().InstConfigReceiveHandler(kInstFeeContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->inst_client()->inst_config_data(), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().InstConfigReceiveHandler(kInstFeeContent));
}

TEST_F(ConfigTest, instUserConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().InstUserConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().InstUserConfigReceiveHandler(kInstUserConfigContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->inst_client()->inst_user_config_data(), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().InstUserConfigReceiveHandler(kInstUserConfigContent));
}

TEST_F(ConfigTest, instFeeReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().InstFeeReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().InstFeeReceiveHandler(kInstFeeContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->inst_client()->inst_fee_data(), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().InstFeeReceiveHandler(kInstFeeContent));
}

TEST_F(ConfigTest, instUserFeeReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().AllInstUserFeeReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().AllInstUserFeeReceiveHandler(kInstUserFeeContent));
  waitRecvHandler();
  auto inst_fee_dto = config::getTlsCfgMgrRaw()->fee_config_mgr()->GetAllUserFee("25984454", "OPTION", "BTC");
  ASSERT_NE(inst_fee_dto, nullptr);
  ASSERT_STREQ(inst_fee_dto->coin_pair_id.c_str(), "1");
  ASSERT_STREQ(inst_fee_dto->owner_id.c_str(), "15961849");
  ASSERT_STREQ(inst_fee_dto->owner_type.c_str(), "INST");
  ASSERT_TRUE(config::ConfigProxy::Instance().AllInstUserFeeReceiveHandler(kInstUserFeeContent));
}

TEST_F(ConfigTest, instTypeKpiConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().InstTypeKpiConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().InstTypeKpiConfigReceiveHandler(kInstKpiConfigContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->inst_client()->inst_type_kpi_data(), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().InstTypeKpiConfigReceiveHandler(kInstKpiConfigContent));
}

TEST_F(ConfigTest, instMmpReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().InstMmpReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().InstMmpReceiveHandler(kMmProtectContent));
  waitRecvHandler();

  auto mmp_config = config::getTlsCfgMgrRaw()->inst_client()->GetMmpConfigByUid("BTC", "21826590");
  ASSERT_NE(mmp_config, nullptr);
  ASSERT_STREQ(mmp_config->coin_pair_id.c_str(), "1");
  ASSERT_STREQ(mmp_config->configurable.c_str(), "Y");
  ASSERT_TRUE(config::ConfigProxy::Instance().InstMmpReceiveHandler(kMmProtectContent));
}

TEST_F(ConfigTest, coinInfoReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().CoinInfoReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().CoinInfoReceiveHandler(kCoinInfoContent));
  waitRecvHandler();

  const char* kTmpContent =
      R"({"data":[{"accuracyLength":8,"bizType":1,"brokerId":2,"chain":"BTC","coin":"BTC","coinId":3,"coinLabel":4,"coinPlate":5,"type":6},
      {"accuracyLength":8,"bizType":1,"brokerId":2,"chain":"USDC","coin":"USDC","coinId":16,"coinLabel":2,"coinPlate":1,"type":6}],
      "version":"197cddb8-1ea9-4e74-9d1f-f727abbc0a1b"})";
  ASSERT_TRUE(config::ConfigProxy::Instance().CoinInfoReceiveHandler(kTmpContent));
  waitRecvHandler();
}

TEST_F(ConfigTest, marginCoinConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().MarginCoinConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().MarginCoinConfigReceiveHandler(kMarginCoinCfgContent));
  waitRecvHandler();
  std::vector<std::shared_ptr<config::MarginCoinConfigDTO>> coin_config_list;
  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_coin_config_client()->QueryAllMarginCoinConfigs(1, coin_config_list),
            true);
  ASSERT_EQ(coin_config_list.size(), 1);
  auto first_dto = *coin_config_list.begin();
  ASSERT_EQ(first_dto->clearing_order, 3);
  auto config_data = config::getTlsCfgMgrRaw()->margin_coin_config_client()->margin_coin_config_data();
  ASSERT_NE(config_data, nullptr);
  ASSERT_EQ(config_data->sorted_list.size(), 2);
  ASSERT_EQ(config_data->sorted_list[0]->clearing_order, 3);
  ASSERT_EQ(config_data->sorted_list[1]->clearing_order, 4);

  ASSERT_TRUE(config::ConfigProxy::Instance().MarginCoinConfigReceiveHandler(kMarginCoinCfgContent));
}

TEST_F(ConfigTest, spotSymbolReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent));
  waitRecvHandler();
  auto spot_dto = config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolId(1, 1);
  ASSERT_EQ(spot_dto, nullptr);
  spot_dto = config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolId(10, 9001);
  ASSERT_NE(spot_dto, nullptr);
  ASSERT_STREQ(spot_dto->base_coin.c_str(), "BTC");
  ASSERT_EQ(spot_dto->settle_coin_type, 1);
  ASSERT_EQ(spot_dto->cross_id, 22009);

  spot_dto = config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolName("unknownSymbol", 1);
  ASSERT_EQ(spot_dto, nullptr);
  spot_dto = config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolName("BTCUSDT", 9001);
  ASSERT_NE(spot_dto, nullptr);
  ASSERT_STREQ(spot_dto->base_coin.c_str(), "BTC");
  ASSERT_EQ(spot_dto->settle_coin_type, 1);
  auto cross_list = config::getTlsCfgMgrRaw()->global_cross_mgr()->cross_list();
  ASSERT_NE(cross_list->find(22009), cross_list->end());
  ASSERT_NE(cross_list->find(20004), cross_list->end());
  ASSERT_EQ(cross_list->find(20001), cross_list->end());
  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent));
}

TEST_F(ConfigTest, spotLoanSymbolReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().SpotLoanSymbolReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().SpotLoanSymbolReceiveHandler(kSpotLoanSymbolContent));
  waitRecvHandler();
  auto loan_data = config::getTlsCfgMgrRaw()->spot_client()->spot_loan_symbol_data();
  ASSERT_NE(loan_data, nullptr);

  ASSERT_EQ(loan_data->symbol_id_map.size(), 2);
  for (auto& [key, dto] : loan_data->symbol_id_map) {
    std::string compare_key = std::to_string(dto->symbol_id) + "_" + std::to_string(dto->broker_id);
    ASSERT_STREQ(key.c_str(), compare_key.c_str());
  }
  for (auto& [key, dto] : loan_data->symbol_map) {
    std::string compare_key = dto->symbol_name + "_" + std::to_string(dto->broker_id);
    ASSERT_STREQ(key.c_str(), compare_key.c_str());
  }
  ASSERT_EQ(loan_data->symbol_map.size(), 2);

  ASSERT_TRUE(config::ConfigProxy::Instance().SpotLoanSymbolReceiveHandler(kSpotLoanSymbolContent));
}

TEST_F(ConfigTest, trusteeUidConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().TrusteeshipUidConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().TrusteeshipUidConfigReceiveHandler(kTrusteeContent));
  waitRecvHandler();
  auto dto = config::getTlsCfgMgrRaw()->trustee_client()->QueryTrusteeshipByLabel("AIO_SYSTEM_TO_USER_RM");
  ASSERT_NE(dto, nullptr);
  ASSERT_EQ(dto->aid, 4224048432);
  ASSERT_EQ(dto->uid, 4224048430);

  dto = config::getTlsCfgMgrRaw()->trustee_client()->QueryTrusteeshipByLabel("label");
  ASSERT_EQ(dto, nullptr);

  ASSERT_TRUE(config::ConfigProxy::Instance().TrusteeshipUidConfigReceiveHandler(kTrusteeContent));
}

TEST_F(ConfigTest, userSpecialConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().UserSpecialConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().UserSpecialConfigReceiveHandler(kMpSpecialUserContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->common_config_svc()->user_special_data(), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().UserSpecialConfigReceiveHandler(kMpSpecialUserContent));
}

TEST_F(ConfigTest, coinRiskPoosLimitReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().CoinRiskPositionLimitReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().CoinRiskPositionLimitReceiveHandler(kCoinRiskPosLimitContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->risk_position_limit_client()->coin_risk_position_limit_data(), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().CoinRiskPositionLimitReceiveHandler(kCoinRiskPosLimitContent));
}

TEST_F(ConfigTest, coinRiskUserPosLimitReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().CoinRiskUserPositionLimitReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().CoinRiskUserPositionLimitReceiveHandler(kCoinRiskUserPosLimitContent));
  waitRecvHandler();
  auto decimal_ptr =
      config::getTlsCfgMgrRaw()->risk_position_limit_client()->GetUserCoinRiskPositionLimit(123456, "XRP");
  ASSERT_NE(decimal_ptr, nullptr);
  ASSERT_EQ(*decimal_ptr, bbase::decimal::Decimal<>("300000.000000000000000000"));
  ASSERT_TRUE(config::ConfigProxy::Instance().CoinRiskUserPositionLimitReceiveHandler(kCoinRiskUserPosLimitContent));
}

TEST_F(ConfigTest, etpRiskPosLimitReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().EtpRiskPositionLimitReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().EtpRiskPositionLimitReceiveHandler(kEtpRiskPosLimitContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->risk_position_limit_client()->etp_risk_position_limit_data(), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().EtpRiskPositionLimitReceiveHandler(kEtpRiskPosLimitContent));
}

TEST_F(ConfigTest, etpRiskUserPosLimitReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().EtpRiskUserPositionLimitReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().EtpRiskUserPositionLimitReceiveHandler(kEtpRiskUserPosLimitContent));
  waitRecvHandler();
  auto decimal_ptr = config::getTlsCfgMgrRaw()->risk_position_limit_client()->GetUserEtpRiskPositionLimit(
      4224475, "USDT", "ETH3SUSDT");
  ASSERT_NE(decimal_ptr, nullptr);
  ASSERT_EQ(*decimal_ptr, bbase::decimal::Decimal<>("0E-18"));

  ASSERT_TRUE(config::ConfigProxy::Instance().EtpRiskUserPositionLimitReceiveHandler(kEtpRiskUserPosLimitContent));
}

TEST_F(ConfigTest, umLadderConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().UmLadderConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().UmLadderConfigReceiveHandler(kUmLadderContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->unified_margin_svc()->um_ladder_config(), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().UmLadderConfigReceiveHandler(kUmLadderContent));
}

TEST_F(ConfigTest, spotPlatformConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().SpotPlatformConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().SpotPlatformConfigReceiveHandler(kSpotPlatformConfigContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->spot_client()->spot_platform_config_data(), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->spot_client()->spot_platform_config_data()->open_price_multiple,
            bbase::decimal::Decimal<>("10"));
  ASSERT_EQ(config::getTlsCfgMgrRaw()->spot_client()->spot_platform_config_data()->place_market_order_limit,
            bbase::decimal::Decimal<>("1"));
  ASSERT_TRUE(config::ConfigProxy::Instance().SpotPlatformConfigReceiveHandler(kSpotPlatformConfigContent));
}

TEST_F(ConfigTest, spotCountryConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().SpotCountryConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().SpotCountryConfigReceiveHandler(kSpotCountryConfigContent));
  waitRecvHandler();
  ASSERT_EQ(config::getTlsCfgMgrRaw()->spot_client()->spot_country_config_data()->alpha3_data.size(), 2);
  auto dto = config::getTlsCfgMgrRaw()->spot_client()->spot_country_config_data()->alpha3_data.at("AND");
  ASSERT_STREQ(dto->alpha2.c_str(), "AD");
  ASSERT_STREQ(dto->name_en.c_str(), "Andorra");
  ASSERT_STREQ(dto->name_zh.c_str(), "安道尔共和国");
  ASSERT_TRUE(config::ConfigProxy::Instance().SpotCountryConfigReceiveHandler(kSpotCountryConfigContent));
}

TEST_F(ConfigTest, fastDumpConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().FastDumpConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().FastDumpConfigReceiveHandler(kFastDumpConfigContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->misc_client()->fast_dump_config(), nullptr);
  ASSERT_STREQ(config::getTlsCfgMgrRaw()->misc_client()->fast_dump_config()->addr.c_str(), "***********:8080");
  ASSERT_TRUE(config::ConfigProxy::Instance().FastDumpConfigReceiveHandler(kFastDumpConfigContent));
}

TEST_F(ConfigTest, filterConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().FilterHelperInfoReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().FilterHelperInfoReceiveHandler(kFilterHelperConfigContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->misc_client()->filter_helper_config(), nullptr);
  ASSERT_STREQ(config::getTlsCfgMgrRaw()->misc_client()->filter_helper_config()->addr.c_str(), "***********:8090");
  ASSERT_TRUE(config::ConfigProxy::Instance().FilterHelperInfoReceiveHandler(kFilterHelperConfigContent));
}

TEST_F(ConfigTest, fundPoolBalanceReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().FundPoolBalanceReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().FundPoolBalanceReceiveHandler(kFundPoolBalanceContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->margin_config_svc()->fund_pool_asset_data(), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_config_svc()->fund_pool_asset_data()->data.size(), 2);
  ASSERT_EQ(
      config::getTlsCfgMgrRaw()->margin_config_svc()->fund_pool_asset_data()->data.at("QNT")->min_available_balance,
      bbase::decimal::Decimal<>("100"));
  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_config_svc()->fund_pool_asset_data()->data.at("QNT")->available_balance,
            bbase::decimal::Decimal<>("300"));
  ASSERT_EQ(
      config::getTlsCfgMgrRaw()->margin_config_svc()->fund_pool_asset_data()->data.at("QNT")->max_available_balance(),
      bbase::decimal::Decimal<>("200"));

  ASSERT_TRUE(config::ConfigProxy::Instance().FundPoolBalanceReceiveHandler(kFundPoolBalanceContent));
}

TEST_F(ConfigTest, MarginServerProductReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().MarginServerProductReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().MarginServerProductReceiveHandler(kMarginServerProductContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->margin_config_svc()->margin_server_product_config(), nullptr);
  ASSERT_FALSE(config::getTlsCfgMgrRaw()->margin_config_svc()->IsInstLoanMarginCoin(2, "USDT"));
  ASSERT_TRUE(config::getTlsCfgMgrRaw()->margin_config_svc()->IsInstLoanMarginCoin(1, "USDT"));
  ASSERT_TRUE(config::ConfigProxy::Instance().MarginServerProductReceiveHandler(kMarginServerProductContent));
}

TEST_F(ConfigTest, MarginServerCommonReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().MarginServerCommonReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().MarginServerCommonReceiveHandler(kMarginServerCommonContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->margin_config_svc()->margin_server_common_config(), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_config_svc()->GetClearingTokenOrder("USDT"), 0);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_config_svc()->GetClearingTokenOrder("ERTHA"), 7);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->margin_config_svc()->GetClearingTokenOrder("not-exist-token"),
            std::numeric_limits<int32_t>::max());
  ASSERT_TRUE(config::ConfigProxy::Instance().MarginServerCommonReceiveHandler(kMarginServerCommonContent));
}

TEST_F(ConfigTest, mosConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().MosConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().MosConfigReceiveHandler(kMosConfigContent));
  waitRecvHandler();
  biz::BigDecimal min_order_size;
  biz::BigDecimal max_order_size;
  ASSERT_EQ(config::getTlsCfgMgrRaw()->common_config_svc()->GetOptionMosConfig("3799717", "BTC", min_order_size,
                                                                               max_order_size),
            true);
  ASSERT_EQ(min_order_size, bbase::decimal::Decimal<>("200.000000"));
  ASSERT_EQ(max_order_size, bbase::decimal::Decimal<>("600.000000"));

  std::string kTradeContentTmp =
      R"({"data":{"BTC":{"orderPriceFromLastPrice":"0.03"}},"version":"32798883-f752-4222-b171-71a8ff43531f"})";
  config::ConfigProxy::Instance().OptionTradeConfigReceiveHandler(kTradeContentTmp);
  waitRecvHandler();
  ASSERT_EQ(
      config::getTlsCfgMgrRaw()->common_config_svc()->GetOptionMosConfig("1234", "BTC", min_order_size, max_order_size),
      false);

  config::ConfigProxy::Instance().OptionTradeConfigReceiveHandler(kTradeContent);
  waitRecvHandler();
  ASSERT_EQ(
      config::getTlsCfgMgrRaw()->common_config_svc()->GetOptionMosConfig("1234", "BTC", min_order_size, max_order_size),
      true);
  ASSERT_EQ(min_order_size, bbase::decimal::Decimal<>("0.01"));
  ASSERT_EQ(max_order_size, bbase::decimal::Decimal<>("50000"));

  ASSERT_NE(config::getTlsCfgMgrRaw()->common_config_svc()->GetFutureMosConfig("3799717", "ETHUSD"), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->common_config_svc()->GetFutureMosConfig("1234", "ETHUSD"), nullptr);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->common_config_svc()->GetFutureMosConfig("3799717", "UnknowSymbol"), nullptr);
  ASSERT_TRUE(config::ConfigProxy::Instance().MosConfigReceiveHandler(kMosConfigContent));
}

TEST_F(ConfigTest, configMgrAtomicUpdate) {
  ASSERT_TRUE(config::ConfigProxy::Instance().FastDumpConfigReceiveHandler(kFastDumpConfigContent));
  waitRecvHandler();
  std::thread update_thread([] {
    for (int i = 0; i < 100; ++i) {
      ASSERT_TRUE(config::ConfigProxy::Instance().FastDumpConfigReceiveHandler(kFastDumpConfigContent));
    }
  });
  std::thread get_thread([] {
    for (int i = 0; i < 100; ++i) {
      ASSERT_NE(config::getTlsCfgMgrRaw()->misc_client()->fast_dump_config(), nullptr);
      ASSERT_STREQ(config::getTlsCfgMgrRaw()->misc_client()->fast_dump_config()->addr.c_str(), "***********:8080");
    }
  });
  update_thread.join();
  get_thread.join();
}

TEST_F(ConfigTest, sendDelayedAck) {
  auto event = std::make_shared<event::SymbolConfigEvent>();
  event->set_msg_offset(1);
  auto client = bbase::object_manager::ObjectManager::GetObject<bbase::object_manager::Kafka>(
      bbase::object_manager::ObjectType::kObjectKafka);
  if (client != nullptr) {
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::ObjectType::kObjectKafka);
  }
  config::ConfigProxy::Instance().sendDelayedAck(event);
  usleep(2'500'000);
  if (client != nullptr) {
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::ObjectType::kObjectKafka, client);
  }
  auto event2 = std::make_shared<event::SymbolConfigEvent>();
  event2->set_msg_offset(2);
  config::ConfigProxy::Instance().sendDelayedAck(event2);
  usleep(2'500'000);
}

TEST_F(ConfigTest, futureSymbolConfigUpdate) {
  auto sc_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
  auto cc_mgr = std::make_shared<biz::sc::CrossConfigManager>();
  auto ev = std::make_shared<event::SymbolConfigEvent>();
  ev->set_use_current_config(false);

  biz::sc::SymbolConfigMap sc_map;
  auto sc_config1 = std::make_shared<biz::SymbolConfig>();
  sc_config1->cross_idx_ = 29;
  sc_config1->coin_name_ = "USDC";
  sc_config1->symbol_ = 24;
  sc_config1->contract_status_ = EContractStatus::Settling;
  auto sc_config2 = std::make_shared<biz::SymbolConfig>();
  sc_config2->cross_idx_ = 5;
  sc_config2->coin_name_ = "USDT";
  sc_config2->symbol_ = 5;
  sc_config2->symbol_name_ = "BTCUSDT";
  sc_map.emplace(sc_config1->symbol_, sc_config1);
  sc_map.emplace(sc_config2->symbol_, sc_config2);
  sc_mgr->set_symbol_config_map(sc_map);
  std::vector<biz::cross_idx_t> new_cross_list = {10, 11};
  sc_mgr->UpdatePendingNewCrossList(new_cross_list);
  ASSERT_EQ(sc_mgr->pending_new_cross_list_.size(), 2);
  bool notify_run = false;
  config::ConfigProxy::Instance().notify_add_cross_cb_ = [&notify_run](const std::vector<biz::cross_idx_t>&) {
    notify_run = true;
  };
  config::ConfigProxy::Instance().future_symbol_config_ready_ = true;
  application::GlobalVarManager::Instance().is_all_recovery_ = false;
  config::ConfigProxy::Instance().symbolConfigUpdateHandler(sc_mgr, cc_mgr, ev);
  auto cross_list = config::getTlsCfgMgrRaw()->global_cross_mgr()->cross_list();
  ASSERT_EQ(cross_list, nullptr);
  application::GlobalVarManager::Instance().set_is_all_recovery();
  config::ConfigProxy::Instance().symbolConfigUpdateHandler(sc_mgr, cc_mgr, ev);
  ASSERT_FALSE(config::ConfigProxy::Instance().enable_symbol_config_queue_);
  cross_list = config::getTlsCfgMgrRaw()->global_cross_mgr()->cross_list();
  ASSERT_NE(cross_list->find(5), cross_list->end());
  ASSERT_EQ(cross_list->find(29), cross_list->end());
  ASSERT_NE(cross_list->find(10), cross_list->end());
  ASSERT_NE(cross_list->find(11), cross_list->end());
  ASSERT_EQ(cross_list->size(), 3);
  auto cross_prod_map = config::getTlsCfgMgrRaw()->global_cross_mgr()->cross_prod_map();
  ASSERT_EQ(cross_prod_map->at(10), EProductType::Futures);
  ASSERT_EQ(cross_prod_map->at(11), EProductType::Futures);
  biz::cross_idx_t cross_id = 0;
  ASSERT_TRUE(config::getTlsCfgMgrRaw()->global_cross_mgr()->GetCrossIdxBySymbolName(EProductType::Futures, "BTCUSDT",
                                                                                     cross_id));
  ASSERT_EQ(cross_id, 5);
  ASSERT_TRUE(notify_run);

  // 定时器触发重放symbol config 队列时间
  std::vector<biz::cross_idx_t> new_cross_list2 = {10, 11, 12};
  auto sc_mgr2 = std::make_shared<biz::sc::SymbolConfigManager>(*sc_mgr);
  sc_mgr2->UpdatePendingNewCrossList(new_cross_list2);
  application::GlobalVarManager::Instance().is_all_recovery_ = false;
  config::ConfigProxy::Instance().enable_symbol_config_queue_ = true;
  config::ConfigProxy::Instance().setSymbolConfigQueueCheckTimer();

  config::ConfigProxy::Instance().symbolConfigUpdateHandler(sc_mgr2, cc_mgr, ev);
  cross_list = config::ConfigProxy::Instance().config_mgr()->global_cross_mgr()->cross_list();
  ASSERT_EQ(cross_list->size(), 3);
  application::GlobalVarManager::Instance().set_is_all_recovery();
  usleep(1'500'000);
  cross_list = config::ConfigProxy::Instance().config_mgr()->global_cross_mgr()->cross_list();
  ASSERT_EQ(cross_list->size(), 4);
  ASSERT_FALSE(config::ConfigProxy::Instance().enable_symbol_config_queue_);
  config::ConfigProxy::Instance().cancelTimer();
}

TEST_F(ConfigTest, umUserConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().UmUserWhiteConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().UmUserWhiteConfigReceiveHandler(kUmUserConfigContent));
  waitRecvHandler();
  ASSERT_NE(config::getTlsCfgMgrRaw()->unified_margin_svc()->um_user_white_config(), nullptr);
  auto uid_white_map = config::getTlsCfgMgrRaw()->unified_margin_svc()->um_user_white_config()->uid_white_config_map;
  ASSERT_EQ(uid_white_map.size(), 2);
  ASSERT_STREQ(uid_white_map["6353623"]->status.c_str(), "ACTIVE");
  ASSERT_EQ(uid_white_map["6353623"]->coin_limit_map["USDT"], bbase::decimal::Decimal<>("10000"));
  ASSERT_EQ(uid_white_map["6355032"]->coin_limit_map["USDT"], bbase::decimal::Decimal<>("20000"));
  ASSERT_EQ(uid_white_map["6355032"]->coin_limit_map["USDC"], bbase::decimal::Decimal<>("10000"));

  ASSERT_TRUE(config::ConfigProxy::Instance().UmUserWhiteConfigReceiveHandler(kUmUserConfigContent));
}

TEST_F(ConfigTest, futureSymbolConfigMgr) {
  auto sc_mgr_1 = std::make_shared<biz::sc::SymbolConfigManager>();
  config::ConfigProxy::Instance().config_mgr()->set_symbol_config_mgr(sc_mgr_1);
  auto sc_mgr_2 = config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr();
  ASSERT_EQ(sc_mgr_1.get(), sc_mgr_2.get());

#ifdef NDEBUG
  std::cout << "start shared ptr ref cnt test" << std::endl;
  auto ref_cnt_2 = sc_mgr_2.use_count();
  auto sc_mgr_22 = config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr();
  auto ref_cnt_22 = sc_mgr_22.use_count();
  ASSERT_EQ(ref_cnt_22, ref_cnt_2 + 1);
  const auto& sc_mgr_ref = config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr();
  auto ref_cnt = sc_mgr_ref.use_count();
  ASSERT_EQ(ref_cnt, ref_cnt_22 + 1);
  std::cout << "ref_cnt_2:" << ref_cnt_2 << " ref_cnt_22:" << ref_cnt_22 << " ref_cnt:" << ref_cnt << std::endl;
#endif
  auto sc_mgr_3 = std::make_shared<biz::sc::SymbolConfigManager>();
  config::ConfigProxy::Instance().config_mgr()->UpdateTlsScMgrAndSwitchToStickyMode(sc_mgr_3);
  auto sc_mgr_4 = config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr();
  auto sc_mgr_5 = config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr();
  ASSERT_EQ(sc_mgr_3.get(), sc_mgr_4.get());
  ASSERT_EQ(sc_mgr_3.get(), sc_mgr_5.get());
  ASSERT_NE(sc_mgr_2.get(), sc_mgr_5.get());
}

TEST_F(ConfigTest, banTradeConfigReceive) {
  ASSERT_FALSE(config::ConfigProxy::Instance().BanTradeConfigReceiveHandler(kInvalidContent));
  ASSERT_TRUE(config::ConfigProxy::Instance().BanTradeConfigReceiveHandler(kBanTradeConfig));
  waitRecvHandler();
  auto ban_trade_config = config::getTlsCfgMgrRaw()->spot_client()->ban_trade_config_data();
  ASSERT_NE(ban_trade_config, nullptr);
  auto iter = ban_trade_config->data.find("BTCUSDT-2-10-1");
  ASSERT_TRUE(iter != ban_trade_config->data.end());
  ASSERT_EQ(iter->second->symbol_id, 9);
  ASSERT_STREQ(iter->second->symbol_name.c_str(), "BTCUSDT");
  ASSERT_EQ(iter->second->ban_trade_type[0].stop_order_type, EStopOrderType::UNKNOWN);
  ASSERT_EQ(iter->second->ban_trade_type[0].order_type, EOrderType::Market);
  ASSERT_TRUE(config::ConfigProxy::Instance().BanTradeConfigReceiveHandler(kBanTradeConfig));
}

TEST_F(ConfigTest, globalCrossManager) {
  config::getTlsCfgMgrRaw()->option_symbol_svc()->set_symbol_full_data(nullptr);
  config::getTlsCfgMgrRaw()->spot_client_sptr()->set_spot_symbol_data(nullptr);
  auto cross_mgr = std::make_shared<config::GlobalCrossManager>();
  config::ConfigProxy::Instance().config_mgr()->set_global_cross_mgr(cross_mgr);
  biz::cross_idx_t cross_id = 0;
  ASSERT_FALSE(config::getTlsCfgMgrRaw()->global_cross_mgr()->GetCrossIdxBySymbolName(EProductType::Options,
                                                                                      "symbol_name", cross_id));
  ASSERT_FALSE(config::getTlsCfgMgrRaw()->global_cross_mgr()->GetCrossIdxBySymbolName(EProductType::Spot, "symbol_name",
                                                                                      cross_id));

  // update spot symbol first
  config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent);
  waitRecvHandler();
  ASSERT_EQ(config::getTlsCfgMgrRaw()->global_cross_mgr()->cross_list()->size(), 2);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->global_cross_mgr()->cross_prod_map()->size(), 2);
  ASSERT_GT(config::getTlsCfgMgrRaw()->global_cross_mgr()->spot_cross_symbol_map()->size(), 0);
  ASSERT_TRUE(
      config::getTlsCfgMgrRaw()->global_cross_mgr()->GetCrossIdxBySymbolName(EProductType::Spot, "BTCUSDT", cross_id));
  ASSERT_EQ(cross_id, 22009);

  // update option symbol
  config::ConfigProxy::Instance().SymbolFullReceiveHandler(kOptionSymbolContent);
  waitRecvHandler();
  ASSERT_EQ(config::getTlsCfgMgrRaw()->global_cross_mgr()->cross_list()->size(), 3);
  ASSERT_EQ(config::getTlsCfgMgrRaw()->global_cross_mgr()->cross_prod_map()->size(), 3);
  ASSERT_GT(config::getTlsCfgMgrRaw()->global_cross_mgr()->spot_cross_symbol_map()->size(), 0);
  ASSERT_GT(config::getTlsCfgMgrRaw()->global_cross_mgr()->option_cross_symbol_map()->size(), 0);
  ASSERT_TRUE(config::getTlsCfgMgrRaw()->global_cross_mgr()->GetCrossIdxBySymbolName(EProductType::Options,
                                                                                     "BTC-31DEC21-40000-C", cross_id));
  ASSERT_EQ(cross_id, 10005);
}

TEST_F(ConfigTest, INST_LOAN_UID_LIST) {
  ASSERT_TRUE(config::ConfigProxy::Instance().InstLoanUidListConfigReceiveHandler(""));
  waitRecvHandler();
  // std::shared_ptr<const InstLoanUidConfig>
  auto instLoanUids = config::getTlsCfgMgrRaw()->margin_config_svc()->inst_loan_uid_config();
  ASSERT_NE(instLoanUids, nullptr);
  ASSERT_EQ(instLoanUids->data.size(), 0);

  ASSERT_TRUE(config::ConfigProxy::Instance().InstLoanUidListConfigReceiveHandler(kInstLoanUidListConfig));
  waitRecvHandler();
  // std::shared_ptr<const InstLoanUidConfig>
  instLoanUids = config::getTlsCfgMgrRaw()->margin_config_svc()->inst_loan_uid_config();
  ASSERT_NE(instLoanUids, nullptr);
  ASSERT_EQ(instLoanUids->data.size(), 1248);

  ASSERT_TRUE(config::ConfigProxy::Instance().InstLoanUidListConfigReceiveHandler("111,222,333,444"));
  waitRecvHandler();
  // std::shared_ptr<const InstLoanUidConfig>
  instLoanUids = config::getTlsCfgMgrRaw()->margin_config_svc()->inst_loan_uid_config();
  ASSERT_NE(instLoanUids, nullptr);
  ASSERT_EQ(instLoanUids->data.size(), 4);
}

TEST_F(ConfigTest, MARGIN_CONFIG_walletReCalcIntervalUs) {
  auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);

  ASSERT_EQ(application::GlobalConfig::re_calc_ticker_count("g0i0"), 1);
  ASSERT_EQ(application::GlobalConfig::re_calc_interval_us(), 10000);
  nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "walletReCalcTickerCount_g0i0", "2");
  ASSERT_EQ(application::GlobalConfig::re_calc_ticker_count("g0i0"), 2);
  ASSERT_EQ(application::GlobalConfig::re_calc_ticker_count("g1i0"), 1);
  nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "walletReCalcTickerCount_g0i0", "3");
  ASSERT_EQ(application::GlobalConfig::re_calc_ticker_count("g0i0"), 3);
  ASSERT_EQ(application::GlobalConfig::re_calc_ticker_count("g0i"), 1);
}

TEST_F(ConfigTest, CONSTRUCT_ACK_DTO) {
  int64_t version = 1000;
  std::string service = "uta_engine.g0i0";
  int64_t offset = 9;

  auto ack_dto = config::ConfigProxy::ConstructSymbolConfigResultAckDTO(version, service, offset);
  ASSERT_EQ(ack_dto->version(), version);
  ASSERT_STREQ((*ack_dto->mutable_ack_service(0)).c_str(), service.c_str());
}

TEST_F(ConfigTest, SpotCopperConfig) {
  ASSERT_TRUE(config::ConfigProxy::Instance().SpotCopperConfigReceiveHandler(kSpotCopperConfig));
  waitRecvHandler();
  ASSERT_TRUE(config::getTlsCfgMgrRaw()->spot_client()->IsInCopperUidList(123));
  ASSERT_TRUE(config::getTlsCfgMgrRaw()->spot_client()->IsInCopperUidList(456));
  ASSERT_FALSE(config::getTlsCfgMgrRaw()->spot_client()->IsInCopperUidList(789));
  ASSERT_TRUE(config::ConfigProxy::Instance().SpotCopperConfigReceiveHandler(kSpotCopperConfig));
}

TEST_F(ConfigTest, listingOpenTradeTimeConfig) {
  ASSERT_TRUE(config::ConfigProxy::Instance().ListingOpenTradeConfigReceiveHandler(kListingOpenTradeTimeConfig));
  waitRecvHandler();
  ASSERT_TRUE(config::getTlsCfgMgrRaw()->common_config_svc()->GetListingOpenTradeConfig("YYYUSDT"));
  ASSERT_FALSE(config::getTlsCfgMgrRaw()->common_config_svc()->GetListingOpenTradeConfig("XXXUSDT"));
  ASSERT_EQ(config::getTlsCfgMgrRaw()->common_config_svc()->GetListingOpenTradeConfig("YYYUSDT")->open_trade_time_ms,
            4070908800000);
}

TEST_F(ConfigTest, externalMarketMakersConfig) {
  ASSERT_TRUE(config::ConfigProxy::Instance().ExternalMarketMakersConfigReceiveHandler(kExternalMarketMakersConfig));
  waitRecvHandler();
  ASSERT_TRUE(
      config::ConfigProxy::Instance().config_mgr()->common_config_svc()->external_market_makers_config()->data.contains(
          "BTCUSDT"));
  ASSERT_FALSE(
      config::ConfigProxy::Instance().config_mgr()->common_config_svc()->external_market_makers_config()->data.contains(
          "XXXUSDT"));
  auto market_makers_list =
      config::ConfigProxy::Instance().config_mgr()->common_config_svc()->external_market_makers_config()->data.at(
          "BTCUSDT");
  ASSERT_TRUE(market_makers_list.contains(1234));
  ASSERT_FALSE(market_makers_list.contains(9999));
}

TEST_F(ConfigTest, symbolConfigCache) {
  auto kafka_client = bbase::object_manager::ObjectManager::GetObject<bbase::object_manager::Kafka>(
      bbase::object_manager::ObjectType::kObjectKafka);
  bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::ObjectType::kObjectKafka);
  if (config::ConfigProxy::Instance().symbol_synchronizer_ == nullptr) {
    config::ConfigProxy::Instance().initSymbolSynchronizer();
    ASSERT_EQ(config::ConfigProxy::Instance().stopSymbolSynchronizer(), 0);
  } else {
    ASSERT_EQ(config::ConfigProxy::Instance().stopSymbolSynchronizer(), 0);
  }
  std::error_code ec;
  std::filesystem::remove_all(config::ConfigProxy::Instance().symbol_synchronizer_->cache_store_.cache_dir_, ec);
  ASSERT_EQ(config::ConfigProxy::Instance().initSymbolSynchronizer(), -1);
  config::ConfigProxy::Instance().symbol_synchronizer_->cache_store_.Init();
  std::string symbol_data{"aaaaaa"};
  config::ConfigProxy::Instance().symbol_synchronizer_->UpdateSymbolConfigCache(symbol_data);
  usleep(100'1000);
  ASSERT_EQ(config::ConfigProxy::Instance().stopSymbolSynchronizer(), 0);

  ASSERT_EQ(config::ConfigProxy::Instance().initSymbolSynchronizer(), 0);
  ASSERT_EQ(config::ConfigProxy::Instance().stopSymbolSynchronizer(), 0);

  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::ObjectType::kObjectKafka, kafka_client);
}

TEST_F(ConfigTest, registerDataid) {
  auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  nacos_client->SetContent(config::ConstConfig::OPTION_CLIENT_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kClientConfigContentNew);
  nacos_client->SetContent(config::ConstConfig::MP_SPOT_COUNTRY_CONFIG, config::ConstConfig::MP_DATA_GROUP,
                           kSpotCountryConfigContent);
  nacos_client->SetContent(config::ConstConfig::MP_INST_SPOT_RLP_DATA, config::ConstConfig::MP_DATA_GROUP,
                           kMpRLPContent);
  nacos_client->SetContent(config::ConstConfig::MP_INST_DERIVATIVES_RLP_DATA, config::ConstConfig::MP_DATA_GROUP,
                           kMpFutureRLPContent);
  nacos_client->SetContent(config::ConstConfig::MP_INST_SPOT_RLP_WHITE_LIST, config::ConstConfig::UTA_GROUP,
                           kRLPWhiteListContent);
  nacos_client->SetContent(config::ConstConfig::MP_SPOT_RLP_SOFT_TAKER_WHITE_DATA, config::ConstConfig::MP_DATA_GROUP,
                           kSoftTakerWhiteList);
  nacos_client->SetContent(config::ConstConfig::MP_DERIVATIVES_RLP_SOFT_TAKER_WHITE_DATA,
                           config::ConstConfig::MP_DATA_GROUP, kSoftTakerWhiteList);

  config::ConfigProxy::Instance().registerDataId(
      config::ConstConfig::MP_INST_SPOT_RLP_DATA, config::ConstConfig::MP_DATA_GROUP,
      &config::ConfigProxy::SpotRLPReceiveHandler, bbase::nacos_client::NacosClient::ConfigFormat::kJson);
  config::ConfigProxy::Instance().registerDataId(
      config::ConstConfig::MP_INST_DERIVATIVES_RLP_DATA, config::ConstConfig::MP_DATA_GROUP,
      &config::ConfigProxy::FutureRLPReceiveHandler, bbase::nacos_client::NacosClient::ConfigFormat::kJson);

  config::ConfigProxy::Instance().registerDataId(
      config::ConstConfig::OPTION_CLIENT_CONFIG, config::ConstConfig::MP_DATA_GROUP,
      &config::ConfigProxy::StoreClientConfig, bbase::nacos_client::NacosClient::ConfigFormat::kJson);
  config::ConfigProxy::Instance().registerDataId(
      config::ConstConfig::OPTION_CLIENT_CONFIG, config::ConstConfig::MP_DATA_GROUP,
      &config::ConfigProxy::StoreClientConfig, bbase::nacos_client::NacosClient::ConfigFormat::kJson);

  config::ConfigProxy::Instance().registerOptionalDataId(
      config::ConstConfig::MP_SPOT_COUNTRY_CONFIG, config::ConstConfig::MP_DATA_GROUP,
      &config::ConfigProxy::SpotCountryConfigReceiveHandler, bbase::nacos_client::NacosClient::ConfigFormat::kJson);
  config::ConfigProxy::Instance().registerOptionalDataId(
      config::ConstConfig::MP_SPOT_COUNTRY_CONFIG, config::ConstConfig::MP_DATA_GROUP,
      &config::ConfigProxy::SpotCountryConfigReceiveHandler, bbase::nacos_client::NacosClient::ConfigFormat::kJson);
  nacos_client->InitCache("not_exist_optional_dataid", config::ConstConfig::MP_DATA_GROUP,
                          bbase::nacos_client::NacosClient::ConfigFormat::kJson);
  config::ConfigProxy::Instance().registerOptionalDataId(
      "not_exist_optional_dataid", config::ConstConfig::MP_DATA_GROUP,
      &config::ConfigProxy::SpotCountryConfigReceiveHandler, bbase::nacos_client::NacosClient::ConfigFormat::kJson);
  nacos_client->InitCache("illegal_format_optional_dataid", config::ConstConfig::MP_DATA_GROUP,
                          bbase::nacos_client::NacosClient::ConfigFormat::kJson);
  config::ConfigProxy::Instance().registerOptionalDataId(
      "illegal_format_optional_dataid", config::ConstConfig::MP_DATA_GROUP,
      &config::ConfigProxy::SpotCountryConfigReceiveHandler, bbase::nacos_client::NacosClient::ConfigFormat::kJson);
  config::ConfigProxy::Instance().dataid_list_.insert(config::ConfigProxy::Instance().dataid_list_.end(),
                                                      config::ConfigProxy::Instance().optional_dataid_list_.begin(),
                                                      config::ConfigProxy::Instance().optional_dataid_list_.end());
  ASSERT_EQ(config::ConfigProxy::Instance().fetchConfig(), 0);

  nacos_client->InitCache("not_exist_dataid", config::ConstConfig::MP_DATA_GROUP,
                          bbase::nacos_client::NacosClient::ConfigFormat::kJson);
  config::ConfigProxy::Instance().registerDataId("not_exist_dataid", config::ConstConfig::MP_DATA_GROUP,
                                                 &config::ConfigProxy::StoreClientConfig,
                                                 bbase::nacos_client::NacosClient::ConfigFormat::kJson);
  nacos_client->InitCache("illegal_format_dataid", config::ConstConfig::MP_DATA_GROUP,
                          bbase::nacos_client::NacosClient::ConfigFormat::kJson);
  config::ConfigProxy::Instance().registerDataId("illegal_format_dataid", config::ConstConfig::MP_DATA_GROUP,
                                                 &config::ConfigProxy::StoreClientConfig,
                                                 bbase::nacos_client::NacosClient::ConfigFormat::kJson);
  ASSERT_NE(config::ConfigProxy::Instance().fetchConfig(), 0);
}

TEST_F(ConfigTest, publicConfig) {
  application::GlobalVarManager::Instance().set_public_node(false);
  application::GlobalVarManager::Instance().seq_mark_threads_ =
      application::GlobalConfig::DefaultSeqMarkThreadsForPublic() + 1;
  application::GlobalVarManager::Instance().AdjustPublicConfig();
  ASSERT_EQ(application::GlobalVarManager::Instance().seq_mark_threads_,
            application::GlobalConfig::DefaultSeqMarkThreadsForPublic() + 1);
  ASSERT_EQ(application::GlobalVarManager::Instance().orig_pre_threads_,
            application::GlobalConfig::DefaultPreThreads());
  ASSERT_EQ(application::GlobalVarManager::Instance().pre_threads_,
            application::GlobalConfig::DefaultPreThreads() + application::GlobalConfig::DefaultPreOptionsThreads() +
                application::GlobalConfig::DefaultPreFuturesThreads());
  ASSERT_EQ(application::GlobalVarManager::Instance().orig_persist_threads_,
            application::GlobalConfig::DefaultPersistThreads());
  ASSERT_EQ(application::GlobalVarManager::Instance().persist_threads_,
            application::GlobalConfig::DefaultPersistThreads() +
                application::GlobalConfig::DefaultPersistOptionsThreads() +
                application::GlobalConfig::DefaultPersistFuturesThreads());

  application::GlobalVarManager::Instance().set_public_node(true);
  application::GlobalVarManager::Instance().AdjustPublicConfig();
  ASSERT_EQ(application::GlobalVarManager::Instance().seq_mark_threads_,
            application::GlobalConfig::DefaultSeqMarkThreadsForPublic());
  ASSERT_EQ(application::GlobalVarManager::Instance().orig_pre_threads_,
            application::GlobalConfig::DefaultPreThreadsForPublic());
  ASSERT_EQ(application::GlobalVarManager::Instance().pre_threads_,
            application::GlobalConfig::DefaultPreThreadsForPublic() +
                application::GlobalConfig::DefaultPreOptionsThreads() +
                application::GlobalConfig::DefaultPreFuturesThreads());
  ASSERT_EQ(application::GlobalVarManager::Instance().orig_persist_threads_,
            application::GlobalConfig::DefaultPersistThreadsForPublic());
  ASSERT_EQ(application::GlobalVarManager::Instance().persist_threads_,
            application::GlobalConfig::DefaultPersistThreadsForPublic() +
                application::GlobalConfig::DefaultPersistOptionsThreads() +
                application::GlobalConfig::DefaultPersistFuturesThreads());
  ASSERT_EQ(application::GlobalVarManager::Instance().post_threads_,
            application::GlobalConfig::DefaultPostThreadsForPublic());
  ASSERT_EQ(application::GlobalVarManager::Instance().grpc_threads_,
            application::GlobalConfig::DefaultGrpcThreadsForPublic());
  ASSERT_EQ(application::GlobalVarManager::Instance().cross_threads_,
            application::GlobalConfig::DefaultCrossThreadsForPublic());
  ASSERT_EQ(application::GlobalVarManager::Instance().trigger_threads_,
            application::GlobalConfig::DefaultTriggerThreadsForPublic());
}

TEST_F(ConfigTest, multiThreadGet) {
  ASSERT_TRUE(config::ConfigProxy::Instance().CoinInfoReceiveHandler(kCoinInfoContent));
  waitRecvHandler();
  std::size_t nthreads = 10;
  std::vector<std::thread> threads;
  threads.reserve(nthreads);
  bool run = true;
  for (size_t i = 0; i < nthreads; i++) {
    threads.push_back(std::thread([&run] {
      while (run) {
        ASSERT_NE(config::ConfigProxy::Instance().config_mgr()->coin_client()->QueryCoinInfoByName("BTC"), nullptr);
        ASSERT_EQ(config::ConfigProxy::Instance().config_mgr()->coin_client()->QueryCoinInfoByName("BTC")->coin_id, 3);
        ASSERT_NE(config::ConfigProxy::Instance().config_mgr()->coin_client()->QueryCoinInfoById(3), nullptr);
        ASSERT_EQ(config::ConfigProxy::Instance().config_mgr()->coin_client()->QueryCoinInfoById(3)->coin_id, 3);
        usleep(10);
      }
    }));
  }
  for (int j = 0; j < 1000; j++) {
    if (j % 2 == 0) {
      ASSERT_TRUE(config::ConfigProxy::Instance().CoinInfoReceiveHandler(kCoinInfoContent));
    } else {
      ASSERT_TRUE(config::ConfigProxy::Instance().CoinInfoReceiveHandler(kCoinInfoContentTmp));
    }
    usleep(10);
  }
  run = false;
  for (size_t i = 0; i < nthreads; i++) {
    threads[i].join();
  }
}
TEST_F(ConfigTest, taxRuleConfig) {
  ASSERT_TRUE(config::ConfigProxy::Instance().TaxRuleConfigReceiveHandler(kTaxRuleConfig));
  waitRecvHandler();
  auto idnTaxRuleId = config::getTlsCfgMgrRaw()->extra_fee_config_mgr()->GetTaxRuleId(config::ConstConfig::IDN_SITE);

  ASSERT_EQ(idnTaxRuleId, 1);

  auto idnTaxRuleConfig = config::getTlsCfgMgrRaw()->extra_fee_config_mgr()->GetTaxConfigByRuleId(idnTaxRuleId);

  ASSERT_EQ(idnTaxRuleConfig->ExtraFeeType(), config::ExtraFeeRuleType::IDN_TAX);

  auto idn_tax_config = std::dynamic_pointer_cast<config::IdnTaxFeeConfig>(idnTaxRuleConfig);

  ASSERT_EQ(idn_tax_config->cfx_fee_, 22200);
  ASSERT_EQ(idn_tax_config->fiat_coin_id_, 32);
  ASSERT_EQ(idn_tax_config->tax_pph_e8_, 100000);
  ASSERT_EQ(idn_tax_config->tax_ppn_e8_, 110000);
  ASSERT_EQ(idn_tax_config->tax_rule_id_, idnTaxRuleId);

  ASSERT_TRUE(config::ConfigProxy::Instance().TaxRuleConfigReceiveHandler(kTaxRuleConfig));
}

TEST_F(ConfigTest, idn_site_config_parse) {
  // json 非法
  static constexpr const char* test_1 =
      R"({"tax_rule_list":
  {"tax_site": "IDN","fiat_coin_id": 32,"tax_rule_id": 1,"tax_ppn_e8": 110000,"tax_pph_e8": 100000,"cfx_fee_e8": 22200}],
  "tax_rule_map": {"EU": 1}
  })";
  ASSERT_EQ(config::ConfigProxy::Instance().TaxRuleConfigReceiveHandler(test_1), false);

  // 缺少字段： tax_ppn_e8
  static constexpr const char* test_2 = R"({"tax_rule_list":[
  {"tax_site": "IDN","fiat_coin_id": 32,"tax_rule_id": 1,"tax_pph_e8": 100000,"cfx_fee_e8": 22200}],
  "tax_rule_map": {"EU": 1}
  })";
  ASSERT_EQ(config::ConfigProxy::Instance().TaxRuleConfigReceiveHandler(test_2), false);

  // ID 字段类型不对
  static constexpr const char* test_3 = R"({"tax_rule_list":[
  {"tax_site": "IDN","fiat_coin_id": "32","tax_rule_id": 1,"tax_ppn_e8": 110000,"tax_pph_e8": 100000,"cfx_fee_e8": 22200}],
  "tax_rule_map": {"EU": 1}
  })";
  ASSERT_EQ(config::ConfigProxy::Instance().TaxRuleConfigReceiveHandler(test_3), false);

  // tax_rule_id 重复
  static constexpr const char* test_4 = R"({"tax_rule_list":[
  {"tax_site": "IDN","fiat_coin_id": 32,"tax_rule_id": 1,"tax_pph_e8": 100000,"cfx_fee_e8": 22200},
  {"tax_site": "IDN","fiat_coin_id": 32,"tax_rule_id": 1,"tax_pph_e8": 100000,"cfx_fee_e8": 22200}],
  "tax_rule_map": {"IDN": 2}
  })";
  ASSERT_EQ(config::ConfigProxy::Instance().TaxRuleConfigReceiveHandler(test_4), false);

  // tax_rule_map IDN 映射的规则不存在
  static constexpr const char* test_5 = R"({"tax_rule_list":[
  {"tax_site": "IDN","fiat_coin_id": 32,"tax_rule_id": 1,"tax_pph_e8": 100000,"cfx_fee_e8": 22200}],
  "tax_rule_map": {"IDN": 2}
  })";
  ASSERT_EQ(config::ConfigProxy::Instance().TaxRuleConfigReceiveHandler(test_5), false);

  // tax_rule_map IDN 重复映射
  static constexpr const char* test_6 = R"({"tax_rule_list":[
  {"tax_site": "IDN","fiat_coin_id": 32,"tax_rule_id": 1,"tax_pph_e8": 100000,"cfx_fee_e8": 22200}],
  "tax_rule_map": {"IDN":2, "IDN":2}
  })";
  ASSERT_EQ(config::ConfigProxy::Instance().TaxRuleConfigReceiveHandler(test_6), false);

  static constexpr const char* test_7 =
      R"({"tax_rule_list":[
  {"tax_site": "IDN","fiat_coin_id": 32,"tax_rule_id": 1,"tax_ppn_e8": 110000,"tax_pph_e8": 100000,"cfx_fee_e8": 22200}],
  "tax_rule_map": {"IDN": 1}
  })";

  ASSERT_EQ(config::ConfigProxy::Instance().TaxRuleConfigReceiveHandler(test_7), true);
}

TEST_F(ConfigTest, rpi_soft_taker_config) {
  // json 非法
  const char* test_1 =
      R"({"data":{"whitelistUids":[{"instId":"BRK001","parentUids":[6,7],"escrowUids":[]}],"apiBrokers":[{"brokerId":"Vq000495","brokerName": "eazybot"}]}}})";
  ASSERT_EQ(config::ConfigProxy::Instance().SpotRLPSoftTakerWhiteListHandler(test_1), false);
  ASSERT_EQ(config::ConfigProxy::Instance().FutureRLPSoftTakerWhiteListHandler(test_1), false);

  // 缺少whitelistUids
  const char* test_2 = R"({"data":{"apiBrokers":[{"brokerId":"Vq000495","brokerName": "eazybot"}]}})";
  ASSERT_EQ(config::ConfigProxy::Instance().SpotRLPSoftTakerWhiteListHandler(test_2), false);
  // 缺少apiBrokers 字段缺少
  const char* test_3 = R"({"data":{"whitelistUids":[{"instId":"BRK001","parentUids":[6,7],"escrowUids":[]}]}})";
  ASSERT_EQ(config::ConfigProxy::Instance().FutureRLPSoftTakerWhiteListHandler(test_3), false);

  // json 非数组 escrow_uids
  const char* test_4 =
      R"({"data":{"whitelistUids":[{"instId":"BRK001","parentUids":[6,7],"escrowUids":1}],"apiBrokers":[{"brokerId":"Vq000495","brokerName": "eazybot"}]}})";
  ASSERT_EQ(config::ConfigProxy::Instance().SpotRLPSoftTakerWhiteListHandler(test_4), false);
  // json 非数组 parentUids
  const char* test_5 =
      R"({"data":{"whitelistUids":[{"instId":"BRK001","parentUids":6,"escrowUids":[]}],"apiBrokers":[{"brokerId":"Vq000495","brokerName": "eazybot"}]}})";
  ASSERT_EQ(config::ConfigProxy::Instance().FutureRLPSoftTakerWhiteListHandler(test_5), false);

  // json 非数组 apiBrokers
  const char* test_6 =
      R"({"data":{"whitelistUids":[{"instId":"BRK001","parentUids":[6,7],"escrowUids":[]}],"apiBrokers":"111"}})";
  ASSERT_EQ(config::ConfigProxy::Instance().SpotRLPSoftTakerWhiteListHandler(test_6), false);
  // apiBrokers 元素类型错误
  const char* test_7 =
      R"({"data":{"whitelistUids":[{"instId":"BRK001","parentUids":[6,7],"escrowUids":[]}],"apiBrokers":[1,2,3]}})";
  ASSERT_EQ(config::ConfigProxy::Instance().FutureRLPSoftTakerWhiteListHandler(test_7), false);

  // json 只配置 escrowUids
  const char* test_8 =
      R"({"data":{"whitelistUids":[{"instId":"BRK001","escrowUids":[]}],"apiBrokers":[{"brokerId":"Vq000495","brokerName": "eazybot"}]}})";
  ASSERT_EQ(config::ConfigProxy::Instance().SpotRLPSoftTakerWhiteListHandler(test_8), false);
  ASSERT_EQ(config::ConfigProxy::Instance().FutureRLPSoftTakerWhiteListHandler(test_8), false);

  // json 正确
  const char* test_9 =
      R"({"data":{"whitelistUids":[{"instId":"BRK001","parentUids":[6,7],"escrowUids":[]}],"apiBrokers":[{"brokerId":"Vq000495","brokerName": "eazybot"}]}})";
  ASSERT_EQ(config::ConfigProxy::Instance().SpotRLPSoftTakerWhiteListHandler(test_9), true);
  ASSERT_EQ(config::ConfigProxy::Instance().FutureRLPSoftTakerWhiteListHandler(test_9), true);

  // 更新配置 json 正确
  const char* test_10 =
      R"({"data":{"whitelistUids":[{"instId":"BRK001","parentUids":[6,7],"escrowUids":[12,13]}],"apiBrokers":[{"brokerId":"Vq000495","brokerName": "eazybot"}]}})";
  ASSERT_EQ(config::ConfigProxy::Instance().SpotRLPSoftTakerWhiteListHandler(test_10), true);
  ASSERT_EQ(config::ConfigProxy::Instance().FutureRLPSoftTakerWhiteListHandler(test_10), true);
}

TEST_F(ConfigTest, parse_rpi_config) {
  std::string msg;
  // MP_INST_SPOT_RLP_DATA

  std::string invalidJson =
      R"({"data":[{"uids":[12345678,1002],"symbolIds":[1,279], {"uids":[1003,1004],"symbolIds": [279,278]}]})";
  ASSERT_FALSE(config::ConfigProxy::Instance().SpotRLPReceiveHandler(invalidJson));

  std::string kInvalidContentData =
      "{\"data\":\"InvalidContent\",\"version\":\"b54eeafb-0a91-4d16-aa74-442d2c3e104f\"}";
  ASSERT_FALSE(config::ConfigProxy::Instance().SpotRLPReceiveHandler(kInvalidContentData));

  std::string kSpotRPIConfig = R"({"data":[{"uids":[12345678,123456789],"symbolIds":"123" }]})";
  config::ConfigProxy::Instance().SpotRLPReceiveHandler(kSpotRPIConfig);

  kSpotRPIConfig = R"({"data":[{"uids":[12345678,123456789],"symbolIds":[1,279] }]})";
  config::ConfigProxy::Instance().SpotRLPReceiveHandler(kSpotRPIConfig);

  config::ConfigProxy::Instance().SpotRLPReceiveHandler(kSpotRPIConfig);
}

TEST_F(ConfigTest, nacosSignInfo) {
  application::GlobalVarManager::Instance().set_enable_nacos_verify(true);
  setenv(config::ConstConfig::RISK_SIGN_PUBLIC_KEY, "", 1);
  config::ConfigProxy::Instance().InitNacosSignInfo();
  ASSERT_FALSE(application::GlobalVarManager::Instance().enable_nacos_verify());

  application::GlobalVarManager::Instance().set_enable_nacos_verify(true);
  setenv(config::ConstConfig::RISK_SIGN_PUBLIC_KEY, "key1 , key2", 1);
  config::ConfigProxy::Instance().InitNacosSignInfo();
  ASSERT_STREQ(config::ConfigProxy::Instance().nacos_public_key_raw_.c_str(), "key1 , key2");
  ASSERT_EQ(config::ConfigProxy::Instance().nacos_public_keys_.size(), 2);
  ASSERT_STREQ(config::ConfigProxy::Instance().nacos_public_keys_[0].c_str(), "key1");
  ASSERT_STREQ(config::ConfigProxy::Instance().nacos_public_keys_[1].c_str(), "key2");

  application::GlobalVarManager::Instance().set_enable_nacos_verify(true);
  setenv(config::ConstConfig::RISK_VERIFY_NACOS_DATAIDS, "", 1);
  config::ConfigProxy::Instance().InitNacosSignInfo();
  ASSERT_FALSE(application::GlobalVarManager::Instance().enable_nacos_verify());

  application::GlobalVarManager::Instance().set_enable_nacos_verify(true);
  setenv(config::ConstConfig::RISK_VERIFY_NACOS_DATAIDS, "groupid1@dataid1 , groupid2@dataid2", 1);
  config::ConfigProxy::Instance().InitNacosSignInfo();
  ASSERT_TRUE(application::GlobalVarManager::Instance().enable_nacos_verify());
  ASSERT_TRUE(config::ConfigProxy::Instance().nacos_verify_dataids_.contains("groupid1@dataid1"));
  ASSERT_TRUE(config::ConfigProxy::Instance().nacos_verify_dataids_.contains("groupid2@dataid2"));
  ASSERT_EQ(config::ConfigProxy::Instance().nacos_verify_dataids_.size(), 2);

  setenv(config::ConstConfig::RISK_SIGN_NACOS_DATAIDS, "groupid1@dataid1,groupid2@dataid2", 1);
  config::ConfigProxy::Instance().InitNacosSignInfo();
  ASSERT_TRUE(config::ConfigProxy::Instance().nacos_sign_dataids_.contains("groupid1@dataid1"));
  ASSERT_TRUE(config::ConfigProxy::Instance().nacos_sign_dataids_.contains("groupid2@dataid2"));
  ASSERT_EQ(config::ConfigProxy::Instance().nacos_sign_dataids_.size(), 2);

  application::GlobalVarManager::Instance().set_enable_nacos_verify(false);
  ASSERT_TRUE(config::ConfigProxy::Instance().VerifySign("dataid1", "groupid1", "content", "invalid_sign"));

  application::GlobalVarManager::Instance().set_enable_nacos_verify(true);
  ASSERT_TRUE(config::ConfigProxy::Instance().VerifySign("dataid1", "groupid_not_exist", "content", "invalid_sign"));
  ASSERT_FALSE(config::ConfigProxy::Instance().VerifySign("dataid1", "groupid1", "content", "invalid_sign"));

  std::cout << "start to verify signature" << std::endl;
  setenv(config::ConstConfig::RISK_SIGN_PUBLIC_KEY, "MCowBQYDK2VwAyEA1argYDFQIeZgmLWmGSaA55zkvHqF1so+sTITF6EFJ6k=", 1);
  config::ConfigProxy::Instance().InitNacosSignInfo();
  ASSERT_TRUE(config::ConfigProxy::Instance().VerifySign(
      "dataid1", "groupid1", "risksign:1311293595:1744364450067569006:bgw",
      "Iviwa9wH2h9k0wGD7Bm0xzDeDGw/JgUtV0w5mJYB15ZW+kMDX5QPN01DWOzqsZD7iQM9Vq3Oi3XvRu4IG8t2DA=="));
}

int main(int argc, char* argv[]) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  auto client = std::make_shared<bbase::nacos_client::NacosClientImpl>("FUTURE-CONFIG", "uta", "");
  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::ObjectType::kObjectConfigCenter, client);
  // cache kv dataids
  config::ConfigProxy::Instance().InitConfigCenter();
  auto time_now = bbase::utils::Time::GetTimeNs();
  std::srand(static_cast<std::uint32_t>(time_now));
  int http_port = (rand() + time_now) % (65535 - 10000 - 1);  // NOLINT
  auto app = std::make_shared<application::App>();
  app->pipeline()->Init();
  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, app);
  monitor::Monitor::Instance().Init();
  monitor::Monitor::Instance().Start(fmt::format("localhost:{}", http_port));
  application::GlobalVarManager::Instance().set_test(true);

  testing::InitGoogleTest(&argc, argv);
  testing::InitGoogleMock(&argc, argv);

  std::int32_t ret = RUN_ALL_TESTS();

  bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::ObjectType::kObjectConfigCenter);

  monitor::Monitor::Instance().Stop();
  monitor::Monitor::Instance().Fini();

  bbase::hdts::Hdts::Fini();

  bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);

  return ret;
}
