#include "src/common/bsmodel/model/global_config.hpp"

#include <gtest/gtest.h>

#include "src/application/application.hpp"

class TestClobalConfig : public testing::Test {
 public:
  void setUp() {}
  void TearDown() override {}
  static void SetupTestSuite() {}
  static void TearDownTestSuite() {}
};

TEST_F(TestClobalConfig, testConfig) {
  ASSERT_TRUE(bsmodel::GlobalConfig::FormulaAccuracy() == bsmodel::FormulaConsts::kDefaultAccuracy);
}

TEST_F(TestClobalConfig, T1) {
  ASSERT_EQ(application::GlobalConfig::adl_calc_interval_us("node"), 60000000);

  class TmpConfigCenter : public bbase::object_manager::ConfigCenter {
   public:
    TmpConfigCenter() : ConfigCenter() {}
    ~TmpConfigCenter() override = default;

   public:
    std::list<std::string> GetStringList(const std::string& /*prefix_key*/) override { return {}; };
    std::string GetStringVar(const std::string& /*key*/, const std::string& /*default_value*/) override { return {}; };
    std::int32_t GetIntVar(const std::string& /*key*/, const std::int32_t /*default_value*/) override { return 0; };
    std::int64_t GetInt64Var(const std::string& /*key*/, const std::int64_t /*default_value*/) override { return 0; };
    bool GetBoolVar(const std::string& /*key*/, const bool /*default_value*/) override { return false; }
    bool SetStringVar(const std::string& /*key*/, const std::string& /*value*/) override { return false; };
    std::list<std::string> GetStringKeyList(const std::string& /*prefix*/) override { return {}; };
  };

  auto pointer = std::make_shared<TmpConfigCenter>();
  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::ObjectType::kObjectConfigCenter, pointer);
  ASSERT_EQ(application::GlobalConfig::adl_calc_interval_us("node"), 60000000);
}
