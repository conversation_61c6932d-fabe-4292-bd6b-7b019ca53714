#include <gtest/gtest.h>

#include "src/common/bsmodel/model/function_state.hpp"

class TestFunctionState : public testing::Test {
 public:
  void setUp() {}
  void TearDown() override {}
  static void SetupTestSuite() {}
  static void TearDownTestSuite() {}
};

TEST_F(TestFunctionState, testConstruct) {
  bsmodel::GreekResult greek_result;
  const double underlying = 32000;
  bsmodel::FunctionState function_state(greek_result, underlying);
  ASSERT_EQ(function_state.greek_result().vega(), 0);
  function_state.set_greek_result(greek_result);
  function_state.set_underlying(32000);
  greek_result.set_delta(0.5);
  ASSERT_EQ(32000, function_state.underlying());
  bsmodel::GreekResult greek_result_1 = greek_result;
  ASSERT_DOUBLE_EQ(0.5, greek_result_1.delta());

  bool eq = (greek_result == greek_result_1);
  ASSERT_TRUE(eq);
}
