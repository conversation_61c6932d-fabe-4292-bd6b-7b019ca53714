#include "src/common/bsmodel/blackscholes/option_function_factory.hpp"

#include <gtest/gtest.h>

#include "common/bsmodel/exception/invalid_enum_exception.hpp"
#include "common/bsmodel/model/call_put_flag.hpp"

class TestOptionFunctionFactory : public testing::Test {
 public:
  void setUp() {}
  void TearDown() override {}
  static void SetupTestSuite() {}
  static void TearDownTestSuite() {}
};

TEST_F(TestOptionFunctionFactory, testCreate) {
  bool has_exception = false;
  try {
    bsmodel::OptionFunctionFactory::Create((bsmodel::CallPutFlag)2, 1);
  } catch (const std::runtime_error& e) {
    has_exception = true;
  }

  ASSERT_TRUE(has_exception);

  has_exception = false;
  try {
    auto abstract_option_function = bsmodel::OptionFunctionFactory::Create(bsmodel::CallPutFlag::kCall, 1);
    ASSERT_TRUE(abstract_option_function != nullptr);
  } catch (const std::runtime_error& e) {
    has_exception = true;
  }

  ASSERT_FALSE(has_exception);

  has_exception = false;
  try {
    auto abstract_option_function = bsmodel::OptionFunctionFactory::Create(bsmodel::CallPutFlag::kPut, 1);
    ASSERT_TRUE(abstract_option_function != nullptr);
  } catch (const std::runtime_error& e) {
    has_exception = true;
  }

  ASSERT_FALSE(has_exception);
}
