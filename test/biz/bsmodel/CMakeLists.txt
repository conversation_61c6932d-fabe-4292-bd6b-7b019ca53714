find_package(GTest CONFIG REQUIRED)

file(GLOB_RECURSE TEST_SRC_FILES "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp")
file(GLOB_RECURSE SRC_FILES "${CMAKE_SOURCE_DIR}/src/common/bsmodel/*.cpp")

set(PROJECT_SRC_FILES ${TEST_SRC_FILES} ${SRC_FILES})

set(bsmodel_ut_bin bsmodel_ut.bin)

add_executable(${bsmodel_ut_bin} ${PROJECT_SRC_FILES})
target_include_directories(${bsmodel_ut_bin} PRIVATE 
    ${CMAKE_SOURCE_DIR}
    ${googletest_INCLUDE_DIR})

target_link_libraries(${bsmodel_ut_bin} PRIVATE
    GTest::gmock
    GTest::gtest
    bbase::bcommon
    proto_gen)

install(TARGETS ${bsmodel_ut_bin}
    COMPONENT ${bsmodel_ut_bin}
    PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
    RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/test/bsmodel_ut)

## make test
include(GoogleTest)
gtest_add_tests(TARGET ${bsmodel_ut_bin})
