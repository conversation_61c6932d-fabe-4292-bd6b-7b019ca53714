#include "src/common/bsmodel/exception/function_state_exception.hpp"

#include <gtest/gtest.h>

#include <stdexcept>

class TestFunctionStateException : public testing::Test {
 public:
  void setUp() {}
  void TearDown() override {}
  static void SetupTestSuite() {}
  static void TearDownTestSuite() {}
};

TEST_F(TestFunctionStateException, testStateException) {
  bsmodel::FunctionStateException function_state_exception("error state");
  ASSERT_TRUE(&function_state_exception);
}
