#include "test/biz/trade/combination/fill.hpp"

#include <bbase/common/decimal/decimal.hpp>
#include <biz_worker/utils/v5_req_converter_utils.hpp>
#include <deque>
#include <string>
#include <unordered_map>

#include "data/enum.hpp"
#include "data/type/biz_type.hpp"
#include "enums/eaccountmode/account_mode.pb.h"
#include "enums/ebizcode/biz_code.pb.h"
#include "enums/ecreatetype/create_type.pb.h"
#include "enums/ecrossstatus/cross_status.pb.h"
#include "enums/eorderstatus/order_status.pb.h"
#include "enums/etimeinforce/time_in_force.pb.h"
#include "src/data/error/error.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

int32_t ComboFillTest::SwitchMarginMode(stub& user, enums::eaccountmode::AccountMode account_mode) {
  // stub user(PmTest::te, uid);
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(user.m_uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(enums::eaction::Action::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(account_mode);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(user.m_uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }
  return 0;
}

TEST_F(ComboFillTest, maker_fill_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);
  {
    // 设置特殊费率
    SpotSetFeeRateBuilder builder("", uid, "", -50000, "maker", false, true, false);
    auto resp = user1.process(builder.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    SpotSetFeeRateBuilder builder2("", uid2, "", 20000, "taker", true, true, false);
    resp = user2.process(builder2.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }
  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  // 下单->全部成交
  {
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    // biz::size_x_t var_size;
    m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8));
    // m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size));
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);
    EXPECT_EQ(m_order_check.m_msg->user_maker_fee_rate_e8(), -50000);
    EXPECT_EQ(m_order_check.m_msg->user_taker_fee_rate_e8(), 50000);

    {
      auto m_wallet_checker = m_result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("1"));
    }

    // taker
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, qty);
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    ASSERT_EQ(spot_related_result.size(), 3);
    ASSERT_EQ(future_related_result.size(), 3);

    // taker new accept
    auto t_com_order_check0 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check0.m_msg, nullptr);
    auto t_leg2_order_id = t_com_order_check0.m_msg->leg2_order_id();
    auto t_spot_order_check0 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
    ASSERT_NE(t_spot_order_check0.m_msg, nullptr);
    t_spot_order_check0.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("1", "9950");
    EXPECT_EQ(t_spot_order_check0.m_msg->user_maker_fee_rate_e8(), 50000);
    EXPECT_EQ(t_spot_order_check0.m_msg->user_taker_fee_rate_e8(), 10000);

    {
      auto t_wallet_checker = spot_related_result[0].RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
      t_wallet_checker.CheckCoin(ECoin::USDT);
      t_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("9950"));
    }

    auto t_leg1_order_id = t_com_order_check0.m_msg->leg1_order_id();
    auto t_future_order_check0 =
        spot_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
    ASSERT_NE(t_future_order_check0.m_msg, nullptr);
    t_future_order_check0.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // taker fill
    auto t_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check1.m_msg, nullptr);
    auto t_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
    ASSERT_NE(t_spot_order_check1.m_msg, nullptr);
    t_spot_order_check1.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill)
        .CheckLeaves("0", "0")
        .CheckExecResult(uid2, "9950", "1", "9950", "0.0001");

    {
      auto t_wallet_checker = spot_related_result[1].RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
      t_wallet_checker.CheckCoin(ECoin::USDT);
      t_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }

    auto t_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
    ASSERT_NE(t_future_order_check1.m_msg, nullptr);
    t_future_order_check1.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // maker fill
    auto m_com_order_check2 =
        spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check2.m_msg, nullptr);
    auto m_leg2_order_id = m_com_order_check2.m_msg->leg2_order_id();
    auto m_spot_order_check2 = spot_related_result[2].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
    m_spot_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill)
        .CheckLeaves("0", "0")
        .CheckExecResult(uid, "9950", "1", "9950", "-0.0001");

    {
      auto m_wallet_checker = spot_related_result[2].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }

    auto m_leg1_order_id = m_com_order_check2.m_msg->leg1_order_id();
    auto m_future_order_check2 =
        future_related_result[2].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check2.m_msg, nullptr);
    EXPECT_EQ(m_future_order_check2.m_msg->user_id(), uid);
    EXPECT_EQ(m_future_order_check2.m_msg->side(), ESide::Buy);
    EXPECT_EQ(m_future_order_check2.m_msg->qty_x(), 100000000);
    EXPECT_EQ(m_future_order_check2.m_msg->price_x(), 100500000);
    EXPECT_EQ(m_future_order_check2.m_msg->exec_qty_x(), 100000000);
    EXPECT_EQ(m_future_order_check2.m_msg->exec_type(), EExecType::FutureSpread);
    EXPECT_EQ(m_future_order_check2.m_msg->exec_price_x(), 100500000);
    EXPECT_EQ(m_future_order_check2.m_msg->last_liquidity_ind(), ELastLiquidityInd::AddedLiquidity);
    EXPECT_EQ(m_future_order_check2.m_msg->cross_status(), ECrossStatus::MakerFill);
    EXPECT_EQ(m_future_order_check2.m_msg->order_status(), EOrderStatus::Filled);

    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Filled, ECombCrossStatus::MakerFill);
  }
}
TEST_F(ComboFillTest, maker_partfill_fill_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "2";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  // 下单->部分成交->全部成交
  {
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    // biz::size_x_t var_size;
    m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8));
    // m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size));
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);

    // taker
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, "1");
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;
    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    // taker new accept
    auto t_com_order_check0 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check0.m_msg, nullptr);
    auto t_leg2_order_id = t_com_order_check0.m_msg->leg2_order_id();
    auto t_spot_order_check0 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
    ASSERT_NE(t_spot_order_check0.m_msg, nullptr);
    t_spot_order_check0.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    auto t_leg1_order_id = t_com_order_check0.m_msg->leg1_order_id();
    auto t_future_order_check0 =
        spot_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
    ASSERT_NE(t_future_order_check0.m_msg, nullptr);
    t_future_order_check0.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // taker fill
    auto t_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check1.m_msg, nullptr);
    auto t_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
    ASSERT_NE(t_spot_order_check1.m_msg, nullptr);
    t_spot_order_check1.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);
    auto t_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check1.m_msg, nullptr);
    auto t_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
    ASSERT_NE(t_future_order_check1.m_msg, nullptr);
    t_future_order_check1.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // maker partial fill
    auto m_com_order_check2 =
        spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check2.m_msg, nullptr);
    auto m_leg2_order_id = m_com_order_check2.m_msg->leg2_order_id();
    auto m_spot_order_check2 = spot_related_result[2].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
    m_spot_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill).CheckLeaves("1", "9950");

    {
      auto m_wallet_checker = spot_related_result[2].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("1"));
    }

    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::PartiallyFilled, ECombCrossStatus::MakerFill);

    spot_related_result.clear();

    auto m_future_com_order_check2 =
        future_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check2.m_msg, nullptr);
    auto m_leg1_order_id = m_future_com_order_check2.m_msg->leg1_order_id();
    auto m_future_order_check2 =
        future_related_result[2].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check2.m_msg, nullptr);
    m_future_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);

    future_related_result.clear();

    // taker 继续下单
    biz::order_link_id_t t_order_link_id1{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build1(symbol, t_order_link_id1, opp_side, order_type, time_in_force, price,
                                               "1");
    RpcContext t_ct1;
    t_resp = user2.comb_siteapi_create_order(t_create_build1.Build(), t_ct1);

    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    ASSERT_EQ(spot_related_result.size(), 3);
    ASSERT_EQ(future_related_result.size(), 3);

    // maker full fill
    m_com_order_check2 = spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check2.m_msg, nullptr);
    // m_leg2_order_id = m_com_order_check2.m_msg->leg2_order_id();
    m_spot_order_check2 = spot_related_result[2].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
    m_spot_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill).CheckLeaves("0", "0");

    {
      auto m_wallet_checker = spot_related_result[2].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }

    m_future_com_order_check2 =
        future_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check2.m_msg, nullptr);
    // m_leg2_order_id = m_com_order_check2.m_msg->leg2_order_id();
    m_future_order_check2 = future_related_result[2].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check2.m_msg, nullptr);
    m_future_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);

    m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Filled, ECombCrossStatus::MakerFill);
  }
}

TEST_F(ComboFillTest, maker_cancel_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "2";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  // 下单->撤单
  {
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    // biz::size_x_t var_size;
    m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8));
    // m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size));
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);

    // maker 撤单
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::ErrorCode::kErrorCodeSuccess);
    auto m_cancel_result = te->PopResult();
    ASSERT_NE(m_cancel_result.m_msg.get(), nullptr);
    auto m_com_order_check_last = m_cancel_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Cancelled, ECombCrossStatus::Canceled);
    auto m_leg2_order_id = m_com_order_check_last.m_msg->leg2_order_id();
    auto m_spot_order_check2 = m_cancel_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
    m_spot_order_check2.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled).CheckLeaves("2", "19900");

    {
      auto m_wallet_checker = m_cancel_result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }
  }
}

TEST_F(ComboFillTest, maker_partialfill_cancel_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "2";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  // 下单->部分成交->撤单
  {
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    // biz::size_x_t var_size;
    m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8));
    // m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size));
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);

    // taker fill
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, "1");
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      }
    }

    // maker 撤单
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::ErrorCode::kErrorCodeSuccess);
    auto m_cancel_result = te->PopResult();
    ASSERT_NE(m_cancel_result.m_msg.get(), nullptr);
    auto m_com_order_check_last = m_cancel_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Cancelled, ECombCrossStatus::Canceled);
    auto m_leg2_order_id = m_com_order_check_last.m_msg->leg2_order_id();
    auto m_spot_order_check2 = m_cancel_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
    m_spot_order_check2.CheckStatus(EOrderStatus::PartiallyFilledCanceled, ECrossStatus::Canceled)
        .CheckLeaves("1", "9950")
        .CheckExecResult(uid, "9950", "1", "9950", "0");

    {
      auto m_wallet_checker = m_cancel_result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }

    auto m_leg1_order_id = m_com_order_check_last.m_msg->leg1_order_id();
    auto m_future_order_check2 = m_cancel_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check2.m_msg, nullptr);
    ASSERT_NE(m_future_order_check2.m_msg, nullptr);
    EXPECT_EQ(m_future_order_check2.m_msg->user_id(), uid);
    EXPECT_EQ(m_future_order_check2.m_msg->side(), ESide::Buy);
    EXPECT_EQ(m_future_order_check2.m_msg->qty_x(), 200000000);
    EXPECT_EQ(m_future_order_check2.m_msg->price_x(), 100500000);
    EXPECT_EQ(m_future_order_check2.m_msg->exec_qty_x(), 100000000);
    EXPECT_EQ(m_future_order_check2.m_msg->exec_type(), EExecType::Cancel);
    EXPECT_EQ(m_future_order_check2.m_msg->exec_price_x(), 100500000);
    EXPECT_EQ(m_future_order_check2.m_msg->cross_status(), ECrossStatus::Canceled);
    EXPECT_EQ(m_future_order_check2.m_msg->order_status(), EOrderStatus::Cancelled);
  }
  // 组合撮合 PendingNew->组合撮合 NewAccept->组合撮合 MakerFill(OS: PartialFilled)->组合撮合撤单失败
  {
    te->SetCrossSingleStepMode();
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;

    // 从组合撮合收NewAccept
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    auto m_leg2_order_id = m_order_check.m_msg->leg2_order_id();
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    // biz::size_x_t var_size;
    m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8));
    // m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size));
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price).CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // taker fill
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, "1");
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    te->ProcessOneXReq(te->GetCombCrossIdx());
    // taker waitingtosetfill
    auto t_result = te->PopResult();
    ASSERT_NE(t_result.m_msg.get(), nullptr);
    // std::string res_str;
    //   std::cout << "res_str:" << utils::PbMessageToJsonString(*t_result.m_msg.get(), &res_str) << '\n';
    auto t_order_check = t_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    auto t_leg2_order_id = t_order_check.m_msg->leg2_order_id();
    t_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    // maker waitingtosetfill
    te->ProcessOneXReq(te->GetCombCrossIdx());
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    // maker 撤单
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), 110114);

    te->UnSetCrossSingleStepMode();
  }
}

TEST_F(ComboFillTest, maker_pending_cancel_partialfill_cancel_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "2";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));

  // 组合撮合 PendingNew->组合撮合 NewAccept->组合撮合 PendingCancel->组合撮合 MakerFill(OS:
  // PartialFilled)->现货撮合透传 MakerFill(OS: Filled)->组合撮合 Canceled(OS:PartiallyFilledCanceled)
  {
    // te->SetCrossSingleStepMode();
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;

    // 从组合撮合收NewAccept
    // te->ProcessOneXReq(te->GetCombCrossIdx());
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    auto m_leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_leg1_order_id = m_order_check.m_msg->leg1_order_id();

    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    // biz::size_x_t var_size;
    m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8));
    // m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size));
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price).CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    te->SuspendCross();
    // taker fill
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, "1");
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    // maker 撤单
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::kErrorCodeSuccess);

    te->ResumeCross();

    // te->ProcessOneXReq(te->GetCombCrossIdx());
    // 组合撮合 taker fill
    auto t_result = te->PopResult();
    ASSERT_NE(t_result.m_msg.get(), nullptr);
    auto t_order_check = t_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    auto t_leg2_order_id = t_order_check.m_msg->leg2_order_id();
    auto t_leg1_order_id = t_order_check.m_msg->leg1_order_id();
    t_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    // te->ProcessOneXReq(te->GetCombCrossIdx());
    // 组合撮合 maker fill
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    // 组合撮合 收到cancel消息
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetCanceled);

    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;
    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    ASSERT_EQ(spot_related_result.size(), 2);
    ASSERT_EQ(future_related_result.size(), 2);

    //  现货 taker fill 透传包
    auto t_com_order_check0 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check0.m_msg, nullptr);
    auto t_spot_order_check0 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
    ASSERT_NE(t_spot_order_check0.m_msg, nullptr);
    t_spot_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    auto t_future_com_order_check0 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check0.m_msg, nullptr);
    auto t_future_order_check0 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
    ASSERT_NE(t_future_order_check0.m_msg, nullptr);
    t_future_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 现货 maker fill 透传包
    auto m_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::PartiallyFilledCanceled, ECrossStatus::Canceled);

    {
      auto m_wallet_checker = spot_related_result[1].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }

    auto m_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);

    // 现货/期货 maker fill 透传包将组合包设置为终态cancel
    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Cancelled, ECombCrossStatus::Canceled);

    // te->UnSetCrossSingleStepMode();
  }
}

TEST_F(ComboFillTest, maker_pending_cancel_filled_cancel_reject_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "2";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));

  // 组合撮合 PendingNew->组合撮合 NewAccept->组合撮合 PendingCancel->组合撮合 MakerFill(OS:
  // PartialFilled)->现货撮合透传 MakerFill(OS: Filled)->组合撮合 Canceled(OS:PartiallyFilledCanceled)
  {
    // te->SetCrossSingleStepMode();
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;

    // 从组合撮合收NewAccept
    // te->ProcessOneXReq(te->GetCombCrossIdx());
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    auto m_leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_leg1_order_id = m_order_check.m_msg->leg1_order_id();

    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    // biz::size_x_t var_size;
    m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8));
    // m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size));
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price).CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    te->SuspendCross();
    // taker fill
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, "2");
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    // maker 撤单
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::kErrorCodeSuccess);

    te->ResumeCross();

    // te->ProcessOneXReq(te->GetCombCrossIdx());
    // 组合撮合 taker fill
    auto t_result = te->PopResult();
    ASSERT_NE(t_result.m_msg.get(), nullptr);
    auto t_order_check = t_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    auto t_leg2_order_id = t_order_check.m_msg->leg2_order_id();
    auto t_leg1_order_id = t_order_check.m_msg->leg1_order_id();
    t_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    // te->ProcessOneXReq(te->GetCombCrossIdx());
    // 组合撮合 maker fill
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    // 组合撮合 收到cancel消息
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetCanceled);

    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    ASSERT_EQ(spot_related_result.size(), 2);
    ASSERT_EQ(future_related_result.size(), 2);

    //  现货 taker fill 透传包
    auto t_com_order_check0 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check0.m_msg, nullptr);
    auto t_spot_order_check0 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
    ASSERT_NE(t_spot_order_check0.m_msg, nullptr);
    t_spot_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    auto t_future_com_order_check0 =
        future_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check0.m_msg, nullptr);
    auto t_future_order_check0 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
    ASSERT_NE(t_future_order_check0.m_msg, nullptr);
    t_future_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 现货 maker fill 透传包
    auto m_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::Filled, ECrossStatus::Canceled).CheckLeaves("0", "0");

    {
      auto m_wallet_checker = spot_related_result[1].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }

    auto m_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::Filled, ECrossStatus::Canceled);

    // 现货/期货 maker fill 透传包将组合包设置为终态cancel
    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Filled, ECombCrossStatus::Canceled).CheckLeavesQty(0);

    // te->UnSetCrossSingleStepMode();
  }
}

TEST_F(ComboFillTest, maker_partialfill_readded_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "2";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  // 下单->部分成交->改单
  {
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    // biz::size_x_t var_size;
    m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8));
    // m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size));
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);

    {
      // taker fill
      biz::order_link_id_t t_order_link_id{te->GenUUID()};
      CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price,
                                                "1");
      RpcContext t_ct;
      auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);
    }

    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }
    ASSERT_EQ(spot_related_result.size(), 3);
    ASSERT_EQ(future_related_result.size(), 3);

    {
      // check maker 部分成交状态
      auto m_com_order_check2 =
          spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_com_order_check2.m_msg, nullptr);
      auto m_leg2_order_id = m_com_order_check2.m_msg->leg2_order_id();
      auto m_spot_order_check2 = spot_related_result[2].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
      ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
      m_spot_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill).CheckLeaves("1", "9950");

      auto m_future_com_order_check2 =
          future_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_future_com_order_check2.m_msg, nullptr);
      auto m_leg1_order_id = m_future_com_order_check2.m_msg->leg1_order_id();
      auto m_future_order_check2 =
          future_related_result[2].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
      ASSERT_NE(m_future_order_check2.m_msg, nullptr);
      m_future_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);
    }

    {
      // maker 改价格
      CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, "200", qty);
      m_replace_build.SetOrderLinkId(m_order_link_id);
      RpcContext m_replace_ct;
      auto m_replace_resp = user1.comb_openapi_replace_order(m_replace_build.Build(), m_replace_ct);
      std::ignore = m_replace_ct;
      ASSERT_EQ(m_replace_ct.RetCode(), error::ErrorCode::kErrorCodeSuccess);
      auto m_replace_result = te->PopResult();
      ASSERT_NE(m_replace_result.m_msg.get(), nullptr);
      auto m_com_order_check_last =
          m_replace_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
      m_com_order_check_last.CheckStatus(ECombOrderStatus::PartiallyFilled, ECombCrossStatus::ReAdded);
      auto m_leg2_order_id = m_com_order_check_last.m_msg->leg2_order_id();
      auto m_spot_order_check2 = m_replace_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
      ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
      m_spot_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::ReAdded)
          .CheckLeaves("1", "9900")
          .CheckExecResult(uid, "0", "0", "9950", "0");

      {
        auto m_wallet_checker = m_replace_result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
        m_wallet_checker.CheckCoin(ECoin::BTC);
        m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("1"));
      }

      auto m_leg1_order_id = m_com_order_check_last.m_msg->leg1_order_id();
      auto m_future_order_check2 = m_replace_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
      ASSERT_NE(m_future_order_check2.m_msg, nullptr);
      m_future_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::ReAdded);
    }

    {
      // maker 改大数量
      CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, "3");
      m_replace_build.SetOrderLinkId(m_order_link_id);
      RpcContext m_replace_ct;
      auto m_replace_resp = user1.comb_openapi_replace_order(m_replace_build.Build(), m_replace_ct);
      std::ignore = m_replace_ct;
      ASSERT_EQ(m_replace_ct.RetCode(), error::ErrorCode::kErrorCodeSuccess);
      auto m_replace_result = te->PopResult();
      ASSERT_NE(m_replace_result.m_msg.get(), nullptr);
      auto m_com_order_check_last =
          m_replace_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
      m_com_order_check_last.CheckStatus(ECombOrderStatus::PartiallyFilled, ECombCrossStatus::ReAdded);
      auto m_leg2_order_id = m_com_order_check_last.m_msg->leg2_order_id();
      auto m_spot_order_check2 = m_replace_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
      ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
      m_spot_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::ReAdded)
          .CheckLeaves("2", "19900")
          .CheckExecResult(uid, "0", "0", "9900", "0");

      {
        auto m_wallet_checker = m_replace_result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
        m_wallet_checker.CheckCoin(ECoin::BTC);
        m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("2"));
      }

      auto m_leg1_order_id = m_com_order_check_last.m_msg->leg1_order_id();
      auto m_future_order_check2 = m_replace_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
      ASSERT_NE(m_future_order_check2.m_msg, nullptr);
      m_future_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::ReAdded);
    }

    {
      // maker 改小数量
      CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, "1.5");
      m_replace_build.SetOrderLinkId(m_order_link_id);
      RpcContext m_replace_ct;
      auto m_replace_resp = user1.comb_openapi_replace_order(m_replace_build.Build(), m_replace_ct);
      std::ignore = m_replace_ct;
      ASSERT_EQ(m_replace_ct.RetCode(), error::ErrorCode::kErrorCodeSuccess);
      auto m_replace_result = te->PopResult();
      ASSERT_NE(m_replace_result.m_msg.get(), nullptr);
      auto m_com_order_check_last =
          m_replace_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
      m_com_order_check_last.CheckStatus(ECombOrderStatus::PartiallyFilled, ECombCrossStatus::Replaced);
      auto m_leg2_order_id = m_com_order_check_last.m_msg->leg2_order_id();
      auto m_spot_order_check2 = m_replace_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
      ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
      m_spot_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::Replaced)
          .CheckLeaves("0.5", "4975")
          .CheckExecResult(uid, "0", "0", "19900", "0");

      {
        auto m_wallet_checker = m_replace_result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
        m_wallet_checker.CheckCoin(ECoin::BTC);
        m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0.5"));
      }

      auto m_leg1_order_id = m_com_order_check_last.m_msg->leg1_order_id();
      auto m_future_order_check2 = m_replace_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
      ASSERT_NE(m_future_order_check2.m_msg, nullptr);
      m_future_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::Replaced);
    }

    {
      // taker fill
      biz::order_link_id_t t_order_link_id{te->GenUUID()};
      CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price,
                                                "1");
      RpcContext t_ct;
      auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);
    }

    spot_related_result.clear();
    future_related_result.clear();

    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }

        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }
    ASSERT_EQ(spot_related_result.size(), 3);
    ASSERT_EQ(future_related_result.size(), 3);

    {
      // check maker 全部成交状态
      auto m_com_order_check2 =
          spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_com_order_check2.m_msg, nullptr);
      auto m_leg2_order_id = m_com_order_check2.m_msg->leg2_order_id();
      auto m_spot_order_check2 = spot_related_result[2].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
      ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
      m_spot_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill).CheckLeaves("0", "0");

      {
        auto m_wallet_checker = spot_related_result[2].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
        m_wallet_checker.CheckCoin(ECoin::BTC);
        m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
      }

      auto m_future_com_order_check2 =
          future_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_future_com_order_check2.m_msg, nullptr);
      auto m_leg1_order_id = m_future_com_order_check2.m_msg->leg1_order_id();
      auto m_future_order_check2 =
          future_related_result[2].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
      ASSERT_NE(m_future_order_check2.m_msg, nullptr);
      m_future_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);

      auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
      m_com_order_check_last.CheckStatus(ECombOrderStatus::Filled, ECombCrossStatus::MakerFill);
    }
  }
}

TEST_F(ComboFillTest, taker_fok_ioc_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "2";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  // FOK单 cancelled
  {
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    // biz::size_x_t var_size;
    m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8));
    // m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size));
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);

    {
      // taker fill
      biz::order_link_id_t t_order_link_id{te->GenUUID()};
      CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, "FOK", price, "3");
      RpcContext t_ct;
      auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

      std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
      std::deque<UnifiedV2ResultDTOChecker> future_related_result;

      UnifiedV2ResultDTOChecker com_last_result(nullptr);
      while (true) {
        auto leg_result = te->PopResult();
        if (leg_result.m_msg == nullptr) {
          break;
        } else {
          com_last_result = leg_result;
          if (leg_result.m_msg->has_spot_margin_result()) {
            spot_related_result.push_back(leg_result);
          }
          if (leg_result.m_msg->has_futures_margin_result()) {
            future_related_result.push_back(leg_result);
          }
        }
      }
      ASSERT_EQ(spot_related_result.size(), 1);
      ASSERT_EQ(future_related_result.size(), 1);

      // taker cancelled
      auto t_com_order_check0 =
          spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
      ASSERT_NE(t_com_order_check0.m_msg, nullptr);
      t_com_order_check0.CheckStatus(ECombOrderStatus::Cancelled, ECombCrossStatus::Canceled);
      auto t_leg2_order_id = t_com_order_check0.m_msg->leg2_order_id();
      auto t_spot_order_check0 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
      ASSERT_NE(t_spot_order_check0.m_msg, nullptr);
      t_spot_order_check0.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled).CheckLeaves("3", "29850");

      {
        auto t_wallet_checker = spot_related_result[0].RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
        t_wallet_checker.CheckCoin(ECoin::USDT);
        t_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
      }

      auto t_leg1_order_id = t_com_order_check0.m_msg->leg1_order_id();
      auto t_future_order_check0 =
          spot_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
      ASSERT_NE(t_future_order_check0.m_msg, nullptr);
      t_future_order_check0.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
    }
    {
      // taker fill
      biz::order_link_id_t t_order_link_id{te->GenUUID()};
      CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, "IOC", price, "3");
      RpcContext t_ct;
      auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

      std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
      std::deque<UnifiedV2ResultDTOChecker> future_related_result;

      UnifiedV2ResultDTOChecker com_last_result(nullptr);
      UnifiedV2ResultDTOChecker com_last_taker_result(nullptr);
      while (true) {
        auto leg_result = te->PopResult();
        if (leg_result.m_msg == nullptr) {
          break;
        } else {
          com_last_result = leg_result;
          if (leg_result.m_msg->has_spot_margin_result()) {
            spot_related_result.push_back(leg_result);
          }
          if (leg_result.m_msg->has_futures_margin_result()) {
            future_related_result.push_back(leg_result);
          }
          if (com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id).m_msg) {
            com_last_taker_result = com_last_result;
          }
        }
      }
      ASSERT_EQ(spot_related_result.size(), 3);
      ASSERT_EQ(future_related_result.size(), 3);

      // taker cancelled
      auto t_com_order_check1 =
          spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
      ASSERT_NE(t_com_order_check1.m_msg, nullptr);
      auto t_leg2_order_id = t_com_order_check1.m_msg->leg2_order_id();
      auto t_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
      ASSERT_NE(t_spot_order_check1.m_msg, nullptr);
      t_spot_order_check1.CheckStatus(EOrderStatus::PartiallyFilledCanceled, ECrossStatus::Canceled)
          .CheckLeaves("1", "9950");

      {
        auto t_wallet_checker = spot_related_result[1].RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
        t_wallet_checker.CheckCoin(ECoin::USDT);
        t_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
      }

      auto t_future_com_order_check1 =
          future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
      ASSERT_NE(t_future_com_order_check1.m_msg, nullptr);
      auto t_leg1_order_id = t_future_com_order_check1.m_msg->leg1_order_id();
      auto t_future_order_check1 =
          future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
      ASSERT_NE(t_future_order_check1.m_msg, nullptr);
      t_future_order_check1.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);

      ASSERT_NE(com_last_taker_result.m_msg, nullptr);
      auto t_com_order_check_last =
          com_last_taker_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
      t_com_order_check_last.CheckStatus(ECombOrderStatus::Cancelled, ECombCrossStatus::Canceled).CheckLeavesQty(0);
      auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
      m_com_order_check_last.CheckStatus(ECombOrderStatus::Filled, ECombCrossStatus::MakerFill);
    }
  }
}

TEST_F(ComboFillTest, maker_pending_new_pending_cancel_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "2";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));

  // 组合撮合  PendingNew->组合撮合  PendingCancel->组合撮合  NewAccept->组合撮合  Canceled
  {
    te->SuspendCross();
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;

    // maker 撤单
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::kErrorCodeSuccess);

    te->ResumeCross();

    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    auto m_leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_leg1_order_id = m_order_check.m_msg->leg1_order_id();

    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);
    auto m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    auto m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // 组合撮合 收到cancel消息
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::Cancelled, ECombCrossStatus::Canceled);
    m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    m_spot_order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);

    m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    m_future_order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
  }
}

TEST_F(ComboFillTest, maker_pending_new_pending_cancel_partfill_cancel_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "2";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));

  // 组合撮合 PendingNew->组合撮合 PendingCancel->组合撮合 NewAccept->组合撮合 MakerFill(OS:
  // PartialFilled)->现货撮合透传 MakerFill(OS: Filled)->组合撮合 Canceled(OS:PartiallyFilledCanceled)
  {
    te->SuspendCross();
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;

    // taker fill
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, "1");
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    // maker 撤单
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::kErrorCodeSuccess);

    te->ResumeCross();

    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    auto m_leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    auto m_leg1_order_id = m_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // taker fill
    auto t_result = te->PopResult();
    ASSERT_NE(t_result.m_msg.get(), nullptr);
    auto t_order_check = t_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    auto t_leg2_order_id = t_order_check.m_msg->leg2_order_id();
    auto t_leg1_order_id = t_order_check.m_msg->leg1_order_id();

    // 组合撮合 makefill
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    // 组合撮合 maker收到 cancel
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetCanceled);
    m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::PendingCancel);

    {
      auto m_wallet_checker = m_result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("2"));
    }

    m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::PendingCancel);

    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    ASSERT_EQ(spot_related_result.size(), 2);
    ASSERT_EQ(future_related_result.size(), 2);

    //  现货 taker fill 透传包
    auto t_com_order_check0 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check0.m_msg, nullptr);
    auto t_spot_order_check0 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
    ASSERT_NE(t_spot_order_check0.m_msg, nullptr);
    t_spot_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    auto t_future_com_order_check0 =
        future_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check0.m_msg, nullptr);
    auto t_future_order_check0 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
    ASSERT_NE(t_future_order_check0.m_msg, nullptr);
    t_future_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 现货 maker fill 透传包
    auto m_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::PartiallyFilledCanceled, ECrossStatus::Canceled)
        .CheckLeaves("1", "9950");

    {
      auto m_wallet_checker = spot_related_result[1].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }

    auto m_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);

    // 现货/期货 maker fill 透传包将组合包设置为终态cancel
    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Cancelled, ECombCrossStatus::Canceled).CheckLeavesQty(0);
  }
}

TEST_F(ComboFillTest, maker_pending_new_pending_cancel_fill_cancel_reject_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "2";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));

  // 组合撮合 PendingNew->组合撮合 PendingCancel->组合撮合 NewAccept->组合撮合 MakerFill(OS:
  // PartialFilled)->现货撮合透传 MakerFill(OS: Filled)->组合撮合 Canceled(OS:PartiallyFilledCanceled)
  {
    te->SuspendCross();
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;

    // taker fill
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, "2");
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    // maker 撤单
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::kErrorCodeSuccess);

    te->ResumeCross();

    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    auto m_leg2_order_id = m_order_check.m_msg->leg2_order_id();
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);
    auto m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    auto m_leg1_order_id = m_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // taker fill
    auto t_result = te->PopResult();
    ASSERT_NE(t_result.m_msg.get(), nullptr);
    auto t_order_check = t_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    auto t_leg2_order_id = t_order_check.m_msg->leg2_order_id();
    auto t_leg1_order_id = t_order_check.m_msg->leg1_order_id();

    // 组合撮合 makefill
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    // 组合撮合 maker收到 cancelreject
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetCanceled);
    m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::PendingCancel);

    m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::PendingCancel);

    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    ASSERT_EQ(spot_related_result.size(), 2);
    ASSERT_EQ(future_related_result.size(), 2);

    //  现货 taker fill 透传包
    auto t_com_order_check0 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check0.m_msg, nullptr);
    auto t_spot_order_check0 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
    ASSERT_NE(t_spot_order_check0.m_msg, nullptr);
    t_spot_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    auto t_future_com_order_check0 =
        future_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check0.m_msg, nullptr);
    auto t_future_order_check0 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
    ASSERT_NE(t_future_order_check0.m_msg, nullptr);
    t_future_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 现货 maker fill 透传包
    auto m_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::Filled, ECrossStatus::Canceled).CheckLeaves("0", "0");

    {
      auto m_wallet_checker = spot_related_result[1].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }

    auto m_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::Filled, ECrossStatus::Canceled);

    // 现货/期货 maker fill 透传包将组合包设置为终态cancel
    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Filled, ECombCrossStatus::Canceled);
  }
}

TEST_F(ComboFillTest, maker_pending_new_pending_replace_partialfill_replaced_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "2";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));

  // 组合撮合 PendingNew->组合撮合 NewAccept->组合撮合 PendingReplace->组合撮合MakerFill(OS:
  // PartialFilled)->现货撮合透传 MakerFill(OS: Filled)->组合撮合 Replaced
  {
    te->SuspendCross();
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;

    // taker fill
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, "1");
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    // maker 改小数量
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, "1.5");
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_replace_ct;
    auto m_replace_resp = user1.comb_openapi_replace_order(m_replace_build.Build(), m_replace_ct);
    std::ignore = m_replace_ct;
    ASSERT_EQ(m_replace_ct.RetCode(), error::ErrorCode::kErrorCodeSuccess);

    te->ResumeCross();

    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    auto m_leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // 改单时 将现货CowSpotOrder里的成本item设置按旧的冻结（按较大成本冻结）
    {
      auto m_wallet_checker = m_result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("2"));
    }

    auto m_leg1_order_id = m_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // taker fill
    auto t_result = te->PopResult();
    ASSERT_NE(t_result.m_msg.get(), nullptr);
    auto t_order_check = t_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    auto t_leg2_order_id = t_order_check.m_msg->leg2_order_id();
    auto t_leg1_order_id = t_order_check.m_msg->leg1_order_id();

    // 组合撮合 makefill
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    // 组合撮合 maker收到 replace ack
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetReplaced);
    m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::Replaced).CheckLeaves("1.5", "14925");

    // pendingreplace时 成本减小，但是还按照原来大的冻结；收到replace
    // ack时，由于有未收完的成交透传，没有直接reset,而是采用reduceonfill的方式变更成本
    {
      auto m_wallet_checker = m_result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("2"));
    }

    m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::PendingReplace);

    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;
    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    ASSERT_EQ(spot_related_result.size(), 2);
    ASSERT_EQ(future_related_result.size(), 2);

    //  现货 taker fill 透传包
    auto t_com_order_check0 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check0.m_msg, nullptr);
    auto t_spot_order_check0 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
    ASSERT_NE(t_spot_order_check0.m_msg, nullptr);
    t_spot_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    auto t_future_com_order_check0 =
        future_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check0.m_msg, nullptr);
    auto t_future_order_check0 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
    ASSERT_NE(t_future_order_check0.m_msg, nullptr);
    t_future_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 现货 maker fill 透传包
    auto m_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::Replaced).CheckLeaves("0.5", "4975");

    {
      auto m_wallet_checker = spot_related_result[1].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0.5"));
    }

    auto m_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::Replaced);

    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::PartiallyFilled, ECombCrossStatus::Replaced)
        .CheckLeavesQty(50000000);
  }
}

TEST_F(ComboFillTest, maker_pending_new_pending_replace_partialfill_readded_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "2";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));

  // 组合撮合 PendingNew->组合撮合 NewAccept->组合撮合 PendingReplace->组合撮合MakerFill(OS:
  // PartialFilled)->现货撮合透传 MakerFill(OS: Filled)->组合撮合 Replaced
  {
    te->SuspendCross();
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;

    // taker fill
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, "1");
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    // maker 改大数量
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, "3");
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_replace_ct;
    auto m_replace_resp = user1.comb_openapi_replace_order(m_replace_build.Build(), m_replace_ct);
    std::ignore = m_replace_ct;
    ASSERT_EQ(m_replace_ct.RetCode(), error::ErrorCode::kErrorCodeSuccess);

    te->ResumeCross();

    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    auto m_leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // 改单时 将现货CowSpotOrder里的成本item设置按新的冻结（按较大成本冻结）
    {
      auto m_wallet_checker = m_result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("3"));
    }

    auto m_leg1_order_id = m_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // taker fill
    auto t_result = te->PopResult();
    ASSERT_NE(t_result.m_msg.get(), nullptr);
    auto t_order_check = t_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    auto t_leg2_order_id = t_order_check.m_msg->leg2_order_id();
    auto t_leg1_order_id = t_order_check.m_msg->leg1_order_id();

    // 组合撮合 makefill
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    // 组合撮合 maker收到 replace ack
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetReAdded);
    m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded);

    {
      auto m_wallet_checker = m_result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("3"));
    }

    m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded);

    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    ASSERT_EQ(spot_related_result.size(), 2);
    ASSERT_EQ(future_related_result.size(), 2);

    //  现货 taker fill 透传包
    auto t_com_order_check0 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check0.m_msg, nullptr);
    auto t_spot_order_check0 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
    ASSERT_NE(t_spot_order_check0.m_msg, nullptr);
    t_spot_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    auto t_future_com_order_check0 =
        future_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check0.m_msg, nullptr);
    auto t_future_order_check0 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
    ASSERT_NE(t_future_order_check0.m_msg, nullptr);
    t_future_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 现货 maker fill 透传包
    auto m_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::ReAdded).CheckLeaves("2", "19900");

    {
      auto m_wallet_checker = spot_related_result[1].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("2"));
    }

    auto m_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::ReAdded);

    // 现货/期货 maker fill 透传包将组合包设置为终态cancel
    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::PartiallyFilled, ECombCrossStatus::ReAdded)
        .CheckLeavesQty(200000000);
  }
}

TEST_F(ComboFillTest, maker_pending_new_pending_replace_partialfill_replace_reject_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "2";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));

  // 组合撮合 PendingNew->组合撮合 NewAccept->组合撮合 PendingReplace->组合撮合MakerFill(OS:
  // PartialFilled)->现货撮合透传 MakerFill(OS: Filled)->组合撮合 Canceled(这个终态有点怪)
  {
    te->SuspendCross();
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;

    // taker fill
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, "1");
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    // maker 改小数量 小于cum_qty
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, "0.5");
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_replace_ct;
    auto m_replace_resp = user1.comb_openapi_replace_order(m_replace_build.Build(), m_replace_ct);
    std::ignore = m_replace_ct;
    ASSERT_EQ(m_replace_ct.RetCode(), error::ErrorCode::kErrorCodeSuccess);

    te->ResumeCross();

    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    auto m_leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    auto m_leg1_order_id = m_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // taker fill
    auto t_result = te->PopResult();
    ASSERT_NE(t_result.m_msg.get(), nullptr);
    auto t_order_check = t_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    auto t_leg2_order_id = t_order_check.m_msg->leg2_order_id();
    auto t_leg1_order_id = t_order_check.m_msg->leg1_order_id();

    // 组合撮合 makefill
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    // 组合撮合 maker收到 replace reject
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetCanceled);
    m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::PendingReplace);

    m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::PendingReplace);

    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    ASSERT_EQ(spot_related_result.size(), 2);
    ASSERT_EQ(future_related_result.size(), 2);

    //  现货 taker fill 透传包
    auto t_com_order_check0 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check0.m_msg, nullptr);
    auto t_spot_order_check0 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
    ASSERT_NE(t_spot_order_check0.m_msg, nullptr);
    t_spot_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    auto t_future_com_order_check0 =
        future_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check0.m_msg, nullptr);
    auto t_future_order_check0 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
    ASSERT_NE(t_future_order_check0.m_msg, nullptr);
    t_future_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 现货 maker fill 透传包
    auto m_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::PartiallyFilledCanceled, ECrossStatus::Canceled)
        .CheckLeaves("1", "9950");

    {
      auto m_wallet_checker = spot_related_result[1].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }

    auto m_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);

    // 现货/期货 maker fill 透传包将组合包设置为终态cancel
    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Cancelled, ECombCrossStatus::Canceled).CheckLeavesQty(0);
  }
}

TEST_F(ComboFillTest, maker_pending_new_pending_replace_fill_replace_reject_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "2";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTC-USDT(现货)

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));

  // 组合撮合 PendingNew->组合撮合 NewAccept->组合撮合 PendingReplace->组合撮合MakerFill(OS:
  // PartialFilled)->现货撮合透传 MakerFill(OS: Filled)->组合撮合 Canceled(这个终态有点怪)
  {
    te->SuspendCross();
    // maker
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;

    // taker fill
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, "2");
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    // maker 改小数量 小于cum_qty
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, "0.5");
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_replace_ct;
    auto m_replace_resp = user1.comb_openapi_replace_order(m_replace_build.Build(), m_replace_ct);
    std::ignore = m_replace_ct;
    ASSERT_EQ(m_replace_ct.RetCode(), error::ErrorCode::kErrorCodeSuccess);

    te->ResumeCross();

    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    auto m_leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    auto m_leg1_order_id = m_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // taker fill
    auto t_result = te->PopResult();
    ASSERT_NE(t_result.m_msg.get(), nullptr);
    auto t_order_check = t_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    auto t_leg2_order_id = t_order_check.m_msg->leg2_order_id();
    auto t_leg1_order_id = t_order_check.m_msg->leg1_order_id();

    // 组合撮合 makefill
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    // 组合撮合 maker收到 replace reject
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetCanceled);
    m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::PendingReplace);

    m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::PendingReplace);

    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    ASSERT_EQ(spot_related_result.size(), 2);
    ASSERT_EQ(future_related_result.size(), 2);

    //  现货 taker fill 透传包
    auto t_com_order_check0 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check0.m_msg, nullptr);
    auto t_spot_order_check0 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
    ASSERT_NE(t_spot_order_check0.m_msg, nullptr);
    t_spot_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    auto t_future_com_order_check0 =
        future_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check0.m_msg, nullptr);
    auto t_future_order_check0 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
    ASSERT_NE(t_future_order_check0.m_msg, nullptr);
    t_future_order_check0.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 现货 maker fill 透传包
    auto m_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::Filled, ECrossStatus::Canceled).CheckLeaves("0", "0");

    {
      auto m_wallet_checker = spot_related_result[1].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }

    auto m_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::Filled, ECrossStatus::Canceled);

    // 现货/期货 maker fill 透传包将组合包设置为终态cancel
    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Filled, ECombCrossStatus::Canceled).CheckLeavesQty(0);
  }
}

TEST_F(ComboFillTest, maker_replace_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTCUSDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  biz::order_link_id_t m_order_link_id{te->GenUUID()};

  // 下单
  {
    // 停止撮合
    te->SuspendCross();

    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_EQ(m_result.m_msg.get(), nullptr);
  }

  {
    // maker replace
    price = "200";
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, qty);
    RpcContext m_ct;
    m_replace_build.SetOrderLinkId(m_order_link_id);
    auto m_resp1 = user1.comb_openapi_replace_order(m_replace_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_ct.RetCode(), error::ErrorCode::kErrorCodeSuccess);
    auto m_result1 = te->PopResult();
    ASSERT_EQ(m_result1.m_msg.get(), nullptr);

    te->ResumeCross();

    // taker
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, qty);
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    // [Maker NewAccepted, Maker Replace/Readd, Taker New Accepted, Taker Filled, Maker Filled]
    ASSERT_EQ(spot_related_result.size(), 5);
    ASSERT_EQ(future_related_result.size(), 5);

    // 校验组合订单 Maker NewAccepted
    auto m_order_check = spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    // biz::size_x_t var_size;
    m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8));
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice("100", 8, var_price);
    m_order_check.CheckPrice(var_price);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    {
      auto m_wallet_checker = spot_related_result[0].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("1"));
    }

    // 校验现货单腿 Maker NewAccepted
    auto spot_order = spot_related_result[0].m_msg.get()->spot_margin_result().related_spot_orders()[0];
    auto t_spot_order_check0 =
        spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(spot_order.order_id());
    t_spot_order_check0.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    t_spot_order_check0.CheckCreateType(user1.m_uid, ECreateType::CreateByFutureSpread);
    t_spot_order_check0.CheckSide(user1.m_uid, ESide::Sell);
    t_spot_order_check0.CheckPrice(user1.m_uid, "9950");
    t_spot_order_check0.CheckQty(user1.m_uid, "1");
    t_spot_order_check0.CheckLeaves("1", "9950");

    auto future_order = future_related_result[0].m_msg.get()->futures_margin_result().related_orders()[0];
    auto t_future_order_check0 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(future_order.order_id());
    t_future_order_check0.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    t_future_order_check0.CheckCreateType(user1.m_uid, ECreateType::CreateByFutureSpread);
    EXPECT_EQ(t_future_order_check0.m_msg->user_id(), user1.m_uid);
    EXPECT_EQ(t_future_order_check0.m_msg->side(), ESide::Buy);
    EXPECT_EQ(t_future_order_check0.m_msg->qty_x(), 100000000);
    EXPECT_EQ(t_future_order_check0.m_msg->price_x(), 100500000);
    EXPECT_EQ(t_future_order_check0.m_msg->exec_qty_x(), 0);

    // Maker Replace/ReAdd
    auto m_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_leg2_order_id = m_com_order_check1.m_msg->leg2_order_id();
    auto m_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded).CheckLeaves("1", "9900");
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_com_order_check1.CheckPrice(var_price);
    m_com_order_check1.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::ReAdded);

    {
      auto m_wallet_checker = spot_related_result[1].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("1"));
    }

    auto m_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_leg1_order_id = m_future_com_order_check1.m_msg->leg1_order_id();
    auto m_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded);
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_future_com_order_check1.CheckPrice(var_price);
    m_future_com_order_check1.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::ReAdded);

    // Taker New Accepted
    auto t_com_order_check1 =
        spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check1.m_msg, nullptr);
    auto t_leg2_order_id1 = t_com_order_check1.m_msg->leg2_order_id();
    auto t_spot_order_check1 = spot_related_result[2].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id1);
    ASSERT_NE(t_spot_order_check1.m_msg, nullptr);
    t_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("1", "9900");
    t_com_order_check1.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    auto t_future_com_order_check1 =
        future_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check1.m_msg, nullptr);
    auto t_leg1_order_id1 = t_future_com_order_check1.m_msg->leg1_order_id();
    auto t_future_order_check1 =
        future_related_result[2].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id1);
    ASSERT_NE(t_future_order_check1.m_msg, nullptr);
    t_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    t_future_com_order_check1.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    // Taker Filled
    auto t_com_order_check2 =
        spot_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check2.m_msg, nullptr);
    auto t_leg2_order_id2 = t_com_order_check2.m_msg->leg2_order_id();
    auto t_spot_order_check2 = spot_related_result[3].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id2);
    ASSERT_NE(t_spot_order_check2.m_msg, nullptr);
    t_spot_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill).CheckLeaves("0", "0");

    auto t_future_com_order_check2 =
        future_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check2.m_msg, nullptr);
    auto t_leg1_order_id2 = t_future_com_order_check2.m_msg->leg1_order_id();
    auto t_future_order_check2 =
        future_related_result[3].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id2);
    ASSERT_NE(t_future_order_check2.m_msg, nullptr);
    t_future_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // Maker Filled
    auto m_com_order_check2 =
        spot_related_result[4].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check2.m_msg, nullptr);
    auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
    auto m_spot_order_check2 = spot_related_result[4].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
    ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
    m_spot_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill).CheckLeaves("0", "0");

    {
      auto m_wallet_checker = spot_related_result[4].RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }

    auto m_future_com_order_check2 =
        future_related_result[4].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check2.m_msg, nullptr);
    auto m_leg1_order_id2 = m_future_com_order_check2.m_msg->leg1_order_id();
    auto m_future_order_check2 =
        future_related_result[4].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id2);
    ASSERT_NE(m_future_order_check2.m_msg, nullptr);
    m_future_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);

    // End result is same as spot dump
    auto m_com_order_check3 = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    m_com_order_check3.CheckStatus(ECombOrderStatus::Filled, ECombCrossStatus::MakerFill);
  }

  {
    // maker
    m_order_link_id = te->GenUUID();
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8));
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);

    // maker replace
    price = "100";
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, qty);
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_resp1 = user1.comb_openapi_replace_order(m_replace_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::ErrorCode::kErrorCodeSuccess);
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_com_order_check1 = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_leg2_order_id = m_com_order_check1.m_msg->leg2_order_id();
    auto m_spot_order_check1 = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded).CheckLeaves("1", "9950");

    auto m_leg1_order_id = m_com_order_check1.m_msg->leg1_order_id();
    auto m_future_order_check1 = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded);

    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_com_order_check1.CheckPrice(var_price);
    m_com_order_check1.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::ReAdded);

    // maker 撤单
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct2;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct2);
    std::ignore = m_ct2;
    ASSERT_EQ(m_ct2.RetCode(), error::ErrorCode::kErrorCodeSuccess);
    m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    auto m_com_order_check2 = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check2.m_msg, nullptr);
    auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
    auto m_spot_order_check2 = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
    ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
    m_spot_order_check2.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled).CheckLeaves("1", "9950");
    auto m_leg1_order_id2 = m_com_order_check2.m_msg->leg1_order_id();
    auto m_future_order_check2 = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id2);
    ASSERT_NE(m_future_order_check2.m_msg, nullptr);
    m_future_order_check2.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
    m_com_order_check2.CheckStatus(ECombOrderStatus::Cancelled, ECombCrossStatus::Canceled);
  }
}

TEST_F(ComboFillTest, maker_replace_failed_after_filled_with_spot_leg) {
  GTEST_SKIP();  // todo
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTCUSDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  biz::order_link_id_t m_order_link_id{te->GenUUID()};

  // 下单
  {
    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);

    // 校验组合订单
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    // biz::size_x_t var_size;
    m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8));
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // 校验现货单腿
    auto spot_order = m_result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
    auto t_spot_order_check0 = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(spot_order.order_id());
    t_spot_order_check0.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    t_spot_order_check0.CheckCreateType(user1.m_uid, ECreateType::CreateByFutureSpread);
    t_spot_order_check0.CheckSide(user1.m_uid, ESide::Sell);
    t_spot_order_check0.CheckPrice(user1.m_uid, "9950");
    t_spot_order_check0.CheckQty(user1.m_uid, "1");
    t_spot_order_check0.CheckLeaves("1", "9950");

    auto future_order = m_result.m_msg.get()->futures_margin_result().related_orders()[0];
    auto t_future_order_check0 = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(future_order.order_id());
    t_future_order_check0.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    t_future_order_check0.CheckCreateType(user1.m_uid, ECreateType::CreateByFutureSpread);
    EXPECT_EQ(t_future_order_check0.m_msg->user_id(), user1.m_uid);
    EXPECT_EQ(t_future_order_check0.m_msg->side(), ESide::Sell);
    EXPECT_EQ(t_future_order_check0.m_msg->qty_x(), 100000000);
    EXPECT_EQ(t_future_order_check0.m_msg->price_x(), 99500000000);
    EXPECT_EQ(t_future_order_check0.m_msg->exec_qty_x(), 0);
  }

  // 停撮合，改单 price: 100->200
  // 组合撮合 PendingNew -> NewAccept -> Set to fill -> PendingReplace -> Cancelled
  {
    // 停止撮合
    te->SuspendCross();

    // taker
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, qty);
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    // maker replace
    price = "200";
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, qty);
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct;
    auto m_resp = user1.comb_openapi_replace_order(m_replace_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_ct.RetCode(), error::ErrorCode::kErrorCodeSuccess);
    auto m_result = te->PopResult();
    ASSERT_EQ(m_result.m_msg.get(), nullptr);

    te->ResumeCross();
    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    // [Taker NewAccepted, Maker PendingReplace, Taker Filled, Maker Cancelled]
    ASSERT_EQ(spot_related_result.size(), 4);
    ASSERT_EQ(future_related_result.size(), 4);

    // Taker NewAccepted
    auto t_com_order_check1 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check1.m_msg, nullptr);
    auto t_leg2_order_id = t_com_order_check1.m_msg->leg2_order_id();
    auto t_spot_order_check1 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
    ASSERT_NE(t_spot_order_check1.m_msg, nullptr);
    t_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("1", "9950");

    auto t_future_com_order_check1 =
        future_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check1.m_msg, nullptr);
    auto t_leg1_order_id = t_future_com_order_check1.m_msg->leg1_order_id();
    auto t_future_order_check1 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
    ASSERT_NE(t_future_order_check1.m_msg, nullptr);
    t_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // Maker PendingReplace
    auto m_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_leg2_order_id = m_com_order_check1.m_msg->leg2_order_id();
    auto m_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::PendingReplace).CheckLeaves("1", "9900");

    auto m_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_leg1_order_id = m_future_com_order_check1.m_msg->leg1_order_id();
    auto m_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::PendingReplace);

    // Taker Filled
    auto t_com_order_check2 =
        spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check2.m_msg, nullptr);
    auto t_leg2_order_id2 = t_com_order_check2.m_msg->leg2_order_id();
    auto t_spot_order_check2 = spot_related_result[2].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id2);
    ASSERT_NE(t_spot_order_check2.m_msg, nullptr);
    t_spot_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill).CheckLeaves("0", "0");

    auto t_future_com_order_check2 =
        future_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check2.m_msg, nullptr);
    auto t_leg1_order_id2 = t_future_com_order_check2.m_msg->leg1_order_id();
    auto t_future_order_check2 =
        future_related_result[2].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id2);
    ASSERT_NE(t_future_order_check2.m_msg, nullptr);
    t_future_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // Cancelled
    auto m_com_order_check2 =
        spot_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check2.m_msg, nullptr);
    auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
    auto m_spot_order_check2 = spot_related_result[3].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
    ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
    m_spot_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::Canceled).CheckLeaves("0", "0");

    auto m_future_com_order_check2 =
        future_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check2.m_msg, nullptr);
    auto m_leg1_order_id2 = m_future_com_order_check2.m_msg->leg1_order_id();
    auto m_future_order_check2 =
        future_related_result[3].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id2);
    ASSERT_NE(m_future_order_check2.m_msg, nullptr);
    m_future_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::Canceled);

    // check last comb dump
    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    // todo： 期货那边也应该将组合订单状态设置为Filled.否则这里check不过
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Filled, ECombCrossStatus::Canceled);
  }

  // 重新生成orderLinkID
  m_order_link_id = te->GenUUID();
  price = "100";
  // 下单
  {
    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    // 校验组合订单
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    m_order_check.CheckQty(biz::V5ReqConverterUtil::ConvertQty(qty, 8));
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // 校验现货单腿
    auto spot_order = m_result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
    auto t_spot_order_check0 = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(spot_order.order_id());
    t_spot_order_check0.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    t_spot_order_check0.CheckCreateType(user1.m_uid, ECreateType::CreateByFutureSpread);
    t_spot_order_check0.CheckSide(user1.m_uid, ESide::Sell);
    t_spot_order_check0.CheckPrice(user1.m_uid, "9950");
    t_spot_order_check0.CheckQty(user1.m_uid, "1");
    t_spot_order_check0.CheckLeaves("1", "9950");

    auto future_order = m_result.m_msg.get()->futures_margin_result().related_orders()[0];
    auto t_future_order_check0 = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(future_order.order_id());
    t_future_order_check0.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    t_future_order_check0.CheckCreateType(user1.m_uid, ECreateType::CreateByFutureSpread);
    EXPECT_EQ(t_future_order_check0.m_msg->user_id(), user1.m_uid);
    EXPECT_EQ(t_future_order_check0.m_msg->side(), ESide::Sell);
    EXPECT_EQ(t_future_order_check0.m_msg->qty_x(), 100000000);
    EXPECT_EQ(t_future_order_check0.m_msg->price_x(), 50000000);
    EXPECT_EQ(t_future_order_check0.m_msg->exec_qty_x(), 0);
  }

  // 停撮合，改单 price: 100->200
  // 组合撮合 PendingNew -> NewAccept -> PendingReplace -> Set to fill -> filled
  {
    // 停止撮合
    te->SuspendCross();

    // combStatus -> PendingReplace
    price = "200";
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, qty);
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct;
    auto m_resp = user1.comb_openapi_replace_order(m_replace_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_ct.RetCode(), error::ErrorCode::kErrorCodeSuccess);
    auto m_result = te->PopResult();
    ASSERT_EQ(m_result.m_msg.get(), nullptr);

    // taker
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, qty);
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    te->ResumeCross();
    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    // [Maker ReAdd, Taker NewAccepted, Taker Filled, Maker Filled]
    ASSERT_EQ(spot_related_result.size(), 4);
    ASSERT_EQ(future_related_result.size(), 4);

    // Maker ReAdd
    auto m_com_order_check1 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_leg2_order_id = m_com_order_check1.m_msg->leg2_order_id();
    auto m_spot_order_check1 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded).CheckLeaves("1", "9900");
    m_com_order_check1.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::ReAdded);

    auto m_future_com_order_check1 =
        future_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_leg1_order_id = m_future_com_order_check1.m_msg->leg1_order_id();
    auto m_future_order_check1 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded);
    m_future_com_order_check1.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::ReAdded);

    // Taker NewAccepted
    auto t_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check1.m_msg, nullptr);
    auto t_leg2_order_id = t_com_order_check1.m_msg->leg2_order_id();
    auto t_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id);
    ASSERT_NE(t_spot_order_check1.m_msg, nullptr);
    t_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("1", "9900");
    t_com_order_check1.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    auto t_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check1.m_msg, nullptr);
    auto t_leg1_order_id = t_future_com_order_check1.m_msg->leg1_order_id();
    auto t_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id);
    ASSERT_NE(t_future_order_check1.m_msg, nullptr);
    t_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    t_future_com_order_check1.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetFill);

    // Taker Filled
    auto t_com_order_check2 =
        spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check2.m_msg, nullptr);
    auto t_leg2_order_id2 = t_com_order_check2.m_msg->leg2_order_id();
    auto t_spot_order_check2 = spot_related_result[2].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id2);
    ASSERT_NE(t_spot_order_check2.m_msg, nullptr);
    t_spot_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill).CheckLeaves("0", "0");

    auto t_future_com_order_check2 =
        future_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check2.m_msg, nullptr);
    auto t_leg1_order_id2 = t_future_com_order_check2.m_msg->leg1_order_id();
    auto t_future_order_check2 =
        future_related_result[2].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id2);
    ASSERT_NE(t_future_order_check2.m_msg, nullptr);
    t_future_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);
    //    t_com_order_check2.CheckStatus(ECombOrderStatus::Filled, ECombCrossStatus::TakerFill);

    // Maker Filled
    auto m_com_order_check2 =
        spot_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check2.m_msg, nullptr);
    auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
    auto m_spot_order_check2 = spot_related_result[3].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
    ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
    m_spot_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill).CheckLeaves("0", "0");

    auto m_future_com_order_check2 =
        future_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check2.m_msg, nullptr);
    auto m_leg1_order_id2 = m_future_com_order_check2.m_msg->leg1_order_id();
    auto m_future_order_check2 =
        future_related_result[3].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id2);
    ASSERT_NE(m_future_order_check2.m_msg, nullptr);
    m_future_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);

    // check last comb dump
    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Filled, ECombCrossStatus::MakerFill);
  }
}

TEST_F(ComboFillTest, maker_cross_lag_with_spot_leg) {
  GTEST_SKIP();
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTCUSDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  biz::order_link_id_t m_order_link_id{te->GenUUID()};

  // 下单->改单->撤单 全部成功
  {
    // 停止撮合
    te->SuspendCross();

    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;

    // maker replace
    price = "200";
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, qty);
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_resp1 = user1.comb_openapi_replace_order(m_replace_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::ErrorCode::kErrorCodeSuccess);

    // maker 撤单
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct2;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct2);
    std::ignore = m_ct2;
    ASSERT_EQ(m_ct2.RetCode(), error::ErrorCode::kErrorCodeSuccess);

    te->ResumeCross();
    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    // [Maker NewAccepted, Maker Replaced, Maker Cancelled]
    ASSERT_EQ(spot_related_result.size(), 3);
    ASSERT_EQ(future_related_result.size(), 3);

    // Maker New Accepted
    auto m_com_order_check1 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_leg2_order_id = m_com_order_check1.m_msg->leg2_order_id();
    auto m_spot_order_check1 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("1", "9950");
    m_com_order_check1.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    auto m_future_com_order_check1 =
        future_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_leg1_order_id = m_future_com_order_check1.m_msg->leg1_order_id();
    auto m_future_order_check1 =
        future_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    m_future_com_order_check1.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // Maker Replaced
    auto m_com_order_check2 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check2.m_msg, nullptr);
    auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
    auto m_spot_order_check2 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
    ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
    m_spot_order_check2.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded).CheckLeaves("1", "9900");
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_com_order_check2.CheckPrice(var_price);
    m_com_order_check2.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::ReAdded);

    auto m_future_com_order_check2 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check2.m_msg, nullptr);
    auto m_leg1_order_id2 = m_future_com_order_check2.m_msg->leg1_order_id();
    auto m_future_order_check2 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id2);
    ASSERT_NE(m_future_order_check2.m_msg, nullptr);
    m_future_order_check2.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded);
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_future_com_order_check2.CheckPrice(var_price);
    m_future_com_order_check2.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::ReAdded);

    // Maker Cancelled
    auto m_com_order_check3 =
        spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check3.m_msg, nullptr);
    auto m_leg2_order_id3 = m_com_order_check3.m_msg->leg2_order_id();
    auto m_spot_order_check3 = spot_related_result[2].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id3);
    ASSERT_NE(m_spot_order_check3.m_msg, nullptr);
    m_spot_order_check3.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled).CheckLeaves("1", "9900");

    auto m_future_com_order_check3 =
        future_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check3.m_msg, nullptr);
    auto m_leg1_order_id3 = m_future_com_order_check3.m_msg->leg1_order_id();
    auto m_future_order_check3 =
        future_related_result[2].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id3);
    ASSERT_NE(m_future_order_check3.m_msg, nullptr);
    m_future_order_check3.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);

    // check last comb dump
    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Cancelled, ECombCrossStatus::Canceled);
  }

  // 重新生成orderLinkID
  m_order_link_id = te->GenUUID();
  price = "100";
  // 下单->部分成交->改单->撤单 全部成功
  {
    // 停止撮合
    te->SuspendCross();

    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;

    // taker partial filled
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price,
                                              "0.5");
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    // maker replace
    price = "200";
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, qty);
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_resp1 = user1.comb_openapi_replace_order(m_replace_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::ErrorCode::kErrorCodeSuccess);

    // maker 撤单
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct2;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct2);
    std::ignore = m_ct2;
    ASSERT_EQ(m_ct2.RetCode(), error::ErrorCode::kErrorCodeSuccess);

    te->ResumeCross();
    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    // [Maker NewAccepted, Taker NewAccepted, Maker PendingReplace, Maker PendingCancelled, Taker Filled, Maker
    // Cancelled]
    ASSERT_EQ(spot_related_result.size(), 6);
    ASSERT_EQ(future_related_result.size(), 6);

    // Maker New Accepted
    auto m_com_order_check1 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_leg2_order_id = m_com_order_check1.m_msg->leg2_order_id();
    auto m_spot_order_check1 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("1", "9950");
    m_com_order_check1.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    auto m_future_com_order_check1 =
        future_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_leg1_order_id = m_future_com_order_check1.m_msg->leg1_order_id();
    auto m_future_order_check1 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    m_future_com_order_check1.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // Taker New Accepted
    auto t_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check1.m_msg, nullptr);
    auto t_leg2_order_id1 = t_com_order_check1.m_msg->leg2_order_id();
    auto t_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id1);
    ASSERT_NE(t_spot_order_check1.m_msg, nullptr);
    t_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("0.5", "4975");

    auto t_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check1.m_msg, nullptr);
    auto t_leg1_order_id1 = t_future_com_order_check1.m_msg->leg1_order_id();
    auto t_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id1);
    ASSERT_NE(t_future_order_check1.m_msg, nullptr);
    t_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // Maker Replaced
    auto m_com_order_check2 =
        spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check2.m_msg, nullptr);
    auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
    auto m_spot_order_check2 = spot_related_result[2].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
    ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
    m_spot_order_check2.CheckStatus(EOrderStatus::New, ECrossStatus::PendingReplace).CheckLeaves("1", "9900");
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_com_order_check2.CheckPrice(var_price);
    m_com_order_check2.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetReAdded);

    auto m_future_com_order_check2 =
        future_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check2.m_msg, nullptr);
    auto m_leg1_order_id2 = m_future_com_order_check2.m_msg->leg1_order_id();
    auto m_future_order_check2 =
        future_related_result[2].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id2);
    ASSERT_NE(m_future_order_check2.m_msg, nullptr);
    m_future_order_check2.CheckStatus(EOrderStatus::New, ECrossStatus::PendingReplace);
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_future_com_order_check2.CheckPrice(var_price);
    m_future_com_order_check2.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetReAdded);

    // Maker Cancelled
    auto m_com_order_check3 =
        spot_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check3.m_msg, nullptr);
    auto m_leg2_order_id3 = m_com_order_check3.m_msg->leg2_order_id();
    auto m_spot_order_check3 = spot_related_result[3].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id3);
    ASSERT_NE(m_spot_order_check3.m_msg, nullptr);
    m_spot_order_check3.CheckStatus(EOrderStatus::New, ECrossStatus::PendingCancel).CheckLeaves("1", "9900");
    m_com_order_check3.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetCanceled);

    auto m_future_com_order_check3 =
        future_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check3.m_msg, nullptr);
    auto m_leg1_order_id3 = m_future_com_order_check3.m_msg->leg1_order_id();
    auto m_future_order_check3 =
        future_related_result[3].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id3);
    ASSERT_NE(m_future_order_check3.m_msg, nullptr);
    m_future_order_check3.CheckStatus(EOrderStatus::New, ECrossStatus::PendingCancel);
    m_future_com_order_check3.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetCanceled);

    // Taker Filled
    auto t_com_order_check2 =
        spot_related_result[4].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check2.m_msg, nullptr);
    auto t_leg2_order_id2 = t_com_order_check2.m_msg->leg2_order_id();
    auto t_spot_order_check2 = spot_related_result[4].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id2);
    ASSERT_NE(t_spot_order_check2.m_msg, nullptr);
    t_spot_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill).CheckLeaves("0", "0");

    auto t_future_com_order_check2 =
        future_related_result[4].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check2.m_msg, nullptr);
    auto t_leg1_order_id2 = t_future_com_order_check2.m_msg->leg1_order_id();
    auto t_future_order_check2 =
        future_related_result[4].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id2);
    ASSERT_NE(t_future_order_check2.m_msg, nullptr);
    t_future_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // Maker Filled
    auto m_com_order_check4 =
        spot_related_result[5].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check4.m_msg, nullptr);
    auto m_leg2_order_id4 = m_com_order_check4.m_msg->leg2_order_id();
    auto m_spot_order_check4 = spot_related_result[5].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id4);
    ASSERT_NE(m_spot_order_check4.m_msg, nullptr);
    m_spot_order_check4.CheckStatus(EOrderStatus::Filled, ECrossStatus::Canceled).CheckLeaves("0.5", "4950");

    auto m_future_com_order_check4 =
        future_related_result[5].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check4.m_msg, nullptr);
    auto m_leg1_order_id4 = m_future_com_order_check4.m_msg->leg1_order_id();
    auto m_future_order_check4 =
        future_related_result[5].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id4);
    ASSERT_NE(m_future_order_check4.m_msg, nullptr);
    m_future_order_check4.CheckStatus(EOrderStatus::Filled, ECrossStatus::Canceled);

    // check last comb dump
    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Filled, ECombCrossStatus::Canceled);
  }

  // 重新生成orderLinkID
  m_order_link_id = te->GenUUID();
  price = "100";
  // 下单->改单->撤单->成交 改单，撤单失败
  {
    // 停止撮合
    te->SuspendCross();

    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_EQ(m_result.m_msg.get(), nullptr);

    // taker filled
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, qty);
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    // maker replace
    price = "200";
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, qty);
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_resp1 = user1.comb_openapi_replace_order(m_replace_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::ErrorCode::kErrorCodeSuccess);

    // maker 撤单
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct2;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct2);
    std::ignore = m_ct2;
    ASSERT_EQ(m_ct2.RetCode(), error::ErrorCode::kErrorCodeSuccess);

    te->ResumeCross();
    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    // [Maker NewAccepted, Taker New Accepted, Maker PendingReplace, Maker PendingCancelled,Taker Filled, MakerFilled]
    ASSERT_EQ(spot_related_result.size(), 6);
    ASSERT_EQ(future_related_result.size(), 6);

    // Maker New Accepted
    auto m_com_order_check1 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_leg2_order_id = m_com_order_check1.m_msg->leg2_order_id();
    auto m_spot_order_check1 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("1", "9950");
    m_com_order_check1.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    auto m_future_com_order_check1 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_leg1_order_id = m_future_com_order_check1.m_msg->leg1_order_id();
    auto m_future_order_check1 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    m_future_com_order_check1.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // Taker New Accepted
    auto t_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check1.m_msg, nullptr);
    auto t_leg2_order_id1 = t_com_order_check1.m_msg->leg2_order_id();
    auto t_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id1);
    ASSERT_NE(t_spot_order_check1.m_msg, nullptr);
    t_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("1", "9950");

    auto t_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check1.m_msg, nullptr);
    auto t_leg1_order_id1 = t_future_com_order_check1.m_msg->leg1_order_id();
    auto t_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id1);
    ASSERT_NE(t_future_order_check1.m_msg, nullptr);
    t_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // Maker Replaced
    auto m_com_order_check2 =
        spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check2.m_msg, nullptr);
    auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
    auto m_spot_order_check2 = spot_related_result[2].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
    ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
    m_spot_order_check2.CheckStatus(EOrderStatus::New, ECrossStatus::PendingReplace).CheckLeaves("1", "9900");
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_com_order_check2.CheckPrice(var_price);
    m_com_order_check2.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetCanceled);

    auto m_future_com_order_check2 =
        future_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check2.m_msg, nullptr);
    auto m_leg1_order_id2 = m_future_com_order_check2.m_msg->leg1_order_id();
    auto m_future_order_check2 =
        future_related_result[2].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id2);
    ASSERT_NE(m_future_order_check2.m_msg, nullptr);
    m_future_order_check2.CheckStatus(EOrderStatus::New, ECrossStatus::PendingReplace);
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_future_com_order_check2.CheckPrice(var_price);
    m_future_com_order_check2.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetCanceled);

    // Maker Cancelled
    auto m_com_order_check3 =
        spot_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check3.m_msg, nullptr);
    auto m_leg2_order_id3 = m_com_order_check3.m_msg->leg2_order_id();
    auto m_spot_order_check3 = spot_related_result[3].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id3);
    ASSERT_NE(m_spot_order_check3.m_msg, nullptr);
    m_spot_order_check3.CheckStatus(EOrderStatus::New, ECrossStatus::PendingCancel).CheckLeaves("1", "9900");
    m_com_order_check3.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetCanceled);

    auto m_future_com_order_check3 =
        future_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check3.m_msg, nullptr);
    auto m_leg1_order_id3 = m_future_com_order_check3.m_msg->leg1_order_id();
    auto m_future_order_check3 =
        future_related_result[3].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id3);
    ASSERT_NE(m_future_order_check3.m_msg, nullptr);
    m_future_order_check3.CheckStatus(EOrderStatus::New, ECrossStatus::PendingCancel);
    m_future_com_order_check3.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetCanceled);

    // Taker Filled
    auto t_com_order_check2 =
        spot_related_result[4].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check2.m_msg, nullptr);
    auto t_leg2_order_id2 = t_com_order_check2.m_msg->leg2_order_id();
    auto t_spot_order_check2 = spot_related_result[4].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id2);
    ASSERT_NE(t_spot_order_check2.m_msg, nullptr);
    t_spot_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill).CheckLeaves("0", "0");

    auto t_future_com_order_check2 =
        future_related_result[4].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check2.m_msg, nullptr);
    auto t_leg1_order_id2 = t_future_com_order_check2.m_msg->leg1_order_id();
    auto t_future_order_check2 =
        future_related_result[4].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id2);
    ASSERT_NE(t_future_order_check2.m_msg, nullptr);
    t_future_order_check2.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // Maker Filled
    auto m_com_order_check4 =
        spot_related_result[5].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check4.m_msg, nullptr);
    auto m_leg2_order_id4 = m_com_order_check4.m_msg->leg2_order_id();
    auto m_spot_order_check4 = spot_related_result[5].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id4);
    ASSERT_NE(m_spot_order_check4.m_msg, nullptr);
    m_spot_order_check4.CheckStatus(EOrderStatus::Filled, ECrossStatus::Canceled).CheckLeaves("0", "0");

    auto m_future_com_order_check4 =
        future_related_result[5].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check4.m_msg, nullptr);
    auto m_leg1_order_id4 = m_future_com_order_check4.m_msg->leg1_order_id();
    auto m_future_order_check4 =
        future_related_result[5].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id4);
    ASSERT_NE(m_future_order_check4.m_msg, nullptr);
    m_future_order_check4.CheckStatus(EOrderStatus::Filled, ECrossStatus::Canceled);

    // check last comb dump
    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    // todo： 期货那边也应该将组合订单状态设置为Filled.否则这里check不过
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Filled, ECombCrossStatus::Canceled);
  }
}

TEST_F(ComboFillTest, maker_cross_lag_with_spot_leg_2) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTCUSDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  biz::order_link_id_t m_order_link_id{te->GenUUID()};

  // 下单->改单->部分成交->撤单->成交 改单成功，撤单失败
  {
    // 停止撮合
    te->SuspendCross();

    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_EQ(m_result.m_msg.get(), nullptr);

    // taker partial filled
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price,
                                              "0.5");
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    // maker replace
    price = "200";
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, qty);
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_resp1 = user1.comb_openapi_replace_order(m_replace_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::ErrorCode::kErrorCodeSuccess);

    // taker filled
    biz::order_link_id_t t_order_link_id1{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build1(symbol, t_order_link_id1, opp_side, order_type, time_in_force, price,
                                               "0.5");
    RpcContext t_ct1;
    auto t_resp1 = user2.comb_siteapi_create_order(t_create_build1.Build(), t_ct1);

    // maker 撤单
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct2;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct2);
    std::ignore = m_ct2;
    ASSERT_EQ(m_ct2.RetCode(), error::ErrorCode::kErrorCodeSuccess);

    te->ResumeCross();
    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    // int i = 0;
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        // std::string res_str;
        // std::cout << "res_str " << i++ << ":" << utils::PbMessageToJsonString(*leg_result.m_msg.get(), &res_str)
        //           << '\n';
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }

        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    // [Maker NewAccepted, Taker New Accepted order 1, Maker PendingReplace, Taker New Accepted order 2, Taker fill,
    // maker partial filled, Maker Cancelled, taker fill, maker fill]
    ASSERT_EQ(spot_related_result.size(), 9);
    ASSERT_EQ(future_related_result.size(), 9);

    // Maker New Accepted
    auto m_com_order_check1 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check1.m_msg, nullptr);
    auto m_leg2_order_id = m_com_order_check1.m_msg->leg2_order_id();
    auto m_spot_order_check1 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id);
    ASSERT_NE(m_spot_order_check1.m_msg, nullptr);
    m_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("1", "9950");
    m_com_order_check1.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    auto m_future_com_order_check1 =
        future_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check1.m_msg, nullptr);
    auto m_leg1_order_id = m_future_com_order_check1.m_msg->leg1_order_id();
    auto m_future_order_check1 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id);
    ASSERT_NE(m_future_order_check1.m_msg, nullptr);
    m_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    m_future_com_order_check1.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // Taker New Accepted order 1
    auto t_com_order_check1 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check1.m_msg, nullptr);
    auto t_leg2_order_id1 = t_com_order_check1.m_msg->leg2_order_id();
    auto t_spot_order_check1 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id1);
    ASSERT_NE(t_spot_order_check1.m_msg, nullptr);
    t_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("0.5", "4975");

    auto t_future_com_order_check1 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check1.m_msg, nullptr);
    auto t_leg1_order_id1 = t_future_com_order_check1.m_msg->leg1_order_id();
    auto t_future_order_check1 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id1);
    ASSERT_NE(t_future_order_check1.m_msg, nullptr);
    t_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // Maker Replaced
    auto m_com_order_check2 =
        spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check2.m_msg, nullptr);
    auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
    auto m_spot_order_check2 = spot_related_result[2].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
    ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
    m_spot_order_check2.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded).CheckLeaves("1", "9900");
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_com_order_check2.CheckPrice(var_price);
    m_com_order_check2.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetReAdded);

    auto m_future_com_order_check2 =
        future_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check2.m_msg, nullptr);
    auto m_leg1_order_id2 = m_future_com_order_check2.m_msg->leg1_order_id();
    auto m_future_order_check2 =
        future_related_result[2].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id2);
    ASSERT_NE(m_future_order_check2.m_msg, nullptr);
    m_future_order_check2.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded);
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_future_com_order_check2.CheckPrice(var_price);
    m_future_com_order_check2.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetReAdded);

    // Taker New Accepted order 2
    auto t_com_order_check2 =
        spot_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id1);
    ASSERT_NE(t_com_order_check2.m_msg, nullptr);
    auto t_leg2_order_id2 = t_com_order_check2.m_msg->leg2_order_id();
    auto t_spot_order_check2 = spot_related_result[3].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id2);
    ASSERT_NE(t_spot_order_check2.m_msg, nullptr);
    t_spot_order_check2.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("0.5", "4950");

    auto t_future_com_order_check2 =
        future_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id1);
    ASSERT_NE(t_future_com_order_check2.m_msg, nullptr);
    auto t_leg1_order_id2 = t_future_com_order_check2.m_msg->leg1_order_id();
    auto t_future_order_check2 =
        future_related_result[3].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id2);
    ASSERT_NE(t_future_order_check2.m_msg, nullptr);
    t_future_order_check2.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    if (spot_related_result[4].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id).m_msg != nullptr) {
      // Taker Filled order 1
      auto t_com_order_check3 =
          spot_related_result[4].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
      ASSERT_NE(t_com_order_check3.m_msg, nullptr);
      auto t_leg2_order_id3 = t_com_order_check3.m_msg->leg2_order_id();
      auto t_spot_order_check3 =
          spot_related_result[4].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id3);
      ASSERT_NE(t_spot_order_check3.m_msg, nullptr);
      t_spot_order_check3.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill).CheckLeaves("0", "0");

      // Maker partial filled
      auto m_com_order_check3 =
          spot_related_result[5].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_com_order_check3.m_msg, nullptr);
      auto m_leg2_order_id3 = m_com_order_check3.m_msg->leg2_order_id();
      auto m_spot_order_check3 =
          spot_related_result[5].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id3);
      ASSERT_NE(m_spot_order_check3.m_msg, nullptr);
      m_spot_order_check3.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill)
          .CheckLeaves("0.5", "4950");

      // Maker Cancelled
      auto m_com_order_check4 =
          spot_related_result[6].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_com_order_check4.m_msg, nullptr);
      auto m_leg2_order_id4 = m_com_order_check4.m_msg->leg2_order_id();
      auto m_spot_order_check4 =
          spot_related_result[6].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id4);
      ASSERT_NE(m_spot_order_check4.m_msg, nullptr);
      m_spot_order_check4.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::PendingCancel)
          .CheckLeaves("0.5", "4950");
      m_com_order_check4.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetCanceled);
    } else {
      // Maker Pending cancelled
      auto m_com_order_check3 =
          spot_related_result[4].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_com_order_check3.m_msg, nullptr);
      auto m_leg2_order_id3 = m_com_order_check3.m_msg->leg2_order_id();
      auto m_spot_order_check3 =
          spot_related_result[4].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id3);
      ASSERT_NE(m_spot_order_check3.m_msg, nullptr);
      m_spot_order_check3.CheckStatus(EOrderStatus::New, ECrossStatus::PendingCancel).CheckLeaves("1", "9900");

      // Taker Filled order 1
      auto t_com_order_check3 =
          spot_related_result[5].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
      ASSERT_NE(t_com_order_check3.m_msg, nullptr);
      auto t_leg2_order_id3 = t_com_order_check3.m_msg->leg2_order_id();
      auto t_spot_order_check3 =
          spot_related_result[5].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id3);
      ASSERT_NE(t_spot_order_check3.m_msg, nullptr);
      t_spot_order_check3.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill).CheckLeaves("0", "0");

      // Maker Cancelled
      auto m_com_order_check4 =
          spot_related_result[6].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_com_order_check4.m_msg, nullptr);
      auto m_leg2_order_id4 = m_com_order_check4.m_msg->leg2_order_id();
      auto m_spot_order_check4 =
          spot_related_result[6].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id4);
      ASSERT_NE(m_spot_order_check4.m_msg, nullptr);
      m_spot_order_check4.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill)
          .CheckLeaves("0.5", "4950");
      m_com_order_check4.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetCanceled);
    }

    // Taker Filled order 2
    auto t_com_order_check4 =
        spot_related_result[7].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id1);
    ASSERT_NE(t_com_order_check4.m_msg, nullptr);
    auto t_leg2_order_id4 = t_com_order_check4.m_msg->leg2_order_id();
    auto t_spot_order_check4 = spot_related_result[7].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id4);
    ASSERT_NE(t_spot_order_check4.m_msg, nullptr);
    t_spot_order_check4.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill).CheckLeaves("0", "0");

    // Maker Filled
    auto m_com_order_check5 =
        spot_related_result[8].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check5.m_msg, nullptr);
    auto m_leg2_order_id5 = m_com_order_check5.m_msg->leg2_order_id();
    auto m_spot_order_check5 = spot_related_result[8].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id5);
    ASSERT_NE(m_spot_order_check5.m_msg, nullptr);
    m_spot_order_check5.CheckStatus(EOrderStatus::Filled, ECrossStatus::Canceled).CheckLeaves("0", "0");

    // check last comb dump
    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::Filled, ECombCrossStatus::Canceled);
  }
}

TEST_F(ComboFillTest, maker_partial_filled_then_replace_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTCUSDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  biz::order_link_id_t m_order_link_id{te->GenUUID()};

  // 下单->改单->部分成交 改单成功
  {
    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);

    // Maker New Accepted
    auto m_com_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check.m_msg, nullptr);
    auto leg2_order_id = m_com_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("1", "9950");
    auto leg1_order_id = m_com_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    m_com_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // 停止撮合
    te->SuspendCross();

    // taker partial filled
    biz::order_link_id_t t_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price,
                                              "0.5");
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);

    // maker replace
    price = "200";
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, qty);
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_resp1 = user1.comb_openapi_replace_order(m_replace_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::ErrorCode::kErrorCodeSuccess);

    te->ResumeCross();
    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    // [Taker New Accepted order 1, Maker PendingReplace, Taker New Accepted order 2, Taker fill, maker Readd]
    ASSERT_EQ(spot_related_result.size(), 4);
    ASSERT_EQ(future_related_result.size(), 4);

    // Taker New Accepted order 1
    auto t_com_order_check1 =
        spot_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check1.m_msg, nullptr);
    auto t_leg2_order_id1 = t_com_order_check1.m_msg->leg2_order_id();
    auto t_spot_order_check1 = spot_related_result[0].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id1);
    ASSERT_NE(t_spot_order_check1.m_msg, nullptr);
    t_spot_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("0.5", "4975");

    auto t_future_com_order_check1 =
        future_related_result[0].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check1.m_msg, nullptr);
    auto t_leg1_order_id1 = t_future_com_order_check1.m_msg->leg1_order_id();
    auto t_future_order_check1 =
        future_related_result[0].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id1);
    ASSERT_NE(t_future_order_check1.m_msg, nullptr);
    t_future_order_check1.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // Maker Replaced
    auto m_com_order_check2 =
        spot_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check2.m_msg, nullptr);
    auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
    auto m_spot_order_check2 = spot_related_result[1].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
    ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
    m_spot_order_check2.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded).CheckLeaves("1", "9900");
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_com_order_check2.CheckPrice(var_price);
    m_com_order_check2.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetReAdded);

    auto m_future_com_order_check2 =
        future_related_result[1].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check2.m_msg, nullptr);
    auto m_leg1_order_id2 = m_future_com_order_check2.m_msg->leg1_order_id();
    auto m_future_order_check2 =
        future_related_result[1].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id2);
    ASSERT_NE(m_future_order_check2.m_msg, nullptr);
    m_future_order_check2.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded);
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_future_com_order_check2.CheckPrice(var_price);
    m_future_com_order_check2.CheckStatus(ECombOrderStatus::WaitToRecvPassthrough, ECombCrossStatus::WaitToSetReAdded);

    // Taker Filled order 1
    auto t_com_order_check3 =
        spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_com_order_check3.m_msg, nullptr);
    auto t_leg2_order_id3 = t_com_order_check3.m_msg->leg2_order_id();
    auto t_spot_order_check3 = spot_related_result[2].RefSpotMarginResult().RefRelatedOrderByOrderId(t_leg2_order_id3);
    ASSERT_NE(t_spot_order_check3.m_msg, nullptr);
    t_spot_order_check3.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill).CheckLeaves("0", "0");

    auto t_future_com_order_check3 =
        future_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_future_com_order_check3.m_msg, nullptr);
    auto t_leg1_order_id3 = t_future_com_order_check3.m_msg->leg1_order_id();
    auto t_future_order_check3 =
        future_related_result[2].RefFutureMarginResult().RefRelatedOrderByOrderId(t_leg1_order_id3);
    ASSERT_NE(t_future_order_check3.m_msg, nullptr);
    t_future_order_check3.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // Maker partial filled
    auto m_com_order_check3 =
        spot_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check3.m_msg, nullptr);
    auto m_leg2_order_id3 = m_com_order_check3.m_msg->leg2_order_id();
    auto m_spot_order_check3 = spot_related_result[3].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id3);
    ASSERT_NE(m_spot_order_check3.m_msg, nullptr);
    m_spot_order_check3.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::ReAdded).CheckLeaves("0.5", "4950");

    auto m_future_com_order_check3 =
        future_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_future_com_order_check3.m_msg, nullptr);
    auto m_leg1_order_id3 = m_future_com_order_check3.m_msg->leg1_order_id();
    auto m_future_order_check3 =
        future_related_result[3].RefFutureMarginResult().RefRelatedOrderByOrderId(m_leg1_order_id3);
    ASSERT_NE(m_future_order_check3.m_msg, nullptr);
    m_future_order_check3.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::ReAdded);

    // check last comb dump
    auto m_com_order_check_last = com_last_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check_last.m_msg, nullptr);
    m_com_order_check_last.CheckStatus(ECombOrderStatus::PartiallyFilled, ECombCrossStatus::ReAdded);
  }
}

TEST_F(ComboFillTest, maker_replace_rejected_with_spot_leg_passthrough_first) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTCUSDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  biz::order_link_id_t m_order_link_id{te->GenUUID()};

  // 下单
  {
    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
  }

  // 撮合进入单步模式
  te->SetCrossSingleStepMode();
  biz::order_link_id_t t_order_link_id{te->GenUUID()};
  {
    // taker filled
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, qty);
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);
    EXPECT_EQ(error::kErrorCodeSuccess, t_resp->ret_code());
  }

  // maker replace
  {
    price = "200";
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, qty);
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_resp1 = user1.comb_openapi_replace_order(m_replace_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::ErrorCode::kErrorCodeSuccess);
  }

  // 启撮合
  {
    // 组合 taker
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，有一条成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 1);

    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }

  {
    // 组合 maker
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，有一条成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 1);

    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }

  {
    // 远腿单腿透传包
    te->ProcessOneXReq(te->GetSpotCrossIdx());

    // taker的远腿单腿透传包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);

    // 有一个组合订单记录，没有成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 0);

    result = te->PopResult();
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }

  {
    // 近腿单腿透传包
    te->ProcessOneXReq(te->GetFutureCrossIdxBySymbolId(5));

    // taker的近腿单腿透传包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，没有成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 0);

    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::TakerFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::Filled);

    result = te->PopResult();
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::MakerFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::Filled);
  }

  {
    // 组合改单
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    // 有一个组合订单记录，没有成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 0);

    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::ReplaceRejected);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::Filled);
    auto leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::ReplaceRejected).CheckLeaves("0", "0");

    {
      auto m_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }

    auto leg1_order_id = m_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::ReplaceRejected);
  }
}

TEST_F(ComboFillTest, maker_replace_rejected_with_spot_leg_passthrough_last) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTCUSDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  biz::order_link_id_t m_order_link_id{te->GenUUID()};

  // 下单
  {
    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
  }

  // 撮合进入单步模式
  te->SetCrossSingleStepMode();
  biz::order_link_id_t t_order_link_id{te->GenUUID()};
  {
    // taker filled
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, qty);
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);
    EXPECT_EQ(error::kErrorCodeSuccess, t_resp->ret_code());
  }

  // maker replace
  {
    price = "200";
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, qty);
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct1;
    auto m_resp1 = user1.comb_openapi_replace_order(m_replace_build.Build(), m_ct1);
    std::ignore = m_ct1;
    ASSERT_EQ(m_ct1.RetCode(), error::ErrorCode::kErrorCodeSuccess);
  }

  // 启撮合
  {
    // 组合 taker
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，有一条成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 1);

    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }

  {
    // 组合 maker
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，有一条成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 1);

    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }

  {
    // 近腿单腿透传包
    te->ProcessOneXReq(te->GetFutureCrossIdxBySymbolId(5));

    // taker的近腿单腿透传包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，没有成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 0);

    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);

    result = te->PopResult();
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }

  {
    // 远腿单腿透传包
    te->ProcessOneXReq(te->GetSpotCrossIdx());

    // taker的远腿单腿透传包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::TakerFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::Filled);

    // 有一个组合订单记录，没有成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 0);

    result = te->PopResult();
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::MakerFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::Filled);
  }

  {
    // 组合改单
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    // 有一个组合订单记录，没有成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 0);

    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::ReplaceRejected);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::Filled);
    auto leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::ReplaceRejected).CheckLeaves("0", "0");

    auto leg1_order_id = m_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::ReplaceRejected);
  }
}

TEST_F(ComboFillTest, maker_cancel_rejected_with_spot_leg_first) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTCUSDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  biz::order_link_id_t m_order_link_id{te->GenUUID()};

  // 下单
  {
    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
  }

  // 撮合进入单步模式
  te->SetCrossSingleStepMode();
  biz::order_link_id_t t_order_link_id{te->GenUUID()};
  {
    // taker filled
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, qty);
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);
    EXPECT_EQ(error::kErrorCodeSuccess, t_resp->ret_code());
  }

  // maker 撤单
  {
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct2;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct2);
    std::ignore = m_ct2;
    ASSERT_EQ(m_ct2.RetCode(), error::ErrorCode::kErrorCodeSuccess);
  }

  // 启撮合
  {
    // 组合 taker
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，有一条成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 1);

    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }

  {
    // 组合 maker
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，有一条成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 1);

    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }

  {
    // 近腿单腿透传包
    te->ProcessOneXReq(te->GetSpotCrossIdx());

    // taker的近腿单腿透传包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);

    // 有一个组合订单记录，没有成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 0);

    result = te->PopResult();
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }

  {
    // 远腿单腿透传包
    te->ProcessOneXReq(te->GetFutureCrossIdxBySymbolId(5));

    // taker的近腿单腿透传包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，没有成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 0);

    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::TakerFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::Filled);

    result = te->PopResult();
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::MakerFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::Filled);
  }

  {
    // 组合撤单
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    // 有一个组合订单记录，没有成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 0);

    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::CancelRejected);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::Filled);
    auto leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::CancelRejected).CheckLeaves("0", "0");

    auto leg1_order_id = m_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::CancelRejected);
  }
}

TEST_F(ComboFillTest, maker_cancel_rejected_with_spot_leg_last) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTCUSDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  biz::order_link_id_t m_order_link_id{te->GenUUID()};

  // 下单
  {
    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
  }

  // 撮合进入单步模式
  te->SetCrossSingleStepMode();
  biz::order_link_id_t t_order_link_id{te->GenUUID()};
  {
    // taker filled
    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price, qty);
    RpcContext t_ct;
    auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);
    EXPECT_EQ(error::kErrorCodeSuccess, t_resp->ret_code());
  }

  // maker 撤单
  {
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct2;
    auto m_cancel_resp = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct2);
    std::ignore = m_ct2;
    ASSERT_EQ(m_ct2.RetCode(), error::ErrorCode::kErrorCodeSuccess);
  }

  // 启撮合
  {
    // 组合 taker
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，有一条成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 1);

    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }

  {
    // 组合 maker
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，有一条成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 1);

    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }

  {
    // 远腿单腿透传包
    te->ProcessOneXReq(te->GetFutureCrossIdxBySymbolId(5));

    // taker的远腿单腿透传包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);

    // 有一个组合订单记录，没有成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 0);

    result = te->PopResult();
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }

  {
    // 近腿单腿透传包
    te->ProcessOneXReq(te->GetSpotCrossIdx());

    // taker的近腿单腿透传包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，没有成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 0);

    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::TakerFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::Filled);

    result = te->PopResult();
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::MakerFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::Filled);
  }

  {
    // 组合改单
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    // 有一个组合订单记录，没有成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 0);

    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::CancelRejected);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::Filled);
    auto leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::CancelRejected).CheckLeaves("0", "0");

    {
      auto m_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }

    auto leg1_order_id = m_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::CancelRejected);
  }
}

TEST_F(ComboFillTest, maker_two_partialfills_then_replace_then_cancel_with_spot_leg) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "200000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Sell";
  std::string opp_side = "Buy";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "10";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTCUSDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  biz::order_link_id_t m_order_link_id{te->GenUUID()};

  {
    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);

    // Maker New Accepted
    auto m_com_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check.m_msg, nullptr);
    auto leg2_order_id = m_com_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("10", "99500");
    auto leg1_order_id = m_com_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    m_com_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // 停止撮合
    te->SuspendCross();

    {
      // taker partial filled
      biz::order_link_id_t t_order_link_id{te->GenUUID()};
      CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price,
                                                "1");
      RpcContext t_ct;
      auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);
    }

    {
      // taker partial filled
      biz::order_link_id_t t_order_link_id{te->GenUUID()};
      CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, opp_side, order_type, time_in_force, price,
                                                "1");
      RpcContext t_ct;
      auto t_resp = user2.comb_siteapi_create_order(t_create_build.Build(), t_ct);
    }

    {
      // maker replace qty
      CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, price, "8");
      m_replace_build.SetOrderLinkId(m_order_link_id);
      RpcContext m_ct1;
      auto m_resp1 = user1.comb_openapi_replace_order(m_replace_build.Build(), m_ct1);
      std::ignore = m_ct1;
      ASSERT_EQ(m_ct1.RetCode(), error::ErrorCode::kErrorCodeSuccess);
    }

    te->ResumeCross();

    std::deque<UnifiedV2ResultDTOChecker> spot_related_result;
    std::deque<UnifiedV2ResultDTOChecker> future_related_result;

    UnifiedV2ResultDTOChecker com_last_result(nullptr);
    while (true) {
      auto leg_result = te->PopResult();
      if (leg_result.m_msg == nullptr) {
        break;
      } else {
        /*
        std::string res_str;
        std::cout << "res_str:" << utils::PbMessageToJsonString(*leg_result.m_msg.get(), &res_str) << '\n';
        */
        com_last_result = leg_result;
        if (leg_result.m_msg->has_spot_margin_result()) {
          spot_related_result.push_back(leg_result);
        }
        if (leg_result.m_msg->has_futures_margin_result()) {
          future_related_result.push_back(leg_result);
        }
      }
    }

    ASSERT_EQ(spot_related_result.size(), 7);
    ASSERT_EQ(future_related_result.size(), 7);

    if (spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id).m_msg) {
      // [Taker New Accepted order 1, Taker New Accepted order 2,Maker PendingReplace, Taker fill 1,Maker fill 1, taker
      // fill 2, maker fill 2]
      std::cout << "case 1: [Taker New Accepted order 1, Taker New Accepted order 2,Maker PendingReplace, Taker fill "
                   "1,Maker fill 1, taker fill 2, maker fill 2]"
                << std::endl;
      // Maker Replaced
      {
        auto m_com_order_check2 =
            spot_related_result[2].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
        ASSERT_NE(m_com_order_check2.m_msg, nullptr);
        auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
        auto m_spot_order_check2 =
            spot_related_result[2].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
        ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
        m_spot_order_check2.CheckStatus(EOrderStatus::New, ECrossStatus::Replaced).CheckLeaves("8", "79600");

        auto m_wallet_checker = spot_related_result[2].RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
        m_wallet_checker.CheckCoin(ECoin::USDT);
        m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("99500"));
      }
      // Maker Fill for order 1
      {
        auto m_com_order_check2 =
            spot_related_result[4].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
        ASSERT_NE(m_com_order_check2.m_msg, nullptr);
        auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
        auto m_spot_order_check2 =
            spot_related_result[4].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
        ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
        m_spot_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill)
            .CheckLeaves("7", "69650");

        auto m_wallet_checker = spot_related_result[4].RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
        m_wallet_checker.CheckCoin(ECoin::USDT);
        m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("89550"));
      }
      // Maker Fill for order 2
      {
        auto m_com_order_check2 =
            spot_related_result[6].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
        ASSERT_NE(m_com_order_check2.m_msg, nullptr);
        auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
        auto m_spot_order_check2 =
            spot_related_result[6].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
        ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
        m_spot_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::Replaced)
            .CheckLeaves("6", "59700");

        auto m_wallet_checker = spot_related_result[6].RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
        m_wallet_checker.CheckCoin(ECoin::USDT);
        m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("59700"));
      }
    } else {
      // [Taker New Accepted order 1, Taker New Accepted order 2, Taker fill 1,Maker fill 1,Maker PendingReplace, taker
      // fill 2, maker fill 2]
      std::cout << "case 2: [Taker New Accepted order 1, Taker New Accepted order 2, Taker fill 1,Maker fill 1,Maker "
                   "PendingReplace, taker "
                   "fill 2, maker fill 2,]"
                << std::endl;
      // Maker Fill for order 1
      {
        auto m_com_order_check2 =
            spot_related_result[3].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
        ASSERT_NE(m_com_order_check2.m_msg, nullptr);
        auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
        auto m_spot_order_check2 =
            spot_related_result[3].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
        ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
        m_spot_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill)
            .CheckLeaves("9", "89550");

        auto m_wallet_checker = spot_related_result[3].RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
        m_wallet_checker.CheckCoin(ECoin::USDT);
        m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("89550"));
      }
      // Maker Replaced
      {
        auto m_com_order_check2 =
            spot_related_result[4].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
        ASSERT_NE(m_com_order_check2.m_msg, nullptr);
        auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
        auto m_spot_order_check2 =
            spot_related_result[4].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
        ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
        m_spot_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::Replaced)
            .CheckLeaves("7", "69650");

        auto m_wallet_checker = spot_related_result[4].RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
        m_wallet_checker.CheckCoin(ECoin::USDT);
        m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("89550"));
      }
      // Maker Fill for order 2
      {
        auto m_com_order_check2 =
            spot_related_result[6].RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
        ASSERT_NE(m_com_order_check2.m_msg, nullptr);
        auto m_leg2_order_id2 = m_com_order_check2.m_msg->leg2_order_id();
        auto m_spot_order_check2 =
            spot_related_result[6].RefSpotMarginResult().RefRelatedOrderByOrderId(m_leg2_order_id2);
        ASSERT_NE(m_spot_order_check2.m_msg, nullptr);
        m_spot_order_check2.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::Replaced)
            .CheckLeaves("6", "59700");

        auto m_wallet_checker = spot_related_result[6].RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
        m_wallet_checker.CheckCoin(ECoin::USDT);
        m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("59700"));
      }
    }
  }
}

TEST_F(ComboFillTest, taker_filled_cancel_after_passthrough) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "200000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Sell";
  std::string opp_side = "Buy";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "10";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTCUSDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  biz::order_link_id_t m_order_link_id{te->GenUUID()};

  {
    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user2.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);

    // Maker New Accepted
    auto m_com_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check.m_msg, nullptr);
    auto leg2_order_id = m_com_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("10", "99500");
    auto leg1_order_id = m_com_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    m_com_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);
  }

  // 撮合进入单步模式
  te->SetCrossSingleStepMode();

  biz::order_link_id_t t_order_link_id{te->GenUUID()};
  {
    // 用户1 下买单，作为taker，taker部分成交，先不pop result
    side = "Buy";
    qty = "11";
    price = "200";

    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(t_create_build.Build(), m_ct);
    EXPECT_EQ(error::kErrorCodeSuccess, m_resp->ret_code());
  }
  // 用户1发起撤单
  {
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(t_order_link_id);
    RpcContext m_ct;
    auto m_resp_c = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct);
    std::ignore = m_ct;
    std::ignore = m_resp_c;
  }

  {
    // 组合 taker
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，有一条成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 1);
    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->user_id(), uid);
    EXPECT_EQ(t_order_check.m_msg->side(), ESide::Buy);
    EXPECT_EQ(t_order_check.m_msg->qty_x(), 1100000000);
    EXPECT_EQ(t_order_check.m_msg->price_x(), 20000000000);
    EXPECT_EQ(t_order_check.m_msg->exec_qty_x(), 1000000000);
    EXPECT_EQ(t_order_check.m_msg->exec_type(), EExecType::Trade);
    EXPECT_EQ(t_order_check.m_msg->exec_price_x(), 10000000000);
    EXPECT_EQ(t_order_check.m_msg->exec_digest_map_size(), 1);
    EXPECT_EQ(t_order_check.m_msg->last_liquidity_ind(), ELastLiquidityInd::RemovedLiquidity);
    EXPECT_EQ(t_order_check.m_msg->opponent_order_id().empty(), false);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);

    auto leg2_order_id = t_order_check.m_msg->leg2_order_id();
    auto t_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(t_spot_order_check.m_msg, nullptr);
    t_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("11", "108900");

    {
      auto m_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("11"));
    }
  }

  {
    // 组合 maker
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }
  {
    // 近腿单腿透传包
    te->ProcessOneXReq(te->GetSpotCrossIdx());
    {
      // taker的近腿单腿透传包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
      ASSERT_NE(t_order_check.m_msg, nullptr);
      EXPECT_EQ(t_order_check.m_msg->last_liquidity_ind(), ELastLiquidityInd::RemovedLiquidity);
      EXPECT_EQ(t_order_check.m_msg->opponent_order_id().empty(), false);
      EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
      EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);

      auto leg2_order_id = t_order_check.m_msg->leg2_order_id();
      auto t_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
      ASSERT_NE(t_spot_order_check.m_msg, nullptr);
      t_spot_order_check.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::TakerFill).CheckLeaves("1", "9900");

      {
        auto m_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
        m_wallet_checker.CheckCoin(ECoin::BTC);
        m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("1"));
      }
    }
    {
      // maker的近腿单腿透传包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);

      auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_order_check.m_msg, nullptr);
      EXPECT_EQ(m_order_check.m_msg->last_liquidity_ind(), ELastLiquidityInd::AddedLiquidity);
      EXPECT_EQ(m_order_check.m_msg->opponent_order_id().empty(), false);
      EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
      EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);

      auto leg2_order_id = m_order_check.m_msg->leg2_order_id();
      auto m_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
      ASSERT_NE(m_spot_order_check.m_msg, nullptr);
      m_spot_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill).CheckLeaves("0", "0");

      {
        auto m_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
        m_wallet_checker.CheckCoin(ECoin::BTC);
        m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
      }
    }
  }

  {
    // 远腿单腿透传包
    te->ProcessOneXReq(te->GetFutureCrossIdxBySymbolId(5));
    {
      // taker的远腿单腿透传包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
    }

    {
      // maker的远腿单腿透传包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
    }
  }
  {
    // 组合撤单
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    EXPECT_EQ(t_order_check.m_msg->exec_digest_map_size(), 0);
    EXPECT_EQ(t_order_check.m_msg->last_liquidity_ind(), ELastLiquidityInd::LiquidityIndNA);
    EXPECT_EQ(t_order_check.m_msg->opponent_order_id().empty(), false);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::Canceled);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::Cancelled);

    auto leg2_order_id = t_order_check.m_msg->leg2_order_id();
    auto t_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(t_spot_order_check.m_msg, nullptr);
    t_spot_order_check.CheckStatus(EOrderStatus::PartiallyFilledCanceled, ECrossStatus::Canceled)
        .CheckLeaves("1", "9900");

    {
      auto m_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      m_wallet_checker.CheckCoin(ECoin::BTC);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }
  }
}

TEST_F(ComboFillTest, maker_filled_replacereject_cancelreject_after_passthrough) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "200000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Sell";
  std::string opp_side = "Buy";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "10";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTCUSDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  biz::order_link_id_t m_order_link_id{te->GenUUID()};

  {
    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);

    // Maker New Accepted
    auto m_com_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check.m_msg, nullptr);
    auto leg2_order_id = m_com_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("10", "99500");
    auto leg1_order_id = m_com_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    m_com_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);
  }

  // 撮合进入单步模式
  te->SetCrossSingleStepMode();

  biz::order_link_id_t t_order_link_id{te->GenUUID()};
  {
    // 用户2 下买单，作为taker，taker全部成交，先不pop result
    side = "Buy";
    qty = "10";
    price = "200";

    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user2.comb_siteapi_create_order(t_create_build.Build(), m_ct);
    EXPECT_EQ(error::kErrorCodeSuccess, m_resp->ret_code());
  }
  // 用户1发起改单
  {
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, "100", "11");
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_replace_ct;
    auto m_replace_resp = user1.comb_openapi_replace_order(m_replace_build.Build(), m_replace_ct);
    std::ignore = m_replace_ct;
    ASSERT_EQ(m_replace_ct.RetCode(), error::ErrorCode::kErrorCodeSuccess);
  }
  // 用户1发起撤单
  {
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct;
    auto m_resp_c = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct);
    std::ignore = m_ct;
    std::ignore = m_resp_c;
  }

  {
    // 组合 taker
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，有一条成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 1);
    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->user_id(), uid2);
    EXPECT_EQ(t_order_check.m_msg->side(), ESide::Buy);
    EXPECT_EQ(t_order_check.m_msg->qty_x(), 1000000000);
    EXPECT_EQ(t_order_check.m_msg->price_x(), 20000000000);
    EXPECT_EQ(t_order_check.m_msg->exec_qty_x(), 1000000000);
    EXPECT_EQ(t_order_check.m_msg->exec_type(), EExecType::Trade);
    EXPECT_EQ(t_order_check.m_msg->exec_price_x(), 10000000000);
    EXPECT_EQ(t_order_check.m_msg->exec_digest_map_size(), 1);
    EXPECT_EQ(t_order_check.m_msg->last_liquidity_ind(), ELastLiquidityInd::RemovedLiquidity);
    EXPECT_EQ(t_order_check.m_msg->opponent_order_id().empty(), false);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);

    auto leg2_order_id = t_order_check.m_msg->leg2_order_id();
    auto t_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(t_spot_order_check.m_msg, nullptr);
    t_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("10", "99000");

    {
      auto t_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      t_wallet_checker.CheckCoin(ECoin::BTC);
      t_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("10"));
    }
  }

  {
    // 组合 maker
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }
  {
    // 近腿单腿透传包
    te->ProcessOneXReq(te->GetSpotCrossIdx());
    {
      // taker的近腿单腿透传包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
      ASSERT_NE(t_order_check.m_msg, nullptr);
      EXPECT_EQ(t_order_check.m_msg->last_liquidity_ind(), ELastLiquidityInd::RemovedLiquidity);
      EXPECT_EQ(t_order_check.m_msg->opponent_order_id().empty(), false);
      EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
      EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);

      auto leg2_order_id = t_order_check.m_msg->leg2_order_id();
      auto t_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
      ASSERT_NE(t_spot_order_check.m_msg, nullptr);
      t_spot_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill).CheckLeaves("0", "0");

      {
        auto t_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
        t_wallet_checker.CheckCoin(ECoin::BTC);
        t_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
      }
    }
    {
      // maker的近腿单腿透传包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);

      auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_order_check.m_msg, nullptr);
      EXPECT_EQ(m_order_check.m_msg->last_liquidity_ind(), ELastLiquidityInd::AddedLiquidity);
      EXPECT_EQ(m_order_check.m_msg->opponent_order_id().empty(), false);
      EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
      EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);

      auto leg2_order_id = m_order_check.m_msg->leg2_order_id();
      auto m_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
      ASSERT_NE(m_spot_order_check.m_msg, nullptr);
      // 由于有pendingreplace pendingcancel 不能置为终态
      m_spot_order_check.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill).CheckLeaves("0", "0");

      {
        auto m_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
        m_wallet_checker.CheckCoin(ECoin::USDT);
        // 有pendingreplace 这里用reducecost 而不是 直接reset
        m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("9950"));
      }
    }
  }

  {
    // 远腿单腿透传包
    te->ProcessOneXReq(te->GetFutureCrossIdxBySymbolId(5));
    {
      // taker的远腿单腿透传包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
    }

    {
      // maker的远腿单腿透传包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
    }
  }
  {
    // 组合改单
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    EXPECT_EQ(m_order_check.m_msg->exec_digest_map_size(), 0);
    EXPECT_EQ(m_order_check.m_msg->last_liquidity_ind(), ELastLiquidityInd::LiquidityIndNA);
    EXPECT_EQ(m_order_check.m_msg->opponent_order_id().empty(), false);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::ReplaceRejected);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::Filled);

    auto leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    // 这里现货单腿会设置为Filled
    m_spot_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::ReplaceRejected).CheckLeaves("0", "0");

    {
      auto m_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
      m_wallet_checker.CheckCoin(ECoin::USDT);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }
  }
  {
    // 组合撤单
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    EXPECT_EQ(m_order_check.m_msg->exec_digest_map_size(), 0);
    EXPECT_EQ(m_order_check.m_msg->last_liquidity_ind(), ELastLiquidityInd::LiquidityIndNA);
    EXPECT_EQ(m_order_check.m_msg->opponent_order_id().empty(), false);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::CancelRejected);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::Filled);

    auto leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::CancelRejected).CheckLeaves("0", "0");

    {
      auto m_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
      m_wallet_checker.CheckCoin(ECoin::USDT);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
    }
  }
}

TEST_F(ComboFillTest, maker_filled_replacereject_cancelreject_before_passthrough) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 充值
  {
    std::string req_id = "65221ffe-7e6d-4755-a645-03ff7dfd3ec0";
    std::string trans_id = "885cf742-3875-446c-b520-5f4da0a12484";
    int wallet_record_type = 1;
    std::string amount = "200000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
    req_id = "23a23b3a-4cd9-4e59-a0b1-68c6008325e0";
    trans_id = "61767593-7d0d-4966-b3ea-01b384d0eb29";
    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");
    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();

    biz::coin_t btc_coin = 1;
    req_id = "req_depost_btc_1";
    trans_id = "trans_depost_btc_1";
    FutureDepositBuilder deposit_build3(req_id, uid, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp3 = user1.process(deposit_build3.Build());
    auto result3 = te->PopResult();
    req_id = "req_depost_btc_2";
    trans_id = "trans_depost_btc_2";
    FutureDepositBuilder deposit_build4(req_id, uid2, trans_id, btc_coin, wallet_record_type, "100", "0");
    auto resp4 = user2.process(deposit_build4.Build());
    auto result4 = te->PopResult();
  }

  // 调整到全仓模式
  {
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-SPOT";  // 5001
  std::string side = "Sell";
  std::string opp_side = "Buy";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "10";
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;  // BTCUSDT
  biz::symbol_t leg2_symbol = 1;  // BTCUSDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000}});
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  biz::order_link_id_t m_order_link_id{te->GenUUID()};

  {
    // maker 下单
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);

    // Maker New Accepted
    auto m_com_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_com_order_check.m_msg, nullptr);
    auto leg2_order_id = m_com_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = m_result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("10", "99500");
    auto leg1_order_id = m_com_order_check.m_msg->leg1_order_id();
    auto m_future_order_check = m_result.RefFutureMarginResult().RefRelatedOrderByOrderId(leg1_order_id);
    ASSERT_NE(m_future_order_check.m_msg, nullptr);
    m_future_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    m_com_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);
  }

  // 撮合进入单步模式
  te->SetCrossSingleStepMode();

  biz::order_link_id_t t_order_link_id{te->GenUUID()};
  {
    // 用户2 下买单，作为taker，taker全部成交，先不pop result
    side = "Buy";
    qty = "10";
    price = "200";

    CombSiteCreateOrderBuilder t_create_build(symbol, t_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user2.comb_siteapi_create_order(t_create_build.Build(), m_ct);
    EXPECT_EQ(error::kErrorCodeSuccess, m_resp->ret_code());
  }
  // 用户1发起改单
  {
    CombOpenApiV5ReplaceOrderBuilder m_replace_build(symbol, "100", "11");
    m_replace_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_replace_ct;
    auto m_replace_resp = user1.comb_openapi_replace_order(m_replace_build.Build(), m_replace_ct);
    std::ignore = m_replace_ct;
    ASSERT_EQ(m_replace_ct.RetCode(), error::ErrorCode::kErrorCodeSuccess);
  }
  // 用户1发起撤单
  {
    CombOpenApiV5CancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(m_order_link_id);
    RpcContext m_ct;
    auto m_resp_c = user1.comb_openapi_cancel_order(m_cancel_build.Build(), m_ct);
    std::ignore = m_ct;
    std::ignore = m_resp_c;
  }

  {
    // 组合 taker
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 有一个组合订单记录，有一条成交记录
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    EXPECT_EQ(result.m_msg->combination_margin_result().related_combination_fills_size(), 1);
    auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
    ASSERT_NE(t_order_check.m_msg, nullptr);
    EXPECT_EQ(t_order_check.m_msg->user_id(), uid2);
    EXPECT_EQ(t_order_check.m_msg->side(), ESide::Buy);
    EXPECT_EQ(t_order_check.m_msg->qty_x(), 1000000000);
    EXPECT_EQ(t_order_check.m_msg->price_x(), 20000000000);
    EXPECT_EQ(t_order_check.m_msg->exec_qty_x(), 1000000000);
    EXPECT_EQ(t_order_check.m_msg->exec_type(), EExecType::Trade);
    EXPECT_EQ(t_order_check.m_msg->exec_price_x(), 10000000000);
    EXPECT_EQ(t_order_check.m_msg->exec_digest_map_size(), 1);
    EXPECT_EQ(t_order_check.m_msg->last_liquidity_ind(), ELastLiquidityInd::RemovedLiquidity);
    EXPECT_EQ(t_order_check.m_msg->opponent_order_id().empty(), false);
    EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);

    auto leg2_order_id = t_order_check.m_msg->leg2_order_id();
    auto t_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(t_spot_order_check.m_msg, nullptr);
    t_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted).CheckLeaves("10", "99000");

    {
      auto t_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
      t_wallet_checker.CheckCoin(ECoin::BTC);
      t_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("10"));
    }
  }

  {
    // 组合 maker
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetFill);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);
  }
  {
    // 组合改单
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    EXPECT_EQ(m_order_check.m_msg->exec_digest_map_size(), 1);
    EXPECT_EQ(m_order_check.m_msg->last_liquidity_ind(), ELastLiquidityInd::LiquidityIndNA);
    EXPECT_EQ(m_order_check.m_msg->opponent_order_id().empty(), false);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetCanceled);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);

    auto leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::PendingReplace).CheckLeaves("10", "99500");

    {
      auto m_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
      m_wallet_checker.CheckCoin(ECoin::USDT);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("109450"));
    }
  }
  {
    // 组合撤单
    te->ProcessOneXReq(te->GetCombCrossIdx());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    EXPECT_EQ(m_order_check.m_msg->exec_digest_map_size(), 1);
    EXPECT_EQ(m_order_check.m_msg->last_liquidity_ind(), ELastLiquidityInd::LiquidityIndNA);
    EXPECT_EQ(m_order_check.m_msg->opponent_order_id().empty(), false);
    EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::WaitToSetCanceled);
    EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::WaitToRecvPassthrough);

    auto leg2_order_id = m_order_check.m_msg->leg2_order_id();
    auto m_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
    ASSERT_NE(m_spot_order_check.m_msg, nullptr);
    m_spot_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::PendingCancel).CheckLeaves("10", "99500");

    {
      auto m_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
      m_wallet_checker.CheckCoin(ECoin::USDT);
      m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("109450"));
    }
  }
  {
    // 远腿单腿透传包
    te->ProcessOneXReq(te->GetFutureCrossIdxBySymbolId(5));
    {
      // taker的远腿单腿透传包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
    }

    {
      // maker的远腿单腿透传包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
    }
  }
  {
    // 近腿单腿透传包
    te->ProcessOneXReq(te->GetSpotCrossIdx());
    {
      // taker的近腿单腿透传包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      auto t_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(t_order_link_id);
      ASSERT_NE(t_order_check.m_msg, nullptr);
      EXPECT_EQ(t_order_check.m_msg->last_liquidity_ind(), ELastLiquidityInd::RemovedLiquidity);
      EXPECT_EQ(t_order_check.m_msg->opponent_order_id().empty(), false);
      EXPECT_EQ(t_order_check.m_msg->cross_status(), ECombCrossStatus::TakerFill);
      EXPECT_EQ(t_order_check.m_msg->order_status(), ECombOrderStatus::Filled);

      auto leg2_order_id = t_order_check.m_msg->leg2_order_id();
      auto t_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
      ASSERT_NE(t_spot_order_check.m_msg, nullptr);
      t_spot_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill).CheckLeaves("0", "0");

      {
        auto t_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::BTC);
        t_wallet_checker.CheckCoin(ECoin::BTC);
        t_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
      }
    }
    {
      // maker的近腿单腿透传包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);

      auto m_order_check = result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
      ASSERT_NE(m_order_check.m_msg, nullptr);
      EXPECT_EQ(m_order_check.m_msg->last_liquidity_ind(), ELastLiquidityInd::LiquidityIndNA);
      EXPECT_EQ(m_order_check.m_msg->opponent_order_id().empty(), false);
      EXPECT_EQ(m_order_check.m_msg->cross_status(), ECombCrossStatus::Canceled);
      EXPECT_EQ(m_order_check.m_msg->order_status(), ECombOrderStatus::Filled);

      auto leg2_order_id = m_order_check.m_msg->leg2_order_id();
      auto m_spot_order_check = result.RefSpotMarginResult().RefRelatedOrderByOrderId(leg2_order_id);
      ASSERT_NE(m_spot_order_check.m_msg, nullptr);
      // 由于有pendingreplace pendingcancel 不能置为终态
      m_spot_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::Canceled).CheckLeaves("0", "0");

      {
        auto m_wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(ECoin::USDT);
        m_wallet_checker.CheckCoin(ECoin::USDT);
        // 有pendingreplace 这里用reducecost 而不是 直接reset
        m_wallet_checker.CheckWalletAssetFrozne(biz::BigDecimal("0"));
      }
    }
  }
}
std::shared_ptr<tmock::CTradeAppMock> ComboFillTest::te;
