#include "test/biz/trade/combination/order/cancel_all_ao.hpp"

#include <gtest/gtest.h>

#include <bbase/common/decimal/decimal.hpp>
#include <biz_worker/utils/v5_req_converter_utils.hpp>
#include <deque>
#include <string>
#include <unordered_map>
#include <unordered_set>

#include "data/enum.hpp"
#include "data/type/biz_type.hpp"
#include "enums/eaccountmode/account_mode.pb.h"
#include "enums/ebizcode/biz_code.pb.h"
#include "enums/ecreatetype/create_type.pb.h"
#include "enums/ecrossstatus/cross_status.pb.h"
#include "enums/eorderstatus/order_status.pb.h"
#include "enums/etimeinforce/time_in_force.pb.h"
#include "src/data/error/error.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

int32_t CombCancelAllAoTest::SwitchMarginMode(stub& user, enums::eaccountmode::AccountMode account_mode) {
  // stub user(PmTest::te, uid);
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(user.m_uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(enums::eaction::Action::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(account_mode);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(user.m_uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }
  return 0;
}

/*
用户有一个BTC的组合订单，有一个ETH的组合订单
cancel_all = true
symbol = "BTCUSDT"
product_type = ""
contract_type = ""
 */
TEST_F(CombCancelAllAoTest, cancel_all_site_with_future_all) {
  biz::user_id_t uid = *********;
  stub user1(te, uid);

  // 充值
  {
    std::string req_id = " 3fb546a3-be59-484e-8a51-4c946e0fcf93";
    std::string trans_id = "96aaf993-955b-4edd-abbd-108a7eaada68";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    FutureDepositBuilder deposit_build2(req_id, uid, trans_id, 1, 1, "10000", "0");
    auto deposit_resp = user1.process(deposit_build2.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  // 调整到全仓模式
  {
    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-26MAY23";  // 5000
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";  // 1
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;    // BTCUSDT
  biz::symbol_t leg2_symbol = 300;  // BTC-USDT-26MAY23
  biz::symbol_t leg3_symbol = 10;   // SOLUSDT
  biz::symbol_t leg4_symbol = 199;  // SOL-USDT-26MAY23

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg3_symbol), 3000, 3000, 3000},
                           {static_cast<ESymbol>(leg4_symbol), 3000, 3000, 3000}});
  }

  // 下5个单
  auto order_cnt = 5;
  auto ord_list = std::unordered_set<std::string>{};
  // BTCUSDT
  for (int i = 0; i < order_cnt - 2; ++i) {
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    ord_list.insert(m_order_link_id.GetValue());
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    // std::string m_res_str;
    // std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';

    // 校验组合订单
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    biz::size_x_t var_size;
    biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size);
    m_order_check.CheckQty(var_size);
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    auto comb_transact = m_order_check.m_msg;
    for (auto& leg_ord : m_result.m_msg.get()->futures_margin_result().related_orders()) {
      if (leg_ord.order_id() == comb_transact->leg1_order_id()) {
        EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
        EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
      }

      if (leg_ord.order_id() == comb_transact->leg2_order_id()) {
        EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
        EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
      }
    }
  }
  // SOLUSDT
  symbol = "SOL-26MAY23";  // 5200
  for (int i = 0; i < order_cnt - 3; ++i) {
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    ord_list.insert(m_order_link_id.GetValue());
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    // std::string m_res_str;
    // std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';

    // 校验组合订单
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    biz::size_x_t var_size;
    biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size);
    m_order_check.CheckQty(var_size);
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    auto comb_transact = m_order_check.m_msg;
    for (auto& leg_ord : m_result.m_msg.get()->futures_margin_result().related_orders()) {
      if (leg_ord.order_id() == comb_transact->leg1_order_id()) {
        EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
        EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
      }

      if (leg_ord.order_id() == comb_transact->leg2_order_id()) {
        EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
        EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
      }
    }
  }

  // 按照symbol 撤全部单
  {
    CombSiteCancelAllOrderBuilder cancel_all_build{};
    cancel_all_build.SetCancelALl(false);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_cancel_all(cancel_all_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
  }
  {
    CombSiteCancelAllOrderBuilder cancel_all_build{};
    cancel_all_build.SetCancelALl(true);
    cancel_all_build.SetSymbol("BTCUSDT");
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_cancel_all(cancel_all_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    // 校验组合订单
    for (int i = 0; i < order_cnt; ++i) {
      auto m_result = te->PopResult();
      ASSERT_NE(m_result.m_msg.get(), nullptr);
      std::string m_res_str;
      std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';
      EXPECT_EQ(m_result.RefCombMarginResult().m_msg->related_combination_orders_size(), 1);
      auto cancelled_comb_order = m_result.RefCombMarginResult().RefRelatedOrderByIndex(0).m_msg;
      ord_list.erase(cancelled_comb_order->order_link_id());
    }
    EXPECT_EQ(ord_list.size(), 0);
  }
}

/*
 用户有两个BTC的组合订单，其中一个在pending-cancel状态, 接口返回成功，一个订单取消成功
cancel_all = true
symbol = "BTCUDT"
product_type = ""
contract_type = ""
 */
TEST_F(CombCancelAllAoTest, cancel_all_site_with_pendingcancel) {
  biz::user_id_t uid = 5346363;
  stub user1(te, uid);

  // 充值
  {
    std::string req_id = " 3fb546a3-be59-484e-8a51-4c946e0fcf93";
    std::string trans_id = "96aaf993-955b-4edd-abbd-108a7eaada68";
    int wallet_record_type = 1;
    std::string amount = "100000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    FutureDepositBuilder deposit_build2(req_id, uid, trans_id, 1, 1, "10000", "0");
    auto deposit_resp = user1.process(deposit_build2.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  // 调整到全仓模式
  {
    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
  }

  // 现货单腿开启可借贷
  {
    auto lsc = const_cast<config::SpotLoanSymbolDTO*>(config::getTlsCfgMgrRaw()->spot_client()->QueryByLoanSymbolName(
        "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id()));
    lsc->status = 1;
  }

  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";  // 1
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;    // BTCUSDT
  biz::symbol_t leg2_symbol = 300;  // BTC-USDT-26MAY23
  biz::symbol_t leg3_symbol = 10;   // SOLUSDT
  biz::symbol_t leg4_symbol = 199;  // SOL-USDT-26MAY23
  biz::symbol_t leg5_symbol = 1;    // spot BTC/USDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg3_symbol), 3000, 3000, 3000},
                           {static_cast<ESymbol>(leg4_symbol), 3000, 3000, 3000},
                           {static_cast<ESymbol>(leg5_symbol), 10000, 10000, 10000}});
    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  }

  // 下2个单
  auto order_cnt = 2;
  auto ord_list = std::unordered_set<std::string>{};
  biz::order_link_id_t spot_ord_id{te->GenUUID()};
  biz::order_link_id_t future_ord_id{te->GenUUID()};  // pending cancel
  // BTCUSDT
  for (int i = 0; i < order_cnt; ++i) {
    std::string symbol = i == 0 ? "PERP-SPOT" : "PERP-26MAY23";
    biz::order_link_id_t m_order_link_id = (i == 0 ? spot_ord_id : future_ord_id);
    ord_list.insert(m_order_link_id.GetValue());
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    // std::string m_res_str;
    // std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';

    // 校验组合订单
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    biz::size_x_t var_size;
    biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size);
    m_order_check.CheckQty(var_size);
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // 检查期货
    if (m_result.m_msg.get()->futures_margin_result().related_orders_size() > 0) {
      auto comb_transact = m_order_check.m_msg;
      for (auto& leg_ord : m_result.m_msg.get()->futures_margin_result().related_orders()) {
        if (leg_ord.order_id() == comb_transact->leg1_order_id()) {
          EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
          EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
        }

        if (leg_ord.order_id() == comb_transact->leg2_order_id()) {
          EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
          EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
        }
      }
    }

    // 检查现货
    if (m_result.m_msg.get()->spot_margin_result().related_spot_orders_size() > 0) {
      auto spot_order = m_result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
      ASSERT_EQ(spot_order.order_status(), EOrderStatus::New);
      ASSERT_EQ(spot_order.cross_status(), ECrossStatus::NewAccepted);
      ASSERT_EQ(spot_order.create_type(), ECreateType::CreateByFutureSpread);
    }
  }

  // 撤销期货组合单
  // 暂停撮合，制造pendingcancel状态的组合单
  te->SuspendCross();
  // cancel
  {
    CombSiteCancelOrderBuilder m_cancel_build{};
    m_cancel_build.SetOrderLinkId(future_ord_id);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_cancel_order(m_cancel_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
  }

  // 按照symbol 撤全部单
  {
    CombSiteCancelAllOrderBuilder cancel_all_build{};
    cancel_all_build.SetCancelALl(true);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_cancel_all(cancel_all_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    // 校验组合订单, 撤spot
    {
      te->ResumeCross();
      auto m_result = te->PopResult();
      ASSERT_NE(m_result.m_msg.get(), nullptr);
      std::string m_res_str;
      std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';
      EXPECT_EQ(m_result.RefCombMarginResult().m_msg->related_combination_orders_size(), 1);
      auto cancelled_comb_order = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(future_ord_id).m_msg;
      EXPECT_EQ(cancelled_comb_order->order_status(), ECombOrderStatus::Cancelled);
      EXPECT_EQ(cancelled_comb_order->cross_status(), ECombCrossStatus::Canceled);
      // ord_list.erase(cancelled_comb_order->order_link_id());
    }
    // EXPECT_EQ(ord_list.size(), 1);
    // EXPECT_EQ(ord_list.count(future_ord_id.GetValue()), 1);

    // cancel all的resp
    {
      auto m_result = te->PopResult();
      ASSERT_NE(m_result.m_msg.get(), nullptr);
      std::string m_res_str;
      std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';
      EXPECT_EQ(m_result.RefCombMarginResult().m_msg->related_combination_orders_size(), 1);
      auto cancelled_comb_order = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(spot_ord_id).m_msg;
      EXPECT_EQ(cancelled_comb_order->order_status(), ECombOrderStatus::Cancelled);
      EXPECT_EQ(cancelled_comb_order->cross_status(), ECombCrossStatus::Canceled);
      ord_list.erase(cancelled_comb_order->order_link_id());
    }
    EXPECT_EQ(ord_list.size(), 1);
    EXPECT_EQ(ord_list.count(future_ord_id.GetValue()), 1);

    auto m_result = te->PopResult(50);
    ASSERT_EQ(m_result.m_msg.get(), nullptr);
  }
}

/*
用户有一个BTC的期货-现货组合订单，有一个ETH的期货-期货组合订单, 取消BTC的订单，ETH的没有取消掉
cancel_all = true
symbol = "BTCUDT"
product_type = ""
contract_type = "Spot"
 */
TEST_F(CombCancelAllAoTest, cancel_all_site_with_future_spot) {
  biz::user_id_t uid = 25909875;
  stub user1(te, uid);

  // 充值
  {
    std::string req_id = " 3fb546a3-be59-484e-8a51-4c946e0fcf93";
    std::string trans_id = "96aaf993-955b-4edd-abbd-108a7eaada68";
    int wallet_record_type = 1;
    std::string amount = "100000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    FutureDepositBuilder deposit_build2(req_id, uid, trans_id, 1, 1, "10000", "0");
    auto deposit_resp = user1.process(deposit_build2.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  // 调整到全仓模式
  {
    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
  }

  // 现货单腿开启可借贷
  {
    auto lsc = const_cast<config::SpotLoanSymbolDTO*>(config::getTlsCfgMgrRaw()->spot_client()->QueryByLoanSymbolName(
        "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id()));
    lsc->status = 1;
  }

  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";  // 1
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;    // BTCUSDT
  biz::symbol_t leg2_symbol = 300;  // BTC-USDT-26MAY23
  biz::symbol_t leg3_symbol = 10;   // SOLUSDT
  biz::symbol_t leg4_symbol = 199;  // SOL-USDT-26MAY23
  biz::symbol_t leg5_symbol = 1;    // spot BTC/USDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg3_symbol), 3000, 3000, 3000},
                           {static_cast<ESymbol>(leg4_symbol), 3000, 3000, 3000},
                           {static_cast<ESymbol>(leg5_symbol), 10000, 10000, 10000}});
    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  }

  // 下5个单
  auto order_cnt = 2;
  auto ord_list = std::unordered_set<std::string>{};
  biz::order_link_id_t spot_ord_id{te->GenUUID()};
  biz::order_link_id_t future_ord_id{te->GenUUID()};
  // BTCUSDT
  for (int i = 0; i < order_cnt; ++i) {
    std::string symbol = i == 0 ? "PERP-SPOT" : "SOL-26MAY23";
    biz::order_link_id_t m_order_link_id = (i == 0 ? spot_ord_id : future_ord_id);
    ord_list.insert(m_order_link_id.GetValue());
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    // std::string m_res_str;
    // std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';

    // 校验组合订单
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    biz::size_x_t var_size;
    biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size);
    m_order_check.CheckQty(var_size);
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // 检查期货
    if (m_result.m_msg.get()->futures_margin_result().related_orders_size() > 0) {
      auto comb_transact = m_order_check.m_msg;
      for (auto& leg_ord : m_result.m_msg.get()->futures_margin_result().related_orders()) {
        if (leg_ord.order_id() == comb_transact->leg1_order_id()) {
          EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
          EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
        }

        if (leg_ord.order_id() == comb_transact->leg2_order_id()) {
          EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
          EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
        }
      }
    }

    // 检查现货
    if (m_result.m_msg.get()->spot_margin_result().related_spot_orders_size() > 0) {
      auto spot_order = m_result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
      ASSERT_EQ(spot_order.order_status(), EOrderStatus::New);
      ASSERT_EQ(spot_order.cross_status(), ECrossStatus::NewAccepted);
      ASSERT_EQ(spot_order.create_type(), ECreateType::CreateByFutureSpread);
    }
  }

  // 按照symbol 撤全部单
  {
    CombSiteCancelAllOrderBuilder cancel_all_build{};
    cancel_all_build.SetCancelALl(true);
    cancel_all_build.SetSymbol("BTCUSDT");
    cancel_all_build.SetContractType("Spot");
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_cancel_all(cancel_all_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    // 校验组合订单
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    std::string m_res_str;
    std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';
    EXPECT_EQ(m_result.RefCombMarginResult().m_msg->related_combination_orders_size(), 1);
    auto cancelled_comb_order = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(spot_ord_id);
    ASSERT_NE(cancelled_comb_order.m_msg, nullptr);
    cancelled_comb_order.CheckStatus(ECombOrderStatus::Cancelled, ECombCrossStatus::Canceled);
    ord_list.erase(cancelled_comb_order.m_msg->order_link_id());

    EXPECT_EQ(ord_list.size(), 1);
    EXPECT_EQ(ord_list.count(future_ord_id.GetValue()), 1);
  }
}

/*
用户有一个BTC的期货-现货组合订单，有一个ETH的期货-期货组合订单, 两个订单取消成功
cancel_all = true
symbol = "BTCUSDT"
product_type = ""
contract_type = "UsdtPerpetual,Spot"
* */
TEST_F(CombCancelAllAoTest, cancel_all_site_with_future_spot_ctus) {
  biz::user_id_t uid = 8765432357;
  stub user1(te, uid);

  // 充值
  {
    std::string req_id = " 3fb546a3-be59-484e-8a51-4c946e0fcf93";
    std::string trans_id = "96aaf993-955b-4edd-abbd-108a7eaada68";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    FutureDepositBuilder deposit_build2(req_id, uid, trans_id, 1, 1, "10000", "0");
    auto deposit_resp = user1.process(deposit_build2.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  // 调整到全仓模式
  {
    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
  }

  // 现货单腿开启可借贷
  {
    auto lsc = const_cast<config::SpotLoanSymbolDTO*>(config::getTlsCfgMgrRaw()->spot_client()->QueryByLoanSymbolName(
        "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id()));
    lsc->status = 1;
  }

  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";  // 1
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;    // BTCUSDT
  biz::symbol_t leg2_symbol = 300;  // BTC-USDT-26MAY23
  biz::symbol_t leg3_symbol = 10;   // SOLUSDT
  biz::symbol_t leg4_symbol = 199;  // SOL-USDT-26MAY23
  biz::symbol_t leg5_symbol = 1;    // spot BTC/USDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg3_symbol), 3000, 3000, 3000},
                           {static_cast<ESymbol>(leg4_symbol), 3000, 3000, 3000},
                           {static_cast<ESymbol>(leg5_symbol), 10000, 10000, 10000}});
  }

  // 下2个单
  auto order_cnt = 2;
  auto ord_list = std::unordered_set<std::string>{};
  // BTCUSDT
  for (int i = 0; i < order_cnt; ++i) {
    std::string symbol = i == 0 ? "PERP-SPOT" : "SOL-26MAY23";
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    ord_list.insert(m_order_link_id.GetValue());
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    // std::string m_res_str;
    // std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';

    // 校验组合订单
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    biz::size_x_t var_size;
    biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size);
    m_order_check.CheckQty(var_size);
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // 检查期货
    if (m_result.m_msg.get()->futures_margin_result().related_orders_size() > 0) {
      auto comb_transact = m_order_check.m_msg;
      for (auto& leg_ord : m_result.m_msg.get()->futures_margin_result().related_orders()) {
        if (leg_ord.order_id() == comb_transact->leg1_order_id()) {
          EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
          EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
        }

        if (leg_ord.order_id() == comb_transact->leg2_order_id()) {
          EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
          EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
        }
      }
    }

    // 检查现货
    if (m_result.m_msg.get()->spot_margin_result().related_spot_orders_size() > 0) {
      auto spot_order = m_result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
      ASSERT_EQ(spot_order.order_status(), EOrderStatus::New);
      ASSERT_EQ(spot_order.cross_status(), ECrossStatus::NewAccepted);
      ASSERT_EQ(spot_order.create_type(), ECreateType::CreateByFutureSpread);
    }
  }

  // 按照symbol 撤全部单
  {
    CombSiteCancelAllOrderBuilder cancel_all_build{};
    cancel_all_build.SetCancelALl(true);
    cancel_all_build.SetSymbol("BTCUSDT");
    cancel_all_build.SetContractType("UsdtPerpetual,Spot");
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_cancel_all(cancel_all_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    // 校验组合订单
    for (int i = 0; i < order_cnt; ++i) {
      auto m_result = te->PopResult();
      ASSERT_NE(m_result.m_msg.get(), nullptr);
      std::string m_res_str;
      std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';
      EXPECT_EQ(m_result.RefCombMarginResult().m_msg->related_combination_orders_size(), 1);
      auto cancelled_comb_order = m_result.RefCombMarginResult().RefRelatedOrderByIndex(0).m_msg;
      ord_list.erase(cancelled_comb_order->order_link_id());
    }
    EXPECT_EQ(ord_list.size(), 0);
  }
}

/*
用户有一个BTC的期货-期货组合订单，有一个BTC的期货-现货组合订单，两个订单取消成功
cancel_all = false
symbol = "BTCUDT"
product_type = "Futures"
contract_type = ""
* */
TEST_F(CombCancelAllAoTest, cancel_all_site_with_future_sopt_ptf) {
  biz::user_id_t uid = 87654129457;
  stub user1(te, uid);

  // 充值
  {
    std::string req_id = " 3fb546a3-be59-484e-8a51-4c946e0fcf93";
    std::string trans_id = "96aaf993-955b-4edd-abbd-108a7eaada68";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    FutureDepositBuilder deposit_build2(req_id, uid, trans_id, 1, 1, "10000", "0");
    auto deposit_resp = user1.process(deposit_build2.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  // 调整到全仓模式
  {
    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
  }

  // 现货单腿开启可借贷
  {
    auto lsc = const_cast<config::SpotLoanSymbolDTO*>(config::getTlsCfgMgrRaw()->spot_client()->QueryByLoanSymbolName(
        "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id()));
    lsc->status = 1;
  }

  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";  // 1
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;    // BTCUSDT
  biz::symbol_t leg2_symbol = 300;  // BTC-USDT-26MAY23
  biz::symbol_t leg3_symbol = 10;   // SOLUSDT
  biz::symbol_t leg4_symbol = 199;  // SOL-USDT-26MAY23
  biz::symbol_t leg5_symbol = 1;    // spot BTC/USDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg3_symbol), 3000, 3000, 3000},
                           {static_cast<ESymbol>(leg4_symbol), 3000, 3000, 3000},
                           {static_cast<ESymbol>(leg5_symbol), 10000, 10000, 10000}});
  }

  // 下5个单
  auto order_cnt = 2;
  auto ord_list = std::unordered_set<std::string>{};
  // BTCUSDT
  for (int i = 0; i < order_cnt; ++i) {
    std::string symbol = i == 0 ? "PERP-SPOT" : "PERP-26MAY23";
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    ord_list.insert(m_order_link_id.GetValue());
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    // std::string m_res_str;
    // std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';

    // 校验组合订单
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    biz::size_x_t var_size;
    biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size);
    m_order_check.CheckQty(var_size);
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // 检查期货
    if (m_result.m_msg.get()->futures_margin_result().related_orders_size() > 0) {
      auto comb_transact = m_order_check.m_msg;
      for (auto& leg_ord : m_result.m_msg.get()->futures_margin_result().related_orders()) {
        if (leg_ord.order_id() == comb_transact->leg1_order_id()) {
          EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
          EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
        }

        if (leg_ord.order_id() == comb_transact->leg2_order_id()) {
          EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
          EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
        }
      }
    }

    // 检查现货
    if (m_result.m_msg.get()->spot_margin_result().related_spot_orders_size() > 0) {
      auto spot_order = m_result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
      ASSERT_EQ(spot_order.order_status(), EOrderStatus::New);
      ASSERT_EQ(spot_order.cross_status(), ECrossStatus::NewAccepted);
      ASSERT_EQ(spot_order.create_type(), ECreateType::CreateByFutureSpread);
    }
  }

  // 按照symbol 撤全部单
  {
    CombSiteCancelAllOrderBuilder cancel_all_build{};
    cancel_all_build.SetCancelALl(false);
    cancel_all_build.SetSymbol("BTCUSDT");
    cancel_all_build.SetProductType("Futures");
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_cancel_all(cancel_all_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    // 校验组合订单
    for (int i = 0; i < order_cnt; ++i) {
      auto m_result = te->PopResult();
      ASSERT_NE(m_result.m_msg.get(), nullptr);
      std::string m_res_str;
      std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';
      EXPECT_EQ(m_result.RefCombMarginResult().m_msg->related_combination_orders_size(), 1);
      auto cancelled_comb_order = m_result.RefCombMarginResult().RefRelatedOrderByIndex(0).m_msg;
      ord_list.erase(cancelled_comb_order->order_link_id());
    }
    EXPECT_EQ(ord_list.size(), 0);
  }
}

/*
用户有一个BTC的期货-期货组合订单，有一个BTC的期货-现货组合订单,期货-现货组合订单取消成功，期货-期货组合订单没有取消
cancel_all = false
symbol = "BTC/USDT"
product_type = "Spot"
contract_type = ""
* */
TEST_F(CombCancelAllAoTest, cancel_all_site_with_future_sopt_invalid1) {
  biz::user_id_t uid = 876543247;
  stub user1(te, uid);

  // 充值
  {
    std::string req_id = " 3fb546a3-be59-484e-8a51-4c946e0fcf93";
    std::string trans_id = "96aaf993-955b-4edd-abbd-108a7eaada68";
    int wallet_record_type = 1;
    std::string amount = "100000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    FutureDepositBuilder deposit_build2(req_id, uid, trans_id, 1, 1, "10000", "0");
    auto deposit_resp = user1.process(deposit_build2.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  // 调整到全仓模式
  {
    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
  }

  // 现货单腿开启可借贷
  {
    auto lsc = const_cast<config::SpotLoanSymbolDTO*>(config::getTlsCfgMgrRaw()->spot_client()->QueryByLoanSymbolName(
        "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id()));
    lsc->status = 1;
  }

  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";  // 1
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;    // BTCUSDT
  biz::symbol_t leg2_symbol = 300;  // BTC-USDT-26MAY23
  biz::symbol_t leg3_symbol = 10;   // SOLUSDT
  biz::symbol_t leg4_symbol = 199;  // SOL-USDT-26MAY23
  biz::symbol_t leg5_symbol = 1;    // spot BTC/USDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg3_symbol), 3000, 3000, 3000},
                           {static_cast<ESymbol>(leg4_symbol), 3000, 3000, 3000},
                           {static_cast<ESymbol>(leg5_symbol), 10000, 10000, 10000}});
    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("10000"));
  }

  // 下5个单
  auto order_cnt = 2;
  auto ord_list = std::unordered_set<std::string>{};
  biz::order_link_id_t spot_ord_id{te->GenUUID()};
  biz::order_link_id_t future_ord_id{te->GenUUID()};
  // BTCUSDT
  for (int i = 0; i < order_cnt; ++i) {
    std::string symbol = i == 0 ? "PERP-SPOT" : "PERP-26MAY23";
    biz::order_link_id_t m_order_link_id = (i == 0 ? spot_ord_id : future_ord_id);
    ord_list.insert(m_order_link_id.GetValue());
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    // std::string m_res_str;
    // std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';

    // 校验组合订单
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    biz::size_x_t var_size;
    biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size);
    m_order_check.CheckQty(var_size);
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // 检查期货
    if (m_result.m_msg.get()->futures_margin_result().related_orders_size() > 0) {
      auto comb_transact = m_order_check.m_msg;
      for (auto& leg_ord : m_result.m_msg.get()->futures_margin_result().related_orders()) {
        if (leg_ord.order_id() == comb_transact->leg1_order_id()) {
          EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
          EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
        }

        if (leg_ord.order_id() == comb_transact->leg2_order_id()) {
          EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
          EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
        }
      }
    }

    // 检查现货
    if (m_result.m_msg.get()->spot_margin_result().related_spot_orders_size() > 0) {
      auto spot_order = m_result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
      ASSERT_EQ(spot_order.order_status(), EOrderStatus::New);
      ASSERT_EQ(spot_order.cross_status(), ECrossStatus::NewAccepted);
      ASSERT_EQ(spot_order.create_type(), ECreateType::CreateByFutureSpread);
    }
  }

  // 按照symbol 撤全部单
  {
    CombSiteCancelAllOrderBuilder cancel_all_build{};
    cancel_all_build.SetCancelALl(false);
    cancel_all_build.SetSymbol("BTCUSDT");
    cancel_all_build.SetProductType("Spot");
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_cancel_all(cancel_all_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    // 校验组合订单, 撤spot
    {
      auto m_result = te->PopResult();
      ASSERT_NE(m_result.m_msg.get(), nullptr);
      std::string m_res_str;
      std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';
      EXPECT_EQ(m_result.RefCombMarginResult().m_msg->related_combination_orders_size(), 1);
      auto cancelled_comb_order = m_result.RefCombMarginResult().RefRelatedOrderByIndex(0).m_msg;
      ord_list.erase(cancelled_comb_order->order_link_id());
    }
    EXPECT_EQ(ord_list.size(), 1);
    EXPECT_EQ(ord_list.count(future_ord_id.GetValue()), 1);
  }
}

/*
用户有一个BTC的期货-期货组合订单，有一个BTC的期货-现货组合订单,
报错或者成功但是没有取消的订单(原因是symbol和product_type不匹配) cancel_all = false symbol = "BTC-USDT" product_type =
"Spot" contract_type = ""
* */
TEST_F(CombCancelAllAoTest, cancel_all_site_with_future_sopt_invalid2) {
  biz::user_id_t uid = 75843284;
  stub user1(te, uid);

  // 充值
  {
    std::string req_id = " 3fb546a3-be59-484e-8a51-4c946e0fcf93";
    std::string trans_id = "96aaf993-955b-4edd-abbd-108a7eaada68";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    FutureDepositBuilder deposit_build2(req_id, uid, trans_id, 1, 1, "10000", "0");
    auto deposit_resp = user1.process(deposit_build2.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  // 调整到全仓模式
  {
    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
  }

  // 现货单腿开启可借贷
  {
    auto lsc = const_cast<config::SpotLoanSymbolDTO*>(config::getTlsCfgMgrRaw()->spot_client()->QueryByLoanSymbolName(
        "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id()));
    lsc->status = 1;
  }

  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";  // 1
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;    // BTCUSDT
  biz::symbol_t leg2_symbol = 300;  // BTC-USDT-26MAY23
  biz::symbol_t leg3_symbol = 10;   // SOLUSDT
  biz::symbol_t leg4_symbol = 199;  // SOL-USDT-26MAY23
  biz::symbol_t leg5_symbol = 1;    // spot BTC/USDT

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg3_symbol), 3000, 3000, 3000},
                           {static_cast<ESymbol>(leg4_symbol), 3000, 3000, 3000},
                           {static_cast<ESymbol>(leg5_symbol), 10000, 10000, 10000}});
  }

  // 下5个单
  auto order_cnt = 2;
  auto ord_list = std::unordered_set<std::string>{};
  // BTCUSDT
  for (int i = 0; i < order_cnt; ++i) {
    std::string symbol = i == 0 ? "PERP-SPOT" : "PERP-26MAY23";
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    ord_list.insert(m_order_link_id.GetValue());
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    // std::string m_res_str;
    // std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';

    // 校验组合订单
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    biz::size_x_t var_size;
    biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size);
    m_order_check.CheckQty(var_size);
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    // 检查期货
    if (m_result.m_msg.get()->futures_margin_result().related_orders_size() > 0) {
      auto comb_transact = m_order_check.m_msg;
      for (auto& leg_ord : m_result.m_msg.get()->futures_margin_result().related_orders()) {
        if (leg_ord.order_id() == comb_transact->leg1_order_id()) {
          EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
          EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
        }

        if (leg_ord.order_id() == comb_transact->leg2_order_id()) {
          EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
          EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
        }
      }
    }

    // 检查现货
    if (m_result.m_msg.get()->spot_margin_result().related_spot_orders_size() > 0) {
      auto spot_order = m_result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
      ASSERT_EQ(spot_order.order_status(), EOrderStatus::New);
      ASSERT_EQ(spot_order.cross_status(), ECrossStatus::NewAccepted);
      ASSERT_EQ(spot_order.create_type(), ECreateType::CreateByFutureSpread);
    }
  }

  // 按照symbol 撤全部单
  {
    CombSiteCancelAllOrderBuilder cancel_all_build{};
    cancel_all_build.SetCancelALl(false);
    cancel_all_build.SetSymbol("BTC-USDT");
    cancel_all_build.SetProductType("Spot");
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_cancel_all(cancel_all_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    auto m_result = te->PopResult(50);
    ASSERT_EQ(m_result.m_msg.get(), nullptr);
  }
}

TEST_F(CombCancelAllAoTest, cancel_all_openapi_with_future_all) {
  biz::user_id_t uid = 62784266;
  stub user1(te, uid);

  // 充值
  {
    std::string req_id = " 3fb546a3-be59-484e-8a51-4c946e0fcf93";
    std::string trans_id = "96aaf993-955b-4edd-abbd-108a7eaada68";
    int wallet_record_type = 1;
    std::string amount = "50000";
    biz::coin_t coin = 5;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");
    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    FutureDepositBuilder deposit_build2(req_id, uid, trans_id, 1, 1, "10000", "0");
    auto deposit_resp = user1.process(deposit_build2.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  // 调整到全仓模式
  {
    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));
  }

  std::string symbol = "PERP-26MAY23";  // 5000
  std::string side = "Buy";
  std::string opp_side = "Sell";
  std::string order_type = "Limit";
  std::string time_in_force = "GTC";
  std::string qty = "1";  // 1
  std::string price = "100";
  biz::symbol_t leg1_symbol = 5;    // BTCUSDT
  biz::symbol_t leg2_symbol = 300;  // BTC-USDT-26MAY23
  biz::symbol_t leg3_symbol = 10;   // SOLUSDT
  biz::symbol_t leg4_symbol = 199;  // SOL-USDT-26MAY23

  // 同步行情
  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(leg1_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg2_symbol), 10000, 10000, 10000},
                           {static_cast<ESymbol>(leg3_symbol), 3000, 3000, 3000},
                           {static_cast<ESymbol>(leg4_symbol), 3000, 3000, 3000}});
  }

  // 下5个单
  auto order_cnt = 5;
  auto ord_list = std::unordered_set<std::string>{};
  // BTCUSDT
  for (int i = 0; i < order_cnt - 2; ++i) {
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    ord_list.insert(m_order_link_id.GetValue());
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    // std::string m_res_str;
    // std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';

    // 校验组合订单
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    biz::size_x_t var_size;
    biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size);
    m_order_check.CheckQty(var_size);
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    auto comb_transact = m_order_check.m_msg;
    for (auto& leg_ord : m_result.m_msg.get()->futures_margin_result().related_orders()) {
      if (leg_ord.order_id() == comb_transact->leg1_order_id()) {
        EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
        EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
      }

      if (leg_ord.order_id() == comb_transact->leg2_order_id()) {
        EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
        EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
      }
    }
  }
  // SOLUSDT
  symbol = "SOL-26MAY23";  // 5200
  for (int i = 0; i < order_cnt - 3; ++i) {
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    ord_list.insert(m_order_link_id.GetValue());
    CombSiteCreateOrderBuilder m_create_build(symbol, m_order_link_id, side, order_type, time_in_force, price, qty);
    RpcContext m_ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), m_ct);
    std::ignore = m_ct;
    ASSERT_EQ(m_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto m_result = te->PopResult();
    ASSERT_NE(m_result.m_msg.get(), nullptr);
    // std::string m_res_str;
    // std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';

    // 校验组合订单
    auto m_order_check = m_result.RefCombMarginResult().RefRelatedOrderByOrderLinkId(m_order_link_id);
    ASSERT_NE(m_order_check.m_msg, nullptr);
    m_order_check.CheckUid(uid);
    m_order_check.CheckSide(biz::V5ReqConverterUtil::ConvertSide(side));
    biz::size_x_t var_size;
    biz::V5ReqConverterUtil::ConvertQty(qty, 8, var_size);
    m_order_check.CheckQty(var_size);
    biz::price_x_t var_price;
    biz::V5ReqConverterUtil::ConvertPrice(price, 8, var_price);
    m_order_check.CheckPrice(var_price);
    m_order_check.CheckStatus(ECombOrderStatus::New, ECombCrossStatus::NewAccepted);

    auto comb_transact = m_order_check.m_msg;
    for (auto& leg_ord : m_result.m_msg.get()->futures_margin_result().related_orders()) {
      if (leg_ord.order_id() == comb_transact->leg1_order_id()) {
        EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
        EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
      }

      if (leg_ord.order_id() == comb_transact->leg2_order_id()) {
        EXPECT_EQ(leg_ord.order_status(), EOrderStatus::New);
        EXPECT_EQ(leg_ord.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_EQ(leg_ord.create_type(), ECreateType::CreateByFutureSpread);
      }
    }
  }

  // 按照user 撤全部单
  {
    CombOpenApiV5CancelAllOrderBuilder cancel_all_build{};
    cancel_all_build.SetCancelAll(true);
    RpcContext m_ct;
    auto m_resp = user1.comb_openapi_cancel_all(cancel_all_build.Build(), m_ct);
    ASSERT_EQ(m_ct.RetCode(), error::ErrorCode::kErrorCodeSuccess);

    // 校验组合订单
    for (int i = 0; i < order_cnt; ++i) {
      auto m_result = te->PopResult();
      ASSERT_NE(m_result.m_msg.get(), nullptr);
      std::string m_res_str;
      std::cout << "m_res_str:" << utils::PbMessageToJsonString(*m_result.m_msg.get(), &m_res_str) << '\n';
      EXPECT_EQ(m_result.RefCombMarginResult().m_msg->related_combination_orders_size(), 1);
      auto cancelled_comb_order = m_result.RefCombMarginResult().RefRelatedOrderByIndex(0).m_msg;
      ord_list.erase(cancelled_comb_order->order_link_id());
    }
    EXPECT_EQ(ord_list.size(), 0);
  }
}

std::shared_ptr<tmock::CTradeAppMock> CombCancelAllAoTest::te;
