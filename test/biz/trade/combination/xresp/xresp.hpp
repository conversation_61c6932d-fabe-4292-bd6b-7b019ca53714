#pragma once

#include <gtest/gtest.h>

#include <iostream>
#include <memory>
#include <string>

#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class CombXespTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }

  static void UtilCombCancelOrd(stub& user, biz::order_link_id_t ord_id);
  static int32_t SwitchMarginMode(stub& user, enums::eaccountmode::AccountMode account_mode);
  static int32_t Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin = 5);
  static std::shared_ptr<tmock::CTradeAppMock> te;
  static int InitCombinationTransactDTO(models::tradingdto::CombinationTransactDTO* dst);
};
