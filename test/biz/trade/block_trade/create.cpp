#include "create.hpp"  // NOLINT

#include <string>

#include "biz_worker/service/base/draft_pkg.hpp"
#include "biz_worker/service/trade/store/resource.hpp"
#include "biz_worker/service/trade/store/user_data_manager.hpp"
#include "enums/eexectype/exec_type.pb.h"
#include "lib/msg_builder.hpp"
#include "src/biz_worker/service/trade/block_trade/event_handler/order_biz.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(BCreateTest, create_just_spot_with_pre_occupy) {
  biz::user_id_t uid = 100000;
  biz::user_id_t uid2 = 10001;
  stub user1(te, uid);
  stub user2(te, uid2);

  FutureDepositBuilder deposit_build(te->GenUUID(), uid, te->GenUUID(), 5, 1, "10000", "0");
  user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2(te->GenUUID(), uid, te->GenUUID(), 1, 1, "100", "0");
  user2.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  BlockTradePreOccupyBuilder occupy(uid, "1234567", false, false);
  occupy.AddPreOrder("spot", "BTCUSDT", "20000", "0.1", ESide::Buy, "12356");
  auto resp1 = user1.block_trade_pre_occupy(occupy.Build());
  ASSERT_EQ(resp1.get()->ret_code(), 0);
  ASSERT_EQ(resp1.get()->pre_order_resp_items().size(), 1);
  ASSERT_NE(resp1.get()->pre_order_resp_items()[0].order_id(), "");
  ASSERT_EQ(resp1.get()->pre_order_resp_items()[0].order_link_id(), "12356");
  ASSERT_EQ(resp1.get()->pre_order_resp_items()[0].fee_rate_e8(), 100000);

  auto result = te->PopResult();
  ASSERT_EQ(result.m_msg.get(), nullptr);

  BlockTradeCreateBuilder create(uid, uid2, "1234567");
  create.AddOrder(
      "spot", "BTCUSDT", "20000", "0.01", ESide::Buy, resp1.get()->pre_order_resp_items()[0].order_id(),
      resp1.get()->pre_order_resp_items()[0].order_link_id(), resp1.get()->pre_order_resp_items()[0].fee_rate_e8(), "",
      0, 0, std::to_string(application::GlobalVarManager::Instance().snow_flake_gen().NextId()),
      std::to_string(application::GlobalVarManager::Instance().snow_flake_gen().NextId()), 100000, "", 0, 0);
  user1.block_trade_create(create.Build());
  ASSERT_NE(te->PopResult().m_msg.get(), nullptr);
  ASSERT_NE(te->PopResult().m_msg.get(), nullptr);
}

TEST_F(BCreateTest, create_just_spot_with_pre_occupy_move_position) {
  biz::user_id_t uid = 100000;
  biz::user_id_t uid2 = 10001;
  stub user1(te, uid);
  stub user2(te, uid2);

  FutureDepositBuilder deposit_build(te->GenUUID(), uid, te->GenUUID(), 5, 1, "10000", "0");
  user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2(te->GenUUID(), uid, te->GenUUID(), 1, 1, "100", "0");
  user2.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  BlockTradePreOccupyBuilder occupy(uid, "1234567", false, false, true, true);
  occupy.AddPreOrder("spot", "BTCUSDT", "20000", "0.1", ESide::Buy, "12356");
  auto resp1 = user1.block_trade_pre_occupy(occupy.Build());
  ASSERT_EQ(resp1.get()->ret_code(), 0);
  ASSERT_EQ(resp1.get()->pre_order_resp_items().size(), 1);
  ASSERT_NE(resp1.get()->pre_order_resp_items()[0].order_id(), "");
  ASSERT_EQ(resp1.get()->pre_order_resp_items()[0].order_link_id(), "12356");
  ASSERT_EQ(resp1.get()->pre_order_resp_items()[0].fee_rate_e8(), 0);

  auto result = te->PopResult();
  ASSERT_EQ(result.m_msg.get(), nullptr);

  BlockTradeCreateBuilder create(uid, uid2, "1234567");
  create.AddOrder("spot", "BTCUSDT", "20000", "0.01", ESide::Buy, resp1.get()->pre_order_resp_items()[0].order_id(),
                  resp1.get()->pre_order_resp_items()[0].order_link_id(),
                  resp1.get()->pre_order_resp_items()[0].fee_rate_e8(), "", 0, 0,
                  std::to_string(application::GlobalVarManager::Instance().snow_flake_gen().NextId()),
                  std::to_string(application::GlobalVarManager::Instance().snow_flake_gen().NextId()), 0, "", 0, 0);
  user1.block_trade_create(create.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].fee_rate_e8(), 0);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].create_type(),
            ECreateType::CreateByBlockTradeMovePosition_PassThrough);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_fills()[0].fee_rate_e8(), 0);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_fills()[0].exec_type(),
            EExecType::BlockTradeMovePosition);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].fee_rate_e8(), 0);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].create_type(),
            ECreateType::CreateByBlockTradeMovePosition_PassThrough);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_fills()[0].fee_rate_e8(), 0);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_fills()[0].exec_type(),
            EExecType::BlockTradeMovePosition);
}

TEST_F(BCreateTest, create_just_spot_without_pre_occupy) {
  biz::user_id_t uid = 100000;
  biz::user_id_t uid2 = 10001;
  stub user1(te, uid);
  stub user2(te, uid2);

  FutureDepositBuilder deposit_build(te->GenUUID(), uid, te->GenUUID(), 5, 1, "10000", "0");
  user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2(te->GenUUID(), uid, te->GenUUID(), 1, 1, "100", "0");
  user2.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  BlockTradeCreateBuilder create(uid, uid2, "1234567");
  create.AddOrder("spot", "BTCUSDT", "20000", "0.01", ESide::Buy,
                  std::to_string(application::GlobalVarManager::Instance().snow_flake_gen().NextId()),
                  std::to_string(application::GlobalVarManager::Instance().snow_flake_gen().NextId()), 100000, "", 0, 0,
                  std::to_string(application::GlobalVarManager::Instance().snow_flake_gen().NextId()),
                  std::to_string(application::GlobalVarManager::Instance().snow_flake_gen().NextId()), 100000, "", 0,
                  0);
  user1.block_trade_create(create.Build());
  ASSERT_NE(te->PopResult().m_msg.get(), nullptr);
  ASSERT_NE(te->PopResult().m_msg.get(), nullptr);
}

TEST_F(BCreateTest, create_just_future_with_pre_occupy) {
  biz::user_id_t uid = 100000;
  biz::user_id_t uid2 = 10001;
  stub user1(te, uid);
  stub user2(te, uid2);

  FutureDepositBuilder deposit_build(te->GenUUID(), uid, te->GenUUID(), 5, 1, "10000", "0");
  user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  user2.process(deposit_build.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  BlockTradePreOccupyBuilder occupy(uid, "1234567", false, false);
  occupy.AddPreOrder("linear", "BTCUSDT", "20000", "0.1", ESide::Buy, "12356");
  auto resp1 = user1.block_trade_pre_occupy(occupy.Build());
  ASSERT_EQ(resp1.get()->ret_code(), 0);
  ASSERT_EQ(resp1.get()->pre_order_resp_items().size(), 1);
  ASSERT_NE(resp1.get()->pre_order_resp_items()[0].order_id(), "");
  ASSERT_EQ(resp1.get()->pre_order_resp_items()[0].order_link_id(), "12356");

  auto result = te->PopResult();
  ASSERT_EQ(result.m_msg.get(), nullptr);

  BlockTradeCreateBuilder create(uid, uid2, "1234567");
  create.AddOrder("linear", "BTCUSDT", "20000", "0.01", ESide::Buy, resp1.get()->pre_order_resp_items()[0].order_id(),
                  resp1.get()->pre_order_resp_items()[0].order_link_id(), 0, "", 0, 0, te->GenUUID(), te->GenUUID(), 0,
                  "", 0, 0);
  user1.block_trade_create(create.Build());
  ASSERT_NE(te->PopResult().m_msg.get(), nullptr);
  ASSERT_NE(te->PopResult().m_msg.get(), nullptr);
}

TEST_F(BCreateTest, create_just_future_without_pre_occupy) {
  biz::user_id_t uid = 100000;
  biz::user_id_t uid2 = 10001;
  stub user1(te, uid);
  stub user2(te, uid2);

  FutureDepositBuilder deposit_build(te->GenUUID(), uid, te->GenUUID(), 5, 1, "10000", "0");
  user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  user2.process(deposit_build.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  BlockTradeCreateBuilder create(uid, uid2, "1234567");
  create.AddOrder("linear", "BTCUSDT", "20000", "0.01", ESide::Buy, te->GenUUID(), te->GenUUID(), 0, "", 0, 0,
                  te->GenUUID(), te->GenUUID(), 0, "", 0, 0);
  user1.block_trade_create(create.Build());
  ASSERT_NE(te->PopResult().m_msg.get(), nullptr);
  ASSERT_NE(te->PopResult().m_msg.get(), nullptr);
}

TEST_F(BCreateTest, create_just_option) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("create_just_option", uid, "create_just_option", ECoin::USDC, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  biz::user_id_t uid2 = 10004;
  stub user2(te, uid2);
  FutureDepositBuilder deposit_build2("create_just_option", uid2, "create_just_option", ECoin::USDC, 1, "10000", "0");
  auto deposit_resp2 = user2.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  BlockTradePreOccupyBuilder occupy(uid, "1234567", false, false);
  occupy.AddPreOrder("option", "BTC-31DEC24-40000-C", "20000", "0.1", ESide::Buy, "12356");
  auto resp1 = user1.block_trade_pre_occupy(occupy.Build());
  ASSERT_EQ(resp1.get()->ret_code(), 0);
  ASSERT_EQ(resp1.get()->pre_order_resp_items().size(), 1);
  ASSERT_NE(resp1.get()->pre_order_resp_items()[0].order_id(), "");
  ASSERT_EQ(resp1.get()->pre_order_resp_items()[0].order_link_id(), "12356");

  auto result = te->PopResult();
  ASSERT_EQ(result.m_msg.get(), nullptr);

  BlockTradeCreateBuilder create(uid2, uid, "1234567");
  create.AddOrder("option", "BTC-31DEC24-40000-C", "20000", "0.01", ESide::Sell,
                  resp1.get()->pre_order_resp_items()[0].order_id(),
                  resp1.get()->pre_order_resp_items()[0].order_link_id(), 300000, "300001", 8000000, 0, te->GenUUID(),
                  te->GenUUID(), 250000, "25001", 700000, 0);
  auto resp2 = user1.block_trade_create(create.Build());
  ASSERT_NE(te->PopResult().m_msg.get(), nullptr);
  ASSERT_NE(te->PopResult().m_msg.get(), nullptr);
}

TEST_F(BCreateTest, create_just_option_without_pre_occupy) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("create_just_option", uid, "create_just_option", ECoin::USDC, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  biz::user_id_t uid2 = 10004;
  stub user2(te, uid2);
  FutureDepositBuilder deposit_build2("create_just_option", uid2, "create_just_option", ECoin::USDC, 1, "10000", "0");
  auto deposit_resp2 = user2.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  BlockTradeCreateBuilder create(uid2, uid, "1234567");
  create.AddOrder("option", "BTC-31DEC24-40000-C", "20000", "0.01", ESide::Sell, te->GenUUID(), te->GenUUID(), 300000,
                  "300001", 8000000, 0, te->GenUUID(), te->GenUUID(), 250000, "25001", 700000, 0);
  auto resp2 = user1.block_trade_create(create.Build());
  ASSERT_NE(te->PopResult().m_msg.get(), nullptr);
  ASSERT_NE(te->PopResult().m_msg.get(), nullptr);
}

TEST_F(BCreateTest, create_just_option_without_pre_occupy_check_order_id) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("create_just_option", uid, "create_just_option", ECoin::USDC, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  biz::user_id_t uid2 = 10004;
  stub user2(te, uid2);
  FutureDepositBuilder deposit_build2("create_just_option", uid2, "create_just_option", ECoin::USDC, 1, "10000", "0");
  auto deposit_resp2 = user2.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  BlockTradeCreateBuilder create1(uid2, uid, "1234569");
  create1.AddOrder("option", "BTC-31DEC24-40000-C", "20000", "0.01", ESide::Sell, "dfafasdf", te->GenUUID(), 300000,
                   "300001", 8000000, 0, te->GenUUID(), te->GenUUID(), 250000, "25001", 700000, 0);
  auto resp3 = user1.block_trade_create(create1.Build());
  ASSERT_EQ(resp3.get()->ret_code(), error::kErrorCodeBlockTradeParamsError);

  BlockTradeCreateBuilder create2(uid2, uid, "1234568");
  create2.AddOrder("option", "BTC-31DEC24-40000-C", "20000", "0.01", ESide::Sell, te->GenUUID(), te->GenUUID(), 300000,
                   "300001", 8000000, 0, "dsagsadgasdg", te->GenUUID(), 250000, "25001", 700000, 0);
  auto resp4 = user1.block_trade_create(create2.Build());
  ASSERT_EQ(resp4.get()->ret_code(), error::kErrorCodeBlockTradeParamsError);
}

//  TEST_F(BCreateTest, test_option_max_fee) {
//    biz::user_id_t uid = 100000;
//    biz::account_id_t account_id(102);
//    biz::symbol_t symbol(1000001);
//    biz::coin_t coin(ECoin::USDC);
//    store::UserDataManager *user_data_manager_ = new store::UserDataManager();
//    store::PerUserStore *perUserStore = user_data_manager_->GetUserData(uid);
//    // 初始化
//    if (perUserStore == nullptr) {
//      user_data_manager_->CreateUserData(uid);
//      perUserStore = user_data_manager_->GetUserData(uid);
//    }
//    perUserStore->working_coins_[coin] = nullptr;
//
//    store::CowSetting::Ptr user_setting_ = std::make_shared<store::CowSetting>();
//    perUserStore->user_setting_ = user_setting_;
//
//    auto header = std::make_shared<store::Header>();
//    header_->op_from = "op_from";
//    header_->user_ip = "127.0.0.1";
//    header_->uid = uid;
//    header_->account_id = account_id;
//    header_->symbol = symbol;
//
//    std::shared_ptr<const event::BizEvent> bizEvent = std::make_shared<const event::BizEvent>(
//        event::BizEvent(uid, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
//                        event::EventDispatchType::kUnicastToUser, event::EventBizType::kCreateOrder));
//    store::PerWorkerStore *per_worker_store = new store::PerWorkerStore();
//    auto draft_pkg = std::make_shared<biz::DraftPkg>(bizEvent, header_, per_worker_store, perUserStore);
//
//    auto ao1 = std::make_shared<store::Order>();
//    ao1->exec_fee = 12345;
//    ao1->order_id = "order_id_1";
//    ao1->symbol = 1;
//    ao1->order_status = EOrderStatus::Cancelled;
//    ao1->cross_status = ECrossStatus::Canceled;
//    ao1->side = ESide::Sell;
//    ao1->price = 100000;
//    ao1->order_type = EOrderType::Limit;
//    draft_pkg->OptionCreateOrAttachOrder(ao1);
//
//    auto ao2 = std::make_shared<store::Order>();
//    ao2->exec_fee = 123456;
//    ao2->order_id = "order_id_2";
//    ao2->symbol = 1;
//    ao2->order_status = EOrderStatus::Cancelled;
//    ao2->cross_status = ECrossStatus::Canceled;
//    ao2->side = ESide::Sell;
//    ao2->price = 100000;
//    ao2->order_type = EOrderType::Limit;
//    draft_pkg->OptionCreateOrAttachOrder(ao2);
//
//    auto ao3 = std::make_shared<store::Order>();
//    ao3->exec_fee = 1234567;
//    ao3->order_id = "order_id_3";
//    ao3->symbol = 1;
//    ao3->order_status = EOrderStatus::Cancelled;
//    ao3->cross_status = ECrossStatus::Canceled;
//    ao3->side = ESide::Sell;
//    ao3->price = 100000;
//    ao3->order_type = EOrderType::Limit;
//    draft_pkg->OptionCreateOrAttachOrder(ao3);
//
//    biz::BlockTradeOrderBiz::CalcOptionMaxFee(draft_pkg.get());
//  }

TEST_F(BCreateTest, test_spot_remove_block_trade_order) {
#ifndef __linux__
  GTEST_SKIP() << "edward.yang will fix it";
#endif

  biz::user_id_t uid = 100000;
  biz::user_id_t uid2 = 10001;
  stub user1(te, uid);
  stub user2(te, uid2);

  FutureDepositBuilder deposit_build(te->GenUUID(), uid, te->GenUUID(), 5, 1, "10000000", "0");
  user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2(te->GenUUID(), uid, te->GenUUID(), 1, 1, "1000000", "0");
  user2.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  for (int i = 0; i < 1100; ++i) {
    auto block_trade_id = te->GenUUID();
    BlockTradeCreateBuilder create(uid, uid2, block_trade_id);
    create.AddOrder("spot", "BTCUSDT", "20000", "0.001", ESide::Buy,
                    std::to_string(application::GlobalVarManager::Instance().snow_flake_gen().NextId()),
                    std::to_string(application::GlobalVarManager::Instance().snow_flake_gen().NextId()), 100000, "", 0,
                    0, std::to_string(application::GlobalVarManager::Instance().snow_flake_gen().NextId()),
                    std::to_string(application::GlobalVarManager::Instance().snow_flake_gen().NextId()), 100000, "", 0,
                    0);
    user1.block_trade_create(create.Build());
    te->PopResult();
    te->PopResult();
  }
}

TEST_F(BCreateTest, test_future_remove_block_trade_order) {
#ifndef __linux__
  GTEST_SKIP() << "edward.yang will fix it";
#endif

  biz::user_id_t uid = 100000;
  biz::user_id_t uid2 = 10001;
  stub user1(te, uid);
  stub user2(te, uid2);

  FutureDepositBuilder deposit_build(te->GenUUID(), uid, te->GenUUID(), 5, 1, "100000000", "0");
  user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  user2.process(deposit_build.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  for (int i = 0; i < 1100; ++i) {
    auto order_id = te->GenUUID();
    auto block_trade_id = te->GenUUID();
    BlockTradePreOccupyBuilder occupy(uid, block_trade_id, false, false);
    occupy.AddPreOrder("linear", "BTCUSDT", "20000", "0.001", ESide::Buy, order_id);
    auto resp1 = user1.block_trade_pre_occupy(occupy.Build());
    ASSERT_EQ(resp1.get()->ret_code(), 0);
    ASSERT_EQ(resp1.get()->pre_order_resp_items().size(), 1);
    ASSERT_NE(resp1.get()->pre_order_resp_items()[0].order_id(), "");
    ASSERT_EQ(resp1.get()->pre_order_resp_items()[0].order_link_id(), order_id);

    BlockTradeCreateBuilder create(uid, uid2, block_trade_id);
    create.AddOrder(
        "linear", "BTCUSDT", "20000", "0.001", ESide::Buy, resp1.get()->pre_order_resp_items()[0].order_id(),
        resp1.get()->pre_order_resp_items()[0].order_link_id(), 0, "", 0, 0, te->GenUUID(), te->GenUUID(), 0, "", 0, 0);
    auto resp = user1.block_trade_create(create.Build());
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    te->PopResult();
    te->PopResult();
    // std::cout << "ret" << resp->ret_code() << " time:" << bbase::utils::Time::GetTimeMs() << i << std::endl;
  }
}

TEST_F(BCreateTest, test_option_remove_block_trade_order) {
#ifndef __linux__
  GTEST_SKIP() << "edward.yang will fix it";
#endif

  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("create_just_option", uid, "create_just_option", ECoin::USDC, 1, "1000000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  biz::user_id_t uid2 = 10004;
  stub user2(te, uid2);
  FutureDepositBuilder deposit_build2("create_just_option", uid2, "create_just_option", ECoin::USDC, 1, "10000000",
                                      "0");
  auto deposit_resp2 = user2.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  for (int i = 0; i < 1100; ++i) {
    auto block_trade_id = te->GenUUID();
    BlockTradeCreateBuilder create(uid2, uid, block_trade_id);
    create.AddOrder("option", "BTC-31DEC24-40000-C", "20000", "0.01", ESide::Sell, te->GenUUID(), te->GenUUID(), 300000,
                    "300001", 8000000, 0, te->GenUUID(), te->GenUUID(), 250000, "25001", 700000, 0);
    auto resp = user1.block_trade_create(create.Build());
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    te->PopResult();
    te->PopResult();
    // std::cout << "ret" << resp->ret_code() << " time:" << bbase::utils::Time::GetTimeMs() << i << std::endl;
  }
}
std::shared_ptr<tmock::CTradeAppMock> BCreateTest::te;
