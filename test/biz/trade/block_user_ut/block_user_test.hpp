#pragma once

#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <iostream>
#include <memory>
#include <vector>

#include "biz_worker/service/trade/block_trade/block_trade_service.hpp"
#include "biz_worker/service/trade/futures/futures_service.hpp"
#include "biz_worker/service/trade/options/options_service.hpp"
#include "biz_worker/service/trade/spot/spot_service.hpp"
#include "biz_worker/service/wallet/wallet_service.hpp"
#include "src/biz_worker/service/base/base_handler.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/store/per_worker_store.hpp"
#include "src/data/error/panic.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class UserBlockManagerTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }

 protected:
  store::PerWorkerStore::Ptr workerStore = std::make_shared<store::PerWorkerStore>();
  worker::ResultHandler result_handler{};
  std::shared_ptr<biz::FuturesService> future_service =
      std::make_shared<biz::FuturesService>(EProductType::Futures, 0, &result_handler);

  std::shared_ptr<biz::SpotService> spot_service =
      std::make_shared<biz::SpotService>(EProductType::Spot, 0, &result_handler);

  std::shared_ptr<biz::OptionsService> options_service =
      std::make_shared<biz::OptionsService>(EProductType::Options, 0, &result_handler);

  std::shared_ptr<biz::BlockTradeService> block_trade_service =
      std::make_shared<biz::BlockTradeService>(EProductType::BlockTrade, 0, &result_handler);

  std::shared_ptr<biz::WalletService> wallet_service = std::make_shared<biz::WalletService>(0, &result_handler);
  static std::shared_ptr<tmock::CTradeAppMock> te;
};

class MockMatchingResultHandler : public biz::HandlerBase {
 public:
  static int32_t OnRecvMatchingResult(biz::DraftPkg* draft_pkg) {
    PANIC(draft_pkg->uid(), "panic for ut");
    return error::Error{};
  }

  int32_t Handle(biz::DraftPkg* draft_pkg) override {
    return MockMatchingResultHandler::OnRecvMatchingResult(draft_pkg);
  }
};
