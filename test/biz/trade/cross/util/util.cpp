#include "test/biz/trade/cross/util/util.hpp"

#include <memory>
#include <string>

#include "cross_worker/cross_common/util/order_submit_type_util.hpp"
#include "cross_worker/cross_common/util/spot_type_convert.hpp"
#include "src/cross_worker/cross_common/protocol/binary/request.hpp"
#include "src/cross_worker/cross_common/util/order_submit_type_util.hpp"
#include "src/cross_worker/cross_common/util/site_id_util.hpp"

TEST_F(UtilTest, error) {
  biz::CrossError c_err = biz::CrossError();
  c_err = std::string("test_error");
  ASSERT_EQ(c_err.ErrMsg(), "test_error");
}

// 行覆盖率
TEST_F(UtilTest, ToBinaryBuf) {
  biz::cross::spot::XHeader header = biz::cross::spot::XHeader();
  const char* temp = "test";
  header.ToBinaryBuf(temp);

  biz::cross::spot::request::XRequest req = biz::cross::spot::request::XRequest();
  const char* temp2 = "test2";
  req.FromBinaryBuf(temp2);
}

TEST_F(UtilTest, CancelTypeUtil) {
  biz::cross::CancelTypeUtil cancel_type_util = biz::cross::CancelTypeUtil();
  ECancelType des;

  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(0), des);
  ASSERT_EQ(des, ECancelType::CancelByUser);

  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(11), des);
  ASSERT_EQ(des, ECancelType::CancelByReduceOnly);

  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(12), des);
  ASSERT_EQ(des, ECancelType::CancelByReplace);

  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(20), des);
  ASSERT_EQ(des, ECancelType::CancelAllBeforeLiq);

  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(21), des);
  ASSERT_EQ(des, ECancelType::CancelByLiq);

  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(22), des);
  ASSERT_EQ(des, ECancelType::CancelByPrepareLiq);

  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(23), des);
  ASSERT_EQ(des, ECancelType::CancelByLiqCloseOut);

  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(24), des);
  ASSERT_EQ(des, ECancelType::CancelByLiqStart);

  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(25), des);
  ASSERT_EQ(des, ECancelType::CancelByLiqTimeout);

  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(30), des);
  ASSERT_EQ(des, ECancelType::CancelAllBeforeAdl);

  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(32), des);
  ASSERT_EQ(des, ECancelType::CancelByPrepareAdl);

  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(40), des);
  ASSERT_EQ(des, ECancelType::CancelByPriceRange);

  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(41), des);
  ASSERT_EQ(des, ECancelType::CancelByPlacer);

  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(50), des);
  ASSERT_EQ(des, ECancelType::CancelByAdmin);

  // other
  cancel_type_util.FromCross(biz::cross::request::CanceledByByte(100), des);
  ASSERT_EQ(des, ECancelType::CancelByUser);

  biz::cross::request::CanceledByByte des2;
  cancel_type_util.ToCross(ECancelType::CancelByUser, des2);
  ASSERT_EQ(des2, 0);

  cancel_type_util.ToCross(ECancelType::CancelByReduceOnly, des2);
  ASSERT_EQ(des2, 11);

  cancel_type_util.ToCross(ECancelType::CancelByReplace, des2);
  ASSERT_EQ(des2, 12);

  cancel_type_util.ToCross(ECancelType::CancelAllBeforeLiq, des2);
  ASSERT_EQ(des2, 20);

  cancel_type_util.ToCross(ECancelType::CancelByLiq, des2);
  ASSERT_EQ(des2, 21);

  cancel_type_util.ToCross(ECancelType::CancelByPrepareLiq, des2);
  ASSERT_EQ(des2, 22);

  cancel_type_util.ToCross(ECancelType::CancelByLiqCloseOut, des2);
  ASSERT_EQ(des2, 23);

  cancel_type_util.ToCross(ECancelType::CancelByLiqStart, des2);
  ASSERT_EQ(des2, 24);

  cancel_type_util.ToCross(ECancelType::CancelByLiqTimeout, des2);
  ASSERT_EQ(des2, 25);

  cancel_type_util.ToCross(ECancelType::CancelAllBeforeAdl, des2);
  ASSERT_EQ(des2, 30);

  cancel_type_util.ToCross(ECancelType::CancelByPrepareAdl, des2);
  ASSERT_EQ(des2, 32);

  cancel_type_util.ToCross(ECancelType::CancelByPriceRange, des2);
  ASSERT_EQ(des2, 40);

  cancel_type_util.ToCross(ECancelType::CancelByPlacer, des2);
  ASSERT_EQ(des2, 41);

  cancel_type_util.ToCross(ECancelType::CancelByAdmin, des2);
  ASSERT_EQ(des2, 50);
}

TEST_F(UtilTest, CreateTypeUtil) {
  biz::cross::CreateTypeUtil create_type_util = biz::cross::CreateTypeUtil();
  ECreateType des;

  create_type_util.FromCross(biz::cross::request::CreatedByByte(0), des);
  ASSERT_EQ(des, ECreateType::CreateByUser);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(1), des);
  ASSERT_EQ(des, ECreateType::CreateByClosing);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(2), des);
  ASSERT_EQ(des, ECreateType::CreateByAdminClosing);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(10), des);
  ASSERT_EQ(des, ECreateType::CreateByStopOrder);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(11), des);
  ASSERT_EQ(des, ECreateType::CreateByTakeProfit);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(12), des);
  ASSERT_EQ(des, ECreateType::CreateByStopLoss);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(13), des);
  ASSERT_EQ(des, ECreateType::CreateByTrailingStop);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(18), des);
  ASSERT_EQ(des, ECreateType::CreateByForce_PassThrough);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(20), des);
  ASSERT_EQ(des, ECreateType::CreateByLiq);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(21), des);
  ASSERT_EQ(des, ECreateType::CreateByLiqCloseOut);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(30), des);
  ASSERT_EQ(des, ECreateType::CreateByAdl_PassThrough);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(31), des);
  ASSERT_EQ(des, ECreateType::CreateByTakeOver_PassThrough);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(32), des);
  ASSERT_EQ(des, ECreateType::CreateBySwapPz_PassThrough);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(40), des);
  ASSERT_EQ(des, ECreateType::CreateByArbitrager);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(41), des);
  ASSERT_EQ(des, ECreateType::CreateByDefender);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(42), des);
  ASSERT_EQ(des, ECreateType::CreateByPlacer);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(43), des);
  ASSERT_EQ(des, ECreateType::CreateByBlock_PassThrough);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(44), des);
  ASSERT_EQ(des, ECreateType::CreateByRiskStepLiqClose);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(45), des);
  ASSERT_EQ(des, ECreateType::CreateByRiskClosePz_PassThrough);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(46), des);
  ASSERT_EQ(des, ECreateType::CreateByStepLiqClose);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(48), des);
  ASSERT_EQ(des, ECreateType::CreateByOTCTakeOver);

  create_type_util.FromCross(biz::cross::request::CreatedByByte(62), des);
  ASSERT_EQ(des, ECreateType::CreateByHedgedStepLiqClose);
  // other
  create_type_util.FromCross(biz::cross::request::CreatedByByte(100), des);
  ASSERT_EQ(des, ECreateType::CreateByUser);

  biz::cross::request::CreatedByByte des2;
  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByUser, des2);
  ASSERT_EQ(des2, 0);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByClosing, des2);
  ASSERT_EQ(des2, 1);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByAdminClosing, des2);
  ASSERT_EQ(des2, 2);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByStopOrder, des2);
  ASSERT_EQ(des2, 10);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByTakeProfit, des2);
  ASSERT_EQ(des2, 11);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByStopLoss, des2);
  ASSERT_EQ(des2, 12);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByTrailingStop, des2);
  ASSERT_EQ(des2, 13);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByLiq, des2);
  ASSERT_EQ(des2, 20);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByLiqCloseOut, des2);
  ASSERT_EQ(des2, 21);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByAdl_PassThrough, des2);
  ASSERT_EQ(des2, 30);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByTakeOver_PassThrough, des2);
  ASSERT_EQ(des2, 31);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByForce_PassThrough, des2);
  ASSERT_EQ(des2, 18);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateBySwapPz_PassThrough, des2);
  ASSERT_EQ(des2, 32);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByArbitrager, des2);
  ASSERT_EQ(des2, 40);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByDefender, des2);
  ASSERT_EQ(des2, 41);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByPlacer, des2);
  ASSERT_EQ(des2, 42);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByBlock_PassThrough, des2);
  ASSERT_EQ(des2, 43);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByRiskStepLiqClose, des2);
  ASSERT_EQ(des2, 44);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByRiskClosePz_PassThrough, des2);
  ASSERT_EQ(des2, 45);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByStepLiqClose, des2);
  ASSERT_EQ(des2, 46);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByOTCTakeOver, des2);
  ASSERT_EQ(des2, 48);

  create_type_util.ToCross(EProductType::Futures, ECreateType::CreateByHedgedStepLiqClose, des2);
  ASSERT_EQ(des2, 62);
}

TEST_F(UtilTest, CxlRejReasonUtil) {
  // String
  biz::cross::CxlRejReasonUtil cxl_rejReason_util = biz::cross::CxlRejReasonUtil();
  ECxlRejReason des;

  biz::CrossError err = cxl_rejReason_util.FromCross("", des);
  ASSERT_EQ(err.ErrMsg(), "");

  cxl_rejReason_util.FromCross("test", des);
  ASSERT_EQ(des, ECxlRejReason::EC_NoError);

  // ErrorCodeUint16
  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(0), des);
  ASSERT_EQ(des, ECxlRejReason::EC_NoError);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(1), des);
  ASSERT_EQ(des, ECxlRejReason::EC_Others);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(2), des);
  ASSERT_EQ(des, ECxlRejReason::EC_MissingClOrdID);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(3), des);
  ASSERT_EQ(des, ECxlRejReason::EC_DuplicatedClOrdID);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(4), des);
  ASSERT_EQ(des, ECxlRejReason::EC_OrigClOrdIDDoesNotExist);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(5), des);
  ASSERT_EQ(des, ECxlRejReason::EC_UnknownMessageType);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(6), des);
  ASSERT_EQ(des, ECxlRejReason::EC_UnknownOrderType);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(7), des);
  ASSERT_EQ(des, ECxlRejReason::EC_UnknownSide);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(8), des);
  ASSERT_EQ(des, ECxlRejReason::EC_UnknownTimeInForce);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(9), des);
  ASSERT_EQ(des, ECxlRejReason::EC_WronglyRouted);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(10), des);
  ASSERT_EQ(des, ECxlRejReason::EC_LimitOrderInvalidPrice);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(11), des);
  ASSERT_EQ(des, ECxlRejReason::EC_NoEnoughQtyToFill);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(12), des);
  ASSERT_EQ(des, ECxlRejReason::EC_NoImmediateQtyToFill);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(13), des);
  ASSERT_EQ(des, ECxlRejReason::EC_PerCancelRequest);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(14), des);
  ASSERT_EQ(des, ECxlRejReason::EC_PostOnlyWillTakeLiquidity);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(15), des);
  ASSERT_EQ(des, ECxlRejReason::EC_InvalidSymbolStatus);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(16), des);
  ASSERT_EQ(des, ECxlRejReason::EC_EcInvalidQty);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(17), des);
  ASSERT_EQ(des, ECxlRejReason::EC_InvalidAmount);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(18), des);
  ASSERT_EQ(des, ECxlRejReason::EC_MarketOrderNoSupportTIF);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(19), des);
  ASSERT_EQ(des, ECxlRejReason::EC_LoadOrderCancel);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(21), des);
  ASSERT_EQ(des, ECxlRejReason::EC_MarketQuoteNoSuppSell);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(22), des);
  ASSERT_EQ(des, ECxlRejReason::EC_DisorderOrderID);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(23), des);
  ASSERT_EQ(des, ECxlRejReason::EC_InvalidBaseValue);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(24), des);
  ASSERT_EQ(des, ECxlRejReason::EC_LoadOrderCanMatch);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(25), des);
  ASSERT_EQ(des, ECxlRejReason::EC_SecurityStatusFail);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(26), des);
  ASSERT_EQ(des, ECxlRejReason::EC_ReachMaxTradeNum);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(27), des);
  ASSERT_EQ(des, ECxlRejReason::EC_ReachMarketPriceLimit);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(28), des);
  ASSERT_EQ(des, ECxlRejReason::EC_BySelfMatch);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(29), des);
  ASSERT_EQ(des, ECxlRejReason::EC_InvalidSmpType);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(30), des);
  ASSERT_EQ(des, ECxlRejReason::EC_ReachRiskPriceLimit);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(31), des);
  ASSERT_EQ(des, ECxlRejReason::EC_CancelReplaceOrder);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(32), des);
  ASSERT_EQ(des, ECxlRejReason::EC_CancelByOrderValueZero);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(33), des);
  ASSERT_EQ(des, ECxlRejReason::EC_CancelByMatchValueZero);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(34), des);
  ASSERT_EQ(des, ECxlRejReason::EC_InvalidUserType);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(35), des);
  ASSERT_EQ(des, ECxlRejReason::EC_InvalidMirrorOid);

  cxl_rejReason_util.FromCrossSpot(biz::cross::spot::ErrorCodeUint16(36), des);
  ASSERT_EQ(des, ECxlRejReason::EC_InvalidMirrorUid);
}

TEST_F(UtilTest, LastLiquidityIndUtil) {
  ELastLiquidityInd des;

  biz::cross::LastLiquidityIndUtil::FromCross(biz::cross::response::LiquidityIndByte(0), des);
  ASSERT_EQ(des, ELastLiquidityInd::LiquidityIndNA);

  biz::cross::LastLiquidityIndUtil::FromCross(biz::cross::response::LiquidityIndByte('1'), des);
  ASSERT_EQ(des, ELastLiquidityInd::AddedLiquidity);

  biz::cross::LastLiquidityIndUtil::FromCross(biz::cross::response::LiquidityIndByte('2'), des);
  ASSERT_EQ(des, ELastLiquidityInd::RemovedLiquidity);

  biz::cross::LastLiquidityIndUtil::FromCross(biz::cross::response::LiquidityIndByte(u_int8_t(-1)), des);
  ASSERT_EQ(des, ELastLiquidityInd::LiquidityIndNA);

  biz::cross::LastLiquidityIndUtil::FromCross(biz::cross::spot::LiquidityInd('0'), des);
  ASSERT_EQ(des, ELastLiquidityInd::LiquidityIndNA);

  biz::cross::LastLiquidityIndUtil::FromCross(biz::cross::spot::LiquidityInd('1'), des);
  ASSERT_EQ(des, ELastLiquidityInd::AddedLiquidity);

  biz::cross::LastLiquidityIndUtil::FromCross(biz::cross::spot::LiquidityInd('2'), des);
  ASSERT_EQ(des, ELastLiquidityInd::RemovedLiquidity);

  biz::cross::LastLiquidityIndUtil::FromCross(biz::cross::spot::LiquidityInd(u_int8_t(-1)), des);
  ASSERT_EQ(des, ELastLiquidityInd::LiquidityIndNA);
}

TEST_F(UtilTest, OrderStatusUtil) {
  EOrderStatus des;

  biz::cross::OrderStatusUtil::FromCross(biz::cross::response::OrderStatusByte('0'), des);
  ASSERT_EQ(des, EOrderStatus::New);

  biz::cross::OrderStatusUtil::FromCross(biz::cross::response::OrderStatusByte('1'), des);
  ASSERT_EQ(des, EOrderStatus::PartiallyFilled);

  biz::cross::OrderStatusUtil::FromCross(biz::cross::response::OrderStatusByte('2'), des);
  ASSERT_EQ(des, EOrderStatus::Filled);

  biz::cross::OrderStatusUtil::FromCross(biz::cross::response::OrderStatusByte('4'), des);
  ASSERT_EQ(des, EOrderStatus::Cancelled);

  biz::cross::OrderStatusUtil::FromCross(biz::cross::response::OrderStatusByte('8'), des);
  ASSERT_EQ(des, EOrderStatus::Rejected);

  biz::cross::OrderStatusUtil::FromCross(biz::cross::spot::OrderStatus('0'), des);
  ASSERT_EQ(des, EOrderStatus::New);

  biz::cross::OrderStatusUtil::FromCross(biz::cross::spot::OrderStatus('1'), des);
  ASSERT_EQ(des, EOrderStatus::PartiallyFilled);

  biz::cross::OrderStatusUtil::FromCross(biz::cross::spot::OrderStatus('2'), des);
  ASSERT_EQ(des, EOrderStatus::Filled);

  biz::cross::OrderStatusUtil::FromCross(biz::cross::spot::OrderStatus('4'), des);
  ASSERT_EQ(des, EOrderStatus::Cancelled);

  biz::cross::OrderStatusUtil::FromCross(biz::cross::spot::OrderStatus('8'), des);
  ASSERT_EQ(des, EOrderStatus::Rejected);
}

TEST_F(UtilTest, OrderTypeUtil) {
  biz::cross::OrderTypeUtil order_type_util = biz::cross::OrderTypeUtil();
  biz::cross::request::OrderTypeByte des;

  order_type_util.ToCross(EOrderType(1), des);
  ASSERT_EQ(des, biz::cross::request::kOTMarketOrder);

  order_type_util.ToCross(EOrderType(2), des);
  ASSERT_EQ(des, biz::cross::request::kOTLimitOrder);

  order_type_util.ToCross(EOrderType(3), des);
  ASSERT_EQ(des, biz::cross::request::kOTLimitOrder);

  order_type_util.ToCross(EOrderType(4), des);
  ASSERT_EQ(des, biz::cross::request::kOTLimitOrder);

  order_type_util.ToCross(EOrderType(5), des);
  ASSERT_EQ(des, biz::cross::request::kOTLimitOrder);

  EOrderType des2;
  order_type_util.FromCross(biz::cross::request::OrderTypeByte('1'), des2);
  ASSERT_EQ(des2, EOrderType::Market);

  order_type_util.FromCross(biz::cross::request::OrderTypeByte('2'), des2);
  ASSERT_EQ(des2, EOrderType::Limit);

  order_type_util.FromCross(biz::cross::request::OrderTypeByte('3'), des2);
  ASSERT_EQ(des2, EOrderType::UNKNOWN);

  order_type_util.FromCross(biz::cross::spot::OrderType('2'), des2);
  ASSERT_EQ(des2, EOrderType::Market);

  order_type_util.FromCross(biz::cross::spot::OrderType('3'), des2);
  ASSERT_EQ(des2, EOrderType::Market);

  order_type_util.FromCross(biz::cross::spot::OrderType('0'), des2);
  ASSERT_EQ(des2, EOrderType::Limit);

  order_type_util.FromCross(biz::cross::spot::OrderType('4'), des2);
  ASSERT_EQ(des2, EOrderType::Limit);

  order_type_util.FromCross(biz::cross::spot::OrderType('5'), des2);
  ASSERT_EQ(des2, EOrderType::Limit);

  order_type_util.FromCross(biz::cross::spot::OrderType('6'), des2);
  ASSERT_EQ(des2, EOrderType::Limit);

  order_type_util.FromCross(biz::cross::spot::OrderType('7'), des2);
  ASSERT_EQ(des2, EOrderType::Limit);

  order_type_util.FromCross(biz::cross::spot::OrderType('8'), des2);
  ASSERT_EQ(des2, EOrderType::Limit);  // 市价转限价,这里再把限价转市价

  order_type_util.FromCross(biz::cross::spot::OrderType('9'), des2);
  ASSERT_EQ(des2, EOrderType::Limit);  // 市价转限价,这里再把限价转市价
}

TEST_F(UtilTest, SideUtil) {
  biz::cross::SideUtil side_util = biz::cross::SideUtil();
  biz::cross::request::SideByte des;

  side_util.ToCross(ESide(0), des);
  ASSERT_EQ(des, 0);

  side_util.ToCross(ESide(1), des);
  ASSERT_EQ(des, '1');

  side_util.ToCross(ESide(2), des);
  ASSERT_EQ(des, '2');

  side_util.ToCross(ESide(3), des);
  ASSERT_EQ(des, 0);

  ESide des2;
  side_util.FromCross(biz::cross::request::SideByte('1'), des2);
  ASSERT_EQ(des2, ESide::Buy);

  side_util.FromCross(biz::cross::request::SideByte('2'), des2);
  ASSERT_EQ(des2, ESide::Sell);

  side_util.FromCross(biz::cross::request::SideByte('3'), des2);
  ASSERT_EQ(des2, ESide::None);

  ASSERT_EQ(side_util.IsValid(biz::cross::request::SideByte('1'), false), true);
  ASSERT_EQ(side_util.IsValid(biz::cross::request::SideByte('2'), false), true);
  ASSERT_EQ(side_util.IsValid(biz::cross::request::SideByte('3'), false), false);

  side_util.FromCross(biz::cross::spot::Side('0'), des2);
  ASSERT_EQ(des2, ESide::Buy);

  side_util.FromCross(biz::cross::spot::Side('1'), des2);
  ASSERT_EQ(des2, ESide::Sell);
}

TEST_F(UtilTest, SmpTypeUtil) {
  biz::cross::SmpTypeUtil smp_type_util = biz::cross::SmpTypeUtil();
  ESmpType des;

  smp_type_util.ToCross(ESmpType(), des);

  smp_type_util.FromCross(biz::cross::request::SMPType(0), des);
  ASSERT_EQ(des, ESmpType::None);

  smp_type_util.FromCross(biz::cross::request::SMPType(1), des);
  ASSERT_EQ(des, ESmpType::CancelTaker);

  smp_type_util.FromCross(biz::cross::request::SMPType(2), des);
  ASSERT_EQ(des, ESmpType::CancelMaker);

  smp_type_util.FromCross(biz::cross::request::SMPType(3), des);
  ASSERT_EQ(des, ESmpType::CancelBoth);
}

TEST_F(UtilTest, SpotTypeConvert) {
  biz::cross::SpotTypeConvert spot_type_convert = biz::cross::SpotTypeConvert();
  biz::cross::spot::OrderType to;

  spot_type_convert.OrderType2Cross(EMarketUnit::UNKNOWN, ESide::Buy, EOrderType::UNKNOWN, to);
  ASSERT_EQ(to, 0);

  spot_type_convert.OrderType2Cross(EMarketUnit::UNKNOWN, ESide::None, EOrderType::Market, to);
  ASSERT_EQ(to, 0);

  spot_type_convert.OrderType2Cross(EMarketUnit::UNKNOWN, ESide::Buy, EOrderType::Market, to);
  ASSERT_EQ(to, biz::cross::spot::OrderType::kMarketQuoteOrder);

  spot_type_convert.OrderType2Cross(EMarketUnit::UNKNOWN, ESide::Sell, EOrderType::Market, to);
  ASSERT_EQ(to, biz::cross::spot::OrderType::kMarketBaseOrder);

  spot_type_convert.OrderType2Cross(EMarketUnit::UNKNOWN, ESide::None, EOrderType::Limit, to);
  ASSERT_EQ(to, biz::cross::spot::OrderType::kLimitOrder);

  spot_type_convert.OrderType2Cross(EMarketUnit::UNKNOWN, ESide::None, EOrderType::BLimit, to);
  ASSERT_EQ(to, biz::cross::spot::OrderType::kLimitOrder);

  spot_type_convert.OrderType2Cross(EMarketUnit::UNKNOWN, ESide::None, EOrderType::MTLLimit, to);
  ASSERT_EQ(to, biz::cross::spot::OrderType::kLimitOrder);

  spot_type_convert.OrderType2Cross(EMarketUnit::BaseCoin, ESide::Buy, EOrderType::Limit, to);
  ASSERT_EQ(to, biz::cross::spot::OrderType::kLimitBaseOrder);

  spot_type_convert.OrderType2Cross(EMarketUnit::QuoteCoin, ESide::Sell, EOrderType::Limit, to);
  ASSERT_EQ(to, biz::cross::spot::OrderType::kLimitQuoteOrder);
}

TEST_F(UtilTest, TimeInForceUtil) {
  biz::cross::TimeInForceUtil time_in_force_util = biz::cross::TimeInForceUtil();
  biz::cross::request::TimeInForceByte des;

  time_in_force_util.ToCross(ETimeInForce(0), des);
  ASSERT_EQ(des, biz::cross::request::kTIFGoodTillCancel);

  time_in_force_util.ToCross(ETimeInForce(1), des);
  ASSERT_EQ(des, biz::cross::request::kTIFGoodTillCancel);

  time_in_force_util.ToCross(ETimeInForce(2), des);
  ASSERT_EQ(des, biz::cross::request::kTIFPostOnly);

  time_in_force_util.ToCross(ETimeInForce(3), des);
  ASSERT_EQ(des, biz::cross::request::kTIFImmediateOrCancel);

  time_in_force_util.ToCross(ETimeInForce(4), des);
  ASSERT_EQ(des, biz::cross::request::kTIFFillOrKill);

  time_in_force_util.ToCross(ETimeInForce(5), des);
  ASSERT_EQ(des, biz::cross::request::kTRetailPriceImprovement);

  ETimeInForce des2;
  time_in_force_util.FromCross(biz::cross::request::TimeInForceByte('1'), des2);
  ASSERT_EQ(des2, ETimeInForce::GoodTillCancel);

  time_in_force_util.FromCross(biz::cross::request::TimeInForceByte('2'), des2);
  ASSERT_EQ(des2, ETimeInForce::PostOnly);

  time_in_force_util.FromCross(biz::cross::request::TimeInForceByte('3'), des2);
  ASSERT_EQ(des2, ETimeInForce::ImmediateOrCancel);

  time_in_force_util.FromCross(biz::cross::request::TimeInForceByte('4'), des2);
  ASSERT_EQ(des2, ETimeInForce::FillOrKill);

  time_in_force_util.FromCross(biz::cross::request::TimeInForceByte('5'), des2);
  ASSERT_EQ(des2, ETimeInForce::RPI);

  time_in_force_util.FromCross(biz::cross::spot::TimeInForce('0'), des2);
  ASSERT_EQ(des2, ETimeInForce::GoodTillCancel);

  time_in_force_util.FromCross(biz::cross::spot::TimeInForce('1'), des2);
  ASSERT_EQ(des2, ETimeInForce::FillOrKill);

  time_in_force_util.FromCross(biz::cross::spot::TimeInForce('2'), des2);
  ASSERT_EQ(des2, ETimeInForce::ImmediateOrCancel);

  time_in_force_util.FromCross(biz::cross::spot::TimeInForce('5'), des2);
  ASSERT_EQ(des2, ETimeInForce::RPI);
}

TEST_F(UtilTest, XReqTypeUtil) {
  biz::cross::XReqTypeUtil x_req_type_util = biz::cross::XReqTypeUtil();
  EXReqType des;

  x_req_type_util.FromCross(biz::cross::request::MessageTypeUint16(100), des);
  ASSERT_EQ(des, EXReqType::UNKNOWN);

  x_req_type_util.FromCross(biz::cross::request::MessageTypeUint16(biz::cross::request::kMTNewOrder), des);
  ASSERT_EQ(des, EXReqType::x_create);

  x_req_type_util.FromCross(biz::cross::request::MessageTypeUint16(biz::cross::request::kMTCancelOrder), des);
  ASSERT_EQ(des, EXReqType::x_cancel);

  x_req_type_util.FromCross(biz::cross::request::MessageTypeUint16(biz::cross::request::kMTCancelReplaceOrder), des);
  ASSERT_EQ(des, EXReqType::x_cancel);

  x_req_type_util.FromCross(biz::cross::request::MessageTypeUint16(biz::cross::request::kMTSettleFundingFee), des);
  ASSERT_EQ(des, EXReqType::x_settle_funding_fee);

  x_req_type_util.FromCross(biz::cross::request::MessageTypeUint16(biz::cross::request::kMTReconciliation), des);
  ASSERT_EQ(des, EXReqType::x_reconciliation);
}

TEST_F(UtilTest, OrderSubmitTypeUtil) {
  biz::cross::response::OrderSubmitType des = 0;
  biz::cross::OrderSubmitTypeUtil::OrderSubmitType2Cross(EOrderSubmitType::None, des);
  ASSERT_EQ(des, 0);
  biz::cross::OrderSubmitTypeUtil::OrderSubmitType2Cross(EOrderSubmitType::RealOrder, des);
  ASSERT_EQ(des, biz::cross::response::kRealOrder);
  biz::cross::OrderSubmitTypeUtil::OrderSubmitType2Cross(EOrderSubmitType::MirrorOrder, des);
  ASSERT_EQ(des, biz::cross::response::kMirrorOrder);

  EOrderSubmitType des_pb;
  biz::cross::OrderSubmitTypeUtil::OrderSubmitTypeFromCross(0, des_pb);
  ASSERT_EQ(des_pb, EOrderSubmitType::None);
  biz::cross::OrderSubmitTypeUtil::OrderSubmitTypeFromCross(biz::cross::response::kRealOrder, des_pb);
  ASSERT_EQ(des_pb, EOrderSubmitType::RealOrder);
  biz::cross::OrderSubmitTypeUtil::OrderSubmitTypeFromCross(biz::cross::response::kMirrorOrder, des_pb);
  ASSERT_EQ(des_pb, EOrderSubmitType::MirrorOrder);

  biz::cross::spot::OrderSubmitType spot_des = biz::cross::spot::OrderSubmitType::UnknownOrderSubmitType;
  biz::cross::OrderSubmitTypeUtil::SpotOrderSubmitType2Cross(EOrderSubmitType::None, spot_des);
  ASSERT_EQ(spot_des, biz::cross::spot::OrderSubmitType::UnknownOrderSubmitType);
  biz::cross::OrderSubmitTypeUtil::SpotOrderSubmitType2Cross(EOrderSubmitType::RealOrder, spot_des);
  ASSERT_EQ(spot_des, biz::cross::spot::OrderSubmitType::RealOrder);
  biz::cross::OrderSubmitTypeUtil::SpotOrderSubmitType2Cross(EOrderSubmitType::MirrorOrder, spot_des);
  ASSERT_EQ(spot_des, biz::cross::spot::OrderSubmitType::MirrorOrder);

  biz::cross::OrderSubmitTypeUtil::SpotOrderSubmitTypeFromCross(
      biz::cross::spot::OrderSubmitType::UnknownOrderSubmitType, des_pb);
  ASSERT_EQ(des_pb, EOrderSubmitType::None);
  biz::cross::OrderSubmitTypeUtil::SpotOrderSubmitTypeFromCross(biz::cross::spot::OrderSubmitType::RealOrder, des_pb);
  ASSERT_EQ(des_pb, EOrderSubmitType::RealOrder);
  biz::cross::OrderSubmitTypeUtil::SpotOrderSubmitTypeFromCross(biz::cross::spot::OrderSubmitType::MirrorOrder, des_pb);
  ASSERT_EQ(des_pb, EOrderSubmitType::MirrorOrder);
}

TEST_F(UtilTest, SiteIdUtil) {
  // tocross
  uint8_t des = 0;
  biz::str128_t src;
  src.SetValue(config::ConstConfig::BYBIT_SITE);
  biz::cross::SiteIdUtil::ToCross(src, des);
  ASSERT_EQ(des, 0);

  des = 0;
  src.SetValue(config::ConstConfig::EU_SITE);
  biz::cross::SiteIdUtil::ToCross(src, des);
  ASSERT_EQ(des, 1);

  des = 0;
  src.SetValue(config::ConstConfig::IDN_SITE);
  biz::cross::SiteIdUtil::ToCross(src, des);
  ASSERT_EQ(des, 2);

  des = 0;
  src.SetValue("");
  biz::cross::SiteIdUtil::ToCross(src, des);
  ASSERT_EQ(des, 0);

  std::string str;
  biz::cross::SiteIdUtil::FromCross(0, str);
  ASSERT_EQ(str, config::ConstConfig::BYBIT_SITE);
  biz::cross::SiteIdUtil::FromCross(1, str);
  ASSERT_EQ(str, config::ConstConfig::EU_SITE);
  biz::cross::SiteIdUtil::FromCross(2, str);
  ASSERT_EQ(str, config::ConstConfig::IDN_SITE);
  biz::cross::SiteIdUtil::FromCross(3, str);
  ASSERT_EQ(str, config::ConstConfig::BYBIT_SITE);
}
std::shared_ptr<tmock::CTradeAppMock> UtilTest::te;
