
#include "test/biz/trade/quote_service/quote_data_service_test.hpp"

#include <idtssdk.h>

#include <memory>
#include <unordered_set>

#include "proto/gen/models/quote/ob_lastprice.pb.h"
#include "src/application/application.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"
#include "test/mocks/hdts/message.hpp"  // NOLINT

TEST_F(QuoteDataReceiveTest, quate_data_reciever_option) {
  auto quote_data_synchronizer = te->quote_data_synchronizer();

  // produce 一条option数据
  mock::HdtsMessage produce_msg;
  dtssdk::Headers* ptrHeader = dtssdk::Headers::Create();
  ptrHeader->Add("type", "2");
  ptrHeader->Add("snap", "1");

  produce_msg.headers_.assign(reinterpret_cast<char*>(ptrHeader->Cptr()), ptrHeader->Size());

  ::models::quote::Orderbooks orderbooks;
  orderbooks.set_biztype(::models::quote::Orderbooks::OPTION);
  orderbooks.set_geartype(models::quote::Orderbooks::GEAR25);
  orderbooks.set_crossidx(10007);
  auto& depth_map = *orderbooks.mutable_depths();
  ::models::quote::OrderbookInfo info;
  info.set_symbolid(102311);
  info.set_crossseq(791);
  info.set_transacttimeus(1680074868193508);
  auto& price = *info.mutable_price();
  ::models::quote::PriceStat pricestat;
  auto openPrice = new ::models::quote::PriceAttribute;
  openPrice->set_price("1500");
  openPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_openprice(openPrice);

  auto highPrice = new ::models::quote::PriceAttribute;
  highPrice->set_price("1500");
  highPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_highprice(highPrice);

  auto lowPrice = new ::models::quote::PriceAttribute;
  lowPrice->set_price("1500");
  lowPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_lowprice(lowPrice);

  auto closePrice = new ::models::quote::PriceAttribute;
  closePrice->set_price("1500");
  closePrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_closeprice(closePrice);

  price[1] = pricestat;
  price[2] = pricestat;
  price[3] = pricestat;

  ::models::quote::DepthInfo* buy1 = info.add_buys();
  buy1->set_price("23000.00");
  buy1->set_quantity("0.300");

  ::models::quote::DepthInfo* sell1 = info.add_sells();
  sell1->set_price("20000.00");
  sell1->set_quantity("0.003");

  depth_map[102311] = info;

  produce_msg.topic_ = "quote2trading.ob.10007";
  produce_msg.pay_load_ = orderbooks.SerializeAsString();

  auto receiver = quote_data_synchronizer->GetReceiver(10007);
  EXPECT_NE(receiver, nullptr);

  auto handle = receiver->msg_handler();
  EXPECT_NE(handle, nullptr);

  handle->OnConsumeCallback(produce_msg);
  dtssdk::Headers::DestroyHeaders(ptrHeader);
}

TEST_F(QuoteDataReceiveTest, quate_data_reciever_future) {
  auto quote_data_synchronizer = te->quote_data_synchronizer();

  // produce 一条option数据
  mock::HdtsMessage produce_msg;
  dtssdk::Headers* ptrHeader = dtssdk::Headers::Create();
  ptrHeader->Add("type", "3");
  ptrHeader->Add("snap", "1");

  produce_msg.headers_.assign(reinterpret_cast<char*>(ptrHeader->Cptr()), ptrHeader->Size());

  ::models::quote::Orderbooks orderbooks;
  orderbooks.set_biztype(::models::quote::Orderbooks::FUTURE);
  orderbooks.set_geartype(models::quote::Orderbooks::GEAR200);
  orderbooks.set_crossidx(5);
  orderbooks.set_issymbollifestatuschanged(true);
  auto& depth_map = *orderbooks.mutable_depths();
  ::models::quote::OrderbookInfo info;
  info.set_symbolid(5);
  info.set_crossseq(791);
  info.set_transacttimeus(1680074868193508);
  auto& price = *info.mutable_price();
  ::models::quote::PriceStat pricestat;
  auto openPrice = new ::models::quote::PriceAttribute;
  openPrice->set_price("1500");
  openPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_openprice(openPrice);

  auto highPrice = new ::models::quote::PriceAttribute;
  highPrice->set_price("1500");
  highPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_highprice(highPrice);

  auto lowPrice = new ::models::quote::PriceAttribute;
  lowPrice->set_price("1500");
  lowPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_lowprice(lowPrice);

  auto closePrice = new ::models::quote::PriceAttribute;
  closePrice->set_price("1500");
  closePrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_closeprice(closePrice);

  price[1] = pricestat;

  ::models::quote::DepthInfo* buy1 = info.add_buys();
  buy1->set_price("23000.00");
  buy1->set_quantity("0.300");

  ::models::quote::DepthInfo* sell1 = info.add_sells();
  sell1->set_price("20000.00");
  sell1->set_quantity("0.003");

  depth_map[5] = info;

  produce_msg.topic_ = "quote2trading.ob.5";
  produce_msg.pay_load_ = orderbooks.SerializeAsString();

  auto receiver = quote_data_synchronizer->GetReceiver(5);
  EXPECT_NE(receiver, nullptr);

  auto handle = receiver->msg_handler();
  EXPECT_NE(handle, nullptr);

  handle->OnConsumeCallback(produce_msg);
  dtssdk::Headers::DestroyHeaders(ptrHeader);
}

TEST_F(QuoteDataReceiveTest, quate_data_reciever_future_1) {
  auto quote_data_synchronizer = te->quote_data_synchronizer();

  // produce 一条option数据
  mock::HdtsMessage produce_msg;
  dtssdk::Headers* ptrHeader = dtssdk::Headers::Create();
  ptrHeader->Add("type", "1");
  ptrHeader->Add("snap", "1");

  produce_msg.headers_.assign(reinterpret_cast<char*>(ptrHeader->Cptr()), ptrHeader->Size());

  ::models::quote::Orderbooks orderbooks;
  orderbooks.set_biztype(::models::quote::Orderbooks::FUTURE);
  orderbooks.set_geartype(models::quote::Orderbooks::GEAR1);
  orderbooks.set_crossidx(5);
  auto& depth_map = *orderbooks.mutable_depths();
  ::models::quote::OrderbookInfo info;
  info.set_symbolid(5);
  info.set_crossseq(791);
  info.set_transacttimeus(1680074868193508);
  auto& price = *info.mutable_price();
  ::models::quote::PriceStat pricestat;
  auto openPrice = new ::models::quote::PriceAttribute;
  openPrice->set_price("1500");
  openPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_openprice(openPrice);

  auto highPrice = new ::models::quote::PriceAttribute;
  highPrice->set_price("1500");
  highPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_highprice(highPrice);

  auto lowPrice = new ::models::quote::PriceAttribute;
  lowPrice->set_price("1500");
  lowPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_lowprice(lowPrice);

  auto closePrice = new ::models::quote::PriceAttribute;
  closePrice->set_price("1500");
  closePrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_closeprice(closePrice);

  price[1] = pricestat;

  ::models::quote::DepthInfo* buy = info.add_buys();
  buy->set_price("20000.00");
  buy->set_quantity("0.300");

  ::models::quote::DepthInfo* sell = info.add_sells();
  sell->set_price("23000.00");
  sell->set_quantity("0.003");

  depth_map[5] = info;

  produce_msg.topic_ = "quote2trading.ob.5";
  produce_msg.pay_load_ = orderbooks.SerializeAsString();

  auto receiver = quote_data_synchronizer->GetReceiver(5);
  EXPECT_NE(receiver, nullptr);

  auto handle = receiver->msg_handler();
  EXPECT_NE(handle, nullptr);

  handle->OnConsumeCallback(produce_msg);
  dtssdk::Headers::DestroyHeaders(ptrHeader);

  // 核对 ask1bid1
  EXPECT_EQ(market_data::ArrAsk1Bid1Info[5].ask1Price.load(), 230000000);
  EXPECT_EQ(market_data::ArrAsk1Bid1Info[5].bid1Price.load(), 200000000);

  // 更新包
  mock::HdtsMessage produce_msg1;
  dtssdk::Headers* ptrHeader1 = dtssdk::Headers::Create();
  ptrHeader1->Add("type", "1");
  ptrHeader1->Add("snap", "0");

  produce_msg1.headers_.assign(reinterpret_cast<char*>(ptrHeader1->Cptr()), ptrHeader1->Size());

  ::models::quote::Orderbooks orderbooks1;
  orderbooks1.set_biztype(::models::quote::Orderbooks::FUTURE);
  orderbooks1.set_geartype(models::quote::Orderbooks::GEAR1);
  orderbooks1.set_crossidx(5);
  auto& depth_map1 = *orderbooks1.mutable_depths();
  ::models::quote::OrderbookInfo info1;
  info1.set_symbolid(5);
  info1.set_crossseq(891);
  info1.set_transacttimeus(1680075868193508);
  auto& price1 = *info1.mutable_price();
  ::models::quote::PriceStat pricestat1;
  auto openPrice1 = new ::models::quote::PriceAttribute;
  openPrice1->set_price("1500");
  openPrice1->set_transacttimeus(1680075868193508);
  pricestat1.set_allocated_openprice(openPrice1);

  auto highPrice1 = new ::models::quote::PriceAttribute;
  highPrice1->set_price("1500");
  highPrice1->set_transacttimeus(1680075868193508);
  pricestat1.set_allocated_highprice(highPrice1);

  auto lowPrice1 = new ::models::quote::PriceAttribute;
  lowPrice1->set_price("1500");
  lowPrice1->set_transacttimeus(1680074869003508);
  pricestat1.set_allocated_lowprice(lowPrice1);

  auto closePrice1 = new ::models::quote::PriceAttribute;
  closePrice1->set_price("1500");
  closePrice1->set_transacttimeus(1680074869003508);
  pricestat1.set_allocated_closeprice(closePrice1);

  price1[1] = pricestat1;

  ::models::quote::DepthInfo* buy1 = info1.add_buys();
  buy1->set_price("21000.00");
  buy1->set_quantity("0.300");

  ::models::quote::DepthInfo* sell1 = info1.add_sells();
  sell1->set_price("24000.00");
  sell1->set_quantity("0.003");

  depth_map1[5] = info1;

  produce_msg1.topic_ = "quote2trading.ob.5";
  produce_msg1.pay_load_ = orderbooks1.SerializeAsString();

  handle->OnConsumeCallback(produce_msg1);
  dtssdk::Headers::DestroyHeaders(ptrHeader1);

  // 核对 ask1bid1
  EXPECT_EQ(market_data::ArrAsk1Bid1Info[5].ask1Price.load(), 240000000);
  EXPECT_EQ(market_data::ArrAsk1Bid1Info[5].bid1Price.load(), 210000000);
}

TEST_F(QuoteDataReceiveTest, quate_data_reciever_future_1_invalid_time) {
  auto quote_data_synchronizer = te->quote_data_synchronizer();

  // produce 一条option数据
  mock::HdtsMessage produce_msg;
  dtssdk::Headers* ptrHeader = dtssdk::Headers::Create();
  ptrHeader->Add("type", "1");
  ptrHeader->Add("snap", "1");

  produce_msg.headers_.assign(reinterpret_cast<char*>(ptrHeader->Cptr()), ptrHeader->Size());

  ::models::quote::Orderbooks orderbooks;
  orderbooks.set_biztype(::models::quote::Orderbooks::FUTURE);
  orderbooks.set_geartype(models::quote::Orderbooks::GEAR1);
  orderbooks.set_crossidx(5);
  auto& depth_map = *orderbooks.mutable_depths();
  ::models::quote::OrderbookInfo info;
  info.set_symbolid(5);
  info.set_crossseq(791);
  info.set_transacttimeus(1680074868193508);
  auto& price = *info.mutable_price();
  ::models::quote::PriceStat pricestat;
  auto openPrice = new ::models::quote::PriceAttribute;
  openPrice->set_price("1500");
  openPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_openprice(openPrice);

  auto highPrice = new ::models::quote::PriceAttribute;
  highPrice->set_price("1500");
  highPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_highprice(highPrice);

  auto lowPrice = new ::models::quote::PriceAttribute;
  lowPrice->set_price("1500");
  lowPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_lowprice(lowPrice);

  auto closePrice = new ::models::quote::PriceAttribute;
  closePrice->set_price("1500");
  closePrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_closeprice(closePrice);

  price[1] = pricestat;

  ::models::quote::DepthInfo* buy = info.add_buys();
  buy->set_price("20000.00");
  buy->set_quantity("0.300");

  ::models::quote::DepthInfo* sell = info.add_sells();
  sell->set_price("23000.00");
  sell->set_quantity("0.003");

  depth_map[5] = info;

  produce_msg.topic_ = "quote2trading.ob.5";
  produce_msg.pay_load_ = orderbooks.SerializeAsString();

  auto receiver = quote_data_synchronizer->GetReceiver(5);
  EXPECT_NE(receiver, nullptr);

  auto handle = receiver->msg_handler();
  EXPECT_NE(handle, nullptr);

  handle->OnConsumeCallback(produce_msg);
  dtssdk::Headers::DestroyHeaders(ptrHeader);

  // 更新包
  mock::HdtsMessage produce_msg1;
  dtssdk::Headers* ptrHeader1 = dtssdk::Headers::Create();
  ptrHeader1->Add("type", "1");
  ptrHeader1->Add("snap", "0");

  produce_msg1.headers_.assign(reinterpret_cast<char*>(ptrHeader1->Cptr()), ptrHeader1->Size());

  ::models::quote::Orderbooks orderbooks1;
  orderbooks1.set_biztype(::models::quote::Orderbooks::FUTURE);
  orderbooks1.set_geartype(models::quote::Orderbooks::GEAR1);
  orderbooks1.set_crossidx(5);
  auto& depth_map1 = *orderbooks1.mutable_depths();
  ::models::quote::OrderbookInfo info1;
  info1.set_symbolid(5);
  info1.set_crossseq(890);
  info1.set_transacttimeus(168007486093508);
  auto& price1 = *info1.mutable_price();
  ::models::quote::PriceStat pricestat1;
  auto openPrice1 = new ::models::quote::PriceAttribute;
  openPrice1->set_price("1500");
  openPrice1->set_transacttimeus(168007486093508);
  pricestat1.set_allocated_openprice(openPrice1);

  auto highPrice1 = new ::models::quote::PriceAttribute;
  highPrice1->set_price("1500");
  highPrice1->set_transacttimeus(168007486093508);
  pricestat1.set_allocated_highprice(highPrice1);

  auto lowPrice1 = new ::models::quote::PriceAttribute;
  lowPrice1->set_price("1500");
  lowPrice1->set_transacttimeus(168007486093508);
  pricestat1.set_allocated_lowprice(lowPrice1);

  auto closePrice1 = new ::models::quote::PriceAttribute;
  closePrice1->set_price("1500");
  closePrice1->set_transacttimeus(168007486093508);
  pricestat1.set_allocated_closeprice(closePrice1);

  price1[1] = pricestat1;

  ::models::quote::DepthInfo* buy1 = info1.add_buys();
  buy1->set_price("21000.00");
  buy1->set_quantity("0.300");

  ::models::quote::DepthInfo* sell1 = info1.add_sells();
  sell1->set_price("24000.00");
  sell1->set_quantity("0.003");

  depth_map1[5] = info1;

  produce_msg1.topic_ = "quote2trading.ob.5";
  produce_msg1.pay_load_ = orderbooks1.SerializeAsString();

  handle->OnConsumeCallback(produce_msg1);
  dtssdk::Headers::DestroyHeaders(ptrHeader1);

  // 更新包
  mock::HdtsMessage produce_msg2;
  dtssdk::Headers* ptrHeader2 = dtssdk::Headers::Create();
  ptrHeader2->Add("type", "1");
  ptrHeader2->Add("snap", "0");

  produce_msg2.headers_.assign(reinterpret_cast<char*>(ptrHeader2->Cptr()), ptrHeader2->Size());

  ::models::quote::Orderbooks orderbooks2;
  orderbooks2.set_biztype(::models::quote::Orderbooks::FUTURE);
  orderbooks2.set_geartype(models::quote::Orderbooks::GEAR1);
  orderbooks2.set_crossidx(5);
  auto& depth_map2 = *orderbooks2.mutable_depths();
  ::models::quote::OrderbookInfo info2;
  info2.set_symbolid(5);
  info2.set_crossseq(890);
  info2.set_transacttimeus(1680075868193508);
  auto& price2 = *info2.mutable_price();
  ::models::quote::PriceStat pricestat2;
  auto openPrice2 = new ::models::quote::PriceAttribute;
  openPrice2->set_price("-1");
  openPrice2->set_transacttimeus(-1);
  pricestat2.set_allocated_openprice(openPrice2);

  auto highPrice2 = new ::models::quote::PriceAttribute;
  highPrice2->set_price("-1");
  highPrice2->set_transacttimeus(-1);
  pricestat2.set_allocated_highprice(highPrice2);

  auto lowPrice2 = new ::models::quote::PriceAttribute;
  lowPrice2->set_price("-1");
  lowPrice2->set_transacttimeus(-1);
  pricestat2.set_allocated_lowprice(lowPrice2);

  auto closePrice2 = new ::models::quote::PriceAttribute;
  closePrice2->set_price("-1");
  closePrice2->set_transacttimeus(-1);
  pricestat2.set_allocated_closeprice(closePrice2);

  price2[1] = pricestat2;

  ::models::quote::DepthInfo* buy2 = info1.add_buys();
  buy2->set_price("21000.00");
  buy2->set_quantity("0.300");

  ::models::quote::DepthInfo* sell2 = info1.add_sells();
  sell2->set_price("24000.00");
  sell2->set_quantity("0.003");

  depth_map2[5] = info2;

  produce_msg2.topic_ = "quote2trading.ob.5";
  produce_msg2.pay_load_ = orderbooks2.SerializeAsString();

  handle->OnConsumeCallback(produce_msg2);
  dtssdk::Headers::DestroyHeaders(ptrHeader2);
}

TEST_F(QuoteDataReceiveTest, quate_data_reciever_spot) {
  auto quote_data_synchronizer = te->quote_data_synchronizer();

  // produce 一条option数据
  mock::HdtsMessage produce_msg;
  dtssdk::Headers* ptrHeader = dtssdk::Headers::Create();
  ptrHeader->Add("type", "2");
  ptrHeader->Add("snap", "1");

  produce_msg.headers_.assign(reinterpret_cast<char*>(ptrHeader->Cptr()), ptrHeader->Size());

  ::models::quote::Orderbooks orderbooks;
  orderbooks.set_biztype(::models::quote::Orderbooks::SPOT);
  orderbooks.set_geartype(models::quote::Orderbooks::GEAR25);
  orderbooks.set_crossidx(22009);
  auto& depth_map = *orderbooks.mutable_depths();
  ::models::quote::OrderbookInfo info;
  info.set_symbolid(10);
  info.set_crossseq(791);
  info.set_transacttimeus(1680074868193508);
  auto& price = *info.mutable_price();
  ::models::quote::PriceStat pricestat;
  auto openPrice = new ::models::quote::PriceAttribute;
  openPrice->set_price("1500");
  openPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_openprice(openPrice);

  auto highPrice = new ::models::quote::PriceAttribute;
  highPrice->set_price("1500");
  highPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_highprice(highPrice);

  auto lowPrice = new ::models::quote::PriceAttribute;
  lowPrice->set_price("1500");
  lowPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_lowprice(lowPrice);

  auto closePrice = new ::models::quote::PriceAttribute;
  closePrice->set_price("1500");
  closePrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_closeprice(closePrice);

  price[1] = pricestat;

  ::models::quote::DepthInfo* buy = info.add_buys();
  buy->set_price("23000.00");
  buy->set_quantity("0.300");

  ::models::quote::DepthInfo* sell = info.add_sells();
  sell->set_price("20000.00");
  sell->set_quantity("0.003");

  depth_map[10] = info;

  produce_msg.topic_ = "quote2trading.ob.22009";
  produce_msg.pay_load_ = orderbooks.SerializeAsString();

  auto receiver = quote_data_synchronizer->GetReceiver(22009);
  EXPECT_NE(receiver, nullptr);

  auto handle = receiver->msg_handler();
  EXPECT_NE(handle, nullptr);

  handle->OnConsumeCallback(produce_msg);
  dtssdk::Headers::DestroyHeaders(ptrHeader);

  mock::HdtsMessage produce_msg1;
  dtssdk::Headers* ptrHeader1 = dtssdk::Headers::Create();
  ptrHeader1->Add("type", "2");
  ptrHeader1->Add("snap", "1");

  produce_msg1.headers_.assign(reinterpret_cast<char*>(ptrHeader1->Cptr()), ptrHeader1->Size());

  ::models::quote::Orderbooks orderbooks1;
  orderbooks1.set_biztype(::models::quote::Orderbooks::SPOT);
  orderbooks1.set_geartype(models::quote::Orderbooks::GEAR25);
  orderbooks1.set_crossidx(22009);
  auto& depth_map1 = *orderbooks.mutable_depths();
  ::models::quote::OrderbookInfo info1;
  info1.set_symbolid(10);
  info1.set_crossseq(799);
  info1.set_transacttimeus(1680074968193508);
  auto& price1 = *info1.mutable_price();
  ::models::quote::PriceStat pricestat1;
  auto openPrice1 = new ::models::quote::PriceAttribute;
  openPrice1->set_price("1500");
  openPrice1->set_transacttimeus(1680074968193508);
  pricestat1.set_allocated_openprice(openPrice1);

  auto highPrice1 = new ::models::quote::PriceAttribute;
  highPrice1->set_price("1800");
  highPrice1->set_transacttimeus(1680074968193508);
  pricestat1.set_allocated_highprice(highPrice1);

  auto lowPrice1 = new ::models::quote::PriceAttribute;
  lowPrice1->set_price("1300");
  lowPrice1->set_transacttimeus(1680074968193508);
  pricestat.set_allocated_lowprice(lowPrice1);

  auto closePrice1 = new ::models::quote::PriceAttribute;
  closePrice1->set_price("1500");
  closePrice1->set_transacttimeus(1680074968193508);
  pricestat1.set_allocated_closeprice(closePrice1);

  price1[1] = pricestat1;

  ::models::quote::DepthInfo* buy1 = info1.add_buys();
  buy1->set_price("23000.00");
  buy1->set_quantity("0.300");

  ::models::quote::DepthInfo* buy2 = info1.add_buys();
  buy2->set_price("20000.00");
  buy2->set_quantity("0.003");

  depth_map1[10] = info1;

  produce_msg1.topic_ = "quote2trading.ob.22009";
  produce_msg1.pay_load_ = orderbooks1.SerializeAsString();

  handle->OnConsumeCallback(produce_msg1);
  dtssdk::Headers::DestroyHeaders(ptrHeader1);
}

TEST_F(QuoteDataReceiveTest, quate_data_reciever_spot_gear1) {
  auto quote_data_synchronizer = te->quote_data_synchronizer();

  // produce 一条数据
  mock::HdtsMessage produce_msg;
  dtssdk::Headers* ptrHeader = dtssdk::Headers::Create();
  ptrHeader->Add("type", "1");
  ptrHeader->Add("snap", "1");

  produce_msg.headers_.assign(reinterpret_cast<char*>(ptrHeader->Cptr()), ptrHeader->Size());

  ::models::quote::Orderbooks orderbooks;
  orderbooks.set_biztype(::models::quote::Orderbooks::SPOT);
  orderbooks.set_geartype(models::quote::Orderbooks::GEAR1);
  orderbooks.set_crossidx(22009);
  auto& depth_map = *orderbooks.mutable_depths();
  ::models::quote::OrderbookInfo info;
  info.set_symbolid(10);
  info.set_crossseq(791);
  info.set_transacttimeus(1680074868193508);
  auto& price = *info.mutable_price();
  ::models::quote::PriceStat pricestat;
  auto openPrice = new ::models::quote::PriceAttribute;
  openPrice->set_price("1500");
  openPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_openprice(openPrice);

  auto highPrice = new ::models::quote::PriceAttribute;
  highPrice->set_price("1500");
  highPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_highprice(highPrice);

  auto lowPrice = new ::models::quote::PriceAttribute;
  lowPrice->set_price("1500");
  lowPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_lowprice(lowPrice);

  auto closePrice = new ::models::quote::PriceAttribute;
  closePrice->set_price("1500");
  closePrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_closeprice(closePrice);

  price[1] = pricestat;

  ::models::quote::DepthInfo* buy = info.add_buys();
  buy->set_price("23000.00");
  buy->set_quantity("0.300");

  ::models::quote::DepthInfo* sell = info.add_sells();
  sell->set_price("20000.00");
  sell->set_quantity("0.003");

  depth_map[10] = info;

  produce_msg.topic_ = "quote2trading.ob.22009";
  produce_msg.pay_load_ = orderbooks.SerializeAsString();

  auto receiver = quote_data_synchronizer->GetReceiver(22009);
  EXPECT_NE(receiver, nullptr);

  auto handle = receiver->msg_handler();
  EXPECT_NE(handle, nullptr);

  handle->OnConsumeCallback(produce_msg);
  dtssdk::Headers::DestroyHeaders(ptrHeader);

  mock::HdtsMessage produce_msg1;
  dtssdk::Headers* ptrHeader1 = dtssdk::Headers::Create();
  ptrHeader1->Add("type", "1");
  ptrHeader1->Add("snap", "1");

  produce_msg1.headers_.assign(reinterpret_cast<char*>(ptrHeader1->Cptr()), ptrHeader1->Size());

  ::models::quote::Orderbooks orderbooks1;
  orderbooks1.set_biztype(::models::quote::Orderbooks::SPOT);
  orderbooks1.set_geartype(models::quote::Orderbooks::GEAR1);
  orderbooks1.set_crossidx(22009);
  auto& depth_map1 = *orderbooks.mutable_depths();
  ::models::quote::OrderbookInfo info1;
  info1.set_symbolid(10);
  info1.set_crossseq(799);
  info1.set_transacttimeus(1680074968193508);
  auto& price1 = *info1.mutable_price();
  ::models::quote::PriceStat pricestat1;
  auto openPrice1 = new ::models::quote::PriceAttribute;
  openPrice1->set_price("1500");
  openPrice1->set_transacttimeus(1680074968193508);
  pricestat1.set_allocated_openprice(openPrice1);

  auto highPrice1 = new ::models::quote::PriceAttribute;
  highPrice1->set_price("1800");
  highPrice1->set_transacttimeus(1680074968193508);
  pricestat1.set_allocated_highprice(highPrice1);

  auto lowPrice1 = new ::models::quote::PriceAttribute;
  lowPrice1->set_price("1300");
  lowPrice1->set_transacttimeus(1680074968193508);
  pricestat.set_allocated_lowprice(lowPrice1);

  auto closePrice1 = new ::models::quote::PriceAttribute;
  closePrice1->set_price("1500");
  closePrice1->set_transacttimeus(1680074968193508);
  pricestat1.set_allocated_closeprice(closePrice1);

  price1[1] = pricestat1;

  ::models::quote::DepthInfo* buy1 = info1.add_buys();
  buy1->set_price("23000.00");
  buy1->set_quantity("0.300");

  depth_map1[10] = info1;

  produce_msg1.topic_ = "quote2trading.ob.22009";
  produce_msg1.pay_load_ = orderbooks1.SerializeAsString();

  handle->OnConsumeCallback(produce_msg1);
  dtssdk::Headers::DestroyHeaders(ptrHeader1);
}

TEST_F(QuoteDataReceiveTest, quate_data_reciever_combination_gear1) {
  auto quote_data_synchronizer = te->quote_data_synchronizer();

  // produce 一条数据
  mock::HdtsMessage produce_msg;
  dtssdk::Headers* ptrHeader = dtssdk::Headers::Create();
  ptrHeader->Add("type", "1");
  ptrHeader->Add("snap", "1");

  produce_msg.headers_.assign(reinterpret_cast<char*>(ptrHeader->Cptr()), ptrHeader->Size());

  ::models::quote::Orderbooks orderbooks;
  orderbooks.set_biztype(::models::quote::Orderbooks::FUTURE_SPREAD);
  orderbooks.set_geartype(models::quote::Orderbooks::GEAR1);
  orderbooks.set_crossidx(40000);
  auto& depth_map = *orderbooks.mutable_depths();
  ::models::quote::OrderbookInfo info;
  info.set_symbolid(5000);
  info.set_crossseq(791);
  info.set_transacttimeus(1680074868193508);
  auto& price = *info.mutable_price();
  ::models::quote::PriceStat pricestat;
  auto openPrice = new ::models::quote::PriceAttribute;
  openPrice->set_price("1500");
  openPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_openprice(openPrice);

  auto highPrice = new ::models::quote::PriceAttribute;
  highPrice->set_price("1500");
  highPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_highprice(highPrice);

  auto lowPrice = new ::models::quote::PriceAttribute;
  lowPrice->set_price("1500");
  lowPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_lowprice(lowPrice);

  auto closePrice = new ::models::quote::PriceAttribute;
  closePrice->set_price("1500");
  closePrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_closeprice(closePrice);

  price[1] = pricestat;

  ::models::quote::DepthInfo* buy = info.add_buys();
  buy->set_price("23000.00");
  buy->set_quantity("0.300");

  ::models::quote::DepthInfo* sell = info.add_sells();
  sell->set_price("20000.00");
  sell->set_quantity("0.003");

  depth_map[5000] = info;

  produce_msg.topic_ = "quote2trading.ob.40000";
  produce_msg.pay_load_ = orderbooks.SerializeAsString();

  auto receiver = quote_data_synchronizer->GetReceiver(40000);
  EXPECT_NE(receiver, nullptr);

  auto handle = receiver->msg_handler();
  EXPECT_NE(handle, nullptr);

  handle->OnConsumeCallback(produce_msg);
  dtssdk::Headers::DestroyHeaders(ptrHeader);

  mock::HdtsMessage produce_msg1;
  dtssdk::Headers* ptrHeader1 = dtssdk::Headers::Create();
  ptrHeader1->Add("type", "1");
  ptrHeader1->Add("snap", "1");

  produce_msg1.headers_.assign(reinterpret_cast<char*>(ptrHeader1->Cptr()), ptrHeader1->Size());

  ::models::quote::Orderbooks orderbooks1;
  orderbooks1.set_biztype(::models::quote::Orderbooks::FUTURE_SPREAD);
  orderbooks1.set_geartype(models::quote::Orderbooks::GEAR1);
  orderbooks1.set_crossidx(40000);
  auto& depth_map1 = *orderbooks.mutable_depths();
  ::models::quote::OrderbookInfo info1;
  info1.set_symbolid(5000);
  info1.set_crossseq(799);
  info1.set_transacttimeus(1680074968193508);
  auto& price1 = *info1.mutable_price();
  ::models::quote::PriceStat pricestat1;
  auto openPrice1 = new ::models::quote::PriceAttribute;
  openPrice1->set_price("1500");
  openPrice1->set_transacttimeus(1680074968193508);
  pricestat1.set_allocated_openprice(openPrice1);

  auto highPrice1 = new ::models::quote::PriceAttribute;
  highPrice1->set_price("1800");
  highPrice1->set_transacttimeus(1680074968193508);
  pricestat1.set_allocated_highprice(highPrice1);

  auto lowPrice1 = new ::models::quote::PriceAttribute;
  lowPrice1->set_price("1300");
  lowPrice1->set_transacttimeus(1680074968193508);
  pricestat.set_allocated_lowprice(lowPrice1);

  auto closePrice1 = new ::models::quote::PriceAttribute;
  closePrice1->set_price("1500");
  closePrice1->set_transacttimeus(1680074968193508);
  pricestat1.set_allocated_closeprice(closePrice1);

  price1[1] = pricestat1;

  ::models::quote::DepthInfo* buy1 = info1.add_buys();
  buy1->set_price("23000.00");
  buy1->set_quantity("0.300");

  depth_map1[5000] = info1;

  produce_msg1.topic_ = "quote2trading.ob.40000";
  produce_msg1.pay_load_ = orderbooks1.SerializeAsString();

  handle->OnConsumeCallback(produce_msg1);
  dtssdk::Headers::DestroyHeaders(ptrHeader1);
}

TEST_F(QuoteDataReceiveTest, quate_data_reciever_error) {
  auto quote_data_synchronizer = te->quote_data_synchronizer();

  // 1. 不存在的reciever
  auto receiver = quote_data_synchronizer->GetReceiver(111);
  EXPECT_EQ(receiver, nullptr);

  // 2. hdts消费错误
  receiver = quote_data_synchronizer->GetReceiver(5);
  EXPECT_NE(receiver, nullptr);

  auto handle = receiver->msg_handler();
  EXPECT_NE(handle, nullptr);

  // handle->OnConsumeError(dtssdk::SysEventCode::kConsumeFailed);

  // 3. 数据错误，没有包头
  mock::HdtsMessage produce_msg1;

  ::models::quote::Orderbooks orderbooks1;
  orderbooks1.set_biztype(::models::quote::Orderbooks::SPOT);
  orderbooks1.set_geartype(models::quote::Orderbooks::GEAR25);
  orderbooks1.set_crossidx(22009);

  produce_msg1.topic_ = "quote2trading.ob.22009";
  produce_msg1.pay_load_ = orderbooks1.SerializeAsString();

  handle->OnConsumeCallback(produce_msg1);

  // 4. 数据错误，包头信息不对
  mock::HdtsMessage produce_msg2;
  dtssdk::Headers* ptrHeader2 = dtssdk::Headers::Create();
  ptrHeader2->Add("111", "2");
  ptrHeader2->Add("222", "1");

  produce_msg2.headers_.assign(reinterpret_cast<char*>(ptrHeader2->Cptr()), ptrHeader2->Size());

  ::models::quote::Orderbooks orderbooks2;
  orderbooks2.set_biztype(::models::quote::Orderbooks::SPOT);
  orderbooks2.set_geartype(models::quote::Orderbooks::GEAR25);
  orderbooks2.set_crossidx(22009);

  produce_msg2.topic_ = "quote2trading.ob.22009";
  produce_msg2.pay_load_ = orderbooks2.SerializeAsString();

  handle->OnConsumeCallback(produce_msg2);
  dtssdk::Headers::DestroyHeaders(ptrHeader2);
}

TEST_F(QuoteDataReceiveTest, construct_order_book) {
  ::models::quote::OrderbookInfo info;
  info.set_symbolid(102311);
  info.set_crossseq(791);
  info.set_transacttimeus(1680074868193508);
  auto& price = *info.mutable_price();
  ::models::quote::PriceStat pricestat;
  auto openPrice = new ::models::quote::PriceAttribute;
  openPrice->set_price("1500");
  openPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_openprice(openPrice);

  auto highPrice = new ::models::quote::PriceAttribute;
  highPrice->set_price("1500");
  highPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_highprice(highPrice);

  auto lowPrice = new ::models::quote::PriceAttribute;
  lowPrice->set_price("1500");
  lowPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_lowprice(lowPrice);

  auto closePrice = new ::models::quote::PriceAttribute;
  closePrice->set_price("1500");
  closePrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_closeprice(closePrice);

  price[1] = pricestat;

  ::models::quote::DepthInfo* buy = info.add_buys();
  buy->set_price("23000.00");
  buy->set_quantity("0.300");

  ::models::quote::DepthInfo* sell = info.add_sells();
  sell->set_price("20000.00");
  sell->set_quantity("0.003");

  market_data::OrderBooks new_orderbooks(EProductType::Spot);
  new_orderbooks.Add(102311, info);

  auto orderbooks = new_orderbooks.orderbook_info_map();
  auto iter = orderbooks.find(102311);
  EXPECT_NE(iter, orderbooks.end());

  auto orderBookInfo = iter->second;
  EXPECT_EQ(orderBookInfo->buys.size(), 1);

  ::models::quote::OrderbookInfo info1;
  info1.set_symbolid(102311);
  info1.set_crossseq(799);
  info1.set_transacttimeus(1680074968193508);
  auto& price1 = *info1.mutable_price();
  ::models::quote::PriceStat pricestat1;
  auto openPrice1 = new ::models::quote::PriceAttribute;
  openPrice1->set_price("1500");
  openPrice1->set_transacttimeus(1680074968193508);
  pricestat1.set_allocated_openprice(openPrice1);

  auto highPrice1 = new ::models::quote::PriceAttribute;
  highPrice1->set_price("1800");
  highPrice1->set_transacttimeus(1680074968193508);
  pricestat1.set_allocated_highprice(highPrice1);

  auto lowPrice1 = new ::models::quote::PriceAttribute;
  lowPrice1->set_price("1300");
  lowPrice1->set_transacttimeus(1680074968193508);
  pricestat.set_allocated_lowprice(lowPrice1);

  auto closePrice1 = new ::models::quote::PriceAttribute;
  closePrice1->set_price("1500");
  closePrice1->set_transacttimeus(1680074968193508);
  pricestat1.set_allocated_closeprice(closePrice1);

  price1[1] = pricestat1;

  ::models::quote::DepthInfo* buy1 = info1.add_buys();
  buy1->set_price("23000.00");
  buy1->set_quantity("0.300");

  ::models::quote::DepthInfo* buy2 = info1.add_buys();
  buy2->set_price("20000.00");
  buy2->set_quantity("0.003");

  new_orderbooks.Add(102311, info1);

  orderbooks = new_orderbooks.orderbook_info_map();
  iter = orderbooks.find(102311);
  EXPECT_NE(iter, orderbooks.end());

  orderBookInfo = iter->second;
  EXPECT_EQ(orderBookInfo->buys.size(), 2);
}

TEST_F(QuoteDataReceiveTest, quate_data_consume_error) {
  auto quote_data_synchronizer = te->quote_data_synchronizer();

  // 1. 不存在的reciever
  auto receiver = quote_data_synchronizer->GetReceiver(111);
  EXPECT_EQ(receiver, nullptr);

  // 2. hdts消费错误
  receiver = quote_data_synchronizer->GetReceiver(5);
  EXPECT_NE(receiver, nullptr);

  auto handle = receiver->msg_handler();
  EXPECT_NE(handle, nullptr);

  handle->OnConsumeError(dtssdk::SysEventCode::kConsumeFailed);
}

TEST_F(QuoteDataReceiveTest, spot_ept_consume_error) {
  auto quote_data_synchronizer = te->quote_data_synchronizer();

  // 2. hdts消费错误
  auto receiver = quote_data_synchronizer->GetEtpReceiver();
  EXPECT_NE(receiver, nullptr);

  receiver->OnConsumeError(dtssdk::SysEventCode::kConsumeFailed);

  models::quote::EtpNetWorthResult enwr;
  auto worth = enwr.mutable_symbol_etp_net_worth()->Add();
  worth->set_symbol("5");
  worth->set_nav("2222");

  mock::HdtsMessage produce_msg;
  produce_msg.topic_ = "unified_quote_spot_etp";
  produce_msg.pay_load_ = enwr.SerializeAsString();

  receiver->OnConsumeCallback(produce_msg);
}

TEST_F(QuoteDataReceiveTest, spot_index_mov_avg_consume_error) {
  auto quote_data_synchronizer = te->quote_data_synchronizer();

  // 2. hdts消费错误
  auto receiver = quote_data_synchronizer->GetSpotIndexMovAvgReceiver();
  EXPECT_NE(receiver, nullptr);

  receiver->OnConsumeError(dtssdk::SysEventCode::kConsumeFailed);

  models::quote::SymbolIndexMovAvg index_mov_avg;
  // auto worth = premium.mutable_symbol_etp_net_worth()->Add();
  // worth->set_symbol("5");
  // worth->set_nav("2222");

  // mock::HdtsMessage produce_msg;
  // produce_msg.topic_ = "unified_quote_spot_etp";
  // produce_msg.pay_load_ = enwr.SerializeAsString();

  // receiver->OnConsumeCallback(produce_msg);
}

std::shared_ptr<tmock::CTradeAppMock> QuoteDataReceiveTest::te;
