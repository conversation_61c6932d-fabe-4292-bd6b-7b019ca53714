#include "test/biz/trade/option/order/set_trading_base_coin.hpp"

#include <list>
#include <string>
#include <unordered_map>

#include "biz_worker/service/trade/store/per_user_store.hpp"
#include "biz_worker/service/trade/store/per_worker_store.hpp"
#include "biz_worker/service/trade/store/resource.hpp"
#include "lib/msg_builder.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(OptionSetTradingBaseCoinTest, set_trading_base_coin) {
  stub maker(te, 10000);
  stub taker(te, 10001);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10000, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = maker.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10001, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = taker.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  std::string category = "option";
  std::string symbol = "BTC-31DEC21-40000-C";
  int position_idx = 0;
  std::string side = "Buy";
  std::string order_type = "Limit";
  std::string qty = "1";
  std::string price = "200";
  std::string base_coin = "BTC";

  // 创建user1 Buy开仓订单 1qty
  CreateOrderBuilder create_ao_builder(category, symbol, position_idx, side, order_type, "200", price);
  auto order_link_id = te->GenUUID();
  create_ao_builder.SetOrderLinkID(order_link_id);
  create_ao_builder.SetMmp(true);
  // 检查user1下单rpc调用结果
  auto resp1 = maker.create_order(create_ao_builder.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  // 检查user1下单撮合处理结果
  auto maker_result = te->PopResult();
  ASSERT_NE(maker_result.m_msg.get(), nullptr);

  // 有挂单的场景，返回成功
  svc::uta_engine::option::InnerSetUserTradingBaseCoinRequest request;
  request.set_isactive(true);
  request.add_basecoinlist("");

  auto resp = maker.OptionInnerSetUserTradingBaseCoin(request);
  ASSERT_EQ(resp->retcode(), std::to_string(error::kErrorCodeSuccess));
}

std::shared_ptr<tmock::CTradeAppMock> OptionSetTradingBaseCoinTest::te;
