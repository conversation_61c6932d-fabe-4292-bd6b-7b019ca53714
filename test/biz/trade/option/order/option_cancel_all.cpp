#include "test/biz/trade/option/order/option_cancel_all.hpp"

#include <string>

#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(OptionCancelAllTest, cancel_all) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  std::string category = "option";
  std::string symbol = "BTC-31DEC21-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "1";
  std::string price = "2000";

  CreateOrderBuilder create_ao_builder(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_1 = te->GenUUID();
  create_ao_builder.SetOrderLinkID(order_link_id_1);
  auto resp11 = user1.create_order(create_ao_builder.Build());
  ASSERT_EQ(resp11->order_link_id(), order_link_id_1);

  auto result = te->PopResult();

  // cancel by baseCoin
  // 请求ID
  std::string out_request_id;
  // 非必填：资产币种
  // 优先级顺序：优先看symbol，再看baseCoin
  std::string base_coin = "BTC";
  svc::uta_engine::option::SiteCancelAllRequest siteCancelAllRequest;
  siteCancelAllRequest.set_outrequestid(te->GenUUID());
  siteCancelAllRequest.set_basecoin(base_coin);
  auto resp1 = user1.option_site_cancel_all(siteCancelAllRequest);
  ASSERT_EQ(resp1->retcode(), 0);

  auto result2 = te->PopResult(50000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
}

std::shared_ptr<tmock::CTradeAppMock> OptionCancelAllTest::te;
