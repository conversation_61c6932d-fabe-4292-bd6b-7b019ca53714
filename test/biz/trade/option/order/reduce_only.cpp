#include "test/biz/trade/option/order/reduce_only.hpp"

#include <string>

#include "lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

/*
测试场景：1. 用户有1手sell方向持仓
        2. 用户挂1手buy方向非RO订单
        3. 用户挂1手buy方向RO订单，价格比非RO订单差
预期结果：RO订单挂单失败
*/
TEST_F(OptionReduceOnlyTest, reduce_only_0) {
  stub user1(te, 10000);
  stub user2(te, 10001);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10000, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10001, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user2.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  std::string category = "option";
  std::string symbol = "BTC-31DEC21-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "1";
  std::string price = "200";

  CreateOrderBuilder create_ao_builder(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id = te->GenUUID();
  create_ao_builder.SetOrderLinkID(order_link_id);
  auto resp1 = user1.create_order(create_ao_builder.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
  ASSERT_NE(orderCheck.m_msg, nullptr);

  side = "Buy";
  CreateOrderBuilder create_ao_builder2(category, symbol, position_idx, side, order_type, qty, price);
  auto resp2 = user2.create_order(create_ao_builder2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto result2 = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 挂非RO订单占满closing box
  CreateOrderBuilder create_ao_builder3(category, symbol, position_idx, side, order_type, qty, price);
  order_link_id = te->GenUUID();
  create_ao_builder3.SetOrderLinkID(order_link_id);
  auto resp3 = user1.create_order(create_ao_builder3.Build());
  ASSERT_EQ(resp3->order_link_id(), order_link_id);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(10000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(20000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());

  // 挂RO订单，挂单失败
  price = "100";
  CreateOrderBuilder create_ao_builder4(category, symbol, position_idx, side, order_type, qty, price);
  order_link_id = te->GenUUID();
  create_ao_builder4.SetOrderLinkID(order_link_id);
  create_ao_builder4.SetReduceOnly();
  auto resp4 = user1.create_order(create_ao_builder4.Build());
  ASSERT_EQ(resp4->order_link_id(), "");
  // todo 校验原因值
}

/*
测试场景：1. 用户有2手sell方向持仓
        2. 用户挂2手buy方向RO订单
        3. 用户挂1手buy方向非RO订单，价格比RO订单优
预期结果：RO订单数量被修改，非RO订单挂单成功
*/
TEST_F(OptionReduceOnlyTest, reduce_only_1) {
  stub user1(te, 10000);
  stub user2(te, 10001);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10000, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10001, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user2.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  std::string category = "option";
  std::string symbol = "BTC-31DEC21-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "2";
  std::string price = "200";

  CreateOrderBuilder create_ao_builder(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id = te->GenUUID();
  create_ao_builder.SetOrderLinkID(order_link_id);
  auto resp1 = user1.create_order(create_ao_builder.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
  ASSERT_NE(orderCheck.m_msg, nullptr);

  side = "Buy";
  CreateOrderBuilder create_ao_builder2(category, symbol, position_idx, side, order_type, qty, price);
  auto resp2 = user2.create_order(create_ao_builder2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto result2 = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 挂RO订单，占满closing box
  qty = "2";
  CreateOrderBuilder create_ao_builder3(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_ro = te->GenUUID();
  create_ao_builder3.SetOrderLinkID(order_link_id_ro);
  create_ao_builder3.SetReduceOnly();
  auto resp3 = user1.create_order(create_ao_builder3.Build());
  ASSERT_EQ(resp3->order_link_id(), order_link_id_ro);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(20000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(20000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());

  // 挂非RO订单，价格更优
  price = "300";
  qty = "1";
  CreateOrderBuilder create_ao_builder4(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_not_ro = te->GenUUID();
  create_ao_builder4.SetOrderLinkID(order_link_id_not_ro);
  auto resp4 = user1.create_order(create_ao_builder4.Build());

  // RO订单数量被裁减
  ASSERT_EQ(resp4->order_link_id(), order_link_id_not_ro);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(10000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(20000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::Replaced, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());

  // 非RO订单挂单成功
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_not_ro);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(10000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(30000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());
}

/*
测试场景：1. 用户有2手sell方向持仓
        2. 用户挂2手buy方向RO订单
        3. 用户挂2手buy方向非RO订单，价格比RO订单优
预期结果：RO订单被取消，非RO订单挂单成功
*/
TEST_F(OptionReduceOnlyTest, reduce_only_2) {
  stub user1(te, 10000);
  stub user2(te, 10001);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10000, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10001, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user2.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  std::string category = "option";
  std::string symbol = "BTC-31DEC21-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "2";
  std::string price = "200";

  CreateOrderBuilder create_ao_builder(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id = te->GenUUID();
  create_ao_builder.SetOrderLinkID(order_link_id);
  auto resp1 = user1.create_order(create_ao_builder.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
  ASSERT_NE(orderCheck.m_msg, nullptr);

  side = "Buy";
  CreateOrderBuilder create_ao_builder2(category, symbol, position_idx, side, order_type, qty, price);
  auto resp2 = user2.create_order(create_ao_builder2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto result2 = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 挂RO订单，占满closing box
  qty = "2";
  CreateOrderBuilder create_ao_builder3(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_ro = te->GenUUID();
  create_ao_builder3.SetOrderLinkID(order_link_id_ro);
  create_ao_builder3.SetReduceOnly();
  auto resp3 = user1.create_order(create_ao_builder3.Build());
  ASSERT_EQ(resp3->order_link_id(), order_link_id_ro);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(20000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(20000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());

  // 挂非RO订单，价格更优，也占满closing box
  price = "300";
  qty = "2";
  CreateOrderBuilder create_ao_builder4(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_not_ro = te->GenUUID();
  create_ao_builder4.SetOrderLinkID(order_link_id_not_ro);
  auto resp4 = user1.create_order(create_ao_builder4.Build());

  // RO订单被取消
  ASSERT_EQ(resp4->order_link_id(), order_link_id_not_ro);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(20000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(20000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::Canceled, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::Cancelled, orderCheck.m_msg->order_status());

  // 非RO订单挂单成功
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_not_ro);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(20000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(30000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());
}

/*
测试场景：1. 用户有2手sell方向持仓
        2. 用户挂2手buy方向RO订单
        3. 用户挂2手buy方向非RO订单，价格比RO订单差
预期结果：非RO订单挂单成功
*/
TEST_F(OptionReduceOnlyTest, reduce_only_3) {
  stub user1(te, 10000);
  stub user2(te, 10001);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10000, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10001, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user2.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  std::string category = "option";
  std::string symbol = "BTC-31DEC21-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "2";
  std::string price = "200";

  CreateOrderBuilder create_ao_builder(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id = te->GenUUID();
  create_ao_builder.SetOrderLinkID(order_link_id);
  auto resp1 = user1.create_order(create_ao_builder.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
  ASSERT_NE(orderCheck.m_msg, nullptr);

  side = "Buy";
  CreateOrderBuilder create_ao_builder2(category, symbol, position_idx, side, order_type, qty, price);
  auto resp2 = user2.create_order(create_ao_builder2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto result2 = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 挂RO订单，占满closing box
  qty = "2";
  CreateOrderBuilder create_ao_builder3(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_ro = te->GenUUID();
  create_ao_builder3.SetOrderLinkID(order_link_id_ro);
  create_ao_builder3.SetReduceOnly();
  auto resp3 = user1.create_order(create_ao_builder3.Build());
  ASSERT_EQ(resp3->order_link_id(), order_link_id_ro);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(20000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(20000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());

  // 挂非RO订单，价格更差
  price = "100";
  qty = "1";
  CreateOrderBuilder create_ao_builder4(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_not_ro = te->GenUUID();
  create_ao_builder4.SetOrderLinkID(order_link_id_not_ro);
  auto resp4 = user1.create_order(create_ao_builder4.Build());
  ASSERT_EQ(resp4->order_link_id(), order_link_id_not_ro);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_not_ro);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(10000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(10000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());
}

/*
测试场景：1. 用户有1手sell方向持仓
        2. 用户挂1手buy方向RO订单
        3. 用户挂1手buy方向RO订单，价格比第一次的RO订单差
预期结果：第二次RO订单挂单失败
*/
TEST_F(OptionReduceOnlyTest, reduce_only_4) {
  stub user1(te, 10000);
  stub user2(te, 10001);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10000, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10001, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user2.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  std::string category = "option";
  std::string symbol = "BTC-31DEC21-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "1";
  std::string price = "200";

  CreateOrderBuilder create_ao_builder(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id = te->GenUUID();
  create_ao_builder.SetOrderLinkID(order_link_id);
  auto resp1 = user1.create_order(create_ao_builder.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
  ASSERT_NE(orderCheck.m_msg, nullptr);

  side = "Buy";
  CreateOrderBuilder create_ao_builder2(category, symbol, position_idx, side, order_type, qty, price);
  auto resp2 = user2.create_order(create_ao_builder2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto result2 = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 第一次挂RO订单，占满closing box
  CreateOrderBuilder create_ao_builder3(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_ro_first = te->GenUUID();
  create_ao_builder3.SetOrderLinkID(order_link_id_ro_first);
  create_ao_builder3.SetReduceOnly();
  auto resp3 = user1.create_order(create_ao_builder3.Build());
  ASSERT_EQ(resp3->order_link_id(), order_link_id_ro_first);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro_first);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(10000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(20000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());

  // 第二次挂RO订单，价格比第一次更差
  price = "100";
  CreateOrderBuilder create_ao_builder4(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_ro_second = te->GenUUID();
  create_ao_builder4.SetOrderLinkID(order_link_id_ro_second);
  create_ao_builder4.SetReduceOnly();
  auto resp4 = user1.create_order(create_ao_builder4.Build());
  ASSERT_EQ(resp4->order_link_id(), "");
  // todo 校验原因值
}

/*
测试场景：1. 用户有2手sell方向持仓
        2. 用户挂1手buy方向RO订单
        3. 用户挂2手buy方向RO订单，价格比第一次的RO订单差
预期结果：第二次RO订单数量被裁减
*/
TEST_F(OptionReduceOnlyTest, reduce_only_5) {
  stub user1(te, 10000);
  stub user2(te, 10001);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10000, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10001, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user2.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  std::string category = "option";
  std::string symbol = "BTC-31DEC21-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "2";
  std::string price = "200";

  CreateOrderBuilder create_ao_builder(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id = te->GenUUID();
  create_ao_builder.SetOrderLinkID(order_link_id);
  auto resp1 = user1.create_order(create_ao_builder.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
  ASSERT_NE(orderCheck.m_msg, nullptr);

  side = "Buy";
  CreateOrderBuilder create_ao_builder2(category, symbol, position_idx, side, order_type, qty, price);
  auto resp2 = user2.create_order(create_ao_builder2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto result2 = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 第一次挂RO订单
  qty = "1";
  CreateOrderBuilder create_ao_builder3(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_ro_first = te->GenUUID();
  create_ao_builder3.SetOrderLinkID(order_link_id_ro_first);
  create_ao_builder3.SetReduceOnly();
  auto resp3 = user1.create_order(create_ao_builder3.Build());
  ASSERT_EQ(resp3->order_link_id(), order_link_id_ro_first);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro_first);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(10000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(20000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());

  // 第二次挂RO订单，价格比第一次更差，超出closing box，数量被裁减
  price = "100";
  qty = "2";
  CreateOrderBuilder create_ao_builder4(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_ro_second = te->GenUUID();
  create_ao_builder4.SetOrderLinkID(order_link_id_ro_second);
  create_ao_builder4.SetReduceOnly();
  auto resp4 = user1.create_order(create_ao_builder4.Build());
  ASSERT_EQ(resp4->order_link_id(), order_link_id_ro_second);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro_second);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(10000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(10000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());
}

/*
测试场景：1. 用户有3手sell方向持仓
        2. 用户挂2手buy方向RO订单
        3. 用户挂2手buy方向RO订单，价格比第一次的RO订单优
预期结果：第一次RO订单数量被裁减
*/
TEST_F(OptionReduceOnlyTest, reduce_only_6) {
  stub user1(te, 10000);
  stub user2(te, 10001);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10000, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10001, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user2.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  std::string category = "option";
  std::string symbol = "BTC-31DEC21-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "3";
  std::string price = "200";

  CreateOrderBuilder create_ao_builder(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id = te->GenUUID();
  create_ao_builder.SetOrderLinkID(order_link_id);
  auto resp1 = user1.create_order(create_ao_builder.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
  ASSERT_NE(orderCheck.m_msg, nullptr);

  side = "Buy";
  CreateOrderBuilder create_ao_builder2(category, symbol, position_idx, side, order_type, qty, price);
  auto resp2 = user2.create_order(create_ao_builder2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto result2 = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 第一次挂RO订单，占满closing box
  qty = "2";
  CreateOrderBuilder create_ao_builder3(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_ro_first = te->GenUUID();
  create_ao_builder3.SetOrderLinkID(order_link_id_ro_first);
  create_ao_builder3.SetReduceOnly();
  auto resp3 = user1.create_order(create_ao_builder3.Build());
  ASSERT_EQ(resp3->order_link_id(), order_link_id_ro_first);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro_first);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(20000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(20000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());

  // 第二次挂RO订单，价格比第一次更优
  price = "300";
  qty = "2";
  CreateOrderBuilder create_ao_builder4(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_ro_second = te->GenUUID();
  create_ao_builder4.SetOrderLinkID(order_link_id_ro_second);
  create_ao_builder4.SetReduceOnly();
  auto resp4 = user1.create_order(create_ao_builder4.Build());

  // 第一次RO订单被裁减
  ASSERT_EQ(resp4->order_link_id(), order_link_id_ro_second);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro_first);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(10000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(20000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::Replaced, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());

  // 第二次RO订单挂单成功
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro_second);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(20000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(30000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());
}

/*
测试场景：1. 用户有3手sell方向持仓
        2. 用户挂2手buy方向RO订单
        3. 用户挂3手buy方向RO订单，价格比第一次的RO订单优
预期结果：第一次RO订单被取消
*/
TEST_F(OptionReduceOnlyTest, reduce_only_7) {
  stub user1(te, 10000);
  stub user2(te, 10001);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10000, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10001, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user2.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  std::string category = "option";
  std::string symbol = "BTC-31DEC21-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "3";
  std::string price = "200";

  CreateOrderBuilder create_ao_builder(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id = te->GenUUID();
  create_ao_builder.SetOrderLinkID(order_link_id);
  auto resp1 = user1.create_order(create_ao_builder.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
  ASSERT_NE(orderCheck.m_msg, nullptr);

  side = "Buy";
  CreateOrderBuilder create_ao_builder2(category, symbol, position_idx, side, order_type, qty, price);
  auto resp2 = user2.create_order(create_ao_builder2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto result2 = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 第一次挂RO订单
  qty = "2";
  CreateOrderBuilder create_ao_builder3(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_ro_first = te->GenUUID();
  create_ao_builder3.SetOrderLinkID(order_link_id_ro_first);
  create_ao_builder3.SetReduceOnly();
  auto resp3 = user1.create_order(create_ao_builder3.Build());
  ASSERT_EQ(resp3->order_link_id(), order_link_id_ro_first);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro_first);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(20000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(20000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());

  // 第二次挂RO订单，价格更优，且占满closing box
  price = "300";
  qty = "3";
  CreateOrderBuilder create_ao_builder4(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_ro_second = te->GenUUID();
  create_ao_builder4.SetOrderLinkID(order_link_id_ro_second);
  create_ao_builder4.SetReduceOnly();
  auto resp4 = user1.create_order(create_ao_builder4.Build());

  // 取消第一次RO订单
  ASSERT_EQ(resp4->order_link_id(), order_link_id_ro_second);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro_first);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  //  EXPECT_EQ(0, orderCheck.m_msg->leaves_qty_x());
  EXPECT_EQ(20000, orderCheck.m_msg->exec_qty_x());
  EXPECT_EQ(ECrossStatus::Canceled, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::Cancelled, orderCheck.m_msg->order_status());

  // 第二次RO订单挂单成功
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro_second);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(30000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(30000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());
}

/*
测试场景：1. 用户有3手buy方向持仓
        2. 用户挂2手sell方向RO订单
        3. 用户挂4手sell方向RO订单，价格比第一次的RO订单优
预期结果：第一次RO订单被取消，第二次RO订单数量被裁减
*/
TEST_F(OptionReduceOnlyTest, reduce_only_8) {
  stub user1(te, 10000);
  stub user2(te, 10001);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10000, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, 10001, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user2.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  std::string category = "option";
  std::string symbol = "BTC-31DEC21-40000-C";
  int position_idx = 0;
  std::string side = "Buy";
  std::string order_type = "Limit";
  std::string qty = "3";
  std::string price = "200";

  CreateOrderBuilder create_ao_builder(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id = te->GenUUID();
  create_ao_builder.SetOrderLinkID(order_link_id);
  auto resp1 = user1.create_order(create_ao_builder.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
  ASSERT_NE(orderCheck.m_msg, nullptr);

  side = "Sell";
  CreateOrderBuilder create_ao_builder2(category, symbol, position_idx, side, order_type, qty, price);
  auto resp2 = user2.create_order(create_ao_builder2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto result2 = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 第一次挂RO订单
  qty = "2";
  CreateOrderBuilder create_ao_builder3(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_ro_first = te->GenUUID();
  create_ao_builder3.SetOrderLinkID(order_link_id_ro_first);
  create_ao_builder3.SetReduceOnly();
  auto resp3 = user1.create_order(create_ao_builder3.Build());
  ASSERT_EQ(resp3->order_link_id(), order_link_id_ro_first);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro_first);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(20000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(20000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());

  // 第二次挂RO订单，价格更优，且占满closing box
  price = "100";
  qty = "4";
  CreateOrderBuilder create_ao_builder4(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_ro_second = te->GenUUID();
  create_ao_builder4.SetOrderLinkID(order_link_id_ro_second);
  create_ao_builder4.SetReduceOnly();
  auto resp4 = user1.create_order(create_ao_builder4.Build());

  // 取消第一次RO订单
  ASSERT_EQ(resp4->order_link_id(), order_link_id_ro_second);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro_first);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  //  EXPECT_EQ(0, orderCheck.m_msg->leaves_qty_x());
  EXPECT_EQ(20000, orderCheck.m_msg->exec_qty_x());
  EXPECT_EQ(ECrossStatus::Canceled, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::Cancelled, orderCheck.m_msg->order_status());

  // 第二次RO订单挂单成功
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_ro_second);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  EXPECT_EQ(30000, orderCheck.m_msg->qty_x());
  EXPECT_EQ(10000000000, orderCheck.m_msg->price_x());
  EXPECT_EQ(ECrossStatus::NewAccepted, orderCheck.m_msg->cross_status());
  EXPECT_EQ(EOrderStatus::New, orderCheck.m_msg->order_status());
}

std::shared_ptr<tmock::CTradeAppMock> OptionReduceOnlyTest::te;
