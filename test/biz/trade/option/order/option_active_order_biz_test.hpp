#ifndef TEST_BIZ_TRADE_OPTION_ORDER_OPTION_ACTIVE_ORDER_BIZ_TEST_HPP_
#define TEST_BIZ_TRADE_OPTION_ORDER_OPTION_ACTIVE_ORDER_BIZ_TEST_HPP_

#include <memory>
#include <unordered_map>

#include "src/biz_worker/service/trade/store/per_coin_store.hpp"
#include "src/biz_worker/service/trade/store/per_symbol_store.hpp"
#include "src/biz_worker/service/trade/store/per_user_store.hpp"
#include "src/biz_worker/service/trade/store/per_worker_store.hpp"
#include "src/biz_worker/service/trade/store/position/cow_position.hpp"
#include "src/biz_worker/service/trade/store/user_data_manager.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class OptionActiveOrderBizTest : public testing::Test {
 public:
  void SetUp() override { InitUserData(); }
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }

  void InitUserData() {
    // 初始化per_worker_store的数据
    per_worker_store_ = new store::PerWorkerStore();
    auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
    auto option_symbol_service = std::make_shared<config::OptionSymbolService>();
    auto symbol_full_data = std::make_shared<config::SymbolFullData>();
    auto symbol_dto = std::make_shared<config::SymbolDTO>();
    symbol_dto->settle_coin = "USDC";
    symbol_dto->base_coin = "BTC";
    symbol_dto->quote_coin = "USD";
    symbol_dto->symbol_name = "BTC-31DEC21-40000-C";
    symbol_dto->id = 10099;
    symbol_dto->status = "ONLINE";
    symbol_dto->delivery_time = ************;
    symbol_full_data->symbol_name_map.emplace("BTC-31DEC21-40000-C", symbol_dto);
    auto symbol2 = std::make_shared<config::SymbolDTO>();
    symbol2->settle_coin = "USDC";
    symbol2->base_coin = "SOL";
    symbol2->quote_coin = "USD";
    symbol2->symbol_name = "BTC-31DEC21-40000-P";
    symbol2->id = 10199;
    symbol2->status = "ONLINE";
    symbol2->delivery_time = ************;
    symbol_full_data->symbol_name_map.emplace("BTC-31DEC21-40000-P", symbol2);
    auto expire_symbol = std::make_shared<config::SymbolDTO>();
    expire_symbol->settle_coin = "USDC";
    expire_symbol->base_coin = "BTC";
    expire_symbol->quote_coin = "USD";
    expire_symbol->symbol_name = "BTC-01JAN19-40000-C";
    expire_symbol->status = "DELIVERING";
    symbol_full_data->symbol_name_map.emplace("BTC-01JAN19-40000-C", expire_symbol);
    auto offline_symbol = std::make_shared<config::SymbolDTO>();
    offline_symbol->settle_coin = "USDC";
    offline_symbol->base_coin = "BTC";
    offline_symbol->quote_coin = "USD";
    offline_symbol->symbol_name = "BTC-02JAN19-40000-C";
    offline_symbol->status = "OFFLINE";
    symbol_full_data->symbol_name_map.emplace("BTC-02JAN19-40000-C", offline_symbol);
    auto delivery_symbol = std::make_shared<config::SymbolDTO>();
    delivery_symbol->settle_coin = "USDC";
    delivery_symbol->base_coin = "BTC";
    delivery_symbol->quote_coin = "USD";
    delivery_symbol->symbol_name = "BTC-03JAN19-40000-C";
    delivery_symbol->status = "ONLINE";
    delivery_symbol->delivery_time = 0;
    symbol_full_data->symbol_name_map.emplace("BTC-03JAN19-40000-C", delivery_symbol);
    auto uninitialized_symbol = std::make_shared<config::SymbolDTO>();
    uninitialized_symbol->settle_coin = "USDC";
    uninitialized_symbol->base_coin = "BTC";
    uninitialized_symbol->quote_coin = "USD";
    uninitialized_symbol->symbol_name = "BTC-04JAN19-40000-C";
    uninitialized_symbol->status = "ONLINE";
    uninitialized_symbol->delivery_time = ************;
    uninitialized_symbol->online_time = ************;
    symbol_full_data->symbol_name_map.emplace("BTC-04JAN19-40000-C", uninitialized_symbol);
    option_symbol_service->set_symbol_full_data(symbol_full_data);

    auto inst_client = std::make_shared<config::InstClient>();
    auto acc_limit_data = std::make_shared<config::AcctLimitData>();
    //    auto uid_map = std::unordered_map<std::string, std::string>();
    //    uid_map[10000] = "white_rule_key";
    //    auto coin_rule_map = std::unordered_map<std::string, std::unordered_map<std::string, std::string>>();
    //    coin_rule_map["white_rule_key"]["maxNumberOfContractsHeldForOneInstrument"] = "1.0";
    acc_limit_data->user_data["BTC"]["10000"] = "white_rule_key";
    acc_limit_data->rule_data["BTC"]["white_rule_key"]["maxNumberOfContractsHeldForOneInstrument"] = "1.0";
    acc_limit_data->rule_data["BTC"]["white_rule_key"]["maxOpenOptionOrders"] = "1";
    inst_client->set_acct_limit_data(acc_limit_data);

    auto common_config_svc = std::make_shared<config::CommonConfigService>();
    auto option_trade_data = std::make_shared<config::CommonConfigData>();
    option_trade_data->data["BTC"]["maxBuyFactor"] = "1.0";
    option_trade_data->data["BTC"]["tickSize"] = "1.0";
    common_config_svc->set_option_trade_data(option_trade_data);

    new_config_mgr->set_option_symbol_svc(option_symbol_service);
    new_config_mgr->set_inst_client(inst_client);
    new_config_mgr->set_common_config_svc(common_config_svc);
    config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

    auto index_price_map =
        std::make_shared<std::unordered_map<biz::str16_t, biz::price_d_t, biz::str16_t::HashFunctor>>();
    index_price_map->emplace("BTC-USD", biz::BigDecimal("1.0"));
    index_price_map->emplace("SOL-USD", biz::BigDecimal("1.0"));
    per_worker_store_->SyncOptionIndexPrice(index_price_map);
    auto mark_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
    mark_price_map->emplace(10099, biz::BigDecimal("1.0"));
    mark_price_map->emplace(10199, biz::BigDecimal("1.0"));
    per_worker_store_->SyncOptionMarkPrice(mark_price_map);
    auto option_delta_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
    option_delta_map->emplace(10099, biz::BigDecimal("1.0"));
    option_delta_map->emplace(10199, biz::BigDecimal("1.0"));
    per_worker_store_->SyncOptionDelta(option_delta_map);
    auto option_underlying_price_map =
        std::make_shared<std::unordered_map<biz::str64_t, biz::price_d_t, biz::str64_t::HashFunctor>>();
    option_underlying_price_map->emplace("BTC-USD-************", biz::BigDecimal("1.0"));
    option_underlying_price_map->emplace("SOL-USD-************", biz::BigDecimal("1.0"));
    per_worker_store_->SyncOptionUnderlyingPrice(option_underlying_price_map);
    auto option_vega_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
    option_vega_map->emplace(10099, biz::BigDecimal("1.0"));
    option_vega_map->emplace(10199, biz::BigDecimal("1.0"));
    per_worker_store_->SyncOptionVega(option_vega_map);

    // 初始化user_data_manager的数据
    user_data_manager_ = new store::UserDataManager();
    user_data_manager_->CreateUserData(user_id);

    // 初始化per_user_store_的数据
    auto per_symbol_store = std::make_shared<store::PerSymbolStore>(10099, EPositionMode::MergedSingle);
    auto position = std::make_shared<store::Position>();
    auto& raw_position = *position;
    raw_position.side = ESide::Buy;
    raw_position.size = 10;

    auto cow_position = std::make_shared<store::CowPosition>(position, EProductType::Options);
    auto order = std::make_shared<store::Order>();
    order->order_status = EOrderStatus::New;
    order->side = ESide::Sell;
    order->qty = 20;
    order->order_id = "order_id";
    auto cow_order = std::make_shared<store::CowOrder>();
    cow_order->cur_order = order;
    per_symbol_store->all_positions_[EPositionIndex::Single] = cow_position;
    per_symbol_store->all_option_active_orders_.emplace("order_id", cow_order.get());
    per_user_store_ = user_data_manager_->GetUserData(user_id);
    per_user_store_->working_coins_[ECoin::USDC] = std::make_shared<store::PerCoinStore>(user_id, ECoin::USDC);
    per_user_store_->working_coins_[ECoin::USDC]->working_option_symbols_[10099] = per_symbol_store;
    per_user_store_->all_option_working_order_map_by_order_id_.emplace("order_id", cow_order.get());
    per_user_store_->all_option_recent_order_map_by_order_link_id_.emplace("order_link_id", cow_order.get());
    auto setting = const_cast<store::Setting*>(per_user_store_->user_setting_->Latest());
    setting->option_user_setting.is_base_coin_white_list_active = true;
    setting->option_user_setting.base_coin_white_list.push_back("BTC");

    // 初始化header对象
    header->coin = ECoin::USDC;
    header->uid = user_id;
  }

  std::shared_ptr<biz::DraftPkg> GetDraftPkg() {
    return std::make_shared<biz::DraftPkg>(nullptr, header, per_worker_store_, per_user_store_);
  }

  static std::shared_ptr<tmock::CTradeAppMock> te;

 protected:
  biz::user_id_t user_id = 10000;
  store::UserDataManager* user_data_manager_{nullptr};
  store::PerWorkerStore* per_worker_store_{nullptr};
  store::PerUserStore* per_user_store_{nullptr};
  std::shared_ptr<store::Header> header = std::make_shared<store::Header>();
};

#endif  // TEST_BIZ_TRADE_OPTION_ORDER_OPTION_ACTIVE_ORDER_BIZ_TEST_HPP_
