#include "test/biz/trade/option/order/option_order_validation_biz_test.hpp"

#include <string>

#include "src/biz_worker/service/trade/options/modules/orderbiz/ordervalidationbiz/option_order_validation_biz.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(OptionOrderValidationBizTest, strategy_trading_check) {
  // 验证请求中order type无效的情况
  auto site_req = svc::uta_engine::option::SiteOrderRequest();
  auto pkg = GetDraftPkg();
  site_req.set_create_type("CreateByStrategy");
  site_req.set_ordertype(EOrderType::Limit);
  biz::optionorderbiz::OrderValidationBiz::StrategyTradingCheck(pkg.get(), site_req);
  ASSERT_EQ(pkg->GetLastError().error_code_, error::ErrorCode::kEcParameterErrorOrderType);

  // 验证请求中time in force参数无效的情况
  site_req = svc::uta_engine::option::SiteOrderRequest();
  site_req.set_create_type("CreateByStrategy");
  site_req.set_ordertype(EOrderType::Market);
  site_req.set_timeinforce(ETimeInForce::FillOrKill);
  pkg = GetDraftPkg();
  biz::optionorderbiz::OrderValidationBiz::StrategyTradingCheck(pkg.get(), site_req);
  ASSERT_EQ(pkg->GetLastError().error_code_, error::ErrorCode::kEcParameterErrorTimeInForce);

  // 验证请求中order id为空的情况
  site_req = svc::uta_engine::option::SiteOrderRequest();
  site_req.set_create_type("CreateByStrategy");
  site_req.set_ordertype(EOrderType::Market);
  site_req.set_timeinforce(ETimeInForce::ImmediateOrCancel);
  pkg = GetDraftPkg();
  biz::optionorderbiz::OrderValidationBiz::StrategyTradingCheck(pkg.get(), site_req);
  ASSERT_EQ(pkg->GetLastError().error_code_, error::ErrorCode::kEcParameterError);

  // 验证请求中symbol_name不一致的情况
  site_req = svc::uta_engine::option::SiteOrderRequest();
  site_req.set_create_type("CreateByStrategy");
  site_req.set_ordertype(EOrderType::Market);
  site_req.set_timeinforce(ETimeInForce::ImmediateOrCancel);
  site_req.set_order_id("order_id");
  site_req.set_symbol("invalid_name");
  pkg = GetDraftPkg();
  biz::optionorderbiz::OrderValidationBiz::StrategyTradingCheck(pkg.get(), site_req);
  ASSERT_EQ(pkg->GetLastError().error_code_, error::ErrorCode::kEcParameterErrorSymbolNotExists);

  // 验证请求中仓位方向不一致的情况
  site_req = svc::uta_engine::option::SiteOrderRequest();
  site_req.set_create_type("CreateByStrategy");
  site_req.set_ordertype(EOrderType::Market);
  site_req.set_timeinforce(ETimeInForce::ImmediateOrCancel);
  site_req.set_order_id("order_id");
  site_req.set_symbol("BTC-31DEC21-40000-C");
  site_req.set_side(ESide::Sell);
  pkg = GetDraftPkg();
  biz::optionorderbiz::OrderValidationBiz::StrategyTradingCheck(pkg.get(), site_req);
  ASSERT_EQ(pkg->GetLastError().error_code_, error::ErrorCode::kEcInvalidStrategy);

  // 验证请求中订单方向不一致的情况
  site_req = svc::uta_engine::option::SiteOrderRequest();
  site_req.set_create_type("CreateByStrategy");
  site_req.set_ordertype(EOrderType::Market);
  site_req.set_timeinforce(ETimeInForce::ImmediateOrCancel);
  site_req.set_order_id("order_id");
  site_req.set_symbol("BTC-31DEC21-40000-C");
  site_req.set_side(ESide::Buy);
  pkg = GetDraftPkg();
  biz::optionorderbiz::OrderValidationBiz::StrategyTradingCheck(pkg.get(), site_req);
  ASSERT_EQ(pkg->GetLastError().error_code_, error::ErrorCode::kEcInvalidStrategy);
}

TEST_F(OptionOrderValidationBizTest, convert_out_req_id) {
  auto order_link_id = biz::optionorderbiz::OrderValidationBiz::ConvertOutRequestIdToOrderLinkId("out_request_id");
}

TEST_F(OptionOrderValidationBizTest, max_per_symbol_check) {
  // 验证单symbol仓位大小超过配置值的情况
  auto pkg = GetDraftPkg();
  auto error =
      biz::optionorderbiz::OrderValidationBiz::MaxNumPerSymbolCheck(pkg.get(), 1, 1, ESide::Buy, "BTC-31DEC21-40000-C");
  ASSERT_EQ(error.error_code_, error::ErrorCode::kEcNumExceededTheUpperLimit);

  // 验证单symbol订单size大小超过配置值的情况
  pkg = GetDraftPkg();
  error = biz::optionorderbiz::OrderValidationBiz::MaxNumPerSymbolCheck(pkg.get(), 0, 1, ESide::Sell,
                                                                        "BTC-31DEC21-40000-C");
  ASSERT_EQ(error.error_code_, error::ErrorCode::kEcNumExceededTheUpperLimit);

  // 验证单symbol仓位和订单大小均为超过配置值的情况
  pkg = GetDraftPkg();
  error =
      biz::optionorderbiz::OrderValidationBiz::MaxNumPerSymbolCheck(pkg.get(), 0, 2, ESide::Buy, "BTC-31DEC21-40000-C");
  ASSERT_EQ(error.error_code_, error::ErrorCode::kErrorCodeNone);
}

TEST_F(OptionOrderValidationBizTest, max_active_order_check) {
  // 验证单symbol订单数量超过配置值的情况
  auto pkg = GetDraftPkg();
  auto req = svc::uta_engine::option::SiteOrderRequest();

  auto error = biz::optionorderbiz::OrderValidationBiz::MaxActiveOrderCheck(pkg.get(), req);
  ASSERT_EQ(error.error_code_, error::ErrorCode::kEcExceedMaxOpenOrders);
}

TEST_F(OptionOrderValidationBizTest, fill_resp_key) {
  auto pkg = GetDraftPkg();

  biz::optionorderbiz::OrderValidationBiz::FillResponseKey(pkg.get(), "value");
  ASSERT_EQ(pkg->GetLastError().ext_info_["key0"], "value");
}

TEST_F(OptionOrderValidationBizTest, is_numeric) {
  // 验证数字字符串非法的情况
  auto valid = biz::optionorderbiz::OrderValidationBiz::IsNumeric("");
  ASSERT_EQ(valid, false);

  valid = biz::optionorderbiz::OrderValidationBiz::IsNumeric("-a");
  ASSERT_EQ(valid, false);

  valid = biz::optionorderbiz::OrderValidationBiz::IsNumeric("1.");
  ASSERT_EQ(valid, false);

  valid = biz::optionorderbiz::OrderValidationBiz::IsNumeric("1..2");
  ASSERT_EQ(valid, false);

  valid = biz::optionorderbiz::OrderValidationBiz::IsNumeric("1..2");
  ASSERT_EQ(valid, false);

  valid = biz::optionorderbiz::OrderValidationBiz::IsNumeric("-.");
  ASSERT_EQ(valid, false);
}

TEST_F(OptionOrderValidationBizTest, check_and_find_symbol) {
  // 验证symbol不存在的情况
  auto pkg = GetDraftPkg();
  auto error = biz::optionorderbiz::OrderValidationBiz::CheckAndFindSymbol(pkg.get(), "invalid_symbol", true);
  ASSERT_EQ(error.error_code_, error::ErrorCode::kEcParameterErrorSymbolNotExists);

  // 验证symbol状态处于交割的情况
  pkg = GetDraftPkg();
  pkg = GetDraftPkg();
  error = biz::optionorderbiz::OrderValidationBiz::CheckAndFindSymbol(pkg.get(), "BTC-01JAN19-40000-C", true);
  ASSERT_EQ(error.error_code_, error::ErrorCode::kEcParameterErrorSymbolInDelivery);

  // 验证symbol状态处于下线的情况
  pkg = GetDraftPkg();
  error = biz::optionorderbiz::OrderValidationBiz::CheckAndFindSymbol(pkg.get(), "BTC-02JAN19-40000-C", true);
  ASSERT_EQ(error.error_code_, error::ErrorCode::kEcParameterErrorSymbolNotExists);

  // 验证symbol处于交割时间内的情况
  pkg = GetDraftPkg();
  error = biz::optionorderbiz::OrderValidationBiz::CheckAndFindSymbol(pkg.get(), "BTC-03JAN19-40000-C", true);
  ASSERT_EQ(error.error_code_, error::ErrorCode::kEcParameterErrorSymbolInDelivery);

  // 验证symbol处于上线时间外的情况
  pkg = GetDraftPkg();
  error = biz::optionorderbiz::OrderValidationBiz::CheckAndFindSymbol(pkg.get(), "BTC-04JAN19-40000-C", true);
  ASSERT_EQ(error.error_code_, error::ErrorCode::kEcParameterErrorSymbolInDelivery);
}

TEST_F(OptionOrderValidationBizTest, check_order_link_id_idempotent) {
  auto pkg = GetDraftPkg();
  auto error = biz::optionorderbiz::OrderValidationBiz::CheckOrderLinkIdIdempotent(pkg.get(), "new_request_id");
  ASSERT_EQ(error.error_code_, error::ErrorCode::kErrorCodeNone);

  pkg = GetDraftPkg();
  error = biz::optionorderbiz::OrderValidationBiz::CheckOrderLinkIdIdempotent(pkg.get(), "order_link_id");
  ASSERT_EQ(error.error_code_, error::ErrorCode::kEcDuplicateRequest);
}

std::shared_ptr<tmock::CTradeAppMock> OptionOrderValidationBizTest::te;
