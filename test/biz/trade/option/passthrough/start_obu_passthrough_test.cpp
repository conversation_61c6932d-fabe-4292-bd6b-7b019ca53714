#include "test/biz/trade/option/passthrough/start_obu_passthrough_test.hpp"

#include <bbase/common/hdts/hdts.hpp>

#include "lib/msg_builder.hpp"
#include "proto/gen/option/model/passthrough_dto.pb.h"
#include "src/biz_worker/service/trade/store/passthrough/raw_recover_pass.hpp"
#include "src/biz_worker/utils/pb_convertor/to_pb.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(StartObuPassthroughTest, obu_passthrough) {
  store::RecoverPass::Ptr pass = std::make_shared<store::RecoverPass>();
  pass->set_symbol(102311);
  pass->set_cross_idx(10007);
  biz::CrossError err = biz::cross::xReqSender::SendRecoverPass(pass, EProductType::Options);
  ASSERT_NE(err.HasErr(), true);
  auto result1 = te->PopResult(5000);
  // todo:暂时还拿不到透传包撮合结果无法校验,后面再完善
  // ASSERT_NE(result1.m_msg.get(), nullptr);
}

TEST_F(StartObuPassthroughTest, obu_passthrough_contract) {
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 0;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = EOrderType::UNKNOWN;
  sender.req_mgr_.request_obj_->req_.side_ = ESide::None;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = ETimeInForce::UNKNOWN;
  sender.req_mgr_.request_obj_->req_.symbol_ = 102311;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = biz::cross::request::kCreatedByUser;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::response::kMTContractResponse;

  store::Contract::Ptr ct = std::make_shared<store::Contract>();
  ct->set_symbol(sender.req_mgr_.request_obj_->req_.symbol_);
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  ::convertor::ToPB::Convert(ct.get(), sender.req_mgr_.x_pass_through_.value().mutable_contract_info());
  // sender.req_mgr_.x_pass_through_->set_contract_info(ct);

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 10007;
  err = sender.DoSend2Cross(cross_idx, EProductType::Options);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult(5000);
}

TEST_F(StartObuPassthroughTest, obu_passthrough_contract_old) {
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 0;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = EOrderType::UNKNOWN;
  sender.req_mgr_.request_obj_->req_.side_ = ESide::None;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = ETimeInForce::UNKNOWN;
  sender.req_mgr_.request_obj_->req_.symbol_ = 102311;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = biz::cross::request::kCreatedByUser;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::response::kMTContractResponse;

  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  auto old_contract = sender.req_mgr_.x_pass_through_.value().mutable_option_passthrough_dto();
  old_contract->set_symbolid(sender.req_mgr_.request_obj_->req_.symbol_);
  old_contract->set_bztype("Contract");
  old_contract->set_sendingtime(bbase::utils::Time::GetTimeNs());
  auto cc = old_contract->mutable_contractdto();
  cc->set_contractstatus(::com::bybit::option::cross::model::pb::Settling);
  auto qp = cc->mutable_quoteprice();
  ::com::bybit::option::cross::model::pb::QuoteMoney money;
  auto apq = qp->mutable_marketprice();
  auto aaa = apq->mutable_amount();
  aaa->set_scale(1);
  aaa->set_unscaledvalue(2);

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 10007;
  err = sender.DoSend2Cross(cross_idx, EProductType::Options);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult(5000);
}

TEST_F(StartObuPassthroughTest, obu_passthrough_liq_old) {
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 0;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = EOrderType::UNKNOWN;
  sender.req_mgr_.request_obj_->req_.side_ = ESide::None;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = ETimeInForce::UNKNOWN;
  sender.req_mgr_.request_obj_->req_.symbol_ = 102312;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = biz::cross::request::kCreatedByUser;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::response::kMTContractResponse;

  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  auto pass_dto = sender.req_mgr_.x_pass_through_.value().mutable_option_passthrough_dto();
  pass_dto->set_symbolid(sender.req_mgr_.request_obj_->req_.symbol_);
  pass_dto->set_bztype("Liq");
  pass_dto->set_sendingtime(bbase::utils::Time::GetTimeNs());

  auto liq = pass_dto->mutable_liqdto();
  liq->set_symbolid(sender.req_mgr_.request_obj_->req_.symbol_);
  liq->set_side(1);
  liq->set_fromuserid(222);
  liq->set_touserid(3333);
  liq->set_liqsize(2);
  liq->set_liqsizeprecision(3);
  liq->set_outrequestid(boost::uuids::to_string(boost::uuids::random_generator()()).c_str());
  auto context = liq->mutable_context();
  context->insert({"bustPrice", "300"});
  context->insert({"liqTakeOverOrderCreateType", "37"});
  context->insert({"indexPrice", "310"});
  context->insert({"viewId", "3"});
  context->insert({"liqPositionBalance", "3000000"});
  context->insert({"liqTakeOverPriceScale", "3"});

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 10007;
  err = sender.DoSend2Cross(cross_idx, EProductType::Options);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult(5000);
}

// create type = 31 CreateByTakeOver_PassThrough
TEST_F(StartObuPassthroughTest, obu_passthrough_31) {
  GTEST_SKIP();
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = '1';
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 102311;
  // sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = 31;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTNewOrder;
  // 组装

  auto ct = std::make_shared<store::Position>();
  ct->symbol = sender.req_mgr_.request_obj_->req_.symbol_;
  ct->user_id = 10010;
  ct->price_scale = 4;
  ct->last_price = 5000;
  ct->qty_scale = 2;
  ct->buy_leaves_qty = 20000;
  ct->cross_seq = 1;
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  ::convertor::ToPB::Convert(ct.get(), sender.req_mgr_.x_pass_through_.value().mutable_orig_position());

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 10007;
  err = sender.DoSend2Cross(cross_idx, EProductType::Options);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult(5000);
}

std::shared_ptr<tmock::CTradeAppMock> StartObuPassthroughTest::te;
