//
// Created by SH00655ML on 2023/4/13.
//

#include "test/biz/trade/option/trigger/trigger_core_test.hpp"

#include "common/worker/result_handler.hpp"
#include "data/type/biz_type.hpp"
#include "enums/ebizcode/biz_code.pb.h"
#include "lib/msg_builder.hpp"
#include "src/biz_worker/engine/pre_worker/pre_engine.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/store/per_symbol_store.hpp"
#include "src/biz_worker/service/trade/store/position/cow_position.hpp"
#include "src/biz_worker/service/trade/store/position/raw_position.hpp"
#include "src/data/event/event.hpp"
#include "src/trigger_worker/options/trigger_core/trigger_core.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(TriggerCoreTest, testSyncOptionIvOrderToTrigger) {
  biz::user_id_t uid = 100000;
  std::string symbol_name = "BTCUSDT";  // BTCUSDT
  std::string test_symbol_name;
  worker::ResultHandler* result_handler = new worker::ResultHandler;

  stub user1(te, uid);
  // 初始化价格参数
  //  te->AddOptionMarkPrice(static_cast<ESymbol>(102312), 20000, 20000, 20000);

  // order.get() == nullptr
  trigger_worker::option::TriggerCore::SyncOptionIvOrderToTrigger(symbol_name, nullptr, result_handler);
  store::Order transact;
  transact.order_id = "123456789012345678901234567890123456";
  transact.order_iv_e8 = -1;
  transact.order_id.SetValue("123456789012345678901234567890123456");
  transact.user_id = uid;
  auto ptr_order = std::make_shared<store::Order>(transact);
  // symbol_name.empty()
  trigger_worker::option::TriggerCore::SyncOptionIvOrderToTrigger(test_symbol_name, ptr_order, result_handler);
  // transact.order_iv_e8 <= 0
  trigger_worker::option::TriggerCore::SyncOptionIvOrderToTrigger(symbol_name, ptr_order, result_handler);
  transact.order_iv_e8 = 10;

  // 状态不对
  transact.order_status = EOrderStatus::Filled;
  auto ptr_order2 = std::make_shared<store::Order>(transact);
  trigger_worker::option::TriggerCore::SyncOptionIvOrderToTrigger(symbol_name, ptr_order2, result_handler);

  transact.order_status = EOrderStatus::New;
  auto ptr_order3 = std::make_shared<store::Order>(transact);
  trigger_worker::option::TriggerCore::SyncOptionIvOrderToTrigger(symbol_name, ptr_order3, result_handler);
}

TEST_F(TriggerCoreTest, testSyncOptionIvOrdersToTrigger) {
  biz::user_id_t uid = 100000;
  std::string symbol_name = "BTCUSDT";  // BTCUSDT
  std::string test_symbol_name;

  biz::symbol_t symbol = 5;  // BTCUSDT
  biz::coin_t coin = 5;
  biz::account_id_t account_id(102);
  store::UserDataManager* user_data_manager_ = new store::UserDataManager();
  store::PerUserStore* perUserStore = user_data_manager_->GetUserData(uid);
  // 初始化
  if (perUserStore == nullptr) {
    user_data_manager_->CreateUserData(uid);
    perUserStore = user_data_manager_->GetUserData(uid);
  }
  auto header_ = std::make_shared<store::Header>();
  header_->op_from = "op_from";
  header_->user_ip = "127.0.0.1";
  header_->uid = uid;
  header_->coin = coin;
  header_->account_id = account_id;
  header_->symbol = symbol;
  event::GrpcContext grpc_context{};
  std::shared_ptr<event::BizEvent> bizEvent = std::make_shared<event::BizEvent>(
      event::BizEvent(uid, event::EventType::kEventGrpcInput, event::EventSourceType::kCross,
                      event::EventDispatchType::kUnicastToUser, event::EventBizType::kCreateOrder));

  auto grpc_biz_event_new = std::dynamic_pointer_cast<event::GrpcBizEvent>(bizEvent);

  auto grpc_biz_event = std::make_shared<event::GrpcBizEvent>(
      uid, event::EventType::kEventGrpcInput, event::EventSourceType::kCross, event::EventDispatchType::kUnicastToUser,
      event::EventBizType::kCreateOrder, event::GrpcSoureType::kGrpcSourceFuture,
      event::GrpcUriType::kGrpcFutureProcess, grpc_context);
  auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
  auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
  auto symbol_config = std::make_shared<biz::SymbolConfig>();

  symbol_config->value_scale_ = 8;
  symbol_config->price_scale_ = 8;
  symbol_config->symbol_ = ESymbol::BTCUSDT;
  symbol_config->value_scale_ = 8;
  symbol_config->tick_size_x_ = 5;
  symbol_config->price_limit_pnt_e6_ = 10;
  symbol_config_mgr->AddSymbolConfig(symbol_config);
  new_config_mgr->set_symbol_config_mgr(symbol_config_mgr);

  store::PerWorkerStore* per_worker_store = new store::PerWorkerStore();
  config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

  // 初始化draft_pkg
  auto draft_pkg = new biz::DraftPkg(grpc_biz_event, header_, per_worker_store, perUserStore);
  draft_pkg->ref_user_setting_ = perUserStore->user_setting_.get();
  draft_pkg->GetOrAttachUnifyWallet();

  auto result_handler1 = worker::ResultHandler();
  auto handler = std::make_shared<worker::ResultHandler>(result_handler1);

  auto ptr_pkg = std::shared_ptr<const biz::DraftPkg>(draft_pkg);
  trigger_worker::option::TriggerCore::SyncOptionIvOrdersToTrigger(ptr_pkg, handler);
  draft_pkg->need_to_persist_ = true;
  auto affect_option_orders_ = ptr_pkg->affect_option_orders_;
  auto commit = store::OrderCommit{};

  commit.need_to_persist = true;
  commit.base_order = std::make_shared<store::Order>();

  trigger_worker::option::TriggerCore::SyncOptionIvOrdersToTrigger(ptr_pkg, handler);

  store::Order transact;
  transact.order_id = "123456789012345678901234567890123456";
  transact.order_iv_e8 = -1;
  transact.order_id.SetValue("123456789012345678901234567890123456");
  transact.user_id = uid;

  commit.current_order = std::make_shared<store::Order>(transact);
  affect_option_orders_.emplace_back(commit);
  draft_pkg->affect_option_orders_.emplace_back(commit);
  trigger_worker::option::TriggerCore::SyncOptionIvOrdersToTrigger(ptr_pkg, handler);

  transact.order_iv_e8 = 10;
  transact.order_status = EOrderStatus::Filled;
  transact.symbol_name = "";
  commit.current_order = std::make_shared<store::Order>(transact);
  draft_pkg->affect_option_orders_.clear();
  draft_pkg->affect_option_orders_.emplace_back(commit);
  trigger_worker::option::TriggerCore::SyncOptionIvOrdersToTrigger(ptr_pkg, handler);

  transact.symbol_name = "BTCUSDT";
  commit.current_order = std::make_shared<store::Order>(transact);
  draft_pkg->affect_option_orders_.clear();
  draft_pkg->affect_option_orders_.emplace_back(commit);
  trigger_worker::option::TriggerCore::SyncOptionIvOrdersToTrigger(ptr_pkg, handler);

  transact.exec_type = EExecType::Replace;
  commit.current_order = std::make_shared<store::Order>(transact);
  draft_pkg->affect_option_orders_.clear();
  draft_pkg->affect_option_orders_.emplace_back(commit);
  trigger_worker::option::TriggerCore::SyncOptionIvOrdersToTrigger(ptr_pkg, handler);
}
std::shared_ptr<tmock::CTradeAppMock> TriggerCoreTest::te;
