//
// Created by SH00655ML on 2023/4/13.
//

#pragma once

#include <gtest/gtest.h>

#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>

#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/store/option/active_iv_order_store.hpp"
#include "src/trigger_worker/options/event/event.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

#ifndef UTA_ENGINE_OPTION_TRIGGER_ENGINE_HPP
#define UTA_ENGINE_OPTION_TRIGGER_ENGINE_HPP

class OptionTriggerEngineTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }

  static std::shared_ptr<tmock::CTradeAppMock> te;
};

#endif  // SRC_BIZ_WORKER_SERVICE_TRADE_OPTIONS_MODULES_ORDERBIZ_ACTIVEORDERBIZ_OPTION_ACTIVE_IV_ORDER_BIZ_HPP_
