//
// Created by SH00655ML on 2023/4/13.
//

#include "test/biz/trade/option/trigger/option_trigger_engine.hpp"

#include "data/type/biz_type.hpp"
#include "lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(OptionTriggerEngineTest, testTrigger) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  // 初始化价格参数
  te->AddOptionMarkPrice(static_cast<ESymbol>(102312), 20000, 20000, 20000);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  std::string category = "option";
  std::string symbol = "BTC-31DEC24-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "1";
  std::string price = "2000";

  CreateOrderBuilder create_ao_builder(category, symbol, position_idx, side, order_type, qty, price);
  auto order_link_id_1 = te->GenUUID();
  create_ao_builder.SetOrderLinkID(order_link_id_1);
  create_ao_builder.SetOrderIv("1");
  auto resp11 = user1.create_order(create_ao_builder.Build());
  ASSERT_EQ(resp11->order_link_id(), order_link_id_1);

  auto result = te->PopResult();
  auto orderCheck = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id_1);
  orderCheck.CheckIv(uid, 100000000);

  std::string order_id = resp11->order_id();

  if (!order_id.empty()) {
    store::ActiveIvOrderStore::IvOrder();
  }
}

std::shared_ptr<tmock::CTradeAppMock> OptionTriggerEngineTest::te;
