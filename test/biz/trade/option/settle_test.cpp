#include "test/biz/trade/option/settle_test.hpp"

#include "biz_worker/service/trade/options/event_handler/settle.hpp"
#include "cross_worker/cross_receiver/xresp/x_option_resp_pkg.hpp"
#include "lib/stub.hpp"
#include "seq_mark_worker/cross_seq_manager.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/options/modules/positionbiz/positionbiz.hpp"
#include "src/biz_worker/service/trade/store/per_coin_store.hpp"
#include "src/biz_worker/service/trade/store/position/raw_position.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "src/data/event/biz_event.hpp"
#include "src/seq_mark_worker/utils.hpp"

std::shared_ptr<tmock::CTradeAppMock> OptionSettleTest::te;

namespace OptionSettle {

void BuildProduceMsg(biz::cross_idx_t cross_idx, biz::cross::xReqSender& sender, bbase::hdts::Producer::Message& msg) {
  biz::CrossError err;
  /// 组装topic
  msg.topic = fmt::format("request_of_{}", cross_idx);

  /// 组装buf
  msg.value.resize(sizeof(biz::cross::RawXHeader) + sizeof(biz::cross::request::RawXRequest));
  memcpy(msg.value.data(), &sender.req_mgr_.header_obj_->header_, sizeof(biz::cross::RawXHeader));
  sender.req_mgr_.request_obj_->ToBinaryBuf(static_cast<char*>(msg.value.data()) + sizeof(biz::cross::RawXHeader));

  /// 处理透传数据 passthrough
  ::models::passthroughdto::PassThroughDTO passthrough;

  auto pass_through = passthrough.mutable_option_passthrough_dto();
  pass_through->set_bztype("Contract");
  pass_through->set_symbolid(102311);
  auto contract_dto = pass_through->mutable_contractdto();
  contract_dto->set_contractstatus(com::bybit::option::cross::model::pb::ContractStatusEnum::Settling);
  ::com::bybit::option::cross::model::pb::QuoteMoney* price = new ::com::bybit::option::cross::model::pb::QuoteMoney();
  auto money = price->mutable_amount();
  money->set_scale(8);
  money->set_unscaledvalue(5000000000000);
  auto* price1 = new ::com::bybit::option::cross::model::pb::QuoteMoney(*price);
  auto* price2 = new ::com::bybit::option::cross::model::pb::QuoteMoney(*price);
  auto* price3 = new ::com::bybit::option::cross::model::pb::QuoteMoney(*price);
  auto* price4 = new ::com::bybit::option::cross::model::pb::QuoteMoney(*price);
  auto* price5 = new ::com::bybit::option::cross::model::pb::QuoteMoney(*price);
  auto mutable_quoteprice = contract_dto->mutable_quoteprice();
  mutable_quoteprice->set_allocated_indexprice(price);
  mutable_quoteprice->set_allocated_strikeprice(price1);
  mutable_quoteprice->set_allocated_markprice(price2);
  mutable_quoteprice->set_allocated_deliveryprice(price3);
  mutable_quoteprice->set_allocated_underlyingprice(price4);
  mutable_quoteprice->set_allocated_marketprice(price5);
  auto value = passthrough.SerializeAsString();

  msg.headers[biz::XComm::x_pass_through_pb] = value;
}

/**
 * 发送交割信息
 */
void SendSettleMessage() {
  bbase::hdts::Producer::Message msg;

  biz::cross::xReqSender sender;
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  auto contract_info = std::make_shared<store::Contract>();
  contract_info->set_contract_status(static_cast<EContractStatus>(EContractStatus::Settling));
  sender.req_mgr_.x_pass_through_->mutable_contract_info()->set_contract_status(EContractStatus::Settling);

  auto err = sender.BuildHeader(biz::cross::request::kMTContract, ECreateType::UNKNOWN);
  if (err.HasErr()) {
    return;
  }

  /// 组装req写入值确保下游不会panic
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTContract;
  sender.req_mgr_.request_obj_->req_.symbol_ = 102311;
  sender.req_mgr_.request_obj_->req_.side_ = biz::cross::request::kSideBuy;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = biz::cross::request::kTIFGoodTillCancel;
  sender.req_mgr_.request_obj_->req_.order_type_ = biz::cross::request::kOTMarketOrder;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);

  bbase::hdts::Producer::Message produce_msg;
  BuildProduceMsg(10007, sender, produce_msg);
  /// #3: 发送数据
  produce_msg.headers[biz::XComm::send_to_cross] = std::to_string(bbase::utils::Time::GetTimeNs());
  produce_msg.need_callback = true;
  produce_msg.callback_data.data = nullptr;
  produce_msg.callback_data.start_timestamp_ns = bbase::utils::Time::GetTimeNs();

  bbase::hdts::Hdts::ProduceMessage(produce_msg);
}

}  // namespace OptionSettle

/**
 * 构造仓位信息，总共4个uid
 * 10000 没有挂单，没有持仓
 * 10001 没有挂单，有持仓
 * 10002 有挂单，有持仓
 * 10003 有挂单，没有持仓
 */
void OptionSettleTest::InitUserPositionInfo() {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  biz::user_id_t uid2 = 100001;
  stub user2(te, uid2);
  biz::user_id_t uid3 = 100002;
  stub user3(te, uid3);
  biz::user_id_t uid4 = 100003;
  stub user4(te, uid4);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user2.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid3, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user3.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid4, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user4.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  const char* symbol = "BTC-31DEC21-40000-C";
  const char* category = "option";
  const char* order_type = "Limit";
  std::string order_id_ss1;
  // uid1挂单，方向Sell，限价单
  {
    CreateOrderBuilder create_ao_builder(category, symbol, 0, "Sell", order_type, "100", "200");
    auto order_id = te->GenUUID();
    order_id_ss1 = order_id;
    create_ao_builder.SetOrderLinkID(order_id);

    auto resp1 = user1.create_order(create_ao_builder.Build());
    ASSERT_EQ(resp1->order_link_id(), order_id);

    // 撮合回包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    EXPECT_EQ(order_check.m_msg->qty_x(), 1000000);
    // auto &affected_positions = result.m_msg->mutable_options_margin_result()->affected_positions();
    EXPECT_EQ(result.m_msg->mutable_options_margin_result()->affected_positions_size(), 1);
  }
  /**
   * uid2 挂单，方向Buy，限价单
   * 此时与uid1 的Sell挂单会撮合成交
   */
  {
    CreateOrderBuilder create_ao_builder(category, symbol, 0, "Buy", order_type, "100", "200");
    auto order_link_id = te->GenUUID();
    create_ao_builder.SetOrderLinkID(order_link_id);

    auto resp1 = user2.create_order(create_ao_builder.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    // 撮合回包，这个为uid2的撮合回包处理
    {
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      auto order_check = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
      ASSERT_NE(order_check.m_msg, nullptr);
      EXPECT_EQ(order_check.m_msg->qty_x(), 1000000);
      EXPECT_EQ(order_check.m_msg->order_status(), EOrderStatus::Filled);
      EXPECT_EQ(order_check.m_msg->side(), ESide::Buy);
      auto& affected_positions = result.m_msg->mutable_options_margin_result()->affected_positions();
      EXPECT_EQ(result.m_msg->mutable_options_margin_result()->affected_positions_size(), 1);
      auto& position_1 = affected_positions[0];
      EXPECT_EQ(position_1.size_x(), 1000000);
      EXPECT_EQ(position_1.side(), ESide::Buy);
      EXPECT_EQ(position_1.user_id(), uid2);
    }

    // uid1的撮合回包处理
    {
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      std::cout << "2222 " << result.m_msg->DebugString() << std::endl;
      auto order_check = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_id_ss1);
      auto order_check_data = order_check.m_msg;
      EXPECT_EQ(order_check_data->side(), ESide::Sell);
      auto& affected_positions = result.m_msg->mutable_options_margin_result()->affected_positions();
      EXPECT_EQ(result.m_msg->mutable_options_margin_result()->affected_positions_size(), 1);
      auto& position_1 = affected_positions[0];
      EXPECT_EQ(position_1.size_x(), 1000000);
      EXPECT_EQ(position_1.side(), ESide::Sell);
      EXPECT_EQ(position_1.user_id(), uid1);
    }
  }
  // uid1挂单，方向Buy，限价单，执行平仓操作
  {
    CreateOrderBuilder create_ao_builder(category, symbol, 0, "Buy", order_type, "100", "200");
    auto order_link_id = te->GenUUID();
    create_ao_builder.SetOrderLinkID(order_link_id);
    order_id_ss1 = order_link_id;

    auto resp1 = user1.create_order(create_ao_builder.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    // 撮合回包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::cout << "1111 " << result.m_msg->DebugString() << std::endl;
    auto order_check = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    EXPECT_EQ(order_check.m_msg->qty_x(), 1000000);
    // auto &affected_positions = result.m_msg->mutable_options_margin_result()->affected_positions();
    EXPECT_EQ(result.m_msg->mutable_options_margin_result()->affected_positions_size(), 1);
  }
  // uid3挂单，方向sell，限价单，执行部分开仓
  {
    {
      CreateOrderBuilder create_ao_builder(category, symbol, 0, "Sell", order_type, "200", "200");
      auto order_link_id = te->GenUUID();
      create_ao_builder.SetOrderLinkID(order_link_id);

      auto resp1 = user3.create_order(create_ao_builder.Build());
      ASSERT_EQ(resp1->order_link_id(), order_link_id);

      // 撮合回包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      auto order_check = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
      ASSERT_NE(order_check.m_msg, nullptr);
      EXPECT_EQ(order_check.m_msg->qty_x(), 2000000);
      EXPECT_EQ(order_check.m_msg->leaves_qty_x(), 1000000);
      auto& affected_positions = result.m_msg->mutable_options_margin_result()->affected_positions();
      EXPECT_EQ(result.m_msg->mutable_options_margin_result()->affected_positions_size(), 1);
      auto& position_1 = affected_positions[0];
      EXPECT_EQ(position_1.side(), ESide::Sell);
      EXPECT_EQ(position_1.size_x(), 1000000);
      EXPECT_EQ(position_1.user_id(), uid3);
    }

    // uid1的撮合回包处理
    {
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      std::cout << "4444 " << result.m_msg->DebugString() << std::endl;
      auto order_check = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_id_ss1);
      auto order_check_data = order_check.m_msg;
      EXPECT_EQ(order_check_data->side(), ESide::Buy);
      EXPECT_EQ(order_check_data->leaves_qty_x(), 0);
      EXPECT_EQ(order_check_data->qty_x(), 1000000);
      auto& affected_positions = result.m_msg->mutable_options_margin_result()->affected_positions();
      EXPECT_EQ(result.m_msg->mutable_options_margin_result()->affected_positions_size(), 1);
      auto& position_1 = affected_positions[0];
      EXPECT_EQ(position_1.size_x(), 0);
      EXPECT_EQ(position_1.side(), ESide::None);
      EXPECT_EQ(position_1.user_id(), uid1);
    }
  }
  // uid4 挂卖单
  {
    {
      CreateOrderBuilder create_ao_builder(category, symbol, 0, "Sell", order_type, "100", "200");
      auto order_link_id = te->GenUUID();
      create_ao_builder.SetOrderLinkID(order_link_id);

      auto resp1 = user4.create_order(create_ao_builder.Build());
      ASSERT_EQ(resp1->order_link_id(), order_link_id);

      // 撮合回包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      std::cout << "55555 " << result.m_msg->DebugString() << std::endl;
      auto order_check = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
      ASSERT_NE(order_check.m_msg, nullptr);
      EXPECT_EQ(order_check.m_msg->qty_x(), 1000000);
      EXPECT_EQ(order_check.m_msg->leaves_qty_x(), 1000000);
      EXPECT_EQ(order_check.m_msg->side(), ESide::Sell);
      auto& affected_positions = result.m_msg->mutable_options_margin_result()->affected_positions();
      EXPECT_EQ(result.m_msg->mutable_options_margin_result()->affected_positions_size(), 1);
      auto& position_1 = affected_positions[0];
      EXPECT_EQ(position_1.size_x(), 0);
    }
    {
      CreateOrderBuilder create_ao_builder(category, symbol, 0, "Sell", order_type, "110", "200");
      auto order_link_id = te->GenUUID();
      create_ao_builder.SetOrderLinkID(order_link_id);

      auto resp1 = user4.create_order(create_ao_builder.Build());
      ASSERT_EQ(resp1->order_link_id(), order_link_id);

      // 撮合回包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      std::cout << "55555 " << result.m_msg->DebugString() << std::endl;
      auto order_check = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
      ASSERT_NE(order_check.m_msg, nullptr);
      EXPECT_EQ(order_check.m_msg->qty_x(), 1100000);
      EXPECT_EQ(order_check.m_msg->leaves_qty_x(), 1100000);
      EXPECT_EQ(order_check.m_msg->side(), ESide::Sell);
      auto& affected_positions = result.m_msg->mutable_options_margin_result()->affected_positions();
      EXPECT_EQ(result.m_msg->mutable_options_margin_result()->affected_positions_size(), 1);
      auto& position_1 = affected_positions[0];
      EXPECT_EQ(position_1.size_x(), 0);
    }
    {
      CreateOrderBuilder create_ao_builder(category, symbol, 0, "Sell", order_type, "120", "200");
      auto order_link_id = te->GenUUID();
      create_ao_builder.SetOrderLinkID(order_link_id);

      auto resp1 = user4.create_order(create_ao_builder.Build());
      ASSERT_EQ(resp1->order_link_id(), order_link_id);

      // 撮合回包
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      std::cout << "55555 " << result.m_msg->DebugString() << std::endl;
      auto order_check = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
      ASSERT_NE(order_check.m_msg, nullptr);
      EXPECT_EQ(order_check.m_msg->qty_x(), 1200000);
      EXPECT_EQ(order_check.m_msg->leaves_qty_x(), 1200000);
      EXPECT_EQ(order_check.m_msg->side(), ESide::Sell);
      auto& affected_positions = result.m_msg->mutable_options_margin_result()->affected_positions();
      EXPECT_EQ(result.m_msg->mutable_options_margin_result()->affected_positions_size(), 1);
      auto& position_1 = affected_positions[0];
      EXPECT_EQ(position_1.size_x(), 0);
    }
  }
}

/**
 * 初始化用户仓位数据
 * 构造2个用户数据，
 * 用户a，有买方向持仓
 * 用户b，有卖方向持仓
 */
void OptionSettleTest::InitUserPositionInfoForSettlePnl() {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  biz::user_id_t uid2 = 100001;
  stub user2(te, uid2);
  biz::user_id_t uid3 = 100002;
  stub user3(te, uid3);
  biz::user_id_t uid4 = 100003;
  stub user4(te, uid4);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user2.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid3, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user3.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid4, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user4.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  const char* symbol = "BTC-31DEC21-40000-C";
  const char* category = "option";
  const char* order_type = "Limit";
  std::string order_id_ss1;
  // uid1挂单，方向Sell，限价单
  {
    CreateOrderBuilder create_ao_builder(category, symbol, 0, "Sell", order_type, "100", "200");
    auto order_id = te->GenUUID();
    order_id_ss1 = order_id;
    create_ao_builder.SetOrderLinkID(order_id);

    auto resp1 = user1.create_order(create_ao_builder.Build());
    ASSERT_EQ(resp1->order_link_id(), order_id);

    // 撮合回包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    EXPECT_EQ(order_check.m_msg->qty_x(), 1000000);
    // auto &affected_positions = result.m_msg->mutable_options_margin_result()->affected_positions();
    EXPECT_EQ(result.m_msg->mutable_options_margin_result()->affected_positions_size(), 1);
  }
  /**
   * uid2 挂单，方向Buy，限价单
   * 此时与uid1 的Sell挂单会撮合成交
   */
  {
    CreateOrderBuilder create_ao_builder(category, symbol, 0, "Buy", order_type, "100", "200");
    auto order_link_id = te->GenUUID();
    create_ao_builder.SetOrderLinkID(order_link_id);

    auto resp1 = user2.create_order(create_ao_builder.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    // 撮合回包，这个为uid2的撮合回包处理
    {
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      auto order_check = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
      ASSERT_NE(order_check.m_msg, nullptr);
      EXPECT_EQ(order_check.m_msg->qty_x(), 1000000);
      EXPECT_EQ(order_check.m_msg->order_status(), EOrderStatus::Filled);
      EXPECT_EQ(order_check.m_msg->side(), ESide::Buy);
      auto& affected_positions = result.m_msg->mutable_options_margin_result()->affected_positions();
      EXPECT_EQ(result.m_msg->mutable_options_margin_result()->affected_positions_size(), 1);
      auto& position_1 = affected_positions[0];
      EXPECT_EQ(position_1.size_x(), 1000000);
      EXPECT_EQ(position_1.side(), ESide::Buy);
      EXPECT_EQ(position_1.user_id(), uid2);
    }

    // uid1的撮合回包处理
    {
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      std::cout << "2222 " << result.m_msg->DebugString() << std::endl;
      auto order_check = result.RefOptionMarginResult().RefRelatedOrderByOrderLinkId(order_id_ss1);
      auto order_check_data = order_check.m_msg;
      EXPECT_EQ(order_check_data->side(), ESide::Sell);
      auto& affected_positions = result.m_msg->mutable_options_margin_result()->affected_positions();
      EXPECT_EQ(result.m_msg->mutable_options_margin_result()->affected_positions_size(), 1);
      auto& position_1 = affected_positions[0];
      EXPECT_EQ(position_1.size_x(), 1000000);
      EXPECT_EQ(position_1.side(), ESide::Sell);
      EXPECT_EQ(position_1.user_id(), uid1);
    }
  }
}

void OptionSettleTest::CheckResultForSettlePnl() {
  for (int i = 0; i < 2; ++i) {
    // 精准过滤之后，无仓位/订单的用户不会有交割
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto& affected_positions = result.m_msg->mutable_options_margin_result()->affected_positions();
    EXPECT_EQ(result.m_msg->mutable_options_margin_result()->affected_positions_size(), 1);
    auto& position_1 = affected_positions[0];
    EXPECT_EQ(position_1.size_x(), 0);
    EXPECT_EQ(position_1.side(), ESide::None);

    // 校验pnl
    EXPECT_NE(position_1.cum_closed_pnl_e8(), 0);

    auto user_id = position_1.user_id();
    auto iter_find = std::find(user_id_list_.begin(), user_id_list_.end(), user_id);
    EXPECT_NE(iter_find, user_id_list_.end());

    EXPECT_EQ(result.m_msg->mutable_options_margin_result()->related_fills_size(), 1);
    auto& related_fill = result.m_msg->mutable_options_margin_result()->related_fills(0);
    if (position_1.transact_time_e9() == related_fill.transact_time_e9()) {
      EXPECT_NE(related_fill.exec_qty_x(), 0);
    }

    if (result.m_msg->mutable_options_margin_result()->related_orders_size() != 0) {
      auto& related_orders = result.m_msg->mutable_options_margin_result()->related_orders();
      for (auto& order : related_orders) {
        EXPECT_EQ(order.user_id(), user_id);
        EXPECT_EQ(order.leaves_value_e8(), 0);
        EXPECT_EQ(order.order_status(), EOrderStatus::Cancelled);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Canceled);
        EXPECT_EQ(order.cancel_type(), ECancelType::CancelByDelivery);
      }
    }
    std::cout << i << "--" << result.m_msg->DebugString() << std::endl;
  }
}

void OptionSettleTest::CheckResult() {
  for (int i = 0; i < 3; ++i) {
    // 精准过滤之后，无仓位/订单的用户不会有交割
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto& affected_positions = result.m_msg->mutable_options_margin_result()->affected_positions();
    EXPECT_EQ(result.m_msg->mutable_options_margin_result()->affected_positions_size(), 1);
    auto& position_1 = affected_positions[0];
    EXPECT_EQ(position_1.size_x(), 0);
    EXPECT_EQ(position_1.side(), ESide::None);
    auto user_id = position_1.user_id();
    auto iter_find = std::find(user_id_list_.begin(), user_id_list_.end(), user_id);
    EXPECT_NE(iter_find, user_id_list_.end());

    EXPECT_EQ(result.m_msg->mutable_options_margin_result()->related_fills_size(), 1);
    auto& related_fill = result.m_msg->mutable_options_margin_result()->related_fills(0);
    if (position_1.transact_time_e9() == related_fill.transact_time_e9()) {
      EXPECT_NE(related_fill.exec_qty_x(), 0);
    }

    if (result.m_msg->mutable_options_margin_result()->related_orders_size() != 0) {
      auto& related_orders = result.m_msg->mutable_options_margin_result()->related_orders();
      for (auto& order : related_orders) {
        EXPECT_EQ(order.user_id(), user_id);
        EXPECT_EQ(order.leaves_value_e8(), 0);
        EXPECT_EQ(order.order_status(), EOrderStatus::Cancelled);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Canceled);
        EXPECT_EQ(order.cancel_type(), ECancelType::CancelByDelivery);
      }
    }
    std::cout << i << "--" << result.m_msg->DebugString() << std::endl;
  }
}

TEST_F(OptionSettleTest, recv_settle_req_pnl_check) {
  te->InitMarkPrice();
  // 初始化数据
  InitUserPositionInfoForSettlePnl();
  // 发送交割请求
  OptionSettle::SendSettleMessage();
  // 检查结果
  CheckResultForSettlePnl();
}

TEST_F(OptionSettleTest, recv_settle_req) {
  te->InitMarkPrice();
  // 初始化数据
  InitUserPositionInfo();
  // 发送交割请求
  OptionSettle::SendSettleMessage();
  // 检查结果
  CheckResult();
}

TEST_F(OptionSettleTest, settle_unit_test) {
  auto header = std::make_shared<store::Header>();
  auto worker_store = std::make_shared<store::PerWorkerStore>();
  auto user_store = std::make_shared<store::PerUserStore>(10000);
  auto cross_event = std::make_shared<event::CrossPassThroughEvent>(
      0, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kBroadcastToUser, event::EventBizType::kMarginRequest);
  auto pass_through = std::make_shared<models::passthroughdto::PassThroughDTO>();
  pass_through->mutable_contract_info()->set_symbol(static_cast<ESymbol>(320330));
  pass_through->mutable_contract_info()->set_contract_status(EContractStatus::Settling);
  cross_event->pass_through = pass_through;
  auto x_pkg = std::make_shared<biz::cross::XOptionRespPkg>(nullptr);
  x_pkg->main_symbol = 320330;
  x_pkg->req_seq = 10;
  x_pkg->user_id_uniq_map.emplace(10000, 1);
  auto uids = std::make_shared<std::unordered_set<biz::user_id_t>>();
  uids->insert(10000);
  auto seq_mark = seq_mark_worker::Utils::MakeCrossResponseEvent(10000, x_pkg, uids);
  seq_mark_worker::Utils::SendToSeqMark(seq_mark);

  // 验证ev为nullptr的情况
  auto draft_pkg = std::make_shared<biz::DraftPkg>(nullptr, header, worker_store.get(), user_store.get());
  biz::optionbiz::OptionSettleHandler::Settle(draft_pkg.get());

  // 验证当前用户没有数据的情况
  cross_event->set_cross_idx(10000);
  cross_event->set_cross_seq(10);
  draft_pkg = std::make_shared<biz::DraftPkg>(cross_event, header, worker_store.get(), user_store.get());
  biz::optionbiz::OptionSettleHandler::Settle(draft_pkg.get());

  // 验证settle被幂等的情况
  header->coin = 16;
  user_store->working_coins_.clear();
  auto coin_store = std::make_shared<store::PerCoinStore>(10000, 16);
  auto symbol_store = std::make_shared<store::PerSymbolStore>(320330, EPositionMode::MergedSingle);
  coin_store->working_option_symbols_.emplace(320330, symbol_store);
  user_store->working_coins_.emplace(16, coin_store);
  user_store->cross_seq_map_.emplace(10000, 99);
  draft_pkg = std::make_shared<biz::DraftPkg>(cross_event, header, worker_store.get(), user_store.get());
  biz::optionbiz::OptionSettleHandler::Settle(draft_pkg.get());
}

TEST_F(OptionSettleTest, positionbiz_order_unit_test) {
  // 验证需要对put期权行权的情况
  biz::optionbiz::PositionBiz::NeedToExercisePut(bbase::decimal::Decimal<>(1), bbase::decimal::Decimal<>(2));

  auto symbol_svc = std::make_shared<config::OptionSymbolService>();
  config::ConfigProxy::Instance().config_mgr()->set_option_symbol_svc(symbol_svc);
  auto symbol_full_data = std::make_shared<config::SymbolFullData>();
  auto btc_dto = std::make_shared<config::SymbolDTO>();
  btc_dto->base_coin = "BTC";
  btc_dto->symbol_name = "BTC-25MAY23-32000-C";
  btc_dto->quote_coin = "USDT";
  btc_dto->delivery_time = 1;
  btc_dto->id = 320330;
  symbol_svc->set_symbol_full_data(symbol_full_data);
  symbol_full_data->symbol_id_map.emplace(320330, btc_dto);
  symbol_full_data->symbol_name_map.emplace("BTC-25MAY23-32000-C", btc_dto);

  // 验证交割订单异常各种case
  auto header = std::make_shared<store::Header>();
  auto worker_store = std::make_shared<store::PerWorkerStore>();
  auto user_store = std::make_shared<store::PerUserStore>(10000);
  auto cross_event = std::make_shared<event::CrossPassThroughEvent>(
      0, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kBroadcastToUser, event::EventBizType::kMarginRequest);
  worker_store->option_index_price_map_->emplace("BTC-USD", bbase::decimal::Decimal<>(1));
  worker_store->option_mark_price_map_->emplace(320330, bbase::decimal::Decimal<>(1));
  worker_store->option_delta_map_->emplace(320330, bbase::decimal::Decimal<>(1));
  worker_store->option_underlying_price_map_->emplace("BTC-USDT-1", bbase::decimal::Decimal<>(1));
  worker_store->option_vega_map_->emplace(320330, bbase::decimal::Decimal<>(1));

  auto coin_store = std::make_shared<store::PerCoinStore>(10000, 0);
  auto symbol_store = std::make_shared<store::PerSymbolStore>(320330, EPositionMode::MergedSingle);
  auto raw_position = std::make_shared<store::Position>();
  auto position = std::make_shared<store::CowPosition>(raw_position, EProductType::Options);
  symbol_store->all_positions_.emplace(EPositionIndex::Single, position);
  coin_store->working_option_symbols_.emplace(320330, symbol_store);
  user_store->working_coins_.emplace(0, coin_store);

  auto ord_1 = std::make_shared<store::CowOrder>();
  ord_1->cur_order = std::make_shared<store::Order>();

  auto ord_2 = std::make_shared<store::CowOrder>();
  ord_2->cur_order = std::make_shared<store::Order>();

  auto ord_3 = std::make_shared<store::CowOrder>();
  ord_3->cur_order = std::make_shared<store::Order>();

  auto ord_4 = std::make_shared<store::CowOrder>();
  ord_4->cur_order = std::make_shared<store::Order>();

  auto ord_5 = std::make_shared<store::CowOrder>();
  ord_5->cur_order = std::make_shared<store::Order>();

  ord_1->DraftForMainChain()->symbol = 320331;
  user_store->all_option_working_order_map_by_order_id_.emplace("ord_1", ord_1.get());
  ord_2->DraftForMainChain()->symbol = 320330;
  ord_2->DraftForMainChain()->order_status = EOrderStatus::Filled;
  user_store->all_option_working_order_map_by_order_id_.emplace("ord_2", ord_2.get());
  ord_3->DraftForMainChain()->symbol = 320330;
  ord_3->DraftForMainChain()->order_status = EOrderStatus::New;
  ord_3->DraftForMainChain()->create_type = ECreateType::CreateByTakeOver_PassThrough;
  user_store->all_option_working_order_map_by_order_id_.emplace("ord_3", ord_3.get());
  ord_4->pending_draft_x_req = std::make_shared<store::Order>();
  ord_4->pending_draft_x_req->symbol = 320330;
  ord_4->pending_draft_x_req->order_id = "ord_4";
  ord_4->pending_draft_x_req->order_status = EOrderStatus::New;
  ord_4->pending_draft_x_req->cross_status = ECrossStatus::Aborted;
  user_store->all_option_working_order_map_by_order_id_.emplace("ord_4", ord_4.get());
  ord_5->pending_draft_x_req = std::make_shared<store::Order>();
  ord_5->pending_draft_x_req->symbol = 320330;
  ord_5->pending_draft_x_req->order_id = "ord_5";
  ord_5->pending_draft_x_req->order_status = EOrderStatus::New;
  ord_5->pending_draft_x_req->cross_status = ECrossStatus::PendingCancel;
  ord_5->PN_x_req = ord_5->pending_draft_x_req;
  ord_5->PR_x_req = ord_5->pending_draft_x_req;
  ord_5->PC_x_req = ord_5->pending_draft_x_req;
  user_store->all_option_working_order_map_by_order_id_.emplace("ord_5", ord_5.get());

  auto draft_pkg = std::make_shared<biz::DraftPkg>(cross_event, header, worker_store.get(), user_store.get());
  auto contract_dto = std::make_shared<models::contractdto::ContractDTO>();
  auto in_taken_over = false;
  biz::optionbiz::PositionBiz::PerOrderSettle(draft_pkg.get(), *contract_dto, in_taken_over);
}

TEST_F(OptionSettleTest, positionbiz_pz_unit_test) {
  auto header = std::make_shared<store::Header>();
  auto worker_store = std::make_shared<store::PerWorkerStore>();
  auto user_store = std::make_shared<store::PerUserStore>(10000);
  auto cross_event = std::make_shared<event::CrossPassThroughEvent>(
      0, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kBroadcastToUser, event::EventBizType::kMarginRequest);

  auto draft_pkg = std::make_shared<biz::DraftPkg>(cross_event, header, worker_store.get(), user_store.get());
  auto contract_dto = std::make_shared<models::contractdto::ContractDTO>();

  biz::optionbiz::PositionBiz::PerPzSettle(draft_pkg.get(), *contract_dto);
}

TEST_F(OptionSettleTest, positionbiz_settle_unit_test) {
  auto option_symbol_config = std::make_shared<config::OptionSymbolService>();

  auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
  config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

  auto symbol_full_data = std::make_shared<config::SymbolFullData>();
  auto option_symbol_dto = std::make_shared<config::SymbolDTO>();
  option_symbol_dto->symbol_name = "BTC-25MAY23-32000-C";
  option_symbol_dto->base_coin = "BTC";
  option_symbol_dto->delivery_time = 1841937600;
  option_symbol_dto->id = 320330;
  option_symbol_dto->status = "ONLINE";
  option_symbol_dto->cross_id = 10000;
  option_symbol_dto->settle_coin = "USDC";

  symbol_full_data->symbol_id_map.emplace(option_symbol_dto->id, option_symbol_dto);
  option_symbol_config->set_symbol_full_data(symbol_full_data);
  new_config_mgr->set_option_symbol_svc(option_symbol_config);

  auto header = std::make_shared<store::Header>();
  auto worker_store = std::make_shared<store::PerWorkerStore>();
  auto user_store = std::make_shared<store::PerUserStore>(2002);
  auto cross_event = std::make_shared<event::CrossPassThroughEvent>(
      0, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kBroadcastToUser, event::EventBizType::kMarginRequest);

  header->coin = 16;
  auto coin_store = std::make_shared<store::PerCoinStore>(2002, 16);
  auto symbol_store = std::make_shared<store::PerSymbolStore>(320330, EPositionMode::MergedSingle);
  auto raw_position = std::make_shared<store::Position>();
  auto position = std::make_shared<store::CowPosition>(raw_position, EProductType::Options);
  symbol_store->all_positions_.emplace(EPositionIndex::Single, position);
  coin_store->working_option_symbols_.emplace(320330, symbol_store);
  user_store->working_coins_.emplace(16, coin_store);

  auto ord_1 = std::make_shared<store::CowOrder>();
  ord_1->cur_order = std::make_shared<store::Order>();

  ord_1->item.key.symbol = 320330;
  ord_1->DraftForMainChain()->symbol = 320330;
  ord_1->DraftForMainChain()->order_status = EOrderStatus::New;
  ord_1->DraftForMainChain()->create_type = ECreateType::CreateByTakeOver_PassThrough;
  user_store->all_option_working_order_map_by_order_id_.emplace("ord_1", ord_1.get());

  auto draft_pkg = std::make_shared<biz::DraftPkg>(cross_event, header, worker_store.get(), user_store.get());
  auto contract_dto = std::make_shared<models::contractdto::ContractDTO>();
  contract_dto->set_symbol(static_cast<ESymbol>(320330));

  biz::optionbiz::PositionBiz::Settle(draft_pkg.get(), *contract_dto);
}

TEST_F(OptionSettleTest, positionbiz_pnl_unit_test) {
  auto symbol_svc = std::make_shared<config::OptionSymbolService>();
  config::ConfigProxy::Instance().config_mgr()->set_option_symbol_svc(symbol_svc);
  auto symbol_full_data = std::make_shared<config::SymbolFullData>();
  auto btc_dto = std::make_shared<config::SymbolDTO>();
  btc_dto->base_coin = "BTC";
  btc_dto->symbol_name = "BTC-25MAY23-32000-P";
  btc_dto->quote_coin = "USDT";
  btc_dto->symbol_type = "P";
  btc_dto->id = 320330;
  btc_dto->strike_price = bbase::decimal::Decimal<>(2);
  symbol_svc->set_symbol_full_data(symbol_full_data);
  symbol_full_data->symbol_id_map.emplace(320330, btc_dto);
  symbol_full_data->symbol_name_map.emplace("BTC-25MAY23-32000-P", btc_dto);

  // 查询不到symbol的场景
  auto transact = store::Order();
  auto pz = store::Position();
  transact.symbol = 1;
  biz::optionbiz::PositionBiz::CalcSettlePnl(transact, pz);

  // 查询有效symbol的场景
  transact = store::Order();
  pz = store::Position();
  transact.symbol = 320330;
  biz::optionbiz::PositionBiz::CalcSettlePnl(transact, pz);
}
