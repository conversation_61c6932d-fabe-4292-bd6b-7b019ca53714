#include <gtest/gtest.h>

#include <memory>

#include "src/biz_worker/service/trade/store/order/cow_order.hpp"
#include "src/biz_worker/service/trade/store/order_manager/half_side_order_book.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/mocks/trading_app_mock.hpp"
#include "test/biz/trade/option/halfob/make_order_test.hpp"

class HalfObOptionFeeTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
};

TEST_F(HalfObOptionFeeTest, HalfObOptionFee) {
  auto buy_ob = std::make_shared<store::HalfSideOrderBook>(ESymbol::BTCUSDT, ESide::Buy, EProductType::Options);
  auto sell_ob = std::make_shared<store::HalfSideOrderBook>(ESymbol::BTCUSDT, ESide::Sell, EProductType::Options);

  buy_ob->UpdateCachedFeeObKey(10000 * 10000);   // key = 56 * 10000
  sell_ob->UpdateCachedFeeObKey(10000 * 10000);  // key = 56 * 10000

  // 添加第1笔buy
  auto o1_buy = MakeLimitBuy(10000 * 10000, 0.0005 * 100000000);
  auto cow_ord1_buy = store::CowOrder::CreateCowOrder(o1_buy);
  buy_ob->InsertOrder(cow_ord1_buy.get());
  EXPECT_EQ(buy_ob->extra_qty_over_index_x_, 50000);
  EXPECT_EQ(buy_ob->extra_value_over_index_e8_, 500000000);
  EXPECT_EQ(buy_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(buy_ob->extra_value_under_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_over_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_over_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_under_index_e8_, 0);

  // 添加第2笔buy
  auto o2_buy = MakeLimitBuy(10 * 10000, 0.0001 * 100000000);
  auto cow_ord2_buy = store::CowOrder::CreateCowOrder(o2_buy);
  buy_ob->InsertOrder(cow_ord2_buy.get());
  EXPECT_EQ(buy_ob->extra_qty_over_index_x_, 50000);
  EXPECT_EQ(buy_ob->extra_value_over_index_e8_, 500000000);
  EXPECT_EQ(buy_ob->extra_qty_under_index_x_, 10000);
  EXPECT_EQ(buy_ob->extra_value_under_index_e8_, 100000);
  EXPECT_EQ(sell_ob->extra_qty_over_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_over_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_under_index_e8_, 0);

  // 添加第3笔buy
  auto o3_buy = MakeLimitBuy(20000 * 10000, 0.0002 * 100000000);
  auto cow_ord3_buy = store::CowOrder::CreateCowOrder(o3_buy);
  buy_ob->InsertOrder(cow_ord3_buy.get());
  EXPECT_EQ(buy_ob->extra_qty_over_index_x_, 70000);
  EXPECT_EQ(buy_ob->extra_value_over_index_e8_, 900000000);
  EXPECT_EQ(buy_ob->extra_qty_under_index_x_, 10000);
  EXPECT_EQ(buy_ob->extra_value_under_index_e8_, 100000);
  EXPECT_EQ(sell_ob->extra_qty_over_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_over_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_under_index_e8_, 0);

  // 删除第1笔buy
  buy_ob->EraseOrder(cow_ord1_buy.get());
  EXPECT_EQ(buy_ob->extra_qty_over_index_x_, 20000);
  EXPECT_EQ(buy_ob->extra_value_over_index_e8_, 400000000);
  EXPECT_EQ(buy_ob->extra_qty_under_index_x_, 10000);
  EXPECT_EQ(buy_ob->extra_value_under_index_e8_, 100000);
  EXPECT_EQ(sell_ob->extra_qty_over_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_over_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_under_index_e8_, 0);

  // 删除第2笔buy
  buy_ob->EraseOrder(cow_ord2_buy.get());
  EXPECT_EQ(buy_ob->extra_qty_over_index_x_, 20000);
  EXPECT_EQ(buy_ob->extra_value_over_index_e8_, 400000000);
  EXPECT_EQ(buy_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(buy_ob->extra_value_under_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_over_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_over_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_under_index_e8_, 0);

  // 添加第1笔sell
  auto o1_sell = MakeLimitSell(20000 * 10000, 0.0002 * 100000000);
  auto cow_ord1_sell = store::CowOrder::CreateCowOrder(o1_sell);
  sell_ob->InsertOrder(cow_ord1_sell.get());
  EXPECT_EQ(buy_ob->extra_qty_over_index_x_, 20000);
  EXPECT_EQ(buy_ob->extra_value_over_index_e8_, 400000000);
  EXPECT_EQ(buy_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(buy_ob->extra_value_under_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_over_index_x_, 20000);
  EXPECT_EQ(sell_ob->extra_value_over_index_e8_, 400000000);
  EXPECT_EQ(sell_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_under_index_e8_, 0);

  // 添加第2笔sell
  auto o2_sell = MakeLimitSell(40000 * 10000, 0.0004 * 100000000);
  auto cow_ord2_sell = store::CowOrder::CreateCowOrder(o2_sell);
  sell_ob->InsertOrder(cow_ord2_sell.get());
  EXPECT_EQ(buy_ob->extra_qty_over_index_x_, 20000);
  EXPECT_EQ(buy_ob->extra_value_over_index_e8_, 400000000);
  EXPECT_EQ(buy_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(buy_ob->extra_value_under_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_over_index_x_, 60000);
  EXPECT_EQ(sell_ob->extra_value_over_index_e8_, 2000000000);
  EXPECT_EQ(sell_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_under_index_e8_, 0);

  // 添加第3笔sell
  auto o3_sell = MakeLimitSell(50 * 10000, 0.0005 * 100000000);
  auto cow_ord3_sell = store::CowOrder::CreateCowOrder(o3_sell);
  sell_ob->InsertOrder(cow_ord3_sell.get());
  EXPECT_EQ(buy_ob->extra_qty_over_index_x_, 20000);
  EXPECT_EQ(buy_ob->extra_value_over_index_e8_, 400000000);
  EXPECT_EQ(buy_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(buy_ob->extra_value_under_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_over_index_x_, 60000);
  EXPECT_EQ(sell_ob->extra_value_over_index_e8_, 2000000000);
  EXPECT_EQ(sell_ob->extra_qty_under_index_x_, 50000);
  EXPECT_EQ(sell_ob->extra_value_under_index_e8_, 2500000);

  // 删除第1笔sell
  sell_ob->EraseOrder(cow_ord1_sell.get());
  EXPECT_EQ(buy_ob->extra_qty_over_index_x_, 20000);
  EXPECT_EQ(buy_ob->extra_value_over_index_e8_, 400000000);
  EXPECT_EQ(buy_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(buy_ob->extra_value_under_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_over_index_x_, 40000);
  EXPECT_EQ(sell_ob->extra_value_over_index_e8_, 1600000000);
  EXPECT_EQ(sell_ob->extra_qty_under_index_x_, 50000);
  EXPECT_EQ(sell_ob->extra_value_under_index_e8_, 2500000);

  // 清楚所有订单
  sell_ob->EraseOrder(cow_ord2_sell.get());
  sell_ob->EraseOrder(cow_ord3_sell.get());
  buy_ob->EraseOrder(cow_ord3_buy.get());
  EXPECT_EQ(buy_ob->extra_qty_over_index_x_, 0);
  EXPECT_EQ(buy_ob->extra_value_over_index_e8_, 0);
  EXPECT_EQ(buy_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(buy_ob->extra_value_under_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_over_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_over_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_under_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_under_index_e8_, 0);

  // 连续添加订单
  auto o4_buy = MakeLimitBuy(50 * 10000, 0.0005 * 100000000);
  auto cow_ord4_buy = store::CowOrder::CreateCowOrder(o4_buy);
  buy_ob->InsertOrder(cow_ord4_buy.get());
  auto o5_buy = MakeLimitBuy(40 * 10000, 0.0005 * 100000000);
  auto cow_ord5_buy = store::CowOrder::CreateCowOrder(o5_buy);
  buy_ob->InsertOrder(cow_ord5_buy.get());
  auto o6_buy = MakeLimitBuy(30 * 10000, 0.0005 * 100000000);
  auto cow_ord6_buy = store::CowOrder::CreateCowOrder(o6_buy);
  buy_ob->InsertOrder(cow_ord6_buy.get());

  auto o4_sell = MakeLimitSell(50 * 10000, 0.0005 * 100000000);
  auto cow_ord4_sell = store::CowOrder::CreateCowOrder(o4_sell);
  sell_ob->InsertOrder(cow_ord4_sell.get());
  auto o5_sell = MakeLimitSell(40 * 10000, 0.0005 * 100000000);
  auto cow_ord5_sell = store::CowOrder::CreateCowOrder(o5_sell);
  sell_ob->InsertOrder(cow_ord5_sell.get());
  auto o6_sell = MakeLimitSell(30 * 10000, 0.0005 * 100000000);
  auto cow_ord6_sell = store::CowOrder::CreateCowOrder(o6_sell);
  sell_ob->InsertOrder(cow_ord6_sell.get());
  EXPECT_EQ(buy_ob->extra_qty_over_index_x_, 0);
  EXPECT_EQ(buy_ob->extra_value_over_index_e8_, 0);
  EXPECT_EQ(buy_ob->extra_qty_under_index_x_, 150000);
  EXPECT_EQ(buy_ob->extra_value_under_index_e8_, 6000000);
  EXPECT_EQ(sell_ob->extra_qty_over_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_over_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_under_index_x_, 150000);
  EXPECT_EQ(sell_ob->extra_value_under_index_e8_, 6000000);

  buy_ob->UpdateCachedFeeObKey(8000 * 10000);   // key = 44.8 * 10000
  sell_ob->UpdateCachedFeeObKey(8000 * 10000);  // key = 44.8 * 10000

  EXPECT_EQ(buy_ob->extra_qty_over_index_x_, 50000);
  EXPECT_EQ(buy_ob->extra_value_over_index_e8_, 2500000);
  EXPECT_EQ(buy_ob->extra_qty_under_index_x_, 100000);
  EXPECT_EQ(buy_ob->extra_value_under_index_e8_, 3500000);
  EXPECT_EQ(sell_ob->extra_qty_over_index_x_, 50000);
  EXPECT_EQ(sell_ob->extra_value_over_index_e8_, 2500000);
  EXPECT_EQ(sell_ob->extra_qty_under_index_x_, 100000);
  EXPECT_EQ(sell_ob->extra_value_under_index_e8_, 3500000);

  buy_ob->UpdateCachedFeeObKey(10000 * 10000);   // key = 56 * 10000
  sell_ob->UpdateCachedFeeObKey(10000 * 10000);  // key = 56 * 10000

  EXPECT_EQ(buy_ob->extra_qty_over_index_x_, 0);
  EXPECT_EQ(buy_ob->extra_value_over_index_e8_, 0);
  EXPECT_EQ(buy_ob->extra_qty_under_index_x_, 150000);
  EXPECT_EQ(buy_ob->extra_value_under_index_e8_, 6000000);
  EXPECT_EQ(sell_ob->extra_qty_over_index_x_, 0);
  EXPECT_EQ(sell_ob->extra_value_over_index_e8_, 0);
  EXPECT_EQ(sell_ob->extra_qty_under_index_x_, 150000);
  EXPECT_EQ(sell_ob->extra_value_under_index_e8_, 6000000);
}
