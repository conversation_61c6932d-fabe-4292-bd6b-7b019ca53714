#include <gtest/gtest.h>

#include <memory>

#include "src/biz_worker/service/trade/store/order/cow_order.hpp"
#include "src/biz_worker/service/trade/store/order_manager/half_side_order_book.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/mocks/trading_app_mock.hpp"
#include "test/biz/trade/option/halfob/check_half_ob_test.hpp"
#include "test/biz/trade/option/halfob/make_order_test.hpp"

class HalfObBuyClosingTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
};

TEST_F(HalfObBuyClosingTest, go_TestHalfObBuyClosing) {
  auto buy_ob = std::make_shared<store::HalfSideOrderBook>(ESymbol::BTCUSDT, ESide::Buy, EProductType::Futures);

  buy_ob->UpdateCachedPzSideSize(ESide::Sell, 500 * 1e4);
  buy_ob->UpdateCachedMarkPrice(10000 * 1e4);

  // 初始状态halfOB为空
  {
    EXPECT_EQ(buy_ob->TryGetNextFloatingOrder(nullptr), nullptr);
    CheckHalfOB(buy_ob, 0, 0, 0, 500 * 1e4, 0, 0, 0, 0, 0, 0, 10000 * 1e4, 0, 0, 0, 0);
  }

  // 添加第1笔buy
  auto o1 = MakeLimitBuy(10030 * 1e4, 110 * 1e4);
  o1->exec_inst = EExecInst::Close;
  auto cow_ord1 = store::CowOrder::CreateCowOrder(o1);
  buy_ob->InsertOrder(cow_ord1.get());

  EXPECT_EQ(cow_ord1->item.close_on_trigger, true);
  EXPECT_EQ(cow_ord1->item.reduce_only, true);
  // 边界等于o1
  CheckHalfOB(buy_ob, 1, o1->leaves_qty, o1->leaves_value, 500 * 1e4, cow_ord1->item.key.price,
              cow_ord1->item.key.added_op_seq, o1->leaves_qty, o1->leaves_value, 0, 0, 10000 * 1e4, 0, 0, 0, 1);

  // 添加第2笔buy
  auto o2 = MakeLimitBuy(10020 * 1e4, 220 * 1e4);
  o2->exec_inst = EExecInst::bit_reduce_only;
  auto cow_ord2 = store::CowOrder::CreateCowOrder(o2);

  buy_ob->InsertOrder(cow_ord2.get());

  EXPECT_EQ(cow_ord1->item.reduce_only, true);
  // 边界等于o2
  CheckHalfOB(buy_ob, 2, o1->leaves_qty + o2->leaves_qty, o1->leaves_value + o2->leaves_value, 500 * 1e4,
              cow_ord2->item.key.price, cow_ord2->item.key.added_op_seq, o1->leaves_qty + o2->leaves_qty,
              o1->leaves_value + o2->leaves_value, 0, 0, 10000 * 1e4, 0, 0, 0,
              1);  // o2 不算浮亏

  // 添加第3笔buy
  auto o3 = MakeLimitBuy(10010 * 1e4, 330 * 1e4);
  auto cow_ord3 = store::CowOrder::CreateCowOrder(o3);
  buy_ob->InsertOrder(cow_ord3.get());

  // 边界等于o3
  // 切割o3后的被closing覆盖住的value `floor(110*10030*1e8) + floor(220*10020*1e8) + floor(170*10010*1e8)`
  // 切割后剩余价值 `floor(330*10010*1e8) - floor(170*10010*1e8)`
  CheckHalfOB(buy_ob, 3, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value, 500 * 1e4, cow_ord3->item.key.price,
              cow_ord3->item.key.added_op_seq, 500 * 1e4, 50094000000, 160 * 1e4, 16016000000, 10000 * 1e4, 160 * 1e4,
              16016000000, 0, 1);

  // 添加第4笔buy
  auto o4 = MakeLimitBuy(9999 * 1e4, 440 * 1e4);
  auto cow_ord4 = store::CowOrder::CreateCowOrder(o4);
  buy_ob->InsertOrder(cow_ord4.get());

  // 边界等于o3
  // 切割o3后的被closing覆盖住的value 4990618
  // 切割后剩余价值+440订单
  // 切割后剩余价值 440订单不算浮亏
  CheckHalfOB(buy_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 500 * 1e4,
              cow_ord3->item.key.price, cow_ord3->item.key.added_op_seq, 500 * 1e4, 50094000000, 600 * 1e4, 60011600000,
              10000 * 1e4, 160 * 1e4, 16016000000, 0, 1);

  ////////////////////////////////////////////////////////////////////////////////
  // 移除第3笔buy
  buy_ob->EraseOrder(cow_ord3.get());
  // 边界等于o4
  // 切割o4后的被closing覆盖住的value 4992487
  // 切割后剩余价值+440订单
  // 切割后剩余价值 440订单不算浮亏
  CheckHalfOB(buy_ob, 3, o1->leaves_qty + o2->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o4->leaves_value, 500 * 1e4, cow_ord4->item.key.price,
              cow_ord4->item.key.added_op_seq, 500 * 1e4, 50075300000, 270 * 1e4, 26997300000, 10000 * 1e4, 0, 0, 0, 1);

  ////////////////////////////////////////////////////////////////////////////////
  // 再把第3笔buy添加回去
  buy_ob->InsertOrder(cow_ord3.get());
  // 边界等于o3
  // 切割o3后的被closing覆盖住的value 4990618
  // 切割后剩余价值+440订单
  // 切割后剩余价值 440订单不算浮亏
  CheckHalfOB(buy_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 500 * 1e4,
              cow_ord3->item.key.price, cow_ord3->item.key.added_op_seq, 500 * 1e4, 50094000000, 600 * 1e4, 60011600000,
              10000 * 1e4, 160 * 1e4, 16016000000, 0, 1);

  ////////////////////////////////////////////////////////////////////////////////
  // 强制将持仓更新为0
  buy_ob->EraseOrder(cow_ord1.get());
  buy_ob->InsertOrder(cow_ord1.get());
  buy_ob->EraseOrder(cow_ord2.get());
  buy_ob->InsertOrder(cow_ord2.get());
  buy_ob->UpdateCachedPzSideSize(ESide::None, 0);
  buy_ob->EraseOrder(cow_ord1.get());
  buy_ob->InsertOrder(cow_ord1.get());
  buy_ob->EraseOrder(cow_ord2.get());
  buy_ob->InsertOrder(cow_ord2.get());

  // 边界重置到最小
  // o1,o2不算浮亏
  // o1,o2不算浮亏, 440订单不算浮亏
  CheckHalfOB(buy_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 0, store::kTopBuyPriceX,
              store::kTopSellPriceX, 0, 0, o3->leaves_qty + o4->leaves_qty, o3->leaves_value + o4->leaves_value,
              10000 * 1e4, o3->leaves_qty, o3->leaves_value, 2, 0);

  ////////////////////////////////////////////////////////////////////////////////
  // 强制将持仓更新为400
  buy_ob->UpdateCachedPzSideSize(ESide::Sell, 500 * 1e4);
  buy_ob->UpdateCachedPzSideSize(ESide::Sell, 330 * 1e4);
  buy_ob->UpdateCachedPzSideSize(ESide::Sell, 400 * 1e4);

  // 边界等于o3
  // 切割o3后的被closing覆盖住的value `floor(110*10030*1e8) + floor(220*10020*1e8) + floor(70*10010*1e8)`
  // 切割后剩余价值+440订单 `floor(330*10010*1e8) - floor(70*10010*1e8)`
  // 切割后剩余价值 440订单不算浮亏
  CheckHalfOB(buy_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 400 * 1e4,
              cow_ord3->item.key.price, cow_ord3->item.key.added_op_seq, 400 * 1e4, 40084000000, 700 * 1e4, 70021600000,
              10000 * 1e4, 260 * 1e4, 26026000000, 0, 1);

  ////////////////////////////////////////////////////////////////////////////////
  // 模拟标记价格浮动
  buy_ob->UpdateCachedMarkPrice(9900 * 1e4);

  // 边界等于o3
  // 切割o3后的被closing覆盖住的value `floor(110*10030*1e8) + floor(220*10020*1e8) + floor(70*10010*1e8)`
  // 切割后剩余价值+440订单 `floor(330*10010*1e8) - floor(70*10010*1e8)`
  // 切割后剩余价值 440订单也算浮亏
  CheckHalfOB(buy_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 400 * 1e4,
              cow_ord3->item.key.price, cow_ord3->item.key.added_op_seq, 400 * 1e4, 40084000000, 700 * 1e4, 70021600000,
              9900 * 1e4, 700 * 1e4, 70021600000, 0, 1);

  buy_ob->UpdateCachedMarkPrice(10100 * 1e4);

  // 边界等于o3
  // 切割o3后的被closing覆盖住的value `floor(110*10030*1e8) + floor(220*10020*1e8) + floor(70*10010*1e8)`
  // 切割后剩余价值+440订单 `floor(330*10010*1e8) - floor(70*10010*1e8)`
  // 没有浮亏
  CheckHalfOB(buy_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 400 * 1e4,
              cow_ord3->item.key.price, cow_ord3->item.key.added_op_seq, 400 * 1e4, 40084000000, 700 * 1e4, 70021600000,
              10100 * 1e4, 0, 0, 0, 1);

  // 边界等于o3
  // 切割o3后的被closing覆盖住的value `floor(110*10030*1e8) + floor(220*10020*1e8) + floor(70*10010*1e8)`
  // 切割后剩余价值+440订单 `floor(330*10010*1e8) - floor(70*10010*1e8)`
  // 切割后剩余价值 440订单不算浮亏
  buy_ob->UpdateCachedMarkPrice(10000 * 1e4);
  CheckHalfOB(buy_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 400 * 1e4,
              cow_ord3->item.key.price, cow_ord3->item.key.added_op_seq, 400 * 1e4, 40084000000, 700 * 1e4, 70021600000,
              10000 * 1e4, 260 * 1e4, 26026000000, 0, 1);
}
