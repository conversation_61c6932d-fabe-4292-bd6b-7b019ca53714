#include <gtest/gtest.h>

#include <memory>

#include "src/biz_worker/service/trade/store/order/cow_order.hpp"
#include "src/biz_worker/service/trade/store/order_manager/half_side_order_book.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/mocks/trading_app_mock.hpp"
#include "test/biz/trade/option/halfob/check_half_ob_test.hpp"
#include "test/biz/trade/option/halfob/make_order_test.hpp"

class HalfObBuyClosingExTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
};

TEST_F(HalfObBuyClosingExTest, TestHalfObBuyClosingEx) {
  auto buy_ob = std::make_shared<store::HalfSideOrderBook>(ESymbol::BTCUSDT, ESide::Buy, EProductType::Futures);

  buy_ob->UpdateCachedPzSideSize(ESide::Sell, 500 * 1e4);
  buy_ob->UpdateCachedMarkPrice(10000 * 1e4);

  // 初始状态halfOB为空
  {
    EXPECT_EQ(buy_ob->TryGetNextFloatingOrder(nullptr), nullptr);
    CheckHalfOB(buy_ob, 0, 0, 0, 500 * 1e4, 0, 0, 0, 0, 0, 0, 10000 * 1e4, 0, 0, 0, 0);
  }

  // 添加第1笔buy
  auto o1 = MakeLimitBuy(10030 * 1e4, 110 * 1e4);
  o1->exec_inst = EExecInst::Close;
  auto cow_ord1 = store::CowOrder::CreateCowOrder(o1);
  buy_ob->InsertOrder(cow_ord1.get());

  EXPECT_EQ(cow_ord1->item.close_on_trigger, true);
  EXPECT_EQ(cow_ord1->item.reduce_only, true);
  // 边界等于o1
  CheckHalfOB(buy_ob, 1, o1->leaves_qty, o1->leaves_value, 500 * 1e4, cow_ord1->item.key.price,
              cow_ord1->item.key.added_op_seq, o1->leaves_qty, o1->leaves_value, 0, 0, 10000 * 1e4, 0, 0, 0, 1);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 110 * 1e4);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 10030 * 110 * 1e4);

  // 添加第2笔buy
  auto o2 = MakeLimitBuy(10020 * 1e4, 220 * 1e4);
  o2->exec_inst = EExecInst::bit_reduce_only;
  auto cow_ord2 = store::CowOrder::CreateCowOrder(o2);

  buy_ob->InsertOrder(cow_ord2.get());

  EXPECT_EQ(cow_ord2->item.reduce_only, true);
  // 边界等于o2
  CheckHalfOB(buy_ob, 2, o1->leaves_qty + o2->leaves_qty, o1->leaves_value + o2->leaves_value, 500 * 1e4,
              cow_ord2->item.key.price, cow_ord2->item.key.added_op_seq, o1->leaves_qty + o2->leaves_qty,
              o1->leaves_value + o2->leaves_value, 0, 0, 10000 * 1e4, 0, 0, 0,
              1);  // o2 不算浮亏
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 10030 * 110 * 1e4 + 10020 * 220 * 1e4);

  // 添加第3笔buy
  auto o3 = MakeLimitBuy(10010 * 1e4, 330 * 1e4);
  auto cow_ord3 = store::CowOrder::CreateCowOrder(o3);
  buy_ob->InsertOrder(cow_ord3.get());

  // 边界等于o3
  // 切割o3后的被closing覆盖住的value `floor(110*10030*1e8) + floor(220*10020*1e8) + floor(170*10010*1e8)`
  // 切割后剩余价值 `floor(330*10010*1e8) - floor(170*10010*1e8)`
  CheckHalfOB(buy_ob, 3, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value, 500 * 1e4, cow_ord3->item.key.price,
              cow_ord3->item.key.added_op_seq, 500 * 1e4, 50094000000, 160 * 1e4, 16016000000, 10000 * 1e4, 160 * 1e4,
              16016000000, 0, 1);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 170 * 1e4);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 10030 * 110 * 1e4 + 10020 * 220 * 1e4 + 10010 * 170 * 1e4);

  // 添加第4笔buy
  auto o4 = MakeLimitBuy(9999 * 1e4, 440 * 1e4);
  auto cow_ord4 = store::CowOrder::CreateCowOrder(o4);
  buy_ob->InsertOrder(cow_ord4.get());

  // 边界等于o3
  // 切割o3后的被closing覆盖住的value 4990618
  // 切割后剩余价值+440订单
  // 切割后剩余价值 440订单不算浮亏
  CheckHalfOB(buy_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 500 * 1e4,
              cow_ord3->item.key.price, cow_ord3->item.key.added_op_seq, 500 * 1e4, 50094000000, 600 * 1e4, 60011600000,
              10000 * 1e4, 160 * 1e4, 16016000000, 0, 1);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 170 * 1e4);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 10030 * 110 * 1e4 + 10020 * 220 * 1e4 + 10010 * 170 * 1e4);

  ////////////////////////////////////////////////////////////////////////////////
  // 移除第3笔buy
  buy_ob->EraseOrder(cow_ord3.get());
  // 边界等于o4
  // 切割o4后的被closing覆盖住的value 4992487
  // 切割后剩余价值+440订单
  // 切割后剩余价值 440订单不算浮亏
  CheckHalfOB(buy_ob, 3, o1->leaves_qty + o2->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o4->leaves_value, 500 * 1e4, cow_ord4->item.key.price,
              cow_ord4->item.key.added_op_seq, 500 * 1e4, 50075300000, 270 * 1e4, 26997300000, 10000 * 1e4, 0, 0, 0, 1);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 10030 * 110 * 1e4 + 10020 * 220 * 1e4);

  ////////////////////////////////////////////////////////////////////////////////
  // 再把第3笔buy添加回去
  buy_ob->InsertOrder(cow_ord3.get());
  // 边界等于o3
  // 切割o3后的被closing覆盖住的value 4990618
  // 切割后剩余价值+440订单
  // 切割后剩余价值 440订单不算浮亏
  CheckHalfOB(buy_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 500 * 1e4,
              cow_ord3->item.key.price, cow_ord3->item.key.added_op_seq, 500 * 1e4, 50094000000, 600 * 1e4, 60011600000,
              10000 * 1e4, 160 * 1e4, 16016000000, 0, 1);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 170 * 1e4);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 10030 * 110 * 1e4 + 10020 * 220 * 1e4 + 10010 * 170 * 1e4);

  ////////////////////////////////////////////////////////////////////////////////
  // 强制将持仓更新为0
  buy_ob->EraseOrder(cow_ord1.get());
  buy_ob->InsertOrder(cow_ord1.get());
  buy_ob->EraseOrder(cow_ord2.get());
  buy_ob->InsertOrder(cow_ord2.get());
  buy_ob->UpdateCachedPzSideSize(ESide::None, 0);
  buy_ob->EraseOrder(cow_ord1.get());
  buy_ob->InsertOrder(cow_ord1.get());
  buy_ob->EraseOrder(cow_ord2.get());
  buy_ob->InsertOrder(cow_ord2.get());

  // 边界重置到最小
  // o1,o2不算浮亏
  // o1,o2不算浮亏, 440订单不算浮亏
  CheckHalfOB(buy_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 0, store::kTopBuyPriceX,
              store::kTopSellPriceX, 0, 0, o3->leaves_qty + o4->leaves_qty, o3->leaves_value + o4->leaves_value,
              10000 * 1e4, o3->leaves_qty, o3->leaves_value, 2, 0);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 0);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 0);

  ////////////////////////////////////////////////////////////////////////////////
  // 强制将持仓更新为400
  buy_ob->UpdateCachedPzSideSize(ESide::Sell, 500 * 1e4);
  buy_ob->UpdateCachedPzSideSize(ESide::Sell, 330 * 1e4);
  buy_ob->UpdateCachedPzSideSize(ESide::Sell, 400 * 1e4);

  // 边界等于o3
  // 切割o3后的被closing覆盖住的value `floor(110*10030*1e8) + floor(220*10020*1e8) + floor(70*10010*1e8)`
  // 切割后剩余价值+440订单 `floor(330*10010*1e8) - floor(70*10010*1e8)`
  // 切割后剩余价值 440订单不算浮亏
  CheckHalfOB(buy_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 400 * 1e4,
              cow_ord3->item.key.price, cow_ord3->item.key.added_op_seq, 400 * 1e4, 40084000000, 700 * 1e4, 70021600000,
              10000 * 1e4, 260 * 1e4, 26026000000, 0, 1);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 70 * 1e4);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 10030 * 110 * 1e4 + 10020 * 220 * 1e4 + 10010 * 70 * 1e4);

  ////////////////////////////////////////////////////////////////////////////////
  // 模拟标记价格浮动
  buy_ob->UpdateCachedMarkPrice(9900 * 1e4);

  // 边界等于o3
  // 切割o3后的被closing覆盖住的value `floor(110*10030*1e8) + floor(220*10020*1e8) + floor(70*10010*1e8)`
  // 切割后剩余价值+440订单 `floor(330*10010*1e8) - floor(70*10010*1e8)`
  // 切割后剩余价值 440订单也算浮亏
  CheckHalfOB(buy_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 400 * 1e4,
              cow_ord3->item.key.price, cow_ord3->item.key.added_op_seq, 400 * 1e4, 40084000000, 700 * 1e4, 70021600000,
              9900 * 1e4, 700 * 1e4, 70021600000, 0, 1);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 70 * 1e4);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 10030 * 110 * 1e4 + 10020 * 220 * 1e4 + 10010 * 70 * 1e4);

  buy_ob->UpdateCachedMarkPrice(10100 * 1e4);

  // 边界等于o3
  // 切割o3后的被closing覆盖住的value `floor(110*10030*1e8) + floor(220*10020*1e8) + floor(70*10010*1e8)`
  // 切割后剩余价值+440订单 `floor(330*10010*1e8) - floor(70*10010*1e8)`
  // 没有浮亏
  CheckHalfOB(buy_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 400 * 1e4,
              cow_ord3->item.key.price, cow_ord3->item.key.added_op_seq, 400 * 1e4, 40084000000, 700 * 1e4, 70021600000,
              10100 * 1e4, 0, 0, 0, 1);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 0);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 0);

  // 边界等于o3
  // 切割o3后的被closing覆盖住的value `floor(110*10030*1e8) + floor(220*10020*1e8) + floor(70*10010*1e8)`
  // 切割后剩余价值+440订单 `floor(330*10010*1e8) - floor(70*10010*1e8)`
  // 切割后剩余价值 440订单不算浮亏
  buy_ob->UpdateCachedMarkPrice(10025 * 1e4);
  CheckHalfOB(buy_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 400 * 1e4,
              cow_ord3->item.key.price, cow_ord3->item.key.added_op_seq, 400 * 1e4, 40084000000, 700 * 1e4, 70021600000,
              10025 * 1e4, 0, 0, 0, 1);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 110 * 1e4);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 10030 * 110 * 1e4);

  /////////////////////////////////////////////////////////////////
  // 添加第5笔buy
  auto o5 = MakeLimitBuy(10005 * 1e4, 100 * 1e4);
  auto cow_ord5 = store::CowOrder::CreateCowOrder(o5);
  buy_ob->InsertOrder(cow_ord5.get());

  buy_ob->UpdateCachedMarkPrice(10000 * 1e4);

  CheckHalfOB(buy_ob, 5, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty + o5->leaves_qty,
              o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value + o5->leaves_value, 400 * 1e4,
              cow_ord3->item.key.price, cow_ord3->item.key.added_op_seq, 400 * 1e4, 40084000000, 800 * 1e4, 80026600000,
              10000 * 1e4, 360 * 1e4, 36031000000, 0, 1);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 70 * 1e4);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 10030 * 110 * 1e4 + 10020 * 220 * 1e4 + 10010 * 70 * 1e4);

  /////////////////////////////////////////////////////////////////
  // 添加第6笔订单，和o3价格相同
  auto o6 = MakeLimitBuy(10010 * 1e4, 20 * 1e4);
  o6->added_op_seq = o6->op_seq;
  auto cow_ord6 = store::CowOrder::CreateCowOrder(o6);
  buy_ob->InsertOrder(cow_ord6.get());

  CheckHalfOB(
      buy_ob, 6, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty + o5->leaves_qty + o6->leaves_qty,
      o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value + o5->leaves_value + o6->leaves_value,
      400 * 1e4, cow_ord3->item.key.price, cow_ord3->item.key.added_op_seq, 400 * 1e4, 40084000000, 820 * 1e4,
      82028600000, 10000 * 1e4, 380 * 1e4, 38033000000, 0, 1);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 70 * 1e4);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 10030 * 110 * 1e4 + 10020 * 220 * 1e4 + 10010 * 70 * 1e4);
}
