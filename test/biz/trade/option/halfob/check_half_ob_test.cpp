#include "test/biz/trade/option/halfob/check_half_ob_test.hpp"

#include <gtest/gtest.h>

void CheckHalfOB(store::HalfSideOrderBook::Ptr half_ob, int32_t btree_len, biz::size_x_t totol_cost_qty,
                 biz::value_e8_t totol_cost_value, biz::size_x_t cached_closing_size, biz::price_x_t edge_price,
                 biz::seq_t edge_added_op_seq, biz::size_x_t closing_box_sum_qty, biz::size_x_t closing_box_sum_value,
                 biz::size_x_t open_extra_qty, biz::value_e8_t open_extra_value, biz::price_x_t mark_price,
                 biz::size_x_t loss_extra_qty, biz::value_e8_t loss_extra_value,
                 biz::size_x_t overflowed_reduce_only_count, biz::size_x_t covered_close_on_trigger_count) {
  EXPECT_EQ(half_ob->size(), btree_len);

  EXPECT_EQ(half_ob->total_cost_qty(), totol_cost_qty);
  EXPECT_EQ(half_ob->total_cost_value(), totol_cost_value);

  EXPECT_EQ(half_ob->cached_closing_size(), cached_closing_size);

  if (edge_price != 0) {
    EXPECT_EQ(half_ob->closing_box_edge_ob_key().price, edge_price);
    EXPECT_EQ(half_ob->closing_box_edge_ob_key().added_op_seq, edge_added_op_seq);
  }

  EXPECT_EQ(half_ob->closing_box_sum_qty_x(), closing_box_sum_qty);
  EXPECT_EQ(half_ob->closing_box_sum_value_e8(), closing_box_sum_value);

  EXPECT_EQ(half_ob->open_extra_qty_x(), open_extra_qty);
  EXPECT_EQ(half_ob->open_extra_value_e8(), open_extra_value);

  EXPECT_EQ(half_ob->cached_mark_price_ob_key().price, mark_price);
  EXPECT_EQ(half_ob->loss_extra_qty_x(), loss_extra_qty);
  EXPECT_EQ(half_ob->loss_extra_value_e8(), loss_extra_value);

  EXPECT_EQ(half_ob->over_flowed_reduce_only_count(), overflowed_reduce_only_count);
  EXPECT_EQ(half_ob->covered_close_on_trigger_count(), covered_close_on_trigger_count);
}
