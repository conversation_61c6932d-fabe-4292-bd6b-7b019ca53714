#include <gtest/gtest.h>

#include <memory>

#include "src/biz_worker/service/trade/store/order/cow_order.hpp"
#include "src/biz_worker/service/trade/store/order_manager/half_side_order_book.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/mocks/trading_app_mock.hpp"
#include "test/biz/trade/option/halfob/check_half_ob_test.hpp"
#include "test/biz/trade/option/halfob/make_order_test.hpp"

class HalfObSellTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
};

TEST_F(HalfObSellTest, go_TestHalfObSell) {
  auto sell_ob = std::make_shared<store::HalfSideOrderBook>(ESymbol::BTCUSDT, ESide::Sell, EProductType::Futures);

  sell_ob->UpdateCachedPzSideSize(ESide::None, 0);
  sell_ob->UpdateCachedMarkPrice(10000 * 1e4);

  // 初始状态halfOB为空
  {
    EXPECT_EQ(sell_ob->TryGetNextFloatingOrder(nullptr), nullptr);
    CheckHalfOB(sell_ob, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10000 * 1e4, 0, 0, 0, 0);
  }

  // 添加第1笔sell
  auto o1 = MakeLimitSell(9993 * 1e4, 100);
  auto cow_ord1 = store::CowOrder::CreateCowOrder(o1);

  sell_ob->InsertOrder(cow_ord1.get());

  CheckHalfOB(sell_ob, 1, o1->leaves_qty, o1->leaves_value, 0, 0, 0, 0, 0, o1->leaves_qty, o1->leaves_value,
              10000 * 1e4, o1->leaves_qty, o1->leaves_value, 0, 0);

  // 添加第2笔sell
  auto o2 = MakeLimitSell(10005 * 1e4, 100);
  auto cow_ord2 = store::CowOrder::CreateCowOrder(o2);

  sell_ob->InsertOrder(cow_ord2.get());
  CheckHalfOB(sell_ob, 2, o1->leaves_qty + o2->leaves_qty, o1->leaves_value + o2->leaves_value, 0, 0, 0, 0, 0,
              o1->leaves_qty + o2->leaves_qty, o1->leaves_value + o2->leaves_value, 10000 * 1e4, o1->leaves_qty,
              o1->leaves_value, 0, 0);  // o2 不算浮亏

  // 移除第2笔sell
  sell_ob->EraseOrder(cow_ord2.get());
  CheckHalfOB(sell_ob, 1, o1->leaves_qty, o1->leaves_value, 0, 0, 0, 0, 0, o1->leaves_qty, o1->leaves_value,
              10000 * 1e4, o1->leaves_qty, o1->leaves_value, 0, 0);

  // 移除第1笔sell
  sell_ob->EraseOrder(cow_ord1.get());
  CheckHalfOB(sell_ob, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10000 * 1e4, 0, 0, 0, 0);
}
