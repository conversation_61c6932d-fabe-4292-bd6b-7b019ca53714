#include "eu_sbu_match.hpp"  // NOLINT

#include <unistd.h>

#include <bbase/common/utils/time_utils.hpp>
#include <iostream>
#include <memory>
#include <nlohmann/json.hpp>
#include <string>
#include <vector>

#include "application/application.hpp"
#include "application/global_var_manager.hpp"
#include "config/data_type.hpp"
#include "data/const.hpp"
#include "data/type/biz_type.hpp"
#include "enums/eopplatform/op_platform.pb.h"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(EUSbuMatchTest, open_api_v5_create_order) {
  GTEST_SKIP() << "自测欧洲站成交使用";
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order", uid, "open_api_v5_create_order", 5, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000",
                                      "0");
  deposit_resp = user1.process(deposit_build2.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.002", "3.01");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "3");

    auto resp1 = user1.create_order(build2.Build());
    ASSERT_NE(resp1->order_link_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }
}

std::shared_ptr<tmock::CTradeAppMock> EUSbuMatchTest::te;
