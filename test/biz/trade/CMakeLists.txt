project(trade_ut)

# enable_testing()
find_package(GTest CONFIG REQUIRED)

# project trade_ut下是不受类访问控制的
add_compile_options(-fno-access-control -g)

# # src files
file(GLOB_RECURSE PROJECT_TEST_SRC_FILES "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp")
list(REMOVE_ITEM PROJECT_TEST_SRC_FILES "${CMAKE_CURRENT_SOURCE_DIR}/future/*/*.cpp")
list(REMOVE_ITEM PROJECT_TEST_SRC_FILES "${CMAKE_CURRENT_SOURCE_DIR}/future/*/*/*.cpp")
list(REMOVE_ITEM PROJECT_TEST_SRC_FILES "${CMAKE_CURRENT_SOURCE_DIR}/spot/*/*.cpp")
list(REMOVE_ITEM PROJECT_TEST_SRC_FILES "${CMAKE_CURRENT_SOURCE_DIR}/feature/*/*.cpp")


file(GLOB_RECURSE MOCK_SOURCES "${CMAKE_SOURCE_DIR}/test/mocks/hdts/*.cpp" "${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/*.cpp" "${CMAKE_SOURCE_DIR}/test/mocks/nacos_client/*.cpp" "${CMAKE_SOURCE_DIR}/test/mocks/nacos_config/nacos_config_mock.cpp")
list(REMOVE_ITEM MOCK_SOURCES "${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/futures_match_sdk/Fbu.cpp")
list(REMOVE_ITEM MOCK_SOURCES "${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/option_match_sdk/Obu.cpp")
list(REMOVE_ITEM MOCK_SOURCES "${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/spot_match_sdk/Sbu.cpp")
list(REMOVE_ITEM MOCK_SOURCES "${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/futures_spread_match_sdk/FuturesSpread.cpp")

# 非test/biz目录且非src/目录的源文件追加在这里
set(PROJECT_SRC_FILES ${PROJECT_TEST_SRC_FILES} ${MOCK_SOURCES})
set(trade_ut_bin trade_ut.bin)

# # 构建可执行文件
add_executable(${trade_ut_bin}
    ${PROJECT_SRC_FILES})

# # 这里添加需要include的目录
target_include_directories(${trade_ut_bin} PRIVATE ${CMAKE_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk
    ${Boost_INCLUDE_DIRS}
    ${googletest_INCLUDE_DIR})

if (APPLE)
    execute_process(COMMAND uname -m
        OUTPUT_VARIABLE CMAKE_OSX_ARCHITECTURES
        OUTPUT_STRIP_TRAILING_WHITESPACE)
endif (APPLE)

if (APPLE)
    if ("${CMAKE_OSX_ARCHITECTURES}" STREQUAL "arm64")
        SET(CROSS_LIB
            ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/futures_match_sdk/lib/mac_arm/libfutures_match_sdk.a
            ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/option_match_sdk/lib/mac_arm/liboption_match_sdk.a
            ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/spot_match_sdk/lib/mac_arm/libspot_match_sdk.a
            ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/spot_match_sdk/lib/mac_arm/libspot_match_sdk_eu.a
            ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/option_match_sdk/lib/mac_arm/liboption_match_sdk_eu.a
            ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/futures_match_sdk/lib/mac_arm/libfutures_match_sdk_eu.a
            ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/futures_spread_match_sdk/lib/mac_arm/libfutures_spread_match_sdk.a)
    else ()
        SET(CROSS_LIB
            ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/futures_match_sdk/lib/mac_intel/libfutures_match_sdk.a
            ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/option_match_sdk/lib/mac_intel/liboption_match_sdk.a
            ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/spot_match_sdk/lib/mac_intel/libspot_match_sdk.a)
    endif ()
else ()
    SET(CROSS_LIB
        ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/futures_match_sdk/lib/linux/libfutures_match_sdk.a
        ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/option_match_sdk/lib/linux/liboption_match_sdk.a
        ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/spot_match_sdk/lib/linux/libspot_match_sdk.a
        ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/futures_match_sdk/lib/linux/libfutures_match_sdk_eu.a
        ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/option_match_sdk/lib/linux/liboption_match_sdk_eu.a
        ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/spot_match_sdk/lib/linux/libspot_match_sdk_eu.a
        ${CMAKE_SOURCE_DIR}/test/mocks/match_sdk/futures_spread_match_sdk/lib/linux/libfutures_spread_match_sdk.a)
endif ()


set(GRPC_REFLECTION "gRPC::grpc++_reflection")

target_link_libraries(${trade_ut_bin} PRIVATE
    ${GRPC_REFLECTION}
    GTest::gtest
    GTest::gmock
    ${GPERFTOOLS}
    proto_gen
    bbase::bcommon
    bbase::bshare
    ${CROSS_LIB}
    ${LIB_WHITEBOX}
    $<TARGET_OBJECTS:application_obj>
    $<TARGET_OBJECTS:application_version_obj>
    $<TARGET_OBJECTS:biz_worker_obj>
    $<TARGET_OBJECTS:adl_worker_obj>
    $<TARGET_OBJECTS:common_obj>
    $<TARGET_OBJECTS:config_obj>
    $<TARGET_OBJECTS:cross_worker_obj>
    $<TARGET_OBJECTS:data_obj>
    $<TARGET_OBJECTS:grpc_worker_obj>
    $<TARGET_OBJECTS:lending_risk_notify_obj>
    $<TARGET_OBJECTS:margin_request_obj>
    $<TARGET_OBJECTS:monitor_obj>
    $<TARGET_OBJECTS:post_worker_obj>
    $<TARGET_OBJECTS:seq_mark_worker_obj>
    $<TARGET_OBJECTS:trading_dump_worker_obj>
    $<TARGET_OBJECTS:trigger_worker_obj>
    $<TARGET_OBJECTS:quote_merge_worker_obj>
    $<TARGET_OBJECTS:persist_worker_obj>
    $<TARGET_OBJECTS:open_interest_obj>)

install(TARGETS ${trade_ut_bin}
    COMPONENT ${trade_ut_bin}
    PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
    RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/test/trade_ut)

# # make test
include(GoogleTest)
gtest_add_tests(TARGET ${trade_ut_bin})
