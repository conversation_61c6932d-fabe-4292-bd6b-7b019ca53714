#pragma once

#include <gtest/gtest.h>
#include <svc/uta_engine/combination/resp/site_api_create_order.pb.h>

#include <boost/uuid/random_generator.hpp>
#include <boost/uuid/uuid_io.hpp>
#include <map>
#include <memory>
#include <string>
#include <vector>

#include "msg_check_helper.hpp"  // NOLINT
#include "svc/uta_engine/openapi/req/get_trade_fee_rate_v5.pb.h"
#include "svc/uta_engine/openapi/resp/get_trade_fee_rate_v5.pb.h"
#include "svc/uta_engine/spot/req/internal_api_open_innovation.pb.h"
#include "svc/uta_engine/spot/req/internal_api_replace_order.pb.h"
#include "svc/uta_engine/spot/req/internal_api_set_fee_rate.pb.h"
#include "svc/uta_engine/spot/req/internal_api_set_max_margin_leverage.pb.h"
#include "svc/uta_engine/spot/req/internal_api_set_supported_symbol_list.pb.h"
#include "svc/uta_engine/spot/resp/internal_api_open_innovation.pb.h"
#include "svc/uta_engine/spot/resp/internal_api_replace_order.pb.h"
#include "svc/uta_engine/spot/resp/internal_api_set_fee_rate.pb.h"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/mocks/trading_app_mock.hpp"

struct RpcContext {
  int32_t RetCode();
  std::string RetMsg();
  std::vector<svc::uta_engine::resp::BatchCreateOrderRespV5ExtInfoItem> RetBatchResp();
  std::string DebugMetaData();

  grpc::ClientContext ct;
};

// stub stands for one client, all related interfaces are created here
class stub {
 public:
  stub(std::shared_ptr<tmock::CTradeAppMock> te, int64_t uid) {
    m_te = te;
    m_uid = uid;
  }

  std::shared_ptr<tmock::CTradeAppMock> m_te;
  int64_t m_uid;

  // asset

  // position
  // cancel order
  std::shared_ptr<svc::uta_engine::resp::CancelOrderRespV5> cancel_order(svc::uta_engine::req::CancelOrderReqV5);
  std::shared_ptr<svc::uta_engine::resp::CancelOrderRespV5> cancel_order(svc::uta_engine::req::CancelOrderReqV5,
                                                                         RpcContext& ct);
  std::shared_ptr<svc::uta_engine::option::BatchCancelOrderResponse> option_site_cancel_all(
      svc::uta_engine::option::SiteCancelAllRequest req);
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> cancel_order(
      svc::uta_engine::spot::req::ClientCancelOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> cancel_order(
      svc::uta_engine::spot::req::ClientCancelOrderRequest, RpcContext& ct);

  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> cancel_order(
      svc::uta_engine::spot::req::CancelPlanOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> cancel_order(
      svc::uta_engine::spot::req::CancelPlanOrderRequest, RpcContext& ct);

  std::shared_ptr<svc::uta_engine::spot::resp::CancelOrderResponse> cancel_order(
      svc::uta_engine::spot::req::CancelOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::CancelOrderResponse> cancel_order(
      svc::uta_engine::spot::req::CancelOrderRequest, RpcContext& ct);

  std::shared_ptr<svc::uta_engine::spot::resp::CancelTriggerOrderResponse> cancel_order(
      svc::uta_engine::spot::req::CancelTriggerOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::CancelTriggerOrderResponse> cancel_order(
      svc::uta_engine::spot::req::CancelTriggerOrderRequest, RpcContext& ct);

  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> cancel_order(
      svc::uta_engine::spot::req::MergeCancelOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> cancel_order(
      svc::uta_engine::spot::req::MergeCancelOrderRequest, RpcContext& ct);

  // replace order
  std::shared_ptr<svc::uta_engine::resp::ReplaceOrderRespV5> replace_order(svc::uta_engine::req::ReplaceOrderReqV5);
  std::shared_ptr<svc::uta_engine::resp::ReplaceOrderRespV5> replace_order(svc::uta_engine::req::ReplaceOrderReqV5,
                                                                           RpcContext&);
  std::shared_ptr<svc::uta_engine::resp::CreateOrderRespV5> create_order(svc::uta_engine::req::CreateOrderReqV5);
  std::shared_ptr<svc::uta_engine::resp::CreateOrderRespV5> create_order(svc::uta_engine::req::CreateOrderReqV5 req,
                                                                         RpcContext& meta_data_resp,
                                                                         std::string op_from = "");
  std::shared_ptr<svc::uta_engine::resp::BatchCreateOrderRespV5> batch_create_order(
      svc::uta_engine::req::BatchCreateOrderReqV5);
  std::shared_ptr<svc::uta_engine::resp::BatchCreateOrderRespV5> batch_create_order(
      svc::uta_engine::req::BatchCreateOrderReqV5, RpcContext& ct);
  std::shared_ptr<svc::uta_engine::resp::BatchReplaceOrderRespV5> batch_replace_order(
      svc::uta_engine::req::BatchReplaceOrderReqV5 req);
  std::shared_ptr<svc::uta_engine::resp::BatchReplaceOrderRespV5> batch_replace_order(
      svc::uta_engine::req::BatchReplaceOrderReqV5, RpcContext& ct);
  std::shared_ptr<svc::uta_engine::resp::BatchCancelOrderRespV5> batch_cancel_order(
      svc::uta_engine::req::BatchCancelOrderReqV5 req);
  std::shared_ptr<svc::uta_engine::resp::BatchCancelOrderRespV5> batch_cancel_order(
      svc::uta_engine::req::BatchCancelOrderReqV5 req, RpcContext& ct);
  std::shared_ptr<svc::uta_engine::option::BatchCancelOrderResponse> site_api_batch_cancel_order(
      svc::uta_engine::option::SiteBatchCancelOrderRequest req);
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCreateOrderResponse> create_order(
      svc::uta_engine::spot::req::ClientCreateOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCreateOrderResponse> create_order(
      svc::uta_engine::spot::req::ClientCreateOrderRequest, RpcContext& ct);

  std::shared_ptr<svc::uta_engine::spot::resp::ClientCreateOrderResponse> create_order(
      svc::uta_engine::spot::req::CrossCreateOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCreateOrderResponse> create_order(
      svc::uta_engine::spot::req::CrossCreateOrderRequest, RpcContext& ct);

  std::shared_ptr<svc::uta_engine::spot::resp::CreateOrderResponse> create_order(
      svc::uta_engine::spot::req::CreateOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::CreateOrderResponse> create_order(
      svc::uta_engine::spot::req::CreateOrderRequest, RpcContext& ct);

  std::shared_ptr<svc::uta_engine::spot::resp::PlanSpotCreateOrderResponse> create_order(
      svc::uta_engine::spot::req::PlanSpotCreateOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::PlanSpotCreateOrderResponse> create_order(
      svc::uta_engine::spot::req::PlanSpotCreateOrderRequest, RpcContext& ct);

  std::shared_ptr<svc::uta_engine::spot::resp::ReplaceOrderResponse> replace_order(
      svc::uta_engine::spot::req::ReplaceOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::ReplaceOrderResponse> replace_order(
      svc::uta_engine::spot::req::ReplaceOrderRequest, RpcContext& ct);

  std::shared_ptr<svc::uta_engine::spot::resp::ReplacePlanOrderResponse> replace_plan_order(
      svc::uta_engine::spot::req::ReplacePlanOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::ReplacePlanOrderResponse> replace_plan_order(
      svc::uta_engine::spot::req::ReplacePlanOrderRequest, RpcContext& ct);

  std::shared_ptr<svc::uta_engine::option::BatchOrderResponse> option_site_api_batch_create_order(
      svc::uta_engine::option::SiteBatchOrderRequest req);

  std::shared_ptr<svc::uta_engine::option::OrderResponse> OptionSiteApiCreateOrder(
      svc::uta_engine::option::SiteOrderRequest req);

  std::shared_ptr<svc::uta_engine::option::BatchOrderResponse> OptionBatchSiteApiCreateOrder(
      svc::uta_engine::option::SiteBatchOrderRequest req);

  std::shared_ptr<svc::uta_engine::option::ReplaceOrderResponse> OptionSiteApiReplaceOrder(
      svc::uta_engine::option::SiteReplaceOrderRequest req);

  std::shared_ptr<svc::uta_engine::option::CancelOrderResponse> OptionSiteApiCancelOrder(
      svc::uta_engine::option::SiteCancelOrderRequest req);

  std::shared_ptr<svc::uta_engine::option::MmpQueryUserMmpResponse> OptionMmpQueryUserMmpLabel(
      svc::uta_engine::option::MmpQueryUserMmpRequest req);

  std::shared_ptr<svc::uta_engine::resp::GetMmpStateRespV5> GetMmpStateV5(svc::uta_engine::req::GetMmpStateReqV5 req);

  std::shared_ptr<svc::uta_engine::option::InnerCommonResponse> OptionInnerMmpInactive(
      svc::uta_engine::option::InnerMmpInactiveRequest req);

  std::shared_ptr<svc::uta_engine::option::InnerOrderResponse> OptionInnerCreateOrder(
      svc::uta_engine::option::InnerOrderRequest req);

  std::shared_ptr<svc::uta_engine::option::InnerCommonResponse> OptionInnerSetUserTradingBaseCoin(
      svc::uta_engine::option::InnerSetUserTradingBaseCoinRequest req);
  std::shared_ptr<svc::uta_engine::option::response::OptionSetFeeRateResponse> OptionInnerSetFeeRate(
      svc::uta_engine::option::request::OptionSetFeeRateRequest req);
  std::shared_ptr<svc::uta_engine::option::response::OptionRefreshUserSettingResponse> OptionRefreshUserSetting(
      svc::uta_engine::option::request::OptionRefreshUserSettingRequest req);

  std::shared_ptr<svc::trading::TradingResp> process(svc::trading::TradingReq);
  std::shared_ptr<svc::uta_engine::DebugResponse> process(svc::uta_engine::DebugRequest);
  std::shared_ptr<svc::uta_engine::DebugResponse> FetchUserStore(const svc::uta_engine::DebugRequest&);
  std::shared_ptr<svc::uta_engine::SeqMarkResponse> update_seq_mark(svc::uta_engine::SeqMarkRequest);
  std::shared_ptr<svc::uta_engine::SeqMarkQueryResponse> query_seq_mark(svc::uta_engine::SeqMarkQueryRequest);

  std::shared_ptr<svc::unified_v2::UnifiedV2ResultDTO> process(svc::unified_v2::UnifiedV2RequestDTO);

  std::shared_ptr<svc::uta_engine::resp::CancelAllOrderRespV5> cancel_all(svc::uta_engine::req::CancelAllOrderReqV5);
  std::shared_ptr<svc::uta_engine::resp::CancelAllOrderRespV5> cancel_all(svc::uta_engine::req::CancelAllOrderReqV5,
                                                                          RpcContext& ct);
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelOrderResponseV2> cancel_all(
      svc::uta_engine::spot::req::BatchCancelOrderRequestV2);

  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> batch_cancel_order(
      svc::uta_engine::spot::req::ClientBatchCancelOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> batch_cancel_order(
      svc::uta_engine::spot::req::ClientBatchCancelOrderRequest, RpcContext& ct);

  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse> batch_cancel_order(
      svc::uta_engine::spot::req::BatchCancelPlanOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse> batch_cancel_order(
      svc::uta_engine::spot::req::BatchCancelPlanOrderRequest, RpcContext& ct);

  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse> batch_cancel_order(
      svc::uta_engine::spot::req::MergeBatchCancelOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse> batch_cancel_order(
      svc::uta_engine::spot::req::MergeBatchCancelOrderRequest, RpcContext& ct);

  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelOrderByIdsResponse> batch_cancel_order(
      svc::uta_engine::spot::req::BatchCancelOrderByIdsRequest);

  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelTriggerOrderByIdsResponse> batch_cancel_order(
      svc::uta_engine::spot::req::BatchCancelTriggerOrderByIdsRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelOrderByUidResponse> batch_cancel_order(
      svc::uta_engine::spot::req::BatchCancelOrderByUidRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::SetFeeRateResponse> set_fee_rate(
      svc::uta_engine::spot::req::SetFeeRateRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::SetFeeRuleResp> set_fee_rule(svc::uta_engine::spot::req::SetFeeRuleReq);
  std::shared_ptr<svc::uta_engine::spot::resp::OpenInnovationResponse> open_innovation(
      svc::uta_engine::spot::req::OpenInnovationRequest);

  std::shared_ptr<svc::uta_engine::spot::resp::SetSupportedSymbolListResponse> set_supported_symbol_list(
      svc::uta_engine::spot::req::SetSupportedSymbolListRequest);

  std::shared_ptr<svc::uta_engine::spot::resp::SetMaxMarginLeverageResponse> set_max_margin_leverage(
      svc::uta_engine::spot::req::SetMaxMarginLeverageRequest);

  std::shared_ptr<svc::uta_engine::resp::GetTradeFeeRateRespV5> get_fee_rate(svc::uta_engine::req::GetTradeFeeRateReqV5,
                                                                             RpcContext& ct);

  std::shared_ptr<svc::uta_engine::resp::SetRiskIdResponseV5> set_risk_id(svc::uta_engine::req::SetRiskIdReqV5,
                                                                          RpcContext&);
  std::shared_ptr<svc::uta_engine::resp::EmptyResp> confirm_pending_mmr(svc::uta_engine::req::ConfirmPendingMmrReqV5,
                                                                        RpcContext&);
  std::shared_ptr<svc::uta_engine::resp::EmptyResp> set_auto_add_margin(
      svc::uta_engine::req::SetAutoAddMarginRequestV5);
  std::shared_ptr<svc::uta_engine::resp::EmptyResp> switch_position_mode(
      svc::uta_engine::req::SwitchPositionModeRequestV5, RpcContext& ct);
  std::shared_ptr<svc::uta_engine::resp::AddMarginResponseV5> add_margin(svc::uta_engine::req::AddMarginRequestV5,
                                                                         RpcContext& ct);
  std::shared_ptr<svc::uta_engine::block_trade::resp::PreOccupyResp> block_trade_pre_occupy(
      svc::uta_engine::block_trade::req::PreOccupyReq);
  std::shared_ptr<svc::uta_engine::block_trade::resp::CreateBlockTradeResp> block_trade_create(
      svc::uta_engine::block_trade::req::CreateBlockTradeReq);
  std::shared_ptr<svc::uta_engine::block_trade::resp::QueryOrderResp> block_trade_query(
      svc::uta_engine::block_trade::req::QueryOrderReq);
  std::shared_ptr<svc::uta_engine::resp::SetTpSlModeRespV5> set_tp_sl_mode(svc::uta_engine::req::SetTpSlModeReqV5 req,
                                                                           RpcContext& ct);
  std::shared_ptr<svc::uta_engine::resp::EmptyResp> set_leverage(svc::uta_engine::req::SetLeverageReqV5);
  std::shared_ptr<svc::uta_engine::resp::EmptyResp> set_leverage(svc::uta_engine::req::SetLeverageReqV5 req,
                                                                 RpcContext& ct);

  std::shared_ptr<svc::uta_engine::resp::EmptyResp> set_tp_sl_ts(svc::uta_engine::req::SetTpSlTsReqV5 req,
                                                                 RpcContext& ct);
  std::shared_ptr<svc::uta_engine::strategy_trade::resp::PreCheckResp> strategy_trade_pre_check(
      svc::uta_engine::strategy_trade::req::PreCheckReq req);
  std::shared_ptr<svc::uta_engine::strategy_trade::resp::DDHStrategyPreCheckResp> ddh_strategy_pre_check(
      svc::uta_engine::strategy_trade::req::DDHStrategyPreCheckReq req);
  std::shared_ptr<svc::uta_engine::strategy_trade::resp::DDHStrategyCreateOrderResp> ddh_strategy_create_order(
      svc::uta_engine::strategy_trade::req::DDHStrategyCreateOrderReq req);
  std::shared_ptr<svc::uta_engine::spot::resp::InnerReplaceOrderResponse> replace_order(
      svc::uta_engine::spot::req::InnerReplaceOrderRequest);
  std::shared_ptr<svc::uta_engine::DebugUserBlockResponse> user_block(svc::uta_engine::DebugUserBlockRequest);
  std::shared_ptr<svc::uta_engine::DebugSymbolConfigResponse> fetch_symbol_config(
      svc::uta_engine::DebugSymbolConfigRequest);
  std::shared_ptr<svc::uta_engine::CleanupHistorySymbolResponse> cleanup_history_symbol(
      svc::uta_engine::CleanupHistorySymbolRequest);
  std::shared_ptr<svc::uta_engine::DebugResponse> process_debug_margin_request(
      svc::uta_engine::DebugMarginRequest req, int expect_ret = 0, std::map<std::string, std::string> metadata = {});
  std::shared_ptr<svc::uta_engine::DebugSDKConfigResponse> set_sdk_config(svc::uta_engine::DebugSDKConfigRequest);

  int deposit(std::string amount, biz::coin_t coin);
  int switch_margin_mode(EAccountMode mode);
  std::shared_ptr<svc::unified_v2::UnifiedV2ResultDTO> SwitchMarginMode(EAccountMode mode);
  std::shared_ptr<svc::unified_v2::UnifiedV2ResultDTO> RiskClosePz();
  bool ReCalcEvent(bool is_final = false);
  std::shared_ptr<svc::uta_engine::DebugResponse> DebugUserData();

  std::shared_ptr<svc::uta_engine::spot::resp::SetAllowTradeConfigResponse> set_allow_trade_config(
      svc::uta_engine::spot::req::SetAllowTradeConfigRequest);

  std::shared_ptr<svc::uta_engine::spot::resp::ClientPreCreateOrderResponse> pre_create_order(
      svc::uta_engine::spot::req::ClientCreateOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::ClientPreCreateOrderResponse> pre_create_order(
      svc::uta_engine::spot::req::ClientCreateOrderRequest, RpcContext& ct);
  std::shared_ptr<svc::uta_engine::spot::resp::PlanSpotPreCreateOrderResponse> pre_create_order(
      svc::uta_engine::spot::req::PlanSpotCreateOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::PlanSpotPreCreateOrderResponse> pre_create_order(
      svc::uta_engine::spot::req::PlanSpotCreateOrderRequest, RpcContext& ct);
  std::shared_ptr<svc::uta_engine::spot::resp::ClientPreCreateOrderResponse> pre_create_order(
      svc::uta_engine::spot::req::CrossCreateOrderRequest);
  std::shared_ptr<svc::uta_engine::spot::resp::ClientPreCreateOrderResponse> pre_create_order(
      svc::uta_engine::spot::req::CrossCreateOrderRequest, RpcContext& ct);
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCreateOrderRespSiteApiResult> batch_create_order_siteapi(
      svc::uta_engine::spot::req::BatchCreateOrderSiteApi);
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCreateOrderRespSiteApiResult> batch_create_order_siteapi(
      svc::uta_engine::spot::req::BatchCreateOrderSiteApi, RpcContext& ct);
  std::shared_ptr<svc::uta_engine::spot::resp::UpdateAveragePriceAndAmountSiteApiResult>
      update_average_price_and_amount(svc::uta_engine::spot::req::UpdateAveragePriceAndAmountSiteApi);
  std::shared_ptr<svc::uta_engine::spot::resp::UpdateAveragePriceAndAmountSiteApiResult>
  update_average_price_and_amount(svc::uta_engine::spot::req::UpdateAveragePriceAndAmountSiteApi, RpcContext& ct);
  std::shared_ptr<svc::uta_engine::combination::resp::CreateOrderResponse> comb_siteapi_create_order(
      svc::uta_engine::combination::req::CreateOrderRequest req, RpcContext& ct) const;
  std::shared_ptr<svc::uta_engine::resp::CreateOrderRespV5> comb_openapi_create_order(
      svc::uta_engine::req::CombinationCreateOrderReqV5 req, RpcContext& ct) const;
  std::shared_ptr<svc::uta_engine::combination::resp::ReplaceOrderResponse> comb_siteapi_replace_order(
      svc::uta_engine::combination::req::ReplaceOrderRequest req, RpcContext& ct) const;
  std::shared_ptr<svc::uta_engine::resp::ReplaceOrderRespV5> comb_openapi_replace_order(
      svc::uta_engine::req::CombinationReplaceOrderReqV5 req, RpcContext& ct) const;
  std::shared_ptr<svc::uta_engine::combination::resp::CancelOrderResponse> comb_siteapi_cancel_order(
      svc::uta_engine::combination::req::CancelOrderRequest req, RpcContext& ct) const;
  std::shared_ptr<svc::uta_engine::resp::CancelOrderRespV5> comb_openapi_cancel_order(
      svc::uta_engine::req::CombinationCancelOrderReqV5 req, RpcContext& ct) const;
  std::shared_ptr<svc::uta_engine::combination::resp::CancelAllOrderResponse> comb_siteapi_cancel_all(
      svc::uta_engine::combination::req::CancelAllOrderRequest req, RpcContext& ct) const;
  std::shared_ptr<svc::uta_engine::resp::CancelAllOrderRespV5> comb_openapi_cancel_all(
      svc::uta_engine::req::CombinationCancelAllOrderReqV5 req, RpcContext& ct) const;
};
