#pragma once
#include <gtest/gtest.h>

#include <bbase/common/decimal/decimal.hpp>
#include <memory>
#include <string>

#include "data/const.hpp"
#include "enums/eexecinst/exec_inst.pb.h"
#include "enums/eexectype/exec_type.pb.h"
#include "enums/eqtytype/qty_type.pb.h"
#include "src/data/type/biz_type.hpp"
#include "svc/trading/trading_service.pb.h"
#include "svc/unified_v2/unified_v2_result_dto.pb.h"
#include "svc/uta_engine/uta_engine_open_api_service.pb.h"

/*
 * TradingResultDTO是比较复杂的嵌套结构，频繁的在嵌套结构中获取数据
 * 做判定，会写出大量重复的代码，参考trading原有的设计模式，对常用的Message，
 * 提供一个MessageChecker，大致如下
 *
 * class MessageChecker{
 *  // Check成功后返回自身，方便写出链式的调用语句
 *  MessageChecker Check1(para1, para2);
 *  MessageChecker Check2(para1, para2);
 *
 *  //拿到一个子结构，需要的话，可以通过参数过滤
 *  SubMessageChecker RefSubMessage(para1);
 *
 *  Message* msg{nullptr};
 * }
 *
 * class SubMessageChecker{
 *  SubMessageChecker Check3(para1, para2);
 *  SubMessageChecker Check4(para1, para2);
 *
 *  SubMessage* msg{nullptr};
 * }
 *
 * 这样就可以方便的写出下面这样的语句。
 * MessageChecker msg_checker(msg);
 * msg_checker.Check1().Check2().RefSubMessage().Check3()
 *
 */

// models.tradingdto.TransactDTO

class TransactDTOChecker {
 public:
  explicit TransactDTOChecker(const models::tradingdto::TransactDTO* msg) { m_msg = msg; }

  auto Check(int64_t uid, int64_t qty) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(qty, m_msg->qty_x());
    return *this;
  }

  auto CheckQty(int64_t uid, int64_t qty) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(qty, m_msg->qty_x());
    return *this;
  }

  auto CheckExecQty(int64_t uid, int64_t exec_qty) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(exec_qty, m_msg->exec_qty_x());
    return *this;
  }

  auto CheckCumQtyX(int64_t uid, int64_t qty) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(qty, m_msg->cum_qty_x());
    return *this;
  }

  auto CheckExecType(int64_t uid, EExecType exec_type) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(exec_type, m_msg->exec_type());
    return *this;
  }

  auto CheckCreateType(int64_t uid, ECreateType create_type) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(create_type, m_msg->create_type());
    return *this;
  }

  ECreateType GetCreateType() { return m_msg->create_type(); }

  auto CheckClosePnl(int64_t uid, int64_t close_pnl) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(close_pnl, m_msg->closed_pnl_e8());
    return *this;
  }

  auto CheckTakeOverChangeAmount(int64_t uid, int64_t taken_over_change_amount_e8) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(taken_over_change_amount_e8, m_msg->taken_over_change_amount_e8());
    return *this;
  }

  auto CheckQtyTypeAndValue(EQtyType qty_type, int64_t qty_type_value) {
    EXPECT_EQ(qty_type, m_msg->qty_type());
    EXPECT_EQ(qty_type_value, m_msg->qty_type_value_e0());
    return *this;
  }

  auto CheckPrice(int64_t uid, int64_t price) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(price, m_msg->price_x());
    return *this;
  }

  /**
   * 检查齐全iv订单中的iv值
   * @param uid
   * @param order_iv
   * @return
   */
  auto CheckIv(int64_t uid, int64_t order_iv) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(order_iv, m_msg->order_iv_e8());
    return *this;
  }

  /**
   * 改单场景下check两个价格不等
   * @param uid
   * @param order_iv
   * @return
   */
  auto CheckPriceChanged(int64_t uid) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_FALSE(m_msg->price_x() == m_msg->orig_price_x());
    return *this;
  }

  auto CheckExecInst(int64_t uid, EExecInst exec_inst) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(exec_inst, m_msg->exec_inst());
    return *this;
  }

  auto CheckIsRO() {
    bool const is_ro = ((m_msg->exec_inst() & EExecInst::bit_reduce_only) != 0);
    EXPECT_EQ(is_ro, true);
    return *this;
  }

  auto CheckIsCOT(bool expect) {
    bool const is_cot = ((m_msg->exec_inst() & EExecInst::Close) != 0);
    EXPECT_EQ(is_cot, expect);
    return *this;
  }

  auto CheckTimeInForce(ETimeInForce tif) {
    EXPECT_EQ(tif, m_msg->time_in_force());
    return *this;
  }

  auto CheckIsTradeBanned() {
    bool const is_trade_banned = ((m_msg->exec_inst() & EExecInst::bit_trading_banned) != 0);
    EXPECT_EQ(is_trade_banned, true);
    return *this;
  }

  auto CheckStatus(EOrderStatus os, ECrossStatus cs) {
    EXPECT_EQ(os, m_msg->order_status());
    EXPECT_EQ(cs, m_msg->cross_status());
    return *this;
  }

  auto CheckOpponentUid(biz::user_id_t op_uid) {
    EXPECT_EQ(op_uid, m_msg->opponent_user_id());
    return *this;
  }

  auto CheckSide(ESide side) {
    EXPECT_EQ(side, m_msg->side());
    return *this;
  }

  auto CheckSMP(std::uint8_t smp_type, std::int64_t smp_group) {
    EXPECT_EQ(smp_type, m_msg->smp_type());
    EXPECT_EQ(smp_group, m_msg->smp_group());
    return *this;
  }

  auto CheckStopOrder(EStopOrderType stop_type, ETriggerBy tg_by, biz::price_x_t tg_px,
                      EPriceDirection price_direction) {
    EXPECT_EQ(stop_type, m_msg->stop_order_type());
    EXPECT_EQ(tg_by, m_msg->trigger_by());
    EXPECT_EQ(tg_px, m_msg->trigger_price_x());
    EXPECT_EQ(price_direction, m_msg->expected_direction());
    return *this;
  }

  /**
   * !!!!!该接口废弃，使用CheckTpSLOrder
   */
  auto ChecckTpSlOrder(EStopOrderType stop_order_type, ETriggerBy trigger_by, EOrderType order_type,
                       biz::price_x_t trigger_price_x, int64_t qty = -1) {
    EXPECT_EQ(m_msg->stop_order_type(), stop_order_type);
    EXPECT_EQ(m_msg->trigger_by(), trigger_by);
    EXPECT_EQ(m_msg->order_type(), order_type);
    EXPECT_EQ(m_msg->trigger_price_x(), trigger_price_x);
    if (qty >= 0) {
      EXPECT_EQ(m_msg->qty_x(), qty);
    }
    return *this;
  }

  /**
   * 检测活动单中携带的止盈信息
   */
  auto CheckOrderWithTp(ETriggerBy tp_trigger_by, biz::price_x_t take_profit_x, EOrderType tp_order_type,
                        biz::price_x_t take_profit_limit_x = 0) {
    EXPECT_EQ(m_msg->tp_trigger_by(), tp_trigger_by);
    EXPECT_EQ(m_msg->take_profit_x(), take_profit_x);
    EXPECT_EQ(m_msg->tp_order_type(), tp_order_type);
    EXPECT_EQ(m_msg->take_profit_limit_x(), take_profit_limit_x);
    return *this;
  }
  /**
   * 检测活动单中携带的止损信息
   */
  auto CheckOrderWithSl(ETriggerBy sl_trigger_by, biz::price_x_t stop_loss_x, EOrderType sl_order_type,
                        biz::price_x_t stop_loss_limit_x = 0) {
    EXPECT_EQ(m_msg->sl_trigger_by(), sl_trigger_by);
    EXPECT_EQ(m_msg->stop_loss_x(), stop_loss_x);
    EXPECT_EQ(m_msg->sl_order_type(), sl_order_type);
    EXPECT_EQ(m_msg->stop_loss_limit_x(), stop_loss_limit_x);
    return *this;
  }

  /**
   * 检测止盈止损订单信息
   */
  auto CheckTpSLOrder(ETriggerBy trigger_by, ETpSlMode tp_sl_mode, biz::price_x_t trigger_price_x,
                      EStopOrderType stop_order_type, EOrderType order_type, int64_t qty_x = -1) {
    EXPECT_EQ(m_msg->trigger_by(), trigger_by);
    EXPECT_EQ(ETpSlMode(m_msg->tp_sl_mode()), tp_sl_mode);
    EXPECT_EQ(m_msg->trigger_price_x(), trigger_price_x);
    EXPECT_EQ(m_msg->stop_order_type(), stop_order_type);
    EXPECT_EQ(m_msg->order_type(), order_type);
    if (qty_x >= 0) {
      EXPECT_EQ(m_msg->qty_x(), qty_x);
    }
    return *this;
  }

  /*
   * 检查这笔单子是否造成仓位翻转
   * */
  auto CheckTerm(std::int32_t before_pz_term, std::int32_t after_pz_term) {
    EXPECT_EQ(m_msg->before_pz_term(), before_pz_term);
    EXPECT_EQ(m_msg->after_pz_term(), after_pz_term);
    return *this;
  }

  const models::tradingdto::TransactDTO* m_msg{nullptr};
};

class SpotTransactDTOChecker {
 public:
  explicit SpotTransactDTOChecker(const models::tradingdto::UnifySpotTransactDTO* msg) { m_msg = msg; }
  auto Check(int64_t uid, std::string qty, std::string amount) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(qty, m_msg->qty());
    EXPECT_EQ(amount, m_msg->amount());
    return *this;
  }

  auto CheckQty(int64_t uid, std::string qty) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(qty, m_msg->qty());
    return *this;
  }

  auto CheckAmount(int64_t uid, std::string amount) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(amount, m_msg->amount());
    return *this;
  }

  auto CheckPrice(int64_t uid, std::string price) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(price, m_msg->price());
    return *this;
  }

  auto CheckOpponentOrderId(int64_t order_id) {
    EXPECT_EQ(order_id, m_msg->opponent_order_id());
    return *this;
  }

  auto CheckOpponentUserId(int64_t uid) {
    EXPECT_EQ(uid, m_msg->opponent_user_id());
    return *this;
  }

  auto CheckOpponentAccountId(int64_t aid) {
    EXPECT_EQ(aid, m_msg->opponent_account_id());
    return *this;
  }

  auto CheckExecResult(int64_t uid, std::string exec_price, std::string exec_qty, std::string exec_amount,
                       std::string exec_fee) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(exec_price, m_msg->exec_price());
    EXPECT_EQ(exec_qty, m_msg->exec_qty());
    EXPECT_EQ(exec_amount, m_msg->exec_amount());
    EXPECT_EQ(exec_fee, m_msg->exec_fee());
    return *this;
  }

  auto CheckStatus(EOrderStatus os, ECrossStatus cs) {
    EXPECT_EQ(os, m_msg->order_status());
    EXPECT_EQ(cs, m_msg->cross_status());
    return *this;
  }

  auto CheckCreateType(int64_t uid, ECreateType create_type) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(create_type, m_msg->create_type());
    return *this;
  }

  auto CheckLeaves(std::string leaves_qty, std::string leaves_amount) {
    EXPECT_EQ(leaves_qty, m_msg->leaves_qty());
    EXPECT_EQ(leaves_amount, m_msg->leaves_amount());
    return *this;
  }

  auto CheckMarketUnit(EMarketUnit market_unit) {
    EXPECT_EQ(market_unit, m_msg->market_unit());
    return *this;
  }

  auto CheckOrderType(EOrderType order_type) {
    EXPECT_EQ(order_type, m_msg->order_type());
    return *this;
  }

  auto CheckTimeInForce(ETimeInForce time_in_force) {
    EXPECT_EQ(time_in_force, m_msg->time_in_force());
    return *this;
  }

  auto CheckSide(int64_t uid, ESide side) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(side, m_msg->side());
    return *this;
  }

  auto CheckTriggerPrice(int64_t uid, std::string trigger_price) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(trigger_price, m_msg->trigger_price());
    return *this;
  }

  auto CheckOrderType(int64_t uid, EOrderType order_type) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(order_type, m_msg->order_type());
    return *this;
  }

  auto CheckTpTriggerPrice(std::string tp_trigger_price) {
    EXPECT_EQ(tp_trigger_price, m_msg->tp_trigger_price());
    return *this;
  }

  auto CheckSlTriggerPrice(std::string sl_trigger_price) {
    EXPECT_EQ(sl_trigger_price, m_msg->sl_trigger_price());
    return *this;
  }

  auto CheckTpLimitPrice(std::string tp_limit_price) {
    EXPECT_EQ(tp_limit_price, m_msg->tp_limit_price());
    return *this;
  }

  auto CheckSlLimitPrice(std::string sl_limit_price) {
    EXPECT_EQ(sl_limit_price, m_msg->sl_limit_price());
    return *this;
  }

  auto CheckTpOrderType(EOrderType tp_order_type) {
    EXPECT_EQ(tp_order_type, m_msg->tp_order_type());
    return *this;
  }

  auto CheckSlOrderType(EOrderType sl_order_type) {
    EXPECT_EQ(sl_order_type, m_msg->sl_order_type());
    return *this;
  }

  auto CheckBusinessLine(int32_t business_line) {
    EXPECT_EQ(business_line, m_msg->business_line());
    return *this;
  }

  auto CheckLeverageOrderType(int32_t leverage_order_type) {
    EXPECT_EQ(leverage_order_type, m_msg->leverage_order_type());
    return *this;
  }

  auto CheckIsLeverage(bool is_leverage) {
    EXPECT_EQ(is_leverage, m_msg->is_leverage());
    return *this;
  }

  auto CheckCxlRejectReason(ECxlRejReason cxl_rej_reason) {
    return *this;
    EXPECT_EQ(cxl_rej_reason, m_msg->cxl_rej_reason());
  }
  const models::tradingdto::UnifySpotTransactDTO* m_msg{nullptr};
};

class PositionDTOChecker {
 public:
  explicit PositionDTOChecker(const models::tradingdto::PositionDTO* msg) { m_msg = msg; }

  auto CheckLv(int64_t lv, int64_t bv2c, int64_t sv2c, int64_t bv2mm, int64_t sv2mm) {
    EXPECT_EQ(lv, m_msg->leverage_e2());
    EXPECT_EQ(bv2c, m_msg->buy_value_to_cost_e8());
    EXPECT_EQ(sv2c, m_msg->sell_value_to_cost_e8());
    EXPECT_EQ(bv2mm, m_msg->buy_value_to_mm_e8());
    EXPECT_EQ(sv2mm, m_msg->sell_value_to_mm_e8());
    return *this;
  }

  auto CheckRiskId(int64_t risk_id) {
    EXPECT_EQ(risk_id, m_msg->risk_id());
    return *this;
  }

  auto CheckPositionMMWithFee(int64_t mm_with_fee) {
    EXPECT_EQ(mm_with_fee, m_msg->position_mm_with_fee_e8());
    return *this;
  }

  auto CheckUid(int64_t user_id) {
    EXPECT_EQ(user_id, m_msg->user_id());
    return *this;
  }

  auto CheckSize(int64_t pz_size) {
    EXPECT_EQ(pz_size, m_msg->size_x());
    return *this;
  }

  auto CheckSide(ESide pz_side) {
    EXPECT_EQ(pz_side, m_msg->side());
    return *this;
  }

  auto CheckPzMode(EPositionMode pz_mode) {
    EXPECT_EQ(pz_mode, m_msg->mode());
    return *this;
  }

  auto CheckPzIndex(EPositionIndex pz_idx) {
    EXPECT_EQ(pz_idx, m_msg->position_idx());
    return *this;
  }

  auto CheckPzStatus(EPositionStatus pz_status) {
    EXPECT_EQ(pz_status, m_msg->position_status());
    return *this;
  }

  auto CheckValue(biz::value_x_t pz_value) {
    EXPECT_EQ(pz_value, m_msg->value_e8());
    return *this;
  }

  auto CheckFreeSize(int64_t pz_size) {
    EXPECT_EQ(pz_size, m_msg->free_qty_x());
    return *this;
  }

  auto CheckBustPrice(int64_t bust_price) {
    EXPECT_EQ(bust_price, m_msg->bust_price_x());
    return *this;
  }

  auto CheckLiqPrice(int64_t liq_price) {
    EXPECT_EQ(liq_price, m_msg->liq_price_x());
    return *this;
  }

  auto CheckPb(int64_t pb) {
    EXPECT_EQ(pb, m_msg->position_balance_e8());
    return *this;
  }

  auto CheckSettlePrice(int64_t settle_price) {
    EXPECT_EQ(settle_price, m_msg->settle_price_x());
    return *this;
  }

  auto CheckTpSlTs(ETriggerBy tp_trigger_by, biz::price_x_t take_profit_x, ETriggerBy sl_trigger_by,
                   biz::price_x_t stop_loss_x, biz::price_x_t trailing_stop_x, biz::price_x_t activation_price_x) {
    EXPECT_EQ(m_msg->tp_trigger_by(), tp_trigger_by);
    EXPECT_EQ(m_msg->take_profit_x(), take_profit_x);
    EXPECT_EQ(m_msg->sl_trigger_by(), sl_trigger_by);
    EXPECT_EQ(m_msg->stop_loss_x(), stop_loss_x);
    EXPECT_EQ(m_msg->trailing_stop_x(), trailing_stop_x);
    EXPECT_EQ(m_msg->activation_price_x(), activation_price_x);
    return *this;
  }

  auto CheckTpSlInfo(int64_t tp_order_num, int64_t sl_order_num, int64_t tp_free_size_x, int64_t sl_free_size_x) {
    EXPECT_EQ(m_msg->tp_order_num(), tp_order_num);
    EXPECT_EQ(m_msg->sl_order_num(), sl_order_num);
    if (tp_free_size_x >= 0) {
      EXPECT_EQ(m_msg->tp_free_size_x(), tp_free_size_x);
    }
    if (sl_free_size_x >= 0) {
      EXPECT_EQ(m_msg->sl_free_size_x(), sl_free_size_x);
    }

    return *this;
  }

  auto CheckAutoMargin(bool open) {
    EXPECT_EQ(open, m_msg->is_auto_add_margin());
    return *this;
  }

  int64_t GetUid() { return m_msg->user_id(); }
  int64_t GetSettlePrice() { return m_msg->settle_price_x(); }
  const models::tradingdto::PositionDTO* m_msg{nullptr};
};

class UserSettingDTOChecker {
 public:
  explicit UserSettingDTOChecker(const models::usersettingdto::PerUserSettingData* msg) { m_msg = msg; }

  auto CheckSymbolList(biz::coin_t coin, bool enabled_perpetual, bool enabled_future, int64_t future_list_size) {
    auto user_setting = m_msg->future_per_coin_setting().find(coin);
    EXPECT_NE(user_setting, m_msg->future_per_coin_setting().end());
    EXPECT_EQ(user_setting->second.enable_symbol_list(), enabled_perpetual);
    EXPECT_EQ(user_setting->second.enable_future(), enabled_future);
    EXPECT_EQ(user_setting->second.support_future_size(), future_list_size);
    return *this;
  }

  auto CheckDefaultMaxLeverage(biz::coin_t coin, int64_t max_leverage_e2, int64_t btc_max_leverage_e2,
                               int64_t eth_max_leverage_e2) {
    auto user_setting = m_msg->future_per_coin_setting().find(coin);
    EXPECT_NE(user_setting, m_msg->future_per_coin_setting().end());
    EXPECT_EQ(user_setting->second.max_leverage_e2(), max_leverage_e2);
    EXPECT_EQ(user_setting->second.future_btc_max_leverage_e2(), btc_max_leverage_e2);
    EXPECT_EQ(user_setting->second.future_eth_max_leverage_e2(), eth_max_leverage_e2);
    return *this;
  }
  auto CheckSymbolMaxLeverage(biz::symbol_t symbol, int64_t max_leverage_e2) {
    auto future_symbol_config = m_msg->future_symbol_setting_dto().future_symbol_config_map().find(symbol);
    if (future_symbol_config != m_msg->future_symbol_setting_dto().future_symbol_config_map().end()) {
      auto future_symbol_config_value = future_symbol_config->second;
      if (future_symbol_config_value.has_max_leverage()) {
        EXPECT_EQ(future_symbol_config_value.max_leverage(), max_leverage_e2);
      } else {
        EXPECT_EQ(max_leverage_e2, 0);
      }
    } else {
      EXPECT_EQ(max_leverage_e2, 0);
    }

    return *this;
  }
  auto CheckSymbolMaxLeverageListSize(int64_t list_size) {
    auto user_setting = m_msg->future_symbol_setting_dto().future_symbol_config_map();

    int32_t max_leverage_size = 0;
    for (auto item : user_setting) {
      if (item.second.has_max_leverage()) {
        max_leverage_size++;
      }
    }

    EXPECT_EQ(max_leverage_size, list_size);

    return *this;
  }

  auto CheckSymbolMaxSymbolValue(int64_t list_size) {
    auto user_setting = m_msg->future_symbol_setting_dto().future_symbol_config_map();

    int32_t max_size = 0;
    for (auto item : user_setting) {
      if (item.second.has_max_leverage()) {
        max_size++;
      }
    }

    EXPECT_EQ(max_size, list_size);

    return *this;
  }

  auto CheckAccountMode(int32_t account_mode) {
    EXPECT_EQ(static_cast<EAccountMode>(account_mode), m_msg->accountmode());

    return *this;
  }

  const models::usersettingdto::PerUserSettingData* m_msg{nullptr};
};

class WalletDTOChecker {
 public:
  explicit WalletDTOChecker(const models::assetdto::UnderlyingData* msg,
                            const models::accountdto::AccountInfoDTO* account) {
    m_msg = msg;
    m_account = account;
  }

  auto CheckVersion(int64_t version) {
    EXPECT_EQ(version, m_account->account_version());
    return *this;
  }

  auto CheckCoin(biz::coin_t coin) {
    EXPECT_EQ(coin, m_msg->settle_coin());
    return *this;
  }

  auto CheckWalletAssetFrozne(biz::money_t asset_frozen) {
    EXPECT_EQ(asset_frozen, biz::money_t(m_msg->wallet().asset_frozen()));
    return *this;
  }

  auto CheckWalletLiability(biz::money_t liability) {
    EXPECT_EQ(liability, biz::money_t(m_msg->wallet().liability()));
    return *this;
  }

  auto CheckWalletCashBalance(biz::money_t cash_balance) {
    EXPECT_EQ(cash_balance, biz::money_t(m_msg->wallet().cash_balance()));
    return *this;
  }

  auto CheckAB(biz::money_t amount) {
    EXPECT_EQ(amount, biz::money_t(m_msg->wallet().available_balance()));
    return *this;
  }

  auto CheckImOverFlow() {
    EXPECT_GE(biz::money_t(m_msg->wallet().asset_total_im()), decimal::kZero);
    EXPECT_GE(biz::money_t(m_msg->wallet().available_balance()), decimal::kZero);
    return *this;
  }

  const models::assetdto::UnderlyingData* m_msg{nullptr};
  const models::accountdto::AccountInfoDTO* m_account{nullptr};
};

class AccountInfoDTOChecker {
 public:
  explicit AccountInfoDTOChecker(const models::accountdto::AccountInfoDTO* account) { m_account = account; }

  auto CheckAccountStatus(int64_t account_status) {
    EXPECT_EQ(account_status, m_account->account_status());
    return *this;
  }

  auto CheckLiqAction(int64_t liq_action) {
    EXPECT_EQ(liq_action, m_account->liq_step());
    return *this;
  }

  const models::accountdto::AccountInfoDTO* m_account{nullptr};
};

class TradingMarginResultChecker {
 public:
  explicit TradingMarginResultChecker(const svc::unified_v2::res::TradingMarginResult* msg) { m_msg = msg; }

  TransactDTOChecker RefRelatedOrderByOrderLinkId(std::string order_link_id) {
    for (int i = 0; i < m_msg->related_orders_size(); i++) {
      auto& order = m_msg->related_orders(i);
      if (order.order_link_id() == order_link_id) {
        return TransactDTOChecker(&order);
      }
    }
    return TransactDTOChecker(nullptr);
  }

  TransactDTOChecker RefRelatedOrderByOrderId(std::string order_id) {
    for (int i = 0; i < m_msg->related_orders_size(); i++) {
      auto& order = m_msg->related_orders(i);
      if (order.order_id() == order_id) {
        return TransactDTOChecker(&order);
      }
    }
    return TransactDTOChecker(nullptr);
  }

  TransactDTOChecker RefRelatedOrderByIndex(int32_t index) {
    if (m_msg->related_orders_size() <= index) {
      return TransactDTOChecker(nullptr);
    }

    auto& order = m_msg->related_orders(index);
    return TransactDTOChecker(&order);
  }

  TransactDTOChecker RefRelatedFillByOrderId(std::string order_id) {
    for (int i = 0; i < m_msg->related_fills_size(); i++) {
      auto& order = m_msg->related_fills(i);
      if (order.order_id() == order_id) {
        return TransactDTOChecker(&order);
      }
    }
    return TransactDTOChecker(nullptr);
  }

  TransactDTOChecker RefRelatedFillByIndex(int32_t index) {
    if (m_msg->related_fills_size() <= index) {
      return TransactDTOChecker(nullptr);
    }

    auto& fill = m_msg->related_fills(index);
    return TransactDTOChecker(&fill);
  }

  int RefRelatedFillSize() { return m_msg->related_fills_size(); }

  PositionDTOChecker RefRelatedPosition(int idx) {
    if (idx != -1 && m_msg->affected_positions().size() >= idx + 1) {
      return PositionDTOChecker(&m_msg->affected_positions(idx));
    }

    return PositionDTOChecker(nullptr);
  }

  PositionDTOChecker RefRelatedPositionByPositionIdx(int pz_idx) {
    for (int i = 0; i < m_msg->affected_positions().size(); i++) {
      if (m_msg->affected_positions(i).position_idx() == pz_idx) {
        return PositionDTOChecker(&m_msg->affected_positions(i));
      }
    }

    return PositionDTOChecker(nullptr);
  }

  PositionDTOChecker RefRelatedPositionBySymbol(biz::symbol_t symbol, EPositionIndex pz_index) {
    int idx = -1;
    int cnt = 0;
    for (auto& pz : m_msg->affected_positions()) {
      if (pz.symbol() == symbol && pz.position_idx() == pz_index) {
        idx = cnt;
        break;
      }
      cnt++;
    }

    return RefRelatedPosition(idx);
  }

  PositionDTOChecker RefRelatedPositionByPositionIdxAndSymbol(int pz_idx, biz::symbol_t symbol) {
    for (int i = 0; i < m_msg->affected_positions().size(); i++) {
      if (m_msg->affected_positions(i).position_idx() == pz_idx && m_msg->affected_positions(i).symbol() == symbol) {
        return PositionDTOChecker(&m_msg->affected_positions(i));
      }
    }

    return PositionDTOChecker(nullptr);
  }

  int RefRelatedPositionSize() { return m_msg->affected_positions().size(); }

  const svc::unified_v2::res::TradingMarginResult* m_msg{nullptr};
};

class SpotTradingMarginResultChecker {
 public:
  explicit SpotTradingMarginResultChecker(const svc::unified_v2::res::TradingMarginResult* msg) { m_msg = msg; }

  SpotTransactDTOChecker RefRelatedOrderByOrderLinkId(std::string order_link_id) {
    for (int i = 0; i < m_msg->related_spot_orders_size(); i++) {
      auto& order = m_msg->related_spot_orders(i);
      if (order.order_link_id() == order_link_id) {
        return SpotTransactDTOChecker(&order);
      }
    }
    return SpotTransactDTOChecker(nullptr);
  }

  SpotTransactDTOChecker RefRelatedOrderByIndex(int32_t index) {
    if (m_msg->related_spot_orders_size() <= index) {
      return SpotTransactDTOChecker(nullptr);
    }

    auto& order = m_msg->related_spot_orders(index);
    return SpotTransactDTOChecker(&order);
  }

  SpotTransactDTOChecker RefRelatedOrderByOrderId(std::string order_id) {
    for (int i = 0; i < m_msg->related_spot_orders_size(); i++) {
      auto& order = m_msg->related_spot_orders(i);
      if (order.order_id() == order_id) {
        return SpotTransactDTOChecker(&order);
      }
    }
    return SpotTransactDTOChecker(nullptr);
  }

  SpotTransactDTOChecker RefRelatedFills(int32_t index) {
    for (int i = 0; i < m_msg->related_spot_fills_size(); i++) {
      if (i == index) {
        auto& order = m_msg->related_spot_fills(i);
        return SpotTransactDTOChecker(&order);
      }
    }
    return SpotTransactDTOChecker(nullptr);
  }

  SpotTransactDTOChecker RefRelatedOrderByStopOrderType(EStopOrderType stop_order_type) {
    for (int i = 0; i < m_msg->related_spot_orders_size(); i++) {
      auto& order = m_msg->related_spot_orders(i);
      if (order.stop_order_type() == stop_order_type) {
        return SpotTransactDTOChecker(&order);
      }
    }
    return SpotTransactDTOChecker(nullptr);
  }

  const svc::unified_v2::res::TradingMarginResult* m_msg{nullptr};
};

class AssetResultChecker {
 public:
  explicit AssetResultChecker(const svc::unified_v2::res::AssetMarginResult* msg) { m_msg = msg; }

  WalletDTOChecker RefRelatedWallet(biz::coin_t coin) {
    auto iter = m_msg->affected_wallets().find(coin);
    if (iter != m_msg->affected_wallets().end()) {
      return WalletDTOChecker(&iter->second, &m_msg->account_info());
    }
    return WalletDTOChecker(nullptr, nullptr);
  }

  UserSettingDTOChecker RefRelatedUserSetting() {
    if (m_msg->has_per_user_setting_data()) {
      return UserSettingDTOChecker(&m_msg->per_user_setting_data());
    }
    return UserSettingDTOChecker(nullptr);
  }

  AccountInfoDTOChecker RefRelatedAccountInfo() {
    if (m_msg->has_account_info()) {
      return AccountInfoDTOChecker(&m_msg->account_info());
    }
    return AccountInfoDTOChecker(nullptr);
  }

  const svc::unified_v2::res::AssetMarginResult* m_msg{nullptr};
};

class CombTransactDTOChecker {
 public:
  explicit CombTransactDTOChecker(const models::tradingdto::CombinationTransactDTO* msg) { m_msg = msg; }

  auto CheckUid(int64_t uid) {
    EXPECT_EQ(uid, m_msg->user_id());
    return *this;
  }
  auto CheckQty(biz::size_x_t qty) {
    EXPECT_EQ(qty, m_msg->qty_x());
    return *this;
  }

  auto CheckPrice(biz::price_x_t price) {
    EXPECT_EQ(price, m_msg->price_x());
    return *this;
  }

  auto CheckOpponentOrderId(biz::order_link_id_t order_link_id) {
    EXPECT_EQ(order_link_id.GetValue(), m_msg->opponent_order_id().c_str());
    return *this;
  }

  auto CheckOpponentUserId(biz::user_id_t uid) {
    EXPECT_EQ(uid, m_msg->opponent_user_id());
    return *this;
  }

  auto CheckExecResult(int64_t uid, biz::price_x_t exec_price, biz::size_x_t exec_qty,
                       enums::eexectype::ExecType exec_type) {
    EXPECT_EQ(uid, m_msg->user_id());
    EXPECT_EQ(exec_price, m_msg->exec_price_x());
    EXPECT_EQ(exec_qty, m_msg->exec_qty_x());
    EXPECT_EQ(exec_type, m_msg->exec_type());
    return *this;
  }

  auto CheckStatus(ECombOrderStatus os, ECombCrossStatus cs) {
    EXPECT_EQ(os, m_msg->order_status());
    EXPECT_EQ(cs, m_msg->cross_status());
    return *this;
  }

  auto CheckCancelType(ECancelType cancel_type) {
    EXPECT_EQ(cancel_type, m_msg->cancel_type());
    return *this;
  }

  auto CheckLeavesQty(biz::size_x_t leaves_qty) {
    EXPECT_EQ(leaves_qty, m_msg->leaves_qty_x());
    return *this;
  }

  auto CheckOrderType(EOrderType order_type) {
    EXPECT_EQ(order_type, m_msg->order_type());
    return *this;
  }

  auto CheckTimeInForce(ETimeInForce time_in_force) {
    EXPECT_EQ(time_in_force, m_msg->time_in_force());
    return *this;
  }

  auto CheckSide(ESide side) {
    EXPECT_EQ(side, m_msg->side());
    return *this;
  }

  auto CheckCxlRejectReason(ECxlRejReason cxl_rej_reason) {
    return *this;
    EXPECT_EQ(cxl_rej_reason, m_msg->cxl_rej_reason());
  }

  const models::tradingdto::CombinationTransactDTO* m_msg{nullptr};
};

class CombTradingMarginResultChecker {
 public:
  explicit CombTradingMarginResultChecker(const svc::unified_v2::res::TradingMarginResult* msg) { m_msg = msg; }

  CombTransactDTOChecker RefRelatedOrderByIndex(int32_t index) {
    if (m_msg->related_combination_orders_size() <= index) {
      return CombTransactDTOChecker(nullptr);
    }

    auto& order = m_msg->related_combination_orders(index);
    return CombTransactDTOChecker(&order);
  }

  CombTransactDTOChecker RefRelatedOrderByOrderLinkId(const biz::order_link_id_t& order_link_id) const {
    for (int i = 0; i < m_msg->related_combination_orders_size(); i++) {
      auto& order = m_msg->related_combination_orders(i);
      if (order.order_link_id().compare(order_link_id.GetValue()) == 0) {
        return CombTransactDTOChecker(&order);
      }
    }
    return CombTransactDTOChecker(nullptr);
  }

  CombTransactDTOChecker RefRelatedFills(int32_t index) const {
    for (int i = 0; i < m_msg->related_combination_fills_size(); i++) {
      if (i == index) {
        auto& order = m_msg->related_combination_fills(i);
        return CombTransactDTOChecker(&order);
      }
    }
    return CombTransactDTOChecker(nullptr);
  }

  CombTransactDTOChecker RefRelatedFillsByOrderLinkId(const biz::order_link_id_t& order_link_id) const {
    for (int i = 0; i < m_msg->related_combination_fills_size(); i++) {
      auto& order = m_msg->related_combination_fills(i);
      if (order.order_link_id().compare(order_link_id.GetValue()) == 0) {
        return CombTransactDTOChecker(&order);
      }
    }
    return CombTransactDTOChecker(nullptr);
  }

  const svc::unified_v2::res::TradingMarginResult* m_msg{nullptr};
};

class UnifiedV2ResultDTOChecker {
 public:
  explicit UnifiedV2ResultDTOChecker(std::shared_ptr<svc::unified_v2::UnifiedV2ResultDTO> msg) { m_msg = msg; }

  UnifiedV2ResultDTOChecker& CheckResultExist() {
    EXPECT_NE(m_msg.get(), nullptr);
    return *this;
  }

  UnifiedV2ResultDTOChecker& CheckUid(int64_t uid) {
    EXPECT_EQ(uid, m_msg->header().user_id());
    return *this;
  }

  UnifiedV2ResultDTOChecker& CheckErrorCode(int64_t err_code) {
    EXPECT_EQ(err_code, m_msg->ret_code());
    return *this;
  }

  AssetResultChecker RefAssetMarginResult() { return AssetResultChecker(&m_msg->asset_margin_result()); }

  TradingMarginResultChecker RefFutureMarginResult() {
    return TradingMarginResultChecker(&m_msg->futures_margin_result());
  }

  TradingMarginResultChecker RefOptionMarginResult() {
    return TradingMarginResultChecker(&m_msg->options_margin_result());
  }

  SpotTradingMarginResultChecker RefSpotMarginResult() {
    return SpotTradingMarginResultChecker(&m_msg->spot_margin_result());
  }

  CombTradingMarginResultChecker RefCombMarginResult() {
    return CombTradingMarginResultChecker(&m_msg->combination_margin_result());
  }

  std::shared_ptr<svc::unified_v2::UnifiedV2ResultDTO> m_msg;
};
