#include "test/biz/trade/lib/stub.hpp"

#include <bbase/common/utils/string_utils.hpp>
#include <memory>
#include <nlohmann/json.hpp>
#include <vector>

#include "msg_check_helper.hpp"  // NOLINT
#include "svc/uta_engine/combination/resp/site_api_create_order.pb.h"
#include "svc/uta_engine/openapi/req/get_trade_fee_rate_v5.pb.h"
#include "svc/uta_engine/openapi/resp/add_margin_v5.pb.h"
#include "svc/uta_engine/openapi/resp/get_trade_fee_rate_v5.pb.h"
#include "svc/uta_engine/spot/req/internal_api_open_innovation.pb.h"
#include "svc/uta_engine/spot/req/internal_api_replace_order.pb.h"
#include "svc/uta_engine/spot/req/internal_api_set_fee_rate.pb.h"
#include "svc/uta_engine/spot/req/internal_api_set_max_margin_leverage.pb.h"
#include "svc/uta_engine/spot/req/internal_api_set_supported_symbol_list.pb.h"
#include "svc/uta_engine/spot/resp/internal_api_open_innovation.pb.h"
#include "svc/uta_engine/spot/resp/internal_api_replace_order.pb.h"
#include "svc/uta_engine/spot/resp/internal_api_set_fee_rate.pb.h"

std::string RpcContext::DebugMetaData() {
  const auto& meta_data = ct.GetServerInitialMetadata();
  fmt::memory_buffer buf{};
  for (const auto& item : meta_data) {
    buf.append(item.first);
    buf.append(std::string_view(":"));
    buf.append(item.second);
    buf.append(std::string_view(";"));
  }
  return {buf.data(), buf.size()};
}

int32_t RpcContext::RetCode() {
  const auto& meta_data = ct.GetServerInitialMetadata();
  auto it = meta_data.find(config::ConstConfig::kNextResponseCodes);
  if (it == meta_data.end()) {
    return -1;
  }

  auto ret_code =
      static_cast<std::int32_t>(std::strtoll(std::string{it->second.data(), it->second.size()}.c_str(), nullptr, 10));

  if (errno != ERANGE) {
    return ret_code;
  }

  return -1;
}

std::string RpcContext::RetMsg() {
  const auto& meta_data = ct.GetServerInitialMetadata();
  auto it = meta_data.find(config::ConstConfig::kNextResponseMsg);
  if (it == meta_data.end()) {
    return {};
  }

  return {it->second.data(), it->second.size()};
}

std::vector<svc::uta_engine::resp::BatchCreateOrderRespV5ExtInfoItem> RpcContext::RetBatchResp() {
  const auto& meta_data = ct.GetServerInitialMetadata();
  std::vector<std::string> ret_codes;
  std::vector<std::string> ret_msgs;
  std::vector<std::string> ret_ext_infos;
  std::vector<svc::uta_engine::resp::BatchCreateOrderRespV5ExtInfoItem> result_vec;
  for (const auto& item : meta_data) {
    if (item.first == config::ConstConfig::kNextResponseCodes) {
      ret_codes.push_back(std::string(item.second.data(), item.second.length()));
    }
    if (item.first == config::ConstConfig::kNextResponseMsg) {
      ret_msgs.push_back(std::string(item.second.data(), item.second.length()));
    }
    if (item.first == config::ConstConfig::kNextResponseExtras) {
      ret_ext_infos.push_back(std::string(item.second.data(), item.second.length()));
    }
  }

  for (size_t idx = 0; idx < ret_codes.size(); idx++) {
    svc::uta_engine::resp::BatchCreateOrderRespV5ExtInfoItem item;
    item.set_code(atoi(ret_codes.at(idx).c_str()));
    item.set_msg(ret_msgs.at(idx));
    result_vec.push_back(item);
  }
  return result_vec;
}

std::shared_ptr<svc::uta_engine::resp::CreateOrderRespV5> stub::create_order(
    svc::uta_engine::req::CreateOrderReqV5 req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::resp::CreateOrderRespV5> resp =
      std::make_shared<svc::uta_engine::resp::CreateOrderRespV5>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  nlohmann::json j;
  j["eopf"] = "3";
  context.AddMetadata("extension", j.dump());

  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  //  context.set_deadline(deadline);

  int ret = m_te->CreateOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::CreateOrderRespV5> stub::create_order(svc::uta_engine::req::CreateOrderReqV5 req,
                                                                             RpcContext& ct, std::string op_from) {
  std::shared_ptr<svc::uta_engine::resp::CreateOrderRespV5> resp =
      std::make_shared<svc::uta_engine::resp::CreateOrderRespV5>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  nlohmann::json j;
  j["eopf"] = "3";
  j["opfrom"] = op_from;
  ct.ct.AddMetadata("extension", j.dump());

  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  //  context.set_deadline(deadline);

  int ret = m_te->CreateOrder(ct.ct, req, *resp.get());
  if (ct.ct.GetServerInitialMetadata().find("next-response-codes") != ct.ct.GetServerInitialMetadata().end() &&
      ct.ct.GetServerInitialMetadata().find("next-response-codes")->second == "10006") {
    EXPECT_EQ(ret, 10006);
  } else {
    EXPECT_EQ(ret, 0);
  }

  return resp;
}

std::shared_ptr<svc::uta_engine::resp::BatchCreateOrderRespV5> stub::batch_create_order(
    svc::uta_engine::req::BatchCreateOrderReqV5 req) {
  std::shared_ptr<svc::uta_engine::resp::BatchCreateOrderRespV5> resp =
      std::make_shared<svc::uta_engine::resp::BatchCreateOrderRespV5>();
  grpc::ClientContext context;

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  //  context.set_deadline(deadline);

  int ret = m_te->BatchCreateOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::BatchCreateOrderRespV5> stub::batch_create_order(
    svc::uta_engine::req::BatchCreateOrderReqV5 req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::resp::BatchCreateOrderRespV5> resp =
      std::make_shared<svc::uta_engine::resp::BatchCreateOrderRespV5>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  //  context.set_deadline(deadline);

  int ret = m_te->BatchCreateOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::BatchReplaceOrderRespV5> stub::batch_replace_order(
    svc::uta_engine::req::BatchReplaceOrderReqV5 req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::resp::BatchReplaceOrderRespV5> resp =
      std::make_shared<svc::uta_engine::resp::BatchReplaceOrderRespV5>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  //  context.set_deadline(deadline);

  int ret = m_te->BatchReplaceOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::BatchReplaceOrderRespV5> stub::batch_replace_order(
    svc::uta_engine::req::BatchReplaceOrderReqV5 req, RpcContext& ct) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::resp::BatchReplaceOrderRespV5> resp =
      std::make_shared<svc::uta_engine::resp::BatchReplaceOrderRespV5>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  //  context.set_deadline(deadline);

  int ret = m_te->BatchReplaceOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::BatchCancelOrderRespV5> stub::batch_cancel_order(
    svc::uta_engine::req::BatchCancelOrderReqV5 req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::resp::BatchCancelOrderRespV5> resp =
      std::make_shared<svc::uta_engine::resp::BatchCancelOrderRespV5>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //   std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  // context.set_deadline(deadline);

  int ret = m_te->BatchCancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::BatchCancelOrderRespV5> stub::batch_cancel_order(
    svc::uta_engine::req::BatchCancelOrderReqV5 req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::resp::BatchCancelOrderRespV5> resp =
      std::make_shared<svc::uta_engine::resp::BatchCancelOrderRespV5>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //   std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  // context.set_deadline(deadline);

  int ret = m_te->BatchCancelOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::option::BatchCancelOrderResponse> stub::site_api_batch_cancel_order(
    svc::uta_engine::option::SiteBatchCancelOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::option::BatchCancelOrderResponse> resp =
      std::make_shared<svc::uta_engine::option::BatchCancelOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //   std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  // context.set_deadline(deadline);

  int ret = m_te->SiteApiBatchCancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::option::BatchOrderResponse> stub::option_site_api_batch_create_order(
    svc::uta_engine::option::SiteBatchOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::option::BatchOrderResponse> resp =
      std::make_shared<svc::uta_engine::option::BatchOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->CreateOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::option::BatchCancelOrderResponse> stub::option_site_cancel_all(
    svc::uta_engine::option::SiteCancelAllRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::option::BatchCancelOrderResponse> resp =
      std::make_shared<svc::uta_engine::option::BatchCancelOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->OptionSiteCancelAllOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::option::MmpQueryUserMmpResponse> stub::OptionMmpQueryUserMmpLabel(
    svc::uta_engine::option::MmpQueryUserMmpRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::option::MmpQueryUserMmpResponse> resp =
      std::make_shared<svc::uta_engine::option::MmpQueryUserMmpResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  //  context.set_deadline(deadline);

  int ret = m_te->OptionMmpQueryUserMmpLabel(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::GetMmpStateRespV5> stub::GetMmpStateV5(
    svc::uta_engine::req::GetMmpStateReqV5 req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::resp::GetMmpStateRespV5> resp =
      std::make_shared<svc::uta_engine::resp::GetMmpStateRespV5>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  //  context.set_deadline(deadline);

  int ret = m_te->GetMmpStateV5(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::option::InnerCommonResponse> stub::OptionInnerMmpInactive(
    svc::uta_engine::option::InnerMmpInactiveRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::option::InnerCommonResponse> resp =
      std::make_shared<svc::uta_engine::option::InnerCommonResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  //  context.set_deadline(deadline);

  int ret = m_te->OptionInnerMmpInactive(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::option::InnerOrderResponse> stub::OptionInnerCreateOrder(
    svc::uta_engine::option::InnerOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::option::InnerOrderResponse> resp =
      std::make_shared<svc::uta_engine::option::InnerOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  //  context.set_deadline(deadline);

  int ret = m_te->OptionInnerCreateOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::option::InnerCommonResponse> stub::OptionInnerSetUserTradingBaseCoin(
    svc::uta_engine::option::InnerSetUserTradingBaseCoinRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::option::InnerCommonResponse> resp =
      std::make_shared<svc::uta_engine::option::InnerCommonResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  //  context.set_deadline(deadline);

  int ret = m_te->OptionInnerSetUserTradingBaseCoin(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::option::response::OptionSetFeeRateResponse> stub::OptionInnerSetFeeRate(
    svc::uta_engine::option::request::OptionSetFeeRateRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::option::response::OptionSetFeeRateResponse> resp =
      std::make_shared<svc::uta_engine::option::response::OptionSetFeeRateResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  //  context.set_deadline(deadline);

  int ret = m_te->OptionInnerSetFeeRate(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::option::response::OptionRefreshUserSettingResponse> stub::OptionRefreshUserSetting(
    svc::uta_engine::option::request::OptionRefreshUserSettingRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::option::response::OptionRefreshUserSettingResponse> resp =
      std::make_shared<svc::uta_engine::option::response::OptionRefreshUserSettingResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  //  context.set_deadline(deadline);

  int ret = m_te->OptionRefreshUserSetting(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientCreateOrderResponse> stub::create_order(
    svc::uta_engine::spot::req::ClientCreateOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCreateOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ClientCreateOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(300000);
  //  context.set_deadline(deadline);

  int ret = m_te->CreateOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientCreateOrderResponse> stub::create_order(
    svc::uta_engine::spot::req::ClientCreateOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCreateOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ClientCreateOrderResponse>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));

  int ret = m_te->CreateOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::CreateOrderResponse> stub::create_order(
    svc::uta_engine::spot::req::CreateOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::CreateOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::CreateOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(300000);
  //  context.set_deadline(deadline);

  int ret = m_te->CreateOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::CreateOrderResponse> stub::create_order(
    svc::uta_engine::spot::req::CreateOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::CreateOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::CreateOrderResponse>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));

  int ret = m_te->CreateOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::PlanSpotCreateOrderResponse> stub::create_order(
    svc::uta_engine::spot::req::PlanSpotCreateOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::PlanSpotCreateOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::PlanSpotCreateOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(300000);
  //  context.set_deadline(deadline);

  int ret = m_te->CreateOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::PlanSpotCreateOrderResponse> stub::create_order(
    svc::uta_engine::spot::req::PlanSpotCreateOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::PlanSpotCreateOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::PlanSpotCreateOrderResponse>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));

  int ret = m_te->CreateOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ReplaceOrderResponse> stub::replace_order(
    svc::uta_engine::spot::req::ReplaceOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::ReplaceOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ReplaceOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(300000);
  //  context.set_deadline(deadline);

  int ret = m_te->SpotSiteApiReplaceOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ReplaceOrderResponse> stub::replace_order(
    svc::uta_engine::spot::req::ReplaceOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::ReplaceOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ReplaceOrderResponse>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(300000);
  //  context.set_deadline(deadline);

  int ret = m_te->SpotSiteApiReplaceOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ReplacePlanOrderResponse> stub::replace_plan_order(
    svc::uta_engine::spot::req::ReplacePlanOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::ReplacePlanOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ReplacePlanOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));

  int ret = m_te->SpotSiteApiReplacePlanOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ReplacePlanOrderResponse> stub::replace_plan_order(
    svc::uta_engine::spot::req::ReplacePlanOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::ReplacePlanOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ReplacePlanOrderResponse>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));

  int ret = m_te->SpotSiteApiReplacePlanOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientCreateOrderResponse> stub::create_order(
    svc::uta_engine::spot::req::CrossCreateOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCreateOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ClientCreateOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //    std::chrono::system_clock::now() + std::chrono::milliseconds(300000);
  // context.set_deadline(deadline);

  int ret = m_te->CreateOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientCreateOrderResponse> stub::create_order(
    svc::uta_engine::spot::req::CrossCreateOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCreateOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ClientCreateOrderResponse>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));

  int ret = m_te->CreateOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::CancelOrderRespV5> stub::cancel_order(
    svc::uta_engine::req::CancelOrderReqV5 req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::resp::CancelOrderRespV5> resp =
      std::make_shared<svc::uta_engine::resp::CancelOrderRespV5>();
  const std::shared_ptr<models::tradingdto::TradingResultDTO> result =
      std::make_shared<models::tradingdto::TradingResultDTO>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  nlohmann::json j;
  j["eopf"] = "3";
  context.AddMetadata("extension", j.dump());
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  //  context.set_deadline(deadline);

  int ret = m_te->CancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::CancelOrderRespV5> stub::cancel_order(svc::uta_engine::req::CancelOrderReqV5 req,
                                                                             RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::resp::CancelOrderRespV5> resp =
      std::make_shared<svc::uta_engine::resp::CancelOrderRespV5>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  nlohmann::json j;
  j["eopf"] = "3";
  ct.ct.AddMetadata("extension", j.dump());

  int ret = m_te->CancelOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> stub::cancel_order(
    svc::uta_engine::spot::req::ClientCancelOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ClientCancelOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  //  context.set_deadline(deadline);

  int ret = m_te->CancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> stub::cancel_order(
    svc::uta_engine::spot::req::ClientCancelOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ClientCancelOrderResponse>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  int ret = m_te->CancelOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> stub::cancel_order(
    svc::uta_engine::spot::req::CancelPlanOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ClientCancelOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->CancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> stub::cancel_order(
    svc::uta_engine::spot::req::CancelPlanOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ClientCancelOrderResponse>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  ct.ct.set_deadline(deadline);

  int ret = m_te->CancelOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::CancelOrderResponse> stub::cancel_order(
    svc::uta_engine::spot::req::CancelOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::CancelOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::CancelOrderResponse>();

  // context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->CancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::CancelTriggerOrderResponse> stub::cancel_order(
    svc::uta_engine::spot::req::CancelTriggerOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::CancelTriggerOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::CancelTriggerOrderResponse>();

  // context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->CancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> stub::cancel_order(
    svc::uta_engine::spot::req::MergeCancelOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ClientCancelOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->CancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> stub::cancel_order(
    svc::uta_engine::spot::req::MergeCancelOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ClientCancelOrderResponse>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  ct.ct.set_deadline(deadline);

  int ret = m_te->CancelOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::ReplaceOrderRespV5> stub::replace_order(
    svc::uta_engine::req::ReplaceOrderReqV5 req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::resp::ReplaceOrderRespV5> resp =
      std::make_shared<svc::uta_engine::resp::ReplaceOrderRespV5>();
  const std::shared_ptr<models::tradingdto::TradingResultDTO> result =
      std::make_shared<models::tradingdto::TradingResultDTO>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->ReplaceOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::ReplaceOrderRespV5> stub::replace_order(
    svc::uta_engine::req::ReplaceOrderReqV5 req, RpcContext& ct) {
  grpc::ClientContext& context = ct.ct;
  std::shared_ptr<svc::uta_engine::resp::ReplaceOrderRespV5> resp =
      std::make_shared<svc::uta_engine::resp::ReplaceOrderRespV5>();
  const std::shared_ptr<models::tradingdto::TradingResultDTO> result =
      std::make_shared<models::tradingdto::TradingResultDTO>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->ReplaceOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::trading::TradingResp> stub::process(svc::trading::TradingReq req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::trading::TradingResp> resp = std::make_shared<svc::trading::TradingResp>();
  const std::shared_ptr<models::tradingdto::TradingResultDTO> result =
      std::make_shared<models::tradingdto::TradingResultDTO>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(30000000'000);
  context.set_deadline(deadline);

  int ret = m_te->Process(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::DebugResponse> stub::process(svc::uta_engine::DebugRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::DebugResponse> resp = std::make_shared<svc::uta_engine::DebugResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  context.set_deadline(deadline);

  int ret = m_te->Process(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::DebugResponse> stub::FetchUserStore(const svc::uta_engine::DebugRequest& req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::DebugResponse> resp = std::make_shared<svc::uta_engine::DebugResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  context.set_deadline(deadline);

  int ret = m_te->FetchUserStore(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::SeqMarkResponse> stub::update_seq_mark(svc::uta_engine::SeqMarkRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::SeqMarkResponse> resp = std::make_shared<svc::uta_engine::SeqMarkResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  context.set_deadline(deadline);

  int ret = m_te->UpdateSeqMark(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::SeqMarkQueryResponse> stub::query_seq_mark(svc::uta_engine::SeqMarkQueryRequest req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::SeqMarkQueryResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  context.set_deadline(deadline);

  int ret = m_te->QuerySeqMark(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::unified_v2::UnifiedV2ResultDTO> stub::process(svc::unified_v2::UnifiedV2RequestDTO req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::unified_v2::UnifiedV2ResultDTO> resp = std::make_shared<svc::unified_v2::UnifiedV2ResultDTO>();
  const std::shared_ptr<models::tradingdto::TradingResultDTO> result =
      std::make_shared<models::tradingdto::TradingResultDTO>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(300000);
  context.set_deadline(deadline);

  int ret = m_te->Process(context, req, *resp.get());
  EXPECT_EQ(ret, 0);

  return resp;
}

std::shared_ptr<svc::uta_engine::option::OrderResponse> stub::OptionSiteApiCreateOrder(
    svc::uta_engine::option::SiteOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::option::OrderResponse> resp =
      std::make_shared<svc::uta_engine::option::OrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->OptionSitaApiCreateOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::option::BatchOrderResponse> stub::OptionBatchSiteApiCreateOrder(
    svc::uta_engine::option::SiteBatchOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::option::BatchOrderResponse> resp =
      std::make_shared<svc::uta_engine::option::BatchOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->OptionSitaBatchApiCreateOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::option::ReplaceOrderResponse> stub::OptionSiteApiReplaceOrder(
    svc::uta_engine::option::SiteReplaceOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::option::ReplaceOrderResponse> resp =
      std::make_shared<svc::uta_engine::option::ReplaceOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->OptionSitaApiReplaceOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::option::CancelOrderResponse> stub::OptionSiteApiCancelOrder(
    svc::uta_engine::option::SiteCancelOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::option::CancelOrderResponse> resp =
      std::make_shared<svc::uta_engine::option::CancelOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->OptionSitaApiCancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::CancelAllOrderRespV5> stub::cancel_all(
    svc::uta_engine::req::CancelAllOrderReqV5 req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::resp::CancelAllOrderRespV5> resp =
      std::make_shared<svc::uta_engine::resp::CancelAllOrderRespV5>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  context.AddMetadata("cancel_type", "CancelByAdmin");
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);
  nlohmann::json j;
  j["eopf"] = "3";
  context.AddMetadata("extension", j.dump());

  int ret = m_te->CancelAll(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::CancelAllOrderRespV5> stub::cancel_all(
    svc::uta_engine::req::CancelAllOrderReqV5 req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::resp::CancelAllOrderRespV5> resp =
      std::make_shared<svc::uta_engine::resp::CancelAllOrderRespV5>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  ct.ct.set_deadline(deadline);
  nlohmann::json j;
  j["eopf"] = "3";
  ct.ct.AddMetadata("extension", j.dump());

  int ret = m_te->CancelAll(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelOrderResponseV2> stub::cancel_all(
    svc::uta_engine::spot::req::BatchCancelOrderRequestV2 req) {
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelOrderResponseV2> resp =
      std::make_shared<svc::uta_engine::spot::resp::BatchCancelOrderResponseV2>();
  grpc::ClientContext context;
  int ret = m_te->CancelAll(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> stub::batch_cancel_order(
    svc::uta_engine::spot::req::ClientBatchCancelOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ClientCancelOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->BatchCancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> stub::batch_cancel_order(
    svc::uta_engine::spot::req::ClientBatchCancelOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::ClientCancelOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ClientCancelOrderResponse>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  ct.ct.set_deadline(deadline);

  int ret = m_te->BatchCancelOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse> stub::batch_cancel_order(
    svc::uta_engine::spot::req::BatchCancelPlanOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->BatchCancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse> stub::batch_cancel_order(
    svc::uta_engine::spot::req::BatchCancelPlanOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  ct.ct.set_deadline(deadline);

  int ret = m_te->BatchCancelOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse> stub::batch_cancel_order(
    svc::uta_engine::spot::req::MergeBatchCancelOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->BatchCancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse> stub::batch_cancel_order(
    svc::uta_engine::spot::req::MergeBatchCancelOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::BatchCancelPlanOrderResponse>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  ct.ct.set_deadline(deadline);

  int ret = m_te->BatchCancelOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelOrderByIdsResponse> stub::batch_cancel_order(
    svc::uta_engine::spot::req::BatchCancelOrderByIdsRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelOrderByIdsResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::BatchCancelOrderByIdsResponse>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->BatchCancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelTriggerOrderByIdsResponse> stub::batch_cancel_order(
    svc::uta_engine::spot::req::BatchCancelTriggerOrderByIdsRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelTriggerOrderByIdsResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::BatchCancelTriggerOrderByIdsResponse>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->BatchCancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelOrderByUidResponse> stub::batch_cancel_order(
    svc::uta_engine::spot::req::BatchCancelOrderByUidRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCancelOrderByUidResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::BatchCancelOrderByUidResponse>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->BatchCancelOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::SetSupportedSymbolListResponse> stub::set_supported_symbol_list(
    svc::uta_engine::spot::req::SetSupportedSymbolListRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::SetSupportedSymbolListResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::SetSupportedSymbolListResponse>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->SetSupportedSymbolList(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::SetMaxMarginLeverageResponse> stub::set_max_margin_leverage(
    svc::uta_engine::spot::req::SetMaxMarginLeverageRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::SetMaxMarginLeverageResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::SetMaxMarginLeverageResponse>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->SetMaxMarginLeverage(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::SetFeeRateResponse> stub::set_fee_rate(
    svc::uta_engine::spot::req::SetFeeRateRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::SetFeeRateResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::SetFeeRateResponse>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(10000);
  context.set_deadline(deadline);

  int ret = m_te->SetFeeRate(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::SetFeeRuleResp> stub::set_fee_rule(
    svc::uta_engine::spot::req::SetFeeRuleReq req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::SetFeeRuleResp> resp =
      std::make_shared<svc::uta_engine::spot::resp::SetFeeRuleResp>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(10000);
  context.set_deadline(deadline);

  int ret = m_te->SetFeeRule(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::OpenInnovationResponse> stub::open_innovation(
    svc::uta_engine::spot::req::OpenInnovationRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::OpenInnovationResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::OpenInnovationResponse>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->OpenInnovation(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::GetTradeFeeRateRespV5> stub::get_fee_rate(
    svc::uta_engine::req::GetTradeFeeRateReqV5 req, RpcContext& ct) {
  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  std::shared_ptr<svc::uta_engine::resp::GetTradeFeeRateRespV5> resp =
      std::make_shared<svc::uta_engine::resp::GetTradeFeeRateRespV5>();
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  ct.ct.set_deadline(deadline);

  int ret = m_te->GetFeeRate(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::EmptyResp> stub::set_leverage(svc::uta_engine::req::SetLeverageReqV5 req) {
  auto resp = std::make_shared<svc::uta_engine::resp::EmptyResp>();

  RpcContext ct{};
  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  ct.ct.set_deadline(deadline);

  int ret = m_te->SetLeverage(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::EmptyResp> stub::set_leverage(svc::uta_engine::req::SetLeverageReqV5 req,
                                                                     RpcContext& ct) {
  auto resp = std::make_shared<svc::uta_engine::resp::EmptyResp>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  ct.ct.set_deadline(deadline);

  int ret = m_te->SetLeverage(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::EmptyResp> stub::set_auto_add_margin(
    svc::uta_engine::req::SetAutoAddMarginRequestV5 req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::resp::EmptyResp>();

  context.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->SetAutoAddMargin(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::EmptyResp> stub::switch_position_mode(
    svc::uta_engine::req::SwitchPositionModeRequestV5 req, RpcContext& ct) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::resp::EmptyResp>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  ct.ct.set_deadline(deadline);

  int ret = m_te->SwitchPositionMode(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::AddMarginResponseV5> stub::add_margin(
    svc::uta_engine::req::AddMarginRequestV5 req, RpcContext& ct) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::resp::AddMarginResponseV5>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  ct.ct.set_deadline(deadline);

  int ret = m_te->AddMargin(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::SetRiskIdResponseV5> stub::set_risk_id(svc::uta_engine::req::SetRiskIdReqV5 req,
                                                                              RpcContext& ct) {
  auto resp = std::make_shared<svc::uta_engine::resp::SetRiskIdResponseV5>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  ct.ct.set_deadline(deadline);

  int ret = m_te->SetRiskId(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);

  return resp;
}
std::shared_ptr<svc::uta_engine::resp::EmptyResp> stub::confirm_pending_mmr(
    svc::uta_engine::req::ConfirmPendingMmrReqV5 req, RpcContext& ct) {
  auto resp = std::make_shared<svc::uta_engine::resp::EmptyResp>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(300000);
  ct.ct.set_deadline(deadline);

  int ret = m_te->ConfirmPendingMMR(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);

  return resp;
}

std::shared_ptr<svc::uta_engine::block_trade::resp::PreOccupyResp> stub::block_trade_pre_occupy(
    svc::uta_engine::block_trade::req::PreOccupyReq req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::block_trade::resp::PreOccupyResp>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->BlockTradePreOccupy(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::strategy_trade::resp::PreCheckResp> stub::strategy_trade_pre_check(
    svc::uta_engine::strategy_trade::req::PreCheckReq req) {
  grpc::ClientContext context;
  context.AddMetadata("memberid", std::to_string(m_uid));
  auto resp = std::make_shared<svc::uta_engine::strategy_trade::resp::PreCheckResp>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->StrategyTradePreCheck(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::strategy_trade::resp::DDHStrategyPreCheckResp> stub::ddh_strategy_pre_check(
    svc::uta_engine::strategy_trade::req::DDHStrategyPreCheckReq req) {
  grpc::ClientContext context;
  context.AddMetadata("memberid", std::to_string(m_uid));
  auto resp = std::make_shared<svc::uta_engine::strategy_trade::resp::DDHStrategyPreCheckResp>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->DdhStrategyPreCheck(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::strategy_trade::resp::DDHStrategyCreateOrderResp> stub::ddh_strategy_create_order(
    svc::uta_engine::strategy_trade::req::DDHStrategyCreateOrderReq req) {
  grpc::ClientContext context;
  context.AddMetadata("memberid", std::to_string(m_uid));
  auto resp = std::make_shared<svc::uta_engine::strategy_trade::resp::DDHStrategyCreateOrderResp>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->DdhStrategyCreateOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::block_trade::resp::CreateBlockTradeResp> stub::block_trade_create(
    svc::uta_engine::block_trade::req::CreateBlockTradeReq req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::block_trade::resp::CreateBlockTradeResp>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(10000);
  context.set_deadline(deadline);

  int ret = m_te->BlockTradeCreate(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::block_trade::resp::QueryOrderResp> stub::block_trade_query(
    svc::uta_engine::block_trade::req::QueryOrderReq req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::block_trade::resp::QueryOrderResp>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->BlockTradeQuery(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::SetTpSlModeRespV5> stub::set_tp_sl_mode(
    svc::uta_engine::req::SetTpSlModeReqV5 req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::resp::SetTpSlModeRespV5> resp =
      std::make_shared<svc::uta_engine::resp::SetTpSlModeRespV5>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  nlohmann::json j;
  j["eopf"] = "3";
  ct.ct.AddMetadata("extension", j.dump());

  // const std::chrono::system_clock::time_point deadline =
  //     std::chrono::system_clock::now() + std::chrono::milliseconds(30000000);
  //  context.set_deadline(deadline);

  int ret = m_te->SetTpSlMode(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::resp::EmptyResp> stub::set_tp_sl_ts(svc::uta_engine::req::SetTpSlTsReqV5 req,
                                                                     RpcContext& ct) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::resp::EmptyResp>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  nlohmann::json j;
  j["eopf"] = "3";
  ct.ct.AddMetadata("extension", j.dump());

  int ret = m_te->SetTpSlTs(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::InnerReplaceOrderResponse> stub::replace_order(
    svc::uta_engine::spot::req::InnerReplaceOrderRequest req) {
  grpc::ClientContext context;
  std::shared_ptr<svc::uta_engine::spot::resp::InnerReplaceOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::InnerReplaceOrderResponse>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->ReplaceOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::DebugUserBlockResponse> stub::user_block(svc::uta_engine::DebugUserBlockRequest req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::DebugUserBlockResponse>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->UserBlock(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::DebugSymbolConfigResponse> stub::fetch_symbol_config(
    svc::uta_engine::DebugSymbolConfigRequest req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::DebugSymbolConfigResponse>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->FetchSymbolConfig(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::CleanupHistorySymbolResponse> stub::cleanup_history_symbol(
    svc::uta_engine::CleanupHistorySymbolRequest req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::CleanupHistorySymbolResponse>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->CleanupHistorySymbol(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::DebugResponse> stub::process_debug_margin_request(
    svc::uta_engine::DebugMarginRequest req, int expect_ret, std::map<std::string, std::string> metadata) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::DebugResponse>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  for (auto& pair : metadata) {
    context.AddMetadata(pair.first, pair.second);
  }

  int ret = m_te->ProcessDebugMarginRequest(context, req, *resp.get());
  EXPECT_EQ(ret, expect_ret);
  return resp;
}

std::shared_ptr<svc::uta_engine::DebugSDKConfigResponse> stub::set_sdk_config(
    svc::uta_engine::DebugSDKConfigRequest req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::DebugSDKConfigResponse>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->SetSDKConfig(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

int stub::deposit(std::string amount, biz::coin_t coin) {
  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, m_uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = process(deposit_build.Build());
  auto result = m_te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int stub::switch_margin_mode(EAccountMode mode) {
  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(m_uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(m_uid, unified_v_2_request_dto, -1);
  m_te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = m_te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

std::shared_ptr<svc::unified_v2::UnifiedV2ResultDTO> stub::SwitchMarginMode(EAccountMode mode) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(m_uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(m_uid, unified_v_2_request_dto, -1);
  auto ret = m_te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  if (ret != 0) {
    return nullptr;
  }
  return m_te->PopResult().m_msg;
}

std::shared_ptr<svc::unified_v2::UnifiedV2ResultDTO> stub::RiskClosePz() {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(m_uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::RiskClosePz);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(m_uid, unified_v_2_request_dto, -1);
  auto ret = m_te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  if (ret != 0) {
    return nullptr;
  }
  return m_te->PopResult().m_msg;
}

bool stub::ReCalcEvent(bool is_final) {
  int64_t sharding_key{};
  if (!is_final) {
    const auto& global_var = application::GlobalVarManager::Instance();
    const auto pre_threads = static_cast<int64_t>(global_var.orig_pre_threads());
    const int32_t total_shards = application::GlobalConfig::re_calc_total_user_sharding();

    const uint64_t user_hash = utils::hash(static_cast<uint64_t>(m_uid));
    sharding_key = static_cast<std::int64_t>((user_hash / pre_threads) % total_shards);
  }

  const auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  const int ret = m_te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  return ret == 0;
}

std::shared_ptr<svc::uta_engine::DebugResponse> stub::DebugUserData() {
  svc::uta_engine::DebugRequest req;
  req.mutable_req_header()->set_user_id(m_uid);
  req.mutable_req_header()->set_action(EAction::DebugUserData);
  req.mutable_user_data_req()->set_dummy(0);
  return FetchUserStore(req);
}

std::shared_ptr<svc::uta_engine::spot::resp::SetAllowTradeConfigResponse> stub::set_allow_trade_config(
    svc::uta_engine::spot::req::SetAllowTradeConfigRequest req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::spot::resp::SetAllowTradeConfigResponse>();

  const std::chrono::system_clock::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(3000);
  context.set_deadline(deadline);

  int ret = m_te->SetAllowTradeConfig(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientPreCreateOrderResponse> stub::pre_create_order(
    svc::uta_engine::spot::req::ClientCreateOrderRequest req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::spot::resp::ClientPreCreateOrderResponse>();
  context.AddMetadata("memberid", std::to_string(m_uid));
  int ret = m_te->PreCreateOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientPreCreateOrderResponse> stub::pre_create_order(
    svc::uta_engine::spot::req::ClientCreateOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::ClientPreCreateOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ClientPreCreateOrderResponse>();
  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  int ret = m_te->PreCreateOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::PlanSpotPreCreateOrderResponse> stub::pre_create_order(
    svc::uta_engine::spot::req::PlanSpotCreateOrderRequest req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::spot::resp::PlanSpotPreCreateOrderResponse>();
  context.AddMetadata("memberid", std::to_string(m_uid));
  int ret = m_te->PreCreateOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::PlanSpotPreCreateOrderResponse> stub::pre_create_order(
    svc::uta_engine::spot::req::PlanSpotCreateOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::PlanSpotPreCreateOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::PlanSpotPreCreateOrderResponse>();
  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  int ret = m_te->PreCreateOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientPreCreateOrderResponse> stub::pre_create_order(
    svc::uta_engine::spot::req::CrossCreateOrderRequest req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::spot::resp::ClientPreCreateOrderResponse>();
  context.AddMetadata("memberid", std::to_string(m_uid));
  int ret = m_te->PreCreateOrder(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::ClientPreCreateOrderResponse> stub::pre_create_order(
    svc::uta_engine::spot::req::CrossCreateOrderRequest req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::ClientPreCreateOrderResponse> resp =
      std::make_shared<svc::uta_engine::spot::resp::ClientPreCreateOrderResponse>();
  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  int ret = m_te->PreCreateOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::BatchCreateOrderRespSiteApiResult> stub::batch_create_order_siteapi(
    svc::uta_engine::spot::req::BatchCreateOrderSiteApi req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::spot::resp::BatchCreateOrderRespSiteApiResult>();
  context.AddMetadata("memberid", std::to_string(m_uid));
  int ret = m_te->BatchCreateOrderSpotSiteApi(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::BatchCreateOrderRespSiteApiResult> stub::batch_create_order_siteapi(
    svc::uta_engine::spot::req::BatchCreateOrderSiteApi req, RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::BatchCreateOrderRespSiteApiResult> resp =
      std::make_shared<svc::uta_engine::spot::resp::BatchCreateOrderRespSiteApiResult>();
  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  int ret = m_te->BatchCreateOrderSpotSiteApi(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::UpdateAveragePriceAndAmountSiteApiResult>
stub::update_average_price_and_amount(svc::uta_engine::spot::req::UpdateAveragePriceAndAmountSiteApi req) {
  grpc::ClientContext context;
  auto resp = std::make_shared<svc::uta_engine::spot::resp::UpdateAveragePriceAndAmountSiteApiResult>();
  context.AddMetadata("memberid", std::to_string(m_uid));
  int ret = m_te->UpdateAveragePriceAndAmountSiteApi(context, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::spot::resp::UpdateAveragePriceAndAmountSiteApiResult>
stub::update_average_price_and_amount(svc::uta_engine::spot::req::UpdateAveragePriceAndAmountSiteApi req,
                                      RpcContext& ct) {
  std::shared_ptr<svc::uta_engine::spot::resp::UpdateAveragePriceAndAmountSiteApiResult> resp =
      std::make_shared<svc::uta_engine::spot::resp::UpdateAveragePriceAndAmountSiteApiResult>();
  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  int ret = m_te->UpdateAveragePriceAndAmountSiteApi(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);
  return resp;
}

std::shared_ptr<svc::uta_engine::combination::resp::CreateOrderResponse> stub::comb_siteapi_create_order(
    svc::uta_engine::combination::req::CreateOrderRequest req, RpcContext& ct) const {
  auto resp = std::make_shared<svc::uta_engine::combination::resp::CreateOrderResponse>();
  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  nlohmann::json j;
  j["eopf"] = "3";
  j["opfrom"] = "ut";
  ct.ct.AddMetadata("extension", j.dump());

  int ret = m_te->CombSiteApiCreateOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);

  return resp;
}

std::shared_ptr<svc::uta_engine::resp::CreateOrderRespV5> stub::comb_openapi_create_order(
    svc::uta_engine::req::CombinationCreateOrderReqV5 req, RpcContext& ct) const {
  auto resp = std::make_shared<svc::uta_engine::resp::CreateOrderRespV5>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  nlohmann::json j;
  j["eopf"] = "3";
  j["opfrom"] = "ut";
  ct.ct.AddMetadata("extension", j.dump());

  int ret = m_te->CombOpenApiCreateOrder(ct.ct, req, *resp.get());
  if (ct.ct.GetServerInitialMetadata().find("next-response-codes") != ct.ct.GetServerInitialMetadata().end() &&
      ct.ct.GetServerInitialMetadata().find("next-response-codes")->second == "10006") {
    EXPECT_EQ(ret, 10006);
  } else {
    EXPECT_EQ(ret, 0);
  }

  return resp;
}

std::shared_ptr<svc::uta_engine::combination::resp::ReplaceOrderResponse> stub::comb_siteapi_replace_order(
    svc::uta_engine::combination::req::ReplaceOrderRequest req, RpcContext& ct) const {
  auto resp = std::make_shared<svc::uta_engine::combination::resp::ReplaceOrderResponse>();
  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  nlohmann::json j;
  j["eopf"] = "3";
  j["opfrom"] = "ut";
  ct.ct.AddMetadata("extension", j.dump());

  int ret = m_te->CombSiteApiReplaceOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);

  return resp;
}

std::shared_ptr<svc::uta_engine::resp::ReplaceOrderRespV5> stub::comb_openapi_replace_order(
    svc::uta_engine::req::CombinationReplaceOrderReqV5 req, RpcContext& ct) const {
  auto resp = std::make_shared<svc::uta_engine::resp::ReplaceOrderRespV5>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  nlohmann::json j;
  j["eopf"] = "3";
  j["opfrom"] = "ut";
  ct.ct.AddMetadata("extension", j.dump());

  int ret = m_te->CombOpenApiReplaceOrder(ct.ct, req, *resp.get());
  if (ct.ct.GetServerInitialMetadata().find("next-response-codes") != ct.ct.GetServerInitialMetadata().end() &&
      ct.ct.GetServerInitialMetadata().find("next-response-codes")->second == "10006") {
    EXPECT_EQ(ret, 10006);
  } else {
    EXPECT_EQ(ret, 0);
  }

  return resp;
}

std::shared_ptr<svc::uta_engine::combination::resp::CancelOrderResponse> stub::comb_siteapi_cancel_order(
    svc::uta_engine::combination::req::CancelOrderRequest req, RpcContext& ct) const {
  auto resp = std::make_shared<svc::uta_engine::combination::resp::CancelOrderResponse>();
  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  nlohmann::json j;
  j["eopf"] = "3";
  j["opfrom"] = "ut";
  ct.ct.AddMetadata("extension", j.dump());

  int ret = m_te->CombSiteApiCancelOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);

  return resp;
}

std::shared_ptr<svc::uta_engine::resp::CancelOrderRespV5> stub::comb_openapi_cancel_order(
    svc::uta_engine::req::CombinationCancelOrderReqV5 req, RpcContext& ct) const {
  auto resp = std::make_shared<svc::uta_engine::resp::CancelOrderRespV5>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  nlohmann::json j;
  j["eopf"] = "3";
  j["opfrom"] = "ut";
  ct.ct.AddMetadata("extension", j.dump());

  int ret = m_te->CombOpenApiCancelOrder(ct.ct, req, *resp.get());
  if (ct.ct.GetServerInitialMetadata().find("next-response-codes") != ct.ct.GetServerInitialMetadata().end() &&
      ct.ct.GetServerInitialMetadata().find("next-response-codes")->second == "10006") {
    EXPECT_EQ(ret, 10006);
  } else {
    EXPECT_EQ(ret, 0);
  }

  return resp;
}

std::shared_ptr<svc::uta_engine::combination::resp::CancelAllOrderResponse> stub::comb_siteapi_cancel_all(
    svc::uta_engine::combination::req::CancelAllOrderRequest req, RpcContext& ct) const {
  auto resp = std::make_shared<svc::uta_engine::combination::resp::CancelAllOrderResponse>();
  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  nlohmann::json j;
  j["eopf"] = "3";
  j["opfrom"] = "ut";
  ct.ct.AddMetadata("extension", j.dump());

  int ret = m_te->CombSiteApiCancelAllOrder(ct.ct, req, *resp.get());
  EXPECT_EQ(ret, 0);

  return resp;
}

std::shared_ptr<svc::uta_engine::resp::CancelAllOrderRespV5> stub::comb_openapi_cancel_all(
    svc::uta_engine::req::CombinationCancelAllOrderReqV5 req, RpcContext& ct) const {
  auto resp = std::make_shared<svc::uta_engine::resp::CancelAllOrderRespV5>();

  ct.ct.AddMetadata("memberid", std::to_string(m_uid));
  nlohmann::json j;
  j["eopf"] = "3";
  j["opfrom"] = "ut";
  ct.ct.AddMetadata("extension", j.dump());

  int ret = m_te->CombOpenApiCancelAllOrder(ct.ct, req, *resp.get());
  if (ct.ct.GetServerInitialMetadata().find("next-response-codes") != ct.ct.GetServerInitialMetadata().end() &&
      ct.ct.GetServerInitialMetadata().find("next-response-codes")->second == "10006") {
    EXPECT_EQ(ret, 10006);
  } else {
    EXPECT_EQ(ret, 0);
  }

  return resp;
}
