#include "main.hpp"  // NOLINT

#include <stdio.h>

#include <bbase/common/hdts/hdts.hpp>
#include <bbase/common/nacos_client/nacos_client_impl.hpp>
#include <cstring>
#include <memory>
#include <string>
#include <vector>

#include "src/config/config_proxy.hpp"
#include "test/mocks/match/MatchMock.h"

std::int32_t argument_count = 5;
char* argument_arrary[] = {const_cast<char*>("./trade_ut.bin"), const_cast<char*>("--skip_campaign"),
                           const_cast<char*>("--log_dir=/tmp"), const_cast<char*>("--test=1"),
                           const_cast<char*>("--debug_grpc_port=12356")};
// OneTradeMockApp* app = nullptr;

int main(int argc, char* argv[]) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");

  // start cross
  std::vector<int> fbu_cross_idx = {1, 2, 5, 6, 11, 33, 34, 28};
  std::vector<int> obu_cross_idx = {10007};
  std::vector<int> sbu_cross_idx = {10000};
  std::vector<int> futures_spread_cross_idx = {40000};  // futures spread 组合撮合cross_idx从40000开始

  tmock::MatchMock::Instance()->start(fbu_cross_idx, obu_cross_idx, sbu_cross_idx, futures_spread_cross_idx);

  // app = dynamic_cast<OneTradeMockApp*>(testing::AddGlobalTestEnvironment(new OneTradeMockApp()));
  auto client = std::make_shared<bbase::nacos_client::NacosClientImpl>("FUTURE-CONFIG", "uta", "");
  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::ObjectType::kObjectConfigCenter, client);
  // cache kv dataids
  config::ConfigProxy::Instance().InitConfigCenter();
  config::ConfigProxy::Instance().initConfigCache();
  std::string data_id = fmt::format("{}.rpc", application::GlobalVarManager::Instance().service_name());
  client->SetContent(data_id, config::ConstConfig::UTA_GROUP, "");

  testing::InitGoogleTest(&argc, argv);

  auto ret = RUN_ALL_TESTS();

  bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::ObjectType::kObjectConfigCenter);
  config::ConfigProxy::Instance().Fini();

  bbase::hdts::Hdts::Fini();

  // 放在hdts销毁以后，避免有request消息在处理中，导致panic
  tmock::MatchMock::Instance()->stop();

  return ret;
}
