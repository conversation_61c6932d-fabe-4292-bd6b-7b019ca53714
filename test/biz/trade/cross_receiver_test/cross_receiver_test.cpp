#include "test/biz/trade/cross_receiver_test/cross_receiver_test.hpp"

#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <iostream>
#include <thread>

#include "src/biz_worker/utils/common_utils.hpp"
#include "src/cross_worker/cross_common/util/spot_type_convert.hpp"
#include "src/cross_worker/cross_receiver/cross_define.hpp"
#include "src/cross_worker/cross_receiver/cross_manager/cross_consume_manager.hpp"
#include "src/cross_worker/cross_receiver/cross_manager/cross_consume_task.hpp"
#include "src/cross_worker/cross_receiver/cross_manager/cross_filter_helper.hpp"
#include "src/cross_worker/cross_receiver/cross_receiver.hpp"
#include "src/cross_worker/cross_receiver/message_handle/filter_resp_message_handle.hpp"
#include "src/cross_worker/cross_receiver/message_handle/message_handle_base.hpp"
#include "test/mocks/hdts/async_producer.hpp"
#include "test/mocks/hdts/message.hpp"
#include "test/mocks/hdts/topic.hpp"
#include "test/mocks/match_sdk/futures_match_sdk/FbuCrossSdk.h"
#include "test/mocks/match_sdk/futures_match_sdk/FbuResponse.h"
#include "test/mocks/match_sdk/futures_match_sdk/FbuRspHeader.h"
#include "test/mocks/match_sdk/spot_match_sdk/SbuCrossSdk.h"
#include "test/mocks/match_sdk/spot_match_sdk/SbuResponse.h"

using testing::GTEST_FLAG(death_test_style);

static std::string MakeRespPkg(fbu::FbuCrossSdk* sdk1, biz::seq_t seq) {
  // 包头
  fbu::Header header;
  header._magicCookie = fbu::Header::MAGIC;
  header._hdrVersion = fbu::Header::VERSION;
  header._hdrLength = sizeof(fbu::Header);
  header._action = fbu::Header::ActionCode::Msg;
  header._passThrough = 0;
  header._count = 1;
  header._offset = 0;
  header._length = sizeof(fbu::yijin::MatchSdk::order::Request);
  std::string sHeader((char*)&header, sizeof(fbu::Header));  // NOLINT
  // 包体
  fbu::yijin::MatchSdk::order::Request request;
  request._orderID = fbu::yijin::MatchSdk::utility::uuid4();
  request._origOrderID = fbu::yijin::MatchSdk::utility::uuid4();
  request._side = fbu::yijin::MatchSdk::order::Side::Buy;
  request._timeInForce = fbu::yijin::MatchSdk::order::TimeInForce::GoodTillCancel;
  request._qty = 1;
  request._price = 5000;
  request._priceScale = 4;
  struct timeval tv;
  gettimeofday(&tv, nullptr);
  request._transactTime[0] = tv.tv_sec;
  request._transactTime[1] = tv.tv_usec;
  request._msgType = fbu::yijin::MatchSdk::order::RequestMsgType::NewOrder;
  request._symbol = 0x05;
  request._orderType = fbu::yijin::MatchSdk::order::OrderType::LimitOrder;
  request._createdBy = fbu::yijin::MatchSdk::order::CreatedBy::CreatedByUser;
  request._canceledBy = fbu::yijin::MatchSdk::order::CanceledBy::CanceledByUser;
  request._senderCompID = seq;  // NOLINT
  request._targetCompID = 0x0;
  request.seal2();
  std::string sRequest((char*)&request, sizeof(fbu::yijin::MatchSdk::order::Request));  // NOLINT
  std::string rsp;
  // case 1
  sdk1->DoCross(seq, sHeader + sRequest, rsp);

  return rsp;
  //   auto req_seq = biz::cross::XHeader::GetOffset((void*)rsp.c_str(), rsp.size());  // NOLINT
}

static std::string MakeSpotReqPkg(sbu::SbuCrossSdk* sdk1, biz::seq_t seq) {
  int32_t symbol = 5;
  // 包头
  sbu::yijin::MatchSdk::order::Header header;
  header._magicCookie = sbu::yijin::MatchSdk::order::Header::MAGIC;
  header._hdrVersion = sbu::yijin::MatchSdk::order::Header::VERSION;
  header._hdrLength = sizeof(sbu::yijin::MatchSdk::order::Header);
  header._msgType = sbu::yijin::MatchSdk::order::HeadMsgType::Normal_Order;  // Normal_Order:下改撤
  header._offset = 0;
  header._length = sizeof(sbu::yijin::MatchSdk::order::Request);
  header._count = 1;
  header._symbol = symbol;
  header._exchangeId = 0;
  header._startCross = 0;
  header._endCross = 0;
  header._sendingTime =
      std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch())
          .count();
  header._orderType = sbu::yijin::MatchSdk::order::HeadOrderType::E_NewOrder;
  header._reserve1 = 0;
  header._reserve2 = 0;
  std::string sHeader((char*)&header, sizeof(sbu::yijin::MatchSdk::order::Header));  // NOLINT
  // 包体
  sbu::yijin::MatchSdk::order::Request request;
  request._orderID =
      std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch())
          .count();
  request._msgLength = sizeof(sbu::yijin::MatchSdk::order::Request);
  request._msgType = sbu::yijin::MatchSdk::order::RequestMsgType::NewOrder;
  request._smpType = sbu::yijin::MatchSdk::order::SmpType::SMPT_NONE;
  request._smpGroup = 0;
  request._senderCompID = seq;  // 现货的uid
  request._targetCompID = 0;    // shardid
  request._sendingTime =
      std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch())
          .count();
  request._accountId = 0;
  request._brokerID = 0;
  request._bizCrossSeq = 0;
  request._orderType = sbu::yijin::MatchSdk::order::OrderType::LimitOrder;
  request._side = sbu::yijin::MatchSdk::order::Side::Buy;
  request._timeInForce = sbu::yijin::MatchSdk::order::TimeInForce::GoodTillCancel;
  request._limitPriceRatio = 0;
  request._symbol = symbol;
  request._reserve1 = 0;
  request._reserve2 = 0;
  request._reserve3 = 0;
  request._reserve4 = 0;
  request._reserve5 = 0;
  request._reserve6 = 0;
  request._makerFeeRate = 0;
  request._takerFeeRate = 0;
  request._makerBonusRate = 0;

  biz::cross::SpotTypeConvert::Decimal2int64(bbase::decimal::Decimal<>("10000.00"),  // 价格
                                             request._price[0], request._price[1]);

  biz::cross::SpotTypeConvert::Decimal2int64(bbase::decimal::Decimal<>("3.0"),  // 委托量
                                             request._qty[0], request._qty[1]);

  biz::cross::SpotTypeConvert::Decimal2int64(bbase::decimal::Decimal<>("0"),  // 委托金额,可以为零
                                             request._amount[0], request._amount[1]);

  biz::cross::SpotTypeConvert::Decimal2int64(bbase::decimal::Decimal<>("0.1"),  // 最小成交量
                                             request._minQty[0], request._minQty[1]);

  biz::cross::SpotTypeConvert::Decimal2int64(bbase::decimal::Decimal<>("0.1"),  // 最小成交金额
                                             request._minAmount[0], request._minAmount[1]);

  std::string sRequest((char*)&request, sizeof(sbu::yijin::MatchSdk::order::Request));  // NOLINT
  std::string req;
  // case 1
  sdk1->DoCross(seq, sHeader + sRequest, req);

  return req;
  //   auto req_seq = biz::cross::XHeader::GetOffset((void*)rsp.c_str(), rsp.size());  // NOLINT
}

TEST_F(CrossReceiverTestM, SpotHelperRespTest) {
  application::GlobalVarManager::Instance().set_cross_consume_topic_type(::cross::TopicType::kRespHelpType);
  application::GlobalVarManager::Instance().set_test(false);

  std::string topic = "cross_helper_22000";
  std::string msg =
      "{\"LeaderT\":\"response_of_22000_m\",\"LeaderCrossSeq\":-1,\"LeaderOffset\":-1,\"FollowerT\":"
      "\"response_of_22000_s\",\"FollowerCrossSeq\":-1,\"FollowerOffset\":-1}";
  // 先生产一条cross_helper消费
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());
  producer->ProduceMessage(topic,
                           (void*)msg.c_str(),  // NOLINT
                           msg.size(), nullptr, 0, nullptr);
  cross::CrossConsumeTaskParam task;
  task.business_type = cross::BusinessType::kSbuType;
  task.topic_type = cross::TopicType::kRespHelpType;
  task.cross_idx = 22000;
  task.next_cross_seq = 1;
  task.process_matching_result =
      std::bind(biz::CommonUtils::ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  auto consumer_task_id = cross::CrossReceiver::AddConsumeTask(task);

  // 发送leader切换
  std::string msg_change =
      "{\"LeaderT\":\"response_of_22000_s\",\"LeaderCrossSeq\":10,\"LeaderOffset\":10,\"FollowerT\":"
      "\"response_of_22000_m\",\"FollowerCrossSeq\":10,\"FollowerOffset\":10}";
  producer->ProduceMessage(topic,
                           (void*)msg_change.c_str(),  // NOLINT
                           msg_change.size(), nullptr, 0, nullptr);
  std::this_thread::sleep_for(std::chrono::seconds(1));

  sbu::SbuCrossSdk* sdk1 = new sbu::SbuCrossSdk();
  for (auto i = 0; i < 10; i++) {
    auto msg1 = MakeSpotReqPkg(sdk1, i);
    producer->ProduceMessage("response_of_22000_s",
                             (void*)msg1.c_str(),  // NOLINT
                             msg1.size(), nullptr, 0, nullptr);
  }

  cross::CrossReceiver::DelConsumeTask(consumer_task_id);
}

TEST_F(CrossReceiverTestM, HelperRespTest) {
  std::string topic = "cross_helper_36";
  std::string msg =
      "{\"LeaderT\":\"response_of_36_m\",\"LeaderCrossSeq\":-1,\"LeaderOffset\":-1,\"FollowerT\":\"response_of_36_s\","
      "\"FollowerCrossSeq\":-1,\"FollowerOffset\":-1}";
  // 先生产一条cross_helper消费
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());
  producer->ProduceMessage(topic,
                           (void*)msg.c_str(),  // NOLINT
                           msg.size(), nullptr, 0, nullptr);
  cross::CrossConsumeTaskParam task;
  task.business_type = cross::BusinessType::kFbuType;
  task.topic_type = cross::TopicType::kRespHelpType;
  task.cross_idx = 36;
  task.next_cross_seq = 1;
  task.process_matching_result =
      std::bind(biz::CommonUtils::ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  auto consumer_task_id = cross::CrossReceiver::AddConsumeTask(task);

  // 发送leader切换
  std::string msg_change =
      "{\"LeaderT\":\"response_of_36_s\",\"LeaderCrossSeq\":10,\"LeaderOffset\":10,\"FollowerT\":\"response_of_36_m\","
      "\"FollowerCrossSeq\":10,\"FollowerOffset\":10}";
  producer->ProduceMessage(topic,
                           (void*)msg_change.c_str(),  // NOLINT
                           msg_change.size(), nullptr, 0, nullptr);
  std::this_thread::sleep_for(std::chrono::seconds(1));

  fbu::FbuCrossSdk* sdk1 = new fbu::FbuCrossSdk();
  for (auto i = 0; i < 10; i++) {
    auto msg1 = MakeRespPkg(sdk1, i);
    producer->ProduceMessage("response_of_36_s",
                             (void*)msg1.c_str(),  // NOLINT
                             msg1.size(), nullptr, 0, nullptr);
  }

  cross::CrossReceiver::DelConsumeTask(consumer_task_id);
}

TEST_F(CrossReceiverTestM, InternalFilterFbuTest) {
  auto&& sharding_config_storage = application::GlobalVarManager::Instance().sharding_config_storage();
  application::GlobalVarManager::Instance().set_test(true);
  sharding_config_storage.Init(sharding_config::ShardingConfigKey{});
  sharding_config_storage.Load({}, {});

  auto consume_type = application::GlobalVarManager::Instance().cross_consume_topic_type();
  auto is_test = application::GlobalVarManager::Instance().test();
  application::GlobalVarManager::Instance().set_cross_consume_topic_type(::cross::TopicType::kRespHelpType);
  application::GlobalVarManager::Instance().set_test(false);
  application::GlobalVarManager::Instance().sharding_config_storage().AddUserWhiteList(100);

  const std::string& topic = "cross_helper_36";
  const std::string& msg =
      "{\"LeaderT\":\"response_of_36_m\",\"LeaderCrossSeq\":-1,\"LeaderOffset\":-1,\"FollowerT\":\"response_of_36_s\","
      "\"FollowerCrossSeq\":-1,\"FollowerOffset\":-1}";
  // 先生产一条cross_helper消费
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());
  producer->ProduceMessage(topic,
                           (void*)msg.c_str(),  // NOLINT
                           msg.size(), nullptr, 0, nullptr);
  cross::CrossConsumeTaskParam task;
  task.business_type = cross::BusinessType::kFbuType;
  task.topic_type = cross::TopicType::kRespHelpType;
  task.cross_idx = 36;
  task.next_cross_seq = 1;
  task.process_matching_result =
      std::bind(biz::CommonUtils::ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  auto consumer_task_id = cross::CrossReceiver::AddConsumeTask(task);
  std::this_thread::sleep_for(std::chrono::seconds(2));

  // 发送leader切换
  const std::string& msg_change =
      "{\"LeaderT\":\"response_of_36_s\",\"LeaderCrossSeq\":10,\"LeaderOffset\":10,\"FollowerT\":\"response_of_36_m\","
      "\"FollowerCrossSeq\":10,\"FollowerOffset\":10}";
  producer->ProduceMessage(topic,
                           (void*)msg_change.c_str(),  // NOLINT
                           msg_change.size(), nullptr, 0, nullptr);
  std::this_thread::sleep_for(std::chrono::seconds(1));

  auto sdk1 = new fbu::FbuCrossSdk();
  for (auto i = 0; i < 10001; i++) {
    auto msg1 = MakeRespPkg(sdk1, i);
    producer->ProduceMessage("response_of_36_s",
                             (void*)msg1.c_str(),  // NOLINT
                             msg1.size(), nullptr, 0, nullptr);
  }

  sleep(5);
  cross::CrossReceiver::DelConsumeTask(consumer_task_id);

  application::GlobalVarManager::Instance().set_test(is_test);
  application::GlobalVarManager::Instance().set_cross_consume_topic_type(consume_type);
}

TEST_F(CrossReceiverTestM, InternalFilterSbuTest) {
  auto&& sharding_config_storage = application::GlobalVarManager::Instance().sharding_config_storage();
  application::GlobalVarManager::Instance().set_test(true);
  sharding_config_storage.Init(sharding_config::ShardingConfigKey{});
  sharding_config_storage.Load({}, {});

  auto consume_type = application::GlobalVarManager::Instance().cross_consume_topic_type();
  auto is_test = application::GlobalVarManager::Instance().test();
  application::GlobalVarManager::Instance().set_cross_consume_topic_type(::cross::TopicType::kRespHelpType);
  application::GlobalVarManager::Instance().set_test(false);
  application::GlobalVarManager::Instance().sharding_config_storage().AddUserWhiteList(100);

  std::string topic = "cross_helper_22000";
  std::string msg =
      "{\"LeaderT\":\"response_of_22000_m\",\"LeaderCrossSeq\":-1,\"LeaderOffset\":-1,\"FollowerT\":"
      "\"response_of_22000_s\",\"FollowerCrossSeq\":-1,\"FollowerOffset\":-1}";
  // 先生产一条cross_helper消费
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());
  producer->ProduceMessage(topic,
                           (void*)msg.c_str(),  // NOLINT
                           msg.size(), nullptr, 0, nullptr);
  cross::CrossConsumeTaskParam task;
  task.business_type = cross::BusinessType::kSbuType;
  task.topic_type = cross::TopicType::kRespHelpType;
  task.cross_idx = 22000;
  task.next_cross_seq = 1;
  task.process_matching_result =
      std::bind(biz::CommonUtils::ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  auto consumer_task_id = cross::CrossReceiver::AddConsumeTask(task);
  std::this_thread::sleep_for(std::chrono::seconds(2));

  // 发送leader切换
  std::string msg_change =
      "{\"LeaderT\":\"response_of_22000_s\",\"LeaderCrossSeq\":10,\"LeaderOffset\":10,\"FollowerT\":"
      "\"response_of_22000_m\",\"FollowerCrossSeq\":10,\"FollowerOffset\":10}";
  producer->ProduceMessage(topic,
                           (void*)msg_change.c_str(),  // NOLINT
                           msg_change.size(), nullptr, 0, nullptr);
  std::this_thread::sleep_for(std::chrono::seconds(1));

  sbu::SbuCrossSdk* sdk1 = new sbu::SbuCrossSdk();
  for (auto i = 0; i < 10001; i++) {
    auto msg1 = MakeSpotReqPkg(sdk1, i);
    producer->ProduceMessage("response_of_22000_s",
                             (void*)msg1.c_str(),  // NOLINT
                             msg1.size(), nullptr, 0, nullptr);
  }

  sleep(5);
  cross::CrossReceiver::DelConsumeTask(consumer_task_id);

  application::GlobalVarManager::Instance().set_test(is_test);
  application::GlobalVarManager::Instance().set_cross_consume_topic_type(consume_type);
}

static void ProcessMatchingResult(biz::cross_idx_t cross_idx, const std::shared_ptr<biz::cross::XRespBase>&) {
  LOG_INFO("cross_idx:{}", cross_idx);
}

TEST_F(CrossReceiverTestM, FilterRespTest) {
  GTEST_SKIP();
  // 发送fileter数据
  std::string topic_fbu = "filter_resp_0_37";
  std::string msg_fbu;
  biz::cross::RawXHeader header;
  header.offset = 1;
  biz::cross::response::RawXResponse res;
  biz::cross::FilterRespHeader filter_header;
  filter_header.filter_resp_seq = 0;
  filter_header.header_len = 32;
  msg_fbu = std::string(reinterpret_cast<char*>(&filter_header), sizeof(filter_header)) +
            std::string(reinterpret_cast<char*>(&header), sizeof(header)) +
            std::string(reinterpret_cast<char*>(&res), sizeof(res));
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());
  producer->ProduceMessage(topic_fbu,
                           (void*)msg_fbu.c_str(),  // NOLINT
                           msg_fbu.size(), nullptr, 0, nullptr);

  // 发送fileter数据
  std::string topic_sbu = "filter_resp_0_13000";
  biz::cross::spot::RawXHeader sbu_header;
  sbu_header.count = 1;
  sbu_header.hdr_length = sizeof(biz::cross::spot::RawXHeader);
  sbu_header.length = sizeof(biz::cross::spot::response::RawXResponse);
  sbu_header.offset = 2;
  biz::cross::spot::response::RawXResponse sbu_res;
  sbu_res.msg_length_ = sizeof(biz::cross::spot::response::RawXResponse);
  sbu_res.client_order_id_len_ = 256;
  filter_header.filter_resp_seq = 1;
  filter_header.header_len = 32;
  std::string msg_sbu = std::string(reinterpret_cast<char*>(&filter_header), sizeof(filter_header)) +
                        std::string(reinterpret_cast<char*>(&sbu_header), sizeof(sbu_header)) +
                        std::string(reinterpret_cast<char*>(&sbu_res), sizeof(sbu_res));
  producer->ProduceMessage(topic_sbu,
                           (void*)msg_sbu.c_str(),  // NOLINT
                           msg_sbu.size(), nullptr, 0, nullptr);

  std::string topic_obu = "filter_resp_0_20001";
  std::string msg_obu;
  filter_header.filter_resp_seq = 2;
  header.offset = 3;
  msg_obu = std::string(reinterpret_cast<char*>(&filter_header), sizeof(filter_header)) +
            std::string(reinterpret_cast<char*>(&header), sizeof(header)) +
            std::string(reinterpret_cast<char*>(&res), sizeof(res));

  producer->ProduceMessage(topic_obu,
                           (void*)msg_obu.c_str(),  // NOLINT
                           msg_obu.size(), nullptr, 0, nullptr);

  cross::CrossConsumeTaskParam task_fbu;
  task_fbu.business_type = cross::BusinessType::kFbuType;
  task_fbu.topic_type = cross::TopicType::kFilterRespType;
  task_fbu.cross_idx = 37;
  task_fbu.shard_id = 0;
  task_fbu.next_cross_seq = 1;
  task_fbu.process_matching_result = std::bind(ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  testing::GTEST_FLAG(death_test_style) = "threadsafe";
  ASSERT_DEATH(
      {
        cross::CrossReceiver::AddConsumeTask(task_fbu);
        sleep(1);
      },
      "");

  cross::CrossConsumeTaskParam task_sbu;
  task_sbu.business_type = cross::BusinessType::kSbuType;
  task_sbu.topic_type = cross::TopicType::kFilterRespType;
  task_sbu.cross_idx = 13000;
  task_sbu.shard_id = 0;
  task_sbu.next_cross_seq = 2;
  task_sbu.process_matching_result = std::bind(ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  ASSERT_DEATH(
      {
        cross::CrossReceiver::AddConsumeTask(task_sbu);
        sleep(1);
      },
      "");

  cross::CrossConsumeTaskParam task_obu;
  task_obu.business_type = cross::BusinessType::kObuType;
  task_obu.topic_type = cross::TopicType::kFilterRespType;
  task_obu.cross_idx = 20001;
  task_obu.shard_id = 0;
  task_obu.next_cross_seq = 3;
  task_obu.process_matching_result = std::bind(ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  ASSERT_DEATH(
      {
        cross::CrossReceiver::AddConsumeTask(task_obu);
        sleep(1);
      },
      "");
}

TEST_F(CrossReceiverTestM, DetectMessage) {
  cross::CrossConsumeTaskParam task_obu;
  task_obu.business_type = cross::BusinessType::kObuType;
  task_obu.topic_type = cross::TopicType::kFilterRespType;
  task_obu.cross_idx = 20001;
  task_obu.shard_id = 0;
  task_obu.next_cross_seq = 1;
  task_obu.process_matching_result = nullptr;
  auto context = std::make_shared<cross::CrossConsumerContext>(task_obu);
  cross::DetectMessageHandle handle("response_20001_m", nullptr, 1, 1, context);
  mock::HdtsMessage msg;
  std::string data(200, '1');
  msg.topic_ = "response_20001_m";
  msg.pay_load_ = data;
  handle.ProcessMessage(msg);
}

TEST_F(CrossReceiverTestM, TestMessageHandle) {
  auto handle = std::make_shared<cross::MessageHandleBase>(nullptr, nullptr);

  auto wait1_ms = handle->GetSalvageTimeoutMs(10);
  auto wait2_ms = handle->GetSalvageTimeoutMs(110);
  auto wait3_ms = handle->GetSalvageTimeoutMs(510);
  EXPECT_EQ(wait1_ms, 10 * 1000);
  EXPECT_EQ(wait2_ms, 20 * 1000);
  EXPECT_EQ(wait3_ms, 30 * 1000);
  std::thread notify([handle]() {
    std::this_thread::sleep_for(std::chrono::seconds(10));
    handle->NotifyConsumEnd();
  });
  auto ready1 = handle->WaitConsumeEndWithTimeOut(10);
  EXPECT_EQ(ready1, false);
  auto ready2 = handle->WaitConsumeEndWithTimeOut(100000);
  EXPECT_EQ(ready2, true);
  notify.join();
}

TEST_F(CrossReceiverTestM, TestCrossConsumeTask1) {
  application::GlobalVarManager::Instance().set_force_use_filter_helper(true);
  cross::CrossConsumeTaskParam task;
  task.business_type = cross::BusinessType::kObuType;
  task.topic_type = cross::TopicType::kFilterRespType;
  task.cross_idx = 20001;
  task.shard_id = 0;
  task.next_cross_seq = 1;
  task.process_matching_result = nullptr;
  cross::CrossConsumeTask consume_task(1, task);
  consume_task.TryRemediateFromSlave(0, 10, 100, 0);
  std::string topic = "test";
  bbase::hdts::Offset master_offset = 10;
  biz::seq_t master_seq = 10;
  biz::seq_t next_cross_seq = 0;
  bbase::hdts::Offset high_offset = 70;

  // next_cross_seq == 0
  auto start_offset = consume_task.GetOffsetByCrossSeq(topic, master_offset, master_seq, next_cross_seq, high_offset);
  EXPECT_EQ(start_offset, next_cross_seq);

  // next_cross_seq >= master_seq && master_offset >0
  next_cross_seq = 40;
  start_offset = consume_task.GetOffsetByCrossSeq(topic, master_offset, master_seq, next_cross_seq, high_offset);
  EXPECT_EQ(start_offset, master_seq);

  sleep(1);
}

TEST_F(CrossReceiverTestM, TestCrossConsumeTask2) {
  cross::CrossConsumeTaskParam task;
  task.business_type = cross::BusinessType::kObuType;
  task.topic_type = cross::TopicType::kFilterRespType;
  task.cross_idx = 20001;
  task.shard_id = 0;
  task.next_cross_seq = 1;
  task.process_matching_result = nullptr;
  cross::CrossConsumeTask consume_task(1, task);
  consume_task.TryRemediateFromSlave(0, 10, 100, 0);
  std::string topic = "test";
  bbase::hdts::Offset master_offset = 10;
  biz::seq_t master_seq = 10;
  biz::seq_t next_cross_seq = 0;
  bbase::hdts::Offset high_offset = 70;
  // next_cross_seq >= master_seq && master_offset < 0
  next_cross_seq = 40;
  master_offset = -1;  // cross_helper第一条数据 master_offset会是-1
  auto start_offset = consume_task.GetOffsetByCrossSeq(topic, master_offset, master_seq, next_cross_seq, high_offset);
  EXPECT_EQ(start_offset, 0);

  // master_offset - (master_seq - next_cross_seq) < 0
  master_offset = 10;
  master_seq = 20;
  next_cross_seq = 1;
  start_offset = consume_task.GetOffsetByCrossSeq(topic, master_offset, master_seq, next_cross_seq, high_offset);
  EXPECT_EQ(start_offset, 0);

  sleep(1);
}

TEST_F(CrossReceiverTestM, TestAdjustOffset) {
  auto params = std::make_shared<cross::CrossConsumeParam>();
  params->consume_task_id = 1;
  params->topic_name = "test";
  params->business_type = cross::BusinessType::kObuType;
  params->alone_cb_thread = false;
  params->start_offset = 10;
  params->end_offset = -1;
  cross::CrossConsume consume1(params);
  consume1.AdjustOffset();
  EXPECT_EQ(consume1.final_start_offset_, 1);

  params->start_offset = dtssdk::LogicOffset::kMinOffset;
  cross::CrossConsume consume2(params);
  consume2.AdjustOffset();
  EXPECT_EQ(consume2.final_start_offset_, 0);

  params->start_offset = dtssdk::LogicOffset::kMaxOffset;
  cross::CrossConsume consume3(params);
  consume3.AdjustOffset();
  EXPECT_EQ(consume3.final_start_offset_, 1);

  params->start_offset = cross::OffsetTail(10);
  cross::CrossConsume consume4(params);
  consume4.AdjustOffset();
  EXPECT_EQ(consume4.final_start_offset_, 0);
}
TEST_F(CrossReceiverTestM, FilterRespTest2) {
  application::GlobalVarManager::Instance().set_force_use_filter_helper(true);
  // 发送fileter数据
  dtssdk::IDtsSdk::Instance()->Init(nullptr, nullptr);
  std::string topic_fbu = "filter_resp_0_37";
  std::string msg_fbu;
  // biz::cross::spot::RawXHeader header;
  // biz::cross::spot::request::RawXRequest req;
  biz::cross::FilterRespHeader filter_header;
  filter_header.filter_resp_seq = 0;
  filter_header.header_len = 32;
  msg_fbu = std::string(reinterpret_cast<char*>(&filter_header), sizeof(filter_header));
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());
  producer->ProduceMessage(topic_fbu,
                           (void*)msg_fbu.c_str(),  // NOLINT
                           msg_fbu.size(), nullptr, 0, nullptr);
  std::this_thread::sleep_for(std::chrono::seconds(1));
  cross::CrossConsumeTaskParam task_fbu;
  task_fbu.business_type = cross::BusinessType::kFbuType;
  task_fbu.topic_type = cross::TopicType::kFilterRespType;
  task_fbu.cross_idx = 37;
  task_fbu.shard_id = 0;
  task_fbu.next_cross_seq = 1;
  task_fbu.process_matching_result = std::bind(ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  testing::GTEST_FLAG(death_test_style) = "threadsafe";
  // ASSERT_DEATH(
  //    {
  cross::CrossReceiver::AddConsumeTask(task_fbu);
  sleep(1);
  //    },
  //    "");

  auto task_id = cross::CrossConsumerManager::Instance().GetConsumeTaskId();
  cross::CrossConsumeTaskParam consume_param;
  auto task = std::make_shared<cross::CrossConsumeTask>(task_id, consume_param);
  cross::CrossConsumerManager::Instance().InsertConsumeTask(task_id, task);
  cross::CrossReceiver::DelConsumeTask(task_id);
}

TEST_F(CrossReceiverTestM, FilterRespTest3) {
  // 发送fileter数据
  std::string topic_fbu = "filter_resp_0_37";
  std::string msg_fbu;
  biz::cross::RawXHeader header;
  biz::cross::request::XRequest req;
  // 组装包头
  header.magic_cookie = biz::cross::kMagicCookie;
  header.hdr_version = biz::cross::kVersion;
  header.hdr_length = sizeof(biz::cross::RawXHeader);
  header.action = 0;
  header.offset = 0;
  header.length = biz::cross::request::kSizeOfRequest;
  header.count = 1;
  header.passthrough = 1;
  // 组装包体
  req.req_.magic_cookie_ = biz::cross::request::kMagic;
  req.req_.msg_version_ = biz::cross::request::kVersion;
  req.req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  req.req_.smp_group_ = 0;
  req.req_.sender_comp_id_ = 1001;
  req.req_.target_comp_id_ = 0;
  req.req_.union_.price_scale_ = 4;
  req.req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  req.req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  req.req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  req.req_.order_type_ = '2';
  req.req_.side_ = '1';
  req.req_.time_in_force_ = '1';
  req.req_.symbol_ = 5;
  req.req_.order_qty_ = 0;
  req.req_.price_ = 0;
  req.req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  req.req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  req.req_.created_by_ = 31;
  req.req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  req.req_.cl_ord_id_ = boost::uuids::random_generator()();
  req.req_.orig_cl_ord_id = {0};
  req.req_.msg_type_ = biz::cross::request::kMTNewOrder;

  biz::cross::FilterRespHeader filter_header;
  filter_header.filter_resp_seq = 0;
  filter_header.header_len = 32;
  msg_fbu = std::string(reinterpret_cast<char*>(&filter_header), sizeof(filter_header)) +
            std::string(reinterpret_cast<char*>(&header), sizeof(header)) +
            std::string(reinterpret_cast<char*>(&req), sizeof(req));

  req.ToBinaryBuf(static_cast<char*>(msg_fbu.data()) + sizeof(biz::cross::RawXHeader) +
                  sizeof(biz::cross::FilterRespHeader));

  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());
  producer->ProduceMessage(topic_fbu,
                           (void*)msg_fbu.c_str(),  // NOLINT
                           msg_fbu.size(), nullptr, 0, nullptr);

  cross::CrossConsumeTaskParam task_fbu;
  task_fbu.business_type = cross::BusinessType::kFbuType;
  task_fbu.topic_type = cross::TopicType::kFilterRespType;
  task_fbu.cross_idx = 37;
  task_fbu.shard_id = 0;
  task_fbu.next_cross_seq = 1;
  task_fbu.process_matching_result = std::bind(ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  testing::GTEST_FLAG(death_test_style) = "threadsafe";
  // ASSERT_DEATH(
  //     {
  cross::CrossReceiver::AddConsumeTask(task_fbu);
  sleep(1);
  // },
  // "");
}

TEST_F(CrossReceiverTestM, FilterRespTest4) {
  // 发送fileter数据
  std::string topic_fbu = "filter_resp_0_37";
  std::string msg_fbu;
  biz::cross::RawXHeader header;
  biz::cross::request::XRequest req;
  // 组装包头
  header.magic_cookie = biz::cross::kMagicCookie;
  header.hdr_version = biz::cross::kVersion;
  header.hdr_length = sizeof(biz::cross::RawXHeader);
  header.action = 0;
  header.offset = 3;
  header.length = biz::cross::request::kSizeOfRequest;
  header.count = 1;
  header.passthrough = 1;
  // 组装包体
  req.req_.magic_cookie_ = biz::cross::request::kMagic;
  req.req_.msg_version_ = biz::cross::request::kVersion;
  req.req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  req.req_.smp_group_ = 0;
  req.req_.sender_comp_id_ = 1001;
  req.req_.target_comp_id_ = 0;
  req.req_.union_.price_scale_ = 4;
  req.req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  req.req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  req.req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  req.req_.order_type_ = '2';
  req.req_.side_ = '1';
  req.req_.time_in_force_ = '1';
  req.req_.symbol_ = 5;
  req.req_.order_qty_ = 0;
  req.req_.price_ = 0;
  req.req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  req.req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  req.req_.created_by_ = 31;
  req.req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  req.req_.cl_ord_id_ = boost::uuids::random_generator()();
  req.req_.orig_cl_ord_id = {0};
  req.req_.msg_type_ = biz::cross::request::kMTNewOrder;

  biz::cross::FilterRespHeader filter_header;
  filter_header.filter_resp_seq = 0;
  filter_header.header_len = 32;
  msg_fbu = std::string(reinterpret_cast<char*>(&filter_header), sizeof(filter_header)) +
            std::string(reinterpret_cast<char*>(&header), sizeof(header)) +
            std::string(reinterpret_cast<char*>(&req), sizeof(req));

  req.ToBinaryBuf(static_cast<char*>(msg_fbu.data()) + sizeof(biz::cross::RawXHeader) +
                  sizeof(biz::cross::FilterRespHeader));

  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());
  producer->ProduceMessage(topic_fbu,
                           (void*)msg_fbu.c_str(),  // NOLINT
                           msg_fbu.size(), nullptr, 0, nullptr);

  cross::CrossConsumeTaskParam task_fbu;
  task_fbu.business_type = cross::BusinessType::kFbuType;
  task_fbu.topic_type = cross::TopicType::kFilterRespType;
  task_fbu.cross_idx = 37;
  task_fbu.shard_id = 0;
  task_fbu.next_cross_seq = 1;
  task_fbu.process_matching_result = std::bind(ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  testing::GTEST_FLAG(death_test_style) = "threadsafe";
  // ASSERT_DEATH(
  //     {
  cross::CrossReceiver::AddConsumeTask(task_fbu);
  sleep(1);
  // },
  // "");
}

TEST_F(CrossReceiverTestM, FilterRespTest5) {
  // 发送fileter数据
  std::string topic_fbu = "filter_resp_0_37";
  std::string msg_fbu;
  biz::cross::RawXHeaderV2 header;
  // biz::cross::request::XRequest req;
  biz::cross::response::XResponse rsp;
  // 组装包头
  header.x_header.magic_cookie = biz::cross::kMagicCookie;
  header.x_header.hdr_version = biz::cross::kVersion;
  header.x_header.hdr_length = sizeof(biz::cross::RawXHeaderV2);
  header.x_header.action = 0;
  header.x_header.offset = 3;
  header.x_header.length = sizeof(biz::cross::response::XResponse);
  header.x_header.count = 1;
  header.x_header.passthrough = 0;
  // 组装包体
  rsp.rsp_.magic_cookie_ = biz::cross::request::kMagic;
  rsp.rsp_.msg_version_ = biz::cross::request::kVersion;
  rsp.rsp_.msg_length_ = sizeof(biz::cross::response::XResponse);
  rsp.rsp_.smp_group_ = 0;
  rsp.rsp_.sender_comp_id_ = 1001;
  rsp.rsp_.target_comp_id_ = 1000;
  rsp.rsp_.price_scale_ = 4;
  rsp.rsp_.smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  rsp.rsp_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  rsp.rsp_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  rsp.rsp_.order_type_ = '5';
  rsp.rsp_.side_ = '1';
  rsp.rsp_.symbol_ = 0;
  rsp.rsp_.order_qty_ = 0;
  rsp.rsp_.price_ = 0;
  rsp.rsp_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  rsp.rsp_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  rsp.rsp_.created_by_ = 31;
  rsp.rsp_.canceled_by_ = biz::cross::request::kCanceledByUser;
  rsp.rsp_.cl_ord_id_ = boost::uuids::random_generator()();
  rsp.rsp_.msg_type_ = 0x3800;

  biz::cross::FilterRespHeader filter_header;
  filter_header.filter_resp_seq = 0;
  filter_header.header_len = 32;
  msg_fbu = std::string(reinterpret_cast<char*>(&filter_header), sizeof(filter_header)) +
            std::string(reinterpret_cast<char*>(&header), sizeof(header)) +
            std::string(reinterpret_cast<char*>(&rsp), sizeof(rsp));

  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());
  producer->ProduceMessage(topic_fbu,
                           (void*)msg_fbu.c_str(),  // NOLINT
                           msg_fbu.size(), nullptr, 0, nullptr);

  cross::CrossConsumeTaskParam task_fbu;
  task_fbu.business_type = cross::BusinessType::kFbuType;
  task_fbu.topic_type = cross::TopicType::kFilterRespType;
  task_fbu.cross_idx = 37;
  task_fbu.shard_id = 0;
  task_fbu.next_cross_seq = 1;
  task_fbu.process_matching_result = std::bind(ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  testing::GTEST_FLAG(death_test_style) = "threadsafe";
  // ASSERT_DEATH(
  //     {
  cross::CrossReceiver::AddConsumeTask(task_fbu);
  sleep(1);
  // },
  // "");
}
// sbu:一致性检查失败,现货数据包
TEST_F(CrossReceiverTestM, FilterRespTest_check_offset) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());

  biz::cross_idx_t cross_idx = 5;
  int32_t shard_id = 0;
  // bbase::hdts::Offset resp_offset_ = 0;
  // cross_seq和last_processed_offset_hint是对应关系
  biz::seq_t cross_seq = 97;
  bbase::hdts::Offset last_processed_offset_hint = 96;

  cross::BusinessType business_type_{cross::BusinessType::kSbuType};
  auto filter_info = cross::FilterInfo(cross_idx, shard_id, cross_seq, business_type_);
  std::string topic = "filter_resp_" + std::to_string(shard_id) + "_" + std::to_string(cross_idx);
  biz::cross::FilterRespHeader filter_header;
  sbu::yijin::MatchSdk::order::Header header;
  sbu::yijin::MatchSdk::order::Request request;
  // 先写入100条数据初始化一下
  int i = 0;
  for (i = 0; i < 100; ++i) {
    filter_header.filter_resp_seq = i;
    filter_header.header_len = sizeof(biz::cross::FilterRespHeader);
    header._offset = i;
    header._magicCookie = sbu::yijin::MatchSdk::order::Header::MAGIC;
    header._hdrLength = sizeof(sbu::yijin::MatchSdk::order::Header);
    header._length = sizeof(sbu::yijin::MatchSdk::order::Request);
    header._msgType = 102;  // 该字段大于等于100为透传包
    request._msgLength = header._length;
    std::string msg;
    int32_t pos = 0;
    msg.resize(filter_header.header_len + header._hdrLength + header._length);
    memcpy(msg.data() + pos, &filter_header, sizeof(filter_header));
    pos += sizeof(filter_header);
    memcpy(msg.data() + pos, &header, sizeof(header));
    pos += sizeof(header);
    memcpy(msg.data() + pos, &request, sizeof(request));
    int32_t msg_len = msg.size();

    producer->ProduceMessage(topic,
                             (void*)msg.c_str(),  // NOLINT
                             msg_len, nullptr, 0, nullptr);
  }
  std::this_thread::sleep_for(std::chrono::milliseconds(1000));  // dtssdk_mock 数据准备太慢，这里等一下
  EXPECT_EQ(filter_info.LastProcessedOffsetCheck(last_processed_offset_hint, cross_seq), true);
}

// fbu:期货msg
TEST_F(CrossReceiverTestM, FilterHelper1_1) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());

  biz::cross_idx_t cross_idx = 5;
  int32_t shard_id = 0;
  biz::seq_t cross_seq = 16;
  bbase::hdts::Offset last_processed_offset_hint = 16;

  cross::BusinessType business_type_{cross::BusinessType::kFbuType};
  auto filter_info = cross::FilterInfo(cross_idx, shard_id, cross_seq, business_type_);
  std::string topic = "filter_resp_" + std::to_string(shard_id) + "_" + std::to_string(cross_idx);
  biz::cross::FilterRespHeader filter_header;
  fbu::Header header;
  fbu::yijin::MatchSdk::order::Request request;
  // 先写入100条数据初始化一下
  for (int i = 0; i < 100; ++i) {
    filter_header.filter_resp_seq = i;
    filter_header.header_len = sizeof(biz::cross::FilterRespHeader);
    header._offset = i;
    header._hdrLength = sizeof(fbu::Header);
    header._length = sizeof(fbu::yijin::MatchSdk::order::Request);
    header._passThrough = true;
    request._msgLength = header._length;
    std::string msg;
    int32_t pos = 0;
    msg.resize(filter_header.header_len + header._hdrLength + header._length);
    memcpy(msg.data() + pos, &filter_header, sizeof(filter_header));
    pos += sizeof(filter_header);
    memcpy(msg.data() + pos, &header, sizeof(header));
    pos += sizeof(header);
    memcpy(msg.data() + pos, &request, sizeof(request));
    int32_t msg_len = msg.size();
    producer->ProduceMessage(topic,
                             (void*)msg.c_str(),  // NOLINT
                             msg_len, nullptr, 0, nullptr);
  }

  // std::this_thread::sleep_for(std::chrono::milliseconds(1000));  // dtssdk_mock 数据准备太慢，这里等一下
  EXPECT_EQ(filter_info.LastProcessedOffsetCheck(last_processed_offset_hint, cross_seq), false);
}

// fbu:期货msg  next_cross_seq = std::numeric_limits<int64_t>::max()
TEST_F(CrossReceiverTestM, FilterHelper1_2) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());

  biz::cross_idx_t cross_idx = 5;
  int32_t shard_id = 0;
  biz::seq_t cross_seq = std::numeric_limits<int64_t>::max();
  bbase::hdts::Offset last_processed_offset_hint = 16;

  cross::BusinessType business_type_{cross::BusinessType::kFbuType};
  auto filter_info = cross::FilterInfo(cross_idx, shard_id, cross_seq, business_type_);
  std::string topic = "filter_resp_" + std::to_string(shard_id) + "_" + std::to_string(cross_idx);
  biz::cross::FilterRespHeader filter_header;
  fbu::Header header;
  fbu::yijin::MatchSdk::order::Request request;
  // 先写入100条数据初始化一下
  for (int i = 0; i < 100; ++i) {
    filter_header.filter_resp_seq = i;
    filter_header.header_len = sizeof(biz::cross::FilterRespHeader);
    header._offset = i;
    header._hdrLength = sizeof(fbu::Header);
    header._length = sizeof(fbu::yijin::MatchSdk::order::Request);
    header._passThrough = true;
    request._msgLength = header._length;
    std::string msg;
    int32_t pos = 0;
    msg.resize(filter_header.header_len + header._hdrLength + header._length);
    memcpy(msg.data() + pos, &filter_header, sizeof(filter_header));
    pos += sizeof(filter_header);
    memcpy(msg.data() + pos, &header, sizeof(header));
    pos += sizeof(header);
    memcpy(msg.data() + pos, &request, sizeof(request));
    int32_t msg_len = msg.size();
    producer->ProduceMessage(topic,
                             (void*)msg.c_str(),  // NOLINT
                             msg_len, nullptr, 0, nullptr);
  }

  // std::this_thread::sleep_for(std::chrono::milliseconds(1000));  // dtssdk_mock 数据准备太慢，这里等一下
  EXPECT_EQ(filter_info.LastProcessedOffsetCheck(last_processed_offset_hint, cross_seq), false);
}

// fbu:期货msg  last_processed_offset_hint = 0
TEST_F(CrossReceiverTestM, FilterHelper1_3) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());

  biz::cross_idx_t cross_idx = 5;
  int32_t shard_id = 0;
  biz::seq_t cross_seq = std::numeric_limits<int64_t>::max();
  bbase::hdts::Offset last_processed_offset_hint = 16;

  cross::BusinessType business_type_{cross::BusinessType::kFbuType};
  auto filter_info = cross::FilterInfo(cross_idx, shard_id, cross_seq, business_type_);
  std::string topic = "filter_resp_" + std::to_string(shard_id) + "_" + std::to_string(cross_idx);
  biz::cross::FilterRespHeader filter_header;
  fbu::Header header;
  fbu::yijin::MatchSdk::order::Request request;
  // 先写入100条数据初始化一下
  for (int i = 0; i < 100; ++i) {
    filter_header.filter_resp_seq = i;
    filter_header.header_len = sizeof(biz::cross::FilterRespHeader);
    header._offset = i;
    header._hdrLength = sizeof(fbu::Header);
    header._length = sizeof(fbu::yijin::MatchSdk::order::Request);
    header._passThrough = true;
    request._msgLength = header._length;
    std::string msg;
    int32_t pos = 0;
    msg.resize(filter_header.header_len + header._hdrLength + header._length);
    memcpy(msg.data() + pos, &filter_header, sizeof(filter_header));
    pos += sizeof(filter_header);
    memcpy(msg.data() + pos, &header, sizeof(header));
    pos += sizeof(header);
    memcpy(msg.data() + pos, &request, sizeof(request));
    int32_t msg_len = msg.size();
    producer->ProduceMessage(topic,
                             (void*)msg.c_str(),  // NOLINT
                             msg_len, nullptr, 0, nullptr);
  }

  // std::this_thread::sleep_for(std::chrono::milliseconds(1000));  // dtssdk_mock 数据准备太慢，这里等一下
  EXPECT_EQ(filter_info.LastProcessedOffsetCheck(last_processed_offset_hint, cross_seq), false);
}

// fbu:期货msg  一致性校验通过
TEST_F(CrossReceiverTestM, FilterHelper1_4) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());

  biz::cross_idx_t cross_idx = 5;
  int32_t shard_id = 0;
  biz::seq_t cross_seq = 17;
  bbase::hdts::Offset last_processed_offset_hint = 16;

  cross::BusinessType business_type_{cross::BusinessType::kFbuType};
  auto filter_info = cross::FilterInfo(cross_idx, shard_id, cross_seq, business_type_);
  std::string topic = "filter_resp_" + std::to_string(shard_id) + "_" + std::to_string(cross_idx);
  biz::cross::FilterRespHeader filter_header;
  fbu::Header header;
  fbu::yijin::MatchSdk::order::Request request;
  // 先写入100条数据初始化一下
  for (int i = 0; i < 100; ++i) {
    filter_header.filter_resp_seq = i;
    filter_header.header_len = sizeof(biz::cross::FilterRespHeader);
    header._offset = i;
    header._hdrLength = sizeof(fbu::Header);
    header._length = sizeof(fbu::yijin::MatchSdk::order::Request);
    header._passThrough = true;
    request._msgLength = header._length;
    std::string msg;
    int32_t pos = 0;
    msg.resize(filter_header.header_len + header._hdrLength + header._length);
    memcpy(msg.data() + pos, &filter_header, sizeof(filter_header));
    pos += sizeof(filter_header);
    memcpy(msg.data() + pos, &header, sizeof(header));
    pos += sizeof(header);
    memcpy(msg.data() + pos, &request, sizeof(request));
    int32_t msg_len = msg.size();
    producer->ProduceMessage(topic,
                             (void*)msg.c_str(),  // NOLINT
                             msg_len, nullptr, 0, nullptr);
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(1000));  // dtssdk_mock 数据准备太慢，这里等一下
  EXPECT_EQ(filter_info.LastProcessedOffsetCheck(last_processed_offset_hint, cross_seq), true);
}

// fbu:期货msg  msg长度小于FilterRespHeader,解包失败, 小于32字节
TEST_F(CrossReceiverTestM, FilterHelper1_5) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());

  biz::cross_idx_t cross_idx = 5;
  int32_t shard_id = 0;
  biz::seq_t cross_seq = 17;
  bbase::hdts::Offset last_processed_offset_hint = 16;

  cross::BusinessType business_type_{cross::BusinessType::kFbuType};
  auto filter_info = cross::FilterInfo(cross_idx, shard_id, cross_seq, business_type_);
  std::string topic = "filter_resp_" + std::to_string(shard_id) + "_" + std::to_string(cross_idx);
  biz::cross::FilterRespHeader filter_header;
  fbu::Header header;
  fbu::yijin::MatchSdk::order::Request request;
  // 先写入100条数据初始化一下
  for (int i = 0; i < 100; ++i) {
    filter_header.filter_resp_seq = i;
    filter_header.header_len = sizeof(biz::cross::FilterRespHeader);
    header._offset = i;
    header._hdrLength = sizeof(fbu::Header);
    header._length = sizeof(fbu::yijin::MatchSdk::order::Request);
    header._passThrough = true;
    request._msgLength = header._length;
    std::string msg;
    int32_t pos = 0;
    msg.resize(filter_header.header_len + header._hdrLength + header._length);
    memcpy(msg.data() + pos, &filter_header, sizeof(filter_header));
    pos += sizeof(filter_header);
    memcpy(msg.data() + pos, &header, sizeof(header));
    pos += sizeof(header);
    memcpy(msg.data() + pos, &request, sizeof(request));
    int32_t msg_len = 10;  // msg.size();
    producer->ProduceMessage(topic,
                             (void*)msg.c_str(),  // NOLINT
                             msg_len, nullptr, 0, nullptr);
  }

  // std::this_thread::sleep_for(std::chrono::milliseconds(1000));  // dtssdk_mock 数据准备太慢，这里等一下
  EXPECT_EQ(filter_info.LastProcessedOffsetCheck(last_processed_offset_hint, cross_seq), false);
}
// fbu:期货msg  msg长度大于32,小于32+header,解包失败
TEST_F(CrossReceiverTestM, FilterHelper1_6) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());

  biz::cross_idx_t cross_idx = 5;
  int32_t shard_id = 0;
  biz::seq_t cross_seq = 17;
  bbase::hdts::Offset last_processed_offset_hint = 16;

  cross::BusinessType business_type_{cross::BusinessType::kFbuType};
  auto filter_info = cross::FilterInfo(cross_idx, shard_id, cross_seq, business_type_);
  std::string topic = "filter_resp_" + std::to_string(shard_id) + "_" + std::to_string(cross_idx);
  biz::cross::FilterRespHeader filter_header;
  fbu::Header header;
  fbu::yijin::MatchSdk::order::Request request;
  // 先写入100条数据初始化一下
  for (int i = 0; i < 100; ++i) {
    filter_header.filter_resp_seq = i;
    filter_header.header_len = sizeof(biz::cross::FilterRespHeader);
    header._offset = i;
    header._hdrLength = sizeof(fbu::Header);
    header._length = sizeof(fbu::yijin::MatchSdk::order::Request);
    header._passThrough = true;
    request._msgLength = header._length;
    std::string msg;
    int32_t pos = 0;
    msg.resize(filter_header.header_len + header._hdrLength + header._length);
    memcpy(msg.data() + pos, &filter_header, sizeof(filter_header));
    pos += sizeof(filter_header);
    memcpy(msg.data() + pos, &header, sizeof(header));
    pos += sizeof(header);
    memcpy(msg.data() + pos, &request, sizeof(request));
    int32_t msg_len = 40;  // msg.size();
    producer->ProduceMessage(topic,
                             (void*)msg.c_str(),  // NOLINT
                             msg_len, nullptr, 0, nullptr);
  }

  // std::this_thread::sleep_for(std::chrono::milliseconds(1000));  // dtssdk_mock 数据准备太慢，这里等一下
  EXPECT_EQ(filter_info.LastProcessedOffsetCheck(last_processed_offset_hint, cross_seq), false);
}

// fbu:期货msg  header magic 有误,解包失败
TEST_F(CrossReceiverTestM, FilterHelper1_7) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());

  biz::cross_idx_t cross_idx = 5;
  int32_t shard_id = 0;
  biz::seq_t cross_seq = 17;
  bbase::hdts::Offset last_processed_offset_hint = 16;

  cross::BusinessType business_type_{cross::BusinessType::kFbuType};
  auto filter_info = cross::FilterInfo(cross_idx, shard_id, cross_seq, business_type_);
  std::string topic = "filter_resp_" + std::to_string(shard_id) + "_" + std::to_string(cross_idx);
  biz::cross::FilterRespHeader filter_header;
  fbu::Header header;
  fbu::yijin::MatchSdk::order::Request request;
  // 先写入100条数据初始化一下
  for (int i = 0; i < 100; ++i) {
    filter_header.filter_resp_seq = i;
    filter_header.header_len = sizeof(biz::cross::FilterRespHeader);
    header._magicCookie = 0xEEFF1357EEFF1358;  // fbu::Header::MAGIC;
    header._offset = i;
    header._hdrLength = sizeof(fbu::Header);
    header._length = sizeof(fbu::yijin::MatchSdk::order::Request);
    header._passThrough = true;
    request._msgLength = header._length;
    std::string msg;
    int32_t pos = 0;
    msg.resize(filter_header.header_len + header._hdrLength + header._length);
    memcpy(msg.data() + pos, &filter_header, sizeof(filter_header));
    pos += sizeof(filter_header);
    memcpy(msg.data() + pos, &header, sizeof(header));
    pos += sizeof(header);
    memcpy(msg.data() + pos, &request, sizeof(request));
    int32_t msg_len = 40;  // msg.size();
    producer->ProduceMessage(topic,
                             (void*)msg.c_str(),  // NOLINT
                             msg_len, nullptr, 0, nullptr);
  }

  // std::this_thread::sleep_for(std::chrono::milliseconds(1000));  // dtssdk_mock 数据准备太慢，这里等一下
  EXPECT_EQ(filter_info.LastProcessedOffsetCheck(last_processed_offset_hint, cross_seq), false);
}

// sbu:header magic 有误,解包失败
TEST_F(CrossReceiverTestM, FilterRespTest_8) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());

  biz::cross_idx_t cross_idx = 5;
  int32_t shard_id = 0;
  // bbase::hdts::Offset resp_offset_ = 0;
  // cross_seq和last_processed_offset_hint是对应关系
  biz::seq_t cross_seq = 97;
  bbase::hdts::Offset last_processed_offset_hint = 96;

  cross::BusinessType business_type_{cross::BusinessType::kSbuType};
  auto filter_info = cross::FilterInfo(cross_idx, shard_id, cross_seq, business_type_);
  std::string topic = "filter_resp_" + std::to_string(shard_id) + "_" + std::to_string(cross_idx);
  biz::cross::FilterRespHeader filter_header;
  sbu::yijin::MatchSdk::order::Header header;
  sbu::yijin::MatchSdk::order::Request request;
  // 先写入100条数据初始化一下
  int i = 0;
  for (i = 0; i < 100; ++i) {
    filter_header.filter_resp_seq = i;
    filter_header.header_len = sizeof(biz::cross::FilterRespHeader);
    header._offset = i;
    header._magicCookie = 0x0EFF1357EEFF1358;  // sbu::yijin::MatchSdk::order::Header::MAGIC;
    header._hdrLength = sizeof(sbu::yijin::MatchSdk::order::Header);
    header._length = sizeof(sbu::yijin::MatchSdk::order::Request);
    header._msgType = 102;  // 该字段大于等于100为透传包
    request._msgLength = header._length;
    std::string msg;
    int32_t pos = 0;
    msg.resize(filter_header.header_len + header._hdrLength + header._length);
    memcpy(msg.data() + pos, &filter_header, sizeof(filter_header));
    pos += sizeof(filter_header);
    memcpy(msg.data() + pos, &header, sizeof(header));
    pos += sizeof(header);
    memcpy(msg.data() + pos, &request, sizeof(request));
    int32_t msg_len = msg.size();

    producer->ProduceMessage(topic,
                             (void*)msg.c_str(),  // NOLINT
                             msg_len, nullptr, 0, nullptr);
  }
  // std::this_thread::sleep_for(std::chrono::milliseconds(1000));  // dtssdk_mock 数据准备太慢，这里等一下
  EXPECT_EQ(filter_info.LastProcessedOffsetCheck(last_processed_offset_hint, cross_seq), false);
}
// fbu:期货msg  未知的biz_type
TEST_F(CrossReceiverTestM, FilterHelper1_9) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());

  biz::cross_idx_t cross_idx = 5;
  int32_t shard_id = 0;
  biz::seq_t cross_seq = 17;
  bbase::hdts::Offset last_processed_offset_hint = 16;

  cross::BusinessType business_type_{cross::BusinessType::kUnkown};
  auto filter_info = cross::FilterInfo(cross_idx, shard_id, cross_seq, business_type_);
  std::string topic = "filter_resp_" + std::to_string(shard_id) + "_" + std::to_string(cross_idx);
  biz::cross::FilterRespHeader filter_header;
  fbu::Header header;
  fbu::yijin::MatchSdk::order::Request request;
  // 先写入100条数据初始化一下
  for (int i = 0; i < 100; ++i) {
    filter_header.filter_resp_seq = i;
    filter_header.header_len = sizeof(biz::cross::FilterRespHeader);
    header._magicCookie = 0xEEFF1357EEFF1358;  // fbu::Header::MAGIC;
    header._offset = i;
    header._hdrLength = sizeof(fbu::Header);
    header._length = sizeof(fbu::yijin::MatchSdk::order::Request);
    header._passThrough = true;
    request._msgLength = header._length;
    std::string msg;
    int32_t pos = 0;
    msg.resize(filter_header.header_len + header._hdrLength + header._length);
    memcpy(msg.data() + pos, &filter_header, sizeof(filter_header));
    pos += sizeof(filter_header);
    memcpy(msg.data() + pos, &header, sizeof(header));
    pos += sizeof(header);
    memcpy(msg.data() + pos, &request, sizeof(request));
    int32_t msg_len = 40;  // msg.size();
    producer->ProduceMessage(topic,
                             (void*)msg.c_str(),  // NOLINT
                             msg_len, nullptr, 0, nullptr);
  }

  // std::this_thread::sleep_for(std::chrono::milliseconds(1000));  // dtssdk_mock 数据准备太慢，这里等一下
  EXPECT_EQ(filter_info.LastProcessedOffsetCheck(last_processed_offset_hint, cross_seq), false);
}

//
TEST_F(CrossReceiverTestM, FilterRespTest8) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  auto* producer = (mock::HdtsAsyncProducer*)(dtssdk::IDtsSdk::Instance()->GetAsyncProducer());

  application::GlobalVarManager::Instance().set_force_use_filter_helper(false);

  biz::cross_idx_t cross_idx = 5;
  int32_t shard_id = 0;
  biz::seq_t cross_seq = 17;
  bbase::hdts::Offset last_processed_offset_hint = 16;

  cross::BusinessType business_type_{cross::BusinessType::kFbuType};
  auto filter_info = cross::FilterInfo(cross_idx, shard_id, cross_seq, business_type_);
  std::string topic = "filter_resp_" + std::to_string(shard_id) + "_" + std::to_string(cross_idx);
  biz::cross::FilterRespHeader filter_header;
  fbu::Header header;
  fbu::yijin::MatchSdk::order::Request request;
  // 先写入100条数据初始化一下
  for (int i = 0; i < 100; ++i) {
    filter_header.filter_resp_seq = i;
    filter_header.header_len = sizeof(biz::cross::FilterRespHeader);
    header._magicCookie = 0xEEFF1357EEFF1358;  // fbu::Header::MAGIC;
    header._offset = i;
    header._hdrLength = sizeof(fbu::Header);
    header._length = sizeof(fbu::yijin::MatchSdk::order::Request);
    header._passThrough = true;
    request._msgLength = header._length;
    std::string msg;
    int32_t pos = 0;
    msg.resize(filter_header.header_len + header._hdrLength + header._length);
    memcpy(msg.data() + pos, &filter_header, sizeof(filter_header));
    pos += sizeof(filter_header);
    memcpy(msg.data() + pos, &header, sizeof(header));
    pos += sizeof(header);
    memcpy(msg.data() + pos, &request, sizeof(request));
    int32_t msg_len = 40;  // msg.size();
    producer->ProduceMessage(topic,
                             (void*)msg.c_str(),  // NOLINT
                             msg_len, nullptr, 0, nullptr);
  }

  std::this_thread::sleep_for(std::chrono::seconds(1));
  cross::CrossConsumeTaskParam task_fbu;
  task_fbu.business_type = cross::BusinessType::kFbuType;
  task_fbu.topic_type = cross::TopicType::kFilterRespType;
  task_fbu.cross_idx = cross_idx;
  task_fbu.shard_id = shard_id;
  task_fbu.next_cross_seq = cross_seq;
  task_fbu.last_processed_offset_hint = last_processed_offset_hint;
  task_fbu.process_matching_result = std::bind(ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);

  cross::CrossReceiver::AddConsumeTask(task_fbu);
  sleep(1);

  auto task_id = cross::CrossConsumerManager::Instance().GetConsumeTaskId();
  cross::CrossConsumeTaskParam consume_param;
  auto task = std::make_shared<cross::CrossConsumeTask>(task_id, consume_param);
  cross::CrossConsumerManager::Instance().InsertConsumeTask(task_id, task);
  cross::CrossReceiver::DelConsumeTask(task_id);
}

TEST_F(CrossReceiverTestM, FilterRespTest9) {
  cross::CrossConsumeTaskParam param;
  param.business_type = cross::BusinessType::kFbuType;
  auto context_ptr = std::make_shared<cross::CrossConsumerContext>(param);
  cross::FilterRespMessageHandle handle("response_20001_m", nullptr, 1, context_ptr);
  mock::HdtsMessage msg;
  std::string data(12593, '1');
  msg.topic_ = "response_20001_m";
  msg.pay_load_ = data;
  handle.ProcessMessage(msg);
}
std::shared_ptr<tmock::CTradeAppMock> CrossReceiverTestM::te;
