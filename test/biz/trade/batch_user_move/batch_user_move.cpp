#include "batch_user_move.hpp"  // NOLINT

#include <google/protobuf/util/json_util.h>

#include "lib/msg_builder.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(BatchUserMoveTest, batch_eject_user) {
  // cross_seq不过
  {
    biz::user_id_t uid = 100000;
    stub user1(te, uid);

    BatchEjectUserBuilder build;
    auto& build_request = build.msg;
    auto* batch_eject_user_req = build_request.mutable_walletreqgroupbody()->mutable_batchejectuserreq();
    auto* cross_seq = batch_eject_user_req->mutable_cross_seq();
    (*cross_seq)[1] = 2;
    (*cross_seq)[5] = 6;
    (*cross_seq)[7] = 8;
    batch_eject_user_req->set_margin_seq(9);

    auto build_resp = user1.process(build_request);
    ASSERT_EQ(build_resp->ret_code(), error::ErrorCode::kErrorCodeBatchInjectUserNewerThanBatchEjectUser);
  }

  // 走用户列表
  {
    biz::user_id_t uid = 100000;
    stub user1(te, uid);

    BatchEjectUserBuilder build;
    auto& build_request = build.msg;
    auto* batch_eject_user_req = build_request.mutable_walletreqgroupbody()->mutable_batchejectuserreq();

    auto* eject_user_policy = batch_eject_user_req->mutable_eject_user_policy();
    auto* eject_user_list = eject_user_policy->mutable_eject_user_list();
    auto* node_to_user_list = eject_user_list->mutable_node_to_user_list();
    (*node_to_user_list)["g0p1"].add_user_ids(1);
    (*node_to_user_list)["g0p1"].add_user_ids(2);
    (*node_to_user_list)["g0p1"].add_user_ids(3);

    auto build_resp = user1.process(build_request);
    ASSERT_EQ(build_resp->ret_code(), error::ErrorCode::kErrorCodeNone);

    // 等1秒钟
    sleep(1);

    // 每个preworker都要发一个空的trading_result
    for (std::size_t i = 0; i < application::GlobalVarManager::Instance().pre_threads(); ++i) {
      auto trading_result = te->PopResult();
      ASSERT_NE(trading_result.m_msg, nullptr);
      ASSERT_EQ(trading_result.m_msg->header().action(), EAction::BatchEjectUser);
    }

    // 最后从seq_worker发一个trading_result
    auto final_trading_result = te->PopResult();
    ASSERT_NE(final_trading_result.m_msg, nullptr);
    ASSERT_EQ(final_trading_result.m_msg->header().action(), EAction::BatchEjectUser);

    const auto& trading_result_eject_user_policy = final_trading_result.m_msg->eject_user_policy();
    ASSERT_TRUE(trading_result_eject_user_policy.has_eject_user_list());
    const auto& trading_result_eject_user_list = trading_result_eject_user_policy.eject_user_list().node_to_user_list();
    auto trading_result_eject_user_list_iter = trading_result_eject_user_list.find("g0p1");
    ASSERT_NE(trading_result_eject_user_list_iter, trading_result_eject_user_list.end());
    ASSERT_EQ((trading_result_eject_user_list_iter->second).user_ids()[0], 1);
    ASSERT_EQ((trading_result_eject_user_list_iter->second).user_ids()[1], 2);
    ASSERT_EQ((trading_result_eject_user_list_iter->second).user_ids()[2], 3);
  }
}

TEST_F(BatchUserMoveTest, batch_eject_user_suffix) {
  // 走用户尾号
  {
    biz::user_id_t uid = 100000;
    stub user1(te, uid);

    BatchEjectUserBuilder build;
    auto& build_request = build.msg;
    auto* batch_eject_user_req = build_request.mutable_walletreqgroupbody()->mutable_batchejectuserreq();

    auto* eject_user_policy = batch_eject_user_req->mutable_eject_user_policy();
    auto* eject_by_suffix = eject_user_policy->mutable_eject_by_suffix();
    auto* node_to_suffix_list = eject_by_suffix->mutable_node_to_suffix_list();
    (*node_to_suffix_list)["g0p1"].add_uid_suffix4(1);
    (*node_to_suffix_list)["g0p1"].add_uid_suffix4(2);
    (*node_to_suffix_list)["g0p1"].add_uid_suffix4(3);

    auto build_resp = user1.process(build_request);
    ASSERT_EQ(build_resp->ret_code(), error::ErrorCode::kErrorCodeNone);

    // 等1秒钟
    sleep(1);

    // 每个preworker都要发一个空的trading_result
    for (std::size_t i = 0; i < application::GlobalVarManager::Instance().pre_threads(); ++i) {
      auto trading_result = te->PopResult();
      ASSERT_NE(trading_result.m_msg, nullptr);
      ASSERT_EQ(trading_result.m_msg->header().action(), EAction::BatchEjectUser);
    }

    // 最后从seq_worker发一个trading_result
    auto final_trading_result = te->PopResult();
    ASSERT_NE(final_trading_result.m_msg, nullptr);
    ASSERT_EQ(final_trading_result.m_msg->header().action(), EAction::BatchEjectUser);

    const auto& trading_result_eject_user_policy = final_trading_result.m_msg->eject_user_policy();
    ASSERT_TRUE(trading_result_eject_user_policy.has_eject_by_suffix());
    const auto& trading_result_eject_by_suffix =
        trading_result_eject_user_policy.eject_by_suffix().node_to_suffix_list();
    auto trading_result_eject_by_suffix_iter = trading_result_eject_by_suffix.find("g0p1");
    ASSERT_NE(trading_result_eject_by_suffix_iter, trading_result_eject_by_suffix.end());
    ASSERT_EQ((trading_result_eject_by_suffix_iter->second).uid_suffix4()[0], 1);
    ASSERT_EQ((trading_result_eject_by_suffix_iter->second).uid_suffix4()[1], 2);
    ASSERT_EQ((trading_result_eject_by_suffix_iter->second).uid_suffix4()[2], 3);
  }
}

TEST_F(BatchUserMoveTest, batch_eject_user_api) {
  auto&& sharding_config_storage = application::GlobalVarManager::Instance().sharding_config_storage();

  application::GlobalVarManager::Instance().set_test(true);
  sharding_config_storage.Init(sharding_config::ShardingConfigKey{});
  sharding_config_storage.Load({}, {});

  ASSERT_EQ(sharding_config_storage.RequestNewBatchEject(nullptr),
            error::ErrorCode::kErrorCodeRequestShardingConfigNotRight);

  ASSERT_EQ(sharding_config_storage.MarkBatchEjectForPreWorker(0), false);

  {
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    auto* header = request->mutable_req_header();
    header->set_action(EAction::BatchInjectUserPrepare);
    ASSERT_EQ(sharding_config_storage.RequestNewBatchEject(request),
              error::ErrorCode::kErrorCodeRequestShardingConfigNotRight);
    header->set_action(EAction::BatchEjectUser);

    auto* batch_eject_user_req = request->mutable_walletreqgroupbody()->mutable_batchejectuserreq();
    auto* eject_user_policy = batch_eject_user_req->mutable_eject_user_policy();
    auto* eject_user_list = eject_user_policy->mutable_eject_user_list();
    auto* node_to_user_list = eject_user_list->mutable_node_to_user_list();
    (*node_to_user_list)["g0p1"].add_user_ids(1);
    (*node_to_user_list)["g0p1"].add_user_ids(2);
    (*node_to_user_list)["g0p1"].add_user_ids(3);

    ASSERT_EQ(sharding_config_storage.RequestNewBatchEject(request), error::ErrorCode::kErrorCodeNone);
    ASSERT_EQ(sharding_config_storage.RequestNewBatchEject(request),
              error::ErrorCode::kErrorCodeOldRequestShardingConfigNotCompleted);

    ASSERT_EQ(sharding_config_storage.MarkBatchEjectForPreWorker(0), true);
    application::GlobalVarManager::Instance().set_test(false);
    ASSERT_EQ(sharding_config_storage.CanProcessUid(1, 0), false);
    ASSERT_EQ(sharding_config_storage.checkUserWhiteList(1, 0), false);
    application::GlobalVarManager::Instance().set_test(true);
    ASSERT_EQ(sharding_config_storage.CompleteBatchEject(), true);
  }

  {
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    auto* header = request->mutable_req_header();
    header->set_action(EAction::BatchInjectUserPrepare);
    ASSERT_EQ(sharding_config_storage.RequestNewBatchEject(request),
              error::ErrorCode::kErrorCodeRequestShardingConfigNotRight);
    header->set_action(EAction::BatchEjectUser);

    auto* batch_eject_user_req = request->mutable_walletreqgroupbody()->mutable_batchejectuserreq();
    auto* eject_user_policy = batch_eject_user_req->mutable_eject_user_policy();
    auto* eject_by_suffix = eject_user_policy->mutable_eject_by_suffix();
    auto* node_to_suffix_list = eject_by_suffix->mutable_node_to_suffix_list();
    (*node_to_suffix_list)["g0p1"].add_uid_suffix4(1);
    (*node_to_suffix_list)["g0p1"].add_uid_suffix4(2);
    (*node_to_suffix_list)["g0p1"].add_uid_suffix4(3);

    ASSERT_EQ(sharding_config_storage.RequestNewBatchEject(request), error::ErrorCode::kErrorCodeNone);
    ASSERT_EQ(sharding_config_storage.RequestNewBatchEject(request),
              error::ErrorCode::kErrorCodeOldRequestShardingConfigNotCompleted);

    ASSERT_EQ(sharding_config_storage.MarkBatchEjectForPreWorker(0), true);
    application::GlobalVarManager::Instance().set_test(false);
    ASSERT_EQ(sharding_config_storage.CanProcessUidInSharding(1, 0), false);
    ASSERT_EQ(sharding_config_storage.checkUserSharding(1, 0), false);
    application::GlobalVarManager::Instance().set_test(true);
    ASSERT_EQ(sharding_config_storage.CompleteBatchEject(), true);
  }
}

TEST_F(BatchUserMoveTest, batch_inject_user) {
  auto& sharding_config_storage = application::GlobalVarManager::Instance().sharding_config_storage();

  // 走用户列表
  {
    biz::user_id_t uid = 100000;
    stub user1(te, uid);

    BatchInjectUserBuilder build;
    auto& build_request = build.msg;
    auto* batch_inject_user_req = build_request.mutable_walletreqgroupbody()->mutable_batchinjectuserpreparereq();

    auto* inject_user_policy = batch_inject_user_req->mutable_inject_user_policy();
    auto* inject_user_list = inject_user_policy->mutable_inject_user_list();
    inject_user_list->add_user_ids(1);
    inject_user_list->add_user_ids(2);
    inject_user_list->add_user_ids(3);

    auto build_resp = user1.process(build_request);
    ASSERT_EQ(build_resp->ret_code(), error::ErrorCode::kErrorCodeNone);

    auto final_trading_result = te->PopResult();
    ASSERT_NE(final_trading_result.m_msg, nullptr);
    ASSERT_EQ(final_trading_result.m_msg->header().action(), EAction::BatchInjectUserPrepare);

    const auto& trading_result_inject_user_policy = final_trading_result.m_msg->inject_user_policy();
    ASSERT_TRUE(trading_result_inject_user_policy.has_inject_user_policy());
    const auto& trading_result_inject_user_list =
        trading_result_inject_user_policy.inject_user_policy().inject_user_list();
    ASSERT_EQ(trading_result_inject_user_list.user_ids()[0], 1);
    ASSERT_EQ(trading_result_inject_user_list.user_ids()[1], 2);
    ASSERT_EQ(trading_result_inject_user_list.user_ids()[2], 3);
  }

  // 走用户列表
  {
    biz::user_id_t uid = 100000;
    stub user1(te, uid);

    BatchInjectUserBuilder build;
    auto& build_request = build.msg;
    auto* batch_inject_user_req = build_request.mutable_walletreqgroupbody()->mutable_batchinjectuserpreparereq();

    auto* inject_user_policy = batch_inject_user_req->mutable_inject_user_policy();
    auto* inject_user_list = inject_user_policy->mutable_inject_user_list();
    inject_user_list->add_user_ids(1);
    inject_user_list->add_user_ids(2);
    inject_user_list->add_user_ids(3);

    auto build_resp = user1.process(build_request);
    ASSERT_EQ(build_resp->ret_code(), error::ErrorCode::kErrorCodeOldRequestShardingConfigNotCompleted);
  }

  sharding_config_storage.CompleteBatchInjectPrepare();

  for (std::size_t i = 0; i < application::GlobalVarManager::Instance().pre_threads(); ++i) {
    sharding_config_storage.CompleteBatchInject(i);
  }

  // 走用户尾号
  {
    biz::user_id_t uid = 100000;
    stub user1(te, uid);

    BatchInjectUserBuilder build;
    auto& build_request = build.msg;
    auto* batch_inject_user_req = build_request.mutable_walletreqgroupbody()->mutable_batchinjectuserpreparereq();

    auto* inject_user_policy = batch_inject_user_req->mutable_inject_user_policy();
    auto* inject_by_suffix = inject_user_policy->mutable_inject_by_suffix();
    inject_by_suffix->add_uid_suffix4(1);
    inject_by_suffix->add_uid_suffix4(2);
    inject_by_suffix->add_uid_suffix4(3);

    auto build_resp = user1.process(build_request);
    ASSERT_EQ(build_resp->ret_code(), error::ErrorCode::kErrorCodeNone);

    auto final_trading_result = te->PopResult();
    ASSERT_NE(final_trading_result.m_msg, nullptr);
    ASSERT_EQ(final_trading_result.m_msg->header().action(), EAction::BatchInjectUserPrepare);

    const auto& trading_result_inject_user_policy = final_trading_result.m_msg->inject_user_policy();
    ASSERT_TRUE(trading_result_inject_user_policy.has_inject_user_policy());
    const auto& trading_result_inject_by_suffix =
        trading_result_inject_user_policy.inject_user_policy().inject_by_suffix();
    ASSERT_EQ(trading_result_inject_by_suffix.uid_suffix4()[0], 1);
    ASSERT_EQ(trading_result_inject_by_suffix.uid_suffix4()[1], 2);
    ASSERT_EQ(trading_result_inject_by_suffix.uid_suffix4()[2], 3);
  }

  sharding_config_storage.CompleteBatchInjectPrepare();

  for (std::size_t i = 0; i < application::GlobalVarManager::Instance().pre_threads(); ++i) {
    sharding_config_storage.CompleteBatchInject(i);
  }
}

TEST_F(BatchUserMoveTest, batch_inject_user_api) {
  auto&& sharding_config_storage = application::GlobalVarManager::Instance().sharding_config_storage();
  application::GlobalVarManager::Instance().set_test(true);
  sharding_config_storage.Init(sharding_config::ShardingConfigKey{});
  sharding_config_storage.Load({}, {});

  ASSERT_EQ(sharding_config_storage.RequestBatchInject(nullptr),
            error::ErrorCode::kErrorCodeRequestShardingConfigNotRight);

  {
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    auto* header = request->mutable_req_header();
    header->set_action(EAction::BatchInjectUserPrepare);

    auto* batch_inject_user_req = request->mutable_walletreqgroupbody()->mutable_batchinjectuserpreparereq();
    auto* inject_user_policy = batch_inject_user_req->mutable_inject_user_policy();
    auto* inject_user_list = inject_user_policy->mutable_inject_user_list();
    inject_user_list->add_user_ids(1);
    inject_user_list->add_user_ids(2);
    inject_user_list->add_user_ids(3);

    ASSERT_EQ(sharding_config_storage.RequestBatchInject(request), error::ErrorCode::kErrorCodeNone);

    sharding_config_storage.CompleteBatchInjectPrepare();

    application::GlobalVarManager::Instance().set_test(false);
    ASSERT_EQ(sharding_config_storage.CanProcessUid(1, 0), true);
    ASSERT_EQ(sharding_config_storage.checkUserWhiteList(1, 0), true);
    application::GlobalVarManager::Instance().set_test(true);

    for (std::size_t i = 0; i < application::GlobalVarManager::Instance().pre_threads(); ++i) {
      sharding_config_storage.CompleteBatchInject(i);
    }
  }

  {
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    auto* header = request->mutable_req_header();
    header->set_action(EAction::BatchInjectUserPrepare);

    auto* batch_inject_user_req = request->mutable_walletreqgroupbody()->mutable_batchinjectuserpreparereq();
    auto* inject_user_policy = batch_inject_user_req->mutable_inject_user_policy();
    auto* inject_by_suffix = inject_user_policy->mutable_inject_by_suffix();
    inject_by_suffix->add_uid_suffix4(1);
    inject_by_suffix->add_uid_suffix4(2);
    inject_by_suffix->add_uid_suffix4(3);

    ASSERT_EQ(sharding_config_storage.RequestBatchInject(request), error::ErrorCode::kErrorCodeNone);

    sharding_config_storage.CompleteBatchInjectPrepare();

    application::GlobalVarManager::Instance().set_test(false);
    ASSERT_EQ(sharding_config_storage.CanProcessUid(1, 0), true);
    ASSERT_EQ(sharding_config_storage.checkUserWhiteList(1, 0), true);
    application::GlobalVarManager::Instance().set_test(true);

    for (std::size_t i = 0; i < application::GlobalVarManager::Instance().pre_threads(); ++i) {
      sharding_config_storage.CompleteBatchInject(i);
    }
  }
}

std::shared_ptr<tmock::CTradeAppMock> BatchUserMoveTest::te;
