//
// Created by SH00728<PERSON> on 2023/3/24.
//
#include <memory>

#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/liquidation/liquidation_test.hpp"
#include "test/biz/trade/wallet_service/wallet_service_test.h"

TEST_F(WalletServcieIntegrationTest, liq_create_order_service_perp_1) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "431030";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "***********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "15", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  while (true) {
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }
  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 20000000, 20000, 20000);
  }

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::LiqCreateOrder);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  // ASSERT_NE(result.m_msg.get(), nullptr);
  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "手工强平订单:" << jsonStr2 << std::endl;
  ASSERT_EQ(result.m_msg->ret_code(), error::ErrorCode::kErrorCodeFailed);
}

TEST_F(WalletServcieIntegrationTest, liq_create_order_service_perp_2) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "431030";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "***********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  switchPm(uid1);

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 20000, 20000, 20000);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "15", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "15", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  while (true) {
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }
  // biz::user_id_t const uid = 10000;
  std::string symbol = "BTC-31DEC24-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string qty = "1";
  std::string price = "2000";

  createOptPosition(uid1, symbol, position_idx, side, qty, price);

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 45000, 20000, 20000);
  }

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::LiqCreateOrder);
  auto order = unified_v_2_request_dto->mutable_ompreqgroupbody()
                   ->mutable_sync_omp_data_to_margin()
                   ->mutable_futures_trading_data()
                   ->add_related_orders();
  order->set_order_id("1111");
  order->set_order_status(EOrderStatus::Created);
  order->set_cross_status(ECrossStatus::WaitToSendNew);
  order->set_create_type(ECreateType::CreateByRiskStepLiqClose);
  order->set_qty_x(10000);
  order->set_side(ESide::Buy);
  order->set_coin(ECoin::USDT);
  order->set_symbol(ESymbol::BTCUSDT);
  order->set_position_idx(0);
  order->set_price_x(200000000);
  order->set_qty_scale(5);
  order->set_price_scale(4);

  auto order_option = unified_v_2_request_dto->mutable_ompreqgroupbody()
                          ->mutable_sync_omp_data_to_margin()
                          ->mutable_options_trading_data()
                          ->add_related_orders();
  order_option->set_order_id("1111");
  order_option->set_order_status(EOrderStatus::Created);
  order_option->set_cross_status(ECrossStatus::WaitToSendNew);
  order_option->set_create_type(ECreateType::CreateByRiskStepLiqClose);
  order_option->set_qty_x(10000);
  order_option->set_side(ESide::Buy);
  order_option->set_coin(ECoin::USDC);
  order_option->set_symbol(static_cast<ESymbol>(102312));
  order_option->set_symbol_name(symbol);
  order_option->set_position_idx(0);
  order_option->set_price_x(200000000);
  order_option->set_qty_scale(5);
  order_option->set_price_scale(4);

  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  // ASSERT_NE(result.m_msg.get(), nullptr);
  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "手工强平订单:" << jsonStr2 << std::endl;
  ASSERT_EQ(result.m_msg->ret_code(), error::ErrorCode::kErrorCodeSuccess);
}

TEST_F(WalletServcieIntegrationTest, liq_create_order_service_liq_manual_error) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "431030";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "***********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  switchPm(uid1);

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 20000, 20000, 20000);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "15", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "15", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  while (true) {
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }
  // biz::user_id_t const uid = 10000;
  std::string symbol = "BTC-31DEC24-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string qty = "1";
  std::string price = "2000";

  createOptPosition(uid1, symbol, position_idx, side, qty, price);

  //  {
  //    // 设置lastPrice
  //    te->AddMarkPrice(static_cast<ESymbol>(5), 45000, 20000, 20000);
  //  }

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::LiqCreateOrder);
  auto order = unified_v_2_request_dto->mutable_ompreqgroupbody()
                   ->mutable_sync_omp_data_to_margin()
                   ->mutable_futures_trading_data()
                   ->add_related_orders();
  order->set_order_id("1111");
  order->set_order_status(EOrderStatus::Created);
  order->set_cross_status(ECrossStatus::WaitToSendNew);
  order->set_create_type(ECreateType::CreateByRiskStepLiqClose);
  order->set_qty_x(10000);
  order->set_side(ESide::Buy);
  order->set_coin(ECoin::USDT);
  order->set_symbol(ESymbol::BTCUSDT);
  order->set_position_idx(0);
  order->set_price_x(200000000);
  order->set_qty_scale(5);
  order->set_price_scale(4);

  auto order_option = unified_v_2_request_dto->mutable_ompreqgroupbody()
                          ->mutable_sync_omp_data_to_margin()
                          ->mutable_options_trading_data()
                          ->add_related_orders();
  order_option->set_order_id("1111");
  order_option->set_order_status(EOrderStatus::Created);
  order_option->set_cross_status(ECrossStatus::WaitToSendNew);
  order_option->set_create_type(ECreateType::CreateByRiskStepLiqClose);
  order_option->set_qty_x(10000);
  order_option->set_side(ESide::Buy);
  order_option->set_coin(ECoin::USDC);
  order_option->set_symbol(static_cast<ESymbol>(102312));
  order_option->set_symbol_name(symbol);
  order_option->set_position_idx(0);
  order_option->set_price_x(200000000);
  order_option->set_qty_scale(5);
  order_option->set_price_scale(4);

  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  // ASSERT_NE(result.m_msg.get(), nullptr);
  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "手工强平订单:" << jsonStr2 << std::endl;
  ASSERT_EQ(result.m_msg->ret_code(), error::ErrorCode::kErrorCodeFailed);
}

TEST_F(WalletServcieIntegrationTest, liq_create_order_service_liq_manual_success) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid1 = ********;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "431030";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "***********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  switchPm(uid1);

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 20000, 20000, 20000);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "15", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "15", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  while (true) {
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }
  // biz::user_id_t const uid = 10000;
  std::string symbol = "BTC-31DEC24-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string qty = "1";
  std::string price = "2000";

  createOptPosition(uid1, symbol, position_idx, side, qty, price);

  //  {
  //    // 设置lastPrice
  //    te->AddMarkPrice(static_cast<ESymbol>(5), 45000, 20000, 20000);
  //  }

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::LiqCreateOrder);

  auto order_option = unified_v_2_request_dto->mutable_ompreqgroupbody()
                          ->mutable_sync_omp_data_to_margin()
                          ->mutable_options_trading_data()
                          ->add_related_orders();
  order_option->set_order_id("1111");
  order_option->set_order_status(EOrderStatus::Created);
  order_option->set_cross_status(ECrossStatus::WaitToSendNew);
  order_option->set_create_type(ECreateType::CreateByRiskStepLiqClose);
  order_option->set_qty_x(10000);
  order_option->set_side(ESide::Buy);
  order_option->set_coin(ECoin::USDC);
  order_option->set_symbol(static_cast<ESymbol>(102312));
  order_option->set_symbol_name(symbol);
  order_option->set_position_idx(0);
  order_option->set_price_x(200000000);
  order_option->set_qty_scale(5);
  order_option->set_price_scale(4);

  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  // ASSERT_NE(result.m_msg.get(), nullptr);
  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "手工强平订单:" << jsonStr2 << std::endl;
  ASSERT_EQ(result.m_msg->ret_code(), error::ErrorCode::kErrorCodeSuccess);

  auto order = unified_v_2_request_dto->mutable_ompreqgroupbody()
                   ->mutable_sync_omp_data_to_margin()
                   ->mutable_futures_trading_data()
                   ->add_related_orders();
  order->set_order_id("1111");
  order->set_order_status(EOrderStatus::Created);
  order->set_cross_status(ECrossStatus::WaitToSendNew);
  order->set_create_type(ECreateType::CreateByRiskStepLiqClose);
  order->set_qty_x(10000);
  order->set_side(ESide::Buy);
  order->set_coin(ECoin::USDT);
  order->set_symbol(ESymbol::BTCUSDT);
  order->set_position_idx(0);
  order->set_price_x(200000000);
  order->set_qty_scale(5);
  order->set_price_scale(4);

  auto event1 = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event1, false);
  auto result1 = te->PopResult();
  // ASSERT_NE(result.m_msg.get(), nullptr);
  std::string jsonStr3;
  (void)google::protobuf::util::MessageToJsonString(*result1.m_msg.get(), &jsonStr3);
  std::cout << "手工强平订单:" << jsonStr3 << std::endl;
}
// namespace biz
