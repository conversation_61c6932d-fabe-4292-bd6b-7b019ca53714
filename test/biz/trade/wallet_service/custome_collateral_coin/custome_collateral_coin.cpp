
#include "test/biz/trade/wallet_service/custome_collateral_coin/custome_collateral_coin.hpp"

#include <string>

#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/wallet_service/wallet_com_test.hpp"

TEST_F(WalletTestCom, close_collateral_coin_eth_fail) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  openAccount(uid1);

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(2), 1, "10", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(5);
    req.set_enable(false);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 5, req);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 3200227);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "设置失败响应:" << jsonStr << std::endl;

    auto disable_coin_list =
        result.m_msg->asset_margin_result().per_user_setting_data().user_disabled_collateral_coin_list();
    ASSERT_EQ(disable_coin_list.size(), 0);
  }
}

TEST_F(WalletTestCom, close_collateral_coin_usdt_fail) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  openAccount(uid1);

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(5), 1, "10", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(5);
    req.set_enable(false);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 5, req);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 3200227);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "设置失败响应:" << jsonStr << std::endl;

    auto disable_coin_list =
        result.m_msg->asset_margin_result().per_user_setting_data().user_disabled_collateral_coin_list();
    ASSERT_EQ(disable_coin_list.size(), 0);
  }
}

TEST_F(WalletTestCom, close_collateral_coin_liability_fail) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  openAccount(uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid2);

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(1);
    req.set_enable(true);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto enable_coin_list =
        result.m_msg->asset_margin_result().per_user_setting_data().user_enabled_collateral_coin_list();
    ASSERT_EQ(enable_coin_list.size(), 1);
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(5), 1, "********", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "充值Result:" << jsonStr << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "3900";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "3.01");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetIsLeverage(true);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");

    auto result = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货挂单:" << jsonStr << std::endl;
  }

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(1);
    req.set_enable(false);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 3200248);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "设置失败响应:" << jsonStr << std::endl;

    auto enable_coin_list =
        result.m_msg->asset_margin_result().per_user_setting_data().user_enabled_collateral_coin_list();
    ASSERT_EQ(enable_coin_list.size(), 1);
  }
}

// 有负债无frozen
TEST_F(WalletTestCom, close_collateral_coin_liability_fail2) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  openAccount(uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid2);

  mockMarginConfig();

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(1);
    req.set_enable(true);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto enable_coin_list =
        result.m_msg->asset_margin_result().per_user_setting_data().user_enabled_collateral_coin_list();
    ASSERT_EQ(enable_coin_list.size(), 1);
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(5), 1, "********", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "充值Result:" << jsonStr << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "3900000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "3.01");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetIsLeverage(true);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");

    auto result = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货挂单:" << jsonStr << std::endl;
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(3));
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.002", "3.01");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetIsLeverage(true);

    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");

    auto result = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货挂单:" << jsonStr << std::endl;

    while (true) {
      result = te->PopResult(300);
      if (result.m_msg.get() == nullptr) {
        break;
      }
    }
  }

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(1);
    req.set_enable(false);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 3200246);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "设置失败响应:" << jsonStr << std::endl;

    auto enable_coin_list =
        result.m_msg->asset_margin_result().per_user_setting_data().user_enabled_collateral_coin_list();
    ASSERT_EQ(enable_coin_list.size(), 1);
  }
}

// 强平状态拦截
TEST_F(WalletTestCom, close_collateral_coin_liq_fail) {
  GTEST_SKIP();  // 测试用例严格以来撮合回包顺序 临时注释 后续更换方式
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  openAccount(uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid2);

  // UID3
  biz::user_id_t uid3 = 2001;
  stub user3(te, uid3);
  openAccount(uid3);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "3900";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "3900";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "3900";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid3, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user3.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "user3充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 30000, 30000, 30000}, {static_cast<ESymbol>(45), 30000, 30000, 30000}});
  }

  switchPm(uid1);
  switchPm(uid3);

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "30000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "期货订单Result:" << jsonStr << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "30000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "user2期货订单成交Result:" << jsonStr << std::endl;

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "user1期货订单成交Result:" << jsonStr << std::endl;
  }

  {
    auto order_id = te->GenUUID();
    // 创建期货订单
    FutureOneOfCreateOrderBuilder create_build(order_id, 5, EPositionIndex::Single, ESide::Buy, EOrderType::Limit,
                                               100000, 200000000, ETimeInForce::GoodTillCancel, uid3, 5);
    auto resp1 = user3.process(create_build.Build());

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "user3期货订单成交Result:" << jsonStr << std::endl;

    FutureOneOfCancelOrderBuilder cancel_build(order_id, 5, uid3, 5);
    auto resp2 = user3.process(cancel_build.Build());
    auto result2 = te->PopResult(100);
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }

  // 有的测试场景需要停止cross
  te->SuspendCross();

  // 修改标记价格
  te->BatchAddMarkPrice(
      {{static_cast<ESymbol>(5), 10000, 10000, 10000}, {static_cast<ESymbol>(45), 10000, 10000, 10000}});

  // 触发强平接管
  {
    // 触发接管
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    while (true) {
      auto result = te->PopResult(300);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      std::string jsonStr;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
      std::cout << "响应:" << jsonStr << std::endl;
    }

    // 恢复cross
    te->ResumeCross();

    {
      std::string req_id = "";
      ::svc::unified_v2::req::settings::CollateralCoin req;
      req.set_coin(5);
      req.set_enable(false);

      AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 5, req);
      auto resp1 = user1.process(collateral_build.Build());
      ASSERT_EQ(resp1->ret_code(), 3200638);
    }

    while (true) {
      auto result = te->PopResult(300);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      std::string jsonStr;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
      std::cout << "响应:" << jsonStr << std::endl;
    }
  }
}

// 切换时逐仓拦截
TEST_F(WalletTestCom, close_collateral_coin_isolate_check_fail) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  openAccount(uid1);

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(1), 1, "********", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "充值Result:" << jsonStr << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(1);
    req.set_enable(false);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 3200247);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "设置失败响应:" << jsonStr << std::endl;

    auto disable_coin_list =
        result.m_msg->asset_margin_result().per_user_setting_data().user_disabled_collateral_coin_list();
    ASSERT_EQ(disable_coin_list.size(), 0);
  }
}

// BTC关闭抵押品属性后又重新打开
TEST_F(WalletTestCom, close_collateral_coin_then_open_success) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  openAccount(uid1);

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(1), 1, "10", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "40000");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货挂单:" << jsonStr << std::endl;
  }

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(1);
    req.set_enable(true);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "设置成功响应:" << jsonStr << std::endl;

    auto disable_coin_list =
        result.m_msg->asset_margin_result().per_user_setting_data().user_disabled_collateral_coin_list();
    ASSERT_EQ(disable_coin_list.size(), 0);
  }
}

// 0不存在的coin设置失败
TEST_F(WalletTestCom, close_collateral_invalid_coin_fail) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  openAccount(uid1);

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(1), 1, "10", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "40000");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货挂单:" << jsonStr << std::endl;
  }

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    // req.set_coin(1);
    // req.set_enable(false);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 3200227);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "设置成功响应:" << jsonStr << std::endl;

    auto disable_coin_list =
        result.m_msg->asset_margin_result().per_user_setting_data().user_disabled_collateral_coin_list();
    ASSERT_EQ(disable_coin_list.size(), 0);
  }
}

// 消息体为空
TEST_F(WalletTestCom, close_collateral_empty_msg_success) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  openAccount(uid1);

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(1), 1, "10", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "40000");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货挂单:" << jsonStr << std::endl;
  }

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    // req.set_coin(1);
    // req.set_enable(false);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req, true);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "设置成功响应:" << jsonStr << std::endl;

    auto disable_coin_list =
        result.m_msg->asset_margin_result().per_user_setting_data().user_disabled_collateral_coin_list();
    ASSERT_EQ(disable_coin_list.size(), 0);
  }
}

// BTC设置成工 消息体为空设置失败
TEST_F(WalletTestCom, clear_setting_success) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  openAccount(uid1);

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(1), 1, "10", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "40000");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货挂单:" << jsonStr << std::endl;
  }

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    // req.set_coin(1);
    // req.set_enable(false);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req, false, true);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "设置成功响应:" << jsonStr << std::endl;

    auto disable_coin_list =
        result.m_msg->asset_margin_result().per_user_setting_data().user_disabled_collateral_coin_list();
    ASSERT_EQ(disable_coin_list.size(), 0);
  }
}

TEST_F(WalletTestCom, close_collateral_coin_the_open_success) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  openAccount(uid1);

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(1), 1, "10", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "40000");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货挂单:" << jsonStr << std::endl;
  }

  setMarginConfigMarginRatio(1, decimal::kZero);

  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "40000");
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货挂单:" << jsonStr << std::endl;
  }

  // 恢复配置
  setMarginConfigMarginRatio(1, bbase::decimal::Decimal<>("0.95"));
}

TEST_F(WalletTestCom, close_collateral_coin_inverse_order_not_empty) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  auto symbol = ESymbol::BTCUSD;
  auto coin = ECoin::BTC;

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(1), 1, "10", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(1);
    req.set_enable(true);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req, false, false);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 9000;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  EXPECT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 9000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid1);

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(1);
    req.set_enable(false);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req, false, false);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 3200421);
  }
}

TEST_F(WalletTestCom, close_collateral_coin_inverse_position_exist) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  biz::user_id_t uid2 = 100002;
  stub user2(te, uid2);

  auto symbol = ESymbol::BTCUSD;
  auto coin = ECoin::BTC;

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(1);
    req.set_enable(true);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req, false, false);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(1), 1, "10", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid2, te->GenUUID(), biz::coin_t(1), 1, "10", "0");
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    te->AddMarkPrice(symbol, 100000, 100000, 100000);

    EPositionIndex pz_index = EPositionIndex::Single;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 9000;
    biz::price_x_t price = 100000 * 1e4;

    // 用户1挂买单
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;
    FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid1, coin);
    auto resp = user1.process(create_build_user1_buy.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 9000);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid1);

    // 用户2挂卖单
    auto order_id_2 = te->GenUUID();
    side = ESide::Sell;
    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    resp = user2.process(create_build_user2_sell.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 9000);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid2);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
    auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(pz_checker.m_msg->size_x(), 9000);
    // value = 9000 / 100000 = 0.09
    EXPECT_EQ(pz_checker.m_msg->value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), ********00);
    EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ********000000);
    EXPECT_EQ(pz_checker.m_msg->user_id(), uid2);
    EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 626000);
    EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 614000);
    // 0.09 * (0.00614000 - 0.0006) = 0.0004986
    EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 49860);
    EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

    result = te->PopResult();
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(pz_checker.m_msg->size_x(), 9000);
    EXPECT_EQ(pz_checker.m_msg->value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), ********00);
    EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ********000000);
    EXPECT_EQ(pz_checker.m_msg->user_id(), uid1);
    EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 626000);
    EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 614000);
    // 0.09 * (0.******** - 0.0006) = 0.0005094
    EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 50940);
    EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  }

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(1);
    req.set_enable(false);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req, false, false);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 3200422);
  }
}

TEST_F(WalletTestCom, close_collateral_coin_inverse_success) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  auto symbol = ESymbol::BTCUSD;
  auto coin = ECoin::BTC;

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), coin, symbol, "10", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(1);
    req.set_enable(false);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req, false, false);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }
}
