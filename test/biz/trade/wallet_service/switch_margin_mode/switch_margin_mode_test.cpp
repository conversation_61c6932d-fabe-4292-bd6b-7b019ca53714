//
// Created by SH00728ML on 2023/3/24.
//

#include <gtest/gtest.h>

#include <memory>

#include "margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/liquidation/liquidation_test.hpp"
#include "test/biz/trade/wallet_service/wallet_service_test.h"

//// 切换保证金模式
// TEST_F(WalletServcieIntegrationTest, SwitchMarginMode) {
//   biz::user_id_t uid = 1000011;
//   stub user1(te, uid);
//   userUpgradingLabel(uid);
//
//   auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
//   unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
//   unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
//
//   unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
//       EAccountMode::Isolated);
//   auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
//   te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
//   auto result = te->PopResult();
//
//   std::string jsonStr2;
//   (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
//   std::cout << "切换保证金模式:" << jsonStr2 << std::endl;
//
//   ASSERT_NE(result.m_msg.get(), nullptr);
// }

// 切换保证金模式，反向，kWillTriggerLiq buy
TEST_F(WalletServcieIntegrationTest, SwitchMarginModeInverse1) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  switchRm(uid1);

  biz::user_id_t uid2 = 100002;
  stub user2(te, uid2);

  auto symbol = ESymbol::BTCUSD;
  auto coin = ECoin::BTC;

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(1), 1, "1", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid2, te->GenUUID(), biz::coin_t(1), 1, "1", "0");
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    te->AddMarkPrice(symbol, 100000, 100000, 100000);

    EPositionIndex pz_index = EPositionIndex::Single;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 9001;
    biz::price_x_t price = 100000 * 1e4;

    // 用户1挂买单
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;
    FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid1, coin);
    auto resp = user1.process(create_build_user1_buy.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 9001);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid1);

    // 用户2挂卖单
    auto order_id_2 = te->GenUUID();
    side = ESide::Sell;
    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, 9000, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    resp = user2.process(create_build_user2_sell.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 9000);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid2);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
    auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(pz_checker.m_msg->size_x(), 9000);
    // value = 9000 / 100000 = 0.09
    EXPECT_EQ(pz_checker.m_msg->value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), 1000000000);
    EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ************00);
    EXPECT_EQ(pz_checker.m_msg->user_id(), uid2);
    EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 626000);
    EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 614000);
    // 0.09 * (0.00614000 - 0.0006) = 0.0004986
    EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 49860);

    result = te->PopResult();
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(pz_checker.m_msg->size_x(), 9000);
    EXPECT_EQ(pz_checker.m_msg->value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), 1000000000);
    EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ************00);
    EXPECT_EQ(pz_checker.m_msg->user_id(), uid1);
    EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 1);
    EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 626000);
    EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 614000);
    // 0.09 * (0.******** - 0.0006) = 0.0005094
    EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 50940);
    EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 7);
  }

  te->AddMarkPrice(symbol, 10500, 10500, 10500);

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      EAccountMode::Isolated);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();

  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "切换保证金模式:" << jsonStr2 << std::endl;

  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg.get()->ret_code(), 3200241);
}

// 切换保证金模式，反向，kWillTriggerLiq sell
TEST_F(WalletServcieIntegrationTest, SwitchMarginModeInverse2) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  biz::user_id_t uid2 = 100002;
  stub user2(te, uid2);

  switchRm(uid2);

  auto symbol = ESymbol::BTCUSD;
  auto coin = ECoin::BTC;

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(1), 1, "1", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid2, te->GenUUID(), biz::coin_t(1), 1, "1", "0");
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    te->AddMarkPrice(symbol, 100000, 100000, 100000);

    EPositionIndex pz_index = EPositionIndex::Single;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 9001;
    biz::price_x_t price = 100000 * 1e4;

    // 用户1挂买单
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;
    FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid1, coin);
    auto resp = user1.process(create_build_user1_buy.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 9001);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid1);

    // 用户2挂卖单
    auto order_id_2 = te->GenUUID();
    side = ESide::Sell;
    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, 9000, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    resp = user2.process(create_build_user2_sell.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 9000);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid2);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
    auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(pz_checker.m_msg->size_x(), 9000);
    // value = 9000 / 100000 = 0.09
    EXPECT_EQ(pz_checker.m_msg->value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), 1000000000);
    EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ************00);
    EXPECT_EQ(pz_checker.m_msg->user_id(), uid2);
    EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 626000);
    EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 614000);
    // 0.09 * (0.00614000 - 0.0006) = 0.0004986
    EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 49860);

    result = te->PopResult();
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(pz_checker.m_msg->size_x(), 9000);
    EXPECT_EQ(pz_checker.m_msg->value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), 1000000000);
    EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ************00);
    EXPECT_EQ(pz_checker.m_msg->user_id(), uid1);
    EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 1);
    EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 626000);
    EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 614000);
    // 0.09 * (0.******** - 0.0006) = 0.0005094
    EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 50940);
    EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 7);
  }

  te->AddMarkPrice(symbol, 120000, 120000, 120000);

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid2);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      EAccountMode::Isolated);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid2, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();

  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "切换保证金模式:" << jsonStr2 << std::endl;

  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg.get()->ret_code(), 3200241);
}

// 切换保证金模式，反向，kWalletBalanceCheckFail
TEST_F(WalletServcieIntegrationTest, SwitchMarginModeInverse3) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  switchRm(uid);

  biz::user_id_t uid2 = 100002;
  stub user2(te, uid2);

  auto symbol = ESymbol::BTCUSD;
  auto coin = ECoin::BTC;

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid, te->GenUUID(), biz::coin_t(1), 1, "1", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid2, te->GenUUID(), biz::coin_t(1), 1, "1", "0");
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    te->AddMarkPrice(symbol, 10000, 10000, 10000);

    EPositionIndex pz_index = EPositionIndex::Single;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 11000;
    biz::price_x_t price = 100000 * 1e4;

    // 用户1挂买单
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;
    FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid, coin);
    auto resp = user1.process(create_build_user1_buy.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 11000);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid);

    // 用户2挂卖单
    auto order_id_2 = te->GenUUID();
    side = ESide::Sell;
    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, 9000, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    resp = user2.process(create_build_user2_sell.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 9000);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid2);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
    auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
  }

  te->AddMarkPrice(symbol, 100, 100, 100);

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      EAccountMode::Isolated);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();

  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "切换保证金模式:" << jsonStr2 << std::endl;

  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg.get()->ret_code(), 3200420);
}

// 切换保证金模式，反向，kWalletBalanceCheckFail sell
TEST_F(WalletServcieIntegrationTest, SwitchMarginModeInverse4) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  biz::user_id_t uid2 = 100002;
  stub user2(te, uid2);
  switchRm(uid2);

  auto symbol = ESymbol::BTCUSD;
  auto coin = ECoin::BTC;

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid, te->GenUUID(), biz::coin_t(1), 1, "1", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid2, te->GenUUID(), biz::coin_t(1), 1, "0.3", "0");
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    te->AddMarkPrice(symbol, 10000, 10000, 10000);

    EPositionIndex pz_index = EPositionIndex::Single;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 1;
    biz::price_x_t price = 100000 * 1e4;

    // 用户1挂买单
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;
    FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid, coin);
    auto resp = user1.process(create_build_user1_buy.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), qty);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid);

    // 用户2挂卖单
    auto order_id_2 = te->GenUUID();
    side = ESide::Sell;
    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, 30000, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    resp = user2.process(create_build_user2_sell.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 30000);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid2);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
    auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
  }

  te->AddMarkPrice(symbol, ************, ************, ************);

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid2);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      EAccountMode::Isolated);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid2, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();

  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "切换保证金模式:" << jsonStr2 << std::endl;

  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg.get()->ret_code(), 3200420);
}

// 切换保证金模式，切换pm,抵押品开启
TEST_F(WalletServcieIntegrationTest, SwitchPMInverse) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  switchRm(uid1);

  biz::user_id_t uid2 = 100002;
  stub user2(te, uid2);

  auto symbol = ESymbol::BTCUSD;
  auto coin = ECoin::BTC;

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(1), 1, "1", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid2, te->GenUUID(), biz::coin_t(1), 1, "1", "0");
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    te->AddMarkPrice(symbol, 100000, 100000, 100000);

    EPositionIndex pz_index = EPositionIndex::Single;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 9001;
    biz::price_x_t price = 100000 * 1e4;

    // 用户1挂买单
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;
    FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid1, coin);
    auto resp = user1.process(create_build_user1_buy.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 9001);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid1);

    // 用户2挂卖单
    auto order_id_2 = te->GenUUID();
    side = ESide::Sell;
    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, 9000, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    resp = user2.process(create_build_user2_sell.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 9000);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid2);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
    auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(pz_checker.m_msg->size_x(), 9000);
    // value = 9000 / 100000 = 0.09
    EXPECT_EQ(pz_checker.m_msg->value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), 1000000000);
    EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ************00);
    EXPECT_EQ(pz_checker.m_msg->user_id(), uid2);
    EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 626000);
    EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 614000);
    // 0.09 * (0.00614000 - 0.0006) = 0.0004986
    EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 49860);

    result = te->PopResult();
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(pz_checker.m_msg->size_x(), 9000);
    EXPECT_EQ(pz_checker.m_msg->value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), 1000000000);
    EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ************00);
    EXPECT_EQ(pz_checker.m_msg->user_id(), uid1);
    EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 1);
    EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 626000);
    EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 614000);
    // 0.09 * (0.******** - 0.0006) = 0.0005094
    EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 50940);
    EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 7);
  }

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      EAccountMode::Portfolio);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();

  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "切换保证金模式:" << jsonStr2 << std::endl;

  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg.get()->ret_code(), 0);
  auto& collateral_coin_list =
      result.m_msg->asset_margin_result().per_user_setting_data().user_enabled_collateral_coin_list();
  ASSERT_EQ(collateral_coin_list[0], 1);
}

// 切换保证金模式，切换pm,im > 100
TEST_F(WalletServcieIntegrationTest, SwitchPMInverseImrCheck) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  switchIm(uid1);

  biz::user_id_t uid2 = 100002;
  stub user2(te, uid2);

  auto symbol = ESymbol::BTCUSD;
  auto coin = ECoin::BTC;

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(1), 1, "1", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid2, te->GenUUID(), biz::coin_t(1), 1, "1", "0");
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    te->AddMarkPrice(symbol, 100000, 100000, 100000);

    EPositionIndex pz_index = EPositionIndex::Single;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 9001;
    biz::price_x_t price = 100000 * 1e4;

    // 用户1挂买单
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;
    FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid1, coin);
    auto resp = user1.process(create_build_user1_buy.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 9001);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid1);

    // 用户2挂卖单
    auto order_id_2 = te->GenUUID();
    side = ESide::Sell;
    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, 9000, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    resp = user2.process(create_build_user2_sell.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 9000);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid2);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
    auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(pz_checker.m_msg->size_x(), 9000);
    // value = 9000 / 100000 = 0.09
    EXPECT_EQ(pz_checker.m_msg->value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), 1000000000);
    EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ************00);
    EXPECT_EQ(pz_checker.m_msg->user_id(), uid2);
    EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 626000);
    EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 614000);
    // 0.09 * (0.00614000 - 0.0006) = 0.0004986
    EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 49860);

    result = te->PopResult();
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(pz_checker.m_msg->size_x(), 9000);
    EXPECT_EQ(pz_checker.m_msg->value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), 1000000000);
    EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ************00);
    EXPECT_EQ(pz_checker.m_msg->user_id(), uid1);
    EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 1);
    EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 626000);
    EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 614000);
    // 0.09 * (0.******** - 0.0006) = 0.0005094
    EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 50940);
    EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 7);
  }

  te->AddMarkPrice(symbol, 1, 1, 1);

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      EAccountMode::Portfolio);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();

  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "切换保证金模式:" << jsonStr2 << std::endl;

  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg.get()->ret_code(), 3200231);
}

// 切换保证金模式，切换cross,im > 100
TEST_F(WalletServcieIntegrationTest, SwitchCrossInverseImrCheck) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  switchIm(uid1);

  biz::user_id_t uid2 = 100002;
  stub user2(te, uid2);

  auto symbol = ESymbol::BTCUSD;
  auto coin = ECoin::BTC;

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), biz::coin_t(1), 1, "1", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid2, te->GenUUID(), biz::coin_t(1), 1, "1", "0");
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    te->AddMarkPrice(symbol, 100000, 100000, 100000);

    EPositionIndex pz_index = EPositionIndex::Single;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 9001;
    biz::price_x_t price = 100000 * 1e4;

    // 用户1挂买单
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;
    FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid1, coin);
    auto resp = user1.process(create_build_user1_buy.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 9001);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid1);

    // 用户2挂卖单
    auto order_id_2 = te->GenUUID();
    side = ESide::Sell;
    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, 9000, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    resp = user2.process(create_build_user2_sell.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(order_checker.m_msg->qty_x(), 9000);
    EXPECT_EQ(order_checker.m_msg->user_id(), uid2);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
    auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(pz_checker.m_msg->size_x(), 9000);
    // value = 9000 / 100000 = 0.09
    EXPECT_EQ(pz_checker.m_msg->value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), 1000000000);
    EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ************00);
    EXPECT_EQ(pz_checker.m_msg->user_id(), uid2);
    EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 626000);
    EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 614000);
    // 0.09 * (0.00614000 - 0.0006) = 0.0004986
    EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 49860);

    result = te->PopResult();
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(pz_checker.m_msg->size_x(), 9000);
    EXPECT_EQ(pz_checker.m_msg->value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 9000000);
    EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), 1000000000);
    EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ************00);
    EXPECT_EQ(pz_checker.m_msg->user_id(), uid1);
    EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 1);
    EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
    EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 626000);
    EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 614000);
    // 0.09 * (0.******** - 0.0006) = 0.0005094
    EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 50940);
    EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 7);
  }

  te->AddMarkPrice(symbol, 1, 1, 1);

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(EAccountMode::Cross);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();

  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "切换保证金模式:" << jsonStr2 << std::endl;

  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg.get()->ret_code(), 3200231);
}
