#include <string>

#include "src/data/type/biz_type.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/wallet_service/wallet_com_test.hpp"

/**
 * 关闭抵押品
 * 场景1：
 *   充值btc 0.01 bonus, 再开现货杠杆交易，sell 0.005 btc, 产生btc出现0.005赠金负债，关闭BTC作为抵押品
 * 期望结果：关闭BTC作为抵押品失败
 * 场景2：
 *   充值0.005 btc, 还清赠金负债0.005，再关闭BTC作为抵押品
 * 期望结果：关闭BTC作为抵押品成功
 * 注意：当前UTA限制USDC或USDT的赠金币种，该单测关闭
 */
TEST_F(WalletTestCom, custome_collateral_coin_bonus_normal_case) {
  // UID1
  //  biz::user_id_t const uid1 = 100000;
  //  biz::user_id_t const uid2 = 200000;
  //  stub user1(te, uid1);
  //  stub user2(te, uid2);
  //
  //  openAccount(uid1);
  //  openAccount(uid2);
  //
  //  {
  //    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), static_cast<biz::coin_t>(1), 1, "0.01",
  //                                       "0.01");
  //    auto resp1 = user1.process(deposit_build.Build());
  //    ASSERT_EQ(resp1->ret_code(), 0);
  //
  //    auto result = te->PopResult();
  //    ASSERT_NE(result.m_msg.get(), nullptr);
  //    std::string jsonStr2;
  //    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  //    std::cout << "uid1充值Result:" << jsonStr2 << '\n';
  //  }
  //
  //  {
  //    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), static_cast<biz::coin_t>(5), 1, "10000",
  //                                       "0");
  //    auto resp1 = user1.process(deposit_build.Build());
  //    ASSERT_EQ(resp1->ret_code(), 0);
  //
  //    auto result = te->PopResult();
  //    ASSERT_NE(result.m_msg.get(), nullptr);
  //    std::string jsonStr2;
  //    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  //    std::cout << "uid1充值Result:" << jsonStr2 << '\n';
  //  }
  //
  //  {
  //    FutureDepositBuilder deposit_build(te->GenUUID(), uid2, te->GenUUID(), static_cast<biz::coin_t>(5), 1, "100000",
  //                                       "0");
  //    auto resp1 = user2.process(deposit_build.Build());
  //    ASSERT_EQ(resp1->ret_code(), 0);
  //
  //    auto result = te->PopResult();
  //    ASSERT_NE(result.m_msg.get(), nullptr);
  //    std::string jsonStr2;
  //    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  //    std::cout << "uid2充值Result:" << jsonStr2 << '\n';
  //  }
  //
  //  {
  //    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.005", "150");
  //    auto order_link_id = te->GenUUID();
  //    build.SetOrderLinkID(order_link_id);
  //    build.SetIsLeverage(true);
  //
  //    auto resp1 = user1.create_order(build.Build());
  //    ASSERT_NE(resp1->order_link_id(), "");
  //
  //    auto result = te->PopResult();
  //    std::string jsonStr;
  //    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
  //    std::cout << "现货挂单:" << jsonStr << std::endl;
  //  }
  //
  //  {
  //    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.005", "150");
  //    auto order_link_id = te->GenUUID();
  //    build.SetOrderLinkID(order_link_id);
  //    build.SetIsLeverage(true);
  //
  //    auto resp1 = user2.create_order(build.Build());
  //    ASSERT_NE(resp1->order_link_id(), "");
  //
  //    auto result = te->PopResult();
  //    std::string jsonStr;
  //    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
  //    std::cout << "现货挂单:" << jsonStr << std::endl;
  //
  //    while (true) {
  //      result = te->PopResult(300);
  //      if (result.m_msg.get() == nullptr) {
  //        break;
  //      }
  //    }
  //  }
  //
  //  {
  //    std::string req_id = "";
  //    ::svc::unified_v2::req::settings::CollateralCoin req;
  //    req.set_coin(1);
  //    req.set_enable(false);
  //
  //    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req);
  //    auto resp1 = user1.process(collateral_build.Build());
  //    ASSERT_EQ(resp1->ret_code(), 3200246);
  //    auto result = te->PopResult();
  //    ASSERT_NE(result.m_msg.get(), nullptr);
  //    std::string jsonStr;
  //    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
  //    std::cout << "设置失败响应:" << jsonStr << std::endl;
  //
  //    auto disable_coin_list =
  //        result.m_msg->asset_margin_result().per_user_setting_data().user_disabled_collateral_coin_list();
  //    ASSERT_EQ(disable_coin_list.size(), 0);
  //  }
  //
  //  {
  //    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), static_cast<biz::coin_t>(1), 1, "0.005",
  //                                       "0");
  //    auto resp1 = user1.process(deposit_build.Build());
  //    ASSERT_EQ(resp1->ret_code(), 0);
  //
  //    auto result = te->PopResult();
  //    ASSERT_NE(result.m_msg.get(), nullptr);
  //    std::string jsonStr2;
  //    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  //    std::cout << "uid1充值Result:" << jsonStr2 << '\n';
  //  }
  //
  //  {
  //    std::string req_id = "";
  //    ::svc::unified_v2::req::settings::CollateralCoin req;
  //    req.set_coin(1);
  //    req.set_enable(false);
  //
  //    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req);
  //    auto resp1 = user1.process(collateral_build.Build());
  //    ASSERT_EQ(resp1->ret_code(), 0);
  //    auto result = te->PopResult();
  //    ASSERT_NE(result.m_msg.get(), nullptr);
  //    std::string jsonStr;
  //    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
  //    std::cout << "设置成功响应:" << jsonStr << std::endl;
  //
  //    auto disable_coin_list =
  //        result.m_msg->asset_margin_result().per_user_setting_data().user_disabled_collateral_coin_list();
  //    ASSERT_EQ(disable_coin_list.size(), 1);
  //  }
}

/**
 * 正常关闭抵押品场景
 */
TEST_F(WalletTestCom, custome_collateral_coin_bonus_normal_case_2) {
  // UID1
  biz::user_id_t const uid1 = 100000;
  biz::user_id_t const uid2 = 200000;
  stub user1(te, uid1);
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(1);
    req.set_enable(true);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto enable_coin_list =
        result.m_msg->asset_margin_result().per_user_setting_data().user_enabled_collateral_coin_list();
    ASSERT_EQ(enable_coin_list.size(), 1);
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), static_cast<biz::coin_t>(5), 1, "10000",
                                       "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << '\n';
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid2, te->GenUUID(), static_cast<biz::coin_t>(5), 1, "100000",
                                       "0");
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << '\n';
  }

  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.005", "150");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetIsLeverage(true);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");

    auto result = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货挂单:" << jsonStr << std::endl;
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("150"));
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.005", "150");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetIsLeverage(true);

    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");

    auto result = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货挂单:" << jsonStr << std::endl;

    while (true) {
      result = te->PopResult(300);
      if (result.m_msg.get() == nullptr) {
        break;
      }
    }
  }

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(1);
    req.set_enable(false);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 3200246);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "设置失败响应:" << jsonStr << std::endl;

    auto enable_coin_list =
        result.m_msg->asset_margin_result().per_user_setting_data().user_enabled_collateral_coin_list();
    ASSERT_EQ(enable_coin_list.size(), 1);
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), static_cast<biz::coin_t>(1), 1, "0.005",
                                       "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << '\n';
  }

  {
    std::string req_id = "";
    ::svc::unified_v2::req::settings::CollateralCoin req;
    req.set_coin(1);
    req.set_enable(false);

    AccountCustomeCollateralBuilder collateral_build(req_id, uid1, 1, req);
    auto resp1 = user1.process(collateral_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "设置成功响应:" << jsonStr << std::endl;

    auto enable_coin_list =
        result.m_msg->asset_margin_result().per_user_setting_data().user_enabled_collateral_coin_list();
    ASSERT_EQ(enable_coin_list.size(), 0);
  }
}
