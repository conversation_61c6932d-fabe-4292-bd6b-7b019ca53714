#include <string>

#include "src/data/type/biz_type.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/wallet_service/wallet_com_test.hpp"

/**
 * 切换逐仓模式-正常场景
 * 场景1：
 *   全仓模式下，uid1充值100USDC bonus,1BTC,期权开仓付出权利金20，再平仓得到1块权利金,UID1产生19块钱赠金负债，
 *   再切换逐仓模式，
 *   期望结果：切换失败（因为某单币种存在赠金负债，不允许切换逐仓）
 * 场景2：
 *   充值USDC19还清赠金产生的负债，
 *   再切换逐仓模式，
 *   期望结果：切换IM成功
 */
TEST_F(WalletTestCom, switch_margin_mode_bonus_normal_case) {
  // UID1
  biz::user_id_t const uid1 = 100000;
  biz::user_id_t const uid2 = 200000;
  stub user1(te, uid1);
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), static_cast<biz::coin_t>(16), 1, "100",
                                       "100");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << '\n';
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), static_cast<biz::coin_t>(1), 1, "2", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << '\n';
  }

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid2, te->GenUUID(), static_cast<biz::coin_t>(16), 1, "100000",
                                       "0");
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << '\n';
  }

  te->AddOptionMarkPrice(static_cast<ESymbol>(102312), 1, 1, 1);

  {
    // UID1 创建期权订单
    auto order_link_id = te->GenUUID();
    CreateOrderBuilder create_ao_builder("option", "BTC-31DEC24-40000-C", 0, "buy", "Limit", "1", "20");
    create_ao_builder.SetOrderLinkID(order_link_id);
    auto resp = user1.create_order(create_ao_builder.Build());
    auto result = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "uid1创建期权订单:" << jsonStr << '\n';
    ASSERT_EQ(resp->order_link_id(), order_link_id);
  }

  {
    // UID2 创建期权订单
    auto order_link_id = te->GenUUID();
    CreateOrderBuilder create_ao_builder("option", "BTC-31DEC24-40000-C", 0, "Sell", "Limit", "1", "20");
    create_ao_builder.SetOrderLinkID(order_link_id);
    auto resp = user2.create_order(create_ao_builder.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id);
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      std::string jsonStr;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
      std::cout << "uid2创建期权订单:" << jsonStr << '\n';
    }
  }

  {
    // UID1 创建期权订单
    auto order_link_id = te->GenUUID();
    CreateOrderBuilder create_ao_builder("option", "BTC-31DEC24-40000-C", 0, "Sell", "Limit", "1", "1");
    create_ao_builder.SetOrderLinkID(order_link_id);
    auto resp = user1.create_order(create_ao_builder.Build());
    auto result = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "uid1创建期权订单:" << jsonStr << '\n';
    ASSERT_EQ(resp->order_link_id(), order_link_id);
  }

  {
    // UID2 创建期权订单
    auto order_link_id = te->GenUUID();
    CreateOrderBuilder create_ao_builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "1", "1");
    create_ao_builder.SetOrderLinkID(order_link_id);
    auto resp = user2.create_order(create_ao_builder.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id);
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      std::string jsonStr;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
      std::cout << "uid2创建期权订单:" << jsonStr << '\n';
    }
  }

  // 切换逐仓模式
  UnifiedV2ResultDTOChecker checker = switchIm(uid1);
  // USDC单币种存在赠金负债 liability_without_bonus > 0 ，因此切换失败
  ASSERT_EQ(checker.m_msg.get()->ret_code(), 3200249);

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), static_cast<biz::coin_t>(16), 1, "19", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << '\n';
  }

  // 切换逐仓模式
  checker = switchIm(uid1);
  // USDC单币种赠金负债还款后，可以正常切换成功
  ASSERT_EQ(checker.m_msg.get()->ret_code(), 0);
}
