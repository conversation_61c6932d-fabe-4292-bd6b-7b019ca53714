//
// Created by SH00728<PERSON> on 2023/3/24.
//
#include <memory>

#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/liquidation/liquidation_test.hpp"
#include "test/biz/trade/wallet_service/wallet_service_test.h"

TEST_F(WalletServcieIntegrationTest, switch_margin_modebizservice_pm_) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "431030";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "***********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "15", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "15", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  while (true) {
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  // 切换模式
  int64_t result = switchMarginModeReturnCode(uid1, EAccountMode::Portfolio);
  ASSERT_EQ(result, error::ErrorCode::kErrorCodeSuccess);
  std::cout << "切换Result:" << result << std::endl;
}

TEST_F(WalletServcieIntegrationTest, switch_margin_modebizservice_isolated_) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "431030";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "***********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "15", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "15", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  while (true) {
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  // 切换模式
  int64_t result = switchMarginModeReturnCode(uid1, EAccountMode::Isolated);
  ASSERT_EQ(result, error::ErrorCode::kErrorCodeSuccess);
  std::cout << "切换Result:" << result << std::endl;
}

TEST_F(WalletServcieIntegrationTest, switch_margin_modebizservice_isolated_position) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  openAccount(uid1);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "**********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  // biz::user_id_t const uid = 10000;
  std::string symbol = "BTC-31DEC24-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string qty = "1";
  std::string price = "2000";

  createOptPosition(uid1, symbol, position_idx, side, qty, price);

  // 切换模式
  int64_t result = switchMarginModeReturnCode(uid1, EAccountMode::Isolated);
  ASSERT_EQ(result, error::ErrorCode::kOptionPositionNotEmpty);
  std::cout << "切换Result:" << result << std::endl;
}

TEST_F(WalletServcieIntegrationTest, switch_margin_modebizservice_isolated_order) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  openAccount(uid1);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "**********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  // biz::user_id_t const uid = 10000;
  std::string symbol = "BTC-31DEC24-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string qty = "1";
  std::string price = "2000";

  {
    // 创建期货订单
    createOptOrder(uid1, symbol, position_idx, side, qty, price);
  }
  // 切换模式
  int64_t result = switchMarginModeReturnCode(uid1, EAccountMode::Isolated);
  ASSERT_EQ(result, error::ErrorCode::kOptionOrderNotEmpty);
  std::cout << "切换Result:" << result << std::endl;
}

TEST_F(WalletServcieIntegrationTest, switch_margin_modebizservice_pm_order) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  openAccount(uid1);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "**********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  // biz::user_id_t const uid = 10000;
  std::string symbol = "BTC-31DEC24-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string qty = "1";
  std::string price = "2000";

  {
    // 创建期货订单
    createOptOrder(uid1, symbol, position_idx, side, qty, price);
  }
  // 切换模式
  int64_t result = switchMarginModePm(uid1);
  ASSERT_EQ(result, error::ErrorCode::kErrorCodeSuccess);
  std::cout << "切换Result:" << result << std::endl;
}

TEST_F(WalletServcieIntegrationTest, switch_margin_modebizservice_pm_position) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  openAccount(uid1);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "**********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  // biz::user_id_t const uid = 10000;
  std::string symbol = "BTC-31DEC24-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string qty = "1";
  std::string price = "2000";

  createOptPosition(uid1, symbol, position_idx, side, qty, price);

  // 切换模式
  int64_t result = switchMarginModePm(uid1);
  ASSERT_EQ(result, error::ErrorCode::kErrorCodeSuccess);
  std::cout << "切换Result:" << result << std::endl;
}

// namespace biz
