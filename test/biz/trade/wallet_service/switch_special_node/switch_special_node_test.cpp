//
// Created by SH00728ML on 2023/3/24.
//

#include <gtest/gtest.h>

#include <memory>
#include <string>

#include "biz_worker/service/wallet/wallet_service.hpp"
#include "lib/msg_builder.hpp"
#include "margin_request/event/event.hpp"
#include "src/application/sharding_config/sharding_config.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/liquidation/liquidation_test.hpp"
#include "test/biz/trade/wallet_service/wallet_service_test.h"

TEST_F(WalletServcieIntegrationTest, SwitchSpecialNodeGroupCheckFail) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchSpecialNode);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
}

TEST_F(WalletServcieIntegrationTest, SwitchSpecialRequestCheckException) {
  biz::user_id_t uid1 = 0;
  stub user1(te, uid1);
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchSpecialNode);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
}

TEST_F(WalletServcieIntegrationTest, SwitchSpecialNodeCheckGroupFail) {
  std::shared_ptr<sharding_config::ShardingConfig> sharding_config =
      std::make_shared<sharding_config::ShardingConfig>();
  std::shared_ptr<sharding_config::ShardingNodeConfig> sharding_node_config =
      std::make_shared<sharding_config::ShardingNodeConfig>();
  sharding_node_config->group = "g0";

  sharding_config->nodes.insert("g0i0", sharding_node_config);
  sharding_config::ShardingConfigWrapper config;
  config.config.insert(1, sharding_config);
  config.current_version = 1;

  application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.push_back(config);
  application::GlobalVarManager::Instance().sharding_config_storage().config_ =
      &application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.back();

  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchSpecialNode);

  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_switchspecialnodereq()->set_spnode(
      application::GlobalVarManager::Instance().node_name());
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
}

TEST_F(WalletServcieIntegrationTest, SwitchSpecialNodeOptOrder) {
  biz::user_id_t const uid = 10000;
  std::string symbol = "BTC-31DEC24-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string qty = "1";
  std::string price = "2000";
  depositAccount(uid, 16, "50000");
  createOptOrder(uid, symbol, position_idx, side, qty, price);

  std::shared_ptr<sharding_config::ShardingConfig> sharding_config =
      std::make_shared<sharding_config::ShardingConfig>();
  std::shared_ptr<sharding_config::ShardingNodeConfig> sharding_node_config =
      std::make_shared<sharding_config::ShardingNodeConfig>();
  sharding_node_config->group = "g0";
  sharding_config->nodes.insert("g0i0", sharding_node_config);
  sharding_config::ShardingConfigWrapper config;
  config.config.insert(1, sharding_config);
  config.current_version = 1;

  application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.push_back(config);
  application::GlobalVarManager::Instance().sharding_config_storage().config_ =
      &application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.back();

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchSpecialNode);
  auto* req = unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_switchspecialnodereq();
  req->set_spnode("g0i0");
  application::GlobalVarManager::Instance().set_node_name("g0i0public");

  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  application::GlobalVarManager::Instance().set_node_name("g0p0");
}

TEST_F(WalletServcieIntegrationTest, SwitchSpecialNodeFutureOrder) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  depositAccount(uid1, 16, "50000");

  // 创建期货订单
  OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
  build.SetOrderLinkID(te->GenUUID());
  auto resp1 = user1.create_order(build.Build());
  ASSERT_NE(resp1->order_id(), "");

  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "uid1期货订单Result:" << jsonStr2 << std::endl;

  std::shared_ptr<sharding_config::ShardingConfig> sharding_config =
      std::make_shared<sharding_config::ShardingConfig>();
  std::shared_ptr<sharding_config::ShardingNodeConfig> sharding_node_config =
      std::make_shared<sharding_config::ShardingNodeConfig>();
  sharding_node_config->group = "g0";
  sharding_config->nodes.insert("g0i0", sharding_node_config);
  sharding_config->nodes.insert("g0i0public", sharding_node_config);
  application::GlobalVarManager::Instance().set_node_name("g0i0public");

  sharding_config::ShardingConfigWrapper config;
  config.config.insert(1, sharding_config);
  config.current_version = 1;

  application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.push_back(config);
  application::GlobalVarManager::Instance().sharding_config_storage().config_ =
      &application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.back();

  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchSpecialNode);
  auto* req = unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_switchspecialnodereq();
  req->set_spnode("g0i0");

  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  application::GlobalVarManager::Instance().set_node_name("g0p0");
}

TEST_F(WalletServcieIntegrationTest, SwitchSpecialNodeIsolatedSuccess) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "31030";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "***********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 20000, 20000, 20000);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  while (true) {
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    std::shared_ptr<sharding_config::ShardingConfig> sharding_config =
        std::make_shared<sharding_config::ShardingConfig>();
    std::shared_ptr<sharding_config::ShardingNodeConfig> sharding_node_config =
        std::make_shared<sharding_config::ShardingNodeConfig>();
    sharding_node_config->group = "g0";
    sharding_config->nodes.insert("g0i0", sharding_node_config);
    sharding_config->nodes.insert("g0i0public", sharding_node_config);
    sharding_config::ShardingConfigWrapper config;
    config.config.insert(1, sharding_config);
    config.current_version = 1;

    application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.push_back(config);
    application::GlobalVarManager::Instance().sharding_config_storage().config_ =
        &application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.back();
  }

  {
    auto unified_v_2_request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request->mutable_req_header()->set_action(EAction::SwitchSpecialNode);
    auto* req_switch_node = unified_v_2_request->mutable_walletreqgroupbody()->mutable_switchspecialnodereq();
    req_switch_node->set_spnode("g0i0");
    application::GlobalVarManager::Instance().set_node_name("g0i0public");

    auto event_switch_node = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event_switch_node, false);
    application::GlobalVarManager::Instance().set_node_name("g0p0");
  }
}

TEST_F(WalletServcieIntegrationTest, SwitchSpecialNodePmSuccess) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "31030";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "***********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  switchPm(uid1);

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 20000, 20000, 20000);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  while (true) {
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    std::shared_ptr<sharding_config::ShardingConfig> sharding_config =
        std::make_shared<sharding_config::ShardingConfig>();
    std::shared_ptr<sharding_config::ShardingNodeConfig> sharding_node_config =
        std::make_shared<sharding_config::ShardingNodeConfig>();
    sharding_node_config->group = "g0";
    sharding_config->nodes.insert("g0i0", sharding_node_config);
    sharding_config->nodes.insert("g0i0public", sharding_node_config);
    sharding_config::ShardingConfigWrapper config;
    config.config.insert(1, sharding_config);
    config.current_version = 1;

    application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.push_back(config);
    application::GlobalVarManager::Instance().sharding_config_storage().config_ =
        &application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.back();
  }

  {
    auto unified_v_2_request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request->mutable_req_header()->set_action(EAction::SwitchSpecialNode);
    auto* req_switch_node = unified_v_2_request->mutable_walletreqgroupbody()->mutable_switchspecialnodereq();
    req_switch_node->set_spnode("g0i0");
    application::GlobalVarManager::Instance().set_node_name("g0i0public");

    auto event_switch_node = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event_switch_node, false);
    application::GlobalVarManager::Instance().set_node_name("g0p0");
  }
}

TEST_F(WalletServcieIntegrationTest, SwitchSpecialNodeStatusInLiq) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  // 账户开户
  openAccount(uid1);
  openAccount(uid2);

  // 账户充值
  depositAccount(uid1, ECoin::USDT, "50000");
  depositAccount(uid2, ECoin::USDT, "50000");

  // 调整风险限额
  increaseRiskLimit(uid1, ESymbol::BTCUSDT, ECoin::USDT, 2);
  increaseRiskLimit(uid2, ESymbol::BTCUSDT, ECoin::USDT, 2);

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Taker:" << jsonStr2 << std::endl;
  }

  {
    // 等待成交回报
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "成交回报Maker:" << jsonStr2 << std::endl;
  }

  {
    auto unified_v_2_request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request->mutable_req_header()->set_action(EAction::LabelCodeSyn);
    auto* label_req = unified_v_2_request->mutable_settingsreqgroupbody()->mutable_labelcodesyncreq();
    label_req->set_label_code("MANUAL_LIQ_MODE");
    label_req->set_label_value("TRUE");

    auto event_switch_node = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event_switch_node, false);
    std::cout << "手工强平设置完成" << std::endl;
  }

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 9000000000000, 20000, 20000);

  {
    std::cout << "定时盈亏1:" << std::endl;
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    usleep(1000);
  }

  // 触发强平取消开仓单
  {
    std::cout << "定时盈亏2:" << std::endl;
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "强平结果2:" << jsonStr2 << std::endl;

    while (true) {
      result = te->PopResult(1000);
      if (result.m_msg == nullptr) {
        break;
      }

      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "强平cancel结果:" << jsonStr2 << std::endl;
    }
  }

  {
    std::shared_ptr<sharding_config::ShardingConfig> sharding_config =
        std::make_shared<sharding_config::ShardingConfig>();
    std::shared_ptr<sharding_config::ShardingNodeConfig> sharding_node_config =
        std::make_shared<sharding_config::ShardingNodeConfig>();
    sharding_node_config->group = "g0";
    sharding_config->nodes.insert("g0i0", sharding_node_config);
    sharding_config->nodes.insert("g0i0public", sharding_node_config);
    sharding_config::ShardingConfigWrapper config;
    config.config.insert(1, sharding_config);
    config.current_version = 1;

    application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.push_back(config);
    application::GlobalVarManager::Instance().sharding_config_storage().config_ =
        &application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.back();
  }

  {
    auto unified_v_2_request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request->mutable_req_header()->set_action(EAction::SwitchSpecialNode);
    auto* req_switch_node = unified_v_2_request->mutable_walletreqgroupbody()->mutable_switchspecialnodereq();
    req_switch_node->set_spnode("g0i0");
    application::GlobalVarManager::Instance().set_node_name("g0i0public");

    auto event_switch_node = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event_switch_node, false);
    std::cout << "SwitchSpecialNode:" << std::endl;
  }
  application::GlobalVarManager::Instance().set_node_name("g0p0");
}

TEST_F(WalletServcieIntegrationTest, SwitchSpecialNodeRmim) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  // 账户开户
  openAccount(uid1);
  openAccount(uid2);

  // 账户充值
  depositAccount(uid1, ECoin::USDT, "50000");
  depositAccount(uid2, ECoin::USDT, "50000");

  // 调整风险限额
  increaseRiskLimit(uid1, ESymbol::BTCUSDT, ECoin::USDT, 2);
  increaseRiskLimit(uid2, ESymbol::BTCUSDT, ECoin::USDT, 2);

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Taker:" << jsonStr2 << std::endl;
  }

  {
    // 等待成交回报
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "成交回报Maker:" << jsonStr2 << std::endl;
  }

  {
    auto unified_v_2_request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request->mutable_req_header()->set_action(EAction::LabelCodeSyn);
    auto* label_req = unified_v_2_request->mutable_settingsreqgroupbody()->mutable_labelcodesyncreq();
    label_req->set_label_code("MANUAL_LIQ_MODE");
    label_req->set_label_value("TRUE");

    auto event_switch_node = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event_switch_node, false);
    std::cout << "手工强平设置完成" << std::endl;
  }

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 518000, 20000, 20000);

  {
    std::cout << "定时盈亏1:" << std::endl;
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    usleep(1000);
  }

  // 触发强平取消开仓单
  {
    std::cout << "定时盈亏2:" << std::endl;
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "强平结果2:" << jsonStr2 << std::endl;

    while (true) {
      result = te->PopResult(1000);
      if (result.m_msg == nullptr) {
        break;
      }

      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "强平cancel结果:" << jsonStr2 << std::endl;
    }
  }

  {
    std::shared_ptr<sharding_config::ShardingConfig> sharding_config =
        std::make_shared<sharding_config::ShardingConfig>();
    std::shared_ptr<sharding_config::ShardingNodeConfig> sharding_node_config =
        std::make_shared<sharding_config::ShardingNodeConfig>();
    sharding_node_config->group = "g0";
    sharding_config->nodes.insert("g0i0", sharding_node_config);
    sharding_config->nodes.insert("g0i0public", sharding_node_config);
    sharding_config::ShardingConfigWrapper config;
    config.config.insert(1, sharding_config);
    config.current_version = 1;

    application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.push_back(config);
    application::GlobalVarManager::Instance().sharding_config_storage().config_ =
        &application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.back();
  }

  {
    auto unified_v_2_request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request->mutable_req_header()->set_action(EAction::SwitchSpecialNode);
    auto* req_switch_node = unified_v_2_request->mutable_walletreqgroupbody()->mutable_switchspecialnodereq();
    req_switch_node->set_spnode("g0i0");
    application::GlobalVarManager::Instance().set_node_name("g0i0public");

    auto event_switch_node = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event_switch_node, false);
    std::cout << "SwitchSpecialNode:" << std::endl;
    application::GlobalVarManager::Instance().set_node_name("g0p0");
  }
}

TEST_F(WalletServcieIntegrationTest, SwitchSpecialNodeIsolatedUSDTPriceFail) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "31030";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "***********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 20000, 20000, 20000);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  while (true) {
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    std::shared_ptr<sharding_config::ShardingConfig> sharding_config =
        std::make_shared<sharding_config::ShardingConfig>();
    std::shared_ptr<sharding_config::ShardingNodeConfig> sharding_node_config =
        std::make_shared<sharding_config::ShardingNodeConfig>();
    sharding_node_config->group = "g0";
    sharding_config->nodes.insert("g0i0", sharding_node_config);
    sharding_config->nodes.insert("g0i0public", sharding_node_config);
    sharding_config::ShardingConfigWrapper config;
    config.config.insert(1, sharding_config);
    config.current_version = 1;

    application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.push_back(config);
    application::GlobalVarManager::Instance().sharding_config_storage().config_ =
        &application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.back();
  }

  {
    te->AddMarkPrice(static_cast<ESymbol>(5), 21900, 21900, 21900);

    auto unified_v_2_request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request->mutable_req_header()->set_action(EAction::SwitchSpecialNode);
    auto* req_switch_node = unified_v_2_request->mutable_walletreqgroupbody()->mutable_switchspecialnodereq();
    req_switch_node->set_spnode("g0i0");
    application::GlobalVarManager::Instance().set_node_name("g0i0public");

    auto event_switch_node = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event_switch_node, false);
    application::GlobalVarManager::Instance().set_node_name("g0p0");
  }
}

TEST_F(WalletServcieIntegrationTest, SwitchSpecialNodeIsolatedUSDCPriceFail) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "31030";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "***********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(45), 20000, 20000, 20000);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCPERP", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCPERP", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  while (true) {
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    std::shared_ptr<sharding_config::ShardingConfig> sharding_config =
        std::make_shared<sharding_config::ShardingConfig>();
    std::shared_ptr<sharding_config::ShardingNodeConfig> sharding_node_config =
        std::make_shared<sharding_config::ShardingNodeConfig>();
    sharding_node_config->group = "g0";
    sharding_config->nodes.insert("g0i0", sharding_node_config);
    sharding_config->nodes.insert("g0i0public", sharding_node_config);
    sharding_config::ShardingConfigWrapper config;
    config.config.insert(1, sharding_config);
    config.current_version = 1;

    application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.push_back(config);
    application::GlobalVarManager::Instance().sharding_config_storage().config_ =
        &application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.back();
  }

  {
    te->AddMarkPrice(static_cast<ESymbol>(45), 21900, 21900, 21900);

    auto unified_v_2_request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request->mutable_req_header()->set_action(EAction::SwitchSpecialNode);
    auto* req_switch_node = unified_v_2_request->mutable_walletreqgroupbody()->mutable_switchspecialnodereq();
    req_switch_node->set_spnode("g0i0");
    application::GlobalVarManager::Instance().set_node_name("g0i0public");

    auto event_switch_node = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event_switch_node, false);
    application::GlobalVarManager::Instance().set_node_name("g0p0");
  }
}

TEST_F(WalletServcieIntegrationTest, SwitchSpecialNodeIsolatedMarkPriceNull) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "31030";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "***********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(45), 20000, 20000, 20000);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCPERP", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCPERP", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  while (true) {
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    std::shared_ptr<sharding_config::ShardingConfig> sharding_config =
        std::make_shared<sharding_config::ShardingConfig>();
    std::shared_ptr<sharding_config::ShardingNodeConfig> sharding_node_config =
        std::make_shared<sharding_config::ShardingNodeConfig>();
    sharding_node_config->group = "g0";
    sharding_config->nodes.insert("g0i0", sharding_node_config);
    sharding_config->nodes.insert("g0i0public", sharding_node_config);
    sharding_config::ShardingConfigWrapper config;
    config.config.insert(1, sharding_config);
    config.current_version = 1;

    application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.push_back(config);
    application::GlobalVarManager::Instance().sharding_config_storage().config_ =
        &application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.back();
  }

  {
    te->AddMarkPrice(static_cast<ESymbol>(45), 0, 0, 0);

    auto unified_v_2_request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request->mutable_req_header()->set_action(EAction::SwitchSpecialNode);
    auto* req_switch_node = unified_v_2_request->mutable_walletreqgroupbody()->mutable_switchspecialnodereq();
    req_switch_node->set_spnode("g0i0");
    application::GlobalVarManager::Instance().set_node_name("g0i0public");

    auto event_switch_node = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event_switch_node, false);
    application::GlobalVarManager::Instance().set_node_name("g0p0");
  }
}

TEST_F(WalletServcieIntegrationTest, SwitchSpecialNodeHitUpgrading) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "31030";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "***********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
  }

  while (true) {
    auto result = te->PopResult(500);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 20000, 20000, 20000);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
  }

  while (true) {
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    std::shared_ptr<sharding_config::ShardingConfig> sharding_config =
        std::make_shared<sharding_config::ShardingConfig>();
    std::shared_ptr<sharding_config::ShardingNodeConfig> sharding_node_config =
        std::make_shared<sharding_config::ShardingNodeConfig>();
    sharding_node_config->group = "g0";
    sharding_config->nodes.insert("g0i0", sharding_node_config);
    sharding_config->nodes.insert("g0i0public", sharding_node_config);
    sharding_config::ShardingConfigWrapper config;
    config.config.insert(1, sharding_config);
    config.current_version = 1;

    application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.push_back(config);
    application::GlobalVarManager::Instance().sharding_config_storage().config_ =
        &application::GlobalVarManager::Instance().sharding_config_storage().config_deque_.back();
  }

  userUpgradingLabel(uid1);
  userUpgradingLabel(uid2);

  {
    auto unified_v_2_request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request->mutable_req_header()->set_action(EAction::SwitchSpecialNode);
    auto* req_switch_node = unified_v_2_request->mutable_walletreqgroupbody()->mutable_switchspecialnodereq();
    req_switch_node->set_spnode("g0i0");
    application::GlobalVarManager::Instance().set_node_name("g0i0public");

    auto event_switch_node = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event_switch_node, false);
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "用户迁出单测阶段性日志:" << jsonStr2 << std::endl;
    }
    application::GlobalVarManager::Instance().set_node_name("g0p0");
  }
}
