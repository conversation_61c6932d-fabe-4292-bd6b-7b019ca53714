#include <string>

#include "biz_worker/service/trade/options/event_handler/settle.hpp"
#include "cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "cross_worker/cross_receiver/xresp/x_option_resp_pkg.hpp"
#include "lib/stub.hpp"
#include "seq_mark_worker/cross_seq_manager.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/options/modules/positionbiz/positionbiz.hpp"
#include "src/biz_worker/service/trade/store/per_coin_store.hpp"
#include "src/biz_worker/service/trade/store/position/raw_position.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "src/data/event/biz_event.hpp"
#include "src/data/type/biz_type.hpp"
#include "src/margin_request/event/event.hpp"
#include "src/seq_mark_worker/utils.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/option/settle_test.hpp"
#include "test/biz/trade/wallet_service/wallet_com_test.hpp"

namespace WalletOptionSettle {

void BuildProduceMsg(biz::cross_idx_t cross_idx, biz::cross::xReqSender& sender, bbase::hdts::Producer::Message& msg) {
  biz::CrossError err;
  /// 组装topic
  msg.topic = fmt::format("request_of_{}", cross_idx);

  /// 组装buf
  msg.value.resize(sizeof(biz::cross::RawXHeader) + sizeof(biz::cross::request::RawXRequest));
  memcpy(msg.value.data(), &sender.req_mgr_.header_obj_->header_, sizeof(biz::cross::RawXHeader));
  sender.req_mgr_.request_obj_->ToBinaryBuf(static_cast<char*>(msg.value.data()) + sizeof(biz::cross::RawXHeader));

  /// 处理透传数据 passthrough
  ::models::passthroughdto::PassThroughDTO passthrough;

  auto pass_through = passthrough.mutable_option_passthrough_dto();
  pass_through->set_bztype("Contract");
  pass_through->set_symbolid(102312);
  auto contract_dto = pass_through->mutable_contractdto();
  contract_dto->set_contractstatus(com::bybit::option::cross::model::pb::ContractStatusEnum::Settling);
  ::com::bybit::option::cross::model::pb::QuoteMoney* price = new ::com::bybit::option::cross::model::pb::QuoteMoney();
  auto money = price->mutable_amount();
  money->set_scale(8);
  money->set_unscaledvalue(5000000000000);
  auto* price1 = new ::com::bybit::option::cross::model::pb::QuoteMoney(*price);
  auto* price2 = new ::com::bybit::option::cross::model::pb::QuoteMoney(*price);
  auto* price3 = new ::com::bybit::option::cross::model::pb::QuoteMoney(*price);
  auto* price4 = new ::com::bybit::option::cross::model::pb::QuoteMoney(*price);
  auto* price5 = new ::com::bybit::option::cross::model::pb::QuoteMoney(*price);
  auto mutable_quoteprice = contract_dto->mutable_quoteprice();
  mutable_quoteprice->set_allocated_indexprice(price);
  mutable_quoteprice->set_allocated_strikeprice(price1);
  mutable_quoteprice->set_allocated_markprice(price2);
  mutable_quoteprice->set_allocated_deliveryprice(price3);
  mutable_quoteprice->set_allocated_underlyingprice(price4);
  mutable_quoteprice->set_allocated_marketprice(price5);
  auto value = passthrough.SerializeAsString();

  msg.headers[biz::XComm::x_pass_through_pb] = value;
}

/**
 * 发送交割信息
 */
void SendSettleMessage() {
  bbase::hdts::Producer::Message msg;

  biz::cross::xReqSender sender;
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  auto contract_info = std::make_shared<store::Contract>();
  contract_info->set_contract_status(static_cast<EContractStatus>(EContractStatus::Settling));
  sender.req_mgr_.x_pass_through_->mutable_contract_info()->set_contract_status(EContractStatus::Settling);

  auto err = sender.BuildHeader(biz::cross::request::kMTContract, ECreateType::UNKNOWN);
  if (err.HasErr()) {
    return;
  }

  /// 组装req写入值确保下游不会panic
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTContract;
  sender.req_mgr_.request_obj_->req_.symbol_ = 102312;
  sender.req_mgr_.request_obj_->req_.side_ = biz::cross::request::kSideBuy;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = biz::cross::request::kTIFGoodTillCancel;
  sender.req_mgr_.request_obj_->req_.order_type_ = biz::cross::request::kOTMarketOrder;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);

  bbase::hdts::Producer::Message produce_msg;
  BuildProduceMsg(10007, sender, produce_msg);
  /// #3: 发送数据
  produce_msg.headers[biz::XComm::send_to_cross] = std::to_string(bbase::utils::Time::GetTimeNs());
  produce_msg.need_callback = true;
  produce_msg.callback_data.data = nullptr;
  produce_msg.callback_data.start_timestamp_ns = bbase::utils::Time::GetTimeNs();

  bbase::hdts::Hdts::ProduceMessage(produce_msg);
}
}  // namespace WalletOptionSettle

/**
 * 钱包cumRealisedPnl-永续场景
 * 场景1：全仓模式下，开仓、平仓，期望仓位的cumRealizedPnl和币种钱包的cumRealizedPnl相等
 */
TEST_F(WalletTestCom, wallet_cumrealisedpnl_future_normal_case) {
  // UID1
  biz::user_id_t const uid1 = 100000;
  stub user1(te, uid1);
  biz::user_id_t const uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  // 切换全仓模式
  //  switchRm(uid1);
  //  switchRm(uid2);

  // 充值
  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), static_cast<biz::coin_t>(5), 1, "20000",
                                       "20000");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值uid1 Result:" << jsonStr2 << '\n';
  }
  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid2, te->GenUUID(), static_cast<biz::coin_t>(5), 1, "20000",
                                       "20000");
    auto resp2 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp2->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值uid2 Result:" << jsonStr2 << '\n';
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 20000, 20000, 20000);
  }

  // 构造开仓场景
  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();

    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1挂单Result:" << jsonStr2 << '\n';
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "uid2成交Result:" << jsonStr2 << '\n';

      auto wallets = result.m_msg.get()->asset_margin_result().affected_wallets();
      ASSERT_EQ(wallets.empty(), false);
      ASSERT_EQ(wallets.find(5)->second.has_wallet(), true);

      auto position = result.m_msg.get()->futures_margin_result().affected_positions(0);

      auto cum_realised_pnl_d = bbase::decimal::Decimal(position.cum_realised_pnl_e8(), -position.value_scale());
      auto w_cum_realised_pnl_s = wallets.find(5)->second.wallet().cum_realised_pnl();
      ASSERT_EQ(cum_realised_pnl_d, bbase::decimal::Decimal(w_cum_realised_pnl_s));
    }
  }

  // 构造平仓场景
  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();

    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1挂单Result:" << jsonStr2 << '\n';
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "uid2成交Result:" << jsonStr2 << '\n';

      auto wallets = result.m_msg.get()->asset_margin_result().affected_wallets();
      ASSERT_EQ(wallets.empty(), false);
      ASSERT_EQ(wallets.find(5)->second.has_wallet(), true);

      auto position = result.m_msg.get()->futures_margin_result().affected_positions(0);

      auto cum_realised_pnl_d = bbase::decimal::Decimal(position.cum_realised_pnl_e8(), -position.value_scale());
      auto w_cum_realised_pnl_s = wallets.find(5)->second.wallet().cum_realised_pnl();
      ASSERT_EQ(cum_realised_pnl_d, bbase::decimal::Decimal(w_cum_realised_pnl_s));
    }
  }
}

/**
 * 钱包cumRealisedPnl-期权场景
 * 场景1：全仓模式下，开仓、平仓，期望仓位的cumRealizedPnl和币种钱包的cumRealizedPnl相等
 */
TEST_F(WalletTestCom, wallet_cumrealisedpnl_option_normal_case) {
  // UID1
  biz::user_id_t const uid1 = 100000;
  stub user1(te, uid1);
  biz::user_id_t const uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  // 切换全仓模式
  //  switchRm(uid1);
  //  switchRm(uid2);

  // 充值
  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), static_cast<biz::coin_t>(5), 1, "20000",
                                       "20000");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值uid1 Result:" << jsonStr2 << '\n';
  }
  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid2, te->GenUUID(), static_cast<biz::coin_t>(5), 1, "20000",
                                       "20000");
    auto resp2 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp2->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值uid2 Result:" << jsonStr2 << '\n';
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 20000, 20000, 20000);
  }

  {
    // UID1 创建期权订单
    auto order_link_id = te->GenUUID();
    CreateOrderBuilder create_ao_builder("option", "BTC-31DEC24-40000-C", 0, "Sell", "Limit", "1", "2000");
    create_ao_builder.SetOrderLinkID(order_link_id);
    auto resp = user1.create_order(create_ao_builder.Build());
    auto result = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "uid1创建期权订单:" << jsonStr << std::endl;
    ASSERT_EQ(resp->order_link_id(), order_link_id);
  }
}

/**
 * 钱包cumRealisedPnl-期权场景
 * 场景1：全仓模式下，期权交割，期望仓位的cumRealizedPnl和币种钱包的cumRealizedPnl相等
 */
TEST_F(WalletTestCom, wallet_cumrealisedpnl_option_delivery_normal_case) {
  // UID1
  biz::user_id_t const uid1 = 100000;
  stub user1(te, uid1);
  biz::user_id_t const uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  // 切换全仓模式
  //  switchRm(uid1);
  //  switchRm(uid2);

  // 充值
  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), static_cast<biz::coin_t>(5), 1, "20000",
                                       "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值uid1 Result:" << jsonStr2 << '\n';
  }
  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid2, te->GenUUID(), static_cast<biz::coin_t>(5), 1, "20000",
                                       "0");
    auto resp2 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp2->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值uid2 Result:" << jsonStr2 << '\n';
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 20000, 20000, 20000);
  }

  {
    // UID1 创建期权订单
    auto order_link_id = te->GenUUID();
    CreateOrderBuilder create_ao_builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "1", "2000");
    create_ao_builder.SetOrderLinkID(order_link_id);
    auto resp = user1.create_order(create_ao_builder.Build());
    auto result = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "uid1创建期权订单:" << jsonStr << std::endl;
    ASSERT_EQ(resp->order_link_id(), order_link_id);
  }

  {
    // UID2 创建期权订单
    auto order_link_id = te->GenUUID();
    CreateOrderBuilder create_ao_builder("option", "BTC-31DEC24-40000-C", 0, "Sell", "Limit", "1", "2000");
    create_ao_builder.SetOrderLinkID(order_link_id);
    auto resp = user2.create_order(create_ao_builder.Build());

    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      std::string jsonStr;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
      std::cout << "uid2期权成交:" << jsonStr << std::endl;
      ASSERT_EQ(resp->order_link_id(), order_link_id);
    }
  }

  // 触发交割
  WalletOptionSettle::SendSettleMessage();
  {
    bbase::decimal::Decimal total_change(0);

    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "交割 Result:" << jsonStr2 << '\n';

      auto wallets = result.m_msg.get()->asset_margin_result().affected_wallets();
      ASSERT_EQ(wallets.empty(), false);
      ASSERT_EQ(wallets.find(16)->second.has_wallet(), true);

      // uid1和uid2仅期权仓位开仓及交割，钱包USDC中的累计盈亏与CB时相等的（钱包期初USDC为0）
      auto dc_wallet = wallets.find(16)->second.wallet();
      auto cash_balance = bbase::decimal::Decimal(dc_wallet.cash_balance());
      auto cum_realised_pnl = bbase::decimal::Decimal(dc_wallet.cum_realised_pnl());
      ASSERT_EQ(cash_balance, cum_realised_pnl);
      // 累加手续费需要按平台维度取反
      total_change.AddEq(bbase::decimal::Decimal(dc_wallet.total_trading_fee()).Negate());
      total_change.AddEq(cum_realised_pnl);

      // uid1和uid2仅期权仓位开仓及交割，钱包USDC中的累计盈亏与仓位上的累计盈亏是相等的
      auto position = result.m_msg.get()->options_margin_result().affected_positions(0);
      auto cum_realised_pnl_d = bbase::decimal::Decimal(position.cum_realised_pnl_e8(), -position.value_scale());
      auto w_cum_realised_pnl_s = wallets.find(16)->second.wallet().cum_realised_pnl();
      ASSERT_EQ(cum_realised_pnl_d, bbase::decimal::Decimal(w_cum_realised_pnl_s));
    }

    // 对账：uid1和uid2钱包USDC上记录的累计盈亏加上平台手续费应该是零和的（交易双方盈亏+平台手续费=0）
    ASSERT_EQ(total_change, bbase::decimal::Decimal(0));
  }
}
