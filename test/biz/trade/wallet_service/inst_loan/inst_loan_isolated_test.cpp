//
// Created by SH00634ML on 2023/7/30.
//
#include <iostream>
#include <thread>

#include "lib/stub.hpp"
#include "proto/gen/enums/einstactionstatus/inst_action_status.pb.h"
#include "src/biz_worker/service/trade/store/wallet/trans_log.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/wallet_service/inst_loan/inst_loan_test.hpp"

TEST_F(InstLoanTest, isolated_cancel) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "20000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // 挂单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(3));
  {
    // 现货活动单
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.002", "3.01");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
  {
    // 现货止盈止损
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.001", "20000");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetOrderFilter("tpslOrder");
    build.SetTriggerPrice("21000");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    te->SuspendCross();
    // 撤销订单
    InstCancelAll(uid1);
    InstCancelAll(uid1);
    te->ResumeCross();
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstCancelAll);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processing);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "第一次cancelResult:" << jsonStr << std::endl;

    // 第二次cancel结果
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    ASSERT_EQ(result1.m_msg->ret_code(), error::kErrorCodeSuccess);
    ASSERT_EQ(result1.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstCancelAll);
    ASSERT_EQ(result1.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processing);
    jsonStr = "";
    (void)google::protobuf::util::MessageToJsonString(*result1.m_msg.get(), &jsonStr);
    std::cout << "第二次cancelResult:" << jsonStr << std::endl;

    auto result2 = te->PopResult();
    jsonStr = "";
    ASSERT_NE(result2.m_msg.get(), nullptr);
    (void)google::protobuf::util::MessageToJsonString(*result2.m_msg.get(), &jsonStr);
    std::cout << "订单cancel撮合回报Result:" << jsonStr << std::endl;

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    ASSERT_EQ(result3.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstCancelAll);
    ASSERT_EQ(result3.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processed);
    jsonStr = "";
    (void)google::protobuf::util::MessageToJsonString(*result3.m_msg.get(), &jsonStr);
    std::cout << "第二次订单cancel撮合回报Result:" << jsonStr << std::endl;
  }

  {
    // 无订单cancel 直接返回成功
    InstCancelAll(uid1);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstCancelAll);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processed);
  }

  {
    // 强平中不允许撤单
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18000, 18000, 18000);
    te->SuspendCross();
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    InstCancelAll(uid1);
    te->ResumeCross();
    auto result1 = te->PopResult();
    std::string jsonStr = "";
    (void)google::protobuf::util::MessageToJsonString(*result1.m_msg.get(), &jsonStr);
    std::cout << "重算result:" << jsonStr << std::endl;
    auto result2 = te->PopResult();
    jsonStr = "";
    (void)google::protobuf::util::MessageToJsonString(*result2.m_msg.get(), &jsonStr);
    std::cout << "机构取消订单result:" << jsonStr << std::endl;
    ASSERT_NE(result2.m_msg.get(), nullptr);
    ASSERT_EQ(result2.m_msg->ret_code(), error::kAccountIsInLiq);
  }

  {
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg == nullptr) {
        break;
      }
      std::string jsonStr;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
      std::cout << "Result:" << jsonStr << std::endl;
    }
  }
}

TEST_F(InstLoanTest, isolated_close) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  // 接管户
  biz::user_id_t twap_uid = 5002;
  stub twap(te, twap_uid);
  openAccount(twap_uid);
  switchIm(twap_uid);

  switchIm(uid1);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "5000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // 挂单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置封禁
    InstBanOrUnban(uid1, true, false, false, false, false, false, false, false, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
  }

  {
    // 有活动单不允许平仓
    InstClosePz(uid1, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeInstLoanOrderNotEmpty);
  }

  {
    InstCancelAll(uid1);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
    // cancel撮合回报
    auto result1 = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result1.m_msg.get(), &jsonStr);
    std::cout << "取消撮合回报Result:" << jsonStr << std::endl;
    ASSERT_EQ(result1.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstCancelAll);
    ASSERT_EQ(result1.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processed);
  }

  {
    // 多次平仓返回处理中
    // 平仓中尝试cancel，返回报错
    te->SuspendCross();
    InstClosePz(uid1, false);
    InstClosePz(uid1, false);
    InstCancelAll(uid1);
    te->ResumeCross();
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstClosePz);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processing);
    // 重入close返回处理中
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    ASSERT_EQ(result1.m_msg->ret_code(), error::kErrorCodeSuccess);
    ASSERT_EQ(result1.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstClosePz);
    ASSERT_EQ(result1.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processing);
    // cancel结果返回报错
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    ASSERT_EQ(result2.m_msg->ret_code(), error::kAccountInstStatusNotNormal);
  }

  // OB转OTC
  {
    int32_t uid_msg_cnt = 0;
    int32_t twap_msg_cnt = 0;
    while (true) {
      auto result = te->PopResult(200);
      if (result.m_msg == nullptr) {
        break;
      }

      std::string jsonStr;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);

      if (result.m_msg->header().user_id() == uid1) {  // OB未成交 -> otc
        if (uid_msg_cnt == 0) {
          std::cout << "OB未成交Result:" << jsonStr << std::endl;

          auto const orders = result.m_msg->futures_margin_result().related_orders();
          ASSERT_EQ(orders.size(), 1);

          auto const order = orders[0];
          ASSERT_EQ(order.create_type(), ECreateType::CreateByInstOBLiqClose);
          ASSERT_EQ(order.order_status(), EOrderStatus::Cancelled);

          ASSERT_EQ(result.m_msg->asset_margin_result().has_inst_exec_result(), false);
          uid_msg_cnt++;
        } else if (uid_msg_cnt == 1) {
          std::cout << "OTC撮合回报Result:" << jsonStr << std::endl;

          auto const orders = result.m_msg->futures_margin_result().related_orders();
          ASSERT_EQ(orders.size(), 1);

          auto const order = orders[0];
          ASSERT_EQ(order.create_type(), ECreateType::CreateByTakeOver_PassThrough);
          ASSERT_EQ(order.order_status(), EOrderStatus::Filled);
          ASSERT_EQ(order.cross_status(), ECrossStatus::TakerFill);
          ASSERT_EQ(order.exec_qty_x(), 100000000);
          ASSERT_EQ(order.exec_price_x(), 180000000);

          auto const translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 1);

          auto const translog = translogs[0];
          ASSERT_EQ(translog.change(), "-2010.800000000000000000");
          ASSERT_EQ(translog.exec_fee(), "-10.800000000000000000");
          ASSERT_EQ(translog.cash_flow(), "-2000.000000000000000000");
          ASSERT_EQ(translog.fee_rate(), "0.000600000000000000");

          ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstClosePz);
          ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
                    enums::einstactionstatus::Processed);
          uid_msg_cnt++;

        } else {
          ASSERT_EQ(false, true);
        }
      } else if (result.m_msg->header().user_id() == twap_uid) {
        auto result1 = te->PopResult();
        if (twap_msg_cnt == 0) {
          std::cout << "twap接管仓位Result:" << jsonStr << std::endl;

          auto const orders = result.m_msg->futures_margin_result().related_orders();
          ASSERT_EQ(orders.size(), 1);

          auto const order = orders[0];
          ASSERT_EQ(order.create_type(), ECreateType::CreateByTakeOver_PassThrough);
          ASSERT_EQ(order.order_status(), EOrderStatus::Filled);
          ASSERT_EQ(order.cross_status(), ECrossStatus::MakerFill);
          ASSERT_EQ(order.exec_qty_x(), 100000000);
          ASSERT_EQ(order.exec_price_x(), 180000000);

          auto const fills = result.m_msg->futures_margin_result().related_fills();
          ASSERT_EQ(fills.size(), 1);

          auto const fill = fills[0];
          ASSERT_EQ(fill.taken_over_change_amount_e8(), ************);

          auto const translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 1);

          auto const translog = translogs[0];
          ASSERT_EQ(translog.change(), "2010.800000000000000000");
          ASSERT_EQ(translog.exec_fee(), "0.0");
          ASSERT_EQ(translog.cash_flow(), "0.0");
          ASSERT_EQ(translog.fee_rate(), "0.0");
          twap_msg_cnt++;
        } else if (twap_msg_cnt == 1) {
          std::cout << "twap仓位甩卖Result:" << jsonStr << std::endl;
          twap_msg_cnt++;
        } else {
          ASSERT_EQ(false, true);
        }
      } else {
        ASSERT_EQ(false, true);
      }
    }

    ASSERT_EQ(uid_msg_cnt, 2);
    ASSERT_EQ(twap_msg_cnt, 1);  // 为什么接管后甩卖单 消息包没有收到
  }

  // OB直接成交
  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 解禁交易
    InstBanOrUnban(uid1, false, false, false, false, false, false, false, false, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  // {
  //   auto result = te->PopResult();
  //   ASSERT_NE(result.m_msg, nullptr);
  //   ASSERT_EQ(result.m_msg->header().user_id(), 5002);
  // }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // 挂买单盘口用于OB成交
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // 设置封禁
    InstBanOrUnban(uid1, true, false, false, false, false, false, false, false, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
  }

  {
    InstClosePz(uid1, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstClosePz);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processing);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    std::string jsonStr = "";
    (void)google::protobuf::util::MessageToJsonString(*result1.m_msg.get(), &jsonStr);
    std::cout << "OB撮合回报Result:" << jsonStr << std::endl;
    ASSERT_EQ(result1.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstClosePz);
    ASSERT_EQ(result1.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processed);
  }
}

/**
 * 逐仓模式下多次发起OB强平场景
 * PostWorker多次投递PreWorker
 */
TEST_F(InstLoanTest, isolated_close_multi) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "*********", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "*********", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  // 修改风险额度
  {
    auto sc_ev = std::make_shared<event::OnUpdateConfigEvent>(0, event::EventType::kEventSymbolConfig,
                                                              event::EventSourceType::kUnknown,
                                                              event::EventDispatchType::kBroadcastToWorker);

    config::ConfigProxy::Instance().config_mgr()->set_symbol_config_mgr(
        std::make_shared<biz::sc::SymbolConfigManager>());
    config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr()->InitMockData();
    config::getTlsCfgMgrRaw()->spot_client_sptr()->InitMockData();
    sc_ev->set_symbol_config_mgr(config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr());

    auto& risk_info_set = sc_ev->symbol_config_mgr()->risk_limits_map_[5];
    risk_info_set->Clear();

    auto risk_info = std::make_shared<biz::RiskLimitCache>();
    risk_info->symbol_ = 5;
    risk_info->risk_id_ = 1;
    risk_info->max_leverage_e2_ = static_cast<biz::leverage_e2_t>(100 * 1e2);
    risk_info->initial_margin_rate_e4_ = 100;
    // 更新riskid=1的 mm rate, 50 -> 90
    risk_info->maintenance_margin_rate_e4_ = 90;
    risk_info->max_ord_pz_value_x_ = static_cast<biz::value_x_t>(100 * 1e6 * 1e8);
    risk_info->is_lowest_risk_ = true;

    risk_info_set->Add(risk_info);

    sc_ev->symbol_config_mgr()->risk_limits_has_changed_ = {5};
    te->pipeline()->ring_buffers(worker::WorkerType::kPreWorker)->Write(sc_ev, false);
  }

  // 成交形成200仓位，超过OB最大限制
  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "50", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "50", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "50", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "50", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "50", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "50", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  // user2挂单150个size，提供流动性给user1平仓
  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "50", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "50", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "50", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  // 设置封禁
  {
    InstBanOrUnban(uid1, true, false, false, false, false, false, false, false, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
  }

  // 机构借贷触发平仓
  {
    InstClosePz(uid1, false);
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "平仓Result:" << jsonStr2 << std::endl;
    }
  }
}
