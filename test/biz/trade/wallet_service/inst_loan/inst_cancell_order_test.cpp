//
// Created by SH00783ML on 2023/7/27.
//

#include <iostream>
#include <liquidation/liq_test.hpp>
#include <memory>
#include <string>
#include <thread>

#include "proto/gen/enums/einstactionstatus/inst_action_status.pb.h"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/wallet_service/inst_loan/inst_loan_test.hpp"

void CommonInstCancelAll(int64_t uid, std::shared_ptr<tmock::CTradeAppMock> te) {
  // 设置取消标记
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstCancelAll);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  // cancellAll取消后下发的marginResult
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "发送InstCancelAll结果:" << jsonStr2 << std::endl;
  ASSERT_EQ(result.m_msg->asset_margin_result().account_info().inst_status(), enums::einststatus::Canceling);
  ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstCancelAll);
  ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
            enums::einstactionstatus::Processing);

  int count = 0;
  while (true) {
    // 撮合回报回来的
    auto cacellMatchingResult = te->PopResult(1000);
    if (cacellMatchingResult.m_msg == nullptr) {
      break;
    }
    (void)google::protobuf::util::MessageToJsonString(*cacellMatchingResult.m_msg.get(), &jsonStr2);
    std::cout << "强平cancel结果:" << jsonStr2 << std::endl;
    // 有订单的 不设置值
    if (count == 0 || count == 1) {
      ASSERT_EQ(cacellMatchingResult.m_msg->asset_margin_result().inst_exec_result().inst_action(), 0);
      ASSERT_EQ(cacellMatchingResult.m_msg->asset_margin_result().inst_exec_result().inst_action_status(), 0);
      count++;
    } else {
      ASSERT_EQ(cacellMatchingResult.m_msg->asset_margin_result().inst_exec_result().inst_action(),
                EAction::InstCancelAll);
      ASSERT_EQ(cacellMatchingResult.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
                enums::einstactionstatus::Processed);
    }
  }
}

void SpotConditionalInstCancelAll(int64_t uid, std::shared_ptr<tmock::CTradeAppMock> te) {
  // 设置取消标记
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstCancelAll);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  // cancellAll取消后下发的marginResult
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "发送InstCancelAll结果:" << jsonStr2 << std::endl;
  ASSERT_EQ(result.m_msg->asset_margin_result().account_info().inst_status(), enums::einststatus::Normal);
  ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstCancelAll);
  ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
            enums::einstactionstatus::Processed);
}

// 逐仓模式
void InslInstCancelAll(int64_t uid, std::shared_ptr<tmock::CTradeAppMock> te) {
  // 设置取消标记
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstCancelAll);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  // cancellAll取消后下发的marginResult
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "发送InstCancelAll结果:" << jsonStr2 << std::endl;
  ASSERT_EQ(result.m_msg->asset_margin_result().account_info().inst_status(), enums::einststatus::Canceling);
  ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstCancelAll);
  ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
            enums::einstactionstatus::Processing);

  //  int count = 0;
  while (true) {
    // 撮合回报回来的
    auto cacellMatchingResult = te->PopResult(1000);
    if (cacellMatchingResult.m_msg == nullptr) {
      break;
    }
    (void)google::protobuf::util::MessageToJsonString(*cacellMatchingResult.m_msg.get(), &jsonStr2);
    std::cout << "强平cancel结果:" << jsonStr2 << std::endl;
    // 有订单的 不设置值
    //    if (count == 0) {
    //      ASSERT_EQ(cacellMatchingResult.m_msg->asset_margin_result().inst_exec_result().inst_action(), 0);
    //      ASSERT_EQ(cacellMatchingResult.m_msg->asset_margin_result().inst_exec_result().inst_action_status(), 0);
    //      count++;
    //    } else {
    ASSERT_EQ(cacellMatchingResult.m_msg->asset_margin_result().inst_exec_result().inst_action(),
              EAction::InstCancelAll);
    ASSERT_EQ(cacellMatchingResult.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processed);
    //    }
  }
}

void CancelError_Not_Set_Disable(int64_t uid, std::shared_ptr<tmock::CTradeAppMock> te) {
  {
    // 设置取消标记
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstCancelAll);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    // cancellAll取消后下发的marginResult
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "发送InstCancelAll结果:" << jsonStr2 << std::endl;
    ASSERT_EQ(result.m_msg->asset_margin_result().account_info().inst_status(), enums::einststatus::Normal);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
  }
}

void CancelError_In_Cancelling(int64_t uid, std::shared_ptr<tmock::CTradeAppMock> te) {
  {
    // 停撮合
    te->SuspendCross();
    // 设置取消标记
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstCancelAll);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    auto unified_v_2_request_dto1 = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto1->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto1->mutable_req_header()->set_action(EAction::InstCancelAll);
    auto event1 = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto1, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    te->ResumeCross();
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstCancelAll);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processing);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    ASSERT_EQ(result1.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstCancelAll);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processing);
  }
}

TEST_F(InstLoanTest, inst_cancel_all1) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  openAccount(uid1);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "5000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
    ASSERT_EQ(result.m_msg->ret_code(), 0);
  }

  {
    // 设置MarkPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    auto order_id = resp1->order_id();
    ASSERT_NE(order_id, "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1期货订单Result:" << jsonStr2 << std::endl;
  }

  {  // 创建期权订单
    auto order_link_id_1 = te->GenUUID();
    CreateOrderBuilder create_ao_builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "1", "2000");
    create_ao_builder.SetOrderLinkID(order_link_id_1);
    auto resp = user1.create_order(create_ao_builder.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期权订单:" << jsonStr2 << std::endl;
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(3));
  {
    // 创建现货订单
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.002", "3.01");
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    //    ASSERT_EQ(jsonStr2., "");
    std::cout << "uid1现货订单Result:" << jsonStr2 << std::endl;
    ASSERT_EQ(result.m_msg->ret_code(), 0);
  }

  {
    // 机构借贷交易封禁
    InstBanOrUnban(uid1, true, true, true, true, true, true, true, true, true);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);

    // 机构借贷取消订单
    CommonInstCancelAll(uid1, te);
  }
}

/**
 * 测试机构借贷撤单功能
 *
 * 测试场景：
 * 1. 验证含有 futureSpread 组合单时的机构借贷撤单逻辑
 *
 * 预期结果：
 * - 正确处理 futureSpread 组合单的撤单逻辑
 */
TEST_F(InstLoanTest, inst_cancel_all_future_spread) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  openAccount(uid1);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "5000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
    ASSERT_EQ(result.m_msg->ret_code(), 0);
  }

  {
    // 设置MarkPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    auto order_id = resp1->order_id();
    ASSERT_NE(order_id, "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1期货订单Result:" << jsonStr2 << std::endl;
  }

  {  // 创建期权订单
    auto order_link_id_1 = te->GenUUID();
    CreateOrderBuilder create_ao_builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "1", "2000");
    create_ao_builder.SetOrderLinkID(order_link_id_1);
    auto resp = user1.create_order(create_ao_builder.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期权订单:" << jsonStr2 << std::endl;
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(3));
  {
    // 创建现货订单
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.002", "3.01");
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    //    ASSERT_EQ(jsonStr2., "");
    std::cout << "uid1现货订单Result:" << jsonStr2 << std::endl;
    ASSERT_EQ(result.m_msg->ret_code(), 0);
  }

  ASSERT_TRUE(TestLiqHelper::Deposit(&user1, ECoin::BTC, "0.01"));

  // u1 挂 futureSpread 组合单
  {
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Buy", "Limit", "GTC", "100", "0.001");
    RpcContext ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  {
    // 机构借贷交易封禁
    InstBanOrUnban(uid1, true, true, true, true, true, true, true, true, true);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
  }

  // 机构借贷取消订单
  {
    // 设置取消标记
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user1.m_uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstCancelAll);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user1.m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    // cancelAll取消后下发的marginResult
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_EQ(result.m_msg->asset_margin_result().account_info().inst_status(), enums::einststatus::Canceling);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstCancelAll);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processing);

    {
      auto res1 = te->PopResult();
      ASSERT_NE(res1.m_msg.get(), nullptr);
      TestLiqHelper::PrintPb(res1.m_msg);

      auto res2 = te->PopResult();
      ASSERT_NE(res2.m_msg.get(), nullptr);
      TestLiqHelper::PrintPb(res2.m_msg);

      auto res3 = te->PopResult();
      ASSERT_NE(res3.m_msg.get(), nullptr);
      TestLiqHelper::PrintPb(res3.m_msg);

      auto res4 = te->PopResult();
      ASSERT_NE(res4.m_msg.get(), nullptr);
      TestLiqHelper::PrintPb(res4.m_msg);
      ASSERT_EQ(res4.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstCancelAll);
      ASSERT_EQ(res4.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
                enums::einstactionstatus::Processed);

      svc::unified_v2::UnifiedV2ResultDTO* comb_msg{};
      for (const auto& item : {res1, res2, res3, res4}) {
        if (item.m_msg->has_combination_margin_result() && item.m_msg->has_futures_margin_result() &&
            item.m_msg->has_spot_margin_result()) {
          comb_msg = item.m_msg.get();
        }
      }
      ASSERT_NE(comb_msg, nullptr);
      ASSERT_EQ(comb_msg->combination_margin_result().related_combination_orders_size(), 1);
      ASSERT_EQ(comb_msg->combination_margin_result().related_combination_orders().begin()->cancel_type(),
                ECancelType::CancelTypeCancelAllByInstLoan);

      auto res = te->PopResult();
      ASSERT_EQ(res.m_msg.get(), nullptr);
    }
  }
}

/**
 * 测试机构借贷撤单功能
 *
 * 测试场景：
 * 1. 验证含有 futureSpread 组合单时的机构借贷撤单逻辑
 *
 * 预期结果：
 * - 在成交中间状态下，撤单流程会卡在撤单状态
 */
TEST_F(InstLoanTest, inst_cancel_all_future_spread_FILL_IN_PROCESS) {
  auto u1 =
      TestLiqHelper::InitTestUser(te, 70001, EAccountMode::Portfolio, {{ECoin::USDT, "100000"}, {ECoin::BTC, "3"}});
  auto u2 =
      TestLiqHelper::InitTestUser(te, 70002, EAccountMode::Portfolio, {{ECoin::USDT, "100000"}, {ECoin::BTC, "3"}});

  {
    te->BatchAddMarkPrice({{ESymbol::BTCUSDT, 70000, 70000, 70000}, {ESymbol::BTCUSD, 70000, 70000, 70000}});
    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("70000"));
  }

  // u1 下期货单构造仓位 3 * 70000
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "3", "70000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp = u1->create_order(build.Build());
    ASSERT_NE(resp->order_id(), "");
    auto result = te->PopResult();
    ASSERT_TRUE(result.m_msg);
  }

  // u2 下期货单构造仓位 -3 * 70000
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "3", "70000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp = u2->create_order(build.Build());
    ASSERT_NE(resp->order_id(), "");

    // u2 taker成交
    auto result = te->PopResult();
    ASSERT_TRUE(result.m_msg);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1 maker 形成仓位
  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1创建期权订单用于构造仓位
  {
    auto order_link_id_1 = te->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id_1);
    auto resp = u1->create_order(builder.Build());
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u2创建期权订单并作为taker成交
  {
    auto order_link_id = te->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Sell", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id);
    auto resp = u2->create_order(builder.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1挂一个期权订单
  {
    auto order_link_id_1 = te->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id_1);
    auto resp = u1->create_order(builder.Build());
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1挂一个现货订单
  {
    auto order_link_id_1 = te->GenUUID();
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.1", "70000");
    build.SetOrderLinkID(order_link_id_1);
    RpcContext ct{};
    auto resp1 = u1->create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), 0);
    ASSERT_EQ(ct.RetMsg(), "OK");
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1 挂 futureSpread 组合单
  {
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Buy", "Limit", "GTC", "100", "3");
    RpcContext ct;
    auto m_resp = u1->comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  {
    // 设置单步模式
    te->SetCrossSingleStepMode();
  }

  {
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Sell", "Limit", "GTC", "100", "1");
    RpcContext ct;
    auto m_resp = u2->comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);
  }

  // 处理一个撮合消息，来自futureSpread组合单对应的撮合
  {
    // 单步处理
    te->ProcessOneXReq(te->GetCombCrossIdx());
  }

  {
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Sell", "Limit", "GTC", "3.10", "0.001");
    RpcContext ct;
    auto m_resp = u2->comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);
  }

  // 获取用户数据
  {
    auto resp = u1->DebugUserData();
    TestLiqHelper::PrintPb(resp);
  }

  {
    te->ProcessOneXReq(te->GetCombCrossIdx());

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    // 机构借贷交易封禁
    InstBanOrUnban(u1->m_uid, true, true, true, true, true, true, true, true, true);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
  }

  // 机构借贷取消订单
  {
    // 设置取消标记
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(u1->m_uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstCancelAll);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(u1->m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    // cancelAll取消后下发的marginResult
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeDefault);
    ASSERT_EQ(result.m_msg->ret_msg(), "cancel order fail.");
  }

  {
    te->UnSetCrossSingleStepMode();

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // 机构借贷取消订单 已经不是中间状态了，预期可以取消成功
  {
    // 设置取消标记
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(u1->m_uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstCancelAll);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(u1->m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    // cancelAll取消后下发的marginResult
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_EQ(result.m_msg->asset_margin_result().account_info().inst_status(), enums::einststatus::Canceling);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstCancelAll);
    ASSERT_EQ(result.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processing);
    {
      auto res1 = te->PopResult();
      ASSERT_NE(res1.m_msg.get(), nullptr);
      TestLiqHelper::PrintPb(res1.m_msg);

      auto res2 = te->PopResult();
      ASSERT_NE(res2.m_msg.get(), nullptr);
      TestLiqHelper::PrintPb(res2.m_msg);

      auto res3 = te->PopResult();
      ASSERT_NE(res3.m_msg.get(), nullptr);
      TestLiqHelper::PrintPb(res3.m_msg);
      ASSERT_EQ(res3.m_msg->asset_margin_result().inst_exec_result().inst_action(), EAction::InstCancelAll);
      ASSERT_EQ(res3.m_msg->asset_margin_result().inst_exec_result().inst_action_status(),
                enums::einstactionstatus::Processed);

      svc::unified_v2::UnifiedV2ResultDTO* comb_msg{};
      for (const auto& item : {res1, res2, res3}) {
        if (item.m_msg->has_combination_margin_result() && item.m_msg->has_futures_margin_result() &&
            item.m_msg->has_spot_margin_result()) {
          comb_msg = item.m_msg.get();
        }
      }
      ASSERT_NE(comb_msg, nullptr);
      ASSERT_EQ(comb_msg->combination_margin_result().related_combination_orders_size(), 1);
      ASSERT_EQ(comb_msg->combination_margin_result().related_combination_orders().begin()->cancel_type(),
                ECancelType::CancelTypeCancelAllByInstLoan);

      auto res = te->PopResult();
      ASSERT_EQ(res.m_msg.get(), nullptr);
    }
  }
}

// 现货条件单模式下的取消
TEST_F(InstLoanTest, inst_cancel_all2) {
  // UID1

  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  openAccount(uid1);

  {
    FutureDepositBuilder deposit_build("open_api_create_tpsl_order", uid1, "open_api_create_tpsl_order", 1, 1, "100",
                                       "0");
    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);

    // 创建现货条件单
    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetOrderFilter("tpslOrder");

    build.SetTriggerPrice("21000");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    // 设置MarkPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 机构借贷交易封禁
    InstBanOrUnban(uid1, true, true, true, true, true, true, true, true, true);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);

    // 机构借贷取消订单
    SpotConditionalInstCancelAll(uid1, te);
  }
}

// 逐仓模式
TEST_F(InstLoanTest, inst_cancel_all3) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  openAccount(uid1);

  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "5000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
    ASSERT_EQ(result.m_msg->ret_code(), 0);
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置MarkPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    auto order_id = resp1->order_id();
    ASSERT_NE(order_id, "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1期货订单Result:" << jsonStr2 << std::endl;
  }

  // 形成持仓

  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2期货订单Result:" << jsonStr2 << std::endl;

    auto pz_sell_order_result = te->PopResult();
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(3));
  {
    // 创建现货订单
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.002", "3.01");
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_link_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    //    ASSERT_EQ(jsonStr2., "");
    std::cout << "uid1现货订单Result:" << jsonStr2 << std::endl;
    ASSERT_EQ(result.m_msg->ret_code(), 0);
  }

  {
    // 逐仓模式
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_margin_mode(true);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切换逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 机构借贷交易封禁
    InstBanOrUnban(uid1, true, true, true, true, true, true, true, true, true);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
    // 机构借贷取消订单
    InslInstCancelAll(uid1, te);
  }
}

// 未设置封禁 不能取消
TEST_F(InstLoanTest, inst_cancel_all4) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  openAccount(uid1);

  {
    FutureDepositBuilder deposit_build("open_api_create_tpsl_order", uid1, "open_api_create_tpsl_order", 1, 1, "100",
                                       "0");
    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  {
    // 机构借贷取消订单
    CancelError_Not_Set_Disable(uid1, te);
  }
}

// 已经是cancelling状态 报错
TEST_F(InstLoanTest, inst_cancel_all5) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  openAccount(uid1);

  {
    FutureDepositBuilder deposit_build("open_api_create_tpsl_order", uid1, "open_api_create_tpsl_order", 5, 1, "10000",
                                       "0");
    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    auto order_id = resp1->order_id();
    ASSERT_NE(order_id, "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 封禁
    InstBanOrUnban(uid1, true, true, true, true, true, true, true, true, true);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeSuccess);
    // 机构借贷取消订单
    CancelError_In_Cancelling(uid1, te);
  }
}
