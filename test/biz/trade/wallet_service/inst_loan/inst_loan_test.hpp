#pragma once

#include "test/biz/trade/wallet_service/wallet_com_test.hpp"

class InstLoanTest : public WalletTestCom {
 public:
  void InstBanOrUnban(int64_t uid, bool all_trade_disabled, bool usdc_open_disabled, bool usdc_trade_disabled,
                      bool usdt_open_disabled, bool usdt_trade_disabled, bool option_open_disabled,
                      bool option_trade_disabled, bool spot_buy_disabled, bool spot_trade_disabled);
  void InstCancelAll(int64_t uid);
  void InstClosePz(int64_t uid, bool close_option_buy);
};
