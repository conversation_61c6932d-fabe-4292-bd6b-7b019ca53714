#include <string>

#include "biz_worker/service/trade/store/wallet/trans_log.hpp"
#include "src/data/type/biz_type.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/wallet_service/wallet_com_test.hpp"

/**
 * OTC闪兑-正常场景
 */
TEST_F(WalletTestCom, otc_balance_convert) {
  // UID1
  biz::user_id_t const uid1 = 100000;
  stub user1(te, uid1);

  openAccount(uid1);

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), static_cast<biz::coin_t>(5), 1, "1000", "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << '\n';
  }

  {
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();

    // req_header
    auto* req_header = unified_v_2_request_dto->mutable_req_header();
    req_header->set_req_id("balance_convert_1001_2");
    req_header->set_user_id(1001);
    req_header->set_action(EAction::BalanceConvert);

    // req_body
    auto* balance_convert_body = unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
    balance_convert_body->set_trans_id("dc2b4dcf-dc3e-4ab9-936f-130f2a6175ea");
    balance_convert_body->set_from_coin(ECoin::USDT);
    balance_convert_body->set_to_coin(ECoin::BTC);
    balance_convert_body->set_from_amount("300");
    balance_convert_body->set_to_amount("0.01");
    balance_convert_body->set_convert_type(1);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "OTC闪兑Result:" << jsonStr2 << '\n';

    auto wallets = result.m_msg.get()->asset_margin_result().affected_wallets();
    ASSERT_EQ(wallets.empty(), false);
    ASSERT_EQ(wallets.find(1)->second.has_wallet(), true);
    ASSERT_EQ(wallets.find(1)->second.related_wallet_records().empty(), false);
    auto in_trans_log = wallets.find(1)->second.related_wallet_records().Get(0);
    ASSERT_EQ(biz::BigDecimal(in_trans_log.change()), biz::BigDecimal("0.01"));
    ASSERT_EQ(biz::BigDecimal(in_trans_log.cash_balance()), biz::BigDecimal("0.01"));
    ASSERT_EQ(in_trans_log.trans_type(), store::TransLog::TransTypeEnum::kTransTypeOTCExchangeIn);

    ASSERT_EQ(wallets.find(5)->second.has_wallet(), true);
    ASSERT_EQ(wallets.find(5)->second.related_wallet_records().empty(), false);
    auto out_trans_log = wallets.find(5)->second.related_wallet_records().Get(0);
    ASSERT_EQ(biz::BigDecimal(out_trans_log.change()), biz::BigDecimal("-300"));
    ASSERT_EQ(biz::BigDecimal(out_trans_log.cash_balance()), biz::BigDecimal("700"));
    ASSERT_EQ(out_trans_log.trans_type(), store::TransLog::TransTypeEnum::kTransTypeOTCExchangeOut);
  }
}
