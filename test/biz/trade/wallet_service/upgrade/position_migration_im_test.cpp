//
// Created by SH00864ML on 2023/8/13.
//
#include <gtest/gtest.h>

#include <iostream>
#include <memory>
#include <string>

#include "google/protobuf/util/json_util.h"
#include "src/data/type/biz_type.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/wallet_service/custome_collateral_coin/custome_collateral_coin.hpp"
#include "test/biz/trade/wallet_service/wallet_com_test.hpp"
#include "test/biz/wallet_service_ut/process_trading_result/trans_log_utils.hpp"
#include "test/biz/wallet_service_ut/wallet_test.hpp"

/**
 * 逐仓迁移
 */
TEST_F(WalletTestCom, PositionMigrationServiceForIm) {
  // UID1
  biz::user_id_t const uid1 = 100000;
  //  biz::account_id_t account_id = 4509274;
  //  int32_t settle_coin = ECoin::USDC;
  stub user1(te, uid1);

  openAccount(uid1);

  // 切换Im模式
  switchIm(uid1);

  {
    FutureDepositBuilder deposit_build(te->GenUUID(), uid1, te->GenUUID(), static_cast<biz::coin_t>(5), 1, "20000",
                                       "0");
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << '\n';
  }

  {
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::PositionMigration);
    auto future_positions = unified_v_2_request_dto->mutable_positionreqgroupbody()
                                ->mutable_position_migration()
                                ->mutable_futurepositions();

    // future pz
    auto future_pz = future_positions->Add();
    future_pz->set_value_scale(8);
    future_pz->set_price_scale(4);
    future_pz->set_qty_scale(8);
    future_pz->set_position_idx(0);
    future_pz->set_user_id(uid1);
    future_pz->set_risk_id(1);
    future_pz->set_leverage_e2(1000);
    future_pz->set_side(ESide::Sell);
    future_pz->set_size_x(100000000);
    future_pz->set_value_e8(3000100000000);
    future_pz->set_entry_price_e8(3000100000000);
    future_pz->set_symbol(ESymbol::BTCUSDT);
    future_pz->set_contract_type(static_cast<int32_t>(EContractType::LinearPerpetual));
    future_pz->set_coin(static_cast<ECoin>(ECoin::USDT));
    future_pz->set_session_value_e8(3000100000000);
    future_pz->set_symbol_name("BTCUSDT");
    future_pz->set_total_balance_change("-169.06470000");
    future_pz->set_total_trading_fee("-7.18470000");
    future_pz->set_session_value_e8(3000100000000);
    future_pz->set_position_status(EPositionStatus::Normal);
    future_pz->set_cross_seq(100);
    future_pz->set_liq_price_x(200);
    future_pz->set_bust_price_x(200);
    future_pz->set_is_isolated(200);
    future_pz->set_is_auto_add_margin(true);
    future_pz->set_position_balance_e8(100000000000);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    ASSERT_EQ(result.m_msg.get()->ret_code(), 0);
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "迁移仓位Result:" << jsonStr2 << '\n';
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 40000, 40000, 40000);
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    te->ResumeCross();
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "强平结果:" << jsonStr2 << std::endl;
    }
  }
}
