//
// Created by SH00856ML on 2023/8/10.
//
#include <memory>

#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/liquidation/liquidation_test.hpp"
#include "test/biz/trade/wallet_service/wallet_service_test.h"

void mockInverseFuturePz(::models::tradingdto::PositionDTO* pz_dto, biz::user_id_t uid) {
  pz_dto->set_user_id(uid);
  pz_dto->set_value_scale(8);
  pz_dto->set_price_scale(4);
  pz_dto->set_qty_scale(0);
  pz_dto->set_position_idx(EPositionIndex::Single);
  pz_dto->set_user_id(uid);
  pz_dto->set_risk_id(1);
  pz_dto->set_leverage_e2(1000);
  pz_dto->set_side(ESide::Buy);
  // size 个数
  pz_dto->set_size_x(*********);
  // value BTC计价
  pz_dto->set_value_e8(************);
  pz_dto->set_session_value_e8(************);
  pz_dto->set_entry_price_e8(*************);
  pz_dto->set_symbol(ESymbol::BTCUSD);
  pz_dto->set_contract_type(EContractType::InversePerpetual);
  pz_dto->set_coin(ECoin::BTC);
  pz_dto->set_symbol_name("BTCUSD");
  pz_dto->set_position_status(EPositionStatus::Normal);
  pz_dto->set_position_balance_e8(*********);
}

// 空参数，请求会成功
TEST_F(WalletServcieIntegrationTest, uta_account_upgrade_test_trail0) {
  {
    // empty request with only header information
    biz::user_id_t const uid = 20001;
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::UtaExistsAccountUpgradeTrial);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();

    ASSERT_EQ(result.m_msg->ret_code(), 0);
  }
}

// 正常入参，CROSS模式预期通过，imr大于80
TEST_F(WalletServcieIntegrationTest, uta_account_upgrade_test_trail1) {
  {
    biz::user_id_t const uid = 20002;
    stub user1(te, uid);
    openAccount(uid);

    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::UtaExistsAccountUpgradeTrial);
    auto trail_req = unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_utaaccountupgradetrailreq();
    trail_req->set_accountmode(EAccountMode::Cross);
    auto wallets = trail_req->mutable_wallet_list();

    // 构造钱包
    auto usdt_wallet = ::models::walletdto::UnifyWallet();
    usdt_wallet.set_settle_coin(5);
    usdt_wallet.set_cash_balance("100.00");
    wallets->Add(std::move(usdt_wallet));

    auto btc_wallet = ::models::walletdto::UnifyWallet();
    btc_wallet.set_settle_coin(1);
    btc_wallet.set_cash_balance("480");
    wallets->Add(std::move(btc_wallet));

    te->AddMarkPrice(ESymbol::BTCUSD, 20000, 20000, 20000);

    // 构造setting
    auto future_setting_req = trail_req->mutable_future_per_coin_setting();
    ::models::usersettingdto::PerCoinUserSettingDTO src;
    src.set_user_id(uid);
    src.set_coin(ECoin::BTC);
    src.set_max_leverage_e2(1000);
    src.set_taker_fee_rate_e8(500);
    src.set_maker_fee_rate_e8(250);
    src.set_taker_fee_rate_desc("Taker fee");
    src.set_maker_fee_rate_desc("Maker fee");
    src.set_is_unified_margin(false);
    src.add_symbol_list(ESymbol::BTCUSDT);
    src.set_setting_version(1);
    src.set_created_at_e9(123456789);
    src.set_updated_at_e9(987654321);
    src.set_default_position_mode(EPositionMode::BothSide);
    future_setting_req->insert({1, src});

    // 构造仓位
    auto pz_dto = ::models::tradingdto::PositionDTO();
    mockInverseFuturePz(&pz_dto, uid);
    trail_req->mutable_future_positions()->Add(std::move(pz_dto));

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();

    // 虽然本action是强制回滚，但是返回码也是0
    ASSERT_EQ(result.m_msg->ret_code(), 0);
    ASSERT_EQ(result.m_msg->mutable_asset_margin_result()->account_trial_im_rate_e2(), 110);
  }
}

// 正常入参，CROSS模式预期通过，imr小于80
TEST_F(WalletServcieIntegrationTest, uta_account_upgrade_test_trail2) {
  {
    biz::user_id_t const uid = 20002;
    stub user1(te, uid);
    openAccount(uid);

    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::UtaExistsAccountUpgradeTrial);
    auto trail_req = unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_utaaccountupgradetrailreq();
    trail_req->set_accountmode(EAccountMode::Cross);
    auto wallets = trail_req->mutable_wallet_list();

    // 构造钱包
    auto usdt_wallet = ::models::walletdto::UnifyWallet();
    usdt_wallet.set_settle_coin(5);
    usdt_wallet.set_cash_balance("100.00");
    wallets->Add(std::move(usdt_wallet));

    auto btc_wallet = ::models::walletdto::UnifyWallet();
    btc_wallet.set_settle_coin(1);
    btc_wallet.set_cash_balance("700");
    wallets->Add(std::move(btc_wallet));

    te->AddMarkPrice(ESymbol::BTCUSD, 20000, 20000, 20000);

    // 构造setting
    auto future_setting_req = trail_req->mutable_future_per_coin_setting();
    ::models::usersettingdto::PerCoinUserSettingDTO src;
    src.set_user_id(uid);
    src.set_coin(ECoin::BTC);
    src.set_max_leverage_e2(1000);
    src.set_taker_fee_rate_e8(500);
    src.set_maker_fee_rate_e8(250);
    src.set_taker_fee_rate_desc("Taker fee");
    src.set_maker_fee_rate_desc("Maker fee");
    src.set_is_unified_margin(false);
    src.add_symbol_list(ESymbol::BTCUSDT);
    src.set_setting_version(1);
    src.set_created_at_e9(123456789);
    src.set_updated_at_e9(987654321);
    src.set_default_position_mode(EPositionMode::BothSide);
    future_setting_req->insert({1, src});

    // 构造仓位
    auto pz_dto = ::models::tradingdto::PositionDTO();
    mockInverseFuturePz(&pz_dto, uid);
    trail_req->mutable_future_positions()->Add(std::move(pz_dto));

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();

    // 虽然本action是强制回滚，但是返回码也是0
    ASSERT_EQ(result.m_msg->ret_code(), 0);
    ASSERT_EQ(result.m_msg->mutable_asset_margin_result()->account_trial_im_rate_e2(), 75);
  }
}
