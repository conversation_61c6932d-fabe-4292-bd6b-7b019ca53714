#include "test/biz/trade/utils/common_utils_test.hpp"

#include "biz_worker/utils/common_utils.hpp"
#include "data/type/biz_type.hpp"
#include "proto/gen/models/usersettingdto/per_coin_user_setting_dto.pb.h"
#include "src/application/global_var_manager.hpp"
#include "src/biz_worker/service/trade/store/per_symbol_store.hpp"
#include "src/biz_worker/service/trade/store/setting/raw_setting.hpp"
#include "src/biz_worker/utils/pb_convertor/from_pb.hpp"
#include "src/common/bgrpc/context.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

std::shared_ptr<tmock::CTradeAppMock> TestCommonUtils::te_{};

void TestCommonUtils::SetUpTestSuite() {
  te_ = std::make_shared<tmock::CTradeAppMock>();
  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te_);

  te_->Init(argument_count, argument_arrary);
  te_->Start();
}

void TestCommonUtils::TearDownTestSuite() {
  te_->Stop(true);
  bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
  te_.reset();
}

struct RVOClass {
  RVOClass() { ++x; }
  RVOClass(const RVOClass&) { ++y; }
  ~RVOClass() { ++z; }

  std::string str;
  int x{0};
  int y{0};
  int z{0};
};

const RVOClass UTPbMessageToJsonString(const google::protobuf::Message& message) {
  RVOClass msg;
  (void)google::protobuf::util::MessageToJsonString(message, &msg.str);
  return msg;
}

TEST_F(TestCommonUtils, T1) {
  ::svc::unified_v2::UnifiedV2RequestDTO x;
  const auto& res = UTPbMessageToJsonString(x);
  ASSERT_EQ(res.x, 1);
  ASSERT_EQ(res.y, 0);
  ASSERT_EQ(res.z, 0);
}

TEST_F(TestCommonUtils, ToProductType) {
  EXPECT_EQ(biz::CommonUtils::ToProductType(EContractType::LinearFutures), EProductType::Futures);
  EXPECT_EQ(biz::CommonUtils::ToProductType(EContractType::LinearPerpetual), EProductType::Futures);
  EXPECT_EQ(biz::CommonUtils::ToProductType(EContractType::InverseFutures), EProductType::Futures);
  EXPECT_EQ(biz::CommonUtils::ToProductType(EContractType::InversePerpetual), EProductType::Futures);
  EXPECT_EQ(biz::CommonUtils::ToProductType(EContractType::Option), EProductType::Options);
  EXPECT_EQ(biz::CommonUtils::ToProductType(EContractType::Spot), EProductType::Spot);
  EXPECT_EQ(biz::CommonUtils::ToProductType(static_cast<EContractType>(100)), EProductType::UNKNOWN);
}

TEST_F(TestCommonUtils, ToEventBizType) {
  std::vector<svc::trading::TradingReq::ReqBodyCase> req_body_case_vec{
      svc::trading::TradingReq::ReqBodyCase::kPreCreateOrder,
      svc::trading::TradingReq::ReqBodyCase::kCreateOrder,
      svc::trading::TradingReq::ReqBodyCase::kCancelOrder,
      svc::trading::TradingReq::ReqBodyCase::kReplaceOrder,
      svc::trading::TradingReq::ReqBodyCase::kCancelAll,
      svc::trading::TradingReq::ReqBodyCase::kCancelAllOrderByCoin,
      svc::trading::TradingReq::ReqBodyCase::kTriggeredToActive,
      svc::trading::TradingReq::ReqBodyCase::kLiqCleanup,
      svc::trading::TradingReq::ReqBodyCase::kLiqExecute,
      svc::trading::TradingReq::ReqBodyCase::kAdlCleanup,
      svc::trading::TradingReq::ReqBodyCase::kAdlExecute,
      svc::trading::TradingReq::ReqBodyCase::kEvictHistoryOrder,
      svc::trading::TradingReq::ReqBodyCase::kDeposit,
      svc::trading::TradingReq::ReqBodyCase::kWithdraw,
      svc::trading::TradingReq::ReqBodyCase::kSetLeverage,
      svc::trading::TradingReq::ReqBodyCase::kSetMargin,
      svc::trading::TradingReq::ReqBodyCase::kAddMargin,
      svc::trading::TradingReq::ReqBodyCase::kSetRiskId,
      svc::trading::TradingReq::ReqBodyCase::kSetAutoAddMargin,
      svc::trading::TradingReq::ReqBodyCase::kSetFeeRate,
      svc::trading::TradingReq::ReqBodyCase::kUpdateAdlRankIndicator,
      svc::trading::TradingReq::ReqBodyCase::kSettleFundingFee,
      svc::trading::TradingReq::ReqBodyCase::kSetTpSlTs,
      svc::trading::TradingReq::ReqBodyCase::kSwitchIsolated,
      svc::trading::TradingReq::ReqBodyCase::kSetOpenLimit,
      svc::trading::TradingReq::ReqBodyCase::kForceAddPosition,
      svc::trading::TradingReq::ReqBodyCase::kForceSubPosition,
      svc::trading::TradingReq::ReqBodyCase::kSwitchPositionMode,
      svc::trading::TradingReq::ReqBodyCase::kSwitchPositionModeWithCoin,
      svc::trading::TradingReq::ReqBodyCase::kForceSwitchPositionMode,
      svc::trading::TradingReq::ReqBodyCase::kSelfFill,
      svc::trading::TradingReq::ReqBodyCase::kSettle,
      svc::trading::TradingReq::ReqBodyCase::kBlockTrade,
      svc::trading::TradingReq::ReqBodyCase::kCheckOrdersDetail,
      svc::trading::TradingReq::ReqBodyCase::kCloseAllPosition,
      svc::trading::TradingReq::ReqBodyCase::kSetLeverageWithSymbols,
      svc::trading::TradingReq::ReqBodyCase::kSwitchIsolatedWithSymbols,
      svc::trading::TradingReq::ReqBodyCase::kSwitchPositionModeWithSymbols,
      svc::trading::TradingReq::ReqBodyCase::kCrossReq,
      svc::trading::TradingReq::ReqBodyCase::kOnRecvMatchingResult,
      svc::trading::TradingReq::ReqBodyCase::kOnMarkPriceUpdate,
      svc::trading::TradingReq::ReqBodyCase::kOnRecvTradingResult,
      svc::trading::TradingReq::ReqBodyCase::kQueryAsset,
      svc::trading::TradingReq::ReqBodyCase::kQueryPosition,
      svc::trading::TradingReq::ReqBodyCase::kQueryOrder,
      svc::trading::TradingReq::ReqBodyCase::kQueryActiveFutureSymbol,
      svc::trading::TradingReq::ReqBodyCase::kQueryReCalcParams,
      svc::trading::TradingReq::ReqBodyCase::kQueryWalletOp,
      svc::trading::TradingReq::ReqBodyCase::kUpdateSubscription,
      svc::trading::TradingReq::ReqBodyCase::kLoadUserData,
      svc::trading::TradingReq::ReqBodyCase::kRecoverPendingOrder,
      svc::trading::TradingReq::ReqBodyCase::kOnSymbolConfigUpdate,
      svc::trading::TradingReq::ReqBodyCase::kSwitchTpSlMode,
      svc::trading::TradingReq::ReqBodyCase::kQueryStopOrderList,
      svc::trading::TradingReq::ReqBodyCase::kCheckInactiveUser,
      svc::trading::TradingReq::ReqBodyCase::kUnloadUserData,
      svc::trading::TradingReq::ReqBodyCase::kSetCustomerFeeRate,
      svc::trading::TradingReq::ReqBodyCase::kSetCustomerDealerUserId,
      svc::trading::TradingReq::ReqBodyCase::kSetCustomerSymbolList,
      svc::trading::TradingReq::ReqBodyCase::kSetCustomerUnifiedMargin,
      svc::trading::TradingReq::ReqBodyCase::kSetCustomerSymbolMaxLeverage,
      svc::trading::TradingReq::ReqBodyCase::kQueryUserSetting,
      svc::trading::TradingReq::ReqBodyCase::kQuerySymbolContents,
      svc::trading::TradingReq::ReqBodyCase::kOnRecvMarginResult,
      svc::trading::TradingReq::ReqBodyCase::kOnRecvUnifiedMarginResult,
      svc::trading::TradingReq::ReqBodyCase::kSyncUserDataToPm,
      svc::trading::TradingReq::ReqBodyCase::kLiqSetStatus,
      svc::trading::TradingReq::ReqBodyCase::kLiqSetRiskid,
      svc::trading::TradingReq::ReqBodyCase::kLiqBatchSetRiskid,
      svc::trading::TradingReq::ReqBodyCase::kLiqCancelOrd,
      svc::trading::TradingReq::ReqBodyCase::kLiqSubPosition,
      svc::trading::TradingReq::ReqBodyCase::kLiqBatchSubPosition,
      svc::trading::TradingReq::ReqBodyCase::kLiqTakenOver,
      svc::trading::TradingReq::ReqBodyCase::kLiqBatchTakenOver,
      svc::trading::TradingReq::ReqBodyCase::kManualTakenOver,
      svc::trading::TradingReq::ReqBodyCase::kOnRiskLimitUpdate,
      svc::trading::TradingReq::ReqBodyCase::kHealthCheck,
      svc::trading::TradingReq::ReqBodyCase::kSetMt4Account,
      svc::trading::TradingReq::ReqBodyCase::kUpdateWsUserStatus,
      svc::trading::TradingReq::ReqBodyCase::kSetSingleFeeRate,
      svc::trading::TradingReq::ReqBodyCase::kRevokeSingleFeeRate,
      svc::trading::TradingReq::ReqBodyCase::kRevokeCustomerFeeRate,
      svc::trading::TradingReq::ReqBodyCase::kOnTriggerRebalance,
      svc::trading::TradingReq::ReqBodyCase::kQueryUserSymbolData,
      svc::trading::TradingReq::ReqBodyCase::kOnSetTradingResult,
      svc::trading::TradingReq::ReqBodyCase::kOnLiqCoinExecute,
      svc::trading::TradingReq::ReqBodyCase::kPreCheckOrders,
      svc::trading::TradingReq::ReqBodyCase::kSetRiskLimitConfigType,
      svc::trading::TradingReq::ReqBodyCase::kTransferPosition,
      svc::trading::TradingReq::ReqBodyCase::kDealingDesk,
      svc::trading::TradingReq::ReqBodyCase::kSetDealingDeskShardConfig,
      svc::trading::TradingReq::ReqBodyCase::kCreateOrderWithLeverage,
      svc::trading::TradingReq::ReqBodyCase::kSetCopyTradeAccount,
      svc::trading::TradingReq::ReqBodyCase::kMarginCancelAll,
      svc::trading::TradingReq::ReqBodyCase::kMarginCancelOrder,
      svc::trading::TradingReq::ReqBodyCase::kLiqRequest,
      svc::trading::TradingReq::ReqBodyCase::kInnerCommonCancelRequest,
      svc::trading::TradingReq::ReqBodyCase::kInnerBatchCancelRequest,
      svc::trading::TradingReq::ReqBodyCase::kInnerBatchOrderRequest,
      svc::trading::TradingReq::ReqBodyCase::kInnerMmpInactiveRequest,
      svc::trading::TradingReq::ReqBodyCase::kSiteOrderRequest,
      svc::trading::TradingReq::ReqBodyCase::kSiteBatchOrderRequest,
      svc::trading::TradingReq::ReqBodyCase::kSiteReplaceOrderRequest,
      svc::trading::TradingReq::ReqBodyCase::kSiteCancelOrderRequest,
      svc::trading::TradingReq::ReqBodyCase::kSiteBatchCancelOrderRequest,
      svc::trading::TradingReq::ReqBodyCase::kSiteCancelAllRequest,
      svc::trading::TradingReq::ReqBodyCase::kBlockOrderRequest,
      svc::trading::TradingReq::ReqBodyCase::kBlockTradeOrderRequest,
      svc::trading::TradingReq::ReqBodyCase::kMmpQueryUserMmpRequest,
      svc::trading::TradingReq::ReqBodyCase::REQ_BODY_NOT_SET};
  std::vector<event::EventBizType> event_biz_type_vec{event::EventBizType::kPreCreateOrder,
                                                      event::EventBizType::kCreateOrder,
                                                      event::EventBizType::kCancelOrder,
                                                      event::EventBizType::kReplaceOrder,
                                                      event::EventBizType::kCancelAll,
                                                      event::EventBizType::kCancelAllOrderByCoin,
                                                      event::EventBizType::kTriggeredToActive,
                                                      event::EventBizType::kLiqCleanup,
                                                      event::EventBizType::kLiqExecute,
                                                      event::EventBizType::kAdlCleanup,
                                                      event::EventBizType::kAdlExecute,
                                                      event::EventBizType::kEvictHistoryOrder,
                                                      event::EventBizType::kDeposit,
                                                      event::EventBizType::kWithdraw,
                                                      event::EventBizType::kSetLeverage,
                                                      event::EventBizType::kSetMargin,
                                                      event::EventBizType::kAddMargin,
                                                      event::EventBizType::kSetRiskId,
                                                      event::EventBizType::kSetAutoAddMargin,
                                                      event::EventBizType::kSetFeeRate,
                                                      event::EventBizType::kUpdateAdlRankIndicator,
                                                      event::EventBizType::kSettleFundingFee,
                                                      event::EventBizType::kSetTpSlTs,
                                                      event::EventBizType::kSwitchIsolated,
                                                      event::EventBizType::kSetOpenLimit,
                                                      event::EventBizType::kForceAddPosition,
                                                      event::EventBizType::kForceSubPosition,
                                                      event::EventBizType::kSwitchPositionMode,
                                                      event::EventBizType::kSwitchPositionModeWithCoin,
                                                      event::EventBizType::kForceSwitchPositionMode,
                                                      event::EventBizType::kSelfFill,
                                                      event::EventBizType::kSettle,
                                                      event::EventBizType::kBlockTrade,
                                                      event::EventBizType::kCheckOrdersDetail,
                                                      event::EventBizType::kCloseAllPosition,
                                                      event::EventBizType::kSetLeverageWithSymbols,
                                                      event::EventBizType::kSwitchIsolatedWithSymbols,
                                                      event::EventBizType::kSwitchPositionModeWithSymbols,
                                                      event::EventBizType::kCrossReq,
                                                      event::EventBizType::kOnRecvMatchingResult,
                                                      event::EventBizType::kOnMarkPriceUpdate,
                                                      event::EventBizType::kOnRecvTradingResult,
                                                      event::EventBizType::kQueryAsset,
                                                      event::EventBizType::kQueryPosition,
                                                      event::EventBizType::kQueryOrder,
                                                      event::EventBizType::kQueryActiveFutureSymbol,
                                                      event::EventBizType::kQueryReCalcParams,
                                                      event::EventBizType::kQueryWalletOp,
                                                      event::EventBizType::kUpdateSubscription,
                                                      event::EventBizType::kLoadUserData,
                                                      event::EventBizType::kRecoverPendingOrder,
                                                      event::EventBizType::kOnSymbolConfigUpdate,
                                                      event::EventBizType::kSwitchTpSlMode,
                                                      event::EventBizType::kQueryStopOrderList,
                                                      event::EventBizType::kCheckInactiveUser,
                                                      event::EventBizType::kUnloadUserData,
                                                      event::EventBizType::kSetCustomerFeeRate,
                                                      event::EventBizType::kSetCustomerDealerUserId,
                                                      event::EventBizType::kSetCustomerSymbolList,
                                                      event::EventBizType::kSetCustomerUnifiedMargin,
                                                      event::EventBizType::kSetCustomerSymbolMaxLeverage,
                                                      event::EventBizType::kQueryUserSetting,
                                                      event::EventBizType::kQuerySymbolContents,
                                                      event::EventBizType::kOnRecvMarginResult,
                                                      event::EventBizType::kOnRecvUnifiedMarginResult,
                                                      event::EventBizType::kSyncUserDataToPm,
                                                      event::EventBizType::kLiqSetStatus,
                                                      event::EventBizType::kLiqSetRiskid,
                                                      event::EventBizType::kLiqBatchSetRiskid,
                                                      event::EventBizType::kLiqCancelOrd,
                                                      event::EventBizType::kLiqSubPosition,
                                                      event::EventBizType::kLiqBatchSubPosition,
                                                      event::EventBizType::kLiqTakenOver,
                                                      event::EventBizType::kLiqBatchTakenOver,
                                                      event::EventBizType::kManualTakenOver,
                                                      event::EventBizType::kOnRiskLimitUpdate,
                                                      event::EventBizType::kHealthCheck,
                                                      event::EventBizType::kSetMt4Account,
                                                      event::EventBizType::kUpdateWsUserStatus,
                                                      event::EventBizType::kSetSingleFeeRate,
                                                      event::EventBizType::kRevokeSingleFeeRate,
                                                      event::EventBizType::kRevokeCustomerFeeRate,
                                                      event::EventBizType::kOnTriggerRebalance,
                                                      event::EventBizType::kQueryUserSymbolData,
                                                      event::EventBizType::kOnSetTradingResult,
                                                      event::EventBizType::kOnLiqCoinExecute,
                                                      event::EventBizType::kPreCheckOrders,
                                                      event::EventBizType::kSetRiskLimitConfigType,
                                                      event::EventBizType::kTransferPosition,
                                                      event::EventBizType::kDealingDesk,
                                                      event::EventBizType::kSetDealingDeskShardConfig,
                                                      event::EventBizType::kCreateOrderWithLeverage,
                                                      event::EventBizType::kSetCopyTradeAccount,
                                                      event::EventBizType::kMarginCancelAll,
                                                      event::EventBizType::kMarginCancelOrder,
                                                      event::EventBizType::kLiqRequest,
                                                      event::EventBizType::kInnerCommonCancelRequest,
                                                      event::EventBizType::kInnerBatchCancelRequest,
                                                      event::EventBizType::kInnerBatchOrderRequest,
                                                      event::EventBizType::kInnerMmpInactiveRequest,
                                                      event::EventBizType::kSiteOrderRequest,
                                                      event::EventBizType::kSiteBatchOrderRequest,
                                                      event::EventBizType::kSiteReplaceOrderRequest,
                                                      event::EventBizType::kSiteCancelOrderRequest,
                                                      event::EventBizType::kSiteBatchCancelOrderRequest,
                                                      event::EventBizType::kSiteCancelAllRequest,
                                                      event::EventBizType::kBlockOrderRequest,
                                                      event::EventBizType::kBlockTradeOrderRequest,
                                                      event::EventBizType::kMmpQueryUserMmpRequest,
                                                      event::EventBizType::kUnknown};
  ASSERT_EQ(req_body_case_vec.size(), event_biz_type_vec.size());
  for (size_t i = 0; i < req_body_case_vec.size(); ++i) {
    std::cout << i << std::endl;
    ASSERT_EQ(biz::CommonUtils::ToEventBizType(req_body_case_vec[i]), event_biz_type_vec[i]);
  }
}

TEST_F(TestCommonUtils, ToHeader) {
  {
    config::ConfigManager config_manager;
    ::svc::unified_v2::UnifiedV2RequestDTO src;
    store::Header* des{nullptr};
    biz::CommonUtils::ToHeader(&config_manager, &src, des);
  }

  {
    config::ConfigManager config_manager;
    ::svc::unified_v2::UnifiedV2RequestDTO src;
    store::Header des{};
    biz::CommonUtils::ToHeader(&config_manager, &src, &des);
  }
  ASSERT_EQ(1, 1);
}

TEST_F(TestCommonUtils, GenDefaultBizResultEvent) {
  config::ConfigManager config_manager;
  auto header = std::make_shared<store::Header>();
  error::ErrorCode err{error::ErrorCode::kErrorCodeNone};
  std::string msg;

  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::DebugRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(
        event::GrpcSoureType::kGrpcSourceFuture, event::GrpcUriType::kGrpcDebugProcess, grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::req::CreateOrderReqV5>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOpenApiCreateOrderV5, grpc_context, 1,
                                                      grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::req::CancelOrderReqV5>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOpenApiCancelOrderV5, grpc_context, 1,
                                                      grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::req::ReplaceOrderReqV5>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOpenApiReplaceOrderV5, grpc_context, 1,
                                                      grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::req::SetRiskIdReqV5>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOpenApiSetRiskIdV5, grpc_context, 1,
                                                      grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::req::SetAutoAddMarginRequestV5>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOpenApiSetAutoAddMarginV5, grpc_context,
                                                      1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::req::AddMarginRequestV5>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOpenApiAddMarginV5, grpc_context, 1,
                                                      grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::req::SwitchPositionModeRequestV5>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOpenApiSwitchPositionModeV5,
                                                      grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::req::SetLeverageReqV5>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOpenApiSetLeverageV5, grpc_context, 1,
                                                      grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::req::BatchCreateOrderReqV5>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOpenApiBatchCreateOrderV5, grpc_context,
                                                      1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::req::BatchReplaceOrderReqV5>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOpenApiBatchReplaceOrderV5, grpc_context,
                                                      1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::req::BatchCancelOrderReqV5>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOpenApiBatchCancelOrderV5, grpc_context,
                                                      1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::option::SiteBatchOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOptionSiteApiSiteBatchPlaceOrder,
                                                      grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::option::SiteBatchOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOptionSiteApiSiteBatchCancelOrder,
                                                      grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::option::SiteOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOptionSiteApiSitePlaceOrder,
                                                      grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::option::SiteReplaceOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOptionSiteApiSiteReplaceOrder,
                                                      grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::option::SiteCancelOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOptionSiteApiSiteCancelOrder,
                                                      grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::option::SiteCancelOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOptionInnerBatchCreateOrders,
                                                      grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::req::CancelAllOrderReqV5>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOpenApiCancelAllOrderV5, grpc_context, 1,
                                                      grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::option::SiteCancelAllRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcOptionSiteApiSiteCancelAllOrders,
                                                      grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::spot::req::ClientCreateOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcSpotSiteApiCreateOrder, grpc_context, 1,
                                                      grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::spot::req::CrossCreateOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcSpotSiteApiCrossCreateOrder,
                                                      grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::spot::req::PlanSpotCreateOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcSpotSiteApiPlanSpotCreate, grpc_context,
                                                      1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::spot::req::ClientCancelOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcSpotSiteApiCancelOrder, grpc_context, 1,
                                                      grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::spot::req::CancelPlanOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcSpotSiteApiCancelPlanOrder, grpc_context,
                                                      1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::spot::req::MergeCancelOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcSpotSiteApiMergeCancelOrder,
                                                      grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::spot::req::MergeBatchCancelOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcSpotSiteApiMergeBatchCancelOrder,
                                                      grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::spot::req::BatchCancelPlanOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcSpotSiteApiBatchCancelPlanOrder,
                                                      grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::spot::req::ClientBatchCancelOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcSpotSiteApiBatchCancelOrder,
                                                      grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::spot::req::CancelOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcSpotCancelOrder, grpc_context, 1,
                                                      grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::spot::req::CancelTriggerOrderRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcSpotCancelPlanSpotOrder, grpc_context, 1,
                                                      grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::spot::req::BatchCancelOrderByIdsRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcSpotBatchCancelOrderByIds, grpc_context,
                                                      1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::spot::req::BatchCancelTriggerOrderByIdsRequest>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcSpotBatchCancelTriggerOrderByIds,
                                                      grpc_context, 1, grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::block_trade::req::PreOccupyReq>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcBlockTradePreOccupy, grpc_context, 1,
                                                      grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::block_trade::req::CreateBlockTradeReq>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcBlockTradeCreate, grpc_context, 1,
                                                      grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
  {
    auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
    auto grpc_request = std::make_shared<svc::uta_engine::block_trade::req::QueryOrderReq>();
    auto ev = std::make_shared<event::GrpcInputEvent>(event::GrpcSoureType::kGrpcSourceFuture,
                                                      event::GrpcUriType::kGrpcBlockTradeQueryOrder, grpc_context, 1,
                                                      grpc_request);

    auto biz_result_ev = biz::CommonUtils::GenDefaultBizResultEvent(&config_manager, header, ev, err, msg);
    ASSERT_NE(biz_result_ev, nullptr);
  }
}

TEST_F(TestCommonUtils, bgrpc_add_headers) {
  auto grpc_context = std::make_shared<bbase::bgrpc::Context<grpc::ServerContext>>(0, "", nullptr);
  bgrpc::add_initial_header(grpc_context, 0, "aaa", "bbb");

  std::string value{"aabbbbbβγδbbbbbeeeeeeβδ？ee"};
  ASSERT_STREQ(value.c_str(), "aabbbbbβγδbbbbbeeeeeeβδ？ee");
  bgrpc::revise_ascii_value(value);
  ASSERT_STREQ(value.c_str(), "aabbbbb??????bbbbbeeeeee???????ee");
}

TEST_F(TestCommonUtils, verify_auth) {
  application::GlobalVarManager::Instance().set_service_name(
      fmt::format("{}.{}", application::GlobalVarManager::Instance().service_name(), "g0p0"));
  std::string to_verify_digest = "";
  ASSERT_FALSE(biz::CommonUtils::VerifyAuth(to_verify_digest));
  to_verify_digest = "invalid_digest";
  ASSERT_FALSE(biz::CommonUtils::VerifyAuth(to_verify_digest));
  // to_verify_digest = "dc746eb4810da848d84c05c0abe6f9f8";
  // ASSERT_TRUE(biz::CommonUtils::VerifyAuth(to_verify_digest));
}

TEST_F(TestCommonUtils, IsReconciliationNeeded) {
  ASSERT_EQ(false, biz::CommonUtils::IsReconciliationNeeded(nullptr));

  svc::unified_v2::UnifiedV2ResultDTO ur;
  auto x_seq_mark = ur.mutable_x_seq_mark_infos()->Add();
  x_seq_mark->set_system_kind(enums::esystemkind::UnifiedEngine);
  x_seq_mark->set_node("node_name_");
  x_seq_mark->set_cross_idx(static_cast<ECrossIdx>(-1));
  x_seq_mark->set_next_cross_seq(-100);
  x_seq_mark->set_transact_time_e9(bbase::utils::Time::GetTimeNs());
  ASSERT_EQ(true, biz::CommonUtils::IsReconciliationNeeded(&ur));

  ur.Clear();
  ur.mutable_futures_margin_result()->mutable_related_fills()->Add();
  ASSERT_EQ(true, biz::CommonUtils::IsReconciliationNeeded(&ur));

  ur.Clear();
  ur.mutable_futures_margin_result()->mutable_related_funding_records()->Add();
  ASSERT_EQ(true, biz::CommonUtils::IsReconciliationNeeded(&ur));

  ur.Clear();
  ur.mutable_futures_margin_result()->mutable_related_orders()->Add();
  ASSERT_EQ(false, biz::CommonUtils::IsReconciliationNeeded(&ur));
}

TEST_F(TestCommonUtils, hash) {
  std::map<uint64_t, uint64_t> map_to_fill;
  std::map<uint64_t, uint64_t> map_uid_loop_count;
  for (uint64_t thread_cnt = 2; thread_cnt < 100; ++thread_cnt) {
    for (std::uint64_t i = 1000000; i < 1010000; i++) {
      uint64_t index = utils::hash(i) % thread_cnt;
      if (map_to_fill.find(index) == map_to_fill.end()) {
        map_to_fill[index] = i;
      }
      if (map_to_fill.size() == thread_cnt) {
        map_uid_loop_count[thread_cnt] = i;
        map_to_fill.clear();
        break;
      }
    }
  }

  for (uint64_t thread_cnt = 2; thread_cnt < 100; ++thread_cnt) {
    ASSERT_NE(map_uid_loop_count.find(thread_cnt), map_uid_loop_count.end());
    std::cout << " thread cnt:" << thread_cnt << " match loop count:" << map_uid_loop_count[thread_cnt];
  }
}
