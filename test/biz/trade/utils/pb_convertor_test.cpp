#include "test/biz/trade/utils/pb_convertor_test.hpp"

#include <memory>

#include "data/type/biz_type.hpp"
#include "proto/gen/models/usersettingdto/per_coin_user_setting_dto.pb.h"
#include "src/biz_worker/service/trade/store/per_symbol_store.hpp"
#include "src/biz_worker/service/trade/store/setting/raw_setting.hpp"
#include "src/biz_worker/utils/pb_convertor/from_pb.hpp"
#include "src/biz_worker/utils/pb_convertor/to_pb.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

std::shared_ptr<tmock::CTradeAppMock> TestPbConvertor::te_{};

void TestPbConvertor::SetUpTestSuite() {
  te_ = std::make_shared<tmock::CTradeAppMock>();
  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te_);

  te_->Init(argument_count, argument_arrary);
  te_->Start();
}

void TestPbConvertor::TearDownTestSuite() {
  te_->Stop(true);
  bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
  te_.reset();
}

// from_pb
TEST_F(TestPbConvertor, ConvertToCoinUserSetting) {
  // NullInput
  {
    ::models::usersettingdto::PerCoinUserSettingDTO* src = nullptr;
    store::PerCoinUserSetting* des = nullptr;
    EXPECT_EQ(-1, bizutils::FromPB::ConvertToCoinUserSetting(src, des));
  }

  // ValidInput
  {
    ::models::usersettingdto::PerCoinUserSettingDTO src;
    store::PerCoinUserSetting des(123, ECoin::BTC);
    src.set_user_id(123);
    src.set_coin(ECoin::BTC);
    src.set_max_leverage_e2(100);
    src.set_taker_fee_rate_e8(500);
    src.set_maker_fee_rate_e8(250);
    src.set_taker_fee_rate_desc("Taker fee");
    src.set_maker_fee_rate_desc("Maker fee");
    src.set_dealer_user_id(456);
    src.set_is_unified_margin(false);
    src.add_symbol_list(ESymbol::BTCUSDT);
    src.set_setting_version(1);
    src.set_created_at_e9(123456789);
    src.set_updated_at_e9(987654321);
    src.set_default_position_mode(EPositionMode::BothSide);
    src.set_risk_limit_config_type(::enums::erisklimitconfigtype::RiskLimitConfigType::First);
    (*src.mutable_per_symbol_max_leverage())[ESymbol::BTCUSDT] = 200;
    EXPECT_EQ(0, bizutils::FromPB::ConvertToCoinUserSetting(&src, &des));
    EXPECT_EQ(123, des.uid);
    EXPECT_EQ(ECoin::BTC, des.coin);
    EXPECT_EQ(100, des.max_leverage_e2);
    EXPECT_EQ(500, des.optional_tkfr.value());
    EXPECT_EQ(250, des.optional_mkfr.value());
    EXPECT_EQ(biz::str64_t("Taker fee"), des.optional_tkfr_desc.value());
    EXPECT_EQ(biz::str64_t("Maker fee"), des.optional_mkfr_desc.value());
    EXPECT_EQ(456, des.dealer_user_id);
    EXPECT_EQ(false, des.is_unified_margin);
    EXPECT_EQ(EPositionMode::BothSide, des.optional_default_pz_mode.value());
    EXPECT_EQ(1, des.risk_limit_config_type);
  }

  // EmptyInput
  {
    ::models::usersettingdto::PerCoinUserSettingDTO src;
    store::PerCoinUserSetting des(0, ECoin::UNKNOWN);
    EXPECT_EQ(0, bizutils::FromPB::ConvertToCoinUserSetting(&src, &des));
    EXPECT_EQ(0, des.uid);
    EXPECT_EQ(0, des.coin);
    EXPECT_EQ(0, des.max_leverage_e2);
    EXPECT_FALSE(des.optional_tkfr.has_value());
    EXPECT_FALSE(des.optional_mkfr.has_value());
    EXPECT_FALSE(des.optional_tkfr_desc.has_value());
    EXPECT_FALSE(des.optional_mkfr_desc.has_value());
    EXPECT_EQ(0, des.dealer_user_id);
    EXPECT_FALSE(des.is_unified_margin);
    EXPECT_TRUE(des.per_symbol_fee_rate.empty());
    EXPECT_FALSE(des.optional_default_pz_mode.has_value());
    EXPECT_EQ(0, des.risk_limit_config_type);
    EXPECT_TRUE(des.per_symbol_max_leverage.empty());
  }

  // MissingOptionalFields
  {
    ::models::usersettingdto::PerCoinUserSettingDTO src;
    store::PerCoinUserSetting des(123, ECoin::BTC);
    src.set_user_id(123);
    src.set_coin(ECoin::BTC);
    src.set_max_leverage_e2(100);
    src.set_dealer_user_id(456);
    src.set_is_unified_margin(false);
    src.add_symbol_list(ESymbol::BTCUSDT);
    src.set_setting_version(1);
    src.set_created_at_e9(123456789);
    src.set_updated_at_e9(987654321);
    EXPECT_EQ(0, bizutils::FromPB::ConvertToCoinUserSetting(&src, &des));
    EXPECT_EQ(123, des.uid);
    EXPECT_EQ(1, des.coin);
    EXPECT_EQ(100, des.max_leverage_e2);
    EXPECT_FALSE(des.optional_tkfr.has_value());
    EXPECT_FALSE(des.optional_mkfr.has_value());
    EXPECT_FALSE(des.optional_tkfr_desc.has_value());
    EXPECT_FALSE(des.optional_mkfr_desc.has_value());
    EXPECT_EQ(456, des.dealer_user_id);
    EXPECT_EQ(false, des.is_unified_margin);
    EXPECT_TRUE(des.per_symbol_fee_rate.empty());
    EXPECT_FALSE(des.optional_default_pz_mode.has_value());
    EXPECT_EQ(0, des.risk_limit_config_type);
    EXPECT_TRUE(des.per_symbol_max_leverage.empty());
  }
}

TEST_F(TestPbConvertor, ConvertToSymbolFeeRate) {
  // Positive Test Case
  {
    ::models::usersettingdto::OptionMmpInfo src;
    store::OptionMmpInfo des;
    src.set_base_coin("BTC");
    src.set_mmp_enabled(true);
    src.set_window(10);
    src.set_frozen_period(20);
    src.set_qty_limit(30);
    src.set_delta_limit(40);
    src.set_mmp_frozen_until(50);
    EXPECT_EQ(bizutils::FromPB::ConvertToMmpInfo(&src, &des), 0);
    EXPECT_EQ(des.base_coin_, "BTC");
    EXPECT_TRUE(des.mmp_enabled_);
    EXPECT_EQ(des.window_, 10);
    EXPECT_EQ(des.frozen_period_, 20);
    EXPECT_EQ(des.qty_limit_, 30);
    EXPECT_EQ(des.delta_limit_, 40);
    EXPECT_EQ(des.mmp_frozen_until_, 50);
  }
  // Negative Test Case
  {
    ::models::usersettingdto::OptionMmpInfo src;
    store::OptionMmpInfo des;
    src.set_base_coin("BTC");
    src.set_mmp_enabled(true);
    src.set_window(10);
    src.set_frozen_period(20);
    src.set_qty_limit(30);
    src.set_delta_limit(40);
    src.set_mmp_frozen_until(50);
    EXPECT_NE(bizutils::FromPB::ConvertToMmpInfo(&src, &des), 1);
    EXPECT_NE(des.base_coin_, "ETH");
    EXPECT_TRUE(des.mmp_enabled_);
    EXPECT_NE(des.window_, 11);
    EXPECT_NE(des.frozen_period_, 21);
    EXPECT_NE(des.qty_limit_, 31);
    EXPECT_NE(des.delta_limit_, 41);
    EXPECT_NE(des.mmp_frozen_until_, 51);
  }
}

TEST_F(TestPbConvertor, ConvertToFuturePerSymbolData) {
  // Positive test case
  {
    ::models::dump::UserData src;
    auto& future_symbol_data = (*src.mutable_future_symbol_data())[5];
    store::PerSymbolStore des(ESymbol::BTCUSDT, EPositionMode::BothSide);
    future_symbol_data.set_symbol(5);
    EXPECT_EQ(bizutils::FromPB::ConvertToFuturePerSymbolData(&src, &des), 0);
    EXPECT_EQ(des.symbol_, 5);
    EXPECT_TRUE(des.all_positions_.empty());
    EXPECT_TRUE(des.all_future_orders_.empty());
    EXPECT_TRUE(des.all_future_active_orders_.empty());
    EXPECT_TRUE(des.pending_orders_.empty());
  }
  // Negative test case
  {
    ::models::dump::UserData* src = nullptr;
    store::PerSymbolStore* des = nullptr;
    EXPECT_EQ(bizutils::FromPB::ConvertToFuturePerSymbolData(src, des), -1);
  }
}

TEST_F(TestPbConvertor, ConvertToRawTransact) {
  {
    ::models::tradingdto::TransactDTO src;
    store::Order* des{nullptr};
    ASSERT_EQ(bizutils::FromPB::ConvertToRawTransact(&src, des), -1);
  }

  {
    ::models::tradingdto::TransactDTO src;
    store::Order des;
    ASSERT_EQ(bizutils::FromPB::ConvertToRawTransact(&src, &des), 0);
  }
}

TEST_F(TestPbConvertor, ConvertToRawPosition) {
  {
    ::models::tradingdto::PositionDTO src;
    store::Position* des{nullptr};
    ASSERT_EQ(bizutils::FromPB::ConvertToRawPosition(&src, des), -1);
  }

  {
    ::models::tradingdto::PositionDTO src;
    store::Position des;
    ASSERT_EQ(bizutils::FromPB::ConvertToRawPosition(&src, &des), 0);
  }
}

TEST_F(TestPbConvertor, ConvertToPositionDTO) {
  {
    store::Position* src{nullptr};
    models::tradingdto::PositionDTO des;
    ASSERT_EQ(convertor::ToPB::ConvertToPositionDTO(src, &des), -1);
  }

  {
    store::Position src;
    models::tradingdto::PositionDTO des;
    ASSERT_EQ(convertor::ToPB::ConvertToPositionDTO(&src, &des), 0);
  }
}

TEST_F(TestPbConvertor, ConvertToRawSpotTransact) {
  {
    ::models::tradingdto::UnifySpotTransactDTO src;
    store::SpotOrder des{};
    ASSERT_EQ(bizutils::FromPB::ConvertToRawSpotTransact(&src, &des, nullptr), -1);
  }

  {
    ::models::tradingdto::UnifySpotTransactDTO src;
    store::SpotOrder des{};
    std::shared_ptr<config::SpotSymbolDTO> sc = std::make_shared<config::SpotSymbolDTO>();
    ASSERT_EQ(bizutils::FromPB::ConvertToRawSpotTransact(&src, &des, sc.get()), 0);
  }
}

TEST_F(TestPbConvertor, ConvertToSpotPerSymbolFeeRate) {
  {
    ::models::usersettingdto::SpotPerSymbolFeeRate src;
    src.set_symbol(5);
    store::SpotPerSymbolFeeRate* des{nullptr};
    ASSERT_EQ(bizutils::FromPB::ConvertToSpotPerSymbolFeeRate(&src, des), -1);
  }

  {
    ::models::usersettingdto::SpotPerSymbolFeeRate src;
    src.set_symbol(5);
    store::SpotPerSymbolFeeRate des{};
    ASSERT_EQ(bizutils::FromPB::ConvertToSpotPerSymbolFeeRate(&src, &des), 0);
  }
}
