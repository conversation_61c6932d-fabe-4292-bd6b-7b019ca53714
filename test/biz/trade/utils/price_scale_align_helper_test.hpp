#ifndef TEST_BIZ_TRADE_UTILS_PRICE_SCALE_ALIGN_HELPER_TEST_HPP_
#define TEST_BIZ_TRADE_UTILS_PRICE_SCALE_ALIGN_HELPER_TEST_HPP_

#include <gtest/gtest.h>

#include <memory>

#include "data/type/biz_type.hpp"

namespace tmock {
class CTradeAppMock;
}

class TestPriceScaleAlignHelper : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite();
  static void TearDownTestSuite();

 protected:
  static std::shared_ptr<tmock::CTradeAppMock> te_;
  biz::coin_t coin_{ECoin::USDT};
};

#endif  // TEST_BIZ_TRADE_UTILS_PRICE_SCALE_ALIGN_HELPER_TEST_HPP_
