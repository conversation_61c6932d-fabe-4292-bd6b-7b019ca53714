#pragma once

#include <folly/MPMCQueue.h>

#include <bbase/common/bgrpc/async_server/async_server.hpp>
#include <memory>
#include <string>

#include "proto/gen/svc/unified_v2/u2_dump_service.grpc.pb.h"
#include "proto/gen/svc/unified_v2/u2_dump_service.pb.h"

namespace tmock {
/**
 * 模拟服务器绑定端口为：23456
 */
class TradingDumpServiceMock {
 public:
  using GrpcServer = bbase::bgrpc::AsyncServer<>;
  using GrpcServerPtr = std::shared_ptr<GrpcServer>;
  using RecoveryRequest = svc::unified_v2::RecoveryRequestDTO;
  using RecoveryResult = svc::unified_v2::RecoveryResultDTO;
  using AsyncServerContextStreamingImpl =
      bbase::bgrpc::AsyncServerContextStreamingImpl<RecoveryRequest, RecoveryResult>;

  struct AsyncServerStreamingPackage {
    std::shared_ptr<AsyncServerContextStreamingImpl> context;
    std::shared_ptr<RecoveryRequest> request;
    std::uint64_t context_verison;
    std::uint64_t sequence;
  };

 public:
  TradingDumpServiceMock() : input_channell_(204800), output_channell_(204800) {}
  ~TradingDumpServiceMock();
  void Start(const std::string& address_p);
  void Stop();
  void ProcessInPackage();
  void ProcessOutPackage();
  void InvokerMethod(const std::shared_ptr<AsyncServerContextStreamingImpl>& context_p, std::uint64_t version_p);
  void ReadMethod(const std::shared_ptr<AsyncServerContextStreamingImpl>& context_p, std::uint64_t version_p,
                  std::uint64_t sequence_p, const std::shared_ptr<RecoveryRequest>& request_p);
  void WriteMethod(const std::shared_ptr<AsyncServerContextStreamingImpl>&, std::uint64_t version_p, bool ok);
  void FinishMethod(const std::shared_ptr<AsyncServerContextStreamingImpl>&, std::uint64_t version_p);
  void ProcessRequest(const std::shared_ptr<AsyncServerContextStreamingImpl>& context_p,
                      const std::shared_ptr<RecoveryRequest>& request_p, std::uint64_t context_version_p,
                      std::uint64_t sequence_p);

 private:
  volatile bool stop_ = false;
  GrpcServerPtr server_ = nullptr;  // GRPC服务器实例
  folly::MPMCQueue<AsyncServerStreamingPackage> input_channell_;
  folly::MPMCQueue<AsyncServerStreamingPackage> output_channell_;
  std::shared_ptr<std::thread> t1_;
  std::shared_ptr<std::thread> t2_;
  std::string session_id_;
};
}  // namespace tmock
