#include "test/biz/trade/trading_dump/fast_dump_mock.hpp"  // NOLINT

#include <utility>

namespace tmock {
void TradingDumpServiceMock::Start(const std::string& address_p) {
  server_ = std::make_shared<bbase::bgrpc::AsyncServer<>>();
  server_->Listen(address_p);
  auto service = server_->RegisterService<svc::unified_v2::U2DumpService::AsyncService>();
  server_->Build(1, 1);

  server_->AddStreamingRequestToken<svc::unified_v2::U2DumpService::AsyncService, RecoveryRequest, RecoveryResult>(
      0, "", 16, service.get(), &svc::unified_v2::U2DumpService::AsyncService::RequestSubscribe, {},
      [this](auto&& PH1, auto&& VERSION) {
        InvokerMethod(std::forward<decltype(PH1)>(PH1), std::forward<decltype(VERSION)>(VERSION));
      },
      [this](auto&& PH1, auto&& VERSION, auto&& SEQUENCE, auto&& PH2) {
        ReadMethod(std::forward<decltype(PH1)>(PH1), std::forward<decltype(VERSION)>(VERSION),
                   std::forward<decltype(SEQUENCE)>(SEQUENCE), std::forward<decltype(PH2)>(PH2));
      },
      {},
      [this](auto&& PH1, auto&& VERSION, auto&& PH2) {
        WriteMethod(std::forward<decltype(PH1)>(PH1), std::forward<decltype(VERSION)>(VERSION),
                    std::forward<decltype(PH2)>(PH2));
      },
      [this](auto&& PH1, auto&& VERSION) {
        FinishMethod(std::forward<decltype(PH1)>(PH1), std::forward<decltype(VERSION)>(VERSION));
      });
  server_->StartProcess();
  t1_ = std::make_shared<std::thread>(&TradingDumpServiceMock::ProcessInPackage, this);
  t2_ = std::make_shared<std::thread>(&TradingDumpServiceMock::ProcessOutPackage, this);
}

void TradingDumpServiceMock::Stop() {
  stop_ = true;
  t1_->join();
  t2_->join();

  server_->StopProcess();
  server_.reset();
}

void TradingDumpServiceMock::ProcessInPackage() {
  AsyncServerStreamingPackage package{};

  while (true) {
    input_channell_.blockingRead(package);
    output_channell_.blockingWrite(package);
  }
}

void TradingDumpServiceMock::ProcessOutPackage() {
  AsyncServerStreamingPackage package{};

  while (true) {
    output_channell_.blockingRead(package);
    ProcessRequest(package.context, package.request, package.context_verison, package.sequence);
  }
}

void TradingDumpServiceMock::ProcessRequest(const std::shared_ptr<AsyncServerContextStreamingImpl>&,
                                            const std::shared_ptr<RecoveryRequest>&, std::uint64_t, std::uint64_t) {}

void TradingDumpServiceMock::InvokerMethod(const std::shared_ptr<AsyncServerContextStreamingImpl>& context_p,
                                           std::uint64_t version_p) {
  LOG_INFO("server InvokerMethod new streaming invoked from client_ip[{}], version_p[{}]", context_p->context().peer(),
           version_p);
}

void TradingDumpServiceMock::ReadMethod(const std::shared_ptr<AsyncServerContextStreamingImpl>& context_p,
                                        std::uint64_t version_p, std::uint64_t sequence_p,
                                        const std::shared_ptr<RecoveryRequest>& request_p) {
  AsyncServerStreamingPackage package{context_p, request_p, version_p, sequence_p};
  input_channell_.blockingWrite(package);
}

void TradingDumpServiceMock::WriteMethod(const std::shared_ptr<AsyncServerContextStreamingImpl>&,
                                         std::uint64_t version_p, bool ok) {
  LOG_INFO("server WriteMethod grpc version[{}] ok[{}]", version_p, ok);
}

void TradingDumpServiceMock::FinishMethod(const std::shared_ptr<AsyncServerContextStreamingImpl>&,
                                          std::uint64_t version_p) {
  LOG_INFO("server FinishMethod: grpc version[{}]", version_p);
}

}  // namespace tmock
