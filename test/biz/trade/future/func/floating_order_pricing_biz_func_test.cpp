#include "floating_order_pricing_biz_func_test.h"  // NOLINT

#include <fmt/format.h>

#include "biz_worker/service/trade/futures/modules/orderbiz/floatingorderpricingbiz/floating_order_pricing_biz.hpp"
#include "data/type/biz_type.hpp"
#include "enums/ebizcode/biz_code.pb.h"
#include "test/biz/trade/future/func/draft_pkg_builder.h"

////////////////////////////////////////////////////////////////////////////////////

TEST_F(FloatingOrderPricingBizFuncTest, test_linear_buy_formula) {
  GTEST_SKIP();
  // TODO(jiawei)
  /*
  // #1: create draft pkg builder
  test::DraftPkgBuilder builder;
  builder.Build();

  // #2: get draft pkg
  biz::DraftPkg *pkg = builder.GetDraftPkg();

  // #3: get input params
  FloatingOrderPricingParams param;

  // #4: do testing
  param.Reset();
  param.extra_qty_x_p = 10;
  param.closing_qty_x_p = 0;
  param.v2c_e8_p = 100000000;
  param.convertedAvailableBalance = biz::money_t(-999999999999);

  auto ret = biz::FloatingOrderPricingBiz::LinearBuyFormula(
      pkg, param.extra_qty_x_p, param.v2c_e8_p, param.mark_price_x, param.convertedAvailableBalance,
      param.convertedNotLiqAvailableBalance, param.free_cost_x, param.closing_qty_x_p, param.pz_size_x_p,
      param.pz_mm_with_fee_value_e8_p, param.least_price_x_p, param.most_price_x_p, param.price_scale, param.qty_scale,
      param.value_scale, param.tick_size_x_, param.taker_fee_rate_e8);

  ASSERT_TRUE(ret.first == 0 && ret.second == enums::ebizcode::BizCode::ParamsError);
  */
}
