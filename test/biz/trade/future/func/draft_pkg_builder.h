#pragma once

#include <memory>

#include "src/biz_worker/service/base/draft_pkg.hpp"

namespace test {

/**
 * DraftPkgBuilder
 */
class DraftPkgBuilder {
 public:
  DraftPkgBuilder();
  explicit DraftPkgBuilder(std::shared_ptr<const event::BizEvent> raw_ev);
  virtual ~DraftPkgBuilder();

 public:
  virtual void Build();
  biz::DraftPkg* GetDraftPkg() { return &draft_pkg_; }

 protected:
  biz::DraftPkg draft_pkg_;
};

}  // namespace test
