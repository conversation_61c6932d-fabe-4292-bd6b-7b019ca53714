#pragma once

#include <gtest/gtest.h>

#include "src/data/type/biz_type.hpp"

/**
 * FloatingOrderPricingParams
 */
struct FloatingOrderPricingParams {
  biz::size_x_t extra_qty_x_p{0};
  biz::size_x_t v2c_e8_p{0};
  biz::price_x_t mark_price_x{0};
  biz::money_t convertedAvailableBalance{0};
  biz::money_t convertedNotLiqAvailableBalance{0};
  biz::money_x_t free_cost_x{0};
  biz::size_x_t closing_qty_x_p{0};
  biz::size_x_t pz_size_x_p{0};
  biz::value_x_t pz_mm_with_fee_value_e8_p{0};
  biz::price_x_t least_price_x_p{0};
  biz::price_x_t most_price_x_p{0};
  biz::scale_t price_scale{0};
  biz::scale_t qty_scale{0};
  biz::scale_t value_scale{0};
  biz::size_x_t tick_size_x_{0};
  biz::fee_rate_e8_t taker_fee_rate_e8{0};

  void Reset() {
    extra_qty_x_p = 0;
    v2c_e8_p = 0;
    mark_price_x = 0;
    convertedAvailableBalance = biz::money_t(0);
    convertedNotLiqAvailableBalance = biz::money_t(0);
    free_cost_x = 0;
    closing_qty_x_p = 0;
    pz_size_x_p = 0;
    pz_mm_with_fee_value_e8_p = 0;
    least_price_x_p = 10000;
    most_price_x_p = 10011;
    price_scale = 8;
    qty_scale = 8;
    value_scale = 8;
    tick_size_x_ = 0;
    taker_fee_rate_e8 = 0;
  }
};

/**
 * FloatingOrderBizFuncTest
 */
class FloatingOrderPricingBizFuncTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {}
  static void TearDownTestSuite() {}
};
