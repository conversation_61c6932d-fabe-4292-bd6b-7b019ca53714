#include "src/biz_worker/service/trade/futures/modules/tpsltsbiz/adjust_tp_sl_ts_biz.h"

#include <boost/uuid/uuid_io.hpp>

#include "gtest/gtest.h"
#include "lib/stub.hpp"
#include "proto/gen/enums/ebizcode/biz_code.pb.h"
#include "proto/gen/svc/trading/req/create_order.pb.h"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/store/per_worker_store.hpp"
#include "src/biz_worker/service/trade/store/user_data_manager.hpp"
#include "src/data/enum.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class TestAdjustPartialTpSlTsCache : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestCase() {}
  static void TearDownTestCase() { /*app::GlobalVarManager::Instance().coin_ = ECoin::UNKNOWN;*/ }

  biz::DraftPkg* GetDraftPkg() {
    store::PerWorkerStore* per_worker_store = new store::PerWorkerStore();
    auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
    auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
    auto symbol_config = std::make_shared<biz::SymbolConfig>();
    symbol_config->value_scale_ = 4;
    symbol_config->price_scale_ = 4;
    symbol_config->symbol_ = ESymbol::BTCUSDT;
    symbol_config->coin_ = ECoin::USDT;
    symbol_config->value_scale_ = 4;
    symbol_config->tick_size_x_ = 5;
    symbol_config->lot_size_x_ = 1000;
    symbol_config->price_limit_pnt_e6_ = 10;
    symbol_config->min_price_x_ = 100;
    symbol_config->min_qty_x_ = 100;
    symbol_config->max_price_x_ = 1000000000;
    symbol_config->max_order_book_qty_x_ = 10000'0000;
    symbol_config->max_new_order_qty_x_ = 10000'0000;
    symbol_config->max_new_market_order_qty_x_ = 1000'0000;
    symbol_config->contract_type_ = EContractType::LinearPerpetual;
    symbol_config_mgr->AddSymbolConfig(symbol_config);
    new_config_mgr->set_symbol_config_mgr(symbol_config_mgr);
    config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

    store::UserDataManager* user_data_manager_ = new store::UserDataManager();
    store::PerUserStore* per_user_store = user_data_manager_->GetUserData(user_id);
    // 初始化
    if (per_user_store == nullptr) {
      user_data_manager_->CreateUserData(user_id);
      per_user_store = user_data_manager_->GetUserData(user_id);
    }
    per_user_store->working_coins_[ECoin::USDC] = {};

    draft_pkg = std::make_shared<biz::DraftPkg>(nullptr, header, per_worker_store, per_user_store);
    return draft_pkg.get();
  }

 protected:
  std::shared_ptr<tmock::CTradeAppMock> te;
  biz::DraftPkg::Ptr draft_pkg;
  biz::user_id_t user_id = 111111;
  std::shared_ptr<store::Header> header = std::make_shared<store::Header>();
};

TEST_F(TestAdjustPartialTpSlTsCache, GetTpSlStopOrderByParentId) {
  biz::AdjustPartialTpSlTsCache data;
  biz::order_id_t id_p;
  auto res = data.GetTpSlStopOrderByParentId(EStopOrderType::UNKNOWN, id_p);
  EXPECT_EQ(std::get<0>(res), nullptr);
}

TEST_F(TestAdjustPartialTpSlTsCache, CreateAndSetTpSlStopOrder) {
  biz::AdjustPartialTpSlTsCache data;
  biz::order_id_t id_p;
  biz::remark_t remark_p;
  biz::order_id_t parent_order_id_p;
  data.CreateAndSetTpSlStopOrder(EStopOrderType::UNKNOWN, 0, 0, ETriggerBy::UNKNOWN, remark_p, parent_order_id_p,
                                 EOrderType::Limit, 0, false);
  EXPECT_EQ(data.tp_stop_orders.size(), 0);
  EXPECT_EQ(data.sl_stop_orders.size(), 0);
}

TEST_F(TestAdjustPartialTpSlTsCache, OrderCompare) {
  auto order = std::make_shared<store::Order>();
  auto res = biz::AdjustTpSlTsBiz::OrderCompare(nullptr, nullptr);
  EXPECT_TRUE(res);
  res = biz::AdjustTpSlTsBiz::OrderCompare(order, nullptr);
  EXPECT_FALSE(res);
}

TEST_F(TestAdjustPartialTpSlTsCache, AdjustReplaceAoTpSl) {
  auto pkg = GetDraftPkg();
  biz::cross::DerivativesTransactionPtr x_resp_item_p = std::make_shared<models::tradingdto::TransactDTO>();
  biz::cross::DerivativesMatchingResult req_body_p;
  x_resp_item_p->set_cross_status(ECrossStatus::Replaced);

  models::tradingdto::TransactDTO order;
  order.set_price_x(10000);
  order.SerializeToString(&req_body_p.orig_taker_new_item);

  biz::order_id_t order_id(te->GenUUID());
  x_resp_item_p->set_order_id(order_id.GetValue());
  auto cow_order = std::make_shared<store::CowOrder>();
  auto new_order = std::make_shared<store::Order>();
  cow_order->cur_order = new_order;
  cow_order->draft_order = new_order;
  pkg->u_store->all_future_working_order_map_by_order_id_[order_id] = cow_order.get();

  biz::AdjustTpSlTsBiz::AdjustReplaceAoTpSl(pkg, x_resp_item_p, &req_body_p);

  order.set_take_profit_x(100);
  order.set_stop_loss_x(100);
  order.set_tp_trigger_by(ETriggerBy::LastPrice);
  order.set_sl_trigger_by(ETriggerBy::LastPrice);
  order.SerializeToString(&req_body_p.orig_taker_new_item);
  biz::AdjustTpSlTsBiz::AdjustReplaceAoTpSl(pkg, x_resp_item_p, &req_body_p);
}

TEST_F(TestAdjustPartialTpSlTsCache, AdjustReplaceAoTpSl1) {
  auto pkg = GetDraftPkg();
  biz::cross::DerivativesTransactionPtr x_resp_item_p = std::make_shared<models::tradingdto::TransactDTO>();
  biz::cross::DerivativesMatchingResult req_body_p;
  x_resp_item_p->set_cross_status(ECrossStatus::ReAdded);

  models::tradingdto::TransactDTO order;
  order.set_price_x(10000);
  order.SerializeToString(&req_body_p.orig_taker_new_item);

  biz::order_id_t order_id(te->GenUUID());
  x_resp_item_p->set_order_id(order_id.GetValue());
  auto cow_order = std::make_shared<store::CowOrder>();
  auto new_order = std::make_shared<store::Order>();
  cow_order->cur_order = (new_order);
  cow_order->draft_order = (new_order);
  pkg->u_store->all_future_working_order_map_by_order_id_[order_id] = cow_order.get();

  biz::AdjustTpSlTsBiz::AdjustReplaceAoTpSl(pkg, x_resp_item_p, &req_body_p);

  order.set_take_profit_x(100);
  order.set_stop_loss_x(100);
  order.set_tp_trigger_by(ETriggerBy::LastPrice);
  order.set_sl_trigger_by(ETriggerBy::LastPrice);
  order.SerializeToString(&req_body_p.orig_taker_new_item);
  biz::AdjustTpSlTsBiz::AdjustReplaceAoTpSl(pkg, x_resp_item_p, &req_body_p);
}

TEST_F(TestAdjustPartialTpSlTsCache, CancelAllFullTpSlTsOrder) {
  auto pkg = GetDraftPkg();
  auto pz_p = std::make_shared<store::Position>();
  pz_p->symbol = ESymbol::BTCUSDT;
  auto cow_position = std::make_shared<store::CowPosition>(pz_p, EProductType::Futures);
  {
    auto tp_cow_order = std::make_shared<store::CowOrder>();
    auto tp_order1 = std::make_shared<store::Order>();
    tp_cow_order->cur_order = (tp_order1);
    cow_position->tp_sl_order_mgr.tp_order_list_->ref_full_tp_sl_ts_order_ = tp_cow_order.get();

    auto tp_order = std::make_shared<store::Order>();
    tp_order->order_status = EOrderStatus::Untriggered;
    auto cow_tp_order = std::make_shared<store::CowOrder>();
    cow_tp_order->cur_order = (tp_order);
    biz::AdjustTpSlTsBiz::CancelAllFullTpSlTsOrder(pkg, cow_position.get(), pz_p.get(), cow_tp_order.get(), nullptr,
                                                   nullptr);
  }

  {
    auto sl_cow_order = std::make_shared<store::CowOrder>();
    auto sl_order1 = std::make_shared<store::Order>();
    sl_cow_order->cur_order = (sl_order1);
    cow_position->tp_sl_order_mgr.sl_order_list_->ref_full_tp_sl_ts_order_ = sl_cow_order.get();

    auto sl_order = std::make_shared<store::Order>();
    sl_order->order_status = EOrderStatus::Untriggered;
    auto cow_sl_order = std::make_shared<store::CowOrder>();
    cow_sl_order->cur_order = (sl_order);
    biz::AdjustTpSlTsBiz::CancelAllFullTpSlTsOrder(pkg, cow_position.get(), pz_p.get(), nullptr, cow_sl_order.get(),
                                                   nullptr);
  }

  {
    auto ts_cow_order = std::make_shared<store::CowOrder>();
    auto ts_order1 = std::make_shared<store::Order>();
    ts_cow_order->cur_order = (ts_order1);
    cow_position->tp_sl_order_mgr.ts_order_list_->ref_full_tp_sl_ts_order_ = ts_cow_order.get();

    auto ts_order = std::make_shared<store::Order>();
    ts_order->order_status = EOrderStatus::Untriggered;
    auto cow_ts_order = std::make_shared<store::CowOrder>();
    cow_ts_order->cur_order = (ts_order);
    biz::AdjustTpSlTsBiz::CancelAllFullTpSlTsOrder(pkg, cow_position.get(), pz_p.get(), nullptr, nullptr,
                                                   cow_ts_order.get());
  }
}

TEST_F(TestAdjustPartialTpSlTsCache, AdjustPartialPositionSizeChangeTpSlTs) {
  auto pkg = GetDraftPkg();
  auto pz_p = std::make_shared<store::Position>();
  pz_p->symbol = ESymbol::BTCUSDT;
  pz_p->size = 100;
  auto cow_position = std::make_shared<store::CowPosition>(pz_p, EProductType::Futures);

  auto new_pz_p = std::make_shared<store::Position>();
  new_pz_p->symbol = ESymbol::BTCUSDT;
  new_pz_p->size = 150;
  cow_position->draft_position = (new_pz_p);

  auto ts_cow_order = std::make_shared<store::CowOrder>();
  auto ts_order1 = std::make_shared<store::Order>();
  ts_order1->order_status = EOrderStatus::Untriggered;
  ts_cow_order->cur_order = (ts_order1);
  cow_position->tp_sl_order_mgr.ts_order_list_->ref_full_tp_sl_ts_order_ = ts_cow_order.get();

  biz::AdjustTpSlTsBiz::AdjustPartialPositionSizeChangeTpSlTs(pkg, cow_position.get(), nullptr);
}

TEST_F(TestAdjustPartialTpSlTsCache, AdjustPartialTpSlTs) {
  GTEST_SKIP();
  auto pkg = GetDraftPkg();
  auto pz_p = std::make_shared<store::Position>();
  pz_p->symbol = ESymbol::BTCUSDT;
  pz_p->size = 100;
  pz_p->side = ESide::Buy;
  auto cow_position = std::make_shared<store::CowPosition>(pz_p, EProductType::Futures);

  auto new_pz_p = std::make_shared<store::Position>();
  new_pz_p->symbol = ESymbol::BTCUSDT;
  new_pz_p->size = 150;
  new_pz_p->side = ESide::Buy;
  cow_position->draft_position = (new_pz_p);

  {
    auto tp_cow_order = std::make_shared<store::CowOrder>();
    auto tp_order1 = std::make_shared<store::Order>();
    tp_order1->side = ESide::Buy;
    tp_order1->order_status = EOrderStatus::Untriggered;
    tp_cow_order->cur_order = (tp_order1);
    cow_position->tp_sl_order_mgr.tp_order_list_->ref_full_tp_sl_ts_order_ = tp_cow_order.get();
  }

  auto ts_cow_order = std::make_shared<store::CowOrder>();
  auto ts_order1 = std::make_shared<store::Order>();
  ts_order1->order_status = EOrderStatus::Untriggered;
  ts_cow_order->cur_order = (ts_order1);
  cow_position->tp_sl_order_mgr.ts_order_list_->ref_full_tp_sl_ts_order_ = ts_cow_order.get();

  auto fill_order = std::make_shared<store::Order>();
  fill_order->side = ESide::Buy;
  fill_order->take_profit_x = 100;
  fill_order->cum_qty = 100;
  fill_order->order_tpsl_mode = ETpSlMode::Full;
  pkg->future_related_fills_.emplace_back(fill_order);

  biz::AdjustTpSlTsBiz::AdjustPartialTpSlTs(pkg, cow_position.get());

  //  pz_p->side = ESide::Sell;
  //  biz::AdjustTpSlTsBiz::AdjustPartialTpSlTs(pkg, cow_position);
  (*pkg->w_store->future_symbol_last_price_map_)[ESymbol::BTCUSDT] = bbase::decimal::Decimal(1200);
  { cow_position->tp_sl_order_mgr.tp_order_list_->ref_full_tp_sl_ts_order_ = nullptr; }
  biz::AdjustTpSlTsBiz::AdjustPartialTpSlTs(pkg, cow_position.get());

  biz::order_id_t order_id(te->GenUUID());
  // {

  //   auto cow_order = std::make_shared<store::CowOrder>();
  //   auto new_order = std::make_shared<store::Order>();
  //   cow_order->set_cur(new_order);
  //   pkg->u_store->all_future_working_order_map_by_order_id_[order_id] = cow_order;
  // }

  // fill_order->take_profit_x = 0;
  {
    auto sl_cow_order = std::make_shared<store::CowOrder>();
    auto sl_order1 = std::make_shared<store::Order>();
    sl_order1->side = ESide::Buy;
    sl_order1->order_status = EOrderStatus::Untriggered;
    sl_cow_order->cur_order = (sl_order1);
    cow_position->tp_sl_order_mgr.sl_order_list_->ref_full_tp_sl_ts_order_ = sl_cow_order.get();
  }
  fill_order->stop_loss_x = 100;
  biz::AdjustTpSlTsBiz::AdjustPartialTpSlTs(pkg, cow_position.get());

  { cow_position->tp_sl_order_mgr.sl_order_list_->ref_full_tp_sl_ts_order_ = nullptr; }
  biz::AdjustTpSlTsBiz::AdjustPartialTpSlTs(pkg, cow_position.get());

  fill_order->side = ESide::Sell;
  {
    auto tp_cow_order = std::make_shared<store::CowOrder>();
    auto tp_order1 = std::make_shared<store::Order>();
    tp_order1->side = ESide::Buy;
    tp_order1->order_status = EOrderStatus::Untriggered;
    tp_cow_order->cur_order = (tp_order1);
    cow_position->tp_sl_order_mgr.tp_order_list_->ref_full_tp_sl_ts_order_ = tp_cow_order.get();
  }
  {
    auto sl_cow_order = std::make_shared<store::CowOrder>();
    auto sl_order1 = std::make_shared<store::Order>();
    sl_order1->side = ESide::Buy;
    sl_order1->order_status = EOrderStatus::Untriggered;
    sl_cow_order->cur_order = (sl_order1);
    cow_position->tp_sl_order_mgr.sl_order_list_->ref_full_tp_sl_ts_order_ = sl_cow_order.get();
  }
  biz::AdjustTpSlTsBiz::AdjustPartialTpSlTs(pkg, cow_position.get());

  // biz::order_id_t temp_order_id("aaa");
  // pkg->ref_future_cow_order_map_[temp_order_id] = nullptr;
  // {
  //   auto cow_order = std::make_shared<store::CowOrder>();
  //   temp_order_id.SetValue("bbb");
  //   pkg->ref_future_cow_order_map_[temp_order_id] = cow_order;
  // }
  // biz::AdjustTpSlTsBiz::AdjustPartialTpSlTs(pkg, cow_position);
}
