#include "src/biz_worker/service/trade/futures/modules/tpsltsbiz/tp_sl_ts_biz.hpp"

#include <boost/uuid/uuid_io.hpp>

#include "biz_worker/service/trade/futures/modules/tpsltsbiz/set_tp_sl_ts_biz.h"
#include "gtest/gtest.h"
#include "lib/stub.hpp"
#include "proto/gen/enums/ebizcode/biz_code.pb.h"
#include "proto/gen/svc/trading/req/create_order.pb.h"
#include "src/biz_worker/service/trade/store/per_worker_store.hpp"
#include "src/biz_worker/service/trade/store/user_data_manager.hpp"
#include "src/data/enum.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class TestTpSlTsBiz : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestCase() {}
  static void TearDownTestCase() { /*app::GlobalVarManager::Instance().coin_ = ECoin::UNKNOWN;*/ }

  biz::DraftPkg* GetDraftPkg() {
    store::PerWorkerStore* per_worker_store = new store::PerWorkerStore();
    auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
    auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
    auto symbol_config = std::make_shared<biz::SymbolConfig>();
    symbol_config->value_scale_ = 4;
    symbol_config->price_scale_ = 4;
    symbol_config->symbol_ = ESymbol::BTCUSDT;
    symbol_config->coin_ = ECoin::USDT;
    symbol_config->value_scale_ = 4;
    symbol_config->tick_size_x_ = 5;
    symbol_config->lot_size_x_ = 1000;
    symbol_config->price_limit_pnt_e6_ = 10;
    symbol_config->min_price_x_ = 100;
    symbol_config->min_qty_x_ = 100;
    symbol_config->max_price_x_ = 1000000000;
    symbol_config->max_order_book_qty_x_ = 10000'0000;
    symbol_config->max_new_order_qty_x_ = 10000'0000;
    symbol_config->max_new_market_order_qty_x_ = 1000'0000;
    symbol_config->contract_type_ = EContractType::LinearPerpetual;
    symbol_config_mgr->AddSymbolConfig(symbol_config);
    new_config_mgr->set_symbol_config_mgr(symbol_config_mgr);
    config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

    store::UserDataManager* user_data_manager_ = new store::UserDataManager();
    store::PerUserStore* per_user_store = user_data_manager_->GetUserData(user_id);
    // 初始化
    if (per_user_store == nullptr) {
      user_data_manager_->CreateUserData(user_id);
      per_user_store = user_data_manager_->GetUserData(user_id);
    }
    per_user_store->working_coins_[ECoin::USDC] = {};
    header->uid = user_id;
    draft_pkg = std::make_shared<biz::DraftPkg>(nullptr, header, per_worker_store, per_user_store);
    return draft_pkg.get();
  }

 protected:
  std::shared_ptr<tmock::CTradeAppMock> te;
  biz::DraftPkg::Ptr draft_pkg;
  biz::user_id_t user_id = 111111;
  std::shared_ptr<store::Header> header = std::make_shared<store::Header>();
};

TEST_F(TestTpSlTsBiz, CancelAllOnlyNormalStopOrders) {
  auto pkg = GetDraftPkg();
  svc::trading::req::CancelAllReq req_body_p;
  req_body_p.set_symbol(ESymbol::BTCUSDT);
  biz::order_id_t order_id(te->GenUUID().c_str());
  {
    auto cow_order = std::make_shared<store::CowOrder>();
    auto order1 = std::make_shared<store::Order>();
    order1->order_id = order_id;
    order1->stop_order_type = EStopOrderType::Stop;
    order1->side = ESide::Buy;
    order1->order_status = EOrderStatus::Filled;
    order1->symbol = ESymbol::BTCUSDT;
    order1->coin = ECoin::USDT;
    cow_order->cur_order = (order1);
    draft_pkg->u_store->all_future_working_order_map_by_order_id_[order_id] = cow_order.get();

    auto pz_p = std::make_shared<store::Position>();
    pz_p->symbol = ESymbol::BTCUSDT;
    pz_p->size = 100;
    pz_p->side = ESide::Buy;
    pz_p->user_id = user_id;
    auto cow_position = std::make_shared<store::CowPosition>(pz_p, EProductType::Futures);

    auto new_pz_p = std::make_shared<store::Position>();
    new_pz_p->symbol = ESymbol::BTCUSDT;
    new_pz_p->size = 150;
    new_pz_p->side = ESide::Buy;
    new_pz_p->user_id = user_id;
    cow_position->draft_position = (new_pz_p);

    auto per_coin_store = std::make_shared<store::PerCoinStore>(user_id, ECoin::USDT);
    auto per_symbol_store = std::make_shared<store::PerSymbolStore>(ESymbol::BTCUSDT, EPositionMode::MergedSingle);
    per_symbol_store->all_positions_[EPositionIndex::Single] = cow_position;
    per_symbol_store->mode_ = EPositionMode::MergedSingle;
    per_coin_store->working_future_symbols_[ESymbol::BTCUSDT] = per_symbol_store;
    pkg->u_store->working_coins_[ECoin::USDT] = per_coin_store;

    auto ret = biz::TpSlTsBiz::CancelAllOnlyNormalStopOrders(pkg, req_body_p);
    EXPECT_EQ(ret, error::ErrorCode::kErrorCodeNone);
  }
}

TEST_F(TestTpSlTsBiz, CancelAllTrailingStopOrders) {
  auto pkg = GetDraftPkg();
  svc::trading::req::CancelAllReq req_body_p;
  req_body_p.set_symbol(ESymbol::BTCUSDT);
  biz::order_id_t order_id(te->GenUUID().c_str());
  auto cow_order = std::make_shared<store::CowOrder>();
  auto order1 = std::make_shared<store::Order>();
  order1->order_id = order_id;
  order1->stop_order_type = EStopOrderType::TrailingStop;
  order1->side = ESide::Buy;
  order1->order_status = EOrderStatus::Filled;
  order1->symbol = ESymbol::BTCUSDT;
  order1->coin = ECoin::USDT;
  cow_order->cur_order = (order1);
  draft_pkg->u_store->all_future_working_order_map_by_order_id_[order_id] = cow_order.get();

  auto pz_p = std::make_shared<store::Position>();
  pz_p->symbol = ESymbol::BTCUSDT;
  pz_p->size = 100;
  pz_p->side = ESide::Buy;
  pz_p->user_id = user_id;
  auto cow_position = std::make_shared<store::CowPosition>(pz_p, EProductType::Futures);

  auto new_pz_p = std::make_shared<store::Position>();
  new_pz_p->symbol = ESymbol::BTCUSDT;
  new_pz_p->size = 150;
  new_pz_p->side = ESide::Buy;
  new_pz_p->user_id = user_id;
  cow_position->draft_position = (new_pz_p);

  auto per_coin_store = std::make_shared<store::PerCoinStore>(user_id, ECoin::USDT);
  auto per_symbol_store = std::make_shared<store::PerSymbolStore>(ESymbol::BTCUSDT, EPositionMode::MergedSingle);
  per_symbol_store->all_positions_[EPositionIndex::Single] = cow_position;
  per_symbol_store->mode_ = EPositionMode::MergedSingle;
  per_coin_store->working_future_symbols_[ESymbol::BTCUSDT] = per_symbol_store;
  pkg->u_store->working_coins_[ECoin::USDT] = per_coin_store;

  auto ret = biz::TpSlTsBiz::CancelAllTrailingStopOrders(pkg, req_body_p);
  EXPECT_EQ(ret, error::ErrorCode::kErrorCodeNone);
}

TEST_F(TestTpSlTsBiz, ValidatePartialTpSlTsFor) {
  auto pkg = GetDraftPkg();
  auto pz_p = std::make_shared<store::Position>();
  pz_p->symbol = ESymbol::BTCUSDT;
  pz_p->size = 100;
  pz_p->side = ESide::Buy;
  pz_p->user_id = user_id;

  (*pkg->w_store->future_symbol_index_price_map_)[ESymbol::BTCUSDT] = bbase::decimal::Decimal(1200);

  biz::ValidateSetTpSlTsEnter::Ptr enter_p = std::make_shared<biz::ValidateSetTpSlTsEnter>();
  enter_p->req_tp_trigger_by = ETriggerBy::IndexPrice;
  enter_p->optional_new_tp = 100'0000;
  auto res = biz::TpSlTsBiz::ValidatePartialTpSlTsFor(pkg, pz_p.get(), enter_p);
  EXPECT_NE(std::get<1>(res), 0);

  enter_p->optional_new_tp = {};
  enter_p->optional_new_ts = 1000;
  res = biz::TpSlTsBiz::ValidatePartialTpSlTsFor(pkg, pz_p.get(), enter_p);
  EXPECT_EQ(std::get<1>(res), 0);

  enter_p->req_trailing_active_x = 1000'0000;
  res = biz::TpSlTsBiz::ValidatePartialTpSlTsFor(pkg, pz_p.get(), enter_p);
  EXPECT_NE(std::get<1>(res), 0);
}

// 模拟从来没交易过，应当初始化一个SymbolStore; 而不是返回-1错误
TEST_F(TestTpSlTsBiz, SetTpSlTSEnsureInitSymbolStore) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);
  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Buy;
  FutureOneOfSetTpSlTsBuilder set_tpsl_build;
  set_tpsl_build.SetValue(user_id, coin, symbol, pz_index, ETriggerBy::LastPrice, 10100 * 1e4, /* tp */
                          ETriggerBy::LastPrice, 9900 * 1e4,                                   /* sl */
                          {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {});

  auto resp = user1.process(set_tpsl_build.Build());
  EXPECT_EQ(error::ErrorCode::kErrorCodePositionNotExists, resp->ret_code());
}

// 模拟从来没交易过，应当初始化一个SymbolStore; 对有效空仓返回 10001,  "can not set tp/sl/ts for zero position",
TEST_F(TestTpSlTsBiz, SetTpSlTSEnsureInitSymbolStoreEmptyPz) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);
  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  FutureOneOfSetTpSlTsBuilder set_tpsl_build;
  set_tpsl_build.SetValue(user_id, coin, symbol, pz_index, ETriggerBy::LastPrice, 10100 * 1e4, /* tp */
                          ETriggerBy::LastPrice, 9900 * 1e4,                                   /* sl */
                          {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {});

  auto resp = user1.process(set_tpsl_build.Build());
  EXPECT_EQ(error::ErrorCode::kErrorCodeCannotSetTpSlForZeroPz, resp->ret_code());
}
