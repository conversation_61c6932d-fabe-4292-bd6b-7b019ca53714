#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <iostream>
#include <memory>

#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/futures/modules/commonbiz/validation_biz.hpp"
#include "src/biz_worker/service/trade/store/position/cow_position.hpp"
#include "src/biz_worker/service/trade/store/position/raw_position.hpp"
#include "src/biz_worker/service/trade/store/user_data_manager.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class RollbackTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {}
  static void TearDownTestSuite() {}
};

TEST_F(RollbackTest, rollback_symbol_store_mode_USDT) {
  std::shared_ptr<const event::BizEvent> ev =
      std::make_shared<event::BizEvent>(123456, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
                                        event::EventDispatchType::kUnicastToUser, event::EventBizType::kAddMargin);
  std::shared_ptr<store::Header> header = std::make_shared<store::Header>();
  biz::DraftPkg draft_pkg(ev, header);
  auto per_user_store = std::make_shared<store::PerUserStore>(123456);
  draft_pkg.u_store = per_user_store.get();
  // USDT coin
  {
    auto coin = ECoin::USDT;
    auto per_coin_store = std::make_shared<store::PerCoinStore>(123456, coin);
    per_user_store->working_coins_[coin] = per_coin_store;
    /**
     * 正常场景1，需要修改symbol_store mode值
     * symbol_store mode = MergedSingle
     * position_array 0: Inactive, 1: Normal, 2: Normal
     */
    {
      auto symbol = ESymbol::BTCUSDT;
      auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, EPositionMode::MergedSingle);
      auto pz0 = std::make_shared<store::Position>();
      pz0->position_status = EPositionStatus::Inactive;
      pz0->symbol = symbol;
      pz0->coin = coin;
      auto cow_position0 = std::make_shared<store::CowPosition>(pz0, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Single] = cow_position0;

      auto pz1 = std::make_shared<store::Position>();
      pz1->position_status = EPositionStatus::Normal;
      auto cow_position1 = std::make_shared<store::CowPosition>(pz1, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Buy] = cow_position1;

      auto pz2 = std::make_shared<store::Position>();
      pz2->position_status = EPositionStatus::Normal;
      auto cow_position2 = std::make_shared<store::CowPosition>(pz2, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Sell] = cow_position2;

      per_coin_store->working_future_symbols_[symbol] = symbol_store;
      draft_pkg.ref_future_cow_positions_.emplace_back(cow_position0.get());
    }

    /**
     * 正常场景2，需要修改symbol_store mode值
     * symbol_store mode = BothSide
     * position_array 0: Normal, 1: Inactive, 2: Inactive
     */
    {
      auto symbol = ESymbol::ETHUSDT;
      auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, EPositionMode::BothSide);
      auto pz0 = std::make_shared<store::Position>();
      pz0->position_status = EPositionStatus::Normal;
      auto cow_position0 = std::make_shared<store::CowPosition>(pz0, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Single] = cow_position0;

      auto pz1 = std::make_shared<store::Position>();
      pz1->position_status = EPositionStatus::Inactive;
      pz1->symbol = symbol;
      pz1->coin = coin;
      auto cow_position1 = std::make_shared<store::CowPosition>(pz1, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Buy] = cow_position1;

      auto pz2 = std::make_shared<store::Position>();
      pz2->position_status = EPositionStatus::Inactive;
      pz2->symbol = symbol;
      pz2->coin = coin;
      auto cow_position2 = std::make_shared<store::CowPosition>(pz2, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Sell] = cow_position2;

      per_coin_store->working_future_symbols_[symbol] = symbol_store;
      draft_pkg.ref_future_cow_positions_.emplace_back(cow_position1.get());
      draft_pkg.ref_future_cow_positions_.emplace_back(cow_position2.get());
    }

    /**
     * 正常场景3，无需修改symbol_store mode值
     * symbol_store mode = BothSide
     * position_array 0: , 1: Normal, 2: Normal
     */
    {
      auto symbol = ESymbol::EOSUSDT;
      auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, EPositionMode::BothSide);
      auto pz0 = std::make_shared<store::Position>();
      pz0->position_status = EPositionStatus::Inactive;
      pz0->symbol = symbol;
      pz0->coin = coin;
      auto cow_position0 = std::make_shared<store::CowPosition>(pz0, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Single] = cow_position0;

      auto pz1 = std::make_shared<store::Position>();
      pz1->position_status = EPositionStatus::Normal;
      auto cow_position1 = std::make_shared<store::CowPosition>(pz1, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Buy] = cow_position1;

      auto pz2 = std::make_shared<store::Position>();
      pz2->position_status = EPositionStatus::Normal;
      auto cow_position2 = std::make_shared<store::CowPosition>(pz2, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Sell] = cow_position2;

      per_coin_store->working_future_symbols_[symbol] = symbol_store;
      draft_pkg.ref_future_cow_positions_.emplace_back(cow_position0.get());
    }

    /**
     * 正常场景4，无需修改symbol_store mode值
     * symbol_store mode = MergedSingle
     * position_array 0: Normal, 1: Inactive, 2: Inactive
     */
    {
      auto symbol = ESymbol(38);
      auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, EPositionMode::MergedSingle);
      auto pz0 = std::make_shared<store::Position>();
      pz0->position_status = EPositionStatus::Normal;
      pz0->symbol = symbol;
      pz0->coin = coin;
      auto cow_position0 = std::make_shared<store::CowPosition>(pz0, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Single] = cow_position0;

      auto pz1 = std::make_shared<store::Position>();
      pz1->position_status = EPositionStatus::Inactive;
      auto cow_position1 = std::make_shared<store::CowPosition>(pz1, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Buy] = cow_position1;

      auto pz2 = std::make_shared<store::Position>();
      pz2->position_status = EPositionStatus::Inactive;
      auto cow_position2 = std::make_shared<store::CowPosition>(pz2, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Sell] = cow_position2;

      per_coin_store->working_future_symbols_[symbol] = symbol_store;
      draft_pkg.ref_future_cow_positions_.emplace_back(cow_position0.get());
    }

    /**
     * 异常场景1
     * symbol_store mode = BothSide
     * position_array 0: Normal, 1: Inactive, 2: Normal
     */
    {
      auto symbol = ESymbol::XRPUSDT;
      auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, EPositionMode::BothSide);
      auto pz0 = std::make_shared<store::Position>();
      pz0->position_status = EPositionStatus::Normal;
      pz0->symbol = symbol;
      pz0->coin = coin;
      auto cow_position0 = std::make_shared<store::CowPosition>(pz0, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Single] = cow_position0;

      auto pz1 = std::make_shared<store::Position>();
      pz1->position_status = EPositionStatus::Inactive;
      auto cow_position1 = std::make_shared<store::CowPosition>(pz1, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Buy] = cow_position1;

      auto pz2 = std::make_shared<store::Position>();
      pz2->position_status = EPositionStatus::Normal;
      auto cow_position2 = std::make_shared<store::CowPosition>(pz2, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Sell] = cow_position2;

      per_coin_store->working_future_symbols_[symbol] = symbol_store;
      draft_pkg.ref_future_cow_positions_.emplace_back(cow_position0.get());
    }

    /**
     * 异常场景2
     * symbol_store mode = BothSide
     * position_array 0: Normal, 1: Normal, 2: Inactive
     */
    {
      auto symbol = ESymbol(127);
      auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, EPositionMode::BothSide);
      auto pz0 = std::make_shared<store::Position>();
      pz0->position_status = EPositionStatus::Normal;
      pz0->symbol = symbol;
      pz0->coin = coin;
      auto cow_position0 = std::make_shared<store::CowPosition>(pz0, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Single] = cow_position0;

      auto pz1 = std::make_shared<store::Position>();
      pz1->position_status = EPositionStatus::Normal;
      auto cow_position1 = std::make_shared<store::CowPosition>(pz1, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Buy] = cow_position1;

      auto pz2 = std::make_shared<store::Position>();
      pz2->position_status = EPositionStatus::Inactive;
      auto cow_position2 = std::make_shared<store::CowPosition>(pz2, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Sell] = cow_position2;

      per_coin_store->working_future_symbols_[symbol] = symbol_store;
      draft_pkg.ref_future_cow_positions_.emplace_back(cow_position0.get());
    }

    /**
     * 异常场景3
     * symbol_store mode = MergedSingle
     * position_array 0: Normal, 1: Normal, 2: Inactive
     */
    {
      auto symbol = ESymbol(27);
      auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, EPositionMode::MergedSingle);
      auto pz0 = std::make_shared<store::Position>();
      pz0->symbol = symbol;
      pz0->coin = coin;
      pz0->position_status = EPositionStatus::Normal;
      auto cow_position0 = std::make_shared<store::CowPosition>(pz0, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Single] = cow_position0;

      auto pz1 = std::make_shared<store::Position>();
      pz1->position_status = EPositionStatus::Normal;
      auto cow_position1 = std::make_shared<store::CowPosition>(pz1, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Buy] = cow_position1;

      auto pz2 = std::make_shared<store::Position>();
      pz2->position_status = EPositionStatus::Inactive;
      auto cow_position2 = std::make_shared<store::CowPosition>(pz2, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Sell] = cow_position2;

      per_coin_store->working_future_symbols_[symbol] = symbol_store;
      draft_pkg.ref_future_cow_positions_.emplace_back(cow_position0.get());
    }

    /**
     * 异常场景4
     * symbol_store mode = MergedSingle
     * position_array 0: Normal, 1: Inactive, 2: Normal
     */
    {
      auto symbol = ESymbol(28);
      auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, EPositionMode::MergedSingle);
      auto pz0 = std::make_shared<store::Position>();
      pz0->symbol = symbol;
      pz0->coin = coin;
      pz0->position_status = EPositionStatus::Normal;
      auto cow_position0 = std::make_shared<store::CowPosition>(pz0, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Single] = cow_position0;

      auto pz1 = std::make_shared<store::Position>();
      pz1->position_status = EPositionStatus::Inactive;
      auto cow_position1 = std::make_shared<store::CowPosition>(pz1, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Buy] = cow_position1;

      auto pz2 = std::make_shared<store::Position>();
      pz2->position_status = EPositionStatus::Normal;
      auto cow_position2 = std::make_shared<store::CowPosition>(pz2, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Sell] = cow_position2;

      per_coin_store->working_future_symbols_[symbol] = symbol_store;
      draft_pkg.ref_future_cow_positions_.emplace_back(cow_position0.get());
    }

    draft_pkg.RollbackAll();
    per_coin_store->working_future_symbols_[ESymbol::BTCUSDT]->mode_ = EPositionMode::BothSide;
    per_coin_store->working_future_symbols_[ESymbol::ETHUSDT]->mode_ = EPositionMode::MergedSingle;
    per_coin_store->working_future_symbols_[ESymbol::EOSUSDT]->mode_ = EPositionMode::BothSide;
    per_coin_store->working_future_symbols_[ESymbol(38)]->mode_ = EPositionMode::MergedSingle;
    per_coin_store->working_future_symbols_[ESymbol::XRPUSDT]->mode_ = EPositionMode::BothSide;
    per_coin_store->working_future_symbols_[ESymbol(127)]->mode_ = EPositionMode::BothSide;
    per_coin_store->working_future_symbols_[ESymbol(27)]->mode_ = EPositionMode::MergedSingle;
    per_coin_store->working_future_symbols_[ESymbol(28)]->mode_ = EPositionMode::MergedSingle;
  }
}

TEST_F(RollbackTest, rollback_symbol_store_mode_USDC) {
  std::shared_ptr<const event::BizEvent> ev =
      std::make_shared<event::BizEvent>(123456, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
                                        event::EventDispatchType::kUnicastToUser, event::EventBizType::kAddMargin);
  std::shared_ptr<store::Header> header = std::make_shared<store::Header>();
  biz::DraftPkg draft_pkg(ev, header);
  auto per_user_store = std::make_shared<store::PerUserStore>(123456);
  draft_pkg.u_store = per_user_store.get();
  // USDC coin
  {
    auto coin = ECoin::USDC;
    auto per_coin_store = std::make_shared<store::PerCoinStore>(123456, coin);
    per_user_store->working_coins_[coin] = per_coin_store;
    /**
     * 正常场景1
     * symbol_store mode = MergedSingle
     * position_array 0: Normal, 1: Inactive, 2: Inactive
     */
    {
      auto symbol = ESymbol(45);
      auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, EPositionMode::MergedSingle);
      auto pz0 = std::make_shared<store::Position>();
      pz0->position_status = EPositionStatus::Normal;
      pz0->symbol = symbol;
      pz0->coin = coin;
      auto cow_position0 = std::make_shared<store::CowPosition>(pz0, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Single] = cow_position0;

      auto pz1 = std::make_shared<store::Position>();
      pz1->position_status = EPositionStatus::Inactive;
      auto cow_position1 = std::make_shared<store::CowPosition>(pz1, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Buy] = cow_position1;

      auto pz2 = std::make_shared<store::Position>();
      pz2->position_status = EPositionStatus::Inactive;
      auto cow_position2 = std::make_shared<store::CowPosition>(pz2, EProductType::Futures);
      symbol_store->all_positions_[EPositionIndex::Sell] = cow_position2;

      per_coin_store->working_future_symbols_[symbol] = symbol_store;
      draft_pkg.ref_future_cow_positions_.emplace_back(cow_position0.get());
    }

    draft_pkg.RollbackAll();
    per_coin_store->working_future_symbols_[ESymbol(45)]->mode_ = EPositionMode::MergedSingle;
  }
}
