#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <iostream>
#include <memory>

#include "src/biz_worker/service/trade/futures/modules/commonbiz/validation_biz.hpp"
#include "src/biz_worker/service/trade/store/user_data_manager.hpp"
#include "test/biz/trade/future/func/draft_pkg_builder.h"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class CommonBizTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {}
  static void TearDownTestSuite() {}
};

TEST_F(CommonBizTest, position_status_branch_test) {
  bool ignore_invalid_pz = true;
  auto position = std::make_shared<store::Position>();
  position->position_status = EPositionStatus::Inactive;

  bool ret = biz::commonbiz::ValidationBiz::CheckPzIsNormalStatus(position.get(), ignore_invalid_pz);
  EXPECT_TRUE(ret);

  position->position_status = EPositionStatus::PendingLiq;
  ret = biz::commonbiz::ValidationBiz::CheckPzIsNormalStatus(position.get(), ignore_invalid_pz);
  EXPECT_FALSE(ret);

  position->position_status = EPositionStatus::Normal;
  ret = biz::commonbiz::ValidationBiz::CheckPzIsNormalStatus(position.get(), ignore_invalid_pz);
  EXPECT_TRUE(ret);
}

TEST_F(CommonBizTest, pz_idx_match_mode) {
  bool ret = biz::commonbiz::ValidationBiz::CheckPzIdxMatchMode(nullptr, ESymbol::BTCUSDT, EPositionIndex::Single);
  EXPECT_FALSE(ret);

  {
    // common fields init
    biz::user_id_t user_id = 65530;
    biz::account_id_t account_id = 65535;
    biz::coin_t coin = ECoin::USDT;
    biz::symbol_t symbol = ESymbol::BTCUSDT;

    // header init
    auto header = std::make_shared<store::Header>();
    header->op_from = "op_from_ut";
    header->user_ip = "127.0.0.1";
    header->uid = user_id;
    header->coin = coin;
    header->account_id = account_id;
    header->symbol = symbol;

    store::PerWorkerStore* per_worker_store = new store::PerWorkerStore();
    auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
    auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
    auto symbol_config = std::make_shared<biz::SymbolConfig>();
    symbol_config->value_scale_ = 8;
    symbol_config->price_scale_ = 8;
    symbol_config->symbol_ = ESymbol::BTCUSDT;
    symbol_config->coin_ = ECoin::USDT;
    symbol_config->value_scale_ = 8;
    symbol_config->tick_size_x_ = 5;
    symbol_config->price_limit_pnt_e6_ = 10;
    symbol_config_mgr->AddSymbolConfig(symbol_config);
    new_config_mgr->set_symbol_config_mgr(symbol_config_mgr);
    config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

    store::UserDataManager* user_data_manager_ = new store::UserDataManager();
    store::PerUserStore* per_user_store = user_data_manager_->GetUserData(user_id);
    // 初始化
    if (per_user_store == nullptr) {
      user_data_manager_->CreateUserData(user_id);
      per_user_store = user_data_manager_->GetUserData(user_id);
    }
    per_user_store->working_coins_[ECoin::USDC] = {};

    biz::DraftPkg::Ptr draft_pkg = std::make_shared<biz::DraftPkg>(nullptr, header, per_worker_store, per_user_store);

    ret = biz::commonbiz::ValidationBiz::CheckPzIdxMatchMode(draft_pkg.get(), symbol, EPositionIndex::Single);
    EXPECT_FALSE(ret);
    auto ret_val =
        biz::commonbiz::ValidationBiz::CheckPzIdxMatchModeWithReportError(draft_pkg.get(), symbol, EPositionIndex::Buy);
    EXPECT_EQ(ret_val, error::kErrorCodeDefault);
    EXPECT_TRUE(draft_pkg->HasErr());

    store::PerCoinStore::Ptr per_coin_store = draft_pkg->u_store->working_coins_[coin];
    if (per_coin_store == nullptr) {
      per_coin_store = std::make_shared<store::PerCoinStore>(draft_pkg->uid(), coin);
      draft_pkg->u_store->working_coins_[coin] = per_coin_store;
    }
    store::PerSymbolStore::Ptr per_symbol_store =
        draft_pkg->u_store->working_coins_[coin]->working_future_symbols_[symbol];
    if (per_symbol_store == nullptr) {
      per_symbol_store = std::make_shared<store::PerSymbolStore>(symbol, EPositionMode::MergedSingle);
      draft_pkg->u_store->working_coins_[coin]->working_future_symbols_[symbol] = per_symbol_store;
    }
    ret = biz::commonbiz::ValidationBiz::CheckPzIdxMatchMode(draft_pkg.get(), symbol, EPositionIndex::Buy);
    EXPECT_FALSE(ret);
    ret_val =
        biz::commonbiz::ValidationBiz::CheckPzIdxMatchModeWithReportError(draft_pkg.get(), symbol, EPositionIndex::Buy);
    EXPECT_EQ(ret_val, error::kErrorCodePositionNotExists);
    EXPECT_TRUE(draft_pkg->HasErr());
    auto tmp = draft_pkg->u_store->working_coins_[coin]->working_future_symbols_[symbol];
    tmp->mode_ = EPositionMode::BothSide;
    ret = biz::commonbiz::ValidationBiz::CheckPzIdxMatchMode(draft_pkg.get(), symbol, EPositionIndex::Single);
    EXPECT_FALSE(ret);
    ret_val = biz::commonbiz::ValidationBiz::CheckPzIdxMatchModeWithReportError(draft_pkg.get(), symbol,
                                                                                EPositionIndex::Single);
    EXPECT_EQ(ret_val, error::kErrorCodePositionNotExists);
    EXPECT_TRUE(draft_pkg->HasErr());
  }
}

TEST_F(CommonBizTest, check_symbol_can_trade) {
  // common fields init
  biz::user_id_t user_id = 65530;
  biz::account_id_t account_id = 65535;
  biz::coin_t coin = ECoin::USDT;
  biz::symbol_t symbol = ESymbol::BTCUSDT;

  // header init
  auto header = std::make_shared<store::Header>();
  header->op_from = "op_from_ut";
  header->user_ip = "127.0.0.1";
  header->uid = user_id;
  header->coin = coin;
  header->account_id = account_id;
  header->symbol = symbol;

  store::PerWorkerStore* per_worker_store = new store::PerWorkerStore();
  auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
  auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
  auto symbol_config = std::make_shared<biz::SymbolConfig>();
  symbol_config->value_scale_ = 8;
  symbol_config->price_scale_ = 8;
  symbol_config->symbol_ = ESymbol::BTCUSDT;
  symbol_config->coin_ = ECoin::USDT;
  symbol_config->value_scale_ = 8;
  symbol_config->tick_size_x_ = 5;
  symbol_config->price_limit_pnt_e6_ = 10;
  symbol_config->contract_type_ = EContractType::LinearPerpetual;
  symbol_config_mgr->AddSymbolConfig(symbol_config);
  auto symbol_config2 = std::make_shared<biz::SymbolConfig>();
  symbol_config2->value_scale_ = 8;
  symbol_config2->price_scale_ = 8;
  symbol_config2->symbol_ = ESymbol::ETHUSDT;
  symbol_config2->coin_ = ECoin::USDT;
  symbol_config2->value_scale_ = 8;
  symbol_config2->tick_size_x_ = 5;
  symbol_config2->price_limit_pnt_e6_ = 10;
  symbol_config2->contract_type_ = EContractType::LinearPerpetual;
  auto symbol_config3 = std::make_shared<biz::SymbolConfig>();
  symbol_config3->value_scale_ = 8;
  symbol_config3->price_scale_ = 8;
  symbol_config3->symbol_ = ESymbol::EOSUSDT;
  symbol_config3->symbol_name_ = "EOSUSDT";
  symbol_config3->coin_ = ECoin::USDT;
  symbol_config3->value_scale_ = 8;
  symbol_config3->tick_size_x_ = 5;
  symbol_config3->price_limit_pnt_e6_ = 10;
  symbol_config3->contract_type_ = EContractType::LinearPerpetual;
  symbol_config_mgr->AddSymbolConfig(symbol_config3);
  auto symbol_config4 = std::make_shared<biz::SymbolConfig>();
  symbol_config4->value_scale_ = 8;
  symbol_config4->price_scale_ = 8;
  symbol_config4->symbol_ = ESymbol::XRPUSDT;
  symbol_config4->symbol_name_ = "XRPUSDT";
  symbol_config4->coin_ = ECoin::USDT;
  symbol_config4->value_scale_ = 8;
  symbol_config4->tick_size_x_ = 5;
  symbol_config4->price_limit_pnt_e6_ = 10;
  symbol_config4->is_enable_all_shard_ = true;
  symbol_config4->contract_type_ = EContractType::LinearPerpetual;
  symbol_config_mgr->AddSymbolConfig(symbol_config4);

  new_config_mgr->set_symbol_config_mgr(symbol_config_mgr);
  config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

  per_worker_store->contract_status_map_[ESymbol::BTCUSDT] = EContractStatus::Closed;
  per_worker_store->contract_status_map_[ESymbol::ETHUSDT] = EContractStatus::Settling;
  per_worker_store->contract_status_map_[ESymbol::EOSUSDT] = EContractStatus::Trading;
  per_worker_store->contract_status_map_[ESymbol::XRPUSDT] = EContractStatus::Trading;

  store::UserDataManager* user_data_manager_ = new store::UserDataManager();
  store::PerUserStore* per_user_store = user_data_manager_->GetUserData(user_id);
  // 初始化
  if (per_user_store == nullptr) {
    user_data_manager_->CreateUserData(user_id);
    per_user_store = user_data_manager_->GetUserData(user_id);
  }
  per_user_store->working_coins_[ECoin::USDC] = {};

  // Symbol not exists
  {
    biz::DraftPkg::Ptr draft_pkg = std::make_shared<biz::DraftPkg>(nullptr, header, per_worker_store, per_user_store);
    auto err = biz::commonbiz::ValidationBiz::CheckSymbolCanTrade(draft_pkg.get(), ESymbol(74));
    EXPECT_EQ(err, error::kErrorCodeSymbolNotExists);
  }

  // Symbol not in trading status
  {
    biz::DraftPkg::Ptr draft_pkg = std::make_shared<biz::DraftPkg>(nullptr, header, per_worker_store, per_user_store);
    auto err = biz::commonbiz::ValidationBiz::CheckSymbolCanTrade(draft_pkg.get(), ESymbol::BTCUSDT);
    EXPECT_EQ(err, error::kErrorCodeSymbolNotInTradingStatus);
  }

  {
    // 灰度阶段:拦截流量
    biz::DraftPkg::Ptr draft_pkg = std::make_shared<biz::DraftPkg>(nullptr, header, per_worker_store, per_user_store);
    auto err = biz::commonbiz::ValidationBiz::CheckSymbolCanTrade(draft_pkg.get(), ESymbol::EOSUSDT);
    EXPECT_EQ(err, error::kErrorCodeSymbolNotInTradingStatus);
  }

  {
    biz::DraftPkg::Ptr draft_pkg = std::make_shared<biz::DraftPkg>(nullptr, header, per_worker_store, per_user_store);
    // 全量阶段:上币后,放过所有流量,不拦截
    auto err = biz::commonbiz::ValidationBiz::CheckSymbolCanTrade(draft_pkg.get(), ESymbol::XRPUSDT);
    EXPECT_EQ(err, error::kErrorCodeSuccess);
    EXPECT_TRUE(!draft_pkg->HasErr());

    // 模拟上币时间
    auto listing_open_trade_config = std::make_shared<config::ListingOpenTradeConfigData>();
    auto listing_open_trade_config_dto = std::make_shared<config::ListingOpenTradeConfigDTO>();
    listing_open_trade_config_dto->open_trade_time_ms = bbase::utils::Time::GetTimeMs() * 2;
    listing_open_trade_config->data["XRPUSDT"] = listing_open_trade_config_dto;
    new_config_mgr->common_config_svc()->set_listing_open_trade_time_data(listing_open_trade_config);

    // 模拟做市商
    auto external_market_makers_config = std::make_shared<config::ExternalMarketMakersConfig>();
    external_market_makers_config->data["XRPUSDT"].insert(user_id);
    new_config_mgr->common_config_svc()->set_external_market_makers_config(external_market_makers_config);

    // 全量阶段:上币前,做市商+PostOnly订单,不拦截
    auto tif = ETimeInForce::PostOnly;
    bool is_allow_tif = tif == ETimeInForce::PostOnly;
    err = biz::commonbiz::ValidationBiz::CheckSymbolCanTrade(draft_pkg.get(), ESymbol::XRPUSDT, false, is_allow_tif);
    EXPECT_EQ(err, error::kErrorCodeSuccess);
    EXPECT_TRUE(!draft_pkg->HasErr());

    // 全量阶段:上币前,做市商+非PostOnly订单,拦截
    tif = ETimeInForce::GoodTillCancel;
    is_allow_tif = tif == ETimeInForce::PostOnly;
    err = biz::commonbiz::ValidationBiz::CheckSymbolCanTrade(draft_pkg.get(), ESymbol::XRPUSDT, false, is_allow_tif);
    EXPECT_EQ(err, error::kEcOnlyPostOnlyOrdersAreAvailable);
  }
}

TEST_F(CommonBizTest, CommonFunc) {
  // common fields init
  biz::user_id_t user_id = 65530;
  biz::account_id_t account_id = 65535;
  biz::coin_t coin = ECoin::USDT;

  // header init
  auto header = std::make_shared<store::Header>();
  header->op_from = "op_from_ut";
  header->user_ip = "127.0.0.1";
  header->uid = user_id;
  header->coin = coin;
  header->account_id = account_id;
  header->symbol = ESymbol::BTCUSDT;

  store::PerWorkerStore* per_worker_store = new store::PerWorkerStore();
  auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
  auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
  auto symbol_config = std::make_shared<biz::SymbolConfig>();
  symbol_config->value_scale_ = 8;
  symbol_config->price_scale_ = 8;
  symbol_config->symbol_ = ESymbol::BTCUSDT;
  symbol_config->symbol_name_ = "BTCUSDT";
  symbol_config->coin_ = ECoin::USDT;
  symbol_config->value_scale_ = 8;
  symbol_config->tick_size_x_ = 5;
  symbol_config->price_limit_pnt_e6_ = 10;
  symbol_config->enable_uid_list = {65530, 65535};
  symbol_config->contract_status_ = EContractStatus::Trading;
  symbol_config_mgr->AddSymbolConfig(symbol_config);
  auto symbol_config2 = std::make_shared<biz::SymbolConfig>();
  symbol_config2->value_scale_ = 8;
  symbol_config2->price_scale_ = 8;
  symbol_config2->symbol_ = ESymbol(45);
  symbol_config2->symbol_name_ = "BTCPERP";
  symbol_config2->coin_ = ECoin::USDC;
  symbol_config2->value_scale_ = 8;
  symbol_config2->tick_size_x_ = 5;
  symbol_config2->price_limit_pnt_e6_ = 10;
  symbol_config2->contract_status_ = EContractStatus::Trading;
  symbol_config2->contract_type_ = EContractType::InversePerpetual;
  symbol_config_mgr->AddSymbolConfig(symbol_config2);
  auto symbol_config3 = std::make_shared<biz::SymbolConfig>();
  symbol_config3->value_scale_ = 8;
  symbol_config3->price_scale_ = 8;
  symbol_config3->symbol_ = ESymbol::XRPUSDT;
  symbol_config3->symbol_name_ = "XRPUSDT";
  symbol_config3->coin_ = ECoin::USDT;
  symbol_config3->value_scale_ = 8;
  symbol_config3->tick_size_x_ = 5;
  symbol_config3->price_limit_pnt_e6_ = 10;
  symbol_config3->is_enable_all_shard_ = true;
  symbol_config3->contract_status_ = EContractStatus::Trading;
  symbol_config3->contract_type_ = EContractType::LinearPerpetual;
  symbol_config_mgr->AddSymbolConfig(symbol_config3);
  auto symbol_config4 = std::make_shared<biz::SymbolConfig>();
  symbol_config4->value_scale_ = 8;
  symbol_config4->price_scale_ = 8;
  symbol_config4->symbol_ = ESymbol(27);
  symbol_config4->symbol_name_ = "ADAUSDT";
  symbol_config4->coin_ = ECoin::USDT;
  symbol_config4->value_scale_ = 8;
  symbol_config4->tick_size_x_ = 5;
  symbol_config4->price_limit_pnt_e6_ = 10;
  symbol_config4->contract_status_ = EContractStatus::Settling;
  symbol_config4->contract_type_ = EContractType::LinearPerpetual;
  symbol_config_mgr->AddSymbolConfig(symbol_config4);
  auto symbol_config5 = std::make_shared<biz::SymbolConfig>();
  symbol_config5->value_scale_ = 8;
  symbol_config5->price_scale_ = 8;
  symbol_config5->symbol_ = ESymbol(12);
  symbol_config5->symbol_name_ = "BTCUSDH21";
  symbol_config5->coin_ = ECoin::BTC;
  symbol_config5->value_scale_ = 8;
  symbol_config5->tick_size_x_ = 5;
  symbol_config5->price_limit_pnt_e6_ = 10;
  symbol_config5->enable_uid_list = {65530};
  symbol_config5->contract_status_ = EContractStatus::Trading;
  symbol_config5->contract_type_ = EContractType::LinearFutures;
  symbol_config5->settle_time_e9_ = bbase::utils::Time::GetTimeNs() + 36000 * 1e9;
  symbol_config_mgr->AddSymbolConfig(symbol_config5);
  auto symbol_config6 = std::make_shared<biz::SymbolConfig>();
  symbol_config6->value_scale_ = 8;
  symbol_config6->price_scale_ = 8;
  symbol_config6->symbol_ = ESymbol(16);
  symbol_config6->symbol_name_ = "BTCUSDH22";
  symbol_config6->coin_ = ECoin::BTC;
  symbol_config6->value_scale_ = 8;
  symbol_config6->tick_size_x_ = 5;
  symbol_config6->price_limit_pnt_e6_ = 10;
  symbol_config6->enable_uid_list = {65530};
  symbol_config6->contract_status_ = EContractStatus::Trading;
  symbol_config6->contract_type_ = EContractType::LinearFutures;
  symbol_config6->settle_time_e9_ = bbase::utils::Time::GetTimeNs() - 36000 * 1e9;
  symbol_config_mgr->AddSymbolConfig(symbol_config6);
  auto symbol_config7 = std::make_shared<biz::SymbolConfig>();
  symbol_config7->value_scale_ = 8;
  symbol_config7->price_scale_ = 8;
  symbol_config7->symbol_ = ESymbol(28);
  symbol_config7->symbol_name_ = "DOTUSDT";
  symbol_config7->coin_ = ECoin::USDT;
  symbol_config7->value_scale_ = 8;
  symbol_config7->tick_size_x_ = 5;
  symbol_config7->price_limit_pnt_e6_ = 10;
  symbol_config7->enable_uid_list = {65530};
  symbol_config7->contract_status_ = EContractStatus::Closed;
  symbol_config7->contract_type_ = EContractType::LinearPerpetual;
  symbol_config_mgr->AddSymbolConfig(symbol_config7);
  auto symbol_config8 = std::make_shared<biz::SymbolConfig>();
  symbol_config8->value_scale_ = 8;
  symbol_config8->price_scale_ = 8;
  symbol_config8->symbol_ = ESymbol::BTCUSD;
  symbol_config8->symbol_name_ = "BTCUSD";
  symbol_config8->coin_ = ECoin::USD;
  symbol_config8->value_scale_ = 8;
  symbol_config8->tick_size_x_ = 5;
  symbol_config8->price_limit_pnt_e6_ = 10;
  symbol_config8->contract_status_ = EContractStatus::Trading;
  symbol_config8->contract_type_ = EContractType::InversePerpetual;
  symbol_config8->is_enable_all_shard_ = true;
  symbol_config_mgr->AddSymbolConfig(symbol_config8);
  new_config_mgr->set_symbol_config_mgr(symbol_config_mgr);
  config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

  per_worker_store->UpdateContractInfo();

  store::UserDataManager* user_data_manager_ = new store::UserDataManager();
  store::PerUserStore* per_user_store = user_data_manager_->GetUserData(user_id);
  // 初始化
  if (per_user_store == nullptr) {
    user_data_manager_->CreateUserData(user_id);
    per_user_store = user_data_manager_->GetUserData(user_id);
  }
  per_user_store->working_coins_[ECoin::USDC] = {};

  biz::DraftPkg::Ptr draft_pkg = std::make_shared<biz::DraftPkg>(nullptr, header, per_worker_store, per_user_store);

  {
    // CheckCategoryIsValid
    std::int32_t ret;
    ret = biz::commonbiz::CheckCategoryIsValid(draft_pkg.get(), "linear");
    EXPECT_EQ(ret, 0);
    ret = biz::commonbiz::CheckCategoryIsValid(draft_pkg.get(), "inverse");
    EXPECT_EQ(ret, error::kErrorCodeSuccess);
  }

  {
    // VerifyUserCanOperateSymbol
    std::int32_t ret;
    ret = biz::commonbiz::VerifyUserCanOperateSymbol(draft_pkg.get(), ESymbol::BTCUSDT);
    EXPECT_EQ(ret, error::kErrorCodeSuccess);
    ret = biz::commonbiz::VerifyUserCanOperateSymbol(draft_pkg.get(), ESymbol(45));
    EXPECT_EQ(ret, error::kErrorCodeParamsError);
    ret = biz::commonbiz::VerifyUserCanOperateSymbol(draft_pkg.get(), ESymbol::XRPUSDT);
    EXPECT_EQ(ret, error::kErrorCodeSuccess);
    ret = biz::commonbiz::VerifyUserCanOperateSymbol(draft_pkg.get(), ESymbol(27));
    EXPECT_EQ(ret, error::kErrorCodeParamsError);
  }
  {
    // CheckSymbolIsValid
    biz::symbol_t symbol{};
    auto ret = biz::commonbiz::CheckSymbolIsValid(draft_pkg.get(), "BITUSDT", symbol);
    EXPECT_EQ(ret, error::kErrorCodeParamsError);
    ret = biz::commonbiz::CheckSymbolIsValid(draft_pkg.get(), "BTC-PERP", symbol);
    EXPECT_EQ(ret, error::kErrorCodeParamsError);
  }
  {
    std::int32_t ret;
    biz::symbol_t symbol{};
    // 无效symbol (改到CheckSymbolIsValid case检查)
    // ret = biz::commonbiz::CheckSymbolIsValid(draft_pkg.get(), "BITUSDT", symbol);
    // EXPECT_EQ(ret, error::kErrorCodeParamsError);
    ret = biz::commonbiz::CheckUserCanTradeSymbol(draft_pkg.get(), "ADAUSDT", symbol);
    EXPECT_EQ(ret, error::kErrorCodeSymbolNotInTradingStatus);
    ret = biz::commonbiz::CheckUserCanTradeSymbol(draft_pkg.get(), "DOTUSDT", symbol);
    EXPECT_EQ(ret, error::kErrorCodeSymbolNotInTradingStatus);
    ret = biz::commonbiz::CheckUserCanTradeSymbol(draft_pkg.get(), "BTCUSDH21", symbol);
    EXPECT_EQ(symbol, ESymbol(12));
    EXPECT_EQ(ret, error::kErrorCodeSuccess);
    ret = biz::commonbiz::CheckUserCanTradeSymbol(draft_pkg.get(), "BTCUSD", symbol);
    EXPECT_EQ(ret, error::kErrorCodeSuccess);
  }
}

TEST_F(CommonBizTest, CommonFunc2) {
  // common fields init
  biz::user_id_t user_id = 65530;
  biz::account_id_t account_id = 65535;
  biz::coin_t coin = ECoin::USDT;
  biz::symbol_t symbol = ESymbol::BTCUSDT;

  // header init
  auto header = std::make_shared<store::Header>();
  header->op_from = "op_from_ut";
  header->user_ip = "127.0.0.1";
  header->uid = user_id;
  header->coin = coin;
  header->account_id = account_id;
  header->symbol = symbol;

  store::PerWorkerStore* per_worker_store = new store::PerWorkerStore();
  auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
  auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
  auto symbol_config = std::make_shared<biz::SymbolConfig>();
  symbol_config->value_scale_ = 8;
  symbol_config->price_scale_ = 8;
  symbol_config->symbol_ = ESymbol::BTCUSDT;
  symbol_config->symbol_name_ = "BTCUSDT";
  symbol_config->coin_ = ECoin::USDT;
  symbol_config->value_scale_ = 8;
  symbol_config->tick_size_x_ = 5;
  symbol_config->price_limit_pnt_e6_ = 10;
  symbol_config->enable_uid_list = {65530, 65535};
  symbol_config_mgr->AddSymbolConfig(symbol_config);
  auto symbol_config2 = std::make_shared<biz::SymbolConfig>();
  symbol_config2->value_scale_ = 8;
  symbol_config2->price_scale_ = 8;
  symbol_config2->symbol_ = ESymbol(45);
  symbol_config2->symbol_name_ = "BTCPERP";
  symbol_config2->coin_ = ECoin::USDC;
  symbol_config2->value_scale_ = 8;
  symbol_config2->tick_size_x_ = 5;
  symbol_config2->price_limit_pnt_e6_ = 10;
  symbol_config2->contract_type_ = EContractType::LinearPerpetual;
  symbol_config_mgr->AddSymbolConfig(symbol_config2);

  new_config_mgr->set_symbol_config_mgr(symbol_config_mgr);
  config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

  per_worker_store->contract_status_map_[ESymbol(45)] = EContractStatus::Trading;
  per_worker_store->contract_status_map_[ESymbol(12)] = EContractStatus::Trading;

  store::UserDataManager* user_data_manager_ = new store::UserDataManager();
  store::PerUserStore* per_user_store = user_data_manager_->GetUserData(user_id);
  // 初始化
  if (per_user_store == nullptr) {
    user_data_manager_->CreateUserData(user_id);
    per_user_store = user_data_manager_->GetUserData(user_id);
  }
  per_user_store->working_coins_[ECoin::USDC] = {};

  biz::DraftPkg::Ptr draft_pkg = std::make_shared<biz::DraftPkg>(nullptr, header, per_worker_store, per_user_store);

  {
    // CheckSymbolIsClosed
    auto ret = biz::commonbiz::CheckSymbolIsClosed(draft_pkg.get(), ESymbol::BTCUSDT);
    EXPECT_EQ(ret, error::kErrorCodeSymbolNotExists);
  }
  {
    biz::symbol_t test_symbol{};
    auto ret = biz::commonbiz::CheckUserCanTradeSymbol(draft_pkg.get(), "BTCPERP", test_symbol);
    EXPECT_EQ(test_symbol, ESymbol(45));
    EXPECT_EQ(ret, error::kErrorCodeParamsError);
  }
  {
    // CheckSettleCoinIsValid
    biz::coin_t test_coin = ECoin::BTC;
    auto ret = biz::commonbiz::CheckSettleCoinIsValid(draft_pkg.get(), "", test_coin);
    EXPECT_EQ(ret, error::kErrorCodeParamsError);
    ret = biz::commonbiz::CheckSettleCoinIsValid(draft_pkg.get(), "BTC", test_coin);
    EXPECT_EQ(ret, error::kErrorCodeParamsError);
  }
  {
    // CheckBaseCoinIsValid
    auto ret = biz::commonbiz::CheckBaseCoinIsValid(draft_pkg.get(), "");
    EXPECT_EQ(ret, error::kErrorCodeParamsError);
    ret = biz::commonbiz::CheckBaseCoinIsValid(draft_pkg.get(), "BTCUSDT");
    EXPECT_EQ(ret, error::kErrorCodeParamsError);
  }
  {
    // CheckIsCoinOnline
    auto ret = biz::commonbiz::CheckIsCoinOnline(draft_pkg.get(), 18);
    EXPECT_EQ(ret, error::kErrorCodeParamsError);
    ret = biz::commonbiz::CheckIsCoinOnline(draft_pkg.get(), 24);
    EXPECT_EQ(ret, error::kErrorCodeParamsError);
    ret = biz::commonbiz::CheckIsCoinOnline(draft_pkg.get(), 9);
    EXPECT_EQ(ret, error::kErrorCodeParamsError);
    ret = biz::commonbiz::CheckIsCoinOnline(draft_pkg.get(), 5);
    EXPECT_EQ(ret, 0);
  }
  {
    // ValidatePositionModeCoinParam
    biz::coin_t test_coin = 5;
    auto ret = biz::commonbiz::ValidatePositionModeCoinParam(draft_pkg.get(), "USDT", test_coin);
    EXPECT_EQ(ret, error::kErrorCodeParamsError);
  }
}
