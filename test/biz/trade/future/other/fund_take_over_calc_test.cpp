#include "src/biz_worker/service/trade/futures/event_handler/fund_take_over_calc.hpp"

#include <gtest/gtest.h>

#include "lib/stub.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
class FundTakeOverCalcTest : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestCase() {}
  static void TearDownTestCase() {}

 protected:
  void NewLine(const char* info = "") {
    std::cout << std::endl << "-------------------" << info << "----------------------" << std::endl << std::endl;
  }

  void ExecDeposit(stub& user, biz::user_id_t user_id, int64_t coin, const std::string& deposit_value) {
    FutureDepositBuilder deposit_build(te->GenUUID(), user_id, te->GenUUID(), coin, 1, deposit_value.c_str(), "0");
    user.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
    std::cout << deposit_result.m_msg->DebugString() << std::endl;
  }

  void SetUserTag(stub& user, biz::user_id_t user_id, EUserTag user_tag = EUserTag::CopyPro) {
    FutureOneOfSetUserTagBuilder user_tag_req;
    user_tag_req.SetValue(ECoin::USDT, user_id, user_tag, ESourceBusinessLine::UNKNOWN);
    auto resp = user.process(user_tag_req.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    NewLine();
    auto result = te->PopResult();
    std::cout << result.m_msg->DebugString() << std::endl;
  }

  void CalcResultCheck(const svc::trading::req::FundTakeOverCalcResponse& calc_resp, int64_t sum_wb_usdt_value_e8,
                       int64_t limit_usdt_value_e8, int64_t min_usdt_value_e8, int64_t slippage_e2,
                       int64_t reduce_percent_e8) {
    EXPECT_EQ(calc_resp.sum_wb_usdt_value_e8(), sum_wb_usdt_value_e8);
    EXPECT_EQ(calc_resp.limit_usdt_value_e8(), limit_usdt_value_e8);
    EXPECT_EQ(calc_resp.min_usdt_value_e8(), min_usdt_value_e8);
    EXPECT_EQ(calc_resp.slippage_e2(), slippage_e2);
    EXPECT_EQ(calc_resp.reduce_percent_e8(), reduce_percent_e8);
  }

  void CalcResultSpotCheck(const svc::trading::req::FundTakeOverCalcResponse& calc_resp, int64_t coin, int64_t qty_e8,
                           int64_t reduce_qty_e8) {
    auto const& spot_calc = calc_resp.spot_calc_result();
    auto iter_find = spot_calc.find(coin);
    EXPECT_TRUE(iter_find != spot_calc.end());
    auto const& result = iter_find->second;
    EXPECT_EQ(result.qty_e8(), qty_e8);
    EXPECT_EQ(result.reduce_qty_e8(), reduce_qty_e8);
  }

  void CalcResultDerivativesCheck(const svc::trading::req::FundTakeOverCalcResponse& calc_resp, int64_t symbol,
                                  int64_t position_idx, int64_t qty_e8, int64_t reduce_qty_e8) {
    auto const& derivatives_calc = calc_resp.derivatives_calc_result();
    auto iter_find = derivatives_calc.find(symbol);
    EXPECT_TRUE(iter_find != derivatives_calc.end());
    auto const& position_result = iter_find->second.position_result();
    auto position_idx_find = position_result.find(position_idx);
    EXPECT_TRUE(position_idx_find != position_result.end());
    auto const& result = position_idx_find->second;
    EXPECT_EQ(result.qty_e8(), qty_e8);
    EXPECT_EQ(result.reduce_qty_e8(), reduce_qty_e8);
  }

  void UpdateMarketData(int64_t price, ESymbol symbol) {
    // auto const underlying_price = bbase::decimal::Decimal<>("10150");
    te->AddMarkPrice(static_cast<ESymbol>(symbol), price, price, price);
  }

  // 基准用户下单
  void BaseUserCreateOrder(stub& user, biz::user_id_t user_id, ESymbol symbol, EPositionIndex pz_index,
                           EOrderType order_type, ECoin coin, biz::size_x_t qty, biz::price_x_t price, ESide side) {
    auto order_id = te->GenUUID();
    // ESide side = ESide::Buy;
    //
    // biz::size_x_t qty = 10000'0000;
    // biz::price_x_t price = 100000000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id, coin);

    auto resp1 = user.process(create_build.Build());
    std::cout << "user: " << user.m_uid << ": " << resp1->DebugString() << std::endl;
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

    NewLine("user 1 create order");
    std::cout << "user: " << user.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
  }

  // 基准用户成交
  void BaseUserDeal(stub& user, biz::user_id_t user_id, biz::size_x_t qty, ESide side) {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine("user 1 deal");
    std::cout << "user1: " << user.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.m_msg->header().user_id(), user_id);
    auto orderCheck2 = result1.RefFutureMarginResult();
    auto positon = orderCheck2.RefRelatedPosition(0);
    positon.CheckSize(qty);
    EXPECT_EQ(positon.m_msg->side(), side);
  }

  // 对手方uid 下单并且成交
  void OtherUserCreateOrderAndDeal(stub& user2, biz::user_id_t user_id2, ESymbol symbol, EPositionIndex pz_index,
                                   EOrderType order_type, ECoin coin, biz::size_x_t qty, biz::price_x_t price,
                                   ESide side) {
    auto order_id = te->GenUUID();
    // ESide side = ESide::Sell;
    // biz::size_x_t qty = 10000'0000;
    // biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);
    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine("user 2 create order");
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
  }

 protected:
  std::shared_ptr<tmock::CTradeAppMock> te;
};

/*
 * BTC 指数价格 20000
 * ETH 指数价格 1700
 * USDC 指数价格 0.99
 * USDT 指数价格 1
 */
TEST_F(FundTakeOverCalcTest, FundTakeOverCalcOnlySpot) {
  biz::user_id_t user_id = 653106;
  // 构建客户端
  stub user(te, user_id);
  // 设置user_tag
  SetUserTag(user, user_id);
  NewLine("Deposit BTC");
  // 充值
  ExecDeposit(user, user_id, 1, "10");
  NewLine("req for calc");
  /*
   * BTC 10, 指数价格 20000， 折U价值 20‘0000
   * 释放金额 1000
   * 加滑点 2% = 1020
   * 所以减仓比例：= 1020/20'0000 = 0.51%
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 1000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 20'0000'0000'0000, 500'0000'0000, 10'0000'0000, 200, 5100'0000);
    CalcResultSpotCheck(calc_resp, ECoin::BTC, 10'0000'0000, 510'0000);
  }
  // USDC  折U价值小于10，会被过滤，不参与计算
  /*
   * BTC 10, 指数价格 20000， 折U价值 20‘0000
   * USDC 8 指数价格 0.99， 折U价值：7.92
   * 释放金额 1000
   * 加滑点 2% = 1020
   * 所以减仓比例：= 1020/20'0000 = 0.51%
   * 新算法：     = 1020/20'0007.92 = 0.5099'7981%
   */
  NewLine("Deposit USDC");
  // 充值
  ExecDeposit(user, user_id, 16, "8");
  NewLine("req for calc");
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 1000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 20'0007'9200'0000, 500'0000'0000, 10'0000'0000, 200, 5099'7981);
    CalcResultSpotCheck(calc_resp, ECoin::BTC, 10'0000'0000, 509'9799);
  }
  NewLine("Deposit ETH");
  // 充值
  ExecDeposit(user, user_id, 2, "300");
  NewLine("req for calc");
  /*
   * BTC 10, 指数价格 20000， 折U价值 20‘0000
   * ETH 300, 指数价格 1700， 折U价值 51‘0000
   * USDC 8 指数价格 0.99， 折U价值：7.92
   * 释放金额 1000
   * 加滑点 2% = 1020
   * 所以减仓比例：= 1020/71'0000 = 0.14366198%
   * 新算法：     = 1020/71'0007.92 = 0.14366037%
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 1000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 71'0007'9200'0000, 500'0000'0000, 10'0000'0000, 200, 14366037);
    CalcResultSpotCheck(calc_resp, ECoin::BTC, 10'0000'0000, 143'6604);
    CalcResultSpotCheck(calc_resp, ECoin::ETH, 300'0000'0000, 4309'8111);
  }

  NewLine("req for calc");
  /*
   * BTC 10, 指数价格 20000， 折U价值 20‘0000
   * ETH 300, 指数价格 1700， 折U价值 51‘0000
   * USDC 8 指数价格 0.99， 折U价值：7.92
   * 释放金额 200'000
   * 加滑点 2% = 204000
   * 所以减仓比例：= 204000/71'0000 = 28.73239437%
   * 新算法：     = 204000/71'0007.92 = 28.73207382%
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 200'000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 71'0007'9200'0000, 500'0000'0000, 10'0000'0000, 200, 2873207387);
    CalcResultSpotCheck(calc_resp, ECoin::BTC, 10'0000'0000, 2'8732'0739);
    CalcResultSpotCheck(calc_resp, ECoin::ETH, 300'0000'0000, 86'1962'2161);
  }
  NewLine("Deposit USDT");
  // 充值
  ExecDeposit(user, user_id, 5, "100000");
  NewLine("req for calc");
  /*
   * BTC 10, 指数价格 20000， 折U价值 20‘0000
   * ETH 300, 指数价格 1700， 折U价值 51‘0000
   * USDC 8 指数价格 0.99， 折U价值：7.92
   * USDT 10'0000
   * 释放金额 710'000
   * 加滑点 2% = 724000
   * 所以减仓比例：= 724'200/81'0000 = 89.40740741%
   * 新算法：     = 724'200/81'0007.92 = 89.40653322%
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 710'000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 81'0007'9200'0000, 500'0000'0000, 10'0000'0000, 200, 8940653322);
    EXPECT_EQ(calc_resp.spot_calc_result().size(), 2);
    CalcResultSpotCheck(calc_resp, ECoin::BTC, 10'0000'0000, 8'9406'5333);
    CalcResultSpotCheck(calc_resp, ECoin::ETH, 300'0000'0000, 268'2195'9966);
  }

  NewLine("req for calc");
  /*
   * BTC 10, 指数价格 20000， 折U价值 20‘0000
   * ETH 300, 指数价格 1700， 折U价值 51‘0000
   * USDT 10'0000
   * USDC 8 指数价格 0.99， 折U价值：7.92
   * 释放金额 810'000
   * 加滑点 2% = 826200
   * 所以减仓比例：= 826'200/81'0007.92 = 100%
   * 减仓比例超过100%
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 810'000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 81'0007'9200'0000, 500'0000'0000, 10'0000'0000, 200, 100'0000'0000);
    EXPECT_EQ(calc_resp.spot_calc_result().size(), 3);
    CalcResultSpotCheck(calc_resp, ECoin::BTC, 10'0000'0000, 10'0000'0000);
    CalcResultSpotCheck(calc_resp, ECoin::ETH, 300'0000'0000, 300'0000'0000);
    CalcResultSpotCheck(calc_resp, ECoin::USDC, 8'0000'0000, 8'0000'0000);
  }

  NewLine("Deposit USDT");
  // 充值
  ExecDeposit(user, user_id, 5, "10000");
  /*
   * BTC 10, 指数价格 20000， 折U价值 20‘0000
   * ETH 300, 指数价格 1700， 折U价值 51‘0000
   * USDT 11'0000
   * USDC 8 指数价格 0.99， 折U价值：7.92
   * 释放金额 810'000
   * 加滑点 2% = 826200
   * 所以减仓比例：= 826'200/82'0007.92 = 100%
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 810'000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 82'0007'9200'0000, 500'0000'0000, 10'0000'0000, 200, 100'0000'0000);
    EXPECT_EQ(calc_resp.spot_calc_result().size(), 3);
    CalcResultSpotCheck(calc_resp, ECoin::BTC, 10'0000'0000, 10'0000'0000);
    CalcResultSpotCheck(calc_resp, ECoin::ETH, 300'0000'0000, 300'0000'0000);
    CalcResultSpotCheck(calc_resp, ECoin::USDC, 8'0000'0000, 8'0000'0000);
  }

  /*
   * BTC 10, 指数价格 20000， 折U价值 20‘0000
   * ETH 300, 指数价格 1700， 折U价值 51‘0000
   * USDT 11'0000
   * USDC 8 指数价格 0.99， 折U价值：7.92
   * 释放金额 1810'000
   * ---加滑点 2% = 826200
   * 所以减仓比例：= 826'200/82'0007.92 = 100%
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 1810'000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 82'0007'9200'0000, 500'0000'0000, 10'0000'0000, 200, 100'0000'0000);
    EXPECT_EQ(calc_resp.spot_calc_result().size(), 3);
    CalcResultSpotCheck(calc_resp, ECoin::BTC, 10'0000'0000, 10'0000'0000);
    CalcResultSpotCheck(calc_resp, ECoin::ETH, 300'0000'0000, 300'0000'0000);
    CalcResultSpotCheck(calc_resp, ECoin::USDC, 8'0000'0000, 8'0000'0000);
  }
  // sleep(10000);
}

TEST_F(FundTakeOverCalcTest, FundTakeOverCalcOnlyDerivatives) {
  biz::user_id_t user_id = 653106;
  // 构建客户端
  stub user(te, user_id);
  // 设置user_tag
  SetUserTag(user, user_id);
  NewLine("Deposit USDT");
  // 充值
  ExecDeposit(user, user_id, 5, "100000");
  NewLine("req for calc");
  /*
   * USDT 100'000
   * 释放金额 1000
   * 加滑点 2% = 1020
   * 所以减仓比例：= 1020/10'0000 = 1.02%
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 1000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 10'0000'0000'0000, 500'0000'0000, 10'0000'0000, 200, 1'0200'0000);
    // 此处没有需要释放的现货coin
    EXPECT_EQ(calc_resp.spot_calc_result().size(), 0);
  }

  biz::user_id_t user_id2 = 111111;
  stub user2(te, user_id2);
  ExecDeposit(user2, user_id2, 5, "100000");

  ESymbol symbol = ESymbol::BTCUSDT;
  UpdateMarketData(10500, symbol);
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  ECoin coin = ECoin::USDT;
  NewLine("create order BTCUSDT");
  // uid1挂单，方向Buy，限价单
  BaseUserCreateOrder(user, user_id, symbol, pz_index, order_type, coin, 10000'0000, 100000000, ESide::Buy);

  NewLine("other create order BTCUSDT");
  // uid2挂单，方向Sell，限价单
  OtherUserCreateOrderAndDeal(user2, user_id2, symbol, pz_index, order_type, coin, 10000'0000, 100000000, ESide::Sell);

  NewLine("has position BTCUSDT");
  // uid1成交记录
  BaseUserDeal(user, user_id, 10000'0000, ESide::Buy);

  NewLine("req for calc--BTCUSDT position");
  /*
   * USDT 100'000，USDT WB 99999
   * BTCUSDT 持仓 1BTC，UPL_e8：500’0000‘0000
   * SUM_WB = 99999 + 5000 = 100499
   * 释放金额 1000
   * 加滑点 2% = 1020
   * 所以减仓比例：= 1020/100499 = 1.01493548%
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 1000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 10'0499'0000'0000, 500'0000'0000, 10'0000'0000, 200, 1'0149'3548);
    // 此处没有需要释放的现货coin
    EXPECT_EQ(calc_resp.spot_calc_result().size(), 0);
    // BTCUSDT 需要甩卖
    EXPECT_EQ(calc_resp.derivatives_calc_result().size(), 1);
    CalcResultDerivativesCheck(calc_resp, ESymbol::BTCUSDT, pz_index, 1'0000'0000, 1014936);
  }

  coin = ECoin::USDC;
  symbol = ESymbol(45);
  UpdateMarketData(10500, symbol);
  NewLine("create order BTCUSDC");
  BaseUserCreateOrder(user, user_id, symbol, pz_index, order_type, coin, 10'0000'0000, 100000000, ESide::Buy);

  NewLine("req for calc--BTCUSDC order");
  /*
   * 有挂单，不能执行计算操作
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 1000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeFutureOrderNotEmpty);
    std::cout << resp->DebugString() << std::endl;
  }

  NewLine("other create order BTCUSDC");
  // uid2挂单，方向Sell，限价单
  OtherUserCreateOrderAndDeal(user2, user_id2, symbol, pz_index, order_type, coin, 10'0000'0000, 100000000,
                              ESide::Sell);

  NewLine("has position BTCUSDC");
  // uid1成交记录
  BaseUserDeal(user, user_id, 10'0000'0000, ESide::Buy);

  NewLine("req for calc--BTCUSDC position");
  /*
   * USDT 100'000，USDT WB 99999
   * BTCUSDT 持仓 1BTC，UPL_e8：500’0000‘0000
   * USDC wb=-10，因为扣除了成交的fee，折U价值：-9.9 (filter)
   * BTCPERP 支持 1BTC，UPL_e8：5000’0000‘0000->4950
   * SUM_WB = 99999 + 500 + 4950 = 105449
   * 释放金额 1000
   * 加滑点 2% = 1020
   * 所以减仓比例：= 1020/105449 = 0.9672‘9225%
   * 新算法：     = 1020/105439.1=0.96738307
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 1000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 10'5439'1000'0000, 500'0000'0000, 10'0000'0000, 200, 9673'8307);
    // 此处没有需要释放的现货coin
    EXPECT_EQ(calc_resp.spot_calc_result().size(), 0);
    // BTCUSDT 需要甩卖
    EXPECT_EQ(calc_resp.derivatives_calc_result().size(), 2);
    CalcResultDerivativesCheck(calc_resp, ESymbol::BTCUSDT, pz_index, 1'0000'0000, 96'7384);
    CalcResultDerivativesCheck(calc_resp, ESymbol(45), pz_index, 10'0000'0000, 967'3831);
  }

  // nacos配置修改验证
  {
    auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
        bbase::object_manager::ObjectType::kObjectConfigCenter);
    if (nacos_client == nullptr) {
      return;
    }
    // 这三个值，会使用默认值
    nacos_client->SetStringVar("copy_pro_min_usdt_value", "1");
    nacos_client->SetStringVar("copy_pro_limit_usdt_value", "2");
    nacos_client->SetStringVar("copy_pro_slippage", "311");
  }
  NewLine("req for calc--BTCUSDC position default");
  /*
   * USDT 100'000，USDT WB 99999
   * BTCUSDT 持仓 1BTC，UPL_e8：500’0000‘0000
   * USDC wb=-10，因为扣除了成交的fee，折U价值：-9.9 (filter)
   * BTCPERP 支持 1BTC，UPL_e8：5000’0000‘0000->4950
   * SUM_WB = 99999 + 500 + 4950 = 105449
   * 释放金额 1000
   * 加滑点 2% = 1020
   * 所以减仓比例：= 1020/105449 = 0.9672‘9225%
   * 新算法：     = 1020/105439.1=0.96738307
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 1000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 10'5439'1000'0000, 500'0000'0000, 10'0000'0000, 200, 9673'8307);
    // 此处没有需要释放的现货coin
    EXPECT_EQ(calc_resp.spot_calc_result().size(), 0);
    // BTCUSDT 需要甩卖
    EXPECT_EQ(calc_resp.derivatives_calc_result().size(), 2);
    CalcResultDerivativesCheck(calc_resp, ESymbol::BTCUSDT, pz_index, 1'0000'0000, 96'7384);
    CalcResultDerivativesCheck(calc_resp, ESymbol(45), pz_index, 10'0000'0000, 967'3831);
  }

  NewLine("create order BTCUSDC");
  BaseUserCreateOrder(user, user_id, symbol, pz_index, order_type, coin, 5'0000'0000, 100000000, ESide::Buy);
  NewLine("other create order BTCUSDC");
  // uid2挂单，方向Sell，限价单
  OtherUserCreateOrderAndDeal(user2, user_id2, symbol, pz_index, order_type, coin, 5'0000'0000, 100000000, ESide::Sell);

  NewLine("has position BTCUSDC");
  // uid1成交记录
  BaseUserDeal(user, user_id, 15'0000'0000, ESide::Buy);

  NewLine("req for calc--BTCUSDC position");
  /*
   * USDT 100'000，USDT WB 99999
   * BTCUSDT 持仓 1BTC，UPL_e8：500’0000‘0000
   * USDC wb=-15，因为扣除了成交的fee，折U价值：-14.85
   * BTCPERP 支持 15BTC，UPL_e8：7500’0000‘0000->7425
   * SUM_WB = 99999 + 500 + 7425 - 14.85 = 107909.15
   * 释放金额 1000
   * 加滑点 2% = 1020
   * 所以减仓比例：= 1020/107909.15 = 0.9452'3959%
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 1000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 10'7909'1500'0000, 500'0000'0000, 10'0000'0000, 200, 9452'3959);
    EXPECT_EQ(calc_resp.spot_calc_result().size(), 1);
    CalcResultSpotCheck(calc_resp, coin, -15'0000'0000, -1417'8594);
    // BTCUSDT 需要甩卖
    EXPECT_EQ(calc_resp.derivatives_calc_result().size(), 2);
    CalcResultDerivativesCheck(calc_resp, ESymbol::BTCUSDT, pz_index, 1'0000'0000, 94'5240);
    CalcResultDerivativesCheck(calc_resp, ESymbol(45), pz_index, 15'0000'0000, 1417'8594);
  }

  {
    auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
        bbase::object_manager::ObjectType::kObjectConfigCenter);
    if (nacos_client == nullptr) {
      return;
    }
    nacos_client->SetStringVar("copy_pro_min_usdt_value", "12");
  }

  coin = ECoin::USDT;
  symbol = ESymbol::XRPUSDT;
  UpdateMarketData(1, symbol);

  NewLine("create order XRPUSDT");
  BaseUserCreateOrder(user, user_id, symbol, pz_index, order_type, coin, 10'0000'0000, 1'0000, ESide::Buy);

  NewLine("other create order XRPUSDT");
  // uid2挂单，方向Sell，限价单
  OtherUserCreateOrderAndDeal(user2, user_id2, symbol, pz_index, order_type, coin, 10'0000'0000, 1'0000, ESide::Sell);

  NewLine("has position XRPUSDT");
  // uid1成交记录
  BaseUserDeal(user, user_id, 10'0000'0000, ESide::Buy);

  NewLine("req for calc--BTCUSDC position");
  /*
   * USDT 100'000，USDT WB 99998.999
   * BTCUSDT 持仓 1BTC，UPL_e8：500’0000‘0000
   * USDC wb=-15，因为扣除了成交的fee，折U价值：-14.85
   * BTCPERP 支持 15BTC，UPL_e8：7500’0000‘0000->7425
   * SUM_WB = 99998.999 + 500 + 7425 - 14.85 = 107909.149
   * 释放金额 1000
   * 加滑点 2% = 1020
   * 所以减仓比例：= 1020/107909.149 = 0.9452'3960%
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 1000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 10'7909'1490'0000, 500'0000'0000, 12'0000'0000, 200, 9452'3960);
    EXPECT_EQ(calc_resp.spot_calc_result().size(), 1);
    CalcResultSpotCheck(calc_resp, ECoin::USDC, -15'0000'0000, -1417'8594);
    // BTCUSDT 需要甩卖
    EXPECT_EQ(calc_resp.derivatives_calc_result().size(), 2);
    CalcResultDerivativesCheck(calc_resp, ESymbol::BTCUSDT, pz_index, 1'0000'0000, 94'5240);
    CalcResultDerivativesCheck(calc_resp, ESymbol(45), pz_index, 15'0000'0000, 1417'8594);
  }

  // nacos配置修改验证
  {
    auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
        bbase::object_manager::ObjectType::kObjectConfigCenter);
    if (nacos_client == nullptr) {
      return;
    }
    nacos_client->SetStringVar("copy_pro_min_usdt_value", "15");
    nacos_client->SetStringVar("copy_pro_limit_usdt_value", "550");
    nacos_client->SetStringVar("copy_pro_slippage", "3");
  }
  /*
   * USDT 100'000，USDT WB 99998.999
   * BTCUSDT 持仓 1BTC，UPL_e8：500’0000‘0000
   * USDC wb=-15，因为扣除了成交的fee，折U价值：-14.85 过滤
   * BTCPERP 支持 15BTC，UPL_e8：7500’0000‘0000->7425
   * SUM_WB = 99998.999 + 500 + 7425 = 107923.999
   * 释放金额 1000
   * 加滑点 3% = 1030
   * 所以减仓比例：= 1030/107923.999 = 0.9543'7531%
   * 新算法：     = 1030/107909.149=0.9545'0665%
   */
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 1000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    auto const& calc_resp = resp->fund_take_over_cacl_response();
    CalcResultCheck(calc_resp, 10'7909'1490'0000, 550'0000'0000, 15'0000'0000, 300, 9545'0665);
    EXPECT_EQ(calc_resp.spot_calc_result().size(), 0);
    // CalcResultSpotCheck(calc_resp, ECoin::USDC, -15'0000'0000, -1417'8594);
    // BTCUSDT 需要甩卖
    EXPECT_EQ(calc_resp.derivatives_calc_result().size(), 2);
    CalcResultDerivativesCheck(calc_resp, ESymbol::BTCUSDT, pz_index, 1'0000'0000, 95'4507);
    CalcResultDerivativesCheck(calc_resp, ESymbol(45), pz_index, 15'0000'0000, 1431'7600);
  }
  // sleep(1000);
}

TEST_F(FundTakeOverCalcTest, TakeOverCalcError) {
  biz::user_id_t user_id = 653106;
  // 构建客户端
  stub user(te, user_id);
  NewLine("Deposit USDT");
  // 充值
  ExecDeposit(user, user_id, 5, "100000");
  // user tag错误
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 1000'0000'0000);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeUserTagError);
    std::cout << resp->DebugString() << std::endl;
  }
  NewLine("set tag");
  // 设置user_tag
  SetUserTag(user, user_id);

  //
  NewLine("set tag and query");
  {
    FutureOneOfFundTakeOverCalcBuilder req_builder;
    req_builder.SetValue(user_id, te->GenUUID(), 100, 0);
    auto resp = user.process(req_builder.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
  }
}

TEST_F(FundTakeOverCalcTest, SpotMarginModeOnOff) {
  biz::user_id_t user_id = 653106;
  // 构建客户端
  stub user(te, user_id);
  NewLine("Deposit USDT");
  // 充值
  ExecDeposit(user, user_id, 5, "100000");

  NewLine("SetSpotMarginOn");
  {
    SwitchSpotMarginModeBuilder builder1(te->GenUUID(), user_id, true);
    auto resp = user.process(builder1.Build());
    std::cout << resp->DebugString() << std::endl;
    auto result = te->PopResult();
    std::cout << result.m_msg->DebugString() << std::endl;
    EXPECT_TRUE(result.m_msg->asset_margin_result().per_user_setting_data().spot_margin_mode());
  }

  // 设置user_tag失败，因为现货杠杆交易开启
  NewLine("user_tag copy pro");
  {
    FutureOneOfSetUserTagBuilder user_tag_req;
    user_tag_req.SetValue(ECoin::USDT, user_id, EUserTag::CopyPro, ESourceBusinessLine::UNKNOWN);
    auto resp = user.process(user_tag_req.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeFuncNotSupportUserSpotMarginOn);
    std::cout << resp->DebugString() << std::endl;
  }
  NewLine("SetSpotMarginOn Off");

  {
    SwitchSpotMarginModeBuilder builder1(te->GenUUID(), user_id, false);
    auto resp = user.process(builder1.Build());
    std::cout << resp->DebugString() << std::endl;
    auto result = te->PopResult();
    std::cout << result.m_msg->DebugString() << std::endl;
    EXPECT_FALSE(result.m_msg->asset_margin_result().per_user_setting_data().spot_margin_mode());
  }
  NewLine("user_tag copy pro");
  {
    FutureOneOfSetUserTagBuilder user_tag_req;
    user_tag_req.SetValue(ECoin::USDT, user_id, EUserTag::CopyPro, ESourceBusinessLine::UNKNOWN);
    auto resp = user.process(user_tag_req.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
  }
  NewLine("to cross mode after copy pro");
  {
    // 切到全仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user_id);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Cross);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user_id, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    std::cout << result.m_msg->DebugString() << std::endl;
    EXPECT_FALSE(result.m_msg->asset_margin_result().per_user_setting_data().spot_margin_mode());
  }
}

/*
 * 切换到cross 模式，此时用户杠杆标志位 为 true
 * 设置CopyPro tag，此时账户模式 cross， 设置失败
 */
TEST_F(FundTakeOverCalcTest, SpotMarginModeOnOff1) {
  biz::user_id_t user_id = 653106;
  // 构建客户端
  stub user(te, user_id);
  NewLine("Deposit USDT");
  // 充值
  ExecDeposit(user, user_id, 5, "100000");

  NewLine("to cross mode");
  {
    // 切到全仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user_id);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Cross);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user_id, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    std::cout << result.m_msg->DebugString() << std::endl;
    EXPECT_TRUE(result.m_msg->asset_margin_result().per_user_setting_data().spot_margin_mode());
    EXPECT_EQ(result.m_msg->asset_margin_result().per_user_setting_data().accountmode(), EAccountMode::Cross);
  }

  // 设置user_tag 失败
  NewLine("user_tag copy pro");
  {
    FutureOneOfSetUserTagBuilder user_tag_req;
    user_tag_req.SetValue(ECoin::USDT, user_id, EUserTag::CopyPro, ESourceBusinessLine::UNKNOWN);
    auto resp = user.process(user_tag_req.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeFuncNotSupportUserSpotMarginOn);
    std::cout << resp->DebugString() << std::endl;
  }
}

/*
 * 一开始这种CopyPro Tag
 * 切换仓位到cross 模式，此时 现货交易杠杆标志位为false
 */
TEST_F(FundTakeOverCalcTest, SpotMarginModeOnOff2) {
  biz::user_id_t user_id = 653106;
  // 构建客户端
  stub user(te, user_id);
  NewLine("Deposit USDT");
  // 充值
  ExecDeposit(user, user_id, 5, "100000");

  // 设置user_tag
  NewLine("user_tag copy pro");
  {
    FutureOneOfSetUserTagBuilder user_tag_req;
    user_tag_req.SetValue(ECoin::USDT, user_id, EUserTag::CopyPro, ESourceBusinessLine::UNKNOWN);
    auto resp = user.process(user_tag_req.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    NewLine();
    auto result = te->PopResult();
    std::cout << result.m_msg->DebugString() << std::endl;
    EXPECT_FALSE(result.m_msg->asset_margin_result().per_user_setting_data().spot_margin_mode());
    EXPECT_EQ(result.m_msg->asset_margin_result().per_user_setting_data().accountmode(), EAccountMode::UnKnown);
  }

  NewLine("to cross mode");
  {
    // 切到全仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user_id);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Cross);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user_id, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    std::cout << result.m_msg->DebugString() << std::endl;
    EXPECT_FALSE(result.m_msg->asset_margin_result().per_user_setting_data().spot_margin_mode());
    EXPECT_EQ(result.m_msg->asset_margin_result().per_user_setting_data().accountmode(), EAccountMode::Cross);
  }
}

TEST_F(FundTakeOverCalcTest, CanNotSetToPM) {
  biz::user_id_t user_id = 653106;
  // 构建客户端
  stub user(te, user_id);
  NewLine("Deposit USDT");
  // 充值
  ExecDeposit(user, user_id, 5, "100000");

  // 设置user_tag
  NewLine("user_tag copy pro");
  {
    FutureOneOfSetUserTagBuilder user_tag_req;
    user_tag_req.SetValue(ECoin::USDT, user_id, EUserTag::CopyPro, ESourceBusinessLine::UNKNOWN);
    auto resp = user.process(user_tag_req.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNone);
    std::cout << resp->DebugString() << std::endl;
    NewLine();
    auto result = te->PopResult();
    std::cout << result.m_msg->DebugString() << std::endl;
    EXPECT_FALSE(result.m_msg->asset_margin_result().per_user_setting_data().spot_margin_mode());
  }

  NewLine("to cross mode");
  {
    // 切到全仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user_id);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Cross);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user_id, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    std::cout << result.m_msg->DebugString() << std::endl;
    EXPECT_FALSE(result.m_msg->asset_margin_result().per_user_setting_data().spot_margin_mode());
    EXPECT_EQ(result.m_msg->asset_margin_result().per_user_setting_data().accountmode(), EAccountMode::Cross);
  }

  NewLine("to PM");
  {
    // 切到全仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user_id);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Portfolio);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user_id, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    std::cout << result.m_msg->DebugString() << std::endl;
    EXPECT_FALSE(result.m_msg->asset_margin_result().per_user_setting_data().spot_margin_mode());
    EXPECT_EQ(result.m_msg->asset_margin_result().per_user_setting_data().accountmode(), EAccountMode::Cross);
  }
}

TEST_F(FundTakeOverCalcTest, CanNotSetToPM1) {
  biz::user_id_t user_id = 653106;
  // 构建客户端
  stub user(te, user_id);
  NewLine("Deposit USDT");
  // 充值
  ExecDeposit(user, user_id, 5, "100000");

  NewLine("to PM");
  {
    // 切到全仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user_id);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Portfolio);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user_id, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    std::cout << result.m_msg->DebugString() << std::endl;
    EXPECT_TRUE(result.m_msg->asset_margin_result().per_user_setting_data().spot_margin_mode());
    EXPECT_EQ(result.m_msg->asset_margin_result().per_user_setting_data().accountmode(), EAccountMode::Portfolio);
  }

  // 设置user_tag，此时设置失败，因为是PM模式
  NewLine("user_tag copy pro");
  {
    FutureOneOfSetUserTagBuilder user_tag_req;
    user_tag_req.SetValue(ECoin::USDT, user_id, EUserTag::CopyPro, ESourceBusinessLine::UNKNOWN);
    auto resp = user.process(user_tag_req.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeFuncNotSupportAccountModeIsPm);
    std::cout << resp->DebugString() << std::endl;
    NewLine();
    auto result = te->PopResult();
    EXPECT_EQ(result.m_msg, nullptr);
  }

  NewLine("to cross mode");
  {
    // 切到全仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user_id);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Cross);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user_id, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    std::cout << result.m_msg->DebugString() << std::endl;
    EXPECT_TRUE(result.m_msg->asset_margin_result().per_user_setting_data().spot_margin_mode());
    EXPECT_EQ(result.m_msg->asset_margin_result().per_user_setting_data().accountmode(), EAccountMode::Cross);
  }

  // 设置user_tag
  NewLine("user_tag copy pro");
  {
    FutureOneOfSetUserTagBuilder user_tag_req;
    user_tag_req.SetValue(ECoin::USDT, user_id, EUserTag::CopyPro, ESourceBusinessLine::UNKNOWN);
    auto resp = user.process(user_tag_req.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeFuncNotSupportUserSpotMarginOn);
    std::cout << resp->DebugString() << std::endl;
    // NewLine();
    // auto result = te->PopResult();
    // std::cout << result.m_msg->DebugString() << std::endl;
    // EXPECT_FALSE(result.m_msg->asset_margin_result().per_user_setting_data().spot_margin_mode());
    // EXPECT_EQ(result.m_msg->asset_margin_result().per_user_setting_data().accountmode(), EAccountMode::Cross);
  }
}

// TEST_F(FundTakeOverCalcTest, CanNotTradeOption) {
//   biz::user_id_t user_id = 653106;
//   // 构建客户端
//   stub user(te, user_id);
//   NewLine("Deposit USDT");
//   // 充值
//   ExecDeposit(user, user_id, 5, "100000");

//   // 设置user_tag
//   NewLine("user_tag copy pro");
//   {
//     FutureOneOfSetUserTagBuilder user_tag_req;
//     user_tag_req.SetValue(ECoin::USDT, user_id, EUserTag::CopyPro, ESourceBusinessLine::UNKNOWN);
//     auto resp = user.process(user_tag_req.Build());
//     EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
//     std::cout << resp->DebugString() << std::endl;
//     NewLine();
//     auto result = te->PopResult();
//     std::cout << result.m_msg->DebugString() << std::endl;
//   }
//   NewLine("option order");
//   {
//     std::string const symbol = "BTC-31DEC21-40000-C";
//     ESide side = ESide::Sell;
//     EOrderType const order_type = EOrderType::Limit;
//     std::string const qty = "0.01";
//     std::string const price = "200";
//     ETimeInForce time_in_force = ETimeInForce::GoodTillCancel;
//     std::string order_link_id = te->GenUUID();
//     std::int32_t const place_mode = EPlaceMode::Advanced;
//     std::int32_t const place_type = EPlaceType::Price;

//     OptionSiteApiCreateOrderBuilder create_ao_builder(symbol, side, order_type, qty, price, time_in_force,
//                                                       te->GenUUID(), place_mode, place_type);
//     auto resp = user.OptionSiteApiCreateOrder(create_ao_builder.Build());
//     std::cout << resp->DebugString() << std::endl;
//     EXPECT_EQ(resp->retcode(), -1);
//     EXPECT_EQ(resp->retmsg(), "CopyPro user can not trade option");
//   }

//   NewLine("option batch order");
//   {
//     std::string symbol = "BTC-31DEC21-40000-C";
//     ESide side = ESide::Buy;
//     EOrderType order_type = EOrderType::Limit;
//     std::string qty = "1";
//     std::string price = "200";
//     ETimeInForce time_in_force = ETimeInForce::GoodTillCancel;
//     std::string order_link_id = te->GenUUID();
//     int place_mode = 1;
//     int place_type = 1;

//     OptionSiteApiCreateOrderBuilder create_ao_builder(symbol, side, order_type, qty, price, time_in_force,
//                                                       order_link_id, place_mode, place_type);

//     svc::uta_engine::option::SiteBatchOrderRequest siteBatchOrderRequest;
//     siteBatchOrderRequest.mutable_orderrequest()->Add(create_ao_builder.Build());
//     siteBatchOrderRequest.mutable_orderrequest()->Add(create_ao_builder.Build());

//     auto resp = user.OptionBatchSiteApiCreateOrder(siteBatchOrderRequest);
//     std::cout << resp->DebugString() << std::endl;
//   }
// }

TEST_F(FundTakeOverCalcTest, CanNotTradeOption1) {
  biz::user_id_t user_id = 653106;
  // 构建客户端
  stub user(te, user_id);
  NewLine("Deposit USDT");
  // 充值
  ExecDeposit(user, user_id, 5, "100000");

  NewLine("option order");
  {
    te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

    std::string const symbol = "BTC-31DEC21-40000-C";
    ESide side = ESide::Sell;
    EOrderType const order_type = EOrderType::Limit;
    std::string const qty = "0.01";
    std::string const price = "200";
    ETimeInForce time_in_force = ETimeInForce::GoodTillCancel;
    std::string order_link_id = te->GenUUID();
    std::int32_t const place_mode = EPlaceMode::Advanced;
    std::int32_t const place_type = EPlaceType::Price;

    OptionSiteApiCreateOrderBuilder create_ao_builder(symbol, side, order_type, qty, price, time_in_force,
                                                      te->GenUUID(), place_mode, place_type);
    auto resp = user.OptionSiteApiCreateOrder(create_ao_builder.Build());
    std::cout << resp->DebugString() << std::endl;
    EXPECT_EQ(resp->retcode(), 0);
    EXPECT_EQ(resp->retmsg(), "OK");
    NewLine();
    auto result = te->PopResult();
    std::cout << result.m_msg->DebugString() << std::endl;
  }

  // 设置user_tag，设置失败，因为包含期权订单
  NewLine("user_tag copy pro");
  {
    FutureOneOfSetUserTagBuilder user_tag_req;
    user_tag_req.SetValue(ECoin::USDT, user_id, EUserTag::CopyPro, ESourceBusinessLine::UNKNOWN);
    auto resp = user.process(user_tag_req.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kOptionOrderNotEmpty);
    EXPECT_EQ(resp->ret_msg(), "user has Option order can not set user_tag = CopyPro");
    std::cout << resp->DebugString() << std::endl;
    NewLine();
    auto result = te->PopResult();
    EXPECT_EQ(result.m_msg, nullptr);
  }
}
