#include "test/biz/trade/future/passthrough/start_passthrough_test.hpp"

#include <bbase/common/hdts/hdts.hpp>

#include "lib/msg_builder.hpp"
#include "src/biz_worker/service/trade/store/passthrough/raw_recover_pass.hpp"
#include "src/biz_worker/utils/pb_convertor/to_pb.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(StartPassthroughTest, recovery) {
  store::RecoverPass::Ptr pass = std::make_shared<store::RecoverPass>();
  pass->set_symbol(5);
  pass->set_cross_idx(5);
  biz::CrossError err = biz::cross::xReqSender::SendRecoverPass(pass, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result1 = te->PopResult();
  // todo:暂时还拿不到透传包撮合结果无法校验,后面再完善
  // ASSERT_NE(result1.m_msg.get(), nullptr);
}

TEST_F(StartPassthroughTest, passthrough_contract) {
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 0;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = EOrderType::UNKNOWN;
  sender.req_mgr_.request_obj_->req_.side_ = ESide::None;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = ETimeInForce::UNKNOWN;
  sender.req_mgr_.request_obj_->req_.symbol_ = ESymbol::UNKNOWN;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = biz::cross::request::kCreatedByUser;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::response::kMTContractResponse;
  // 组装
  /*
  sender.req_mgr_.x_recovery_item_ = store::Order();
  sender.req_mgr_.x_recovery_item_->cross_status = ECrossStatus::Init;
  sender.req_mgr_.x_recovery_item_->user_id = 0;
  sender.req_mgr_.x_recovery_item_->order_id = "";
  sender.req_mgr_.x_recovery_item_->exec_id = "";
  sender.req_mgr_.x_recovery_item_->exec_type = EExecType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->order_status = EOrderStatus::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->cxl_rej_reason = ECxlRejReason::EC_NoError;
  sender.req_mgr_.x_recovery_item_->exec_qty = 0;
  sender.req_mgr_.x_recovery_item_->exec_price = 0;
  sender.req_mgr_.x_recovery_item_->cum_qty = 0;
  sender.req_mgr_.x_recovery_item_->leaves_qty = 0;
  sender.req_mgr_.x_recovery_item_->transact_time = 0;
  sender.req_mgr_.x_recovery_item_->last_liquidity_ind = ELastLiquidityInd::LiquidityIndNA;
  sender.req_mgr_.x_recovery_item_->create_type = ECreateType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->cancel_type = ECancelType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->order_type = EOrderType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->symbol = ESymbol::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->side = ESide::None;
  sender.req_mgr_.x_recovery_item_->added_cross_seq = 0;
  sender.req_mgr_.x_recovery_item_->qty = 0;
  sender.req_mgr_.x_recovery_item_->price = 0;
  sender.req_mgr_.x_recovery_item_->price_scale = 0;
   */
  // 组装

  store::Contract::Ptr ct = std::make_shared<store::Contract>();
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  ::convertor::ToPB::Convert(ct.get(), sender.req_mgr_.x_pass_through_.value().mutable_contract_info());

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}

// create type = 31 CreateByTakeOver_PassThrough
TEST_F(StartPassthroughTest, passthrough_31) {
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = '1';
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = 31;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTNewOrder;
  // 组装
  /*
  sender.req_mgr_.x_recovery_item_ = store::Order();
  sender.req_mgr_.x_recovery_item_->cross_status = ECrossStatus::Init;
  sender.req_mgr_.x_recovery_item_->user_id = 0;
  sender.req_mgr_.x_recovery_item_->order_id = "";
  sender.req_mgr_.x_recovery_item_->exec_id = "";
  sender.req_mgr_.x_recovery_item_->exec_type = EExecType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->order_status = EOrderStatus::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->cxl_rej_reason = ECxlRejReason::EC_NoError;
  sender.req_mgr_.x_recovery_item_->exec_qty = 0;
  sender.req_mgr_.x_recovery_item_->exec_price = 0;
  sender.req_mgr_.x_recovery_item_->cum_qty = 0;
  sender.req_mgr_.x_recovery_item_->leaves_qty = 0;
  sender.req_mgr_.x_recovery_item_->transact_time = 0;
  sender.req_mgr_.x_recovery_item_->last_liquidity_ind = ELastLiquidityInd::LiquidityIndNA;
  sender.req_mgr_.x_recovery_item_->create_type = ECreateType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->cancel_type = ECancelType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->order_type = EOrderType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->symbol = ESymbol::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->side = ESide::None;
  sender.req_mgr_.x_recovery_item_->added_cross_seq = 0;
  sender.req_mgr_.x_recovery_item_->qty = 0;
  sender.req_mgr_.x_recovery_item_->price = 0;
  sender.req_mgr_.x_recovery_item_->price_scale = 0;
   */
  // 组装

  auto ct = std::make_shared<store::Position>();
  ct->symbol = sender.req_mgr_.request_obj_->req_.symbol_;
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  ::convertor::ToPB::Convert(ct.get(), sender.req_mgr_.x_pass_through_.value().mutable_orig_position());

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}

// create type = 30   CreateByAdl_PassThrough
TEST_F(StartPassthroughTest, passthrough_30) {
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = '1';
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 1e8;
  sender.req_mgr_.request_obj_->req_.price_ = 20000 * 1e4;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = 30;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTNewOrder;
  // 组装
  /*
  sender.req_mgr_.x_recovery_item_ = store::Order();
  sender.req_mgr_.x_recovery_item_->cross_status = ECrossStatus::Init;
  sender.req_mgr_.x_recovery_item_->user_id = 0;
  sender.req_mgr_.x_recovery_item_->order_id = "";
  sender.req_mgr_.x_recovery_item_->exec_id = "";
  sender.req_mgr_.x_recovery_item_->exec_type = EExecType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->order_status = EOrderStatus::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->cxl_rej_reason = ECxlRejReason::EC_NoError;
  sender.req_mgr_.x_recovery_item_->exec_qty = 0;
  sender.req_mgr_.x_recovery_item_->exec_price = 0;
  sender.req_mgr_.x_recovery_item_->cum_qty = 0;
  sender.req_mgr_.x_recovery_item_->leaves_qty = 0;
  sender.req_mgr_.x_recovery_item_->transact_time = 0;
  sender.req_mgr_.x_recovery_item_->last_liquidity_ind = ELastLiquidityInd::LiquidityIndNA;
  sender.req_mgr_.x_recovery_item_->create_type = ECreateType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->cancel_type = ECancelType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->order_type = EOrderType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->symbol = ESymbol::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->side = ESide::None;
  sender.req_mgr_.x_recovery_item_->added_cross_seq = 0;
  sender.req_mgr_.x_recovery_item_->qty = 0;
  sender.req_mgr_.x_recovery_item_->price = 0;
  sender.req_mgr_.x_recovery_item_->price_scale = 0;
   */
  // 组装

  store::Contract::Ptr ct = std::make_shared<store::Contract>();
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  ::convertor::ToPB::Convert(ct.get(), sender.req_mgr_.x_pass_through_.value().mutable_contract_info());

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}

// create type = 18
TEST_F(StartPassthroughTest, passthrough_18) {
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = '1';
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = 18;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTNewOrder;
  // 组装
  /*
  sender.req_mgr_.x_recovery_item_ = store::Order();
  sender.req_mgr_.x_recovery_item_->cross_status = ECrossStatus::Init;
  sender.req_mgr_.x_recovery_item_->user_id = 0;
  sender.req_mgr_.x_recovery_item_->order_id = "";
  sender.req_mgr_.x_recovery_item_->exec_id = "";
  sender.req_mgr_.x_recovery_item_->exec_type = EExecType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->order_status = EOrderStatus::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->cxl_rej_reason = ECxlRejReason::EC_NoError;
  sender.req_mgr_.x_recovery_item_->exec_qty = 0;
  sender.req_mgr_.x_recovery_item_->exec_price = 0;
  sender.req_mgr_.x_recovery_item_->cum_qty = 0;
  sender.req_mgr_.x_recovery_item_->leaves_qty = 0;
  sender.req_mgr_.x_recovery_item_->transact_time = 0;
  sender.req_mgr_.x_recovery_item_->last_liquidity_ind = ELastLiquidityInd::LiquidityIndNA;
  sender.req_mgr_.x_recovery_item_->create_type = ECreateType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->cancel_type = ECancelType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->order_type = EOrderType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->symbol = ESymbol::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->side = ESide::None;
  sender.req_mgr_.x_recovery_item_->added_cross_seq = 0;
  sender.req_mgr_.x_recovery_item_->qty = 0;
  sender.req_mgr_.x_recovery_item_->price = 0;
  sender.req_mgr_.x_recovery_item_->price_scale = 0;
   */
  // 组装

  store::Contract::Ptr ct = std::make_shared<store::Contract>();
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  ::convertor::ToPB::Convert(ct.get(), sender.req_mgr_.x_pass_through_.value().mutable_contract_info());

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}

// create type = 32  CreateBySwapPz_PassThrough
TEST_F(StartPassthroughTest, passthrough_32) {
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = '1';
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = 32;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTNewOrder;
  // 组装

  std::shared_ptr<store::RawSwapPositionRequest> ct = std::make_shared<store::RawSwapPositionRequest>();
  ct->symbol = sender.req_mgr_.request_obj_->req_.symbol_;
  ct->maker_position_idx = EPositionIndex::Single;
  ct->maker_user_id = 10010;
  ct->maker_side = ESide::Sell;
  ct->taker_position_idx = EPositionIndex::Single;
  ct->taker_user_id = 10012;
  ct->taker_side = ESide::Buy;
  ct->exec_price_x = 10000;
  ct->exec_size_x = 100000000;
  ct->price_scale = 4;

  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  ::convertor::ToPB::Convert(ct.get(), sender.req_mgr_.x_pass_through_.value().mutable_swap_position());

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}

// create type = 43 CreateByBlock_PassThrough
TEST_F(StartPassthroughTest, passthrough_43) {
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = '1';
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = 43;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTNewOrder;
  // 组装

  std::shared_ptr<store::RawBlockTradeRequest> ct = std::make_shared<store::RawBlockTradeRequest>();
  ct->symbol = sender.req_mgr_.request_obj_->req_.symbol_;
  ct->taker_side = ESide::Buy;
  ct->maker_side = ESide::Sell;
  ct->exec_price_x = 1;
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  ::convertor::ToPB::Convert(ct.get(), sender.req_mgr_.x_pass_through_.value().mutable_block_trade());

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
  sleep(3);
}

// fundingfee
TEST_F(StartPassthroughTest, passthrough_fundingfee) {
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = '1';
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = biz::cross::request::kCreatedByUser;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTSettleFundingFee;
  // 组装
  /*
  sender.req_mgr_.x_recovery_item_ = store::Order();
  sender.req_mgr_.x_recovery_item_->cross_status = ECrossStatus::Init;
  sender.req_mgr_.x_recovery_item_->user_id = 0;
  sender.req_mgr_.x_recovery_item_->order_id = "";
  sender.req_mgr_.x_recovery_item_->exec_id = "";
  sender.req_mgr_.x_recovery_item_->exec_type = EExecType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->order_status = EOrderStatus::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->cxl_rej_reason = ECxlRejReason::EC_NoError;
  sender.req_mgr_.x_recovery_item_->exec_qty = 0;
  sender.req_mgr_.x_recovery_item_->exec_price = 0;
  sender.req_mgr_.x_recovery_item_->cum_qty = 0;
  sender.req_mgr_.x_recovery_item_->leaves_qty = 0;
  sender.req_mgr_.x_recovery_item_->transact_time = 0;
  sender.req_mgr_.x_recovery_item_->last_liquidity_ind = ELastLiquidityInd::LiquidityIndNA;
  sender.req_mgr_.x_recovery_item_->create_type = ECreateType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->cancel_type = ECancelType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->order_type = EOrderType::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->symbol = ESymbol::UNKNOWN;
  sender.req_mgr_.x_recovery_item_->side = ESide::None;
  sender.req_mgr_.x_recovery_item_->added_cross_seq = 0;
  sender.req_mgr_.x_recovery_item_->qty = 0;
  sender.req_mgr_.x_recovery_item_->price = 0;
  sender.req_mgr_.x_recovery_item_->price_scale = 0;
   */
  // 组装

  store::FundingFee::Ptr ct = std::make_shared<store::FundingFee>();
  ct->set_symbol(sender.req_mgr_.request_obj_->req_.symbol_);
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  ::convertor::ToPB::Convert(ct.get(), sender.req_mgr_.x_pass_through_.value().mutable_funding_fee());

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}

TEST_F(StartPassthroughTest, funding) {
  SendFundingFee(nullptr, BuildFundingFeeReq(ESymbol::BTCUSDT), EProductType::Futures);
  auto result1 = te->PopResult();
  // todo:暂时还拿不到透传包撮合结果无法校验,后面再完善
}

store::FundingFee::Ptr StartPassthroughTest::BuildFundingFeeReq(biz::symbol_t symbol) {
  auto sc = config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol);
  store::FundingFee::Ptr funding_fee_req = std::make_shared<store::FundingFee>();
  funding_fee_req->set_cross_idx(sc->cross_idx_);
  funding_fee_req->set_exec_id({});
  funding_fee_req->set_exec_price(22000);
  funding_fee_req->set_exec_time(bbase::utils::Time().GetTimeNs());
  funding_fee_req->set_fee_rate(0.001 * 1e8);
  funding_fee_req->set_symbol(symbol);
  funding_fee_req->set_price_scale(sc->price_scale_);

  return funding_fee_req;
}

biz::CrossError StartPassthroughTest::SendFundingFee(const std::shared_ptr<const config::ConfigManager>,
                                                     store::FundingFee::Ptr funding_fee, EProductType product_type) {
  biz::CrossError err;
  biz::cross::xReqSender sender;
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  ::convertor::ToPB::Convert(funding_fee.get(), sender.req_mgr_.x_pass_through_.value().mutable_funding_fee());

  int32_t symbol = static_cast<biz::symbol_t>(funding_fee->symbol());

  /// 组装header
  auto sending_time = bbase::utils::Time::GetTimeUs();
  err = sender.BuildHeader(biz::cross::request::kMTSettleFundingFee, ECreateType::UNKNOWN);
  if (err.HasErr()) {
    return err;
  }

  /// 组装req写入值确保下游不会panic
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTSettleFundingFee;
  sender.req_mgr_.request_obj_->req_.symbol_ = symbol;
  sender.req_mgr_.request_obj_->req_.side_ = biz::cross::request::kSideBuy;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = biz::cross::request::kTIFGoodTillCancel;
  sender.req_mgr_.request_obj_->req_.order_type_ = biz::cross::request::kOTMarketOrder;
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);

  /// #3: 发送数据
  return sender.DoSend2Cross(funding_fee->cross_idx(), product_type);
}

// reject
TEST_F(StartPassthroughTest, cross_request_reject) {
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 0;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = '1';
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 10;
  sender.req_mgr_.request_obj_->req_.price_ = 200000;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = 31;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTNewOrder;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 0;
  // 组装
  /*
    store::FundingFee::Ptr ct = std::make_shared<store::FundingFee>();
    ct->set_symbol(sender.req_mgr_.request_obj_->req_.symbol_);
    sender.req_mgr_.x_pass_through_ = store::PassThrough();
    sender.req_mgr_.x_pass_through_->set_funding_fee(ct);
  */
  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}

// reject
TEST_F(StartPassthroughTest, cross_cancel_reject) {
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 0;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = '1';
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 10;
  sender.req_mgr_.request_obj_->req_.price_ = 200000;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = 31;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTCancelOrder;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 0;
  // 组装
  /*
    store::FundingFee::Ptr ct = std::make_shared<store::FundingFee>();
    ct->set_symbol(sender.req_mgr_.request_obj_->req_.symbol_);
    sender.req_mgr_.x_pass_through_ = store::PassThrough();
    sender.req_mgr_.x_pass_through_->set_funding_fee(ct);
  */
  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}

// reject
TEST_F(StartPassthroughTest, cross_replace_reject) {
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 0;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = '1';
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 10;
  sender.req_mgr_.request_obj_->req_.price_ = 200000;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = 31;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTCancelReplaceOrder;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 0;
  // 组装
  /*
    store::FundingFee::Ptr ct = std::make_shared<store::FundingFee>();
    ct->set_symbol(sender.req_mgr_.request_obj_->req_.symbol_);
    sender.req_mgr_.x_pass_through_ = store::PassThrough();
    sender.req_mgr_.x_pass_through_->set_funding_fee(ct);
  */
  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}
// fundingfee 失败后主动告警+panic测试
TEST_F(StartPassthroughTest, fundingfee_error_panic_test) {
  GTEST_SKIP();  // 自测使用,不能用于CI跑,程序会panic
  int32_t err_symbol = 0;
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = '1';
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = err_symbol;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = biz::cross::request::kCreatedByUser;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTSettleFundingFee;
  // 组装
  store::FundingFee::Ptr ct = std::make_shared<store::FundingFee>();
  // ct->set_symbol(sender.req_mgr_.request_obj_->req_.symbol_);
  ct->set_symbol(err_symbol);  // 使用错误的symbol,让校验失败
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  ::convertor::ToPB::Convert(ct.get(), sender.req_mgr_.x_pass_through_.value().mutable_funding_fee());

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}

// 用于测试丢包拉黑uid
TEST_F(StartPassthroughTest, new_block_test) {
  char err_side = '0';
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 0;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = err_side;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = biz::cross::request::kCreatedByUser;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTSettleFundingFee;
  // 组装
  store::FundingFee::Ptr ct = std::make_shared<store::FundingFee>();
  ct->set_symbol(sender.req_mgr_.request_obj_->req_.symbol_);
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  ::convertor::ToPB::Convert(ct.get(), sender.req_mgr_.x_pass_through_.value().mutable_funding_fee());

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}

// 用于测试直连撮合resp时,解析失败未更新cross_seq导致下一条消息来了告警跳号
// side非法而解析失败丢弃
TEST_F(StartPassthroughTest, cross_seq_skip_test) {
  char err_side = '0';
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 0;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = err_side;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = biz::cross::request::kCreatedByUser;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTSettleFundingFee;
  // 组装
  store::FundingFee::Ptr ct = std::make_shared<store::FundingFee>();
  ct->set_symbol(sender.req_mgr_.request_obj_->req_.symbol_);
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  ::convertor::ToPB::Convert(ct.get(), sender.req_mgr_.x_pass_through_.value().mutable_funding_fee());

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}
// 集合竞价相关ut  发送撮合补偿包 obu_bit_index_='0' 忽略信号
TEST_F(StartPassthroughTest, pre_market_exec_match) {
  char err_side = '0';
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = err_side;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = biz::cross::request::kCreatedByUser;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::response::kMTPreMarketExecMatch;
  sender.req_mgr_.request_obj_->req_.obu_bit_index_ = '0';
  // 组装
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  auto dto1 = sender.req_mgr_.x_pass_through_->mutable_pre_market_pass_through_dto();
  auto dto2 = dto1->mutable_premarket_call_auction_mut_exec_cross_match();
  dto2->set_symbol_id(5);

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}

// 集合竞价相关ut  发送撮合补偿包 obu_bit_index_='1' 忽略信号
TEST_F(StartPassthroughTest, pre_market_exec_match1) {
  char err_side = '0';
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = err_side;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = biz::cross::request::kCreatedByUser;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::response::kMTPreMarketExecMatch;
  sender.req_mgr_.request_obj_->req_.obu_bit_index_ = '0';
  // 组装
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  auto dto1 = sender.req_mgr_.x_pass_through_->mutable_pre_market_pass_through_dto();
  auto dto2 = dto1->mutable_premarket_call_auction_mut_exec_cross_match();
  dto2->set_symbol_id(5);

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}

// 集合竞价相关ut  发送撮合补偿包 obu_bit_index_='2' 忽略信号
TEST_F(StartPassthroughTest, pre_market_exec_match2) {
  char err_side = '0';
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = err_side;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = biz::cross::request::kCreatedByUser;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::response::kMTPreMarketExecMatch;
  sender.req_mgr_.request_obj_->req_.obu_bit_index_ = '0';
  // 组装
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  auto dto1 = sender.req_mgr_.x_pass_through_->mutable_pre_market_pass_through_dto();
  auto dto2 = dto1->mutable_premarket_call_auction_mut_exec_cross_match();
  dto2->set_symbol_id(5);

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}

// 集合竞价相关ut  S4状态信号
TEST_F(StartPassthroughTest, pre_market_S4) {
  char err_side = '0';
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 1;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = ESmpType::None;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = '2';
  sender.req_mgr_.request_obj_->req_.side_ = err_side;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = '1';
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 0;
  sender.req_mgr_.request_obj_->req_.price_ = 0;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = biz::cross::request::kCreatedByUser;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::response::kMTPreMarketSymbolStatus;
  sender.req_mgr_.request_obj_->req_.obu_bit_index_ = '0';
  // 组装
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  auto dto1 = sender.req_mgr_.x_pass_through_->mutable_pre_market_pass_through_dto();
  auto dto2 = dto1->mutable_pre_market_continuous_trading_s4();
  dto2->set_symbol_id(5);

  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}

// 期货下单 --> 改单失败 cancel
TEST_F(StartPassthroughTest, repalce_check_failed_cancel) {
  biz::cross::xReqSender sender;
  // 组装包头
  sender.req_mgr_.header_obj_->header_.magic_cookie = biz::cross::kMagicCookie;
  sender.req_mgr_.header_obj_->header_.hdr_version = biz::cross::kVersion;
  sender.req_mgr_.header_obj_->header_.hdr_length = sizeof(biz::cross::RawXHeader);
  sender.req_mgr_.header_obj_->header_.action = 0;
  sender.req_mgr_.header_obj_->header_.offset = 0;
  sender.req_mgr_.header_obj_->header_.length = biz::cross::request::kSizeOfRequest;
  sender.req_mgr_.header_obj_->header_.count = 1;
  sender.req_mgr_.header_obj_->header_.passthrough = 0;
  // 组装包体
  sender.req_mgr_.request_obj_->req_.magic_cookie_ = biz::cross::request::kMagic;
  sender.req_mgr_.request_obj_->req_.msg_version_ = biz::cross::request::kVersion;
  sender.req_mgr_.request_obj_->req_.msg_length_ = sizeof(biz::cross::request::RawXRequest);
  sender.req_mgr_.request_obj_->req_.smp_group_ = 0;
  sender.req_mgr_.request_obj_->req_.sender_comp_id_ = 1001;
  sender.req_mgr_.request_obj_->req_.target_comp_id_ = 0;
  sender.req_mgr_.request_obj_->req_.union_.price_scale_ = 4;
  sender.req_mgr_.request_obj_->req_.fbu_smp_type_ = biz::cross::request::kSMPNone;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.order_type_ = biz::cross::request::kOTLimitOrder;
  sender.req_mgr_.request_obj_->req_.side_ = biz::cross::request::kSideBuy;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = biz::cross::request::kTIFGoodTillCancel;
  sender.req_mgr_.request_obj_->req_.symbol_ = ESymbol::BTCUSD;
  sender.req_mgr_.request_obj_->req_.order_qty_ = 100000000;
  sender.req_mgr_.request_obj_->req_.price_ = 30000;
  sender.req_mgr_.request_obj_->req_.transact_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.transact_time_usec_ = u_int64_t(sending_time % 1000000);
  sender.req_mgr_.request_obj_->req_.created_by_ = biz::cross::request::kCreatedByUser;
  sender.req_mgr_.request_obj_->req_.canceled_by_ = biz::cross::request::kCanceledByUser;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = {0};
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTNewOrder;

  // 下单组装发撮合
  biz::CrossError err;
  biz::cross_idx_t cross_idx = 5;
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  // 改单
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTCancelReplaceOrder;
  sender.req_mgr_.request_obj_->req_.orig_cl_ord_id = sender.req_mgr_.request_obj_->req_.cl_ord_id_;
  sender.req_mgr_.request_obj_->req_.cl_ord_id_ = boost::uuids::random_generator()();
  sender.req_mgr_.request_obj_->req_.price_ = 0;  // 改单价格为零会校验失败
  err = sender.DoSend2Cross(cross_idx, EProductType::Futures);
  ASSERT_NE(err.HasErr(), true);
  auto result3 = te->PopResult();
}
std::shared_ptr<tmock::CTradeAppMock> StartPassthroughTest::te;
