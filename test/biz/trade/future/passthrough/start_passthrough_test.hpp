#pragma once

#include <gtest/gtest.h>

#include <iostream>
#include <memory>

#include "biz_worker/service/trade/store/passthrough/raw_funding_fee.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class StartPassthroughTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }

  store::FundingFee::Ptr BuildFundingFeeReq(biz::symbol_t symbol);
  biz::CrossError SendFundingFee(const std::shared_ptr<const config::ConfigManager> config_mgr,
                                 store::FundingFee::Ptr funding_fee, EProductType product_type);

  static std::shared_ptr<tmock::CTradeAppMock> te;
};
