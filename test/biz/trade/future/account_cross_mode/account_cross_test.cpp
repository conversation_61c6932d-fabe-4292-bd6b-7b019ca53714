#include "test/biz/trade/future/account_cross_mode/account_cross_test.hpp"

#include <cxxabi.h>
#include <sys/errno.h>

#include <string>

#include "data/enum.hpp"
#include "data/error/error.hpp"
#include "enums/eaccountmode/account_mode.pb.h"
#include "enums/eside/side.pb.h"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/futures/modules/orderbiz/activeorderbiz/active_order_biz.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/future/func/draft_pkg_builder.h"
#include "test/biz/trade/lib/msg_builder.hpp"

std::shared_ptr<tmock::CTradeAppMock> AccountCrossTest::te;

// 切换margin mode, isolated, cross, pm
int32_t AccountCrossTest::SwitchMarginMode(stub& user, enums::eaccountmode::AccountMode account_mode) {
  // stub user(PmTest::te, uid);
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(user.m_uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(enums::eaction::Action::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(account_mode);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(user.m_uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }
  return 0;
}

// 充值
int32_t AccountCrossTest::Deposit(stub& user, std::string&& amount, biz::coin_t coin) {
  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string bonus_change = "0";
    FutureDepositBuilder deposit_build(req_id, user.m_uid, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp = user.process(deposit_build.Build());
    auto result = te->PopResult();
    if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
      if (resp->ret_code()) {
        return resp->ret_code();
      }
      return -1;
    }
  }
  return 0;
}

// 切换position mode
int32_t AccountCrossTest::SwitchPositionMode(stub& user, EPositionMode p_mode) {
  FutureOneOfSwitchPositionModeBuilder switch_position_mode(ESymbol::BTCUSDT, ECoin::USDT, p_mode, user.m_uid);
  auto resp = user.process(switch_position_mode.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }
  return 0;
}

// 调杠杆
int32_t AccountCrossTest::SetLeverage(stub& user, biz::leverage_e2_t leverage) {
  FutureOneOfSetLeverageBuilder set_leverage(ESymbol::BTCUSDT, ECoin::USDT, user.m_uid, leverage, leverage);
  auto resp = user.process(set_leverage.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    if (resp->ret_code()) {
      return resp->ret_code();
    }
    return -1;
  }
  return 0;
}

// 调整风险限额
int32_t AccountCrossTest::SetRiskId(stub& user, biz::risk_id_t risk_id) {
  FutureOneOfSetRiskIdBuilder set_risk_id(ESymbol::BTCUSDT, ECoin::USDT, user.m_uid, risk_id);
  auto resp = user.process(set_risk_id.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    if (resp->ret_code()) {
      return resp->ret_code();
    }
    return -1;
  }
  return 0;
}

// 调整风险限额
int32_t AccountCrossTest::SetRiskIdWithPIdx(stub& user, biz::risk_id_t risk_id, EPositionIndex p_idx) {
  FutureOneOfSetRiskIdBuilder set_risk_id(ESymbol::BTCUSDT, ECoin::USDT, user.m_uid, risk_id);
  set_risk_id.msg.mutable_set_risk_id()->set_position_idx(p_idx);
  auto resp = user.process(set_risk_id.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    if (resp->ret_code()) {
      return resp->ret_code();
    }
    return -1;
  }
  return 0;
}

/*
不完美对锁情况下调整风险限额
*/
TEST_F(AccountCrossTest, cross_mode_both_side) {
  biz::user_id_t uid_1 = 10000;
  stub user1(te, uid_1);
  biz::user_id_t uid_2 = 10002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(user1, "1000"));
  EXPECT_EQ(0, Deposit(user2, "1000"));

  EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::AccountMode::Cross));
  EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::AccountMode::Cross));

  EXPECT_EQ(0, SwitchPositionMode(user1, EPositionMode::BothSide));
  EXPECT_EQ(0, SwitchPositionMode(user2, EPositionMode::BothSide));

  // 开仓参数
  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  auto pz_index = EPositionIndex::Buy;
  ESide side;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  // trade_engine 更新基础行情
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);
  // user1 下买单
  {
    side = ESide::Buy;
    pz_index = EPositionIndex::Buy;
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build_1(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, uid_1, coin);
    auto resp1 = user1.process(create_build_1.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 20000000);
  }

  // user2 下卖单
  {
    auto order_id_2 = te->GenUUID();
    side = ESide::Sell;
    pz_index = EPositionIndex::Sell;
    FutureOneOfCreateOrderBuilder create_build_2(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, uid_2, coin);
    auto resp2 = user2.process(create_build_2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 20000000);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);
    EXPECT_EQ(20000000, sell_pz_check.m_msg->size_x());
    EXPECT_EQ(180000000000, sell_pz_check.m_msg->session_value_e8());
    auto future_cfg = config::getTlsCfgMgrRaw()->symbol_config_mgr();

    auto fc = future_cfg->GetSymbolConfig(symbol);
    auto stringname = abi::__cxa_demangle(typeid(fc).name(), 0, 0, 0);
    std::cout << "fc string name: " << stringname << std::endl;
    std::cout << "cur taker_fee_rate: " << fc->default_taker_fee_rate_e8_ << std::endl;
    std::cout << "cur maker_fee_rate: " << fc->default_maker_fee_rate_e8_ << std::endl;
    EXPECT_EQ(18118800000, sell_pz_check.m_msg->min_position_cost_e8());
    EXPECT_EQ(1018800000, sell_pz_check.m_msg->position_mm_with_fee_e8());
    EXPECT_EQ(10126000, sell_pz_check.m_msg->sell_value_to_cost_e8());
    EXPECT_EQ(10114000, sell_pz_check.m_msg->buy_value_to_cost_e8());
    EXPECT_EQ(626000, sell_pz_check.m_msg->sell_value_to_mm_e8());
    EXPECT_EQ(614000, sell_pz_check.m_msg->buy_value_to_mm_e8());
    EXPECT_EQ(0, sell_pz_check.m_msg->position_balance_e8());
    EXPECT_EQ(0, sell_pz_check.m_msg->liq_price_x());
    EXPECT_EQ(0, sell_pz_check.m_msg->bust_price_x());

    auto result3 = te->PopResult();
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(20000000, buy_pz_check.m_msg->size_x());
    EXPECT_EQ(18097200000, buy_pz_check.m_msg->min_position_cost_e8());
    EXPECT_EQ(997200000, buy_pz_check.m_msg->position_mm_with_fee_e8());
    EXPECT_EQ(10126000, buy_pz_check.m_msg->sell_value_to_cost_e8());
    EXPECT_EQ(10114000, buy_pz_check.m_msg->buy_value_to_cost_e8());
    EXPECT_EQ(626000, buy_pz_check.m_msg->sell_value_to_mm_e8());
    EXPECT_EQ(614000, buy_pz_check.m_msg->buy_value_to_mm_e8());
    EXPECT_EQ(0, buy_pz_check.m_msg->position_balance_e8());
    EXPECT_EQ(0, buy_pz_check.m_msg->liq_price_x());
    EXPECT_EQ(0, buy_pz_check.m_msg->bust_price_x());
  }

  {
    auto order_id = te->GenUUID();
    // user1 下卖单
    FutureOneOfCreateOrderBuilder create_build_4(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, uid_1, coin);
    auto resp4 = user1.process(create_build_4.Build());
    auto result4 = te->PopResult();
    ASSERT_NE(result4.m_msg.get(), nullptr);
    auto orderCheck4 = result4.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck4.m_msg, nullptr);
    orderCheck4.Check(10000, 20000000);

    order_id = te->GenUUID();
    side = ESide::Buy;
    pz_index = EPositionIndex::Buy;
    // user2 下买单，形成完美对锁
    FutureOneOfCreateOrderBuilder create_build_5(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, uid_2, coin);
    auto resp5 = user2.process(create_build_5.Build());
    auto result5 = te->PopResult();
    ASSERT_NE(result5.m_msg.get(), nullptr);
    auto orderCheck5 = result5.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck5.m_msg, nullptr);
    orderCheck5.Check(10002, 20000000);
    auto user2_buy_pz_check = result5.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(user2_buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(20000000, user2_buy_pz_check.m_msg->size_x());
    EXPECT_EQ(180000000000, user2_buy_pz_check.m_msg->session_value_e8());
    EXPECT_EQ(10126000, user2_buy_pz_check.m_msg->sell_value_to_cost_e8());
    EXPECT_EQ(10114000, user2_buy_pz_check.m_msg->buy_value_to_cost_e8());
    EXPECT_EQ(626000, user2_buy_pz_check.m_msg->sell_value_to_mm_e8());
    EXPECT_EQ(614000, user2_buy_pz_check.m_msg->buy_value_to_mm_e8());
    EXPECT_EQ(::enums::ecrosslockedmode::CrossLockedMode(ECrossLockedMode::LePosition),
              user2_buy_pz_check.m_msg->cross_locked_mode());
    // minpc = pz_mm = 2 * occ_fee = 2 * 1800 * ((10-1)/10*0.0006) = 2 * 0.972
    EXPECT_EQ(194400000, user2_buy_pz_check.m_msg->min_position_cost_e8());  // 卖仓位的minpc是18118800000
    EXPECT_EQ(194400000, user2_buy_pz_check.m_msg->position_mm_with_fee_e8());
    EXPECT_EQ(0, user2_buy_pz_check.m_msg->position_balance_e8());
    EXPECT_EQ(0, user2_buy_pz_check.m_msg->liq_price_x());
    EXPECT_EQ(0, user2_buy_pz_check.m_msg->bust_price_x());
    auto user2_sell_pz_check = result5.RefFutureMarginResult().RefRelatedPosition(1);
    ASSERT_NE(user2_sell_pz_check.m_msg, nullptr);
    EXPECT_EQ(20000000, user2_sell_pz_check.m_msg->size_x());
    EXPECT_EQ(180000000000, user2_sell_pz_check.m_msg->session_value_e8());
    EXPECT_EQ(10126000, user2_sell_pz_check.m_msg->sell_value_to_cost_e8());
    EXPECT_EQ(10114000, user2_sell_pz_check.m_msg->buy_value_to_cost_e8());
    EXPECT_EQ(626000, user2_sell_pz_check.m_msg->sell_value_to_mm_e8());
    EXPECT_EQ(614000, user2_sell_pz_check.m_msg->buy_value_to_mm_e8());
    EXPECT_EQ(::enums::ecrosslockedmode::CrossLockedMode(ECrossLockedMode::EqPosition),
              user2_sell_pz_check.m_msg->cross_locked_mode());
    EXPECT_EQ(18237600000, user2_sell_pz_check.m_msg->min_position_cost_e8());
    // pz_mm = 2 * occ_fee + net_mm = 2 * 1800 * ((10+1)/10*0.0006) = 2 * 118800000
    EXPECT_EQ(237600000, user2_sell_pz_check.m_msg->position_mm_with_fee_e8());
    EXPECT_EQ(0, user2_sell_pz_check.m_msg->position_balance_e8());
    EXPECT_EQ(0, user2_sell_pz_check.m_msg->liq_price_x());
    EXPECT_EQ(0, user2_sell_pz_check.m_msg->bust_price_x());

    auto result6 = te->PopResult();
    auto user1_sell_pz_check = result6.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(user1_sell_pz_check.m_msg, nullptr);
    EXPECT_EQ(20000000, user1_sell_pz_check.m_msg->size_x());
    EXPECT_EQ(10126000, user1_sell_pz_check.m_msg->sell_value_to_cost_e8());
    EXPECT_EQ(10114000, user1_sell_pz_check.m_msg->buy_value_to_cost_e8());
    EXPECT_EQ(626000, user1_sell_pz_check.m_msg->sell_value_to_mm_e8());
    EXPECT_EQ(614000, user1_sell_pz_check.m_msg->buy_value_to_mm_e8());
    EXPECT_EQ(::enums::ecrosslockedmode::CrossLockedMode(ECrossLockedMode::EqPosition),
              user1_sell_pz_check.m_msg->cross_locked_mode());
    EXPECT_EQ(18237600000, user1_sell_pz_check.m_msg->min_position_cost_e8());  // 买仓位的minpc是18097200000
    EXPECT_EQ(0, user1_sell_pz_check.m_msg->position_balance_e8());
    // pz_mm = 2 * occ_fee + net_mm = 2 * 1800 * ((10+1)/10*0.0006) = 2 * 118800000
    EXPECT_EQ(237600000, user1_sell_pz_check.m_msg->position_mm_with_fee_e8());
    EXPECT_EQ(0, user1_sell_pz_check.m_msg->liq_price_x());
    EXPECT_EQ(0, user1_sell_pz_check.m_msg->bust_price_x());

    auto user1_buy_pz_check = result6.RefFutureMarginResult().RefRelatedPosition(1);
    ASSERT_NE(user1_buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(20000000, user1_buy_pz_check.m_msg->size_x());
    EXPECT_EQ(10126000, user1_buy_pz_check.m_msg->sell_value_to_cost_e8());
    EXPECT_EQ(10114000, user1_buy_pz_check.m_msg->buy_value_to_cost_e8());
    EXPECT_EQ(626000, user1_buy_pz_check.m_msg->sell_value_to_mm_e8());
    EXPECT_EQ(614000, user1_buy_pz_check.m_msg->buy_value_to_mm_e8());
    EXPECT_EQ(::enums::ecrosslockedmode::CrossLockedMode(ECrossLockedMode::LePosition),
              user1_buy_pz_check.m_msg->cross_locked_mode());
    EXPECT_EQ(0, user1_buy_pz_check.m_msg->position_balance_e8());
    // pz_mm = 2 * occ_fee + net_mm = 2 * 1800 * ((10-1)/10*0.0006) = 2 * 97200000
    EXPECT_EQ(194400000, user1_buy_pz_check.m_msg->min_position_cost_e8());
    EXPECT_EQ(194400000, user1_buy_pz_check.m_msg->position_mm_with_fee_e8());
    EXPECT_EQ(0, user1_buy_pz_check.m_msg->liq_price_x());
    EXPECT_EQ(0, user1_buy_pz_check.m_msg->bust_price_x());
  }

  // 当前处于完美对锁状态，调整杠杆和风险限额
  {
    EXPECT_EQ(0, SetLeverage(user1, 500));
    FutureOneOfSetRiskIdBuilder set_risk_id(ESymbol::BTCUSDT, ECoin::USDT, user1.m_uid, 3);
    set_risk_id.msg.mutable_set_risk_id()->set_position_idx(EPositionIndex::Sell);
    auto resp = user1.process(set_risk_id.Build());
    auto result = te->PopResult();
    auto user1_sell_pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(user1_sell_pz_check.m_msg, nullptr);
    EXPECT_EQ(20000000, user1_sell_pz_check.m_msg->size_x());
    EXPECT_EQ(180000000000, user1_sell_pz_check.m_msg->session_value_e8());
    EXPECT_EQ(20132000, user1_sell_pz_check.m_msg->sell_value_to_cost_e8());
    EXPECT_EQ(20108000, user1_sell_pz_check.m_msg->buy_value_to_cost_e8());
    EXPECT_EQ(1632000, user1_sell_pz_check.m_msg->sell_value_to_mm_e8());
    EXPECT_EQ(1608000, user1_sell_pz_check.m_msg->buy_value_to_mm_e8());
    EXPECT_EQ(::enums::ecrosslockedmode::CrossLockedMode(ECrossLockedMode::LePosition),
              user1_sell_pz_check.m_msg->cross_locked_mode());
    // min_cost = 2 * occ_fee + net_mm = 2*1800*(5-1)/5*0.006 = 8.64*2
    EXPECT_EQ(172800000, user1_sell_pz_check.m_msg->min_position_cost_e8());  // 买仓位的minpc是18097200000
    EXPECT_EQ(0, user1_sell_pz_check.m_msg->position_balance_e8());
    // min_cost = pz_mm
    EXPECT_EQ(172800000, user1_sell_pz_check.m_msg->position_mm_with_fee_e8());
    EXPECT_EQ(0, user1_sell_pz_check.m_msg->liq_price_x());
    EXPECT_EQ(0, user1_sell_pz_check.m_msg->bust_price_x());
    // EXPECT_EQ(0, SetRiskIdWithPIdx(user1, 3, data::PositionIndex::kPZIndexBuy));
    // EXPECT_EQ(0, SetRiskIdWithPIdx(user1, 3, data::PositionIndex::kPZIndexSell));
  }

  biz::user_id_t uid_3 = 10004;
  stub user3(te, uid_3);
  EXPECT_EQ(0, Deposit(user3, "1000"));
  EXPECT_EQ(0, SwitchPositionMode(user3, EPositionMode::BothSide));
  // user1 买方向加仓, 加仓后买方向高，卖方向低, 校验minpc和position_mm
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    pz_index = EPositionIndex::Buy;
    qty = 0.1 * 1e8;
    // user1 下买单
    FutureOneOfCreateOrderBuilder create_build_7(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, uid_1, coin);
    auto resp7 = user1.process(create_build_7.Build());
    auto result7 = te->PopResult();
    ASSERT_NE(result7.m_msg.get(), nullptr);
    auto orderCheck7 = result7.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck7.m_msg, nullptr);
    orderCheck7.Check(10000, 10000000);
  }

  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    pz_index = EPositionIndex::Sell;

    // user3 下卖单
    FutureOneOfCreateOrderBuilder create_build_8(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, uid_3, coin);
    auto resp8 = user3.process(create_build_8.Build());
    auto result8 = te->PopResult();
    ASSERT_NE(result8.m_msg.get(), nullptr);
    auto orderCheck8 = result8.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck8.m_msg, nullptr);
    orderCheck8.Check(10004, 10000000);
  }

  {
    auto result9 = te->PopResult();
    // for (auto& pz : result9.RefFutureMarginResult().m_msg->affected_positions()) {
    //   if (pz->position_index() == data::PositionIndex::kPZIndexSell) {
    auto user1_sell_pz_check = result9.RefFutureMarginResult().RefRelatedPositionByPositionIdx(2);
    ASSERT_NE(user1_sell_pz_check.m_msg, nullptr);
    EXPECT_EQ(20000000, user1_sell_pz_check.m_msg->size_x());
    EXPECT_EQ(180000000000, user1_sell_pz_check.m_msg->session_value_e8());
    EXPECT_EQ(20132000, user1_sell_pz_check.m_msg->sell_value_to_cost_e8());
    EXPECT_EQ(20108000, user1_sell_pz_check.m_msg->buy_value_to_cost_e8());
    EXPECT_EQ(632000, user1_sell_pz_check.m_msg->sell_value_to_mm_e8());
    EXPECT_EQ(608000, user1_sell_pz_check.m_msg->buy_value_to_mm_e8());
    EXPECT_EQ(::enums::ecrosslockedmode::CrossLockedMode(ECrossLockedMode::LePosition),
              user1_sell_pz_check.m_msg->cross_locked_mode());
    // minpc = pz_mm = occ_fee = 1800 * ((10+1)/10*0.0006)
    EXPECT_EQ(129600000 << 1, user1_sell_pz_check.m_msg->min_position_cost_e8());
    EXPECT_EQ(129600000 << 1, user1_sell_pz_check.m_msg->position_mm_with_fee_e8());
    EXPECT_EQ(0, user1_sell_pz_check.m_msg->position_balance_e8());
    EXPECT_EQ(0, user1_sell_pz_check.m_msg->liq_price_x());
    EXPECT_EQ(0, user1_sell_pz_check.m_msg->bust_price_x());
    // }

    // else if (pz->position_index() == data::PositionIndex::kPZIndexBuy) {
    auto user1_buy_pz_check = result9.RefFutureMarginResult().RefRelatedPositionByPositionIdx(1);
    ASSERT_NE(user1_buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(30000000, user1_buy_pz_check.m_msg->size_x());
    EXPECT_EQ(270000000000, user1_buy_pz_check.m_msg->session_value_e8());
    EXPECT_EQ(20132000, user1_buy_pz_check.m_msg->sell_value_to_cost_e8());
    EXPECT_EQ(20108000, user1_buy_pz_check.m_msg->buy_value_to_cost_e8());
    EXPECT_EQ(632000, user1_buy_pz_check.m_msg->sell_value_to_mm_e8());
    EXPECT_EQ(608000, user1_buy_pz_check.m_msg->buy_value_to_mm_e8());
    EXPECT_EQ(::enums::ecrosslockedmode::CrossLockedMode(ECrossLockedMode::GtPosition),
              user1_buy_pz_check.m_msg->cross_locked_mode());
    // minpc = 2700 * (0.20108000-0.0006)+ 1800 * ((5-1)/5*0.0006) = 542.16
    EXPECT_EQ(54216000000, user1_buy_pz_check.m_msg->min_position_cost_e8());
    // pz_mm = 2*occ_fee + net_mm = 2*1800 * ((5-1)/5*0.0006) + 900 * (0.01608000-0.0006) = 15.66
    EXPECT_EQ(666000000, user1_buy_pz_check.m_msg->position_mm_with_fee_e8());
    EXPECT_EQ(0, user1_buy_pz_check.m_msg->position_balance_e8());
    EXPECT_EQ(0, user1_buy_pz_check.m_msg->liq_price_x());
    EXPECT_EQ(0, user1_buy_pz_check.m_msg->bust_price_x());
    // }
  }

  // user1 卖方向加仓, 加仓后卖方向高，买方向低, 校验minpc和position_mm
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    pz_index = EPositionIndex::Sell;
    qty = 0.2 * 1e8;
    // user1 下卖单
    FutureOneOfCreateOrderBuilder create_build_7(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, uid_1, coin);
    auto resp7 = user1.process(create_build_7.Build());
    auto result7 = te->PopResult();
    ASSERT_NE(result7.m_msg.get(), nullptr);
    auto orderCheck7 = result7.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck7.m_msg, nullptr);
    orderCheck7.Check(10000, 20000000);
  }

  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    pz_index = EPositionIndex::Buy;

    // user3 下买单
    FutureOneOfCreateOrderBuilder create_build_8(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, uid_3, coin);
    auto resp8 = user3.process(create_build_8.Build());
    auto result8 = te->PopResult();
    ASSERT_NE(result8.m_msg.get(), nullptr);
    auto orderCheck8 = result8.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck8.m_msg, nullptr);
    orderCheck8.Check(10004, 20000000);
  }

  {
    auto result9 = te->PopResult();
    auto user1_sell_pz_check = result9.RefFutureMarginResult().RefRelatedPositionByPositionIdx(2);
    ASSERT_NE(user1_sell_pz_check.m_msg, nullptr);
    EXPECT_EQ(40000000, user1_sell_pz_check.m_msg->size_x());
    EXPECT_EQ(user1_sell_pz_check.m_msg->side(), ::enums::eside::Sell);
    EXPECT_EQ(************, user1_sell_pz_check.m_msg->session_value_e8());
    EXPECT_EQ(20132000, user1_sell_pz_check.m_msg->sell_value_to_cost_e8());
    EXPECT_EQ(20108000, user1_sell_pz_check.m_msg->buy_value_to_cost_e8());
    EXPECT_EQ(632000, user1_sell_pz_check.m_msg->sell_value_to_mm_e8());
    EXPECT_EQ(608000, user1_sell_pz_check.m_msg->buy_value_to_mm_e8());
    EXPECT_EQ(::enums::ecrosslockedmode::CrossLockedMode(ECrossLockedMode::GtPosition),
              user1_sell_pz_check.m_msg->cross_locked_mode());
    // minpc = im + cross_mm = 3600*(0.20132000-0.0006) + 2700 * (6/5*0.0006)
    EXPECT_EQ(72453600000, user1_sell_pz_check.m_msg->min_position_cost_e8());
    // pz_mm = 2 * cross_occ_fee + net_mm = 2*2700*(5+1)/5*0.0006+900*(0.01632-0.0006)
    EXPECT_EQ(903600000, user1_sell_pz_check.m_msg->position_mm_with_fee_e8());
    EXPECT_EQ(0, user1_sell_pz_check.m_msg->position_balance_e8());
    EXPECT_EQ(0, user1_sell_pz_check.m_msg->liq_price_x());
    EXPECT_EQ(0, user1_sell_pz_check.m_msg->bust_price_x());

    auto user1_buy_pz_check = result9.RefFutureMarginResult().RefRelatedPositionByPositionIdx(1);
    ASSERT_NE(user1_buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(30000000, user1_buy_pz_check.m_msg->size_x());
    EXPECT_EQ(user1_buy_pz_check.m_msg->side(), ::enums::eside::Buy);
    EXPECT_EQ(270000000000, user1_buy_pz_check.m_msg->session_value_e8());
    EXPECT_EQ(20132000, user1_buy_pz_check.m_msg->sell_value_to_cost_e8());
    EXPECT_EQ(20108000, user1_buy_pz_check.m_msg->buy_value_to_cost_e8());
    EXPECT_EQ(632000, user1_buy_pz_check.m_msg->sell_value_to_mm_e8());
    EXPECT_EQ(608000, user1_buy_pz_check.m_msg->buy_value_to_mm_e8());
    EXPECT_EQ(::enums::ecrosslockedmode::CrossLockedMode(ECrossLockedMode::LePosition),
              user1_buy_pz_check.m_msg->cross_locked_mode());
    // minpc = 2700 *  ((5-1)/5*0.0006)*2 = 259.20
    EXPECT_EQ(259200000, user1_buy_pz_check.m_msg->min_position_cost_e8());
    // 矮仓位 pz_mm = minpc
    EXPECT_EQ(259200000, user1_buy_pz_check.m_msg->position_mm_with_fee_e8());
    EXPECT_EQ(0, user1_buy_pz_check.m_msg->position_balance_e8());
    EXPECT_EQ(0, user1_buy_pz_check.m_msg->liq_price_x());
    EXPECT_EQ(0, user1_buy_pz_check.m_msg->bust_price_x());
  }

  // 挂单和撤单, 仓位对锁情况不受影响
  {
    EXPECT_EQ(0, Deposit(user1, "1000"));
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    pz_index = EPositionIndex::Sell;
    qty = 0.2 * 1e8;
    // user1 下卖单
    FutureOneOfCreateOrderBuilder create_build_7(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, uid_1, coin);
    auto resp7 = user1.process(create_build_7.Build());
    auto result7 = te->PopResult();
    ASSERT_NE(result7.m_msg.get(), nullptr);
    auto orderCheck7 = result7.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck7.m_msg, nullptr);
    orderCheck7.Check(10000, 20000000);
    {
      auto user1_sell_pz_check = result7.RefFutureMarginResult().RefRelatedPositionByPositionIdx(2);
      ASSERT_NE(user1_sell_pz_check.m_msg, nullptr);
      EXPECT_EQ(40000000, user1_sell_pz_check.m_msg->size_x());
      EXPECT_EQ(user1_sell_pz_check.m_msg->side(), ::enums::eside::Sell);
      EXPECT_EQ(************, user1_sell_pz_check.m_msg->session_value_e8());
      EXPECT_EQ(20132000, user1_sell_pz_check.m_msg->sell_value_to_cost_e8());
      EXPECT_EQ(20108000, user1_sell_pz_check.m_msg->buy_value_to_cost_e8());
      EXPECT_EQ(632000, user1_sell_pz_check.m_msg->sell_value_to_mm_e8());
      EXPECT_EQ(608000, user1_sell_pz_check.m_msg->buy_value_to_mm_e8());
      EXPECT_EQ(::enums::ecrosslockedmode::CrossLockedMode(ECrossLockedMode::GtPosition),
                user1_sell_pz_check.m_msg->cross_locked_mode());
      // minpc = im + cross_mm = 3600*(0.20132000-0.0006) + 2700 * (6/5*0.0006)
      EXPECT_EQ(72453600000, user1_sell_pz_check.m_msg->min_position_cost_e8());
      // pz_mm = 2 * cross_occ_fee + net_mm = 2*2700*(5+1)/5*0.0006+900*(0.01632-0.0006)
      EXPECT_EQ(903600000, user1_sell_pz_check.m_msg->position_mm_with_fee_e8());
      EXPECT_EQ(0, user1_sell_pz_check.m_msg->position_balance_e8());
      EXPECT_EQ(0, user1_sell_pz_check.m_msg->liq_price_x());
      EXPECT_EQ(0, user1_sell_pz_check.m_msg->bust_price_x());

      auto user1_buy_pz_check = result7.RefFutureMarginResult().RefRelatedPositionByPositionIdx(1);
      ASSERT_NE(user1_buy_pz_check.m_msg, nullptr);
      EXPECT_EQ(30000000, user1_buy_pz_check.m_msg->size_x());
      EXPECT_EQ(user1_buy_pz_check.m_msg->side(), ::enums::eside::Buy);
      EXPECT_EQ(270000000000, user1_buy_pz_check.m_msg->session_value_e8());
      EXPECT_EQ(20132000, user1_buy_pz_check.m_msg->sell_value_to_cost_e8());
      EXPECT_EQ(20108000, user1_buy_pz_check.m_msg->buy_value_to_cost_e8());
      EXPECT_EQ(632000, user1_buy_pz_check.m_msg->sell_value_to_mm_e8());
      EXPECT_EQ(608000, user1_buy_pz_check.m_msg->buy_value_to_mm_e8());
      EXPECT_EQ(::enums::ecrosslockedmode::CrossLockedMode(ECrossLockedMode::LePosition),
                user1_buy_pz_check.m_msg->cross_locked_mode());
      // minpc = 2700 *  ((5-1)/5*0.0006)*2 = 259.20
      EXPECT_EQ(259200000, user1_buy_pz_check.m_msg->min_position_cost_e8());
      // 矮仓位 pz_mm = minpc
      EXPECT_EQ(259200000, user1_buy_pz_check.m_msg->position_mm_with_fee_e8());
      EXPECT_EQ(0, user1_buy_pz_check.m_msg->position_balance_e8());
      EXPECT_EQ(0, user1_buy_pz_check.m_msg->liq_price_x());
      EXPECT_EQ(0, user1_buy_pz_check.m_msg->bust_price_x());
    }

    FutureOneOfCancelOrderBuilder cancel_build_8(order_id, symbol, uid_1, coin);
    auto resp8 = user1.process(cancel_build_8.Build());
    auto result8 = te->PopResult();
    ASSERT_NE(result8.m_msg.get(), nullptr);
    auto orderCheck8 = result8.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck8.m_msg, nullptr);
    orderCheck8.Check(10000, 20000000);

    // user1 下买单
    order_id = te->GenUUID();
    side = ESide::Buy;
    pz_index = EPositionIndex::Buy;

    FutureOneOfCreateOrderBuilder create_build_9(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, uid_1, coin);
    auto resp9 = user1.process(create_build_9.Build());
    auto result9 = te->PopResult();
    ASSERT_NE(result9.m_msg.get(), nullptr);
    auto orderCheck9 = result9.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck9.m_msg, nullptr);
    orderCheck9.Check(10000, 20000000);

    {
      auto user1_sell_pz_check = result9.RefFutureMarginResult().RefRelatedPositionByPositionIdx(2);
      ASSERT_NE(user1_sell_pz_check.m_msg, nullptr);
      EXPECT_EQ(40000000, user1_sell_pz_check.m_msg->size_x());
      EXPECT_EQ(user1_sell_pz_check.m_msg->side(), ::enums::eside::Sell);
      EXPECT_EQ(************, user1_sell_pz_check.m_msg->session_value_e8());
      EXPECT_EQ(20132000, user1_sell_pz_check.m_msg->sell_value_to_cost_e8());
      EXPECT_EQ(20108000, user1_sell_pz_check.m_msg->buy_value_to_cost_e8());
      EXPECT_EQ(632000, user1_sell_pz_check.m_msg->sell_value_to_mm_e8());
      EXPECT_EQ(608000, user1_sell_pz_check.m_msg->buy_value_to_mm_e8());
      EXPECT_EQ(::enums::ecrosslockedmode::CrossLockedMode(ECrossLockedMode::GtPosition),
                user1_sell_pz_check.m_msg->cross_locked_mode());
      // minpc = im + cross_mm = 3600*(0.20132000-0.0006) + 2700 * (6/5*0.0006)
      EXPECT_EQ(72453600000, user1_sell_pz_check.m_msg->min_position_cost_e8());
      // pz_mm = 2 * cross_occ_fee + net_mm = 2*2700*(5+1)/5*0.0006+900*(0.01632-0.0006)
      EXPECT_EQ(903600000, user1_sell_pz_check.m_msg->position_mm_with_fee_e8());
      EXPECT_EQ(0, user1_sell_pz_check.m_msg->position_balance_e8());
      EXPECT_EQ(0, user1_sell_pz_check.m_msg->liq_price_x());
      EXPECT_EQ(0, user1_sell_pz_check.m_msg->bust_price_x());

      auto user1_buy_pz_check = result9.RefFutureMarginResult().RefRelatedPositionByPositionIdx(1);
      ASSERT_NE(user1_buy_pz_check.m_msg, nullptr);
      EXPECT_EQ(30000000, user1_buy_pz_check.m_msg->size_x());
      EXPECT_EQ(user1_buy_pz_check.m_msg->side(), ::enums::eside::Buy);
      EXPECT_EQ(270000000000, user1_buy_pz_check.m_msg->session_value_e8());
      EXPECT_EQ(20132000, user1_buy_pz_check.m_msg->sell_value_to_cost_e8());
      EXPECT_EQ(20108000, user1_buy_pz_check.m_msg->buy_value_to_cost_e8());
      EXPECT_EQ(632000, user1_buy_pz_check.m_msg->sell_value_to_mm_e8());
      EXPECT_EQ(608000, user1_buy_pz_check.m_msg->buy_value_to_mm_e8());
      EXPECT_EQ(::enums::ecrosslockedmode::CrossLockedMode(ECrossLockedMode::LePosition),
                user1_buy_pz_check.m_msg->cross_locked_mode());
      // minpc = 2700 *  ((5-1)/5*0.0006)*2 = 259.20
      EXPECT_EQ(259200000, user1_buy_pz_check.m_msg->min_position_cost_e8());
      // 矮仓位 pz_mm = minpc
      EXPECT_EQ(259200000, user1_buy_pz_check.m_msg->position_mm_with_fee_e8());
      EXPECT_EQ(0, user1_buy_pz_check.m_msg->position_balance_e8());
      EXPECT_EQ(0, user1_buy_pz_check.m_msg->liq_price_x());
      EXPECT_EQ(0, user1_buy_pz_check.m_msg->bust_price_x());
    }

    FutureOneOfCancelOrderBuilder cancel_build_10(order_id, symbol, uid_1, coin);
    auto resp10 = user1.process(cancel_build_10.Build());
    auto result10 = te->PopResult();
    ASSERT_NE(result10.m_msg.get(), nullptr);
    auto orderCheck10 = result10.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck10.m_msg, nullptr);
    orderCheck10.Check(10000, 20000000);
  }
}
