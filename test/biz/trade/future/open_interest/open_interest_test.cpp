#include "test/biz/trade/future/open_interest/open_interest_test.hpp"

#include <biz_worker/service/trade/futures/modules/commonbiz/validation_biz.hpp>
#include <biz_worker/service/trade/spot/modules/commonbiz/init_biz.hpp>

#include "src/biz_worker/service/trade/store/per_worker_store.hpp"
#include "src/biz_worker/service/trade/store/user_data_manager.hpp"
#include "src/data/event/biz_event.hpp"

int32_t OpenInterestTest::SwitchMarginMode(stub& user, enums::eaccountmode::AccountMode account_mode) {
  // stub user(PmTest::te, uid);
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(user.m_uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(enums::eaction::Action::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(account_mode);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(user.m_uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }
  return 0;
}

int32_t OpenInterestTest::Deposit(stub& user, std::string amount, biz::coin_t coin) {
  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, user.m_uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

TEST_F(OpenInterestTest, check_test) {
  // common fields init
  biz::user_id_t user_id = 10025;
  biz::account_id_t account_id = 10025;
  biz::coin_t coin = ECoin::USDT;
  biz::symbol_t symbol = ESymbol::BTCUSDT;

  // header init
  auto header = std::make_shared<store::Header>();
  header->op_from = "op_from_ut";
  header->user_ip = "127.0.0.1";
  header->uid = user_id;
  header->coin = coin;
  header->account_id = account_id;
  header->symbol = symbol;

  store::PerWorkerStore* per_worker_store = new store::PerWorkerStore();
  std::shared_ptr<store::ExceededResult> btcusdt_exceeded_result = std::make_shared<store::ExceededResult>();
  btcusdt_exceeded_result->buy_exceeded_result_map.insert({user_id, 1000 * 1e8});
  per_worker_store->user_exceeded_result_map_->insert({ESymbol::BTCUSDT, btcusdt_exceeded_result});
  std::shared_ptr<store::ExceededResult> ethusdt_exceeded_result = std::make_shared<store::ExceededResult>();
  ethusdt_exceeded_result->sell_exceeded_result_map.insert({user_id, 1000 * 1e8});
  per_worker_store->user_exceeded_result_map_->insert({ESymbol::ETHUSDT, ethusdt_exceeded_result});

  store::UserDataManager* user_data_manager_ = new store::UserDataManager();
  store::PerUserStore* per_user_store = user_data_manager_->GetUserData(user_id);
  // 初始化
  if (per_user_store == nullptr) {
    user_data_manager_->CreateUserData(user_id);
    per_user_store = user_data_manager_->GetUserData(user_id);
  }
  per_user_store->working_coins_[ECoin::USDC] = {};

  {
    biz::DraftPkg::Ptr draft_pkg = std::make_shared<biz::DraftPkg>(nullptr, header, per_worker_store, per_user_store);
    // 1. BTCUSDT buy open limited
    auto open_limited = draft_pkg->w_store->IsBuyOpenLimited(user_id, ESymbol::BTCUSDT);
    EXPECT_TRUE(open_limited);
    // 2. BTCUSDT sell open unlimited
    open_limited = draft_pkg->w_store->IsSellOpenLimited(user_id, ESymbol::BTCUSDT);
    EXPECT_FALSE(open_limited);
    // 3. ETHUSDT buy open unlimited
    open_limited = draft_pkg->w_store->IsBuyOpenLimited(user_id, ESymbol::ETHUSDT);
    EXPECT_FALSE(open_limited);
    // 4. ETHUSDT buy open limited
    open_limited = draft_pkg->w_store->IsSellOpenLimited(user_id, ESymbol::ETHUSDT);
    EXPECT_TRUE(open_limited);
    // 5. other user BTCUSDT buy open unlimited
    open_limited = draft_pkg->w_store->IsBuyOpenLimited(10026, ESymbol::BTCUSDT);
    EXPECT_FALSE(open_limited);
    // 6. other user BTCUSDT sell open unlimited
    open_limited = draft_pkg->w_store->IsSellOpenLimited(10026, ESymbol::BTCUSDT);
    EXPECT_FALSE(open_limited);
  }
}

TEST_F(OpenInterestTest, CheckOILimitedForCreate) {
  // common fields init
  biz::user_id_t user_id = 10025;
  biz::account_id_t account_id = 10025;
  biz::coin_t coin = ECoin::USDT;
  biz::symbol_t symbol = ESymbol::BTCUSDT;

  // header init
  auto header = std::make_shared<store::Header>();
  header->op_from = "op_from_ut";
  header->user_ip = "127.0.0.1";
  header->uid = user_id;
  header->coin = coin;
  header->account_id = account_id;
  header->symbol = symbol;

  store::PerWorkerStore* per_worker_store = new store::PerWorkerStore();
  std::shared_ptr<store::ExceededResult> btcusdt_exceeded_result = std::make_shared<store::ExceededResult>();
  btcusdt_exceeded_result->buy_exceeded_result_map.insert({user_id, 1000 * 1e8});
  per_worker_store->user_exceeded_result_map_->insert({ESymbol::BTCUSDT, btcusdt_exceeded_result});
  std::shared_ptr<store::ExceededResult> ethusdt_exceeded_result = std::make_shared<store::ExceededResult>();
  ethusdt_exceeded_result->sell_exceeded_result_map.insert({user_id, 1000 * 1e8});
  per_worker_store->user_exceeded_result_map_->insert({ESymbol::ETHUSDT, ethusdt_exceeded_result});

  store::UserDataManager* user_data_manager_ = new store::UserDataManager();
  store::PerUserStore* per_user_store = user_data_manager_->GetUserData(user_id);
  // 初始化
  if (per_user_store == nullptr) {
    user_data_manager_->CreateUserData(user_id);
    per_user_store = user_data_manager_->GetUserData(user_id);
  }
  per_user_store->working_coins_[ECoin::USDC] = {};

  biz::DraftPkg::Ptr draft_pkg = std::make_shared<biz::DraftPkg>(nullptr, header, per_worker_store, per_user_store);

  svc::trading::req::CreateOrderReq create_req;
  create_req.set_side(enums::eside::Buy);
  create_req.set_symbol(enums::esymbol::BTCUSDT);
  create_req.set_order_id(te->GenUUID());
  create_req.set_create_type(enums::ecreatetype::CreateByUser);
  create_req.set_qty_x((0.1 * 1e8));
  create_req.set_price_x(0);
  create_req.set_order_type(enums::eordertype::Market);
  auto ret = biz::commonbiz::ValidationBiz::CheckOILimitedForCreate(draft_pkg.get(), create_req);
  EXPECT_EQ(ret, error::ErrorCode::kErrorOpenInterestRuleNotSatisfied);
  create_req.set_reduce_only(true);
  ret = biz::commonbiz::ValidationBiz::CheckOILimitedForCreate(draft_pkg.get(), create_req);
  EXPECT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
  create_req.set_side(enums::eside::Sell);
  ret = biz::commonbiz::ValidationBiz::CheckOILimitedForCreate(draft_pkg.get(), create_req);
  EXPECT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
}

std::shared_ptr<tmock::CTradeAppMock> OpenInterestTest::te;
