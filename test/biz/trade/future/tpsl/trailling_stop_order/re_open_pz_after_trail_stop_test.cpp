//
// Created by Gdso.li on 31/3/2023.
//
#include "test/biz/trade/future/tpsl/trailling_stop_order/re_open_pz_after_trail_stop_test.hpp"

#include <unistd.h>

#include <string>

#include "enums/ecrossstatus/cross_status.pb.h"
#include "enums/eorderstatus/order_status.pb.h"
#include "src/data/enum.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

std::shared_ptr<tmock::CTradeAppMock> ReOpenPzAfterTrailStopTest::te;

TEST_F(ReOpenPzAfterTrailStopTest, TriggerTs_ReOpenPz_AfterTrailStop) {
  biz::coin_t const coin = 5;       // BTC
  biz::coin_t const cross_idx = 5;  // BTCUSDT
  biz::symbol_t const symbol = 5;   // BTCUSDT
  EPositionIndex const pz_index = EPositionIndex::Single;
  EOrderType const order_type = EOrderType::Limit;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;
  ESmpType smp_type = ESmpType::CancelTaker;  // 避免ut之间有影响，使用maker模式

  // user1
  biz::user_id_t const uid_1 = 653408;
  stub user1(te, uid_1);

  // opposite user2
  biz::user_id_t const uid_2 = 652604;
  stub user2(te, uid_2);

  // user1 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin2 = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid_1, trans_id, coin2, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult(100);
  }

  // user2 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin2 = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid_2, trans_id, coin2, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult(100);
  }

  // 校准标记价格 (mark_price)10000
  {
    auto const underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 校准最新价格 (last_price)10000
  {
    auto lv0_px = "10000";
    auto lv0_qty = "100";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty);
  }

  // case准备 -- 构建持仓 10qty
  {
    // user1: create position 10qty
    biz::size_x_t const pz_qty = 10 * 1e6;
    biz::price_x_t const pz_price = 10000 * 1e4;

    // user1 卖出开仓订单 10qty
    auto user1_pz_buy_id = "d7204aa0-13d0-400f-890e-1a85b478b9a2";
    FutureOneOfCreateOrderBuilder pz_buy_order_build(user1_pz_buy_id, symbol, pz_index, ESide::Sell, order_type, pz_qty,
                                                     pz_price, time_in_force, uid_1, coin, smp_type);
    auto pz_buy_order_resp = user1.process(pz_buy_order_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 sell单result
    auto pz_buy_order_result = te->PopResult(100);
    ASSERT_NE(pz_buy_order_result.m_msg.get(), nullptr);
    auto pz_buy_order_check = pz_buy_order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(user1_pz_buy_id);
    ASSERT_NE(pz_buy_order_check.m_msg, nullptr);
    pz_buy_order_check.CheckQty(uid_1, pz_qty);
    pz_buy_order_check.CheckPrice(uid_1, pz_price);
    pz_buy_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // user2 卖出开仓订单 10qty（主要为了给user1建立持仓）
    auto user2_pz_sell_id = "7ebd5667-c0e9-45c5-81dc-d086b99ff3a5";
    FutureOneOfCreateOrderBuilder pz_sell_order_build(user2_pz_sell_id, symbol, pz_index, ESide::Buy, order_type,
                                                      pz_qty, pz_price, time_in_force, uid_2, coin, smp_type);
    auto pz_sell_order_resp = user2.process(pz_sell_order_build.Build());
    ASSERT_EQ(pz_sell_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 buy单result
    auto pz_sell_order_result = te->PopResult(100);
    {
      std::string msg{};
      (void)google::protobuf::util::MessageToJsonString(*pz_sell_order_result.m_msg, &msg);
      std::cout << "#0: result:" << msg << std::endl;
    }
    ASSERT_NE(pz_sell_order_result.m_msg.get(), nullptr);
    auto pz_sell_order_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(user2_pz_sell_id);
    ASSERT_NE(pz_sell_order_check.m_msg, nullptr);
    pz_sell_order_check.CheckQty(uid_2, pz_qty);
    pz_sell_order_check.CheckPrice(uid_2, pz_price);
    pz_sell_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 检查user2 持仓
    auto user2_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user2_pz_check.CheckUid(uid_2);
    user2_pz_check.CheckSize(pz_qty);
    user2_pz_check.CheckSize(pz_qty);
    user2_pz_check.CheckFreeSize(-pz_qty);

    // 收user1 sell单成交回报
    pz_buy_order_result = te->PopResult(1000);
    {
      std::string msg{};
      (void)google::protobuf::util::MessageToJsonString(*pz_buy_order_result.m_msg, &msg);
      std::cout << "#1: result:" << msg << std::endl;
    }
    ASSERT_NE(pz_buy_order_result.m_msg.get(), nullptr);
    // 检查user1 持仓
    auto user1_pz_check = pz_buy_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user1_pz_check.CheckSize(pz_qty);
  }

  // ============================================================================================================
  // 以下全是user1的操作
  // ============================================================================================================

  // #1 设置一个追踪止盈，激发11000，回撤百分之10
  {
    FutureOneOfSetTpSlTsBuilder set_tp_sl_ts_builder;
    set_tp_sl_ts_builder.SetValue(uid_1, coin, symbol, pz_index, {}, {}, {}, {}, ETriggerBy::LastPrice, {}, {}, {}, {},
                                  {}, {}, {}, {}, {}, {}, {}, {}, {});
    set_tp_sl_ts_builder.msg.mutable_set_tp_sl_ts()->set_trail_value_percentage_e4(10 * 1e2);  // 10%
    auto set_tp_sl_ts_resp = user1.process(set_tp_sl_ts_builder.Build());
    ASSERT_EQ(set_tp_sl_ts_resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    // result
    auto set_tp_sl_ts_result = te->PopResult(100);
    ASSERT_NE(set_tp_sl_ts_result.m_msg.get(), nullptr);

    const auto& result = set_tp_sl_ts_result.m_msg->futures_margin_result();
    ASSERT_EQ(result.related_orders().size(), 1);
    ASSERT_EQ(result.related_orders(0).trigger_price_x(), 11000 * 1e4);
    ASSERT_EQ(result.affected_positions(0).ts_trigger_price_p_x(), 11000 * 1e4);
    ASSERT_EQ(result.affected_positions(0).trailing_stop_x(), 1000 * 1e4);
    ASSERT_EQ(result.related_orders(0).has_trail_value_percentage_e4(), true);
    ASSERT_EQ(result.affected_positions(0).has_trail_value_percentage_e4(), true);
    ASSERT_EQ(result.related_orders(0).trail_value_percentage_e4(), 10 * 1e2);
    ASSERT_EQ(result.affected_positions(0).trail_value_percentage_e4(), 10 * 1e2);
    ASSERT_EQ(result.affected_positions(0).activation_price_x(), 0);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result, &msg);
    std::cout << "#2: result:" << msg << std::endl;
  }

  // #2 user2下sell单，接user1的平仓单
  {
    biz::size_x_t const pz_qty = 20 * 1e6;
    biz::price_x_t const pz_price = 10500 * 1e4;

    // user2 卖出订单 20qty
    auto user2_close_pz_sell_id = "a1f1f8c9-5777-48e0-969c-91e6452817de";
    FutureOneOfCreateOrderBuilder pz_buy_order_build(user2_close_pz_sell_id, symbol, pz_index, ESide::Sell, order_type,
                                                     pz_qty, pz_price, time_in_force, uid_2, coin, smp_type);
    auto pz_buy_order_resp = user2.process(pz_buy_order_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto sell_order_result = te->PopResult(100);
    {
      std::string msg{};
      (void)google::protobuf::util::MessageToJsonString(*sell_order_result.m_msg, &msg);
      std::cout << "#0: result:" << msg << std::endl;
    }
    ASSERT_NE(sell_order_result.m_msg.get(), nullptr);
    auto pz_sell_order_check =
        sell_order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(user2_close_pz_sell_id);
    ASSERT_NE(pz_sell_order_check.m_msg, nullptr);
    pz_sell_order_check.CheckQty(uid_2, pz_qty);
    pz_sell_order_check.CheckPrice(uid_2, pz_price);
    pz_sell_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
  }

  // #3 行情到达11000，触发条件单
  {
    auto lv0_px = "11000";
    auto lv0_qty = "0.01";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty);

    // 检查result
    auto try_times = 0;
    auto set_tp_sl_ts_result = te->PopResult();
    if (set_tp_sl_ts_result.m_msg == nullptr) {
      // 第一次查不到，阻塞查
      while (try_times < 3) {
        set_tp_sl_ts_result = te->PopResult(100);
        if (set_tp_sl_ts_result.m_msg == nullptr) {
          usleep(100000);
          try_times++;
          continue;
        }
      }
    }
    ASSERT_NE(set_tp_sl_ts_result.m_msg.get(), nullptr);

    const auto& result = set_tp_sl_ts_result.m_msg->futures_margin_result();
    ASSERT_EQ(result.related_orders().size(), 1);
    ASSERT_EQ(result.related_orders(0).order_status(), EOrderStatus::Triggered);
    ASSERT_EQ(result.related_orders(0).cross_status(), ECrossStatus::Init);
    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result, &msg);
    std::cout << "#6: result:" << msg << std::endl;
  }

  // 追踪出场成交， 取尽result
  {
    auto result = te->PopResult();
    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg, &msg);
    std::cout << "#7: result:" << msg << std::endl;
  }

  // user1 重新开仓，检查tsTriggerPrice字段
  {
    biz::size_x_t const pz_qty = 10 * 1e6;
    biz::price_x_t const pz_price = 10500 * 1e4;
    // user1 买入开仓订单 10qty
    auto reopen_pz_id = "a96b81f5-**************-aedffd852a94";
    FutureOneOfCreateOrderBuilder reopen_pz_order_build(reopen_pz_id, symbol, pz_index, ESide::Buy, order_type, pz_qty,
                                                        pz_price, time_in_force, uid_1, coin, smp_type);
    auto pz_buy_order_resp = user1.process(reopen_pz_order_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收之前user2 buy单result
    auto user2_reopen_pz_result = te->PopResult(100);
    {
      std::string msg{};
      (void)google::protobuf::util::MessageToJsonString(*user2_reopen_pz_result.m_msg, &msg);
      std::cout << "#8: result:" << msg << std::endl;
    }

    // 收 user1 sell单result
    auto user1_reopen_pz_result = te->PopResult(100);
    {
      std::string msg{};
      (void)google::protobuf::util::MessageToJsonString(*user1_reopen_pz_result.m_msg, &msg);
      std::cout << "#9: result:" << msg << std::endl;
    }
    ASSERT_NE(user1_reopen_pz_result.m_msg.get(), nullptr);
    auto future_result = user1_reopen_pz_result.RefFutureMarginResult();
    auto pz_sell_order_check = future_result.RefRelatedOrderByOrderId(reopen_pz_id);
    ASSERT_NE(pz_sell_order_check.m_msg, nullptr);
    pz_sell_order_check.CheckQty(uid_1, pz_qty);
    pz_sell_order_check.CheckPrice(uid_1, pz_price);
    pz_sell_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    auto pz_check = future_result.RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    ASSERT_EQ(pz_check.m_msg->ts_trigger_price_p_x(), 0);
  }
}
