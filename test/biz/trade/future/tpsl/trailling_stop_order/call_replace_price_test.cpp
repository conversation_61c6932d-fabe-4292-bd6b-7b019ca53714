#include <boost/uuid/uuid_io.hpp>

#include "gtest/gtest.h"
#include "lib/stub.hpp"
#include "src/data/enum.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"
#include "test/mocks/hdts/message.hpp"

class TestTraillingStopOrderCallReplacePrice : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestCase() {}
  static void TearDownTestCase() { /*app::GlobalVarManager::Instance().coin_ = ECoin::UNKNOWN;*/ }

 protected:
  void Deposit(stub& user1) {
    // 充值
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, user1.m_uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                       std::to_string(10'000'000'000), bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    // std::cout << "deposit result :" << deposit_result.m_msg->DebugString() << std::endl;
  }

  void UpdateMarketData(int64_t price) {
    // auto const underlying_price = bbase::decimal::Decimal<>("10150");
    te->AddMarkPrice(static_cast<ESymbol>(symbol), price, price, price);
  }
  void NewLine() { std::cout << std::endl << "-----------------------------------------" << std::endl; }

 protected:
  std::shared_ptr<tmock::CTradeAppMock> te;
  biz::coin_t coin = ECoin::USDT;
  biz::symbol_t const symbol = 5;
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
};

#define TestTraillingStopOrderCallReplacePrice_PrintResult

TEST_F(TestTraillingStopOrderCallReplacePrice, CallReplacePriceBuy) {
  biz::user_id_t user_id1 = 653106;
  // 构建客户端
  stub user1(te, user_id1);
  // 设置逐仓10x杠杆+充钱
  Deposit(user1);

  UpdateMarketData(10000);

  biz::user_id_t user_id2 = 111111;
  stub user2(te, user_id2);
  // 设置逐仓10x杠杆+充钱
  Deposit(user2);

  // 开仓做多: +100@$10,000
  std::string buy_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::UNKNOWN, -1,
                                  ETriggerBy::UNKNOWN, -1, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestTraillingStopOrderCallReplacePrice_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    buy_order_id = orderCheck.m_msg->order_id();
  }

  // user_id2开仓 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id2, coin, ETriggerBy::UNKNOWN, -1,
                                  ETriggerBy::UNKNOWN, -1, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestTraillingStopOrderCallReplacePrice_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
  }

  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestTraillingStopOrderCallReplacePrice_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
  // 设置追踪出场
  std::string ts_order_id;
  {
    FutureOneOfSetTpSlTsBuilder msg_build;
    msg_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::UNKNOWN, -1,
                       ETriggerBy::LastPrice, 1000, -1, -1, -1, "", "", 0, ETpSlMode::Full, EOrderType::UNKNOWN,
                       EOrderType::UNKNOWN, -1, -1, true);
    auto resp1 = user1.process(msg_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);

#ifdef TestTraillingStopOrderCallReplacePrice_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.m_msg->futures_margin_result().related_orders().size(), 1);
    for (auto& order : result1.m_msg->futures_margin_result().related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::TrailingStop) {
        ts_order_id = order.order_id();
        orderCheck.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_TRUE(true);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 1000, 0);
  }

  sleep(1);

  {
    // 模拟市价行情波动到10500
    auto quote_data_synchronizer = te->quote_data_synchronizer();
    mock::HdtsMessage produce_msg;
    dtssdk::Headers* ptr_hdr = dtssdk::Headers::Create();
    ptr_hdr->Add("type", "3");
    ptr_hdr->Add("snap", "1");
    produce_msg.headers_.assign(reinterpret_cast<char*>(ptr_hdr->Cptr()), ptr_hdr->Size());

    ::models::quote::Orderbooks orderbooks;
    orderbooks.set_biztype(::models::quote::Orderbooks::FUTURE);
    orderbooks.set_geartype(::models::quote::Orderbooks::GEAR200);
    orderbooks.set_crossidx(5);
    auto& depth_map = *orderbooks.mutable_depths();
    ::models::quote::OrderbookInfo info;
    info.set_symbolid(5);
    info.set_crossseq(791);
    info.set_transacttimeus(1680074868193508);
    info.add_deals("10500");
    auto& price_map = *info.mutable_price();
    ::models::quote::PriceStat pricestat;
    auto openPrice = new ::models::quote::PriceAttribute;
    openPrice->set_price("10500");
    openPrice->set_transacttimeus(1680074868193508);
    pricestat.set_allocated_openprice(openPrice);

    auto highPrice = new ::models::quote::PriceAttribute;
    highPrice->set_price("10500");
    highPrice->set_transacttimeus(1680074868193508);
    pricestat.set_allocated_highprice(highPrice);

    auto lowPrice = new ::models::quote::PriceAttribute;
    lowPrice->set_price("10500");
    lowPrice->set_transacttimeus(1680074868193508);
    pricestat.set_allocated_lowprice(lowPrice);

    auto closePrice = new ::models::quote::PriceAttribute;
    closePrice->set_price("10500");
    closePrice->set_transacttimeus(1680074868193508);
    pricestat.set_allocated_closeprice(closePrice);

    price_map[1] = pricestat;

    ::models::quote::DepthInfo* buy1 = info.add_buys();
    buy1->set_price("10500");
    buy1->set_quantity("0.01");

    ::models::quote::DepthInfo* sell1 = info.add_sells();
    sell1->set_price("10500");
    sell1->set_quantity("0.01");

    depth_map[ESymbol::BTCUSDT] = info;

    produce_msg.topic_ = "quote2trading.ob.5";
    produce_msg.pay_load_ = orderbooks.SerializeAsString();

    auto receiver = quote_data_synchronizer->GetReceiver(5);
    EXPECT_NE(receiver, nullptr);
    auto handle = receiver->msg_handler();
    EXPECT_NE(handle, nullptr);

    handle->OnConsumeCallback(produce_msg);
    dtssdk::Headers::DestroyHeaders(ptr_hdr);
  }

  // UpdateMarketData(10100);
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);

#ifdef TestTraillingStopOrderCallReplacePrice_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_NE(result1.m_msg->header().req_init_at_e9(), 0);
    EXPECT_NE(result1.m_msg->header().req_recv_at_e9(), 0);
    EXPECT_NE(result1.m_msg->header().req_expire_at_e9(), 0);
    EXPECT_EQ(result1.m_msg->header().op_from(), "trigger");
    EXPECT_EQ(result1.m_msg->header().req_from(), "trigger");
    EXPECT_EQ(result1.m_msg->header().req_init_at_e9(), 1680074868193508 * 1000);
    for (auto& order : result1.m_msg->futures_margin_result().related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::TrailingStop) {
        ts_order_id = order.order_id();
        orderCheck.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
        EXPECT_EQ(ts_order_id, order.order_id());
        EXPECT_EQ(order.base_price_x(), 10500'0000);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 1000, 0);
  }
}

TEST_F(TestTraillingStopOrderCallReplacePrice, CallReplacePriceSell) {
  biz::user_id_t user_id1 = 653106;
  // 构建客户端
  stub user1(te, user_id1);
  // 设置逐仓10x杠杆+充钱
  Deposit(user1);

  UpdateMarketData(10000);

  biz::user_id_t user_id2 = 111111;
  stub user2(te, user_id2);
  // 设置逐仓10x杠杆+充钱
  Deposit(user2);

  // 开仓做多: +100@$10,000
  std::string buy_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::UNKNOWN, -1,
                                  ETriggerBy::UNKNOWN, -1, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestTraillingStopOrderCallReplacePrice_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    buy_order_id = orderCheck.m_msg->order_id();
  }

  // user_id2开仓 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id2, coin, ETriggerBy::UNKNOWN, -1,
                                  ETriggerBy::UNKNOWN, -1, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestTraillingStopOrderCallReplacePrice_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
  }

  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestTraillingStopOrderCallReplacePrice_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
  // 设置追踪出场
  std::string ts_order_id;
  {
    FutureOneOfSetTpSlTsBuilder msg_build;
    msg_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::UNKNOWN, -1,
                       ETriggerBy::LastPrice, 1000, -1, -1, -1, "", "", 0, ETpSlMode::Full, EOrderType::UNKNOWN,
                       EOrderType::UNKNOWN, -1, -1, true);
    auto resp1 = user1.process(msg_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);

#ifdef TestTraillingStopOrderCallReplacePrice_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.m_msg->futures_margin_result().related_orders().size(), 1);
    for (auto& order : result1.m_msg->futures_margin_result().related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::TrailingStop) {
        ts_order_id = order.order_id();
        orderCheck.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_TRUE(true);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 1000, 0);
  }

  sleep(1);

  {
    auto lv0_px = "9500";
    auto lv0_qty = "0.01";
    te->UpdateQuoteData(symbol, ECrossIdx::BTCLP, lv0_px, lv0_qty);
  }

  // UpdateMarketData(10100);
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);

#ifdef TestTraillingStopOrderCallReplacePrice_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.m_msg->futures_margin_result().related_orders().size(), 1);
    for (auto& order : result1.m_msg->futures_margin_result().related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::TrailingStop) {
        ts_order_id = order.order_id();
        orderCheck.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
        EXPECT_EQ(ts_order_id, order.order_id());
        EXPECT_EQ(order.base_price_x(), 9500'0000);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 1000, 0);
  }
}
