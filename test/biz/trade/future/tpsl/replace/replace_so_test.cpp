#include <unistd.h>

#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>

#include "gmock/gmock.h"
#include "google/protobuf/util/json_util.h"
#include "gtest/gtest.h"
#include "lib/stub.hpp"
#include "proto/gen/svc/trading/trading_service.pb.h"
#include "src/data/enum.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class TestReplaceSoTpSlParameter : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestCase() {}
  static void TearDownTestCase() {}

 protected:
  void Deposit(stub& user1) {
    // 充值
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, user1.m_uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                       std::to_string(10'000'000'000), bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    std::cout << "deposit result :" << deposit_result.m_msg->DebugString() << std::endl;
  }

  void UpdateMarketData(int64_t price) {
    // auto const underlying_price = bbase::decimal::Decimal<>("10150");
    te->AddMarkPrice(static_cast<ESymbol>(symbol), price, price, price);
  }
  void NewLine() { std::cout << std::endl << "-----------------------------------------" << std::endl; }

 protected:
  std::shared_ptr<tmock::CTradeAppMock> te;

  biz::coin_t coin = ECoin::USDT;
  biz::symbol_t const symbol = ESymbol::BTCUSDT;
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
};

TEST_F(TestReplaceSoTpSlParameter, TestReplaceSoTpSlParameter) {
  biz::user_id_t other_user_id = 11111;
  stub other_user(te, other_user_id);
  Deposit(other_user);

  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  Deposit(user);

  UpdateMarketData(10000);

  // user: +100@9999.5
  // other_user: -100@10000.5
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderWithTriggerBuilder create_build0(
        user_id, coin, symbol, order_id, pz_index, ESide::Buy, EOrderType::Limit, 100 * 1e4, 9999.5 * 1e4,
        ETimeInForce::GoodTillCancel, EStopOrderType::UNKNOWN, ETriggerBy::UNKNOWN, {}, {}, {});
    auto resp = user.process(create_build0.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderWithTriggerBuilder create_build1(
        other_user_id, coin, symbol, order_id, pz_index, ESide::Sell, EOrderType::Limit, 100 * 1e4, 10000.5 * 1e4,
        ETimeInForce::GoodTillCancel, EStopOrderType::UNKNOWN, ETriggerBy::UNKNOWN, {}, {}, {});
    resp = other_user.process(create_build1.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  }

  // 修改携带止盈止损的条件单市价单的价格
  // 修改携带止盈止损的条件单市价单止盈止损参数
  {
    // 条件市价单
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderWithTriggerBuilder create_build(
        user_id, coin, symbol, order_id, pz_index, ESide::Buy, EOrderType::Market, 10 * 1e4, {},
        ETimeInForce::ImmediateOrCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice, 8000 * 1e4, {}, 9000 * 1e4, {},
        12000 * 1e4, 7000 * 1e4);
    auto resp = user.process(create_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    auto related_order = result.m_msg->futures_margin_result().related_orders().at(0);
    EXPECT_EQ(related_order.order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(related_order.cross_status(), ECrossStatus::Init);
    EXPECT_EQ(related_order.take_profit_x(), 12000 * 1e4);
    EXPECT_EQ(related_order.stop_loss_x(), 7000 * 1e4);
    EXPECT_EQ(related_order.order_id(), order_id);

    // 改单sl
    FutureOneOfReplaceOrderBuilder replace_build;
    replace_build.SetValue(order_id, coin, symbol, user_id, {}, {}, {}, 7999 * 1e4, EStopOrderType::Stop, {}, {}, {},
                           ETriggerBy::LastPrice);
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    related_order = result.m_msg->futures_margin_result().related_orders().at(0);
    EXPECT_EQ(related_order.stop_loss_x(), 7999 * 1e4);

    // 改单tp
    replace_build.SetValue(order_id, coin, symbol, user_id, {}, {}, 9949 * 1e4, {}, EStopOrderType::Stop, {}, {},
                           ETriggerBy::LastPrice, {});
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    related_order = result.m_msg->futures_margin_result().related_orders().at(0);
    EXPECT_EQ(related_order.take_profit_x(), 9949 * 1e4);

    // 改单tp < base = 8000, fail
    replace_build.SetValue(order_id, coin, symbol, user_id, {}, {}, 7999 * 1e4, {}, EStopOrderType::Stop, {}, {},
                           ETriggerBy::LastPrice, {});
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeTpShouldGtBase);
  }

  // 修改携带止盈止损的条件限价单 做多limitPrice大于triggerPrice
  // 1修改limitPrice大于triggerPrice
  // 2修改止盈止损价格
  {
    // 下单
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderWithTriggerBuilder create_build(
        user_id, coin, symbol, order_id, pz_index, ESide::Buy, EOrderType::Limit, 10 * 1e4, 10002 * 1e4,
        ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice, 8000 * 1e4, {}, 9000 * 1e4, {},
        12000 * 1e4, 7999 * 1e4);
    auto resp = user.process(create_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    auto related_order = result.m_msg->futures_margin_result().related_orders().at(0);
    EXPECT_EQ(related_order.order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(related_order.cross_status(), ECrossStatus::Init);
    EXPECT_EQ(related_order.take_profit_x(), 12000 * 1e4);
    EXPECT_EQ(related_order.stop_loss_x(), 7999 * 1e4);
    EXPECT_EQ(related_order.order_id(), order_id);

    // 修改价格 sl > price fail
    FutureOneOfReplaceOrderBuilder replace_build;
    replace_build.SetValue(order_id, coin, symbol, user_id, 7000 * 1e4, {}, {}, {}, EStopOrderType::Stop, {}, {}, {},
                           {});
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSlShouldLtBase);

    // 修改止盈
    replace_build.SetValue(order_id, coin, symbol, user_id, {}, {}, 8001 * 1e4, {}, EStopOrderType::Stop, {}, {}, {},
                           {});
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).take_profit_x(), 8001 * 1e4);

    // 修改止损
    replace_build.SetValue(order_id, coin, symbol, user_id, {}, {}, {}, 7000 * 1e4, EStopOrderType::Stop, {}, {}, {},
                           {});
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).stop_loss_x(), 7000 * 1e4);

    // 修改止盈triggerBy
    replace_build.SetValue(order_id, coin, symbol, user_id, {}, {}, 10001 * 1e4, 7001 * 1e4, EStopOrderType::Stop, {},
                           {}, ETriggerBy::IndexPrice, ETriggerBy::IndexPrice);
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    related_order = result.m_msg->futures_margin_result().related_orders().at(0);
    EXPECT_EQ(related_order.take_profit_x(), 10001 * 1e4);
    EXPECT_EQ(related_order.stop_loss_x(), 7001 * 1e4);
    EXPECT_EQ(related_order.tp_trigger_by(), ETriggerBy::IndexPrice);
    EXPECT_EQ(related_order.sl_trigger_by(), ETriggerBy::IndexPrice);
  }

  // 修改携带止盈止损的条件限价单 做多limitPrice小于triggerPrice
  // 1修改limitPrice小于triggerPrice
  // 2修改止盈止损价格
  {
    // 下单
    auto order_id = te->GenUUID();
    // TODO(weiwei): 这里需要对构造函数增加止盈止损类型传入
    FutureOneOfCreateOrderWithTriggerBuilder create_build(
        user_id, coin, symbol, order_id, pz_index, ESide::Buy, EOrderType::Limit, 10 * 1e4, 7500 * 1e4,
        ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice, 8000 * 1e4, {}, 9000 * 1e4, {},
        7501 * 1e4, 7499 * 1e4);
    auto resp = user.process(create_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    auto related_order = result.m_msg->futures_margin_result().related_orders().at(0);
    EXPECT_EQ(related_order.order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(related_order.cross_status(), ECrossStatus::Init);
    EXPECT_EQ(related_order.take_profit_x(), 7501 * 1e4);
    EXPECT_EQ(related_order.stop_loss_x(), 7499 * 1e4);
    EXPECT_EQ(related_order.order_id(), order_id);

    // 修改价格 tp < price fail
    FutureOneOfReplaceOrderBuilder replace_build;
    replace_build.SetValue(order_id, coin, symbol, user_id, 7502 * 1e4, {}, {}, {}, EStopOrderType::Stop, {}, {}, {},
                           {});
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeTpShouldGtBase);

    // 修改tp
    replace_build.SetValue(order_id, coin, symbol, user_id, {}, {}, 8001 * 1e4, {}, EStopOrderType::Stop, {}, {}, {},
                           {});
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).take_profit_x(), 8001 * 1e4);

    // 修改sl
    replace_build.SetValue(order_id, coin, symbol, user_id, {}, {}, {}, 7000 * 1e4, EStopOrderType::Stop, {}, {}, {},
                           ETriggerBy::LastPrice);
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).stop_loss_x(), 7000 * 1e4);
  }

  // 修改携带止盈止损的条件限价单 做空limitPrice小于triggerPrice
  // 1修改limitPrice大于triggerPrice
  // 2修改止盈止损价格
  {
    // 下单
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderWithTriggerBuilder create_build;
    create_build.SetValueWithTpSl(user_id, coin, symbol, order_id, pz_index, ESide::Sell, EOrderType::Limit, 10 * 1e4,
                                  7500 * 1e4, ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice,
                                  8000 * 1e4, {}, 9000 * 1e4, {}, ETriggerBy::LastPrice, 7999 * 1e4,
                                  ETriggerBy::LastPrice, 8001 * 1e4);
    auto resp = user.process(create_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    auto related_order = result.m_msg->futures_margin_result().related_orders().at(0);
    EXPECT_EQ(related_order.order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(related_order.cross_status(), ECrossStatus::Init);
    EXPECT_EQ(related_order.take_profit_x(), 7999 * 1e4);
    EXPECT_EQ(related_order.stop_loss_x(), 8001 * 1e4);
    EXPECT_EQ(related_order.order_id(), order_id);

    // 修改价格 sl < price fail
    FutureOneOfReplaceOrderBuilder replace_build;
    replace_build.SetValue(order_id, coin, symbol, user_id, 8002 * 1e4, {}, {}, {}, EStopOrderType::Stop, {}, {}, {},
                           {});
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSlShouldGtBase);

    // 修改tp
    replace_build.SetValue(order_id, coin, symbol, user_id, {}, {}, 7500 * 1e4, {}, EStopOrderType::Stop, {}, {}, {},
                           {});
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).take_profit_x(), 7500 * 1e4);

    // 修改sl
    replace_build.SetValue(order_id, coin, symbol, user_id, {}, {}, {}, 9000 * 1e4, EStopOrderType::Stop, {}, {}, {},
                           ETriggerBy::LastPrice);
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).stop_loss_x(), 9000 * 1e4);

    // 修改tp_triggerby
    replace_build.SetValue(order_id, coin, symbol, user_id, {}, {}, {}, {}, EStopOrderType::Stop, {}, {},
                           ETriggerBy::IndexPrice, {});
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).tp_trigger_by(), ETriggerBy::IndexPrice);

    // 修改sl_triggerby
    replace_build.SetValue(order_id, coin, symbol, user_id, {}, {}, {}, {}, EStopOrderType::Stop, {}, {}, {},
                           ETriggerBy::MarkPrice);
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).sl_trigger_by(), ETriggerBy::MarkPrice);
  }

  // 修改携带止盈止损的条件限价单 做空limitPrice大于triggerPrice
  // 1修改limitPrice小于triggerPrice
  // 2修改止盈止损价格
  {
    // 下单
    auto order_id = te->GenUUID();
    // TODO(weiwei): 这里需要对构造函数增加止盈止损类型传入
    FutureOneOfCreateOrderWithTriggerBuilder create_build(
        user_id, coin, symbol, order_id, pz_index, ESide::Sell, EOrderType::Limit, 10 * 1e4, 9500 * 1e4,
        ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice, 8000 * 1e4, {}, 9000 * 1e4, {},
        9499 * 1e4, 9501 * 1e4);
    auto resp = user.process(create_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    auto related_order = result.m_msg->futures_margin_result().related_orders().at(0);
    EXPECT_EQ(related_order.order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(related_order.cross_status(), ECrossStatus::Init);
    EXPECT_EQ(related_order.take_profit_x(), 9499 * 1e4);
    EXPECT_EQ(related_order.stop_loss_x(), 9501 * 1e4);
    EXPECT_EQ(related_order.order_id(), order_id);

    // 修改价格sl < price fail
    FutureOneOfReplaceOrderBuilder replace_build;
    replace_build.SetValue(order_id, coin, symbol, user_id, 9502 * 1e4, {}, {}, {}, EStopOrderType::Stop, {}, {}, {},
                           {});
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSlShouldGtBase);

    // 修改tp
    replace_build.SetValue(order_id, coin, symbol, user_id, {}, {}, 7500 * 1e4, {}, EStopOrderType::Stop, {}, {}, {},
                           {});
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).take_profit_x(), 7500 * 1e4);
  }

  // 取消全部条件单，切换到部分止盈止损模式，下条件单携带止盈止损，测试关于20条的校验
  // 1. 取消全部条件单
  // 2. 切换到部分止盈止损模式
  // 3. 下9笔止盈止损条件单(累计18个止盈止损设置)
  // 4. 下一个条件单携带止盈(累计19个止盈止损设置)
  // 5. 下一个活动单携带止盈止损, 被拒绝(19+2>20, 累计19个止盈止损设置)
  // 6. 取消第10笔条件单的止盈设置(止盈止损设置回到18)
  // 7. 再下一个活动单携带止盈(止盈止损设置达到19条)
  // 8. 修改条件单增加止盈(累计止盈止损设置达到20)
  // 9. 修改条件单取消止盈(累计止盈止损设置回退到19), 增加止损(20条止盈止损设置)
  // 10. 修改条件单取消止损(累计止盈止损设置回退到19), 增加止盈(20条止盈止损设置)
  {
    // 1. 取消全部条件单
    FutureCancelAllBuilder cancel_all_build;
    cancel_all_build.SetValue(symbol, coin, user_id, ECancelType::UNKNOWN, ECancelAllType::NormalStopOrder, "tpsl_ut");
    auto resp = user.process(cancel_all_build.Build());
    auto result = te->PopResult();

    // 2. 切换到部分止盈止损模式
    FutureOneOfSwitchTpSlModeBuilder switch_tpsl_mode_build;
    switch_tpsl_mode_build.SetValue(user_id, coin, symbol, ETpSlMode::Partial);
    resp = user.process(switch_tpsl_mode_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(position.m_msg->tp_sl_mode(), ETpSlMode::Partial);

    // 3. 下9笔止盈止损条件单(18个止盈止损设置)
    std::string order_id = {};
    FutureOneOfCreateOrderWithTriggerBuilder create_build;
    for (int i = 0; i < 9; i++) {
      order_id = te->GenUUID();
      create_build.SetValueWithTpSl(user_id, coin, symbol, order_id, pz_index, ESide::Sell, EOrderType::Limit, 10 * 1e4,
                                    9500 * 1e4, ETimeInForce::GoodTillCancel, EStopOrderType::Stop,
                                    ETriggerBy::MarkPrice, 8000 * 1e4, {}, 9000 * 1e4, {}, ETriggerBy::LastPrice,
                                    9499 * 1e4, ETriggerBy::LastPrice, 9501 * 1e4);
      resp = user.process(create_build.Build());
      ASSERT_NE(resp, nullptr);
      EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
      result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
      EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::Untriggered);
      EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cross_status(), ECrossStatus::Init);
      EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).take_profit_x(), 9499 * 1e4);
      EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).stop_loss_x(), 9501 * 1e4);
    }

    // 4. 此时已有9个条件单(共18个止盈止损设置), 再下一个条件单携带止盈(共有19个止盈止损设置)
    order_id = te->GenUUID();
    create_build.SetValueWithTpSl(user_id, coin, symbol, order_id, pz_index, ESide::Sell, EOrderType::Limit, 10 * 1e4,
                                  9500 * 1e4, ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice,
                                  8000 * 1e4, {}, 9000 * 1e4, {}, ETriggerBy::LastPrice, 9499 * 1e4, {}, {});
    resp = user.process(create_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cross_status(), ECrossStatus::Init);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).take_profit_x(), 9499 * 1e4);
    auto stop_order_id = result.m_msg->futures_margin_result().related_orders().at(0).order_id();

    // 5. 下一个活动单携带止盈止损，被拒绝(19 + 2 > 20, 累计19个止盈止损设置)
    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_tpsl_ao;
    create_tpsl_ao.SetValueWithTpSl(
        order_id, symbol, pz_index, ESide::Buy, EOrderType::Limit, 100 * 1e4, 10000 * 1e4, ETimeInForce::GoodTillCancel,
        user_id, coin, ETriggerBy::LastPrice, 12000 * 1e4, ETriggerBy::IndexPrice, 9950 * 1e4, ETriggerBy::UNKNOWN, -1,
        -1, EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0, true);
    resp = user.process(create_tpsl_ao.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeTpSLStopOrderNoMoreThan20);

    // 6. 取消第10笔条件单的止盈设置(止盈止损设置回到18)
    FutureOneOfReplaceOrderBuilder replace_build;
    replace_build.SetValue(stop_order_id, coin, symbol, user_id, {}, {}, 0, {}, EStopOrderType::Stop, {}, {}, {}, {});
    resp = user.process(replace_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).take_profit_x(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).tp_trigger_by(), ETriggerBy::UNKNOWN);

    // 7. 下一个活动单携带止盈(止盈止损设置达到19条)
    order_id = te->GenUUID();
    create_tpsl_ao.SetValueWithTpSl(order_id, symbol, pz_index, ESide::Buy, EOrderType::Limit, 100 * 1e4, 9800 * 1e4,
                                    ETimeInForce::GoodTillCancel, user_id, coin, ETriggerBy::LastPrice, 12000 * 1e4,
                                    ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                    ETpSlMode::Partial, EOrderType::Market, EOrderType::UNKNOWN, 0, 0, 0, true);
    resp = user.process(create_tpsl_ao.Build());
    EXPECT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).take_profit_x(), 12000 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).tp_trigger_by(), ETriggerBy::LastPrice);

    // 8. 修改条件单增加止盈, 达到20条止盈止损设置
    replace_build.SetValue(stop_order_id, coin, symbol, user_id, {}, {}, 9499 * 1e4, {}, EStopOrderType::Stop, {}, {},
                           ETriggerBy::MarkPrice, {});
    resp = user.process(replace_build.Build());
    EXPECT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).take_profit_x(), 9499 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).tp_trigger_by(), ETriggerBy::MarkPrice);

    // 9. 修改条件单取消止盈(19条止盈止损设置), 增加止损(20条止盈止损设置)
    replace_build.SetValue(stop_order_id, coin, symbol, user_id, {}, {}, 0, 9800 * 1e4, EStopOrderType::Stop, {}, {},
                           {}, ETriggerBy::MarkPrice);
    resp = user.process(replace_build.Build());
    EXPECT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).take_profit_x(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).tp_trigger_by(), ETriggerBy::UNKNOWN);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).stop_loss_x(), 9800 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).sl_trigger_by(), ETriggerBy::MarkPrice);

    // 10. 修改条件单取消止损(止盈止损设置回退到19), 增加止盈(20条止盈止损设置)
    replace_build.SetValue(stop_order_id, coin, symbol, user_id, {}, {}, 9480 * 1e4, 0, EStopOrderType::Stop, {}, {},
                           ETriggerBy::IndexPrice, {});
    resp = user.process(replace_build.Build());
    EXPECT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).take_profit_x(), 9480 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).tp_trigger_by(), ETriggerBy::IndexPrice);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).stop_loss_x(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).sl_trigger_by(), ETriggerBy::UNKNOWN);
  }
}
