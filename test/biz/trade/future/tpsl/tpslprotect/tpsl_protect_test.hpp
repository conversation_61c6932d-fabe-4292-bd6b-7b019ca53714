#ifndef TEST_BIZ_TRADE_FUTURE_TPSL_TPSLPROTECT_TPSL_PROTECT_TEST_HPP_
#define TEST_BIZ_TRADE_FUTURE_TPSL_TPSLPROTECT_TPSL_PROTECT_TEST_HPP_
#include <memory>

#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "lib/stub.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class TpSlProtectTest : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestCase() {}
  static void TearDownTestCase() {}

 protected:
  void Deposit(stub& user1) {
    // 充值
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, user1.m_uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                       std::to_string(10'000'000'000), bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    std::cout << "deposit result :" << deposit_result.m_msg->DebugString() << std::endl;
  }

  void UpdateMarketData(int64_t price) {
    // auto const underlying_price = bbase::decimal::Decimal<>("10150");
    te->AddMarkPrice(static_cast<ESymbol>(symbol), price, price, price);
  }
  void NewLine() { std::cout << std::endl << "-----------------------------------------" << std::endl; }
  static std::shared_ptr<event::OnUpdateConfigEvent> MockConfig(bool gray, int risk_limit_version);

 protected:
  std::shared_ptr<tmock::CTradeAppMock> te;

 protected:
  biz::coin_t coin = ECoin::USDT;
  biz::symbol_t const symbol = 5;
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
};

#endif  // TEST_BIZ_TRADE_FUTURE_TPSL_TPSLPROTECT_TPSL_PROTECT_TEST_HPP_
