#include <unistd.h>

#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>

#include "gmock/gmock.h"
#include "google/protobuf/util/json_util.h"
#include "gtest/gtest.h"
#include "lib/stub.hpp"
#include "proto/gen/svc/trading/trading_service.pb.h"
#include "src/data/enum.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class MergeSingleFullTpSlTSTest : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestCase() {}
  static void TearDownTestCase() {}

 protected:
  void Deposit(stub& user1) {
    // 充值
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, user1.m_uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                       std::to_string(10'000'000'000), bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    // std::cout << "deposit result :" << deposit_result.m_msg->DebugString() << std::endl;
  }

  void UpdateMarketData(int64_t price) {
    // auto const underlying_price = bbase::decimal::Decimal<>("10150");
    te->AddMarkPrice(static_cast<ESymbol>(symbol), price, price, price);
  }
  void NewLine() { std::cout << std::endl << "-----------------------------------------" << std::endl; }

 protected:
  std::shared_ptr<tmock::CTradeAppMock> te;

  biz::coin_t coin = ECoin::USDT;
  biz::symbol_t const symbol = ESymbol::BTCUSDT;
  biz::cross_idx_t const cross_idx = ECrossIdx::BTCLP;
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
};

// 全量止盈止损模式 当前单向持仓，仓位有止盈止损，下一笔会反向持仓的订单成交携带止盈止损成交
TEST_F(MergeSingleFullTpSlTSTest, TestMergedSingle) {
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  Deposit(user);

  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  Deposit(other_user);

  // mark price 校准
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  // last price 校准
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  {
    FutureOneOfSwitchTpSlModeBuilder switch_tpsl_b;
    switch_tpsl_b.SetValue(user_id, coin, symbol, ETpSlMode::Full);
    auto resp = user.process(switch_tpsl_b.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSameTpSlMode);

    auto gen_order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_tpsl_b;
    /* clang-format off */
    buy_tpsl_b.SetValueWithTpSl(gen_order_id, symbol, pz_index,
                                 ESide::Buy, EOrderType::Limit,
                                 100 * 1e4,
                                 10000 * 1e4,
                                 ETimeInForce::GoodTillCancel,
                                 user_id,
                                 coin,
                                 {}, 10002 * 1e4,
                                 {}, 9991 * 1e4,
                                 ETriggerBy::UNKNOWN, -1,
                                 -1,
                                 EStopOrderType::UNKNOWN, ETpSlMode::Full,
                                 {}, {}, 0, 0, 0, true);
    /* clang-format on */
    resp = user.process(buy_tpsl_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);

    gen_order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_limit_b(gen_order_id, symbol, pz_index, ESide::Sell, EOrderType::Limit,
                                               100 * 1e4, 10000 * 1e4, ETimeInForce::GoodTillCancel, other_user_id,
                                               coin);
    resp = other_user.process(sell_limit_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();  // taker
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 100 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 100 * 10000 * 1e4);
    result = te->PopResult();  // maker
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 3);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 100 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 100 * 10000 * 1e4);

    // 下单, 反向持仓
    gen_order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_tpsl_b;
    /* clang-format off */
    sell_tpsl_b.SetValueWithTpSl(gen_order_id, symbol, pz_index,
                                 ESide::Sell, EOrderType::Limit,
                                 300 * 1e4,
                                 10000 * 1e4,
                                 ETimeInForce::GoodTillCancel,
                                 user_id,
                                 coin,
                                 {}, 9902 * 1e4,
                                 {}, 10002 * 1e4,
                                 ETriggerBy::UNKNOWN, -1,
                                 -1,
                                 EStopOrderType::UNKNOWN, ETpSlMode::Full,
                                 {}, {}, 0, 0, 0, true);
    /* clang-format on */
    resp = user.process(sell_tpsl_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);

    gen_order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_limit_b(gen_order_id, symbol, pz_index, ESide::Buy, EOrderType::Limit, 300 * 1e4,
                                              10000 * 1e4, ETimeInForce::GoodTillCancel, other_user_id, coin);
    resp = other_user.process(buy_limit_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();  // taker
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 200 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 200 * 10000 * 1e4);
    result = te->PopResult();  // maker
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 5);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 200 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 200 * 10000 * 1e4);

    // TODO(martin): 订单先后顺序是否需要对齐?
    auto& related_orders = result.m_msg->futures_margin_result().related_orders();
    // EXPECT_EQ(related_orders.at(0).order_status(), EOrderStatus::Filled);
    // EXPECT_EQ(related_orders.at(1).order_status(), EOrderStatus::Deactivated);
    // EXPECT_EQ(related_orders.at(2).order_status(), EOrderStatus::Deactivated);
    // EXPECT_EQ(related_orders.at(3).order_status(), EOrderStatus::Untriggered);
    // EXPECT_EQ(related_orders.at(4).order_status(), EOrderStatus::Untriggered);
    // EXPECT_EQ(related_orders.at(4).qty_x(), 200 * 1e4);
    for (auto o : related_orders) {
      if (o.order_status() == EOrderStatus::Untriggered) {
        EXPECT_EQ(o.qty_x(), 200 * 1e4);
      }
    }
  }
}

TEST_F(MergeSingleFullTpSlTSTest, TestMergedSingleV2) {
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  Deposit(user);

  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  Deposit(other_user);

  // mark price 校准
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  // last price 校准
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  {
    FutureOneOfSwitchTpSlModeBuilder switch_tpsl_b;
    switch_tpsl_b.SetValue(user_id, coin, symbol, ETpSlMode::Full);
    auto resp = user.process(switch_tpsl_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSameTpSlMode);

    auto gen_order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_limit_b(gen_order_id, symbol, pz_index, ESide::Buy, EOrderType::Limit, 100 * 1e4,
                                              10000 * 1e4, ETimeInForce::GoodTillCancel, user_id, coin);
    resp = user.process(buy_limit_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);

    gen_order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_limit_b(gen_order_id, symbol, pz_index, ESide::Sell, EOrderType::Limit,
                                               100 * 1e4, 10000 * 1e4, ETimeInForce::GoodTillCancel, other_user_id,
                                               coin);
    resp = other_user.process(sell_limit_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();  // taker, other_user on Sell
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 100 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 100 * 10000 * 1e4);
    result = te->PopResult();  // maker, test_user on Buy
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 100 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 100 * 10000 * 1e4);

    // 下单 反向持仓
    gen_order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_tpsl_b;
    /* clang-format off */
    sell_tpsl_b.SetValueWithTpSl(gen_order_id, symbol, pz_index,
                                 ESide::Sell, EOrderType::Limit,
                                 300 * 1e4,
                                 10000 * 1e4,
                                 ETimeInForce::GoodTillCancel,
                                 user_id,
                                 coin,
                                 {}, 9902 * 1e4,
                                 {}, 10002 * 1e4,
                                 ETriggerBy::UNKNOWN, -1,
                                 -1,
                                 EStopOrderType::UNKNOWN, ETpSlMode::Full,
                                 {}, {}, 0, 0, 0, true);
    /* clang-format on */
    resp = user.process(sell_tpsl_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);

    gen_order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_limit_b2(gen_order_id, symbol, pz_index, ESide::Buy, EOrderType::Limit, 300 * 1e4,
                                               10000 * 1e4, ETimeInForce::GoodTillCancel, other_user_id, coin);
    resp = other_user.process(buy_limit_b2.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();  // taker
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 200 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 200 * 10000 * 1e4);
    result = te->PopResult();  // maker
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 3);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 200 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 200 * 10000 * 1e4);

    // TODO(martin): 订单先后顺序是否需要对齐?
    auto& related_orders = result.m_msg->futures_margin_result().related_orders();
    // EXPECT_EQ(related_orders.at(0).order_status(), EOrderStatus::Filled);
    // EXPECT_EQ(related_orders.at(1).order_status(), EOrderStatus::Untriggered);
    // EXPECT_EQ(related_orders.at(2).order_status(), EOrderStatus::Untriggered);
    // EXPECT_EQ(related_orders.at(2).qty_x(), 200 * 1e4);
    for (auto o : related_orders) {
      if (o.order_status() == EOrderStatus::Untriggered) {
        EXPECT_EQ(o.qty_x(), 200 * 1e4);
      }
    }
  }
}
