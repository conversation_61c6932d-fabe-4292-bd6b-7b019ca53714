#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>

#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "lib/stub.hpp"
#include "src/data/enum.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class SpecialTest : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestCase() {}
  static void TearDownTestCase() { /*app::GlobalVarManager::Instance().coin_ = ECoin::UNKNOWN;*/ }

 protected:
  void Deposit(stub& user1) {
    // 充值
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, user1.m_uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                       std::to_string(10'000'000'000), bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    // std::cout << "deposit result :" << deposit_result.m_msg->DebugString() << std::endl;
  }

  void UpdateMarketData(int64_t price) { te->AddMarkPrice(static_cast<ESymbol>(symbol), price, price, price); }
  void NewLine() { std::cout << std::endl << "-----------------------------------------" << std::endl; }

 protected:
  std::shared_ptr<tmock::CTradeAppMock> te;
  biz::coin_t coin = ECoin::USDT;
  biz::symbol_t const symbol = 5;
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
};

TEST_F(SpecialTest, TestSpecialSingle) {
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  Deposit(user);

  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  Deposit(other_user);

  // mark price 校准
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  // last price 校准
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, 5 /*cross_idx*/, lv0_px, lv0_qty, sync_trigger);

  // 全量止盈止损模式 当前单向持仓，持仓110w手，有止盈止损，止盈出场吃到自己的开仓单（携带止盈止损）
  // 最后，止盈不变，止损被替换
  {
    FutureOneOfSwitchTpSlModeBuilder switch_tpsl_b;
    switch_tpsl_b.SetValue(user_id, coin, symbol, ETpSlMode::Full);
    auto resp = user.process(switch_tpsl_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSameTpSlMode);

    auto gen_order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_tpsl_b;
    /* clang-format off */
    buy_tpsl_b.SetValueWithTpSl(gen_order_id, symbol, pz_index,
                                 ESide::Buy, EOrderType::Limit,
                                 100 * 1e4,
                                 10000 * 1e4,
                                 ETimeInForce::GoodTillCancel,
                                 user_id,
                                 coin,
                                 ETriggerBy::LastPrice, 10002 * 1e4,
                                 ETriggerBy::LastPrice, 9991 * 1e4,
                                 ETriggerBy::UNKNOWN, -1,
                                 -1,
                                 EStopOrderType::UNKNOWN, ETpSlMode::Full,
                                 {}, {}, 0, 0, 0, true);
    /* clang-format on */
    resp = user.process(buy_tpsl_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);

    gen_order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_limit_b(gen_order_id, symbol, pz_index, ESide::Sell, EOrderType::Limit,
                                               100 * 1e4, 10000 * 1e4, ETimeInForce::GoodTillCancel, other_user_id,
                                               coin);
    resp = other_user.process(sell_limit_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();  // taker
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 100 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 100 * 10000 * 1e4);
    result = te->PopResult();  // maker
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 3);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 100 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 100 * 10000 * 1e4);

    // 再次开仓20w
    gen_order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_tpsl_b2;
    /* clang-format off */
    buy_tpsl_b2.SetValueWithTpSl(gen_order_id, symbol, pz_index,
                                 ESide::Buy, EOrderType::Limit,
                                 20 * 1e4,
                                 10000 * 1e4,
                                 ETimeInForce::GoodTillCancel,
                                 user_id,
                                 coin,
                                 ETriggerBy::LastPrice, 10002 * 1e4,
                                 ETriggerBy::LastPrice, 9991 * 1e4,
                                 ETriggerBy::UNKNOWN, -1,
                                 -1,
                                 EStopOrderType::UNKNOWN, ETpSlMode::Full,
                                 {}, {}, 0, 0, 0, true);
    /* clang-format on */
    resp = user.process(buy_tpsl_b2.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);

    gen_order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_limit_b2(gen_order_id, symbol, pz_index, ESide::Sell, EOrderType::Limit,
                                                20 * 1e4, 10000 * 1e4, ETimeInForce::GoodTillCancel, other_user_id,
                                                coin);
    resp = other_user.process(sell_limit_b2.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();  // taker
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 120 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 120 * 10000 * 1e4);
    result = te->PopResult();  // maker
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 3);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 120 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 120 * 10000 * 1e4);
  }
  // 新建止盈止损
  {
    FutureOneOfSetTpSlTsBuilder set_tpsl_b;
    /* clang-format off */
    set_tpsl_b.SetValue(user_id, coin, symbol, pz_index,
                        ETriggerBy::LastPrice, 10005 * 1e4,
                        ETriggerBy::LastPrice, 9600 * 1e4,
                        ETriggerBy::UNKNOWN, -1,
                        -1, -1, -1, "", "", 0,
                        ETpSlMode::Full,
                        EOrderType::UNKNOWN, EOrderType::UNKNOWN,
                        {}, {}, true);
    /* clang-format on */
    auto resp = user.process(set_tpsl_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 2);
  }
  // 触发止盈 吃到自己的开仓单携带止盈止损
  {
    auto gen_order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_tpsl_b;
    /* clang-format off */
    buy_tpsl_b.SetValueWithTpSl(gen_order_id, symbol, pz_index,
                                 ESide::Buy, EOrderType::Limit,
                                 20 * 1e4,
                                 10000 * 1e4,
                                 ETimeInForce::GoodTillCancel,
                                 user_id,
                                 coin,
                                 ETriggerBy::LastPrice, 10002 * 1e4,
                                 ETriggerBy::LastPrice, 9991 * 1e4,
                                 ETriggerBy::UNKNOWN, -1,
                                 -1,
                                 EStopOrderType::UNKNOWN, ETpSlMode::Full,
                                 {}, {}, 0, 0, 0, true);
    /* clang-format on */
    auto resp = user.process(buy_tpsl_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::New);

    underlying_price = bbase::decimal::Decimal<>("10005");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(last_price)10999 <= (trigger_price)11000
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截
      auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
      auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
      trigger_info->symbol_ = symbol;
      trigger_info->trigger_by_ = ETriggerBy::LastPrice;
      trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
      trigger_info->trigger_src_seq_ = 1;
      trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
      quote_update_event->SetRawTriggerInfo(trigger_info);
      quote_update_event->set_type(event::kEventSyncFuturesTrigger);
      std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
      EXPECT_EQ(triggered_so.qty_x(), 120 * 1e4);

      auto ao_result = te->PopResult();
      if (ao_result.m_msg == nullptr) {
        ++try_times;
        continue;
      }
      std::string msg{};
      std::cout << "active ao result:" << utils::PbMessageToJsonString(*ao_result.m_msg, &msg) << std::endl;
      ASSERT_EQ(ao_result.m_msg->futures_margin_result().affected_positions().size(), 1);
      EXPECT_EQ(ao_result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
      EXPECT_EQ(ao_result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 120 * 1e4);
      EXPECT_EQ(ao_result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 120 * 10000 * 1e4);
      ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 4);
      auto related_orders = ao_result.m_msg->futures_margin_result().related_orders();

      for (auto o : related_orders) {
        if (o.order_status() == EOrderStatus::Cancelled) {
          EXPECT_EQ(o.cum_qty_x(), 20 * 1e4);
          EXPECT_EQ(o.stop_order_type(), EStopOrderType::TakeProfit);
        } else if (o.order_status() == EOrderStatus::Filled) {
          EXPECT_EQ(o.cum_qty_x(), 20 * 1e4);
          EXPECT_EQ(o.stop_order_type(), EStopOrderType::UNKNOWN);
        } else if (o.order_status() == EOrderStatus::Triggered) {
          EXPECT_EQ(o.qty_x(), 120 * 1e4);
          EXPECT_EQ(o.stop_order_type(), EStopOrderType::TakeProfit);
        } else if (o.order_status() == EOrderStatus::Deactivated) {
          EXPECT_EQ(o.qty_x(), 120 * 1e4);
        }
      }

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

// 自成交
TEST_F(SpecialTest, TestSpecial_v2) {
  // other user
  biz::user_id_t user_id_other = 653106;
  stub user_other(te, user_id_other);
  Deposit(user_other);

  // test user
  biz::user_id_t user_id_test = 111111;
  stub user_test(te, user_id_test);
  Deposit(user_test);

  // 标记价格更新
  UpdateMarketData(10000);

  // 切换为 全部止盈止损
  {
    FutureOneOfSwitchTpSlModeBuilder create_build;
    create_build.SetValue(user_id_test, coin, symbol, ETpSlMode::Full);
    auto resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSameTpSlMode);
  }
  // 开仓 +100@10000
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;
    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id_other, coin);

    auto resp_other = user_other.process(create_build.Build());
    EXPECT_EQ(resp_other->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_other = te->PopResult();
    ASSERT_NE(result_other.m_msg.get(), nullptr);
    auto orderCheck = result_other.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }
  // test user 挂单 -100@10000 && tp@10010 sl@-1
  // 和 other user 订单成交  pz: 0+100@1000
  std::string tp_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;
    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;
    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin,
        ETriggerBy::LastPrice, 10002'0000, ETriggerBy::LastPrice, 9991'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test = te->PopResult();
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10002'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    tp_order_id = orderCheck.m_msg->order_id();
    result_test.RefFutureMarginResult().RefRelatedPosition(0).CheckSize(100'0000);

    te->PopResult();
  }
  // other user 挂单 -20@10000
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id_other, coin);

    auto resp_other = user_other.process(create_build.Build());
    EXPECT_EQ(resp_other->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_other = te->PopResult();
    ASSERT_NE(result_other.m_msg.get(), nullptr);
    auto orderCheck = result_other.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }
  // test user 挂单 -20@1000
  // 和 other user 订单成交  pz: 0+100@1000
  std::string tp_order_id_x;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::UNKNOWN, 10010'0000,
                                  ETriggerBy::UNKNOWN, -1, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test = te->PopResult();
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    tp_order_id_x = orderCheck.m_msg->order_id();
    result_test.RefFutureMarginResult().RefRelatedPosition(0).CheckSize(120'0000);

    te->PopResult();
  }

  // 新建止盈止损
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id_test, coin, symbol, pz_index, ETriggerBy::LastPrice, 10005'0000,
                          ETriggerBy::LastPrice, 9600'0000, ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", "", 0,
                          ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test = te->PopResult();
    ASSERT_NE(result_test.m_msg.get(), nullptr);
  }

  // 触发止盈 吃到自己的开仓单携带止盈止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin,
        ETriggerBy::LastPrice, 10002'0000, ETriggerBy::LastPrice, 9991'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10002'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);

    // 标记价格更新
    UpdateMarketData(10006);

    // 生成一笔循环开仓的止盈单
    // auto result2 = te->PopResult();
    // auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    // EXPECT_EQ(orderCheck2.m_msg->cum_qty_x(), 100'0000);
  }
}
