#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>

#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "lib/stub.hpp"
#include "src/data/enum.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class MergeSinglePartialTpSlTSTest : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestCase() {}
  static void TearDownTestCase() {}

 protected:
  void Deposit(stub& user1) {
    // 充值
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, user1.m_uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                       std::to_string(10'000'000'000), bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    std::cout << "deposit result :" << deposit_result.m_msg->DebugString() << std::endl;
  }

  void UpdateMarketData(int64_t price) {
    // auto const underlying_price = bbase::decimal::Decimal<>("10150");
    te->AddMarkPrice(static_cast<ESymbol>(symbol), price, price, price);
  }
  void NewLine() { std::cout << std::endl << "-----------------------------------------" << std::endl; }

 protected:
  std::shared_ptr<tmock::CTradeAppMock> te;

  biz::coin_t coin = ECoin::USDT;
  biz::symbol_t const symbol = 5;
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
};

#define MergeSinglePartialTpSlTSTest_PrintResult

// internal/tests/tpslts/crossrespadjusttpslts/partial/merged_single_test.go
TEST_F(MergeSinglePartialTpSlTSTest, TestMergedSingle_1_to_3) {
  biz::user_id_t user_id1 = 653106;
  // 构建客户端
  stub user1(te, user_id1);
  // 设置逐仓10x杠杆+充钱
  Deposit(user1);

  UpdateMarketData(10000);

  biz::user_id_t user_id2 = 111111;
  stub user2(te, user_id2);
  // 设置逐仓10x杠杆+充钱
  Deposit(user2);

  // 切换到部分止盈止损模式
  {
    FutureOneOfSwitchTpSlModeBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, ETpSlMode::Partial);
    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 switch tp sl mode: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 switch tp sl mode:  " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(position.m_msg->tp_sl_mode(), static_cast<ETpSlMode>(ETpSlMode::Partial));
  }

  // 1 当前单仓，仓位为0，没有活动单 ，当前止盈止损模式为部分，下一笔开仓单携带止盈止损
  // 结果 正常生成单子上的止盈止损设置
  std::string tp_order_id;
  std::string sl_order_id;
  std::string buy_order_id;
  std::string parent_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0, true);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 11000'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9900'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id2, coin,
        ETriggerBy::IndexPrice, 9900'0000, ETriggerBy::MarkPrice, 11000'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Market, 100'0000);
        sl_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Market, 100'0000);
        tp_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(100'0000);
  }
  // 平仓
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 buy
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 100'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 100'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(0);
  }

  // 2 当前单仓，仓位为0，没有活动单，当前止盈止损模式为部分，下一笔市价开仓单携带止盈止损，单子分三次成交
  // 如果当前三次成交过程单子没有被cancel，没有被replace，则最后这个单子生成一笔止盈止损设置
  // 第一次成交生效的止盈单被取消，结果为三笔成交的止损，后两笔成交的止盈
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0, true);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 11000'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9900'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 10'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }

  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);
        parent_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        sl_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        tp_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(10'0000);
  }
  // 取消止盈订单
  {
    FutureOneOfCancelOrderBuilder create_build(tp_order_id, symbol, user_id1, coin);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    // auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(tp_order_id);
    // ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 2);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == tp_order_id) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(10'0000);
  }
  // 吃40手
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 40'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);
        parent_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 40'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        sl_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 40'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        tp_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
  }
  // 吃50手
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
        parent_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 90'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_EQ(sl_order_id, order.order_id());
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 90'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_EQ(tp_order_id, order.order_id());
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(100'0000);
  }
  // 平仓
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 buy
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 90'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 90'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(0);
  }

  // 2 当前单仓，仓位为0，没有活动单，当前止盈止损模式为部分，下一笔市价开仓单携带止盈止损，单子分三次成交
  // 第一次成交生效的止损单被取消，结果为后两笔成交的止损，三笔成交的止盈
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0, true);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 11000'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9900'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // 吃10手
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 10'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);
        parent_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        sl_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        tp_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(10'0000);
  }
  // 取消止损订单
  {
    FutureOneOfCancelOrderBuilder create_build(sl_order_id, symbol, user_id1, coin);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    // auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(tp_order_id);
    // ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 2);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == tp_order_id) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(10'0000);
  }
  // 吃40手
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 40'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);
        parent_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 40'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        sl_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 40'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        tp_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
  }
  // 吃50手
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
        parent_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 90'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_EQ(sl_order_id, order.order_id());
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 90'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_EQ(tp_order_id, order.order_id());
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(100'0000);
  }
  // 平仓
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 buy
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 90'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 90'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(0);
  }

  // 2 当前单仓，仓位为0，没有活动单，当前止盈止损模式为部分，下一笔市价开仓单携带止盈止损，单子分三次成交
  // 第一次成交生效的止盈单trigger价格被修改，结果为三笔成交的止损，后两笔成交的止盈
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0, true);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 11000'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9900'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // 吃10手
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 10'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);
        parent_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        sl_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        tp_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(10'0000);
  }
  // 修改止盈订单
  {
    FutureOneOfReplaceOrderBuilder create_build(tp_order_id, symbol, 0, 0, user_id1, coin, 11001'0000);
    create_build.msg.mutable_replace_order()->set_need_generate_pid(false);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == tp_order_id) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11001'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(10'0000);
  }
  // 吃40手
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 40'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);
        parent_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 50'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        sl_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 40'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        tp_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
  }
  // 吃50手
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
        parent_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 100'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_EQ(sl_order_id, order.order_id());
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 100'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_EQ(tp_order_id, order.order_id());
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(100'0000);
  }
  // 平仓
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 buy
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 4);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 100'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        if (order.trigger_price_x() == 11000'0000) {
          orderCheck
              .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                              EOrderType::Market, 100'0000)
              .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
        } else {
          orderCheck
              .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11001'0000, EStopOrderType::PartialTakeProfit,
                              EOrderType::Market, 10'0000)
              .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
        }

      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(0);
  }

  // 2 当前单仓，仓位为0，没有活动单，当前止盈止损模式为部分，下一笔市价开仓单携带止盈止损，单子分三次成交
  // 第一次成交生效的止损单trigger价格被修改，结果为后两笔成交的止损，三笔成交的止盈
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0, true);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 11000'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9900'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // 吃10手
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 10'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);
        parent_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        sl_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        tp_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(10'0000);
  }
  // 修改止损订单
  {
    FutureOneOfReplaceOrderBuilder create_build(sl_order_id, symbol, 0, 0, user_id1, coin, 9909'0000);
    create_build.msg.mutable_replace_order()->set_need_generate_pid(true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == sl_order_id) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9909'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(10'0000);
  }
  // 吃40手
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 40'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);
        parent_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9909'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 50'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
        sl_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 50'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        tp_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
  }
  // 吃50手
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
        parent_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9909'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 100'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
        EXPECT_EQ(sl_order_id, order.order_id());
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 100'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_EQ(tp_order_id, order.order_id());
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(100'0000);
  }
  // 平仓
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 buy
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9909'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 100'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 100'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(0);
  }

  // 3 如果当前有仓位100，分别设置了止盈 40，止盈 50 ，平仓 20
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = order_id;
  }
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
        parent_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(100'0000);
  }
  // 设置40止盈
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 10001'0000, ETriggerBy::UNKNOWN, -1,
                          ETriggerBy::UNKNOWN, -1, -1, 40'0000, -1, "", "", 0, ETpSlMode::Partial, EOrderType::Market,
                          EOrderType::UNKNOWN, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 10001'0000, EStopOrderType::PartialTakeProfit,
                          EOrderType::Market)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(100'0000);
  }
  // 设置50止盈
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 10002'0000, ETriggerBy::UNKNOWN, -1,
                          ETriggerBy::UNKNOWN, -1, -1, 50'0000, -1, "", "", 0, ETpSlMode::Partial, EOrderType::Market,
                          EOrderType::UNKNOWN, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 10002'0000, EStopOrderType::PartialTakeProfit,
                          EOrderType::Market)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(100'0000);
  }
  // 平仓
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 buy
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(80'0000);
  }
  // 设置30止损
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::LastPrice, 9901'0000,
                          ETriggerBy::UNKNOWN, -1, -1, -1, 30'0000, "", "", 0, ETpSlMode::Partial, EOrderType::UNKNOWN,
                          EOrderType::Market, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 9901'0000, EStopOrderType::PartialStopLoss,
                          EOrderType::Market, 30'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(80'0000);
  }
  // 设置40止损
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::LastPrice, 9902'0000,
                          ETriggerBy::UNKNOWN, -1, -1, -1, 40'0000, "", "", 0, ETpSlMode::Partial, EOrderType::UNKNOWN,
                          EOrderType::Market, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 9902'0000, EStopOrderType::PartialStopLoss,
                          EOrderType::Market, 40'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(80'0000);
  }
  // 平仓20
  // 平仓
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 buy
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(60'0000);
  }
  // 平仓
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 60'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 buy
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 60'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 5);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        if (order.trigger_price_x() == 9901'0000) {
          orderCheck
              .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 9901'0000, EStopOrderType::PartialStopLoss,
                              EOrderType::Market, 30'0000)
              .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
        } else {
          orderCheck
              .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 9902'0000, EStopOrderType::PartialStopLoss,
                              EOrderType::Market, 40'0000)
              .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
        }
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        if (order.trigger_price_x() == 10002'0000) {
          orderCheck
              .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 10002'0000, EStopOrderType::PartialTakeProfit,
                              EOrderType::Market, 50'0000)
              .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
        } else {
          orderCheck
              .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 10001'0000, EStopOrderType::PartialTakeProfit,
                              EOrderType::Market, 40'0000)
              .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
        }
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(0);
  }
}

TEST_F(MergeSinglePartialTpSlTSTest, TestMergedSingle_4_to_8) {
  biz::user_id_t user_id1 = 653106;
  // 构建客户端
  stub user1(te, user_id1);
  // 设置逐仓10x杠杆+充钱
  Deposit(user1);

  UpdateMarketData(10000);

  biz::user_id_t user_id2 = 111111;
  stub user2(te, user_id2);
  // 设置逐仓10x杠杆+充钱
  Deposit(user2);

  // 切换到部分止盈止损模式
  {
    FutureOneOfSwitchTpSlModeBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, ETpSlMode::Partial);
    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 switch tp sl mode: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 switch tp sl mode:  " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(position.m_msg->tp_sl_mode(), static_cast<ETpSlMode>(ETpSlMode::Partial));
  }

  // 4 如果当前有多仓位100，分别设置了止盈 40，止盈 50 ，平仓（空 taker） 20 和自己成交20（maker 多单携带止盈止损设置）
  // 先处理maker，再处理taker
  // 先处理maker 20手开仓单生成止盈止损
  // 再处理taker  将最早的40 修改成30
  // 结果为 30 ，50 ，20
  // 开仓
  std::string buy_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = order_id;
  }
  // user_id 2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(100'0000);
  }

  // 设置40止盈
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 10001'0000, ETriggerBy::UNKNOWN, -1,
                          ETriggerBy::UNKNOWN, -1, -1, 40'0000, -1, "", "", 0, ETpSlMode::Partial, EOrderType::Market,
                          EOrderType::UNKNOWN, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 10001'0000, EStopOrderType::PartialTakeProfit,
                          EOrderType::Market, 40'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(100'0000);
  }
  // 设置50止盈
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 10002'0000, ETriggerBy::UNKNOWN, -1,
                          ETriggerBy::UNKNOWN, -1, -1, 50'0000, -1, "", "", 0, ETpSlMode::Partial, EOrderType::Market,
                          EOrderType::UNKNOWN, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 10002'0000, EStopOrderType::PartialTakeProfit,
                          EOrderType::Market, 50'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(100'0000);
  }
  // buy 20，携带止盈止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 10001'0000, ETriggerBy::LastPrice, 9901'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0, true);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 10001'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 9901'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // sell 20, 自成交
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 4);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 9901'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 20'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 10001'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 20'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else if (order.cum_qty_x() != 0) {
        if (order.side() == ESide::Buy) {
          orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
        } else {
          orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);
        }
      } else {
        EXPECT_TRUE(false);
      }
    }

    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(100'0000);
  }
  // +1tp
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 10'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::LastPrice, 12001'0000,
                                  ETriggerBy::UNKNOWN, -1, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Partial, EOrderType::Market, EOrderType::UNKNOWN, 0, 0, 0, true);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 12001'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 10'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 2);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 12001'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(110'0000);
  }
  // +1sl
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 10'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, -1, ETriggerBy::LastPrice, 9901'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0, true);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 9901'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 10'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 2);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 9901'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(120'0000);
  }
  // 平仓
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 120'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 buy
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 120'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 7);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else {
        orderCheck.CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(0);
  }

  // 5 有2手仓位 2手部分止盈 2手部分止损 此时下三手空单携带止盈止损 结果为1手空仓 1收空仓的止盈 1手空仓的止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = order_id;
  }
  // user_id 2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(20'0000);
  }
  // 设置止盈止损 2手
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000'0000, ETriggerBy::MarkPrice,
                          9900'0000, ETriggerBy::UNKNOWN, -1, -1, 20'0000, 20'0000, "", "", 0, ETpSlMode::Partial,
                          EOrderType::Market, EOrderType::Market, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 2);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                          EOrderType::Market, 20'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        cc.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                          EOrderType::Market, 20'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(20'0000);
  }
  // 反向3
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 30'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9900'0000, ETriggerBy::LastPrice, 10500'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0, true);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 9900'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 10500'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 buy
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 30'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 5);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (order.parent_order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_EQ(order.qty_x(), 10'0000);
      } else if (order.parent_order_id() != buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(10'0000);
    EXPECT_EQ(position.m_msg->side(), ESide::Sell);
  }
  // 平仓
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 10'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = order_id;
  }
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 10'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 10500'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(0);
  }

  // 6 有2手多仓仓位 2手部分止盈 2手部分止损 有三手多单携带止盈止损 此时下6手空单携带止盈止损吃掉自己的三手多单
  // 结果为1手空仓 1收空仓的止盈 1手空仓的止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = order_id;
  }
  // user_id 2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(20'0000);
  }
  // 设置止盈止损
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000'0000, ETriggerBy::MarkPrice,
                          9900'0000, ETriggerBy::UNKNOWN, -1, -1, 20'0000, 20'0000, "", "", 0, ETpSlMode::Partial,
                          EOrderType::Market, EOrderType::Market, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 2);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                          EOrderType::Market, 20'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        cc.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                          EOrderType::Market, 20'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(20'0000);
  }
  // buy，携带止盈止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 30'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 10050'0000, ETriggerBy::LastPrice, 9950'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0, true);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 10050'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 9950'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id 2 buy
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 30'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // sell 部分自成交
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 60'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9900'0000, ETriggerBy::LastPrice, 10500'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::UNKNOWN, 0, 0, 0);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(orderCheck2.m_msg->related_orders().size(), 6);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::UNKNOWN) {
        if (order.side() == ESide::Buy) {
          orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
        } else {
          orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);
        }
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        if (order.side() == ESide::Buy) {
          orderCheck.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init)
              .ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::LastPrice, EOrderType::Market, 9900'0000,
                               10'0000);
        } else {
          orderCheck.CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
        }
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        if (order.side() == ESide::Buy) {
          orderCheck.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init)
              .ChecckTpSlOrder(EStopOrderType::PartialStopLoss, ETriggerBy::LastPrice, EOrderType::Market, 10500'0000,
                               10'0000);
        } else {
          orderCheck.CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
        }
      } else {
        ASSERT_TRUE(false);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(10'0000);
    EXPECT_EQ(position.m_msg->side(), ESide::Sell);
  }
  // user_id2 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // 下单将头寸平掉, 恢复状态
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 10'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 10'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 10500'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 10'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(0);
  }

  // 7 有3手多仓仓位 2手部分止盈 2手部分止损 有两个手数为2的空单都携带止盈止损被一次吃掉 结果为1手空仓
  // 1收空仓的止盈
  // 1手空仓的止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 30'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = order_id;
  }
  // user_id 2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 30'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(30'0000);
  }

  // 2手部分止盈 2手部分止损
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000'0000, ETriggerBy::MarkPrice,
                          9900'0000, ETriggerBy::UNKNOWN, -1, -1, 20'0000, 20'0000, "", "", 0, ETpSlMode::Partial,
                          EOrderType::Market, EOrderType::UNKNOWN, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 2);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                          EOrderType::Market, 20'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        cc.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                          EOrderType::Market, 20'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(30'0000);
  }
  // sell 有两个手数为2的空单都携带止盈止损被一次吃掉
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9950'0000, ETriggerBy::LastPrice, 10500'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 9950'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 10500'0000, EOrderType::Market, 0);
    buy_order_id = order_id;
  }
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9950'0000, ETriggerBy::LastPrice, 10500'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 9950'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 10500'0000, EOrderType::Market, 0);
  }
  // user_id 2 buy
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 40'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  // 结果为1手空仓 1收空仓的止盈 1手空仓的止损
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 6);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        if (order.side() == ESide::Buy) {
          orderCheck.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init)
              .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 9950'0000, stop_order_type, EOrderType::Market,
                              10'0000);
        } else {
          orderCheck.CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated)
              .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                              EOrderType::Market, 20'0000);
        }
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        if (order.side() == ESide::Buy) {
          orderCheck.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init)
              .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 10500'0000, stop_order_type,
                              EOrderType::Market, 10'0000);
        } else {
          orderCheck.CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated)
              .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                              EOrderType::Market, 20'0000);
        }
      } else {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(10'0000);
    EXPECT_EQ(position.m_msg->side(), ESide::Sell);
  }

  // 下单将头寸平掉, 恢复状态
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 10'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 10'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 10500'0000, stop_order_type, EOrderType::Market,
                            10'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 9950'0000, stop_order_type, EOrderType::Market,
                            10'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(0);
  }

  // 8 有5手多仓仓位 3手部分止盈 4手部分止损 有两个手数为2的空单都携带止盈止损被自己的多单携带止盈止损一次吃掉
  // 先处理maker 2手平仓的 仓位变成  3手部分止盈   3手部分止损
  // 再处理taker 2手开仓 仓位变成 3手部分止盈   3手部分止损 2手部分止盈   2手部分止损
  // 先处理maker 2手平仓的 仓位变成  1手部分止盈   1手部分止损  2手部分止盈   2手部分止损
  // 再处理taker 2手开仓 仓位变成  1手部分止盈   1手部分止损  2手部分止盈   2手部分止损   2手部分止盈  2手部分止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = order_id;
  }
  // user_id 2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
  }
  // 3手部分止盈 4手部分止损
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000'0000, ETriggerBy::UNKNOWN, -1,
                          ETriggerBy::UNKNOWN, -1, -1, 30'0000, -1, "", "", 0, ETpSlMode::Partial, EOrderType::Market,
                          EOrderType::UNKNOWN, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                          EOrderType::Market, 30'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
  }
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::MarkPrice, 9900'0000,
                          ETriggerBy::UNKNOWN, -1, -1, -1, 40'0000, "", "", 0, ETpSlMode::Partial, EOrderType::Market,
                          EOrderType::UNKNOWN, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        cc.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, stop_order_type, EOrderType::Market,
                          40'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
  }
  // 有两个手数为2的空单都携带止盈止损被自己的多单携带止盈止损一次吃掉
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9950'0000, ETriggerBy::LastPrice, 10500'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 9950'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 10500'0000, EOrderType::Market, 0);
    buy_order_id = order_id;
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlInfo(1, 1, -1, -1);
    position.CheckSize(50'0000);
  }
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9950'0000, ETriggerBy::LastPrice, 10500'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 9950'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 10500'0000, EOrderType::Market, 0);
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlInfo(1, 1, -1, -1);
    position.CheckSize(50'0000);
  }
  // buy 40，携带止盈止损 自成交
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 40'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 12000'0000, ETriggerBy::LastPrice, 9800'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0, true);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 5);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      TransactDTOChecker orderCheck(&order);
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 12000'0000, stop_order_type, EOrderType::Market,
                            40'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 9800'0000, stop_order_type, EOrderType::Market,
                            40'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        if (order.side() == ESide::Buy) {
          orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);
        } else {
          orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
        }
      }
    }

    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlInfo(2, 2, 50'0000, 50'0000);
    position.CheckSize(50'0000);
  }
  // 下单将头寸平掉, 恢复状态
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 5);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss ||
                 stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(0);
    position.CheckTpSlInfo(0, 0, 0, 0);
  }
}

TEST_F(MergeSinglePartialTpSlTSTest, TestMergedSingle_9_to_11) {
  biz::user_id_t user_id1 = 653106;
  // 构建客户端
  stub user1(te, user_id1);
  // 设置逐仓10x杠杆+充钱
  Deposit(user1);

  UpdateMarketData(10000);

  biz::user_id_t user_id2 = 111111;
  stub user2(te, user_id2);
  // 设置逐仓10x杠杆+充钱
  Deposit(user2);

  // 切换到部分止盈止损模式
  {
    FutureOneOfSwitchTpSlModeBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, ETpSlMode::Partial);
    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 switch tp sl mode: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 switch tp sl mode:  " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(position.m_msg->tp_sl_mode(), static_cast<ETpSlMode>(ETpSlMode::Partial));
  }

  // 9 有5手多仓仓位 3手部分止盈 4手部分止损 有两个手数为2的空单都携带止盈止损被自己的多单不携带止盈止损一次吃掉
  std::string buy_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = order_id;
  }
  // user_id 2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
    position.CheckTpSlInfo(0, 0, -1, -1);
  }
  // 3手部分止盈 4手部分止损
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000'0000, ETriggerBy::UNKNOWN, -1,
                          ETriggerBy::UNKNOWN, -1, -1, 30'0000, -1, "", "", 0, ETpSlMode::Partial, EOrderType::Market,
                          EOrderType::UNKNOWN, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                          EOrderType::Market, 30'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
    position.CheckTpSlInfo(1, 0, -1, -1);
  }
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::MarkPrice, 9900'0000,
                          ETriggerBy::UNKNOWN, -1, -1, -1, 40'0000, "", "", 0, ETpSlMode::Partial, EOrderType::Market,
                          EOrderType::UNKNOWN, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        cc.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, stop_order_type, EOrderType::Market,
                          40'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
    position.CheckTpSlInfo(1, 1, -1, -1);
  }
  // 有两个手数为2的空单都携带止盈止损被自己的多单不携带止盈止损一次吃掉
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9950'0000, ETriggerBy::LastPrice, 10500'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0, false);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 9950'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 10500'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9950'0000, ETriggerBy::LastPrice, 10500'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0, false);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 9950'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 10500'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // 自成交
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 40'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.cum_qty_x() != 0) {
        if (order.side() == ESide::Buy) {
          orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);
        } else {
          orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
        }
      } else {
        EXPECT_TRUE(false);
      }
    }

    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
    position.CheckTpSlInfo(1, 1, 50'0000, 50'0000);
  }
  // 下单将头寸平掉, 恢复状态
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 buy
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, stop_order_type, EOrderType::Market,
                            40'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 11000'0000, stop_order_type, EOrderType::Market,
                            30'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(0);
    position.CheckTpSlInfo(0, 0, 0, 0);
    EXPECT_EQ(position.m_msg->side(), ESide::None);
  }

  // 10 有仓位 部分止盈止损占满，平一部分仓位
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 10100'0000, ETriggerBy::LastPrice, 9990'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0, 0);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 10100'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 9990'0000, EOrderType::Market, 0);
    buy_order_id = order_id;
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(0);
    position.CheckTpSlInfo(0, 0, -1, -1);
  }
  // user_id 2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init)
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 10100'0000, stop_order_type, EOrderType::Market,
                            50'0000);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init)
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 9990'0000, stop_order_type, EOrderType::Market,
                            50'0000);
      } else {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
    position.CheckTpSlInfo(1, 1, -1, -1);
  }
  // 平一部分仓位
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 buy
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(30'0000);
    position.CheckTpSlInfo(1, 1, -1, -1);
  }
  // 下单将头寸平掉, 恢复状态
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 30'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 30'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 9990'0000, stop_order_type, EOrderType::Market,
                            50'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 10100'0000, stop_order_type, EOrderType::Market,
                            50'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(0);
    position.CheckTpSlInfo(0, 0, 0, 0);
  }

  // 11 有仓位 部分止盈止损（tp 3 sl 2 tp 2 sl 3）占满，平一部分仓位(平2手)
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = order_id;
  }
  // user_id 2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 50'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
    position.CheckTpSlInfo(0, 0, -1, -1);
  }
  // 设置止盈止损信息
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000'0000, ETriggerBy::UNKNOWN, -1,
                          ETriggerBy::UNKNOWN, -1, -1, 30'0000, -1, "", "", 0, ETpSlMode::Partial, EOrderType::Market,
                          EOrderType::UNKNOWN, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                          EOrderType::Market, 30'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
    position.CheckTpSlInfo(1, 0, -1, -1);
  }
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::MarkPrice, 9900'0000,
                          ETriggerBy::UNKNOWN, -1, -1, -1, 20'0000, "", "", 0, ETpSlMode::Partial, EOrderType::Market,
                          EOrderType::UNKNOWN, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        cc.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, stop_order_type, EOrderType::Market,
                          20'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
    position.CheckTpSlInfo(1, 1, -1, -1);
  }
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000'0000, ETriggerBy::MarkPrice,
                          9900'0000, ETriggerBy::UNKNOWN, -1, -1, 30'0000, 30'0000, "", "", 0, ETpSlMode::Partial,
                          EOrderType::Market, EOrderType::UNKNOWN, -1, -1, true);
    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 2);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        cc.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, stop_order_type, EOrderType::Market,
                          30'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, 11000'0000, stop_order_type, EOrderType::Market,
                          30'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(50'0000);
    position.CheckTpSlInfo(2, 2, -1, -1);
  }
  // 平2手
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2 sell
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 20'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user_id2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef MergeSinglePartialTpSlTSTest_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(30'0000);
    position.CheckTpSlInfo(2, 2, 30'0000, 30'0000);
  }
}
