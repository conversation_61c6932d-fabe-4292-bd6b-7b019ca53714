#include <boost/uuid/uuid_io.hpp>

#include "gtest/gtest.h"
#include "lib/stub.hpp"
#include "src/data/enum.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class TestReplaceCarryTpSl : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestCase() {}
  static void TearDownTestCase() { /*app::GlobalVarManager::Instance().coin_ = ECoin::UNKNOWN;*/ }

 protected:
  void Deposit(stub& user1) {
    // 充值
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, user1.m_uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                       std::to_string(10'000'000'000), bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    std::cout << "deposit result :" << deposit_result.m_msg->DebugString() << std::endl;
  }

  void UpdateMarketData(int64_t price) {
    // auto const underlying_price = bbase::decimal::Decimal<>("10150");
    te->AddMarkPrice(static_cast<ESymbol>(symbol), price, price, price);
  }
  void NewLine() { std::cout << std::endl << "-----------------------------------------" << std::endl; }

 protected:
  std::shared_ptr<tmock::CTradeAppMock> te;
  biz::coin_t coin = ECoin::USDT;
  biz::symbol_t const symbol = 5;
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  /*tmock::TradeEngineMock te_{"BTC"};
  tmock::TriggerEngineMock tre_;
  tmock::Matching_Engine me_;*/
};

TEST_F(TestReplaceCarryTpSl, TestReplaceCarryTpSl) {
  biz::user_id_t user_id_other = 653106;
  stub user_other(te, user_id_other);
  Deposit(user_other);

  biz::user_id_t user_id_test = 111111;
  stub user_test(te, user_id_test);
  Deposit(user_test);

  UpdateMarketData(10000);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin,
        ETriggerBy::LastPrice, 10010'0000, ETriggerBy::LastPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);

    // 修改订单  价格超过止盈价  失败
    FutureOneOfReplaceOrderBuilder replace_build2(order_id, symbol, 0, 10011'0000, user_id_test, coin);
    auto resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(resp_test2->ret_code(), error::ErrorCode::kErrorCodeTpShouldGtBase);

    // 修改订单  价格低于止损价  失败
    FutureOneOfReplaceOrderBuilder replace_build3(order_id, symbol, 0, 8000'0000, user_id_test, coin);
    auto resp_test3 = user_test.process(replace_build3.Build());
    EXPECT_EQ(resp_test3->ret_code(), error::ErrorCode::kErrorCodeSlShouldLtBase);

    // 修改订单  价格在止盈止损之间  成功
    FutureOneOfReplaceOrderBuilder replace_build4(order_id, symbol, 0, 10009'0000, user_id_test, coin);
    auto resp_test4 = user_test.process(replace_build4.Build());
    EXPECT_EQ(resp_test4->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    te->PopResult();
  }

  UpdateMarketData(11000);
  // 开仓做空: -100@$10,100 开仓单携带止盈止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 11000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin,
        ETriggerBy::LastPrice, 10909'0000, ETriggerBy::LastPrice, 11010'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10909'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);

    // 修改订单  价格低于止盈价  失败
    FutureOneOfReplaceOrderBuilder replace_build2(order_id, symbol, 0, 10908'0000, user_id_test, coin);
    auto resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(resp_test2->ret_code(), error::ErrorCode::kErrorCodeTpShouldLtBase);

    // 修改订单  价格高于止损价  失败
    FutureOneOfReplaceOrderBuilder replace_build3(order_id, symbol, 0, 11011'0000, user_id_test, coin);
    auto resp_test3 = user_test.process(replace_build3.Build());
    EXPECT_EQ(resp_test3->ret_code(), error::ErrorCode::kErrorCodeSlShouldGtBase);

    // 修改订单  价格在止盈止损之间  成功
    FutureOneOfReplaceOrderBuilder replace_build4(order_id, symbol, 0, 10910'0000, user_id_test, coin);
    auto resp_test4 = user_test.process(replace_build4.Build());
    EXPECT_EQ(resp_test4->ret_code(), error::ErrorCode::kErrorCodeSuccess);
  }
}

TEST_F(TestReplaceCarryTpSl, TestReplaceCarryTpSl_change_tpslMode) {
  biz::user_id_t user_id_other = 653106;
  stub user_other(te, user_id_other);
  Deposit(user_other);

  biz::user_id_t user_id_test = 111111;
  stub user_test(te, user_id_test);
  Deposit(user_test);

  UpdateMarketData(10000);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin,
        ETriggerBy::LastPrice, 10010'0000, ETriggerBy::LastPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);

    // 尝试改变携带的tpsl参数, 取消止盈止损
    FutureOneOfReplaceOrderBuilder replace_build;
    replace_build.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 0, 0, {}, {}, {}, {}, {}, {}, {}, {});
    auto replace_resp_test = user_test.process(replace_build.Build());
    auto replace_test = te->PopResult();
    EXPECT_EQ(replace_resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(replace_test.m_msg.get(), nullptr);
    auto replace_orderCheck = replace_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(replace_orderCheck.m_msg, nullptr);
    EXPECT_EQ(replace_orderCheck.m_msg->take_profit_x(), 0);
    EXPECT_EQ(replace_orderCheck.m_msg->tp_trigger_by(), ETriggerBy::UNKNOWN);
    EXPECT_EQ(static_cast<ETpSlMode>(replace_orderCheck.m_msg->tp_sl_mode()), ETpSlMode::UNKNOWN);

    // 再次改单，设置止盈止损，之前是full，这次设partial
    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10010'0000, 9990'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Limit, EOrderType::Limit,
                            ETpSlMode::Partial, 11000'0000, 9000'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    auto replace_test2 = te->PopResult();
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(replace_test2.m_msg.get(), nullptr);
    auto replace_orderCheck2 = replace_test2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(replace_orderCheck2.m_msg, nullptr);
    EXPECT_EQ(replace_orderCheck2.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(replace_orderCheck2.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(replace_orderCheck2.m_msg->stop_loss_x(), 9990'0000);
    EXPECT_EQ(replace_orderCheck2.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(replace_orderCheck2.m_msg->tp_sl_mode()), ETpSlMode::Partial);

    // 直接改tpslmode，失败，只能从无到有或从有到无
    FutureOneOfReplaceOrderBuilder replace_build3;
    replace_build3.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10010'0000, 9990'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, {}, {}, ETpSlMode::Full);
    auto replace_resp_test3 = user_test.process(replace_build3.Build());
    EXPECT_EQ(replace_resp_test3->ret_code(), error::ErrorCode::kErrorCodeParamsError);

    // 尝试改变携带的tpsl参数, 取消止盈止损, 但是保持一些其他参数
    FutureOneOfReplaceOrderBuilder replace_build4;
    replace_build4.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 0, 0, {}, {}, {}, ETriggerBy::LastPrice,
                            ETriggerBy::LastPrice, EOrderType::Limit, EOrderType::Limit, {}, 11000'0000, 9000'0000);
    auto replace_resp_test4 = user_test.process(replace_build4.Build());
    auto replace_test4 = te->PopResult();
    EXPECT_EQ(replace_resp_test4->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(replace_test4.m_msg.get(), nullptr);
    auto replace_orderCheck4 = replace_test4.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(replace_orderCheck4.m_msg, nullptr);
    EXPECT_EQ(replace_orderCheck4.m_msg->take_profit_x(), 0);
    EXPECT_EQ(replace_orderCheck4.m_msg->tp_trigger_by(), ETriggerBy::UNKNOWN);
    EXPECT_EQ(static_cast<ETpSlMode>(replace_orderCheck4.m_msg->tp_sl_mode()), ETpSlMode::UNKNOWN);
  }
}

TEST_F(TestReplaceCarryTpSl, TestReplaceCarryTpSl_replace_price_qty_fail) {
  biz::user_id_t user_id_test = 111111;
  stub user_test(te, user_id_test);
  Deposit(user_test);

  UpdateMarketData(10000);
  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin,
        ETriggerBy::LastPrice, 10010'0000, ETriggerBy::LastPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);

    // 尝试改变价格参数失败, 过小
    FutureOneOfReplaceOrderBuilder replace_build;
    replace_build.SetValue(order_id, coin, symbol, user_id_test, 900'0000, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {});
    auto replace_resp_test = user_test.process(replace_build.Build());
    EXPECT_EQ(replace_resp_test->ret_code(), error::ErrorCode::kErrorCodeOrderPriceOutOfRange);

    // 尝试改变价格参数失败, 过大
    FutureOneOfReplaceOrderBuilder replace_build1;
    replace_build1.SetValue(order_id, coin, symbol, user_id_test, 100000000000'0000, {}, {}, {}, {}, {}, {}, {}, {}, {},
                            {}, {});
    auto replace_resp_test1 = user_test.process(replace_build1.Build());
    EXPECT_EQ(replace_resp_test1->ret_code(), error::ErrorCode::kErrorCodeOrderPriceOutOfRange);

    // 尝试改变qty参数失败 过大
    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, 100000000000'0000, {}, {}, {}, {}, {}, {}, {}, {},
                            {}, {});
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeQtyTooLargeOrInvalid);

    te->SuspendCross();

    // 尝试改变qty参数, 过小, 由于round存在，不会报错
    FutureOneOfReplaceOrderBuilder replace_build3;
    replace_build3.SetValue(order_id, coin, symbol, user_id_test, {}, 10'0000, {}, {}, {}, {}, {}, {}, {}, {}, {}, {});
    auto replace_resp_test3 = user_test.process(replace_build3.Build());
    EXPECT_EQ(replace_resp_test3->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    // result_test = te->PopResult();
    // orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    // ASSERT_NE(orderCheck.m_msg, nullptr);
    // EXPECT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::New);

    // 尝试改变qty, 存在pend replace，不能修改
    FutureOneOfReplaceOrderBuilder replace_build4;
    replace_build4.SetValue(order_id, coin, symbol, user_id_test, {}, 100000000000'0000, {}, {}, {}, {}, {}, {}, {}, {},
                            {}, {});
    auto replace_resp_test4 = user_test.process(replace_build4.Build());
    EXPECT_EQ(replace_resp_test4->ret_code(), error::ErrorCode::kErrorCodeOrderStatusIsPendingCannotOperate);
  }
}

TEST_F(TestReplaceCarryTpSl, TestReplaceCarryTpSl_tp) {
  biz::user_id_t user_id_other = 653106;
  stub user_other(te, user_id_other);
  Deposit(user_other);

  biz::user_id_t user_id_test = 111111;
  stub user_test(te, user_id_test);
  Deposit(user_test);

  UpdateMarketData(10000);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100'0000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::LastPrice, 10100'0000,
                                  ETriggerBy::LastPrice, 9900'0000, ETriggerBy::MarkPrice, -1, 10010'0000,
                                  EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::UNKNOWN, EOrderType::UNKNOWN,
                                  -1, -1, 0, 0, {}, 0, EPriceDirection::Rising);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10100'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->trigger_by(), ETriggerBy::MarkPrice);

    // // 修改订单  价格超过止盈价  失败
    // FutureOneOfReplaceOrderBuilder replace_build2(order_id, symbol, 0, 10110'0000, user_id_test, coin);
    // auto resp_test2 = user_test.process(replace_build2.Build());
    // EXPECT_EQ(resp_test2->ret_code(), error::ErrorCode::kErrorCodeTpShouldGtBase);

    // 修改订单  价格低于止损价  失败
    FutureOneOfReplaceOrderBuilder replace_build3(order_id, symbol, 0, 8000'0000, user_id_test, coin);
    auto resp_test3 = user_test.process(replace_build3.Build());
    EXPECT_EQ(resp_test3->ret_code(), error::ErrorCode::kErrorCodeSlShouldLtBase);

    // 修改订单  价格在止盈止损之间  成功
    FutureOneOfReplaceOrderBuilder replace_build4(order_id, symbol, 0, 10009'0000, user_id_test, coin);
    auto resp_test4 = user_test.process(replace_build4.Build());
    EXPECT_EQ(resp_test4->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    te->PopResult();

    // 模拟下跌触发 (mark_price)10040 < (trigger_price)10050
    auto underlying_price = bbase::decimal::Decimal<>("10040");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次，最多2.5s
    while (!checked && try_times < 5) {
      usleep(500 * 1000);

      // 更新标记价格
      // 模拟下跌触发 (mark_price)10040- < (trigger_price)10050
      underlying_price = underlying_price.Sub(1);  // 每次需要变一下价格，不然会被trigger拦截
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);

      // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      std::int32_t ret =
          te->pipeline_->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 取trading result
      auto active_so_result = te->PopResult();
      if (active_so_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发标记价格，尝试触发
        try_times++;
        continue;
      }

      // 拿到active_so的result, 说明触发成功，不需要循环发标记价和timer了，走完下面的检查流程即可
      checked = true;

      // 获取订单数据
      auto order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
      ASSERT_NE(order_check.m_msg, nullptr);

      order_check.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);

      // 取trading result
      active_so_result = te->PopResult();
      ASSERT_NE(active_so_result.m_msg, nullptr);

      // 获取订单数据
      order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
      ASSERT_NE(order_check.m_msg, nullptr);

      // 这个中间过程目前框架截取不了，直接检查终态（New, NewAccepted）
      //  order_check.CheckStatus(EOrderStatus::Triggered,
      //  ECrossStatus::PendingNew);
      order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

      to_check_res = true;
    }
    ASSERT_EQ(to_check_res, true);

    // 改单，带动tp triggerby到mark price
    FutureOneOfReplaceOrderBuilder replace_build5(order_id, symbol, 0, 10009'0000, user_id_test, coin, {}, {}, {},
                                                  ETriggerBy::MarkPrice);
    auto resp_test5 = user_test.process(replace_build5.Build());
    EXPECT_EQ(resp_test5->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    result_test = te->PopResult();
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10100'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->trigger_by(), ETriggerBy::MarkPrice);
  }
}

TEST_F(TestReplaceCarryTpSl, TestReplaceCarryTpSl_change_limit_price) {
  biz::user_id_t user_id_test = 111111;
  stub user_test(te, user_id_test);
  Deposit(user_test);

  UpdateMarketData(10000);
  ESide side = ESide::Buy;

  biz::size_x_t qty = 100'0000;
  biz::price_x_t price = 10000'0000;
  // 场景1：当前的订单的tpslmode是full, 无法设置tp/sl limit price, 报错
  {
    auto order_id = te->GenUUID();

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin,
        ETriggerBy::LastPrice, 10010'0000, ETriggerBy::LastPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);
    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->order_type(), EOrderType::Limit);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Full);

    // 无法设置tp/sl limit price, 报错
    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                            10000'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeParamsError);
  }

  // 场景2，之前没有带tpsl参数，设置tp/sl limit price，成功
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, {}, {}, {}, {}, {}, {}, {}, {}, {},
                                  {}, {}, {}, {}, {}, {}, {}, {}, {});
    auto create_resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(create_resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test = te->PopResult();
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->price_x(), price);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 0);

    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10010'0000, 9900'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 10000'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test2 = te->PopResult();
    auto orderCheck2 = result_test2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck2.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck2.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck2.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck2.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_limit_x(), 10000'0000);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_limit_x(), 9900'0000);
  }

  // 场景3，订单原先有partial 市价tpsl，修改为限价tpsl，成功
  {
    FutureOneOfCreateOrderBuilder create_build;
    auto order_id = te->GenUUID();
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin,
        ETriggerBy::LastPrice, 10010'0000, ETriggerBy::LastPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);
    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->order_type(), EOrderType::Limit);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);

    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10010'0000, 9900'0000, {}, {}, {}, {}, {}, {},
                            {}, {}, 10000'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test2 = te->PopResult();
    auto orderCheck2 = result_test2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck2.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck2.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck2.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck2.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_limit_x(), 10000'0000);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_limit_x(), 9900'0000);
  }
}

TEST_F(TestReplaceCarryTpSl, TestReplaceCarryTpSl_change_limit_price_buy_fail) {
  biz::user_id_t user_id_test = 111111;
  stub user_test(te, user_id_test);
  Deposit(user_test);

  UpdateMarketData(10000);
  ESide side = ESide::Buy;

  biz::size_x_t qty = 100'0000;
  biz::price_x_t price = 10000'0000;

  {
    // 场景1，之前没有带tpsl参数，设置tp/sl limit price，但是tp小于base price，失败
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, {}, {}, {}, {}, {}, {}, {}, {}, {},
                                  {}, {}, {}, {}, {}, {}, {}, {}, {});
    auto create_resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(create_resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test = te->PopResult();
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->price_x(), price);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 0);

    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10010'0000, 9900'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 9900'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeTpShouldGtBase);

    // 场景2，设置tp/sl limit price，但是sl大于base price，失败
    FutureOneOfReplaceOrderBuilder replace_build3;
    replace_build3.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10010'0000, 9900'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 9900'0000, 11000'0000);
    auto replace_resp_test3 = user_test.process(replace_build3.Build());
    EXPECT_EQ(replace_resp_test3->ret_code(), error::ErrorCode::kErrorCodeSlShouldLtBase);
  }

  // 使用条件单
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin, {},
        {}, {}, {}, ETriggerBy::LastPrice, {}, 10010'0000, EStopOrderType::Stop, {}, {}, {}, {}, {}, {}, {}, {}, {});
    auto create_resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(create_resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test = te->PopResult();
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->price_x(), price);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(orderCheck.m_msg->cross_status(), ECrossStatus::Init);
    // 场景1，之前没有带tpsl参数，设置tp/sl limit price，但是tp小于base price，失败
    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10010'0000, 9900'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 9900'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeTpShouldGtBase);

    // 场景2，设置tp/sl limit price，但是sl大于base price，失败
    FutureOneOfReplaceOrderBuilder replace_build3;
    replace_build3.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10010'0000, 9900'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 11000'0000, 11000'0000);
    auto replace_resp_test3 = user_test.process(replace_build3.Build());
    EXPECT_EQ(replace_resp_test3->ret_code(), error::ErrorCode::kErrorCodeSlShouldLtBase);
  }
}

TEST_F(TestReplaceCarryTpSl, TestReplaceCarryTpSl_change_limit_price_sell_fail) {
  biz::user_id_t user_id_test = 111111;
  stub user_test(te, user_id_test);
  Deposit(user_test);

  UpdateMarketData(10000);
  ESide side = ESide::Sell;

  biz::size_x_t qty = 100'0000;
  biz::price_x_t price = 10000'0000;

  {
    // 场景1，之前没有带tpsl参数，设置tp/sl limit price，但是tp大于base price，失败
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, {}, {}, {}, {}, {}, {}, {}, {}, {},
                                  {}, {}, {}, {}, {}, {}, {}, {}, {});
    auto create_resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(create_resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test = te->PopResult();
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->price_x(), price);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 0);

    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 9900'0000, 10010'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 10100'0000, 10100'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeTpShouldLtBase);

    // 场景2，设置tp/sl limit price，但是sl小于base price，失败
    FutureOneOfReplaceOrderBuilder replace_build3;
    replace_build3.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 9900'0000, 10010'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 9900'0000, 9900'0000);
    auto replace_resp_test3 = user_test.process(replace_build3.Build());
    EXPECT_EQ(replace_resp_test3->ret_code(), error::ErrorCode::kErrorCodeSlShouldGtBase);
  }

  // 使用条件单
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin, {},
        {}, {}, {}, ETriggerBy::LastPrice, {}, 10010'0000, EStopOrderType::Stop, {}, {}, {}, {}, {}, {}, {}, {}, {});
    auto create_resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(create_resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test = te->PopResult();
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->price_x(), price);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(orderCheck.m_msg->cross_status(), ECrossStatus::Init);
    // 场景1，之前没有带tpsl参数，设置tp/sl limit price，但是tp大于base price，失败
    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 9900'0000, 10010'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 10100'0000, 10100'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeTpShouldLtBase);

    // 场景2，设置tp/sl limit price，但是sl小于base price，失败
    FutureOneOfReplaceOrderBuilder replace_build3;
    replace_build3.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 9900'0000, 10010'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 9900'0000, 9900'0000);
    auto replace_resp_test3 = user_test.process(replace_build3.Build());
    EXPECT_EQ(replace_resp_test3->ret_code(), error::ErrorCode::kErrorCodeSlShouldGtBase);
  }
}

TEST_F(TestReplaceCarryTpSl, TestReplaceCarryTpSl_change_limit_price_stop) {
  biz::user_id_t user_id_test = 111111;
  stub user_test(te, user_id_test);
  Deposit(user_test);

  UpdateMarketData(10000);
  ESide side = ESide::Buy;

  biz::size_x_t qty = 100'0000;
  biz::price_x_t price = 10000'0000;
  // 场景1：当前的订单的tpslmode是full, 无法设置tp/sl limit price, 报错
  {
    auto order_id = te->GenUUID();

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin,
        ETriggerBy::LastPrice, 10010'0000, ETriggerBy::LastPrice, 9900'0000, ETriggerBy::LastPrice, -1, 10010'0000,
        EStopOrderType::Stop, ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);
    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(orderCheck.m_msg->cross_status(), ECrossStatus::Init);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Full);

    // 无法设置tp/sl limit price, 报错
    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                            10000'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeParamsError);
  }

  // 场景2，之前没有带tpsl参数，设置tp/sl limit price，成功
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin, {},
        {}, {}, {}, ETriggerBy::LastPrice, {}, 10010'0000, EStopOrderType::Stop, {}, {}, {}, {}, {}, {}, {}, {}, {});
    auto create_resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(create_resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test = te->PopResult();
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->price_x(), price);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(orderCheck.m_msg->cross_status(), ECrossStatus::Init);

    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10010'0000, 9900'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 10000'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test2 = te->PopResult();
    auto orderCheck2 = result_test2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck2.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck2.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck2.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck2.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_limit_x(), 10000'0000);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_limit_x(), 9900'0000);
  }

  // 场景3，订单原先有partial 市价tpsl，修改为限价tpsl，成功
  {
    FutureOneOfCreateOrderBuilder create_build;
    auto order_id = te->GenUUID();
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin,
        ETriggerBy::LastPrice, 10010'0000, ETriggerBy::LastPrice, 9900'0000, ETriggerBy::LastPrice, -1, 10010'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);
    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(orderCheck.m_msg->cross_status(), ECrossStatus::Init);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);

    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10010'0000, 9900'0000, {}, {}, {}, {}, {}, {},
                            {}, {}, 10000'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test2 = te->PopResult();
    auto orderCheck2 = result_test2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck2.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck2.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck2.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck2.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_limit_x(), 10000'0000);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_limit_x(), 9900'0000);
  }
}

// 场景：有设置tpsl参数时，只修改价格或订单，其余tpsl参数不受影响, 活动限价单
TEST_F(TestReplaceCarryTpSl, ReplaceCarryTpSl_Test3) {
  biz::user_id_t user_id_test = 111111;
  stub user_test(te, user_id_test);
  Deposit(user_test);

  UpdateMarketData(10000);
  ESide side = ESide::Buy;

  biz::size_x_t qty = 100'0000;
  biz::price_x_t price = 10000'0000;

  // 场景开始：之前没有带tpsl参数，设置tp/sl limit price，成功
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, {}, {}, {}, {}, {}, {}, {}, {}, {},
                                  {}, {}, {}, {}, {}, {}, {}, {}, {});
    auto create_resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(create_resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test = te->PopResult();
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->price_x(), price);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 0);

    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10010'0000, 9900'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 10000'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test2 = te->PopResult();
    auto orderCheck2 = result_test2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck2.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck2.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck2.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck2.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_limit_x(), 10000'0000);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_limit_x(), 9900'0000);

    FutureOneOfReplaceOrderBuilder replace_build3;
    replace_build3.SetValue(order_id, coin, symbol, user_id_test, 10001'0000, {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                            {}, {}, {});
    auto replace_resp_test3 = user_test.process(replace_build3.Build());
    auto result_test3 = te->PopResult();
    auto orderCheck3 = result_test3.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck3.m_msg, nullptr);
    EXPECT_EQ(orderCheck3.m_msg->price_x(), 10001'0000);
    EXPECT_EQ(orderCheck3.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck3.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck3.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck3.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck3.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck3.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck3.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck3.m_msg->take_profit_limit_x(), 10000'0000);
    EXPECT_EQ(orderCheck3.m_msg->stop_loss_limit_x(), 9900'0000);
  }

  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, {}, {}, {}, {}, {}, {}, {}, {}, {},
                                  {}, {}, {}, {}, {}, {}, {}, {}, {});
    auto create_resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(create_resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test = te->PopResult();
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->price_x(), price);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 0);

    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10010'0000, 9900'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 10000'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test2 = te->PopResult();
    auto orderCheck2 = result_test2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck2.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck2.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck2.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck2.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_limit_x(), 10000'0000);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_limit_x(), 9900'0000);

    FutureOneOfReplaceOrderBuilder replace_build4;
    replace_build4.SetValue(order_id, coin, symbol, user_id_test, {}, 110'0000, {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                            {}, {});
    auto replace_resp_test4 = user_test.process(replace_build4.Build());
    auto result_test4 = te->PopResult();
    auto orderCheck4 = result_test4.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck4.m_msg, nullptr);
    EXPECT_EQ(orderCheck4.m_msg->price_x(), 10000'0000);
    EXPECT_EQ(orderCheck4.m_msg->qty_x(), 110'0000);
    EXPECT_EQ(orderCheck4.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck4.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck4.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck4.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck4.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck4.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck4.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck4.m_msg->take_profit_limit_x(), 10000'0000);
    EXPECT_EQ(orderCheck4.m_msg->stop_loss_limit_x(), 9900'0000);
  }
}

// 场景：有设置tpsl参数时，只修改价格或订单，其余tpsl参数不受影响, 条件限价单
TEST_F(TestReplaceCarryTpSl, ReplaceCarryTpSl_Test4) {
  biz::user_id_t user_id_test = 111111;
  stub user_test(te, user_id_test);
  Deposit(user_test);

  UpdateMarketData(10000);
  ESide side = ESide::Buy;

  biz::size_x_t qty = 100'0000;
  biz::price_x_t price = 10000'0000;

  // 场景开始：之前没有带tpsl参数，设置tp/sl limit price，成功
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin, {},
        {}, {}, {}, ETriggerBy::LastPrice, {}, 10010'0000, EStopOrderType::Stop, {}, {}, {}, {}, {}, {}, {}, {}, {});
    auto create_resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(create_resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test = te->PopResult();
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->price_x(), price);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(orderCheck.m_msg->cross_status(), ECrossStatus::Init);

    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10010'0000, 9900'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 10000'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test2 = te->PopResult();
    auto orderCheck2 = result_test2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck2.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck2.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck2.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck2.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_limit_x(), 10000'0000);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_limit_x(), 9900'0000);

    FutureOneOfReplaceOrderBuilder replace_build3;
    replace_build3.SetValue(order_id, coin, symbol, user_id_test, 10001'0000, {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                            {}, {}, {});
    auto replace_resp_test3 = user_test.process(replace_build3.Build());
    auto result_test3 = te->PopResult();
    auto orderCheck3 = result_test3.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck3.m_msg, nullptr);
    EXPECT_EQ(orderCheck3.m_msg->price_x(), 10001'0000);
    EXPECT_EQ(orderCheck3.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck3.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck3.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck3.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck3.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck3.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck3.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck3.m_msg->take_profit_limit_x(), 10000'0000);
    EXPECT_EQ(orderCheck3.m_msg->stop_loss_limit_x(), 9900'0000);
  }

  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin, {},
        {}, {}, {}, ETriggerBy::LastPrice, {}, 10010'0000, EStopOrderType::Stop, {}, {}, {}, {}, {}, {}, {}, {}, {});
    auto create_resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(create_resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test = te->PopResult();
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->price_x(), price);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(orderCheck.m_msg->cross_status(), ECrossStatus::Init);

    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10010'0000, 9900'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 10000'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test2 = te->PopResult();
    auto orderCheck2 = result_test2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck2.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck2.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck2.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck2.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_limit_x(), 10000'0000);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_limit_x(), 9900'0000);

    FutureOneOfReplaceOrderBuilder replace_build4;
    replace_build4.SetValue(order_id, coin, symbol, user_id_test, {}, 101'0000, {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                            {}, {});
    auto replace_resp_test4 = user_test.process(replace_build4.Build());
    auto result_test4 = te->PopResult();
    auto orderCheck4 = result_test4.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck4.m_msg, nullptr);
    EXPECT_EQ(orderCheck4.m_msg->price_x(), 10000'0000);
    EXPECT_EQ(orderCheck4.m_msg->qty_x(), 100'0000);
    EXPECT_EQ(orderCheck4.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck4.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck4.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck4.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck4.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck4.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck4.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck4.m_msg->take_profit_limit_x(), 10000'0000);
    EXPECT_EQ(orderCheck4.m_msg->stop_loss_limit_x(), 9900'0000);
  }
}

// 场景：有设置tpsl参数，条件市价单，买方向
TEST_F(TestReplaceCarryTpSl, change_limit_price_market_stop) {
  biz::user_id_t user_id_test = 111111;
  stub user_test(te, user_id_test);
  Deposit(user_test);

  UpdateMarketData(10000);
  ESide side = ESide::Buy;

  biz::size_x_t qty = 100'0000;
  biz::price_x_t price = 10000'0000;
  EOrderType cur_order_type = EOrderType::Market;
  // 场景1：当前的订单的tpslmode是full, 无法设置tp/sl limit price, 报错
  {
    auto order_id = te->GenUUID();

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin,
        ETriggerBy::LastPrice, 10010'0000, ETriggerBy::LastPrice, 9900'0000, ETriggerBy::LastPrice, -1, 10010'0000,
        EStopOrderType::Stop, ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);
    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->order_type(), EOrderType::Market);
    EXPECT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(orderCheck.m_msg->cross_status(), ECrossStatus::Init);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Full);

    // 无法设置tp/sl limit price, 报错
    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                            10000'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeParamsError);
  }

  // 场景2，之前没有带tpsl参数，设置tp/sl limit price，成功
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, cur_order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, {}, {}, {}, {},
                                  ETriggerBy::LastPrice, {}, 10010'0000, EStopOrderType::Stop, {}, {}, {}, {}, {}, {},
                                  {}, {}, {});
    auto create_resp_test = user_test.process(create_build.Build());
    EXPECT_EQ(create_resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test = te->PopResult();
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(orderCheck.m_msg->cross_status(), ECrossStatus::Init);

    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10020'0000, 9900'0000, {}, {}, {},
                            ETriggerBy::LastPrice, ETriggerBy::LastPrice, EOrderType::Market, EOrderType::Market,
                            ETpSlMode::Partial, 10020'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test2 = te->PopResult();
    auto orderCheck2 = result_test2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_x(), 10020'0000);
    EXPECT_EQ(orderCheck2.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck2.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck2.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck2.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_limit_x(), 10020'0000);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_limit_x(), 9900'0000);
  }

  // 场景3，订单原先有partial 市价tpsl，修改为限价tpsl，成功
  {
    FutureOneOfCreateOrderBuilder create_build;
    auto order_id = te->GenUUID();
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin,
        ETriggerBy::LastPrice, 10020'0000, ETriggerBy::LastPrice, 9900'0000, ETriggerBy::LastPrice, -1, 10010'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);
    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10020'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->order_type(), EOrderType::Market);
    EXPECT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(orderCheck.m_msg->cross_status(), ECrossStatus::Init);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);

    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10020'0000, 9900'0000, {}, {}, {}, {}, {}, {},
                            {}, {}, 10020'0000, 9900'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test2 = te->PopResult();
    auto orderCheck2 = result_test2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_x(), 10020'0000);
    EXPECT_EQ(orderCheck2.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck2.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck2.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck2.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_limit_x(), 10020'0000);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_limit_x(), 9900'0000);

    // 继续修改qty其余tpsl参数不改变
    FutureOneOfReplaceOrderBuilder replace_build3;
    replace_build3.SetValue(order_id, coin, symbol, user_id_test, {}, 101'0000, {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                            {}, {});
    auto replace_resp_test3 = user_test.process(replace_build3.Build());
    auto result_test3 = te->PopResult();
    auto orderCheck3 = result_test3.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck3.m_msg, nullptr);
    EXPECT_EQ(orderCheck3.m_msg->price_x(), 0);
    EXPECT_EQ(orderCheck3.m_msg->trigger_price_x(), 10010'0000);
    EXPECT_EQ(orderCheck3.m_msg->qty_x(), 100'0000);
    EXPECT_EQ(orderCheck3.m_msg->take_profit_x(), 10020'0000);
    EXPECT_EQ(orderCheck3.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck3.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck3.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck3.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck3.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck3.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck3.m_msg->take_profit_limit_x(), 10020'0000);
    EXPECT_EQ(orderCheck3.m_msg->stop_loss_limit_x(), 9900'0000);

    // 继续修改trigger price，其余tpsl参数不改变
    FutureOneOfReplaceOrderBuilder replace_build4;
    replace_build4.SetValue(order_id, coin, symbol, user_id_test, {}, {}, {}, {}, {}, {}, 10015'0000, {}, {}, {}, {},
                            {}, {}, {});
    auto replace_resp_test4 = user_test.process(replace_build4.Build());
    auto result_test4 = te->PopResult();
    auto orderCheck4 = result_test4.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck4.m_msg, nullptr);
    EXPECT_EQ(orderCheck4.m_msg->price_x(), 0);
    EXPECT_EQ(orderCheck4.m_msg->trigger_price_x(), 10015'0000);
    EXPECT_EQ(orderCheck4.m_msg->qty_x(), 100'0000);
    EXPECT_EQ(orderCheck4.m_msg->take_profit_x(), 10020'0000);
    EXPECT_EQ(orderCheck4.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck4.m_msg->stop_loss_x(), 9900'0000);
    EXPECT_EQ(orderCheck4.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck4.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck4.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck4.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck4.m_msg->take_profit_limit_x(), 10020'0000);
    EXPECT_EQ(orderCheck4.m_msg->stop_loss_limit_x(), 9900'0000);

    // 修改不合适的tp、sl limit price，报错
    FutureOneOfReplaceOrderBuilder replace_build6;
    replace_build6.SetValue(order_id, coin, symbol, user_id_test, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                            10014'0000, {});
    auto replace_resp_test6 = user_test.process(replace_build6.Build());
    EXPECT_EQ(replace_resp_test6->ret_code(), error::ErrorCode::kErrorCodeTpShouldGtBase);

    FutureOneOfReplaceOrderBuilder replace_build7;
    replace_build7.SetValue(order_id, coin, symbol, user_id_test, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                            10016'0000);
    auto replace_resp_test7 = user_test.process(replace_build7.Build());
    EXPECT_EQ(replace_resp_test7->ret_code(), error::ErrorCode::kErrorCodeSlShouldLtBase);

    // 修改不合适的tp、sl触发价格，报错
    FutureOneOfReplaceOrderBuilder replace_build8;
    replace_build8.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10014'0000, {}, {}, {}, {}, {}, {}, {}, {},
                            {}, {}, {});
    auto replace_resp_test8 = user_test.process(replace_build8.Build());
    EXPECT_EQ(replace_resp_test6->ret_code(), error::ErrorCode::kErrorCodeTpShouldGtBase);

    FutureOneOfReplaceOrderBuilder replace_build9;
    replace_build9.SetValue(order_id, coin, symbol, user_id_test, {}, {}, {}, 10016'0000, {}, {}, {}, {}, {}, {}, {},
                            {}, {}, {});
    auto replace_resp_test9 = user_test.process(replace_build9.Build());
    EXPECT_EQ(replace_resp_test9->ret_code(), error::ErrorCode::kErrorCodeSlShouldLtBase);

    // 把tpsl参数取消掉，成功
    // sleep(1);
    FutureOneOfReplaceOrderBuilder replace_build5;
    replace_build5.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 0, 0, {}, {}, 10015'0000, {}, {}, {}, {}, {},
                            {}, {});
    auto replace_resp_test5 = user_test.process(replace_build5.Build());
    auto result_test5 = te->PopResult();
    auto orderCheck5 = result_test5.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck5.m_msg, nullptr);
    EXPECT_EQ(orderCheck5.m_msg->price_x(), 0);
    EXPECT_EQ(orderCheck5.m_msg->trigger_price_x(), 10015'0000);
    EXPECT_EQ(orderCheck5.m_msg->qty_x(), 100'0000);
    EXPECT_EQ(orderCheck5.m_msg->take_profit_x(), 0);
    EXPECT_EQ(orderCheck5.m_msg->tp_trigger_by(), ETriggerBy::UNKNOWN);
    EXPECT_EQ(orderCheck5.m_msg->stop_loss_x(), 0);
    EXPECT_EQ(orderCheck5.m_msg->sl_trigger_by(), ETriggerBy::UNKNOWN);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck5.m_msg->tp_sl_mode()), ETpSlMode::UNKNOWN);
    EXPECT_EQ(orderCheck5.m_msg->tp_order_type(), EOrderType::UNKNOWN);
    EXPECT_EQ(orderCheck5.m_msg->sl_order_type(), EOrderType::UNKNOWN);
    EXPECT_EQ(orderCheck5.m_msg->take_profit_limit_x(), 0);
    EXPECT_EQ(orderCheck5.m_msg->stop_loss_limit_x(), 0);
  }
}

// 场景 修改条件市价单，卖方向，价格校验失败
TEST_F(TestReplaceCarryTpSl, change_limit_price_market_stop_sell) {
  biz::user_id_t user_id_test = 111111;
  stub user_test(te, user_id_test);
  Deposit(user_test);

  UpdateMarketData(10000);
  ESide side = ESide::Sell;

  biz::size_x_t qty = 100'0000;
  biz::price_x_t price = 10000'0000;
  EOrderType cur_order_type = EOrderType::Market;

  {
    FutureOneOfCreateOrderBuilder create_build;
    auto order_id = te->GenUUID();
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_order_type, qty, price, ETimeInForce::GoodTillCancel, user_id_test, coin,
        ETriggerBy::LastPrice, 9900'0000, ETriggerBy::LastPrice, 10020'0000, ETriggerBy::LastPrice, -1, 10010'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);
    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 9900'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 10020'0000);
    EXPECT_EQ(orderCheck.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck.m_msg->order_type(), EOrderType::Market);
    EXPECT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(orderCheck.m_msg->cross_status(), ECrossStatus::Init);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);

    FutureOneOfReplaceOrderBuilder replace_build2;
    replace_build2.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 9900'0000, 10020'0000, {}, {}, {}, {}, {}, {},
                            {}, {}, 9900'0000, 10020'0000);
    auto replace_resp_test2 = user_test.process(replace_build2.Build());
    EXPECT_EQ(replace_resp_test2->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result_test2 = te->PopResult();
    auto orderCheck2 = result_test2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_x(), 9900'0000);
    EXPECT_EQ(orderCheck2.m_msg->tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_x(), 10020'0000);
    EXPECT_EQ(orderCheck2.m_msg->sl_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck2.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck2.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck2.m_msg->take_profit_limit_x(), 9900'0000);
    EXPECT_EQ(orderCheck2.m_msg->stop_loss_limit_x(), 10020'0000);

    // 目前状态：trigger price 10010, tp:9900, sl: 10020 限价和触发价设置为相同

    // 修改不合适的tp、sl limit price，报错
    FutureOneOfReplaceOrderBuilder replace_build6;
    replace_build6.SetValue(order_id, coin, symbol, user_id_test, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                            10011'0000, {});
    auto replace_resp_test6 = user_test.process(replace_build6.Build());
    EXPECT_EQ(replace_resp_test6->ret_code(), error::ErrorCode::kErrorCodeTpShouldLtBase);

    FutureOneOfReplaceOrderBuilder replace_build7;
    replace_build7.SetValue(order_id, coin, symbol, user_id_test, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                            10009'0000);
    auto replace_resp_test7 = user_test.process(replace_build7.Build());
    EXPECT_EQ(replace_resp_test7->ret_code(), error::ErrorCode::kErrorCodeSlShouldGtBase);

    // 修改不合适的tp、sl触发价格，报错
    FutureOneOfReplaceOrderBuilder replace_build8;
    replace_build8.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 10011'0000, {}, {}, {}, {}, {}, {}, {}, {},
                            {}, {}, {});
    auto replace_resp_test8 = user_test.process(replace_build8.Build());
    EXPECT_EQ(replace_resp_test6->ret_code(), error::ErrorCode::kErrorCodeTpShouldLtBase);

    FutureOneOfReplaceOrderBuilder replace_build9;
    replace_build9.SetValue(order_id, coin, symbol, user_id_test, {}, {}, {}, 10009'0000, {}, {}, {}, {}, {}, {}, {},
                            {}, {}, {});
    auto replace_resp_test9 = user_test.process(replace_build9.Build());
    EXPECT_EQ(replace_resp_test9->ret_code(), error::ErrorCode::kErrorCodeSlShouldGtBase);

    // 把tpsl参数取消掉，成功
    FutureOneOfReplaceOrderBuilder replace_build5;
    replace_build5.SetValue(order_id, coin, symbol, user_id_test, {}, {}, 0, 0, {}, {}, 10015'0000, {}, {}, {}, {}, {},
                            {}, {});
    auto replace_resp_test5 = user_test.process(replace_build5.Build());
    auto result_test5 = te->PopResult();
    auto orderCheck5 = result_test5.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck5.m_msg, nullptr);
    EXPECT_EQ(orderCheck5.m_msg->price_x(), 0);
    EXPECT_EQ(orderCheck5.m_msg->trigger_price_x(), 10015'0000);
    EXPECT_EQ(orderCheck5.m_msg->qty_x(), 100'0000);
    EXPECT_EQ(orderCheck5.m_msg->take_profit_x(), 0);
    EXPECT_EQ(orderCheck5.m_msg->tp_trigger_by(), ETriggerBy::UNKNOWN);
    EXPECT_EQ(orderCheck5.m_msg->stop_loss_x(), 0);
    EXPECT_EQ(orderCheck5.m_msg->sl_trigger_by(), ETriggerBy::UNKNOWN);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck5.m_msg->tp_sl_mode()), ETpSlMode::UNKNOWN);
    EXPECT_EQ(orderCheck5.m_msg->tp_order_type(), EOrderType::UNKNOWN);
    EXPECT_EQ(orderCheck5.m_msg->sl_order_type(), EOrderType::UNKNOWN);
    EXPECT_EQ(orderCheck5.m_msg->take_profit_limit_x(), 0);
    EXPECT_EQ(orderCheck5.m_msg->stop_loss_limit_x(), 0);
  }
}
