#include <iostream>
#include <memory>
#include <set>
#include <string>

#include "lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/main.hpp"
class TestCustomerSymbollist : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestSuite() {}
  static void TearDownTestSuite() {}

  static std::shared_ptr<tmock::CTradeAppMock> te;
};
/*
1. 开启usdt enabled参数，但是列表中无配置，检查usdt合约开仓回报错误码
2. 构造BTCUSDT、XRPUSDT仓位且有XRPUSDT挂单，后开启usdt enabled参数，列表配置BTCUSDT，设置报错检查错误码
3. 撤销XRPUSDT订单，开启usdt enabled参数，列表中配置BTCUSDT，设置成功后，检查BTCUSDT及XRPUSDT开平仓回报
4. 造BTC交割和ETH交割仓位，开启交割enabled参数，列表中配置BTC，检查BTC交割和ETH价格开平仓回报
5. 所有enabled参数全部关闭，检查开仓回报
6. Clear_all参数为true，检查配置清理情况
*/
TEST_F(TestCustomerSymbollist, TestSetSymbolList) {
  biz::user_id_t uid = 100000;
  biz::user_id_t uid1 = 10001;
  stub user(te, uid);
  stub user1(te, uid1);
  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp = user.process(deposit_build.Build());
    auto result = te->PopResult();

    FutureDepositBuilder deposit_build1(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build1.Build());
    auto result1 = te->PopResult();
  }

  // 1. 开启usdt enabled参数，但是列表中无配置，检查usdt合约开仓回报错误码
  std::set<int64_t> usdt_list;
  std::set<int64_t> usdc_list;
  std::set<std::string> usdc_future_list;
  {
    FutureSetSymbolListBuilder set_req_build(uid, false, true, usdt_list, false, usdt_list, false, usdc_future_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto setting_check = result.RefAssetMarginResult().RefRelatedUserSetting();
    setting_check.CheckSymbolList(ECoin::USDT, true, false, 0);
    setting_check.CheckSymbolList(ECoin::USDC, false, false, 0);
  }

  {
    auto order_id = te->GenUUID();
    biz::symbol_t symbol = 5;  // BTCUSDT
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 100000;
    biz::price_x_t price = 20000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, ECoin::USDT);
    auto resp = user.process(create_build.Build());
    ASSERT_EQ(error::kErrorCodeInstLoanNotInWhiteList, resp->ret_code());
  }

  // 2.构造BTCUSDT、XRPUSDT仓位且有XRPUSDT挂单，后开启usdt enabled参数，列表配置BTCUSDT，设置报错检查错误码
  {
    FutureSetSymbolListBuilder set_req_build(uid, false, false, usdt_list, false, usdt_list, false, usdc_future_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto setting_check = result.RefAssetMarginResult().RefRelatedUserSetting();
    setting_check.CheckSymbolList(ECoin::USDT, false, false, 0);
    setting_check.CheckSymbolList(ECoin::USDC, false, false, 0);
  }

  {
    // 构造BTCUSDT仓位
    auto order_id = te->GenUUID();
    biz::symbol_t symbol = 5;  // BTCUSDT
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    ESide opp_side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 100000;
    biz::price_x_t price = 20000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, ECoin::USDT);
    auto resp = user.process(create_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    orderCheck.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    auto order_id1 = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build1(order_id1, symbol, pz_index, opp_side, order_type, qty, price,
                                                ETimeInForce::GoodTillCancel, uid1, ECoin::USDT);
    auto resp1 = user1.process(create_build1.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp1->ret_code());

    // sell方向仓位
    auto pz_sell_order_result = te->PopResult();
    auto user1_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user1_pz_check.CheckSize(qty);
    user1_pz_check.CheckFreeSize(qty);
    //  buy方向仓位
    auto pz_buy_order_result = te->PopResult();
    auto user_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user_pz_check.CheckSize(qty);
    user_pz_check.CheckFreeSize(qty);
  }

  auto order_id = te->GenUUID();
  {
    // 构造XRPUSDT仓位，同时预留挂单
    biz::symbol_t symbol = 8;  // XRPUSDT
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    ESide opp_side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 1 * 1e8;
    biz::price_x_t price = 1000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 1000, 1000, 1000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty * 2, price,
                                               ETimeInForce::GoodTillCancel, uid, ECoin::USDT);
    auto resp = user.process(create_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    orderCheck.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    auto order_id1 = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build1(order_id1, symbol, pz_index, opp_side, order_type, qty, price,
                                                ETimeInForce::GoodTillCancel, uid1, ECoin::USDT);
    auto resp1 = user1.process(create_build1.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp1->ret_code());

    // sell方向仓位
    auto pz_sell_order_result = te->PopResult();
    auto user1_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user1_pz_check.CheckSize(qty);
    user1_pz_check.CheckFreeSize(qty);
    // buy方向仓位
    auto pz_buy_order_result = te->PopResult();
    auto user_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user_pz_check.CheckSize(qty);
    user_pz_check.CheckFreeSize(qty);
  }

  {
    usdt_list.insert(5);
    FutureSetSymbolListBuilder set_req_build(uid, false, true, usdt_list, false, usdt_list, false, usdc_future_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    te->PopResult();
  }

  // 3. 撤销XRPUSDT订单，开启usdt enabled参数，列表中配置BTCUSDT，设置成功后，检查BTCUSDT及XRPUSDT开平仓回报
  {
    FutureOneOfCancelOrderBuilder cancel_build(order_id, 8, uid, ECoin::USDT);
    auto resp = user.process(cancel_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    orderCheck.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);

    FutureSetSymbolListBuilder set_req_build(uid, false, true, usdt_list, false, usdt_list, false, usdc_future_list);
    auto resp1 = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp1->ret_code());
    auto result1 = te->PopResult();
    auto setting_check = result1.RefAssetMarginResult().RefRelatedUserSetting();
    setting_check.CheckSymbolList(ECoin::USDT, true, false, 0);
    setting_check.CheckSymbolList(ECoin::USDC, false, false, 0);
  }

  {
    // 挂平仓单
    auto order_id1 = te->GenUUID();
    biz::symbol_t symbol = 5;  // BTCUSDT
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    ESide opp_side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 100000;
    biz::price_x_t price = 20000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id1, symbol, pz_index, opp_side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, ECoin::USDT);
    create_build.msg.mutable_create_order()->set_reduce_only(true);
    auto resp = user.process(create_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id1);
    orderCheck.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    auto order_id2 = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build1(order_id2, symbol, pz_index, side, order_type, qty, price,
                                                ETimeInForce::GoodTillCancel, uid1, ECoin::USDT);
    auto resp1 = user1.process(create_build1.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp1->ret_code());

    // sell方向仓位
    auto pz_sell_order_result = te->PopResult();
    auto user1_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user1_pz_check.CheckSize(0);
    //  buy方向仓位
    auto pz_buy_order_result = te->PopResult();
    auto user_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user_pz_check.CheckSize(0);

    // 挂开仓单
    auto order_id3 = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id3, symbol, pz_index, side, order_type, qty, price,
                                                ETimeInForce::GoodTillCancel, uid, ECoin::USDT);
    auto resp2 = user.process(create_build2.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp2->ret_code());
    auto result2 = te->PopResult();
  }

  {
    // 挂平仓单
    auto order_id1 = te->GenUUID();
    biz::symbol_t symbol = 8;  // XRPUSDT
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    ESide opp_side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 1 * 1e8;
    biz::price_x_t price = 2 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 2, 2, 2);
    FutureOneOfCreateOrderBuilder create_build(order_id1, symbol, pz_index, opp_side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, ECoin::USDT);
    create_build.msg.mutable_create_order()->set_reduce_only(true);
    auto resp = user.process(create_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id1);
    orderCheck.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    auto order_id2 = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build1(order_id2, symbol, pz_index, side, order_type, qty, price,
                                                ETimeInForce::GoodTillCancel, uid1, ECoin::USDT);
    auto resp1 = user1.process(create_build1.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp1->ret_code());

    // sell方向仓位
    auto pz_sell_order_result = te->PopResult();
    auto user1_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user1_pz_check.CheckSize(0);
    // buy方向仓位
    auto pz_buy_order_result = te->PopResult();
    auto user_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user_pz_check.CheckSize(0);

    // 挂开仓单
    auto order_id3 = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id3, symbol, pz_index, side, order_type, qty, price,
                                                ETimeInForce::GoodTillCancel, uid, ECoin::USDT);
    auto resp2 = user.process(create_build2.Build());
    ASSERT_EQ(error::kErrorCodeInstLoanNotInWhiteList, resp2->ret_code());
  }

  // 4.造BTC交割和ETH交割仓位，开启交割enabled参数，列表中配置BTC，检查BTC交割和ETH价格开平仓回
  {
    // 构造BTC-26MAY23仓位
    auto order_id11 = te->GenUUID();
    biz::symbol_t symbol = 284;  // BTC-26MAY23
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    ESide opp_side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 100000;
    biz::price_x_t price = 20000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id11, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, ECoin::USDC);
    auto resp = user.process(create_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id11);
    orderCheck.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    auto order_id1 = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build1(order_id1, symbol, pz_index, opp_side, order_type, qty, price,
                                                ETimeInForce::GoodTillCancel, uid1, ECoin::USDC);
    auto resp1 = user1.process(create_build1.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp1->ret_code());

    // sell方向仓位
    auto pz_sell_order_result = te->PopResult();
    auto user1_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user1_pz_check.CheckSize(qty);
    user1_pz_check.CheckFreeSize(qty);
    //  buy方向仓位
    auto pz_buy_order_result = te->PopResult();
    auto user_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user_pz_check.CheckSize(qty);
    user_pz_check.CheckFreeSize(qty);
  }

  {
    // 构造ETH-26MAY23仓位
    auto order_id11 = te->GenUUID();
    biz::symbol_t symbol = 289;  // ETH-26MAY23
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    ESide opp_side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 100000;
    biz::price_x_t price = 2000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 2000, 2000, 2000);
    FutureOneOfCreateOrderBuilder create_build(order_id11, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, ECoin::USDC);
    auto resp = user.process(create_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id11);
    orderCheck.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    auto order_id1 = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build1(order_id1, symbol, pz_index, opp_side, order_type, qty, price,
                                                ETimeInForce::GoodTillCancel, uid1, ECoin::USDC);
    auto resp1 = user1.process(create_build1.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp1->ret_code());

    // sell方向仓位
    auto pz_sell_order_result = te->PopResult();
    auto user1_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user1_pz_check.CheckSize(qty);
    user1_pz_check.CheckFreeSize(qty);
    //  buy方向仓位
    auto pz_buy_order_result = te->PopResult();
    auto user_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user_pz_check.CheckSize(qty);
    user_pz_check.CheckFreeSize(qty);
  }

  // 设置白名单
  {
    usdc_future_list.insert("BTC");
    FutureSetSymbolListBuilder set_req_build(uid, false, true, usdt_list, false, usdt_list, true, usdc_future_list);
    auto resp1 = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp1->ret_code());
    auto result = te->PopResult();
    auto setting_check = result.RefAssetMarginResult().RefRelatedUserSetting();
    setting_check.CheckSymbolList(ECoin::USDT, true, false, 0);
    setting_check.CheckSymbolList(ECoin::USDC, false, true, 1);
  }

  {
    // 平仓
    auto order_id11 = te->GenUUID();
    biz::symbol_t symbol = 284;  // BTC-26MAY23
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    ESide opp_side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 100000;
    biz::price_x_t price = 20000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id11, symbol, pz_index, opp_side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, ECoin::USDC);
    create_build.msg.mutable_create_order()->set_reduce_only(true);
    auto resp = user.process(create_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id11);
    orderCheck.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    auto order_id1 = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build1(order_id1, symbol, pz_index, side, order_type, qty, price,
                                                ETimeInForce::GoodTillCancel, uid1, ECoin::USDC);
    auto resp1 = user1.process(create_build1.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp1->ret_code());

    // sell方向仓位
    auto pz_sell_order_result = te->PopResult();
    auto user1_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user1_pz_check.CheckSize(0);
    //  buy方向仓位
    auto pz_buy_order_result = te->PopResult();
    auto user_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user_pz_check.CheckSize(0);

    // 开仓
    auto order_id2 = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id2, symbol, pz_index, side, order_type, qty, price,
                                                ETimeInForce::GoodTillCancel, uid, ECoin::USDC);
    auto resp2 = user.process(create_build2.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp2->ret_code());
    auto result2 = te->PopResult();
  }

  {
    // 构造ETH-26MAY23仓位
    auto order_id11 = te->GenUUID();
    biz::symbol_t symbol = 289;  // ETH-26MAY23
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    ESide opp_side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 100000;
    biz::price_x_t price = 2000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 2000, 2000, 2000);
    FutureOneOfCreateOrderBuilder create_build(order_id11, symbol, pz_index, opp_side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, ECoin::USDC);
    create_build.msg.mutable_create_order()->set_reduce_only(true);
    auto resp = user.process(create_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id11);
    orderCheck.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    auto order_id1 = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build1(order_id1, symbol, pz_index, side, order_type, qty, price,
                                                ETimeInForce::GoodTillCancel, uid1, ECoin::USDC);
    auto resp1 = user1.process(create_build1.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp1->ret_code());

    // sell方向仓位
    auto pz_sell_order_result = te->PopResult();
    auto user1_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user1_pz_check.CheckSize(0);
    //  buy方向仓位
    auto pz_buy_order_result = te->PopResult();
    auto user_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user_pz_check.CheckSize(0);

    // 开仓
    auto order_id2 = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id2, symbol, pz_index, side, order_type, qty, price,
                                                ETimeInForce::GoodTillCancel, uid, ECoin::USDC);
    auto resp2 = user.process(create_build2.Build());
    ASSERT_EQ(error::kErrorCodeInstLoanNotInUSDCWhiteList, resp2->ret_code());
  }

  // 5. 所有enabled参数全部关闭，检查开仓回报
  {
    FutureSetSymbolListBuilder set_req_build(uid, false, false, usdt_list, false, usdt_list, false, usdc_future_list);
    auto resp1 = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp1->ret_code());
    auto result = te->PopResult();
    auto setting_check = result.RefAssetMarginResult().RefRelatedUserSetting();
    setting_check.CheckSymbolList(ECoin::USDT, false, false, 0);
    setting_check.CheckSymbolList(ECoin::USDC, false, false, 1);
  }

  {
    auto order_id1 = te->GenUUID();
    biz::symbol_t symbol = 8;  // XRPUSDT
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 1 * 1e8;
    biz::price_x_t price = 1000 * 1e4;

    // 挂开仓单
    te->AddMarkPrice(static_cast<ESymbol>(symbol), 1000, 1000, 1000);
    FutureOneOfCreateOrderBuilder create_build(order_id1, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, ECoin::USDT);
    auto resp2 = user.process(create_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp2->ret_code());
    auto result = te->PopResult();
  }
  {
    auto order_id11 = te->GenUUID();
    biz::symbol_t symbol = 289;  // ETH-26MAY23
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 100000;
    biz::price_x_t price = 2000 * 1e4;
    // 开仓
    te->AddMarkPrice(static_cast<ESymbol>(symbol), 2000, 2000, 2000);
    FutureOneOfCreateOrderBuilder create_build2(order_id11, symbol, pz_index, side, order_type, qty, price,
                                                ETimeInForce::GoodTillCancel, uid, ECoin::USDC);
    auto resp2 = user.process(create_build2.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp2->ret_code());
    auto result = te->PopResult();
  }

  // 6. Clear_all参数为true，检查配置清理情况
  {
    FutureSetSymbolListBuilder set_req_build(uid, true, true, usdt_list, false, usdt_list, false, usdc_future_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto setting_check = result.RefAssetMarginResult().RefRelatedUserSetting();
    setting_check.CheckSymbolList(ECoin::USDT, false, false, 0);
    setting_check.CheckSymbolList(ECoin::USDC, false, false, 0);

    auto inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                           .m_msg->per_user_setting_data()
                                           .future_symbol_setting_dto()
                                           .inst_loan_symbol_white_list();
    ASSERT_EQ(inst_loan_symbol_white_list.size(), 0);
  }
}

// 覆盖增量部分
TEST_F(TestCustomerSymbollist, TestSetSymbolList2) {
  biz::user_id_t uid = 234000;
  stub user(te, uid);

  std::set<int64_t> usdt_list;
  std::set<int64_t> usdc_list;
  std::set<int64_t> support_inverse_perp_symbol_list;
  // 1. 开启usdt enabled参数，但是列表中无配置
  {
    FutureSetSymbolListBuilderV2 set_req_build(uid, false, true, usdt_list, false, usdc_list, false,
                                               support_inverse_perp_symbol_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto const& inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                                  .RefRelatedUserSetting()
                                                  .m_msg->future_symbol_setting_dto()
                                                  .inst_loan_symbol_white_list();
    ASSERT_EQ(inst_loan_symbol_white_list.size(), 0);
  }

  // 2. 开启usdc enabled参数，但是列表中无配置
  {
    FutureSetSymbolListBuilderV2 set_req_build(uid, false, false, usdt_list, true, usdc_list, false,
                                               support_inverse_perp_symbol_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto const& inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                                  .RefRelatedUserSetting()
                                                  .m_msg->future_symbol_setting_dto()
                                                  .inst_loan_symbol_white_list();
    ASSERT_EQ(inst_loan_symbol_white_list.size(), 0);
  }

  // 3. 开启inverse enabled参数，但是列表中无配置
  {
    FutureSetSymbolListBuilderV2 set_req_build(uid, false, false, usdt_list, false, usdc_list, true,
                                               support_inverse_perp_symbol_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto const& inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                                  .RefRelatedUserSetting()
                                                  .m_msg->future_symbol_setting_dto()
                                                  .inst_loan_symbol_white_list();
    ASSERT_EQ(inst_loan_symbol_white_list.size(), 0);
  }
}

TEST_F(TestCustomerSymbollist, TestSetSymbolList3) {
  biz::user_id_t uid = 235000;
  stub user(te, uid);

  std::set<int64_t> usdt_list;
  std::set<int64_t> usdc_list;
  std::set<int64_t> support_inverse_perp_symbol_list;
  // 4.1 分别插入两次USDT的symbol，验证coin一样，symbol会覆盖的情况
  {
    usdt_list.insert(ESymbol::BTCUSDT);
    FutureSetSymbolListBuilderV2 set_req_build(uid, false, true, usdt_list, false, usdc_list, false,
                                               support_inverse_perp_symbol_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto const& inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                                  .RefRelatedUserSetting()
                                                  .m_msg->future_symbol_setting_dto()
                                                  .inst_loan_symbol_white_list();
    ASSERT_EQ(inst_loan_symbol_white_list.size(), 1);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::USDT)->second.values().size(), 1);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::USDT)->second.values().Get(0), ESymbol::BTCUSDT);
  }
  // 4.2 再插入一次，symbol会覆盖
  {
    usdt_list.clear();
    usdt_list.insert(ESymbol::ETHUSDT);
    FutureSetSymbolListBuilderV2 set_req_build(uid, false, true, usdt_list, false, usdc_list, false,
                                               support_inverse_perp_symbol_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto const& inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                                  .RefRelatedUserSetting()
                                                  .m_msg->future_symbol_setting_dto()
                                                  .inst_loan_symbol_white_list();
    ASSERT_EQ(inst_loan_symbol_white_list.size(), 1);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::USDT)->second.values().size(), 1);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::USDT)->second.values().Get(0), ESymbol::ETHUSDT);
  }
}

TEST_F(TestCustomerSymbollist, TestSetSymbolList4) {
  biz::user_id_t uid = 236000;
  stub user(te, uid);

  std::set<int64_t> usdt_list;
  std::set<int64_t> usdc_list;
  std::set<int64_t> support_inverse_perp_symbol_list;

  // 5.1 一起插入两个USDT的symbol
  {
    usdt_list.clear();
    usdt_list.insert(ESymbol::BTCUSDT);
    usdt_list.insert(ESymbol::ETHUSDT);

    FutureSetSymbolListBuilderV2 set_req_build(uid, false, true, usdt_list, false, usdc_list, false,
                                               support_inverse_perp_symbol_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto const& inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                                  .RefRelatedUserSetting()
                                                  .m_msg->future_symbol_setting_dto()
                                                  .inst_loan_symbol_white_list();
    ASSERT_EQ(inst_loan_symbol_white_list.size(), 1);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::USDT)->second.values().size(), 2);
  }

  // 5.2 只插入一个BTCUSDT，那么之前的ETHUSDT也会被覆盖掉了
  {
    usdt_list.clear();
    usdt_list.insert(ESymbol::BTCUSDT);

    FutureSetSymbolListBuilderV2 set_req_build(uid, false, true, usdt_list, false, usdc_list, false,
                                               support_inverse_perp_symbol_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto const& inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                                  .RefRelatedUserSetting()
                                                  .m_msg->future_symbol_setting_dto()
                                                  .inst_loan_symbol_white_list();
    ASSERT_EQ(inst_loan_symbol_white_list.size(), 1);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::USDT)->second.values().size(), 1);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::USDT)->second.values().Get(0), ESymbol::BTCUSDT);
  }

  // 6. clear all
  {
    FutureSetSymbolListBuilderV2 set_req_build(uid, true, false, usdt_list, false, usdc_list, false,
                                               support_inverse_perp_symbol_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto const& inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                                  .RefRelatedUserSetting()
                                                  .m_msg->future_symbol_setting_dto()
                                                  .inst_loan_symbol_white_list();
    ASSERT_TRUE(inst_loan_symbol_white_list.empty());
  }
}

TEST_F(TestCustomerSymbollist, TestSetSymbolList5) {
  biz::user_id_t uid = 237000;
  stub user(te, uid);

  std::set<int64_t> usdt_list;
  std::set<int64_t> usdc_list;
  std::set<int64_t> support_inverse_perp_symbol_list;
  // 7.1 一起插入两个USDC的symbol，会全部下发两个symbol
  {
    usdc_list.clear();
    usdc_list.insert(45);
    usdc_list.insert(230);

    FutureSetSymbolListBuilderV2 set_req_build(uid, false, false, usdt_list, true, usdc_list, false,
                                               support_inverse_perp_symbol_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto const& inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                                  .RefRelatedUserSetting()
                                                  .m_msg->future_symbol_setting_dto()
                                                  .inst_loan_symbol_white_list();
    ASSERT_EQ(inst_loan_symbol_white_list.size(), 1);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::USDC)->second.values().size(), 2);
  }
  // 7.2 usdc只插入45， 230也会被覆盖
  {
    usdc_list.clear();
    usdc_list.insert(45);
    FutureSetSymbolListBuilderV2 set_req_build(uid, false, false, usdt_list, true, usdc_list, false,
                                               support_inverse_perp_symbol_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto const& inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                                  .RefRelatedUserSetting()
                                                  .m_msg->future_symbol_setting_dto()
                                                  .inst_loan_symbol_white_list();
    ASSERT_EQ(inst_loan_symbol_white_list.size(), 1);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::USDC)->second.values().size(), 1);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::USDC)->second.values().Get(0), 45);
  }

  // 8. clear all
  {
    FutureSetSymbolListBuilderV2 set_req_build(uid, true, false, usdt_list, false, usdc_list, false,
                                               support_inverse_perp_symbol_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto const& inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                                  .RefRelatedUserSetting()
                                                  .m_msg->future_symbol_setting_dto()
                                                  .inst_loan_symbol_white_list();
    ASSERT_TRUE(inst_loan_symbol_white_list.empty());
  }
}

TEST_F(TestCustomerSymbollist, TestSetSymbolList6) {
  biz::user_id_t uid = 238000;
  stub user(te, uid);

  std::set<int64_t> usdt_list;
  std::set<int64_t> usdc_list;
  std::set<int64_t> support_inverse_perp_symbol_list;
  // 9.1 反向的插入两个
  {
    support_inverse_perp_symbol_list.clear();
    support_inverse_perp_symbol_list.insert(ESymbol::BTCUSD);
    support_inverse_perp_symbol_list.insert(ESymbol::ETHUSD);

    FutureSetSymbolListBuilderV2 set_req_build(uid, false, false, usdt_list, false, usdc_list, true,
                                               support_inverse_perp_symbol_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto const& inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                                  .RefRelatedUserSetting()
                                                  .m_msg->future_symbol_setting_dto()
                                                  .inst_loan_symbol_white_list();
    ASSERT_EQ(inst_loan_symbol_white_list.size(), 2);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::BTC)->second.values().Get(0), 1);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::ETH)->second.values().Get(0), 2);
  }

  // 9.2 反向再插入一次，结果跟上一次一样
  {
    support_inverse_perp_symbol_list.clear();
    support_inverse_perp_symbol_list.insert(ESymbol::BTCUSD);
    support_inverse_perp_symbol_list.insert(ESymbol::ETHUSD);

    FutureSetSymbolListBuilderV2 set_req_build(uid, false, false, usdt_list, false, usdc_list, true,
                                               support_inverse_perp_symbol_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto const& inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                                  .RefRelatedUserSetting()
                                                  .m_msg->future_symbol_setting_dto()
                                                  .inst_loan_symbol_white_list();
    ASSERT_EQ(inst_loan_symbol_white_list.size(), 2);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::BTC)->second.values().Get(0), 1);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::ETH)->second.values().Get(0), 2);
  }
  // 10. 反向只插入一个BTCUSD，ETHUSD的会被清空掉
  {
    support_inverse_perp_symbol_list.clear();
    support_inverse_perp_symbol_list.insert(ESymbol::BTCUSD);

    FutureSetSymbolListBuilderV2 set_req_build(uid, false, false, usdt_list, false, usdc_list, true,
                                               support_inverse_perp_symbol_list);
    auto resp = user.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    auto const& inst_loan_symbol_white_list = result.RefAssetMarginResult()
                                                  .RefRelatedUserSetting()
                                                  .m_msg->future_symbol_setting_dto()
                                                  .inst_loan_symbol_white_list();
    ASSERT_EQ(inst_loan_symbol_white_list.size(), 1);
    ASSERT_EQ(inst_loan_symbol_white_list.find(ECoin::BTC)->second.values().Get(0), 1);
    ASSERT_TRUE(inst_loan_symbol_white_list.find(ECoin::ETH) == inst_loan_symbol_white_list.end());
  }
}

std::shared_ptr<tmock::CTradeAppMock> TestCustomerSymbollist::te;
