#include "lib/msg_builder.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/main.hpp"
class TestInstitutionLoanBanTest : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  void BuildPosition(biz::user_id_t uid_2, biz::user_id_t uid_1, stub& user1, stub& user2);
  static void SetUpTestSuite() {}
  static void TearDownTestSuite() {}

  static std::shared_ptr<tmock::CTradeAppMock> te;
};

/*
1.开仓封禁，平仓正常，开仓拒绝
2.开仓解禁，开平仓都正常
3.开平仓封禁，开平仓都拒绝
4.开平仓解禁，开平仓都正常
5.交易封禁，调杠杆、设置风险限额、下单一键平仓等接口全部拒绝
6.交易解禁，全部调用正常
*/

TEST_F(TestInstitutionLoanBanTest, TestOpenBan) {
  // user1
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  // opposite user2
  biz::user_id_t const uid_2 = 658002;
  stub user2(te, uid_2);

  // #1.开仓封禁，平仓正常，开仓拒绝

  // 构建仓位
  BuildPosition(uid_1, uid_2, user1, user2);
  {
    // 设置封禁
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid_1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstBanOrUnban);
    auto* setting = unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_instbanorunbanreq();
    setting->set_future_usdt_open_disabled(true);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid_1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "设置instLiq:" << jsonStr2 << std::endl;
  }

  {
    // 封禁后不允许开仓，允许平仓
    auto user1_pz_buy_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder pz_buy_order_build(user1_pz_buy_id, 5, EPositionIndex::Single, ESide::Sell,
                                                     EOrderType::Limit, 100 * 1e6, 10000 * 1e4,
                                                     ETimeInForce::GoodTillCancel, uid_1, 5);
    auto pz_buy_order_resp = user1.process(pz_buy_order_build.Build());
    ASSERT_EQ(error::kErrorCodeMarginNotAllowOpen, pz_buy_order_resp->ret_code());

    pz_buy_order_build.msg.mutable_create_order()->set_reduce_only(true);
    pz_buy_order_resp = user1.process(pz_buy_order_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, pz_buy_order_resp->ret_code());
  }

  // #2. 开仓解禁，开平仓都正常
  {
    // 解禁
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid_1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstBanOrUnban);
    auto* setting = unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_instbanorunbanreq();
    setting->set_future_usdt_open_disabled(false);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid_1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "设置instLiq:" << jsonStr2 << std::endl;
  }

  {
    // 平仓正常
    auto user1_pz_buy_id2 = te->GenUUID();
    FutureOneOfCreateOrderBuilder pz_buy_order_build2(user1_pz_buy_id2, 5, EPositionIndex::Single, ESide::Sell,
                                                      EOrderType::Limit, 100 * 1e6, 9999 * 1e4,
                                                      ETimeInForce::GoodTillCancel, uid_1, 5);
    pz_buy_order_build2.msg.mutable_create_order()->set_reduce_only(true);
    auto pz_buy_order_resp2 = user1.process(pz_buy_order_build2.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, pz_buy_order_resp2->ret_code());

    // 开仓正常
    auto user1_pz_buy_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder pz_buy_order_build(user1_pz_buy_id, 5, EPositionIndex::Single, ESide::Sell,
                                                     EOrderType::Limit, 100 * 1e6, 10000 * 1e4,
                                                     ETimeInForce::GoodTillCancel, uid_1, 5);
    auto pz_buy_order_resp = user1.process(pz_buy_order_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, pz_buy_order_resp->ret_code());
  }
}

TEST_F(TestInstitutionLoanBanTest, TestBanTradeDisable) {
  // user1
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  // opposite user2
  biz::user_id_t const uid_2 = 658002;
  stub user2(te, uid_2);

  // #3. 开平仓封禁，开平仓都拒绝
  BuildPosition(uid_1, uid_2, user1, user2);
  {
    // 设置封禁
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid_1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstBanOrUnban);
    auto* setting = unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_instbanorunbanreq();
    setting->set_future_usdt_trade_disabled(true);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid_1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "设置instLiq:" << jsonStr2 << std::endl;
  }

  {
    // 封禁后不允许开仓，不允许平仓
    auto user1_pz_buy_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder pz_buy_order_build(user1_pz_buy_id, 5, EPositionIndex::Single, ESide::Sell,
                                                     EOrderType::Limit, 100 * 1e6, 10000 * 1e4,
                                                     ETimeInForce::GoodTillCancel, uid_1, 5);
    auto pz_buy_order_resp = user1.process(pz_buy_order_build.Build());
    ASSERT_EQ(error::kErrorCodeMarginNotAllowTrade, pz_buy_order_resp->ret_code());

    pz_buy_order_build.msg.mutable_create_order()->set_reduce_only(true);
    pz_buy_order_resp = user1.process(pz_buy_order_build.Build());
    ASSERT_EQ(error::kErrorCodeMarginNotAllowTrade, pz_buy_order_resp->ret_code());
  }

  // #4. 开平仓解禁，开平仓都正常
  {
    // 解禁
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid_1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstBanOrUnban);
    auto* setting = unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_instbanorunbanreq();
    setting->set_future_usdt_trade_disabled(false);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid_1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "设置instLiq:" << jsonStr2 << std::endl;
  }

  {
    // 平仓正常
    auto user1_pz_buy_id2 = te->GenUUID();
    FutureOneOfCreateOrderBuilder pz_buy_order_build2(user1_pz_buy_id2, 5, EPositionIndex::Single, ESide::Sell,
                                                      EOrderType::Limit, 100 * 1e6, 9999 * 1e4,
                                                      ETimeInForce::GoodTillCancel, uid_1, 5);
    pz_buy_order_build2.msg.mutable_create_order()->set_reduce_only(true);
    auto pz_buy_order_resp2 = user1.process(pz_buy_order_build2.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, pz_buy_order_resp2->ret_code());

    // 开仓正常
    auto user1_pz_buy_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder pz_buy_order_build(user1_pz_buy_id, 5, EPositionIndex::Single, ESide::Sell,
                                                     EOrderType::Limit, 100 * 1e6, 10000 * 1e4,
                                                     ETimeInForce::GoodTillCancel, uid_1, 5);
    auto pz_buy_order_resp = user1.process(pz_buy_order_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, pz_buy_order_resp->ret_code());
  }
}

TEST_F(TestInstitutionLoanBanTest, TestBanALLTradeDisable) {
  // user1
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  // opposite user2
  biz::user_id_t const uid_2 = 658002;
  stub user2(te, uid_2);

  // #5. 交易封禁，调杠杆、设置风险限额、下单一键平仓等接口全部拒绝
  BuildPosition(uid_1, uid_2, user1, user2);
  {
    // 设置封禁
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid_1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstBanOrUnban);
    auto* setting = unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_instbanorunbanreq();
    setting->set_all_trade_disabled(true);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid_1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "设置instLiq:" << jsonStr2 << std::endl;
  }

  {
    // 封禁后不允许开平仓
    auto user1_pz_buy_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder pz_buy_order_build(user1_pz_buy_id, 5, EPositionIndex::Single, ESide::Sell,
                                                     EOrderType::Limit, 100 * 1e6, 10000 * 1e4,
                                                     ETimeInForce::GoodTillCancel, uid_1, 5);
    auto pz_buy_order_resp = user1.process(pz_buy_order_build.Build());
    ASSERT_EQ(error::kErrorCodeInstLiqIng, pz_buy_order_resp->ret_code());

    pz_buy_order_build.msg.mutable_create_order()->set_reduce_only(true);
    pz_buy_order_resp = user1.process(pz_buy_order_build.Build());
    ASSERT_EQ(error::kErrorCodeInstLiqIng, pz_buy_order_resp->ret_code());
  }

  {
    // 不允许调杠杆
    FutureOneOfSetLeverageBuilder set_lv_build(5, 5, uid_1, 1500, 1500);
    auto resp = user1.process(set_lv_build.Build());
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeInstLiqIng);
  }

  {
    // 不允许调风险限额
    FutureOneOfSetRiskIdBuilder set_risk_id_build(5, 5, uid_1, 2);
    auto resp = user1.process(set_risk_id_build.Build());
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeInstLiqIng);
  }

  {
    // 不允许一键平仓
    CloseAllPositionReqBuilder close_req_unknown(0, uid_1, 5);
    auto resp = user1.process(close_req_unknown.Build());
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeInstLiqIng);
  }

  {
    // 不允许一键平仓
    CloseAllPositionReqBuilder close_req_unknown(0, uid_1, 5);
    auto resp = user1.process(close_req_unknown.Build());
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeInstLiqIng);
  }

  // #6. 交易解禁，全部调用正常
  {
    // 解禁
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid_1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstBanOrUnban);
    auto* setting = unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_instbanorunbanreq();
    setting->set_all_trade_disabled(false);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid_1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "设置instLiq:" << jsonStr2 << std::endl;
  }

  {
    // 平仓正常
    auto user1_pz_buy_id2 = te->GenUUID();
    FutureOneOfCreateOrderBuilder pz_buy_order_build2(user1_pz_buy_id2, 5, EPositionIndex::Single, ESide::Sell,
                                                      EOrderType::Limit, 100 * 1e6, 9999 * 1e4,
                                                      ETimeInForce::GoodTillCancel, uid_1, 5);
    pz_buy_order_build2.msg.mutable_create_order()->set_reduce_only(true);
    auto pz_buy_order_resp2 = user1.process(pz_buy_order_build2.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, pz_buy_order_resp2->ret_code());

    // 开仓正常
    auto user1_pz_buy_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder pz_buy_order_build(user1_pz_buy_id, 5, EPositionIndex::Single, ESide::Sell,
                                                     EOrderType::Limit, 100 * 1e6, 10000 * 1e4,
                                                     ETimeInForce::GoodTillCancel, uid_1, 5);
    auto pz_buy_order_resp = user1.process(pz_buy_order_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, pz_buy_order_resp->ret_code());
  }
}

void TestInstitutionLoanBanTest::BuildPosition(biz::user_id_t uid_1, biz::user_id_t uid_2, stub& user1, stub& user2) {
  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin2 = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid_1, trans_id, coin2, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin2 = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid_2, trans_id, coin2, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  biz::coin_t const coin = 5;      // BTC
  biz::symbol_t const symbol = 5;  // BTCUSDT
  EPositionIndex const pz_index = EPositionIndex::Single;
  EOrderType const order_type = EOrderType::Limit;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    te->AddMarkPrice(static_cast<ESymbol>(symbol), 10000, 10000, 10000);
    // user1: create position 100qty
    biz::size_x_t const pz_qty = 100 * 1e6;
    biz::price_x_t const pz_price = 10000 * 1e4;

    // case准备 -- 构建持仓 100qty
    // ============================================================================================================
    // user1 买入开仓订单 100qty
    auto user1_pz_buy_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder pz_buy_order_build(user1_pz_buy_id, symbol, pz_index, ESide::Buy, order_type, pz_qty,
                                                     pz_price, time_in_force, uid_1, coin);
    auto pz_buy_order_resp = user1.process(pz_buy_order_build.Build());

    // 收user1 buy单result
    auto pz_buy_order_result = te->PopResult();
    ASSERT_NE(pz_buy_order_result.m_msg.get(), nullptr);
    auto pz_buy_order_check = pz_buy_order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(user1_pz_buy_id);
    ASSERT_NE(pz_buy_order_check.m_msg, nullptr);
    pz_buy_order_check.CheckQty(uid_1, pz_qty);
    pz_buy_order_check.CheckPrice(uid_1, pz_price);
    pz_buy_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // user2 卖出开仓订单 100qty（主要为了给user1建立持仓）
    auto user2_pz_sell_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder pz_sell_order_build(user2_pz_sell_id, symbol, pz_index, ESide::Sell, order_type,
                                                      pz_qty, pz_price, time_in_force, uid_2, coin);
    auto pz_sell_order_resp = user2.process(pz_sell_order_build.Build());
    ASSERT_EQ(pz_sell_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto pz_sell_order_result = te->PopResult();
    ASSERT_NE(pz_sell_order_result.m_msg.get(), nullptr);
    auto pz_sell_order_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(user2_pz_sell_id);
    ASSERT_NE(pz_sell_order_check.m_msg, nullptr);
    pz_sell_order_check.CheckQty(uid_2, pz_qty);
    pz_sell_order_check.CheckPrice(uid_2, pz_price);
    pz_sell_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 检查user2 持仓
    auto user2_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user2_pz_check.CheckSize(pz_qty);
    user2_pz_check.CheckFreeSize(pz_qty);

    // 收user1 buy单成交回报
    pz_buy_order_result = te->PopResult();
    ASSERT_NE(pz_buy_order_result.m_msg.get(), nullptr);
    // 检查user1 持仓
    auto user1_pz_check = pz_buy_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    user1_pz_check.CheckSize(pz_qty);
  }
}

std::shared_ptr<tmock::CTradeAppMock> TestInstitutionLoanBanTest::te;
