#include <bbase/common/nacos_client/nacos_client_impl.hpp>
#include <iostream>
#include <memory>

#include "lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/main.hpp"
#include "test/biz/trade/mocks/trading_app_mock.hpp"
class TestQueryUserSetting : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestSuite() {}
  static void TearDownTestSuite() {}

  static std::shared_ptr<tmock::CTradeAppMock> te;
};

TEST_F(TestQueryUserSetting, test_query) {
  biz::user_id_t uid = 100000;
  stub user(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  biz::coin_t coin = ECoin::USDT;
  ESymbol symbol = ESymbol::BTCUSDT;
  biz::fee_rate_e8_t tkfr_value = 30000;
  std::string tkfr_desc = "new_taker_fee_rate_desc";
  biz::fee_rate_e8_t mkfr_value = 15000;
  std::string mkfr_desc = "new_maker_fee_rate_desc";

  // case1: 没有设置过, 初次设置symbol维度的taker fee rate和maker fee rate.
  FutureSetSingleFeeRateBuilder set_req_build(coin, uid, symbol, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  auto resp = user.process(set_req_build.Build());
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).taker_fee_rate_e8(),
            tkfr_value);
  ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).maker_fee_rate_e8(),
            mkfr_value);
  ASSERT_STREQ(
      ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).taker_fee_rate_desc().c_str(),
      tkfr_desc.c_str());
  ASSERT_STREQ(
      ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).maker_fee_rate_desc().c_str(),
      mkfr_desc.c_str());
  EXPECT_EQ(ret_setting.setting_version(), 2);

  {
    FutureQueryUserSettingBuilder query_setting_build(coin, uid);
    resp = user.process(query_setting_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result2 = resp->result();
    auto per_symbol_feerate = result2.user_setting().per_symbol_fee_rate().find(symbol);
    ASSERT_NE(per_symbol_feerate, result2.user_setting().per_symbol_fee_rate().end());
    ASSERT_EQ(per_symbol_feerate->second.taker_fee_rate_e8(), tkfr_value);
    ASSERT_EQ(per_symbol_feerate->second.maker_fee_rate_e8(), mkfr_value);
  }
}

std::shared_ptr<tmock::CTradeAppMock> TestQueryUserSetting::te;
