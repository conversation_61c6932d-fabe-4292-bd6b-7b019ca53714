#include "lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/main.hpp"
class TestCustomerSymbolMaxValue : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestSuite() {}
  static void TearDownTestSuite() {}

  static std::shared_ptr<tmock::CTradeAppMock> te;
};

std::shared_ptr<tmock::CTradeAppMock> TestCustomerSymbolMaxValue::te;

TEST_F(TestCustomerSymbolMaxValue, SetCustomerSymbolMaxValue) {
  biz::user_id_t uid = 100000;
  biz::user_id_t uid1 = 10001;
  stub user(te, uid);
  stub user1(te, uid1);
  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp = user.process(deposit_build.Build());
    auto result = te->PopResult();

    FutureDepositBuilder deposit_build1(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build1.Build());
    auto result1 = te->PopResult();
  }

  {
    std::map<int64_t, int64_t> symbol_max_value_map;
    symbol_max_value_map[45] = 10;  // BTC-26MAY23

    FutureOneOfSetSymbolMaxValueBuilder setSymbolMaxValueBuilder(uid, false, symbol_max_value_map);
    auto resp = user.process(setSymbolMaxValueBuilder.Build());
    auto result = te->PopResult();
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result1 = te->PopResult();
    //    auto setting_check = result1.RefAssetMarginResult().RefRelatedUserSetting();
    //    setting_check.CheckSymbolMaxSymbolValue(1);
  }

  {
    // 构造BTCPERP挂单
    auto order_id = te->GenUUID();
    biz::symbol_t symbol = 45;  // BTCPERP
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 100000;
    biz::price_x_t price = 20000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, ECoin::USDC);
    auto resp = user.process(create_build.Build());
    ASSERT_EQ(error::kErrorCodeOrderValueMaxLimit, resp->ret_code());
  }

  {
    std::map<int64_t, int64_t> symbol_max_value_map;
    symbol_max_value_map[45] = 1000;  // BTC-26MAY23

    FutureOneOfSetSymbolMaxValueBuilder setSymbolMaxValueBuilder(uid, true, symbol_max_value_map);
    auto resp = user.process(setSymbolMaxValueBuilder.Build());
    auto result = te->PopResult();
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result1 = te->PopResult();
    //    auto setting_check = result1.RefAssetMarginResult().RefRelatedUserSetting();
    //    setting_check.CheckSymbolMaxSymbolValue(0);
  }

  {
    // 构造BTCPERP挂单
    auto order_id = te->GenUUID();
    biz::symbol_t symbol = 45;  // BTCPERP
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 100000;
    biz::price_x_t price = 20000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, ECoin::USDC);
    auto resp = user.process(create_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  }
}
