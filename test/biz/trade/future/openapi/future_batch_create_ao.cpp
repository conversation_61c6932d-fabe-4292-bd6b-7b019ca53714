#include "test/biz/trade/future/openapi/future_batch_create_ao.hpp"

#include <list>
#include <string>
#include <vector>

#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(FutureBatchCreateAoTest, f_create_order_open_api) {
  biz::symbol_t symbol_id = 5;  // BTCUSDT

  biz::user_id_t uid = 39632634;
  stub user1(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol_id), 20000, 20000, 20000);

  std::string category = "linear";
  std::string symbol = "BTCUSDT";
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "0.1";
  std::string price = "20000";
  std::string time_in_force = "GTC";

  auto items_count = 2;

  std::list<svc::uta_engine::req::BatchCreateOrderReqV5Item> list;

  for (int i = 0; i < items_count; i++) {
    svc::uta_engine::req::BatchCreateOrderReqV5Item batchCreateOrderReqV5Item;
    batchCreateOrderReqV5Item.set_symbol(symbol);
    batchCreateOrderReqV5Item.set_order_link_id("link-" + std::to_string(i));
    batchCreateOrderReqV5Item.set_price(price);
    batchCreateOrderReqV5Item.set_qty(qty);
    batchCreateOrderReqV5Item.set_side(side);
    batchCreateOrderReqV5Item.set_order_type(order_type);
    batchCreateOrderReqV5Item.set_time_in_force(time_in_force);

    list.emplace_back(batchCreateOrderReqV5Item);
  }

  svc::uta_engine::req::BatchCreateOrderReqV5 batchCreateOrderReqV5;
  batchCreateOrderReqV5.set_category(category);
  batchCreateOrderReqV5.mutable_request()->Add(list.begin(), list.end());

  RpcContext ct{};
  auto resp = user1.batch_create_order(batchCreateOrderReqV5, ct);
  ASSERT_NE(resp.get(), nullptr);

  auto ret_info_vec = ct.RetBatchResp();
  int idx = 0;
  for (const auto& ret_info : ret_info_vec) {
    std::cout << "Idx: " << idx << ", RetCode: " << ret_info.code() << ", RetMsg: " << ret_info.msg() << std::endl;
    if (idx == 0) {
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeSuccess);
    }
    if (idx == 1) {
      //  下单成功
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeSuccess);

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);

      std::string msg{};
      (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
      std::cout << fmt::format("Margin result {}:", idx).data() << msg << std::endl;

      auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId("link-" + std::to_string(idx - 1));
      ASSERT_NE(order_check.m_msg, nullptr);

      order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    }
    if (idx == 2) {
      //  下单成功
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeSuccess);

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);

      std::string msg{};
      (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
      std::cout << fmt::format("Margin result {}:", idx).data() << msg << std::endl;

      auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId("link-" + std::to_string(idx - 1));
      ASSERT_NE(order_check.m_msg, nullptr);

      order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    }
    idx++;
  }

  std::cout << "RetMetaData: " << ct.DebugMetaData() << std::endl;

  std::string resp_msg{};
  std::cout << "Resp:" << utils::PbMessageToJsonString(*resp, &resp_msg) << std::endl;
}

TEST_F(FutureBatchCreateAoTest, f_create_order_open_api_symbol_null) {
  biz::symbol_t symbol_id = 5;  // BTCUSDT

  biz::user_id_t uid = 2547657;
  stub user1(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol_id), 20000, 20000, 20000);

  std::string category = "linear";
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "0.1";
  std::string price = "20000";
  std::string time_in_force = "GTC";

  std::list<svc::uta_engine::req::BatchCreateOrderReqV5Item> list;

  svc::uta_engine::req::BatchCreateOrderReqV5Item batchCreateOrderReqV5Item;
  batchCreateOrderReqV5Item.set_order_link_id("db704374-55cc-4d17-8996-6e641c6b342e");
  batchCreateOrderReqV5Item.set_price(price);
  batchCreateOrderReqV5Item.set_qty(qty);
  batchCreateOrderReqV5Item.set_side(side);
  batchCreateOrderReqV5Item.set_order_type(order_type);
  batchCreateOrderReqV5Item.set_time_in_force(time_in_force);

  list.emplace_back(batchCreateOrderReqV5Item);

  svc::uta_engine::req::BatchCreateOrderReqV5 batchCreateOrderReqV5;
  batchCreateOrderReqV5.set_category(category);
  batchCreateOrderReqV5.mutable_request()->Add(list.begin(), list.end());

  RpcContext ct{};
  auto resp = user1.batch_create_order(batchCreateOrderReqV5, ct);
  ASSERT_NE(resp.get(), nullptr);

  std::string msg{};
  std::cout << "Resp:" << utils::PbMessageToJsonString(*resp, &msg) << std::endl;
  std::cout << "RetMetaData: " << ct.DebugMetaData() << std::endl;

  auto ret_info_vec = ct.RetBatchResp();
  int idx = 0;
  for (const auto& ret_info : ret_info_vec) {
    std::cout << "Idx: " << idx << ", RetCode: " << ret_info.code() << ", RetMsg: " << ret_info.msg() << std::endl;
    if (idx == 0) {
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeSuccess);
    }
    if (idx == 1) {
      // 参数错误kErrorCodeParamsError
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeParamsError);
    }
    idx++;
  }
}

TEST_F(FutureBatchCreateAoTest, f_create_order_open_api_reduce_only) {
  biz::symbol_t symbol_id = 5;  // BTCUSDT

  biz::user_id_t uid = 3453522;
  stub user1(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol_id), 20000, 20000, 20000);

  std::string category = "linear";
  std::string symbol = "BTCUSDT";
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "0.1";
  std::string price = "20000";
  std::string time_in_force = "GTC";

  auto order_link_id_1 = "0c61b301-51b0-486e-a0bd-fadf2dfa9cc5";
  std::list<svc::uta_engine::req::BatchCreateOrderReqV5Item> list;
  svc::uta_engine::req::BatchCreateOrderReqV5Item batchCreateOrderReqV5Item;
  batchCreateOrderReqV5Item.set_symbol(symbol);
  batchCreateOrderReqV5Item.set_order_link_id(order_link_id_1);
  batchCreateOrderReqV5Item.set_price(price);
  batchCreateOrderReqV5Item.set_qty(qty);
  batchCreateOrderReqV5Item.set_side(side);
  batchCreateOrderReqV5Item.set_order_type(order_type);
  batchCreateOrderReqV5Item.set_time_in_force(time_in_force);
  batchCreateOrderReqV5Item.set_reduce_only(true);

  list.emplace_back(batchCreateOrderReqV5Item);

  auto order_link_id_2 = "aec95b04-67e1-4ab0-a0f9-143a3458d4dd";
  svc::uta_engine::req::BatchCreateOrderReqV5Item batchCreateOrderReqV5Item1;
  batchCreateOrderReqV5Item1.set_symbol(symbol);
  batchCreateOrderReqV5Item1.set_order_link_id(order_link_id_2);
  batchCreateOrderReqV5Item1.set_price(price);
  batchCreateOrderReqV5Item1.set_qty(qty);
  batchCreateOrderReqV5Item1.set_side(side);
  batchCreateOrderReqV5Item1.set_order_type(order_type);
  batchCreateOrderReqV5Item1.set_time_in_force(time_in_force);
  batchCreateOrderReqV5Item1.set_reduce_only(true);

  list.emplace_back(batchCreateOrderReqV5Item1);

  svc::uta_engine::req::BatchCreateOrderReqV5 batchCreateOrderReqV5;
  batchCreateOrderReqV5.set_category(category);
  batchCreateOrderReqV5.mutable_request()->Add(list.begin(), list.end());

  auto resp = user1.batch_create_order(batchCreateOrderReqV5);
  ASSERT_NE(resp.get(), nullptr);
}

TEST_F(FutureBatchCreateAoTest, f_create_order_open_api_limit_20) {
  biz::symbol_t symbol_id = 5;  // BTCUSDT

  biz::user_id_t uid = 56767855;
  stub user1(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol_id), 20000, 20000, 20000);

  std::string category = "linear";
  std::string symbol = "BTCUSDT";
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "0.1";
  std::string price = "20000";
  std::string time_in_force = "GTC";

  std::list<svc::uta_engine::req::BatchCreateOrderReqV5Item> list;

  for (int i = 0; i <= 20; i++) {
    svc::uta_engine::req::BatchCreateOrderReqV5Item batchCreateOrderReqV5Item;
    batchCreateOrderReqV5Item.set_symbol(symbol);
    batchCreateOrderReqV5Item.set_order_link_id(te->GenUUID());
    batchCreateOrderReqV5Item.set_price(price);
    batchCreateOrderReqV5Item.set_qty(qty);
    batchCreateOrderReqV5Item.set_side(side);
    batchCreateOrderReqV5Item.set_order_type(order_type);
    batchCreateOrderReqV5Item.set_time_in_force(time_in_force);

    list.emplace_back(batchCreateOrderReqV5Item);
  }

  RpcContext ct{};
  svc::uta_engine::req::BatchCreateOrderReqV5 batchCreateOrderReqV5;
  batchCreateOrderReqV5.set_category(category);
  batchCreateOrderReqV5.mutable_request()->Add(list.begin(), list.end());

  auto resp = user1.batch_create_order(batchCreateOrderReqV5, ct);
  ASSERT_NE(resp.get(), nullptr);
  ASSERT_EQ(ct.RetCode(), error::ErrorCode::kEcRequestCountLimit);

  //  std::cout << "RetMetaData: " << ct.DebugMetaData() << std::endl;
  //  std::string msg{};
  //  std::cout << "Resp:" << utils::PbMessageToJsonString(*resp, &msg) << std::endl;
}

TEST_F(FutureBatchCreateAoTest, f_create_order_open_api_uae_limit) {
  biz::symbol_t symbol_id = 5;  // BTCUSDT

  biz::user_id_t uid = 99881221;
  stub user1(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol_id), 20000, 20000, 20000);

  std::string category = "linear";
  std::string symbol = "BTCUSDT";
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "0.1";
  std::string price = "20000";
  std::string time_in_force = "GTC";

  auto order_link_id_1 = "ec893237-f1be-464c-9ac4-2c24112ec4c1";
  std::list<svc::uta_engine::req::BatchCreateOrderReqV5Item> list;
  svc::uta_engine::req::BatchCreateOrderReqV5Item batchCreateOrderReqV5Item;
  batchCreateOrderReqV5Item.set_symbol(symbol);
  batchCreateOrderReqV5Item.set_order_link_id(order_link_id_1);
  batchCreateOrderReqV5Item.set_price(price);
  batchCreateOrderReqV5Item.set_qty(qty);
  batchCreateOrderReqV5Item.set_side(side);
  batchCreateOrderReqV5Item.set_order_type(order_type);
  batchCreateOrderReqV5Item.set_time_in_force(time_in_force);

  list.emplace_back(batchCreateOrderReqV5Item);

  svc::uta_engine::req::BatchCreateOrderReqV5 batchCreateOrderReqV5;
  batchCreateOrderReqV5.set_category(category);
  batchCreateOrderReqV5.mutable_request()->Add(list.begin(), list.end());

  RpcContext ct{};
  ct.ct.AddMetadata("batch_uaesymbol", "{\"BTCUSDT\":1}");
  auto resp = user1.batch_create_order(batchCreateOrderReqV5, ct);
  ASSERT_NE(resp.get(), nullptr);

  std::cout << "RetMetaData: " << ct.DebugMetaData() << std::endl;

  std::string msg{};
  std::cout << "Resp:" << utils::PbMessageToJsonString(*resp, &msg) << std::endl;

  auto ret_info_vec = ct.RetBatchResp();
  int idx = 0;
  for (const auto& ret_info : ret_info_vec) {
    std::cout << "Idx: " << idx << ", RetCode: " << ret_info.code() << ", RetMsg: " << ret_info.msg() << std::endl;
    if (idx == 0) {
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeSuccess);
    }
    if (idx == 1) {
      // 合规墙拦截返回 10001
      ASSERT_EQ(ret_info.code(), 10001);
    }
    idx++;
  }
}

TEST_F(FutureBatchCreateAoTest, f_create_order_open_api_oi_limit) {
  biz::symbol_t symbol_id = 5;  // BTCUSDT

  biz::user_id_t uid = 232113456;
  stub user1(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol_id), 20000, 20000, 20000);

  std::string category = "linear";
  std::string symbol = "BTCUSDT";
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "0.1";
  std::string price = "20000";
  std::string time_in_force = "GTC";

  auto order_link_id_1 = "ec893237-f1be-464c-9ac4-2c24112ec4c1";
  std::list<svc::uta_engine::req::BatchCreateOrderReqV5Item> list;
  svc::uta_engine::req::BatchCreateOrderReqV5Item batchCreateOrderReqV5Item;
  batchCreateOrderReqV5Item.set_symbol(symbol);
  batchCreateOrderReqV5Item.set_order_link_id(order_link_id_1);
  batchCreateOrderReqV5Item.set_price(price);
  batchCreateOrderReqV5Item.set_qty(qty);
  batchCreateOrderReqV5Item.set_side(side);
  batchCreateOrderReqV5Item.set_order_type(order_type);
  batchCreateOrderReqV5Item.set_time_in_force(time_in_force);

  list.emplace_back(batchCreateOrderReqV5Item);

  svc::uta_engine::req::BatchCreateOrderReqV5 batchCreateOrderReqV5;
  batchCreateOrderReqV5.set_category(category);
  batchCreateOrderReqV5.mutable_request()->Add(list.begin(), list.end());

  RpcContext ct{};
  ct.ct.AddMetadata("batch_oi", "{\"BTCUSDT\":\"1#1\"}");
  auto resp = user1.batch_create_order(batchCreateOrderReqV5, ct);
  ASSERT_NE(resp.get(), nullptr);

  auto ret_info_vec = ct.RetBatchResp();
  int idx = 0;
  for (const auto& ret_info : ret_info_vec) {
    std::cout << "Idx: " << idx << ", RetCode: " << ret_info.code() << ", RetMsg: " << ret_info.msg() << std::endl;
    if (idx == 0) {
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeSuccess);
    }
    if (idx == 1) {
      // OI拦截返回 OpenInterestRuleNotSatisfied (30077) -> 110021
      // ASSERT_EQ(ret_info.code(), 110021); oi下沉后不再通过metadata数据做限仓拦截
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeSuccess);
    }
    idx++;
  }

  //  std::cout << "RetMetaData: " << ct.DebugMetaData() << std::endl;
  //  std::string msg{};
  //  std::cout << "Resp:" << utils::PbMessageToJsonString(*resp, &msg) << std::endl;
}

TEST_F(FutureBatchCreateAoTest, f_create_order_open_api_banned_limit) {
  biz::user_id_t uid = 989012224;
  stub user1(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  // 校准标记价格
  std::vector<tmock::MockPrice> mp_vec{{ESymbol::BTCUSDT, 20000, 20000, 20000}, {ESymbol::XRPUSDT, 1000, 1000, 1000}};
  te->BatchAddMarkPrice(mp_vec);

  std::string category = "linear";
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "0.1";
  std::string price = "20000";
  std::string time_in_force = "GTC";

  std::list<svc::uta_engine::req::BatchCreateOrderReqV5Item> list;
  svc::uta_engine::req::BatchCreateOrderReqV5Item btc_usdt_item;
  btc_usdt_item.set_symbol("BTCUSDT");
  btc_usdt_item.set_order_link_id("ec893237-f1be-464c-9ac4-2c24112ec4c1");
  btc_usdt_item.set_price(price);
  btc_usdt_item.set_qty(qty);
  btc_usdt_item.set_side(side);
  btc_usdt_item.set_order_type(order_type);
  btc_usdt_item.set_time_in_force(time_in_force);
  list.emplace_back(btc_usdt_item);

  svc::uta_engine::req::BatchCreateOrderReqV5Item eth_usdt_item;
  eth_usdt_item.set_symbol("XRPUSDT");
  eth_usdt_item.set_order_link_id("dce6a902-fbe8-456f-8ec6-772071701262");
  eth_usdt_item.set_price(price);
  eth_usdt_item.set_qty(qty);
  eth_usdt_item.set_side(side);
  eth_usdt_item.set_order_type(order_type);
  eth_usdt_item.set_time_in_force(time_in_force);
  list.emplace_back(eth_usdt_item);

  svc::uta_engine::req::BatchCreateOrderReqV5Item eth_usdt_ro_item;
  eth_usdt_item.set_symbol("XRPUSDT");
  eth_usdt_item.set_order_link_id("00328aec-5d40-40ab-abd3-39f90d1f9ee2");
  eth_usdt_item.set_price(price);
  eth_usdt_item.set_qty(qty);
  eth_usdt_item.set_side(side);
  eth_usdt_item.set_order_type(order_type);
  eth_usdt_item.set_time_in_force(time_in_force);
  eth_usdt_item.set_reduce_only(true);
  list.emplace_back(eth_usdt_item);

  svc::uta_engine::req::BatchCreateOrderReqV5 batchCreateOrderReqV5;
  batchCreateOrderReqV5.set_category(category);
  batchCreateOrderReqV5.mutable_request()->Add(list.begin(), list.end());

  RpcContext ct{};
  ct.ct.AddMetadata("batch_tradecheck", "{\"BTCUSDT\":11108, \"XRPUSDT\":1}");

  auto resp = user1.batch_create_order(batchCreateOrderReqV5, ct);
  ASSERT_NE(resp.get(), nullptr);

  auto ret_info_vec = ct.RetBatchResp();
  int idx = 0;
  for (const auto& ret_info : ret_info_vec) {
    std::cout << "Idx: " << idx << ", RetCode: " << ret_info.code() << ", RetMsg: " << ret_info.msg() << std::endl;
    if (idx == 0) {
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeSuccess);
    }
    if (idx == 1) {
      // 全部封禁 返回指定错误码， ex.: 11108
      ASSERT_EQ(ret_info.code(), 11108);
    }
    if (idx == 2) {
      // 只减仓封禁 返回指定错误码， ex.: 10008
      ASSERT_EQ(ret_info.code(), 10008);
    }
    if (idx == 3) {
      // 只减仓封禁 RO通过，但没有持仓，返回错误码 30063 -> 110017
      ASSERT_EQ(ret_info.code(), 110017);
    }
    idx++;
  }

  std::cout << "RetMetaData: " << ct.DebugMetaData() << std::endl;

  std::string msg{};
  std::cout << "Resp:" << utils::PbMessageToJsonString(*resp, &msg) << std::endl;
}

TEST_F(FutureBatchCreateAoTest, f_create_order_open_api_rate_limit) {
  biz::symbol_t symbol_id = 5;  // BTCUSDT

  biz::user_id_t uid = 39632634;
  stub user1(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol_id), 20000, 20000, 20000);

  std::string category = "linear";
  std::string symbol = "BTCUSDT";
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "0.1";
  std::string price = "20000";
  std::string time_in_force = "GTC";

  auto limit_value = 1;
  auto items_count = 2;

  std::list<svc::uta_engine::req::BatchCreateOrderReqV5Item> list;

  for (int i = 0; i < items_count; i++) {
    svc::uta_engine::req::BatchCreateOrderReqV5Item batchCreateOrderReqV5Item;
    batchCreateOrderReqV5Item.set_symbol(symbol);
    batchCreateOrderReqV5Item.set_order_link_id(te->GenUUID());
    batchCreateOrderReqV5Item.set_price(price);
    batchCreateOrderReqV5Item.set_qty(qty);
    batchCreateOrderReqV5Item.set_side(side);
    batchCreateOrderReqV5Item.set_order_type(order_type);
    batchCreateOrderReqV5Item.set_time_in_force(time_in_force);

    list.emplace_back(batchCreateOrderReqV5Item);
  }

  svc::uta_engine::req::BatchCreateOrderReqV5 batchCreateOrderReqV5;
  batchCreateOrderReqV5.set_category(category);
  batchCreateOrderReqV5.mutable_request()->Add(list.begin(), list.end());

  RpcContext ct{};
  ct.ct.AddMetadata("limit_rule", "order");
  ct.ct.AddMetadata("limit_value", std::to_string(limit_value));
  ct.ct.AddMetadata("items_count", std::to_string(items_count));

  auto resp = user1.batch_create_order(batchCreateOrderReqV5, ct);
  ASSERT_NE(resp.get(), nullptr);

  auto ret_info_vec = ct.RetBatchResp();
  int idx = 0;
  for (const auto& ret_info : ret_info_vec) {
    std::cout << "Idx: " << idx << ", RetCode: " << ret_info.code() << ", RetMsg: " << ret_info.msg() << std::endl;
    if (idx == 0) {
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeSuccess);
    }
    if (idx == 1) {
      //  不拦截
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeSuccess);
    }
    if (idx == 2) {
      // OrateLimit 拦截返回 10006
      ASSERT_EQ(ret_info.code(), 10006);
    }
    idx++;
  }

  std::cout << "RetMetaData: " << ct.DebugMetaData() << std::endl;

  std::string msg{};
  std::cout << "Resp:" << utils::PbMessageToJsonString(*resp, &msg) << std::endl;
}

TEST_F(FutureBatchCreateAoTest, f_create_order_open_api_invalid_qty) {
  biz::symbol_t symbol_id = 5;  // BTCUSDT

  biz::user_id_t uid = 39632634;
  stub user1(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol_id), 20000, 20000, 20000);

  std::string category = "linear";
  std::string symbol = "BTCUSDT";
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string price = "20000";
  std::string time_in_force = "GTC";

  auto items_count = 2;

  std::list<svc::uta_engine::req::BatchCreateOrderReqV5Item> list;

  auto idx = 0;
  for (int i = 0; i < items_count; i++) {
    svc::uta_engine::req::BatchCreateOrderReqV5Item batchCreateOrderReqV5Item;
    batchCreateOrderReqV5Item.set_symbol(symbol);
    batchCreateOrderReqV5Item.set_order_link_id("link-" + std::to_string(i));
    batchCreateOrderReqV5Item.set_price(price);
    batchCreateOrderReqV5Item.set_side(side);
    batchCreateOrderReqV5Item.set_order_type(order_type);
    batchCreateOrderReqV5Item.set_time_in_force(time_in_force);

    if (idx == 0) {
      // qty 小于min_qty
      batchCreateOrderReqV5Item.set_qty("0.0000001");
    }

    if (idx == 1) {
      // qty 精度超过规定lot_fraction_
      batchCreateOrderReqV5Item.set_qty("1.000000000002001");
    }

    list.emplace_back(batchCreateOrderReqV5Item);
    idx++;
  }

  svc::uta_engine::req::BatchCreateOrderReqV5 batchCreateOrderReqV5;
  batchCreateOrderReqV5.set_category(category);
  batchCreateOrderReqV5.mutable_request()->Add(list.begin(), list.end());

  RpcContext ct{};
  auto resp = user1.batch_create_order(batchCreateOrderReqV5, ct);
  ASSERT_NE(resp.get(), nullptr);

  auto ret_info_vec = ct.RetBatchResp();
  idx = 0;
  for (const auto& ret_info : ret_info_vec) {
    std::cout << "Idx: " << idx << ", RetCode: " << ret_info.code() << ", RetMsg: " << ret_info.msg() << std::endl;
    if (idx == 0) {
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeSuccess);
    }
    if (idx == 1) {
      //  下单失败
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeParamsError);
    }
    if (idx == 2) {
      //  下单失败
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeParamsError);
    }
    idx++;
  }

  std::cout << "RetMetaData: " << ct.DebugMetaData() << std::endl;

  std::string resp_msg{};
  std::cout << "Resp:" << utils::PbMessageToJsonString(*resp, &resp_msg) << std::endl;
}

TEST_F(FutureBatchCreateAoTest, f_create_order_open_api_kyc_site_config_limit) {
  biz::user_id_t uid = 982012524;
  stub user1(te, uid);

  {
    // 充值USDT
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值USDC
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  // 校准标记价格
  std::vector<tmock::MockPrice> mp_vec{
      {ESymbol::BTCUSDT, 20000, 20000, 20000}, {ESymbol(45), 1000, 1000, 1000}, {ESymbol(289), 1000, 1000, 1000}};
  te->BatchAddMarkPrice(mp_vec);

  std::string category = "linear";
  std::string side = "Sell";
  std::string order_type = "Limit";
  std::string qty = "0.1";
  std::string price = "20000";
  std::string time_in_force = "GTC";

  std::list<svc::uta_engine::req::BatchCreateOrderReqV5Item> list;
  svc::uta_engine::req::BatchCreateOrderReqV5Item btc_usdt_item;
  btc_usdt_item.set_symbol("BTCUSDT");
  btc_usdt_item.set_order_link_id("ec893237-f1be-464c-9ac4-2c24112ec4c1");
  btc_usdt_item.set_price(price);
  btc_usdt_item.set_qty(qty);
  btc_usdt_item.set_side(side);
  btc_usdt_item.set_order_type(order_type);
  btc_usdt_item.set_time_in_force(time_in_force);
  list.emplace_back(btc_usdt_item);

  svc::uta_engine::req::BatchCreateOrderReqV5Item eth_usdc_item;
  eth_usdc_item.set_symbol("ETH-26MAY23");
  eth_usdc_item.set_order_link_id("dce6a902-fbe8-456f-8ec6-772071701262");
  eth_usdc_item.set_price(price);
  eth_usdc_item.set_qty(qty);
  eth_usdc_item.set_side(side);
  eth_usdc_item.set_order_type(order_type);
  eth_usdc_item.set_time_in_force(time_in_force);
  list.emplace_back(eth_usdc_item);

  svc::uta_engine::req::BatchCreateOrderReqV5Item eth_usdc_ro_item;
  eth_usdc_ro_item.set_symbol("ETH-26MAY23");
  eth_usdc_ro_item.set_order_link_id("00328aec-5d40-40ab-abd3-39f90d1f9ee2");
  eth_usdc_ro_item.set_price(price);
  eth_usdc_ro_item.set_qty(qty);
  eth_usdc_ro_item.set_side(side);
  eth_usdc_ro_item.set_order_type(order_type);
  eth_usdc_ro_item.set_time_in_force(time_in_force);
  eth_usdc_ro_item.set_reduce_only(true);
  list.emplace_back(eth_usdc_ro_item);

  svc::uta_engine::req::BatchCreateOrderReqV5Item btc_usdc_item;
  btc_usdc_item.set_symbol("BTCPERP");
  btc_usdc_item.set_order_link_id("614ede18-471b-4891-b774-f5d0ed25d498");
  btc_usdc_item.set_price(price);
  btc_usdc_item.set_qty(qty);
  btc_usdc_item.set_side(side);
  btc_usdc_item.set_order_type(order_type);
  btc_usdc_item.set_time_in_force(time_in_force);
  list.emplace_back(btc_usdc_item);

  svc::uta_engine::req::BatchCreateOrderReqV5 batchCreateOrderReqV5;
  batchCreateOrderReqV5.set_category(category);
  batchCreateOrderReqV5.mutable_request()->Add(list.begin(), list.end());

  RpcContext ct{};
  ct.ct.AddMetadata("site_config", "{\"coin_white_list\":[\"BTC\", \"USDT\", \"ETH\"]}");

  auto resp = user1.batch_create_order(batchCreateOrderReqV5, ct);
  ASSERT_NE(resp.get(), nullptr);

  auto ret_info_vec = ct.RetBatchResp();
  int idx = 0;
  for (const auto& ret_info : ret_info_vec) {
    std::cout << "Idx: " << idx << ", RetCode: " << ret_info.code() << ", RetMsg: " << ret_info.msg() << std::endl;
    if (idx == 0) {
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeSuccess);
    }
    if (idx == 1) {
      // USDC不在白名单
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeSuccess);
    }
    if (idx == 2) {
      // USDC不在白名单
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeParamsError);
    }
    if (idx == 3) {
      // USDC不在白名单 RO 放过, 没有持仓返回110017
      ASSERT_EQ(ret_info.code(), 110017);
    }
    if (idx == 4) {
      // USDC不在 白名单
      ASSERT_EQ(ret_info.code(), error::ErrorCode::kErrorCodeParamsError);
    }
    idx++;
  }

  std::cout << "RetMetaData: " << ct.DebugMetaData() << std::endl;

  std::string msg{};
  std::cout << "Resp:" << utils::PbMessageToJsonString(*resp, &msg) << std::endl;
}

std::shared_ptr<tmock::CTradeAppMock> FutureBatchCreateAoTest::te;
