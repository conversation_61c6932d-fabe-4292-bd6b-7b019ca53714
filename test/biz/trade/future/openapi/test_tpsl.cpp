#include "test/biz/trade/future/openapi/test_tpsl.hpp"

#include <gtest/gtest.h>

#include <string>
#include <vector>

#include "test/biz/trade/lib/stub.hpp"

TEST_F(OpenApiTpslTest, switch_tpsl_mode) {
  biz::user_id_t uid = 800012;
  stub user(te, uid);

  {
    FutureSwitchTpSlModeV5Builder set_tp_sl_m_v5;
    set_tp_sl_m_v5.SetValue("linear", "BTCUSDT", "Partial");

    RpcContext ct{};
    auto resp = user.set_tp_sl_mode(set_tp_sl_m_v5.Build(), ct);

    ASSERT_NE(resp, nullptr);

    std::string msg{};
    // std::cout << utils::PbMessageToJsonString(*resp, &msg) << std::endl;
    ASSERT_EQ(resp->tp_sl_mode(), "Partial");

    // std::cout << "RetCode: " << ct.RetCode() << ", RetMsg: " << ct.RetMsg() << std::endl;
    ASSERT_EQ(0, ct.RetCode());
  }
}

TEST_F(OpenApiTpslTest, switch_tpsl_mode_invalid) {
  biz::user_id_t uid = 800015;
  stub user(te, uid);

  {
    FutureSwitchTpSlModeV5Builder set_tp_sl_m_v5;
    set_tp_sl_m_v5.SetValue("linear", "BTCUSDT", "333");

    RpcContext ct{};
    auto resp = user.set_tp_sl_mode(set_tp_sl_m_v5.Build(), ct);
    // std::cout << "RetCode: " << ct.RetCode() << ", RetMsg: " << ct.RetMsg() << std::endl;

    ASSERT_EQ(10001, ct.RetCode());
  }
}

TEST_F(OpenApiTpslTest, duplicate_set_leverage_under_pz_BothSide) {
  biz::user_id_t uid = 800013;
  stub user(te, uid);

  // 切双仓
  {
    FutureOneOfSwitchPositionModeBuilder switch_position_mode(ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::BothSide,
                                                              uid);
    auto resp = user.process(switch_position_mode.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto pz_checker_0 = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker_0.m_msg, nullptr);
    EXPECT_EQ(EPositionMode::MergedSingle, static_cast<EPositionMode>(pz_checker_0.m_msg->mode()));
    EXPECT_EQ(ESide::None, pz_checker_0.m_msg->side());
    EXPECT_EQ(EPositionStatus::Inactive, pz_checker_0.m_msg->position_status());
    auto pz_checker_1 = result.RefFutureMarginResult().RefRelatedPosition(1);
    ASSERT_NE(pz_checker_1.m_msg, nullptr);
    EXPECT_EQ(EPositionMode::BothSide, static_cast<EPositionMode>(pz_checker_1.m_msg->mode()));
    EXPECT_EQ(ESide::None, pz_checker_1.m_msg->side());
    EXPECT_EQ(EPositionStatus::Normal, pz_checker_1.m_msg->position_status());
    auto pz_checker_2 = result.RefFutureMarginResult().RefRelatedPosition(2);
    ASSERT_NE(pz_checker_2.m_msg, nullptr);
    EXPECT_EQ(EPositionMode::BothSide, static_cast<EPositionMode>(pz_checker_2.m_msg->mode()));
    EXPECT_EQ(ESide::None, pz_checker_2.m_msg->side());
    EXPECT_EQ(EPositionStatus::Normal, pz_checker_2.m_msg->position_status());
  }

  {
    OpenApiV5SetLeverageBuilder set_lv_build("linear", "BTCUSDT", "10", "10");
    RpcContext ct{};
    auto resp = user.set_leverage(set_lv_build.Build(), ct);
    // std::cout << "RetCode: " << ct.RetCode() << ", RetMsg: " << ct.RetMsg() << std::endl;
  }

  {
    OpenApiV5SetLeverageBuilder set_lv_build("linear", "BTCUSDT", "10", "10");
    RpcContext ct{};
    auto resp = user.set_leverage(set_lv_build.Build(), ct);
    // std::cout << "RetCode: " << ct.RetCode() << ", RetMsg: " << ct.RetMsg() << std::endl;

    ASSERT_EQ(110043, ct.RetCode());
  }
}

TEST_F(OpenApiTpslTest, amend_tp_sl_v5) {
  biz::coin_t const coin = 5;      // BTC
  biz::symbol_t const symbol = 5;  // BTCUSDT

  biz::user_id_t uid_1 = 82231455;
  stub user1(te, uid_1);
  {
    // wallet
    std::string amount = "1000000";
    std::string bonus_change = "0";

    // 充值请求
    std::string req_id = "c0283205-487f-4530-9e03-d0fffe18b8c7";
    std::string trans_id = "9a098dce-1df6-462d-b268-83dac3cf7c29";
    int wallet_record_type = 1;

    FutureDepositBuilder u1_deposit_build(req_id, uid_1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto u1_resp = user1.process(u1_deposit_build.Build());
    auto u1_result = te->PopResult();
    auto wallet_check = u1_result.RefAssetMarginResult().RefRelatedWallet(coin);
    wallet_check.CheckCoin(coin).CheckAB(biz::money_t(amount));
  }

  // 校准标记价格
  auto const underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  auto ao_carry_tpsl_order_id = "d5f57f76-df66-49e1-924c-a2fdd374b138";

  // openAPI V5, 创建止盈止损活动单
  {
    OpenApiV5CreateOrderBuilder create_tpsl_order_builder("linear", "BTCUSDT", EPositionIndex::Single, "Buy", "Limit",
                                                          "100", "10000");
    create_tpsl_order_builder.SetOrderLinkID(ao_carry_tpsl_order_id);
    create_tpsl_order_builder.SetTpSl("LastPrice", "10500", "MarkPrice", "9500");
    create_tpsl_order_builder.SetTpSlLimit("Partial", "11000", "", "Limit", "Market");

    RpcContext ct{};
    auto create_tpsl_resp = user1.create_order(create_tpsl_order_builder.Build(), ct);
    ASSERT_NE(create_tpsl_resp, nullptr);

    // std::cout << "OpenApi create: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    ASSERT_EQ(create_tpsl_resp->order_link_id(), ao_carry_tpsl_order_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(ao_carry_tpsl_order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckOrderWithTp(ETriggerBy::LastPrice, 10500'0000, EOrderType::Limit, 11000'0000);
    order_check.CheckOrderWithSl(ETriggerBy::MarkPrice, 9500'0000, EOrderType::Market);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    // std::cout << "#1: result:" << msg << std::endl;
  }

  // openAPI V5, 取消止盈止损
  {
    ReplaceOrderBuilder replace_tp_build("linear", "BTCUSDT", "", "", ao_carry_tpsl_order_id);
    replace_tp_build.SetTpParam("0", "");
    replace_tp_build.SetSlParam("0", "");

    RpcContext ct{};
    auto replace_tp_resp = user1.replace_order(replace_tp_build.Build(), ct);
    ASSERT_NE(replace_tp_resp, nullptr);

    // std::cout << "OpenApi amend: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    ASSERT_EQ(replace_tp_resp->order_link_id(), ao_carry_tpsl_order_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(ao_carry_tpsl_order_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    order_check.CheckStatus(EOrderStatus::New, ECrossStatus::Replaced);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    // std::cout << "#2: result:" << msg << std::endl;
  }

  // openAPI V5, 设置止盈止损（少sp/sl triggerby）
  {
    ReplaceOrderBuilder replace_tp_build("linear", "BTCUSDT", "", "", ao_carry_tpsl_order_id);
    replace_tp_build.SetTpParam("12000", "");
    replace_tp_build.SetSlParam("9500", "");

    RpcContext ct{};
    auto replace_tp_resp = user1.replace_order(replace_tp_build.Build(), ct);
    ASSERT_NE(replace_tp_resp, nullptr);

    // std::cout << "OpenApi amend: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeParamsError);
  }

  // openAPI V5, 设置止盈止损 success
  {
    ReplaceOrderBuilder replace_tp_build("linear", "BTCUSDT", "", "", ao_carry_tpsl_order_id);
    replace_tp_build.SetTpParam("12000", "MarkPrice");
    replace_tp_build.SetSlParam("9500", "IndexPrice");

    RpcContext ct{};
    auto replace_tp_resp = user1.replace_order(replace_tp_build.Build(), ct);
    ASSERT_NE(replace_tp_resp, nullptr);

    // std::cout << "OpenApi amend: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    ASSERT_EQ(replace_tp_resp->order_link_id(), ao_carry_tpsl_order_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(ao_carry_tpsl_order_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    order_check.CheckStatus(EOrderStatus::New, ECrossStatus::Replaced);

    // 已知问题, 取消tpsl再设置后， sl/tp order type是unknown; 当创建tp/sl单时， order_type会被设置成Market
    order_check.CheckOrderWithTp(ETriggerBy::MarkPrice, 12000'0000, EOrderType::Market);
    order_check.CheckOrderWithSl(ETriggerBy::IndexPrice, 9500'0000, EOrderType::Market);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    // std::cout << "#3: result:" << msg << std::endl;
  }

  // 验证是否成功创建tpsl, 以及sl/tp order type是否是Market
  {
    biz::user_id_t uid_2 = 82231456;
    stub user2(te, uid_2);

    // user2 充值
    {
      // wallet
      std::string amount = "1000000";
      std::string bonus_change = "0";

      // 充值请求
      std::string req_id = "cfc6343d-6509-489b-8e69-a5dd6c207dca";
      std::string trans_id = "9f7ad2be-3923-4bea-baac-ed9e08211983";
      int wallet_record_type = 1;

      FutureDepositBuilder u2_deposit_build(req_id, uid_2, trans_id, coin, wallet_record_type, amount, bonus_change);
      auto u2_resp = user2.process(u2_deposit_build.Build());
      auto u2_result = te->PopResult();
      auto wallet_check = u2_result.RefAssetMarginResult().RefRelatedWallet(coin);
      wallet_check.CheckCoin(coin).CheckAB(biz::money_t(amount));
    }

    // user2 下单 做对手
    {
      auto ao_order_link_id = "4332297a-30b6-470c-a9c5-ed468c7bb349";
      OpenApiV5CreateOrderBuilder create_order_builder("linear", "BTCUSDT", EPositionIndex::Single, "Sell", "Limit",
                                                       "100", "10000");
      create_order_builder.SetOrderLinkID(ao_order_link_id);

      RpcContext ct{};
      auto create_tpsl_resp = user2.create_order(create_order_builder.Build(), ct);
      ASSERT_NE(create_tpsl_resp, nullptr);

      // std::cout << "OpenApi create: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
      EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

      ASSERT_EQ(create_tpsl_resp->order_link_id(), ao_order_link_id);

      {
        auto result = te->PopResult();
        ASSERT_NE(result.m_msg.get(), nullptr);

        auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(ao_order_link_id);
        ASSERT_NE(order_check.m_msg, nullptr);

        std::string msg{};
        (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
        // std::cout << "#5: result:" << msg << std::endl;
      }

      // 验证user1 是否成功创建tpsl, 以及sl/tp order type是否是Market
      {
        auto result = te->PopResult();
        ASSERT_NE(result.m_msg.get(), nullptr);

        auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(ao_carry_tpsl_order_id);
        ASSERT_NE(order_check.m_msg, nullptr);

        for (const auto& order : result.RefFutureMarginResult().m_msg->related_orders()) {
          if (order.create_type() == ECreateType::CreateByUser) {
            // user1的活动单
            ASSERT_EQ(order.order_status(), EOrderStatus::Filled);
            ASSERT_EQ(order.cross_status(), ECrossStatus::MakerFill);

            continue;
          }

          if (order.create_type() == ECreateType::CreateByTakeProfit) {
            // user1的止盈条件单
            ASSERT_EQ(order.order_type(), EOrderType::Market);
            ASSERT_EQ(order.order_status(), EOrderStatus::Untriggered);
            ASSERT_EQ(order.cross_status(), ECrossStatus::Init);
          }

          if (order.create_type() == ECreateType::CreateByStopLoss) {
            // user1的止损条件单
            ASSERT_EQ(order.order_type(), EOrderType::Market);
            ASSERT_EQ(order.order_status(), EOrderStatus::Untriggered);
            ASSERT_EQ(order.cross_status(), ECrossStatus::Init);
          }
        }

        std::string msg{};
        (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
        std::cout << "#6: result:" << msg << std::endl;
      }
    }
  }
}

TEST_F(OpenApiTpslTest, amend_tp_sl_v5_invalid_symbol) {
  auto coin = ECoin::USDT;  // BTC

  biz::user_id_t uid_1 = 234235556;
  stub user1(te, uid_1);
  {
    // wallet
    std::string amount = "1000000";
    std::string bonus_change = "0";

    // 充值请求
    std::string req_id = "c0283205-487f-4530-9e03-d0fffe18b8c7";
    std::string trans_id = "9a098dce-1df6-462d-b268-83dac3cf7c29";
    int wallet_record_type = 1;

    FutureDepositBuilder u1_deposit_build(req_id, uid_1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto u1_resp = user1.process(u1_deposit_build.Build());
    auto u1_result = te->PopResult();
    auto wallet_check = u1_result.RefAssetMarginResult().RefRelatedWallet(coin);
    wallet_check.CheckCoin(coin).CheckAB(biz::money_t(amount));
  }

  // 校准标记价格
  std::vector<tmock::MockPrice> mp_vec{{ESymbol::BTCUSDT, 10000, 10000, 10000}, {ESymbol::XRPUSDT, 1000, 1000, 1000}};
  te->BatchAddMarkPrice(mp_vec);

  auto normal_ao_order_link_id = "3237808b-b8fe-48dc-9b6f-b4fac30c264c";
  // openAPI V5, 创建普通活动单，构造异常场景需要先有个其他Symbol的订单， 触发异常时会返回这个symbol的orderid
  {
    OpenApiV5CreateOrderBuilder create_tpsl_order_builder("linear", "XRPUSDT", EPositionIndex::Single, "Buy", "Limit",
                                                          "0.01", "1000");
    create_tpsl_order_builder.SetOrderLinkID(normal_ao_order_link_id);

    RpcContext ct{};
    auto create_tpsl_resp = user1.create_order(create_tpsl_order_builder.Build(), ct);
    ASSERT_NE(create_tpsl_resp, nullptr);

    std::cout << "OpenApi create: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    ASSERT_EQ(create_tpsl_resp->order_link_id(), normal_ao_order_link_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(normal_ao_order_link_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#1: result:" << msg << std::endl;
  }

  auto ao_carry_tpsl_order_id = "d5f57f76-df66-49e1-924c-a2fdd374b138";

  // openAPI V5, 创建止盈止损活动单
  {
    OpenApiV5CreateOrderBuilder create_tpsl_order_builder("linear", "BTCUSDT", EPositionIndex::Single, "Buy", "Limit",
                                                          "0.01", "10000");
    create_tpsl_order_builder.SetOrderLinkID(ao_carry_tpsl_order_id);
    create_tpsl_order_builder.SetTpSl("LastPrice", "10500", "MarkPrice", "9500");

    RpcContext ct{};
    auto create_tpsl_resp = user1.create_order(create_tpsl_order_builder.Build(), ct);
    ASSERT_NE(create_tpsl_resp, nullptr);

    std::cout << "OpenApi create: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    ASSERT_EQ(create_tpsl_resp->order_link_id(), ao_carry_tpsl_order_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(ao_carry_tpsl_order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckOrderWithTp(ETriggerBy::LastPrice, 10500'0000, EOrderType::Market);
    order_check.CheckOrderWithSl(ETriggerBy::MarkPrice, 9500'0000, EOrderType::Market);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#2: result:" << msg << std::endl;
  }

  // openAPI V5, 修改止盈止损 symbol与order不一致，最终会以order的symbol为准
  {
    ReplaceOrderBuilder replace_tp_build("linear", "XRPUSDT", "", "", ao_carry_tpsl_order_id);
    replace_tp_build.SetTpParam("11000", "MarkPrice");

    RpcContext ct{};
    auto replace_tp_resp = user1.replace_order(replace_tp_build.Build(), ct);
    ASSERT_NE(replace_tp_resp, nullptr);

    std::cout << "OpenApi amend: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    ASSERT_EQ(replace_tp_resp->order_link_id(), ao_carry_tpsl_order_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(ao_carry_tpsl_order_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    order_check.CheckStatus(EOrderStatus::New, ECrossStatus::Replaced);

    // 已知问题, 取消tpsl再设置后， sl/tp order type是unknown; 当创建tp/sl单时， order_type会被设置成Market
    order_check.CheckOrderWithTp(ETriggerBy::MarkPrice, 11000'0000, EOrderType::Market);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#3: result:" << msg << std::endl;
  }
}

TEST_F(OpenApiTpslTest, trading_stop_modify_tpslts_full_mod_full) {
  auto coin = ECoin::USDT;  // BTC

  biz::user_id_t uid_1 = 542342389;
  stub user1(te, uid_1);
  {
    // wallet
    std::string amount = "1000000";
    std::string bonus_change = "0";

    // 充值请求
    std::string req_id = "c0281105-487f-4340-9e03-d0ff1e18bac7";
    std::string trans_id = "9a095ce-9df6-312d-b268-21ff1418cfc9";
    int wallet_record_type = 1;

    FutureDepositBuilder u1_deposit_build(req_id, uid_1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto u1_resp = user1.process(u1_deposit_build.Build());
    auto u1_result = te->PopResult();
    auto wallet_check = u1_result.RefAssetMarginResult().RefRelatedWallet(coin);
    wallet_check.CheckCoin(coin).CheckAB(biz::money_t(amount));
  }

  biz::user_id_t uid_2 = 9032423;
  stub user2(te, uid_2);
  {
    // wallet
    std::string amount = "1000000";
    std::string bonus_change = "0";

    // 充值请求
    std::string req_id = "01a12e22-0958-4dda-ed97-4ef993d94024";
    std::string trans_id = "92db4809-cac8-468c-8f8e-bad345de4889";
    int wallet_record_type = 1;

    FutureDepositBuilder u2_deposit_build(req_id, uid_2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto u2_resp = user2.process(u2_deposit_build.Build());
    auto u2_result = te->PopResult();
    auto wallet_check = u2_result.RefAssetMarginResult().RefRelatedWallet(coin);
    wallet_check.CheckCoin(coin).CheckAB(biz::money_t(amount));
  }

  // 校准标记价格
  te->AddMarkPrice(ESymbol::XRPUSDT, 1000, 1000, 1000);

  // 对手user2， 为user1构建持仓
  auto u2_order_id = "546d24ab-b3a5-434b-a2ed-1b0ea0f9ee21";
  // openAPI V5, 创建普通活动单，构造异常场景需要先有个其他Symbol的订单， 触发异常时会返回这个symbol的orderid
  {
    OpenApiV5CreateOrderBuilder create_order_builder("linear", "XRPUSDT", EPositionIndex::Single, "Sell", "Limit",
                                                     "0.1", "1000");
    create_order_builder.SetOrderLinkID(u2_order_id);

    RpcContext ct{};
    auto create_ao_resp = user2.create_order(create_order_builder.Build(), ct);
    ASSERT_NE(create_ao_resp, nullptr);

    std::cout << "OpenApi create: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    ASSERT_EQ(create_ao_resp->order_link_id(), u2_order_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(u2_order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#1: result:" << msg << std::endl;
  }

  // u1创建待tpsl的持仓
  auto u1_ao_carry_tpsl_order_id = "6ef3dd70-12db-4e25-841a-453f3e80a6c1";
  std::string full_tp_ord_id = "";
  std::string full_sl_ord_id = "";
  // openAPI V5, 创建止盈止损活动单
  {
    OpenApiV5CreateOrderBuilder create_tpsl_order_builder("linear", "XRPUSDT", EPositionIndex::Single, "Buy", "Limit",
                                                          "0.1", "1000");
    create_tpsl_order_builder.SetOrderLinkID(u1_ao_carry_tpsl_order_id);
    create_tpsl_order_builder.SetTpSl("LastPrice", "1100", "MarkPrice", "900");

    RpcContext ct{};
    auto create_tpsl_resp = user1.create_order(create_tpsl_order_builder.Build(), ct);
    ASSERT_NE(create_tpsl_resp, nullptr);

    std::cout << "OpenApi create: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    ASSERT_EQ(create_tpsl_resp->order_link_id(), u1_ao_carry_tpsl_order_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 3);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);

    // u1成交回报
    auto& pz = result.m_msg->futures_margin_result().affected_positions().at(0);
    EXPECT_EQ(pz.user_id(), uid_1);
    EXPECT_EQ(pz.size_x(), 0.1 * 1e8);
    EXPECT_EQ(pz.value_e8(), 0.1 * 1000 * 1e8);
    EXPECT_EQ(pz.side(), ESide::Buy);
    EXPECT_EQ(pz.take_profit_x(), 1100'0000);
    EXPECT_EQ(pz.stop_loss_x(), 900'0000);
    EXPECT_EQ(pz.tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(pz.sl_trigger_by(), ETriggerBy::MarkPrice);
    // 下单只能是全部止盈止损，这里是Unknown （感觉不合理）
    // EXPECT_EQ(pz.tp_sl_mode(), ETpSlMode::Full);

    for (auto& ord : result.m_msg->futures_margin_result().related_orders()) {
      if (ord.order_link_id() == u1_ao_carry_tpsl_order_id) {
        // u1订单回报
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.price_x(), 1000'0000);
        ASSERT_EQ(ord.side(), ESide::Buy);
        ASSERT_EQ(ord.order_type(), EOrderType::Limit);

        ASSERT_EQ(ord.take_profit_x(), 1100'0000);
        ASSERT_EQ(ord.take_profit_limit_x(), 0);
        ASSERT_EQ(ord.tp_order_type(), EOrderType::Market);
        ASSERT_EQ(ord.tp_trigger_by(), ETriggerBy::LastPrice);

        ASSERT_EQ(ord.stop_loss_x(), 900'0000);
        ASSERT_EQ(ord.stop_loss_limit_x(), 0);
        ASSERT_EQ(ord.sl_order_type(), EOrderType::Market);
        ASSERT_EQ(ord.sl_trigger_by(), ETriggerBy::MarkPrice);
      }
      if (ord.stop_order_type() == EStopOrderType::TakeProfit) {
        full_tp_ord_id = ord.order_id();
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.tp_order_type(), EOrderType::Market);  // 什么用？？
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::LastPrice);
        ASSERT_EQ(ord.trigger_price_x(), 1100'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Full);
      }
      if (ord.stop_order_type() == EStopOrderType::StopLoss) {
        full_sl_ord_id = ord.order_id();
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.sl_order_type(), EOrderType::Market);  // 什么用？？
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::MarkPrice);
        ASSERT_EQ(ord.trigger_price_x(), 900'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Full);
      }
    }

    ASSERT_NE(full_tp_ord_id, "");
    ASSERT_NE(full_sl_ord_id, "");

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#2: result:" << msg << std::endl;
  }

  // user成交回报
  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);

    // user 2
    auto& pz = result.m_msg->futures_margin_result().affected_positions().at(0);
    EXPECT_EQ(pz.user_id(), uid_2);
    EXPECT_EQ(pz.size_x(), 0.1 * 1e8);
    EXPECT_EQ(pz.value_e8(), 0.1 * 1000 * 1e8);
    EXPECT_EQ(pz.side(), ESide::Sell);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#3: result:" << msg << std::endl;
  }

  // openAPI V5, 修改止盈止损
  bool check_ts_so = false;
  bool check_tp_so = false;
  bool check_sl_so = false;
  {
    FutureSetTpSlTsV5Builder set_tp_sl_ts_v5;
    set_tp_sl_ts_v5.SetValue("linear", "XRPUSDT", EPositionIndex::Single, "1100", "500", "1200", "800", "MarkPrice",
                             "IndexPrice", "0.1", "0.1", "Full", {}, {}, {}, {});
    RpcContext ct{};
    auto resp = user1.set_tp_sl_ts(set_tp_sl_ts_v5.Build(), ct);
    ASSERT_NE(resp, nullptr);
    std::cout << "retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 3);  // ao, tp_so, sl_so

    auto& pz = result.m_msg->futures_margin_result().affected_positions().at(0);
    EXPECT_EQ(pz.user_id(), uid_1);
    EXPECT_EQ(pz.size_x(), 0.1 * 1e8);
    EXPECT_EQ(pz.value_e8(), 0.1 * 1000 * 1e8);
    EXPECT_EQ(pz.side(), ESide::Buy);
    // 下面4个字段，只有 tpsl_mode=Full时才会更新, 所以这里不再是建仓时ao带的tpsl信息
    EXPECT_EQ(pz.take_profit_x(), 1200'0000);
    EXPECT_EQ(pz.stop_loss_x(), 800'0000);
    EXPECT_EQ(pz.tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(pz.sl_trigger_by(), ETriggerBy::IndexPrice);
    // 当前场景是Partial的，但试了Full就是下面的结论
    // trading_stop接口，修改止盈止损，即使明确是全部止盈止损，这里也是Unknown （感觉不合理）
    // EXPECT_EQ(pz.tp_sl_mode(), ETpSlMode::Full);

    // 建仓时有Full的Tpsl, 修改止盈止损单也是保持Full,
    // 所以直接改旧的止盈止损单（与Partial不同，因为Partial就是追加）和加一个追踪单
    for (auto& ord : result.m_msg->futures_margin_result().related_orders()) {
      if (ord.stop_order_type() == EStopOrderType::TrailingProfit) {
        check_ts_so = true;
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.side(), ESide::Sell);
        // 追踪只能是市价触发
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::LastPrice);
        // active_px - trail_value 这是第一个触发价格
        ASSERT_EQ(ord.trigger_price_x(), 600'0000);
        ASSERT_EQ(ord.trail_value_x(), 500'0000);
      }
      if (ord.stop_order_type() == EStopOrderType::TakeProfit) {
        check_tp_so = true;
        ASSERT_EQ(full_tp_ord_id, ord.order_id());
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.tp_order_type(), EOrderType::Market);  // 什么用？？
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::MarkPrice);
        ASSERT_EQ(ord.trigger_price_x(), 1200'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Full);
      }
      if (ord.stop_order_type() == EStopOrderType::StopLoss) {
        check_sl_so = true;
        ASSERT_EQ(full_sl_ord_id, ord.order_id());
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.sl_order_type(), EOrderType::Market);  // 什么用？？
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::IndexPrice);
        ASSERT_EQ(ord.trigger_price_x(), 800'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Full);
      }
    }

    ASSERT_EQ(check_ts_so, true);
    ASSERT_EQ(check_tp_so, true);
    ASSERT_EQ(check_sl_so, true);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#4: result:" << msg << std::endl;
  }
}

TEST_F(OpenApiTpslTest, trading_stop_modify_tpslts_full_append_partial) {
  auto coin = ECoin::USDT;  // BTC

  biz::user_id_t uid_1 = 23425466;
  stub user1(te, uid_1);
  {
    // wallet
    std::string amount = "1000000";
    std::string bonus_change = "0";

    // 充值请求
    std::string req_id = "c0283205-487f-4530-9e03-d0fffe18b8c7";
    std::string trans_id = "9a098dce-1df6-462d-b268-83dac3cf7c29";
    int wallet_record_type = 1;

    FutureDepositBuilder u1_deposit_build(req_id, uid_1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto u1_resp = user1.process(u1_deposit_build.Build());
    auto u1_result = te->PopResult();
    auto wallet_check = u1_result.RefAssetMarginResult().RefRelatedWallet(coin);
    wallet_check.CheckCoin(coin).CheckAB(biz::money_t(amount));
  }

  biz::user_id_t uid_2 = 668946278;
  stub user2(te, uid_2);
  {
    // wallet
    std::string amount = "1000000";
    std::string bonus_change = "0";

    // 充值请求
    std::string req_id = "01a13e22-0928-4dda-ad97-4ee993d92024";
    std::string trans_id = "e2db4809-bac8-468c-8f8e-bbd045de4879";
    int wallet_record_type = 1;

    FutureDepositBuilder u2_deposit_build(req_id, uid_2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto u2_resp = user2.process(u2_deposit_build.Build());
    auto u2_result = te->PopResult();
    auto wallet_check = u2_result.RefAssetMarginResult().RefRelatedWallet(coin);
    wallet_check.CheckCoin(coin).CheckAB(biz::money_t(amount));
  }

  // 校准标记价格
  te->AddMarkPrice(ESymbol::XRPUSDT, 1000, 1000, 1000);

  // 对手user2， 为user1构建持仓
  auto u2_order_id = "846df4eb-b3a5-484b-a2ed-3b0ea0fade28";
  // openAPI V5, 创建普通活动单，构造异常场景需要先有个其他Symbol的订单， 触发异常时会返回这个symbol的orderid
  {
    OpenApiV5CreateOrderBuilder create_order_builder("linear", "XRPUSDT", EPositionIndex::Single, "Sell", "Limit",
                                                     "0.1", "1000");
    create_order_builder.SetOrderLinkID(u2_order_id);

    RpcContext ct{};
    auto create_ao_resp = user2.create_order(create_order_builder.Build(), ct);
    ASSERT_NE(create_ao_resp, nullptr);

    std::cout << "OpenApi create: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    ASSERT_EQ(create_ao_resp->order_link_id(), u2_order_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(u2_order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#1: result:" << msg << std::endl;
  }

  // u1创建待tpsl的持仓
  auto u1_ao_carry_tpsl_order_id = "9ef3dd70-15db-4b25-841a-453f3d80a8c0";
  std::string full_tp_ord_id = "";
  std::string full_sl_ord_id = "";
  // openAPI V5, 创建止盈止损活动单
  {
    OpenApiV5CreateOrderBuilder create_tpsl_order_builder("linear", "XRPUSDT", EPositionIndex::Single, "Buy", "Limit",
                                                          "0.1", "1000");
    create_tpsl_order_builder.SetOrderLinkID(u1_ao_carry_tpsl_order_id);
    create_tpsl_order_builder.SetTpSl("LastPrice", "1100", "MarkPrice", "900");

    RpcContext ct{};
    auto create_tpsl_resp = user1.create_order(create_tpsl_order_builder.Build(), ct);
    ASSERT_NE(create_tpsl_resp, nullptr);

    std::cout << "OpenApi create: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    ASSERT_EQ(create_tpsl_resp->order_link_id(), u1_ao_carry_tpsl_order_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 3);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);

    // u1成交回报
    auto& pz = result.m_msg->futures_margin_result().affected_positions().at(0);
    EXPECT_EQ(pz.user_id(), uid_1);
    EXPECT_EQ(pz.size_x(), 0.1 * 1e8);
    EXPECT_EQ(pz.value_e8(), 0.1 * 1000 * 1e8);
    EXPECT_EQ(pz.side(), ESide::Buy);
    EXPECT_EQ(pz.take_profit_x(), 1100'0000);
    EXPECT_EQ(pz.stop_loss_x(), 900'0000);
    EXPECT_EQ(pz.tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(pz.sl_trigger_by(), ETriggerBy::MarkPrice);
    // 下单只能是全部止盈止损，这里是Unknown （感觉不合理）
    // EXPECT_EQ(pz.tp_sl_mode(), ETpSlMode::Full);

    for (auto& ord : result.m_msg->futures_margin_result().related_orders()) {
      if (ord.order_link_id() == u1_ao_carry_tpsl_order_id) {
        // u1订单回报
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.price_x(), 1000'0000);
        ASSERT_EQ(ord.side(), ESide::Buy);
        ASSERT_EQ(ord.order_type(), EOrderType::Limit);

        ASSERT_EQ(ord.take_profit_x(), 1100'0000);
        ASSERT_EQ(ord.take_profit_limit_x(), 0);
        ASSERT_EQ(ord.tp_order_type(), EOrderType::Market);
        ASSERT_EQ(ord.tp_trigger_by(), ETriggerBy::LastPrice);

        ASSERT_EQ(ord.stop_loss_x(), 900'0000);
        ASSERT_EQ(ord.stop_loss_limit_x(), 0);
        ASSERT_EQ(ord.sl_order_type(), EOrderType::Market);
        ASSERT_EQ(ord.sl_trigger_by(), ETriggerBy::MarkPrice);
      }
      if (ord.stop_order_type() == EStopOrderType::TakeProfit) {
        full_tp_ord_id = ord.order_id();
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.tp_order_type(), EOrderType::Market);  // 建仓时传承过来，后面因该是用不到了
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::LastPrice);
        ASSERT_EQ(ord.trigger_price_x(), 1100'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Full);
      }
      if (ord.stop_order_type() == EStopOrderType::StopLoss) {
        full_sl_ord_id = ord.order_id();
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.sl_order_type(), EOrderType::Market);  // 建仓时传承过来，后面因该是用不到了
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::MarkPrice);
        ASSERT_EQ(ord.trigger_price_x(), 900'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Full);
      }
    }

    ASSERT_NE(full_tp_ord_id, "");
    ASSERT_NE(full_sl_ord_id, "");

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#2: result:" << msg << std::endl;
  }

  // user成交回报
  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);

    // user 2
    auto& pz = result.m_msg->futures_margin_result().affected_positions().at(0);
    EXPECT_EQ(pz.user_id(), uid_2);
    EXPECT_EQ(pz.size_x(), 0.1 * 1e8);
    EXPECT_EQ(pz.value_e8(), 0.1 * 1000 * 1e8);
    EXPECT_EQ(pz.side(), ESide::Sell);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#3: result:" << msg << std::endl;
  }

  // openAPI V5, 修改止盈止损
  bool check_ts_so = true;
  bool check_tp_so = true;
  bool check_sl_so = true;
  {
    FutureSetTpSlTsV5Builder set_tp_sl_ts_v5;
    set_tp_sl_ts_v5.SetValue("linear", "XRPUSDT", EPositionIndex::Single, "1100", "500", "1200", "800", "MarkPrice",
                             "IndexPrice", "0.05", "0.05", "Partial", {}, {}, {}, {});
    RpcContext ct{};
    auto resp = user1.set_tp_sl_ts(set_tp_sl_ts_v5.Build(), ct);
    ASSERT_NE(resp, nullptr);
    std::cout << "retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 3);  // ao, tp_so, sl_so

    auto& pz = result.m_msg->futures_margin_result().affected_positions().at(0);
    EXPECT_EQ(pz.user_id(), uid_1);
    EXPECT_EQ(pz.size_x(), 0.1 * 1e8);
    EXPECT_EQ(pz.value_e8(), 0.1 * 1000 * 1e8);
    EXPECT_EQ(pz.side(), ESide::Buy);
    // 下面4个字段，只有 tpsl_mode=Full时才会更新, 所以这里还是建仓时ao带的tpsl信息
    EXPECT_EQ(pz.take_profit_x(), 1100'0000);
    EXPECT_EQ(pz.stop_loss_x(), 900'0000);
    EXPECT_EQ(pz.tp_trigger_by(), ETriggerBy::LastPrice);
    EXPECT_EQ(pz.sl_trigger_by(), ETriggerBy::MarkPrice);
    // 当前场景是Partial的，但试了Full就是下面的结论
    // trading_stop接口，修改止盈止损，即使明确是全部止盈止损，这里也是Unknown （感觉不合理）
    // EXPECT_EQ(pz.tp_sl_mode(), ETpSlMode::Full);

    // 建仓时有Full的Tpsl, 这里新增两个部分止盈止损单和一个追踪单
    for (auto& ord : result.m_msg->futures_margin_result().related_orders()) {
      if (ord.stop_order_type() == EStopOrderType::TrailingProfit) {
        check_ts_so = true;
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.side(), ESide::Sell);
        // 追踪只能是市价触发
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::LastPrice);
        // active_px - trail_value 这是第一个触发价格
        ASSERT_EQ(ord.trigger_price_x(), 600'0000);
        ASSERT_EQ(ord.trail_value_x(), 500'0000);
      }
      if (ord.stop_order_type() == EStopOrderType::PartialTakeProfit) {
        check_tp_so = true;
        ASSERT_NE(full_tp_ord_id, ord.order_id());
        ASSERT_EQ(ord.qty_x(), 0.05 * 1e8);
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.tp_order_type(), EOrderType::Market);  // 建仓时传承过来，后面因该是用不到了
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::MarkPrice);
        ASSERT_EQ(ord.trigger_price_x(), 1200'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Partial);
      }
      if (ord.stop_order_type() == EStopOrderType::PartialStopLoss) {
        check_sl_so = true;
        ASSERT_NE(full_sl_ord_id, ord.order_id());
        ASSERT_EQ(ord.qty_x(), 0.05 * 1e8);
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.sl_order_type(), EOrderType::Market);  // 建仓时传承过来，后面因该是用不到了
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::IndexPrice);
        ASSERT_EQ(ord.trigger_price_x(), 800'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Partial);
      }
    }

    ASSERT_EQ(check_ts_so, true);
    ASSERT_EQ(check_tp_so, true);
    ASSERT_EQ(check_sl_so, true);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#4: result:" << msg << std::endl;
  }
}

TEST_F(OpenApiTpslTest, trading_stop_modify_tpslts_partial_append_partial) {
  auto coin = ECoin::USDT;  // BTC

  biz::user_id_t uid_1 = 38232944;
  stub user1(te, uid_1);
  {
    // wallet
    std::string amount = "1000000";
    std::string bonus_change = "0";

    // 充值请求
    std::string req_id = "809635e8-e057-4013-9151-73d26abefd8d";
    std::string trans_id = "fcbd25d6-70c7-4345-a7d4-5c6810a65e21";
    int wallet_record_type = 1;

    FutureDepositBuilder u1_deposit_build(req_id, uid_1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto u1_resp = user1.process(u1_deposit_build.Build());
    auto u1_result = te->PopResult();
    auto wallet_check = u1_result.RefAssetMarginResult().RefRelatedWallet(coin);
    wallet_check.CheckCoin(coin).CheckAB(biz::money_t(amount));
  }

  biz::user_id_t uid_2 = 24566676;
  stub user2(te, uid_2);
  {
    // wallet
    std::string amount = "1000000";
    std::string bonus_change = "0";

    // 充值请求
    std::string req_id = "999a9c57-e0e8-47a6-9e49-c14ecb7de7ec";
    std::string trans_id = "09e75bf5-9952-4064-9b9c-fbe0be3b0830";
    int wallet_record_type = 1;

    FutureDepositBuilder u2_deposit_build(req_id, uid_2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto u2_resp = user2.process(u2_deposit_build.Build());
    auto u2_result = te->PopResult();
    auto wallet_check = u2_result.RefAssetMarginResult().RefRelatedWallet(coin);
    wallet_check.CheckCoin(coin).CheckAB(biz::money_t(amount));
  }

  // 校准标记价格
  te->AddMarkPrice(ESymbol::XRPUSDT, 1000, 1000, 1000);

  // 对手user2， 为user1构建持仓
  auto u2_order_id = "675df35d-**************-76b9cea63f0c";
  // openAPI V5, 创建普通活动单，构造异常场景需要先有个其他Symbol的订单， 触发异常时会返回这个symbol的orderid
  {
    OpenApiV5CreateOrderBuilder create_order_builder("linear", "XRPUSDT", EPositionIndex::Single, "Sell", "Limit",
                                                     "0.1", "1000");
    create_order_builder.SetOrderLinkID(u2_order_id);

    RpcContext ct{};
    auto create_ao_resp = user2.create_order(create_order_builder.Build(), ct);
    ASSERT_NE(create_ao_resp, nullptr);

    std::cout << "OpenApi create: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    ASSERT_EQ(create_ao_resp->order_link_id(), u2_order_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(u2_order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#1: result:" << msg << std::endl;
  }

  // u1创建待tpsl的持仓
  auto u1_ao_carry_tpsl_order_id = "249e8033-4235-446c-b54b-a879a9607c36";
  std::string full_tp_ord_id = "";
  std::string full_sl_ord_id = "";
  // openAPI V5, 创建止盈止损活动单
  {
    OpenApiV5CreateOrderBuilder create_tpsl_order_builder("linear", "XRPUSDT", EPositionIndex::Single, "Buy", "Limit",
                                                          "0.1", "1000");
    create_tpsl_order_builder.SetOrderLinkID(u1_ao_carry_tpsl_order_id);
    create_tpsl_order_builder.SetTpSl("LastPrice", "1100", "MarkPrice", "900");
    create_tpsl_order_builder.AppendLimitTpSl("Partial", "Limit", "1105", "", "");

    RpcContext ct{};
    auto create_tpsl_resp = user1.create_order(create_tpsl_order_builder.Build(), ct);
    ASSERT_NE(create_tpsl_resp, nullptr);

    std::cout << "OpenApi create: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    ASSERT_EQ(create_tpsl_resp->order_link_id(), u1_ao_carry_tpsl_order_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 3);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);

    // u1成交回报
    auto& pz = result.m_msg->futures_margin_result().affected_positions().at(0);
    EXPECT_EQ(pz.user_id(), uid_1);
    EXPECT_EQ(pz.size_x(), 0.1 * 1e8);
    EXPECT_EQ(pz.value_e8(), 0.1 * 1000 * 1e8);
    EXPECT_EQ(pz.side(), ESide::Buy);
    // Full时，才能设置在下面4个字段上；Partial可以理解成与Pz无关
    EXPECT_EQ(pz.take_profit_x(), 0);
    EXPECT_EQ(pz.stop_loss_x(), 0);
    EXPECT_EQ(pz.tp_trigger_by(), ETriggerBy::UNKNOWN);
    EXPECT_EQ(pz.sl_trigger_by(), ETriggerBy::UNKNOWN);
    // 无论时Full还是Partial，这里都是Unknown （感觉不合理， 起码Full应该有值）
    // EXPECT_EQ(pz.tp_sl_mode(), ETpSlMode::Full);

    for (auto& ord : result.m_msg->futures_margin_result().related_orders()) {
      if (ord.order_link_id() == u1_ao_carry_tpsl_order_id) {
        // u1订单回报
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.price_x(), 1000'0000);
        ASSERT_EQ(ord.side(), ESide::Buy);
        ASSERT_EQ(ord.order_type(), EOrderType::Limit);

        ASSERT_EQ(ord.take_profit_x(), 1100'0000);
        ASSERT_EQ(ord.take_profit_limit_x(), 1105'0000);
        ASSERT_EQ(ord.tp_order_type(), EOrderType::Limit);
        ASSERT_EQ(ord.tp_trigger_by(), ETriggerBy::LastPrice);

        ASSERT_EQ(ord.stop_loss_x(), 900'0000);
        ASSERT_EQ(ord.stop_loss_limit_x(), 0);
        ASSERT_EQ(ord.sl_order_type(), EOrderType::Market);
        ASSERT_EQ(ord.sl_trigger_by(), ETriggerBy::MarkPrice);
      }
      if (ord.stop_order_type() == EStopOrderType::PartialTakeProfit) {
        full_tp_ord_id = ord.order_id();
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.price_x(), 1105'0000);
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Limit);
        // 问题： 为什么order_type=Limit，但tp_order_type=Market(保持和建仓是一样的值，说明这个字端只有在建仓是有用？)
        ASSERT_EQ(ord.tp_order_type(), EOrderType::Market);  // 建仓时传承过来，后面因该是用不到了
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::LastPrice);
        ASSERT_EQ(ord.trigger_price_x(), 1100'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Partial);
      }
      if (ord.stop_order_type() == EStopOrderType::PartialStopLoss) {
        full_sl_ord_id = ord.order_id();
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.price_x(), 0);
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.sl_order_type(), EOrderType::Market);  // 建仓时传承过来，后面因该是用不到了
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::MarkPrice);
        ASSERT_EQ(ord.trigger_price_x(), 900'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Partial);
      }
    }

    ASSERT_NE(full_tp_ord_id, "");
    ASSERT_NE(full_sl_ord_id, "");

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#2: result:" << msg << std::endl;
  }

  // user成交回报
  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);

    // user 2
    auto& pz = result.m_msg->futures_margin_result().affected_positions().at(0);
    EXPECT_EQ(pz.user_id(), uid_2);
    EXPECT_EQ(pz.size_x(), 0.1 * 1e8);
    EXPECT_EQ(pz.value_e8(), 0.1 * 1000 * 1e8);
    EXPECT_EQ(pz.side(), ESide::Sell);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#3: result:" << msg << std::endl;
  }

  // openAPI V5, 修改止盈止损
  bool check_ts_so = true;
  bool check_tp_so = true;
  bool check_sl_so = true;
  {
    FutureSetTpSlTsV5Builder set_tp_sl_ts_v5;
    set_tp_sl_ts_v5.SetValue("linear", "XRPUSDT", EPositionIndex::Single, "1100", "500", "1200", "800", "MarkPrice",
                             "IndexPrice", "0.05", "0.05", "Partial", {}, "805", {}, "Limit");
    RpcContext ct{};
    auto resp = user1.set_tp_sl_ts(set_tp_sl_ts_v5.Build(), ct);
    ASSERT_NE(resp, nullptr);
    std::cout << "retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 3);  // ao, tp_so, sl_so

    auto& pz = result.m_msg->futures_margin_result().affected_positions().at(0);
    EXPECT_EQ(pz.user_id(), uid_1);
    EXPECT_EQ(pz.size_x(), 0.1 * 1e8);
    EXPECT_EQ(pz.value_e8(), 0.1 * 1000 * 1e8);
    EXPECT_EQ(pz.side(), ESide::Buy);
    // 下面4个字段，只有 tpsl_mode=Full时才会更新, 所以这里还是建仓时ao带的tpsl信息，建仓时也是Partial, 所以没值
    EXPECT_EQ(pz.take_profit_x(), 0);
    EXPECT_EQ(pz.stop_loss_x(), 0);
    EXPECT_EQ(pz.tp_trigger_by(), ETriggerBy::UNKNOWN);
    EXPECT_EQ(pz.sl_trigger_by(), ETriggerBy::UNKNOWN);
    // 当前场景是Partial的，但试了Full就是下面的结论
    // trading_stop接口，修改止盈止损，即使明确是全部止盈止损，这里也是Unknown （感觉不合理）
    // EXPECT_EQ(pz.tp_sl_mode(), ETpSlMode::Full);

    // 建仓时有Partial的Tpsl保留，这里新增2个部分止盈止损单和一个追踪单
    for (auto& ord : result.m_msg->futures_margin_result().related_orders()) {
      if (ord.stop_order_type() == EStopOrderType::TrailingProfit) {
        check_ts_so = true;
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.side(), ESide::Sell);
        // 追踪只能是市价触发
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::LastPrice);
        // active_px - trail_value 这是第一个触发价格
        ASSERT_EQ(ord.trigger_price_x(), 600'0000);
        ASSERT_EQ(ord.trail_value_x(), 500'0000);
      }
      if (ord.stop_order_type() == EStopOrderType::PartialTakeProfit) {
        check_tp_so = true;
        ASSERT_NE(full_tp_ord_id, ord.order_id());
        ASSERT_EQ(ord.qty_x(), 0.05 * 1e8);
        ASSERT_EQ(ord.price_x(), 0);
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.tp_order_type(), EOrderType::Market);  // 延续建仓时的值，这时没啥用了
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::MarkPrice);
        ASSERT_EQ(ord.trigger_price_x(), 1200'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Partial);
      }
      if (ord.stop_order_type() == EStopOrderType::PartialStopLoss) {
        check_sl_so = true;
        ASSERT_NE(full_sl_ord_id, ord.order_id());
        ASSERT_EQ(ord.qty_x(), 0.05 * 1e8);
        ASSERT_EQ(ord.price_x(), 805'0000);
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Limit);
        ASSERT_EQ(ord.sl_order_type(), EOrderType::Market);  // 延续建仓时的值，这时没啥用了
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::IndexPrice);
        ASSERT_EQ(ord.trigger_price_x(), 800'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Partial);
      }
    }

    ASSERT_EQ(check_ts_so, true);
    ASSERT_EQ(check_tp_so, true);
    ASSERT_EQ(check_sl_so, true);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#4: result:" << msg << std::endl;
  }
}

TEST_F(OpenApiTpslTest, trading_stop_modify_tpslts_partial_append_full) {
  auto coin = ECoin::USDT;  // BTC

  biz::user_id_t uid_1 = 53824923;
  stub user1(te, uid_1);
  {
    // wallet
    std::string amount = "1000000";
    std::string bonus_change = "0";

    // 充值请求
    std::string req_id = "809635e8-e057-4013-9151-73d26a2efd81";
    std::string trans_id = "fcbd25d6-70c7-4345-a7d4-5c6820a63e11";
    int wallet_record_type = 1;

    FutureDepositBuilder u1_deposit_build(req_id, uid_1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto u1_resp = user1.process(u1_deposit_build.Build());
    auto u1_result = te->PopResult();
    auto wallet_check = u1_result.RefAssetMarginResult().RefRelatedWallet(coin);
    wallet_check.CheckCoin(coin).CheckAB(biz::money_t(amount));
  }

  biz::user_id_t uid_2 = 22954332;
  stub user2(te, uid_2);
  {
    // wallet
    std::string amount = "1000000";
    std::string bonus_change = "0";

    // 充值请求
    std::string req_id = "999a9c57-e0e8-47a6-9e49-c14ecb7de7ec";
    std::string trans_id = "09e75bf5-9952-4064-9b9c-fbe0be3b0830";
    int wallet_record_type = 1;

    FutureDepositBuilder u2_deposit_build(req_id, uid_2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto u2_resp = user2.process(u2_deposit_build.Build());
    auto u2_result = te->PopResult();
    auto wallet_check = u2_result.RefAssetMarginResult().RefRelatedWallet(coin);
    wallet_check.CheckCoin(coin).CheckAB(biz::money_t(amount));
  }

  // 校准标记价格
  te->AddMarkPrice(ESymbol::XRPUSDT, 1000, 1000, 1000);

  // 对手user2， 为user1构建持仓
  auto u2_order_id = "675df35d-**************-76b2ced63f1c";
  // openAPI V5, 创建普通活动单，构造异常场景需要先有个其他Symbol的订单， 触发异常时会返回这个symbol的orderid
  {
    OpenApiV5CreateOrderBuilder create_order_builder("linear", "XRPUSDT", EPositionIndex::Single, "Sell", "Limit",
                                                     "0.1", "1000");
    create_order_builder.SetOrderLinkID(u2_order_id);

    RpcContext ct{};
    auto create_ao_resp = user2.create_order(create_order_builder.Build(), ct);
    ASSERT_NE(create_ao_resp, nullptr);

    std::cout << "OpenApi create: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    ASSERT_EQ(create_ao_resp->order_link_id(), u2_order_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(u2_order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#1: result:" << msg << std::endl;
  }

  // u1创建待tpsl的持仓
  auto u1_ao_carry_tpsl_order_id = "249e8033-4235-446c-b54b-a872a96d7c21";
  std::string full_tp_ord_id = "";
  std::string full_sl_ord_id = "";
  // openAPI V5, 创建止盈止损活动单
  {
    OpenApiV5CreateOrderBuilder create_tpsl_order_builder("linear", "XRPUSDT", EPositionIndex::Single, "Buy", "Limit",
                                                          "0.1", "1000");
    create_tpsl_order_builder.SetOrderLinkID(u1_ao_carry_tpsl_order_id);
    create_tpsl_order_builder.SetTpSl("LastPrice", "1100", "MarkPrice", "900");
    create_tpsl_order_builder.AppendLimitTpSl("Partial", "Limit", "1105", "", "");

    RpcContext ct{};
    auto create_tpsl_resp = user1.create_order(create_tpsl_order_builder.Build(), ct);
    ASSERT_NE(create_tpsl_resp, nullptr);

    std::cout << "OpenApi create: retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    ASSERT_EQ(create_tpsl_resp->order_link_id(), u1_ao_carry_tpsl_order_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 3);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);

    // u1成交回报
    auto& pz = result.m_msg->futures_margin_result().affected_positions().at(0);
    EXPECT_EQ(pz.user_id(), uid_1);
    EXPECT_EQ(pz.size_x(), 0.1 * 1e8);
    EXPECT_EQ(pz.value_e8(), 0.1 * 1000 * 1e8);
    EXPECT_EQ(pz.side(), ESide::Buy);
    // Full时，才能设置在下面4个字段上；Partial可以理解成与Pz无关
    EXPECT_EQ(pz.take_profit_x(), 0);
    EXPECT_EQ(pz.stop_loss_x(), 0);
    EXPECT_EQ(pz.tp_trigger_by(), ETriggerBy::UNKNOWN);
    EXPECT_EQ(pz.sl_trigger_by(), ETriggerBy::UNKNOWN);
    // 无论时Full还是Partial，这里都是Unknown （感觉不合理， 起码Full应该有值）
    // EXPECT_EQ(pz.tp_sl_mode(), ETpSlMode::Full);

    for (auto& ord : result.m_msg->futures_margin_result().related_orders()) {
      if (ord.order_link_id() == u1_ao_carry_tpsl_order_id) {
        // u1订单回报
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.price_x(), 1000'0000);
        ASSERT_EQ(ord.side(), ESide::Buy);
        ASSERT_EQ(ord.order_type(), EOrderType::Limit);

        ASSERT_EQ(ord.take_profit_x(), 1100'0000);
        ASSERT_EQ(ord.take_profit_limit_x(), 1105'0000);
        ASSERT_EQ(ord.tp_order_type(), EOrderType::Limit);
        ASSERT_EQ(ord.tp_trigger_by(), ETriggerBy::LastPrice);

        ASSERT_EQ(ord.stop_loss_x(), 900'0000);
        ASSERT_EQ(ord.stop_loss_limit_x(), 0);
        ASSERT_EQ(ord.sl_order_type(), EOrderType::Market);
        ASSERT_EQ(ord.sl_trigger_by(), ETriggerBy::MarkPrice);
      }
      if (ord.stop_order_type() == EStopOrderType::PartialTakeProfit) {
        full_tp_ord_id = ord.order_id();
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.price_x(), 1105'0000);
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Limit);
        // 问题： 为什么order_type=Limit，但tp_order_type=Market(保持和建仓是一样的值，说明这个字端只有在建仓是有用？)
        ASSERT_EQ(ord.tp_order_type(), EOrderType::Market);  // why？？
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::LastPrice);
        ASSERT_EQ(ord.trigger_price_x(), 1100'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Partial);
      }
      if (ord.stop_order_type() == EStopOrderType::PartialStopLoss) {
        full_sl_ord_id = ord.order_id();
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.price_x(), 0);
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.sl_order_type(), EOrderType::Market);  // 建仓时传承过来，后面因该是用不到了
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::MarkPrice);
        ASSERT_EQ(ord.trigger_price_x(), 900'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Partial);
      }
    }

    ASSERT_NE(full_tp_ord_id, "");
    ASSERT_NE(full_sl_ord_id, "");

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#2: result:" << msg << std::endl;
  }

  // user成交回报
  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);

    // user 2
    auto& pz = result.m_msg->futures_margin_result().affected_positions().at(0);
    EXPECT_EQ(pz.user_id(), uid_2);
    EXPECT_EQ(pz.size_x(), 0.1 * 1e8);
    EXPECT_EQ(pz.value_e8(), 0.1 * 1000 * 1e8);
    EXPECT_EQ(pz.side(), ESide::Sell);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#3: result:" << msg << std::endl;
  }

  // openAPI V5, 修改止盈止损
  bool check_ts_so = false;
  bool check_tp_so = false;
  bool check_sl_so = false;
  {
    FutureSetTpSlTsV5Builder set_tp_sl_ts_v5;
    // 默认tpsl_mode=Full; 设置tp/sl_size，但mode不设Partial, 相当于无效; 比如下面这个参数
    set_tp_sl_ts_v5.SetValue("linear", "XRPUSDT", EPositionIndex::Single, "1100", "500", "1200", "800", "MarkPrice",
                             "IndexPrice", "0.05", {}, {}, {}, {}, {}, {});
    RpcContext ct{};
    auto resp = user1.set_tp_sl_ts(set_tp_sl_ts_v5.Build(), ct);
    ASSERT_NE(resp, nullptr);
    std::cout << "retCode:" << ct.RetCode() << ",retMsg:" << ct.RetMsg() << std::endl;
    EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 3);  // ao, tp_so, sl_so

    auto& pz = result.m_msg->futures_margin_result().affected_positions().at(0);
    EXPECT_EQ(pz.user_id(), uid_1);
    EXPECT_EQ(pz.size_x(), 0.1 * 1e8);
    EXPECT_EQ(pz.value_e8(), 0.1 * 1000 * 1e8);
    EXPECT_EQ(pz.side(), ESide::Buy);
    // 下面4个字段，只有 tpsl_mode=Full时才会更新, 所以这里不再是建仓时ao带的tpsl信息
    EXPECT_EQ(pz.take_profit_x(), 1200'0000);
    EXPECT_EQ(pz.stop_loss_x(), 800'0000);
    EXPECT_EQ(pz.tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(pz.sl_trigger_by(), ETriggerBy::IndexPrice);
    // 当前场景是Partial的，但试了Full就是下面的结论
    // trading_stop接口，修改止盈止损，即使明确是全部止盈止损，这里也是Unknown （感觉不合理）
    // EXPECT_EQ(pz.tp_sl_mode(), ETpSlMode::Full);

    // 建仓时有Partial的Tpsl保留，这里新增2个部分止盈止损单和一个追踪单
    for (auto& ord : result.m_msg->futures_margin_result().related_orders()) {
      if (ord.stop_order_type() == EStopOrderType::TrailingProfit) {
        check_ts_so = true;
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.side(), ESide::Sell);
        // 追踪只能是市价触发
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::LastPrice);
        // active_px - trail_value 这是第一个触发价格
        ASSERT_EQ(ord.trigger_price_x(), 600'0000);
        ASSERT_EQ(ord.trail_value_x(), 500'0000);
      }
      if (ord.stop_order_type() == EStopOrderType::TakeProfit) {
        check_tp_so = true;
        ASSERT_NE(full_tp_ord_id, ord.order_id());
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.tp_order_type(), EOrderType::Market);  // 建仓时传承过来，后面因该是用不到了
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::MarkPrice);
        ASSERT_EQ(ord.trigger_price_x(), 1200'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Full);
      }
      if (ord.stop_order_type() == EStopOrderType::StopLoss) {
        check_sl_so = true;
        ASSERT_NE(full_sl_ord_id, ord.order_id());
        ASSERT_EQ(ord.qty_x(), 0.1 * 1e8);
        ASSERT_EQ(ord.side(), ESide::Sell);
        ASSERT_EQ(ord.order_type(), EOrderType::Market);
        ASSERT_EQ(ord.sl_order_type(), EOrderType::Market);  // 建仓时传承过来，后面因该是用不到了
        ASSERT_EQ(ord.trigger_by(), ETriggerBy::IndexPrice);
        ASSERT_EQ(ord.trigger_price_x(), 800'0000);
        ASSERT_EQ(ord.tp_sl_mode(), ETpSlMode::Full);
      }
    }

    ASSERT_EQ(check_ts_so, true);
    ASSERT_EQ(check_tp_so, true);
    ASSERT_EQ(check_sl_so, true);

    std::string msg{};
    (void)google::protobuf::util::MessageToJsonString(result.m_msg->futures_margin_result(), &msg);
    std::cout << "#4: result:" << msg << std::endl;
  }
}

std::shared_ptr<tmock::CTradeAppMock> OpenApiTpslTest::te;
