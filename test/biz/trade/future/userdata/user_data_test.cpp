#include "test/biz/trade/future/userdata/user_data_test.hpp"

#include <memory>
#include <string>

#include "data/type/biz_type.hpp"
#include "models/usersettingdto/spot_user_setting_dto.pb.h"
#include "proto/gen/models/dump/userdata.pb.h"
#include "proto/gen/svc/uta_engine/uta_engine_debug_service.grpc.pb.h"
#include "src/application/global_var_manager.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/store/header.hpp"
#include "src/biz_worker/service/trade/store/per_user_store.hpp"
#include "src/biz_worker/service/trade/store/per_worker_store.hpp"
#include "src/biz_worker/service/trade/store/user_data_manager.hpp"
#include "src/data/event/biz_event.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/mocks/pipeline/result_handler_mock.hpp"

TEST_F(TestUserData, A1) {
  store::UserDataManager ud_mgr;

  const biz::user_id_t uid17 = 999999;
  const biz::user_id_t uid27 = 888888;
  const biz::user_id_t uid37 = 777777;

  application::GlobalVarManager::Instance().sharding_config_storage().AddUserWhiteList(uid17);
  application::GlobalVarManager::Instance().sharding_config_storage().AddUserWhiteList(uid27);
  application::GlobalVarManager::Instance().sharding_config_storage().AddUserWhiteList(uid37);

  ASSERT_EQ(ud_mgr.IsExistUserData(uid17), false);
  ASSERT_EQ(ud_mgr.GetUserData(uid17), nullptr);

  MockResultHandler result_handler{};
  store::PerUserStore* per_user_store{nullptr};
  store::PerWorkerStore per_worker_store;
  std::string msg;
  auto header = std::make_shared<store::Header>();

  auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
  auto symbol_config = std::make_shared<biz::SymbolConfig>();
  symbol_config_mgr->risk_limits_map()[0] = std::make_shared<biz::sc::RiskLimitSet>();
  symbol_config_mgr->risk_limits_map()[0]->Add(std::make_shared<biz::RiskLimitCache>());
  symbol_config_mgr->risk_limits_map()[5] = std::make_shared<biz::sc::RiskLimitSet>();
  symbol_config_mgr->risk_limits_map()[5]->Add(std::make_shared<biz::RiskLimitCache>());
  symbol_config->value_scale_ = 8;
  symbol_config->price_scale_ = 8;
  symbol_config->symbol_ = ESymbol::BTCUSDT;
  symbol_config->value_scale_ = 8;
  symbol_config_mgr->AddSymbolConfig(symbol_config);
  auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
  new_config_mgr->set_symbol_config_mgr(symbol_config_mgr);
  config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

  (*per_worker_store.future_symbol_mark_price_map_)[5] = biz::price_d_t("1200.678");

  // 第一次请求加载
  {
    per_user_store = nullptr;

    auto ev = std::make_shared<event::BizEvent>(uid27, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
                                                event::EventDispatchType::kBroadcastToUser,
                                                event::EventBizType::kCreateOrder);
    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(ev->uid(), ev, header, &result_handler, &per_user_store, &msg), false);
    ASSERT_NE(per_user_store, nullptr);
    ASSERT_EQ(per_user_store->load_status_, ELoadDumpStatus::Loading);
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 1);

    ev = std::make_shared<event::BizEvent>(uid27, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
                                           event::EventDispatchType::kBroadcastToUser,
                                           event::EventBizType::kMarginHdtsRequest);
    header->action = EAction::CreateAccount;
    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(ev->uid(), ev, header, &result_handler, &per_user_store, &msg), false);
    ASSERT_NE(per_user_store, nullptr);
    ASSERT_EQ(per_user_store->load_status_, ELoadDumpStatus::Loading);
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 2);

    ev = std::make_shared<event::BizEvent>(uid27, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
                                           event::EventDispatchType::kBroadcastToUser,
                                           event::EventBizType::kCreateOrder);
    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(ev->uid(), ev, header, &result_handler, &per_user_store, &msg), false);
    ASSERT_NE(per_user_store, nullptr);
    ASSERT_EQ(per_user_store->load_status_, ELoadDumpStatus::Loading);
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 3);
  }

  // 数据回来后 10002(不存在此用户) 有kMarginHdtsRequest请求会自动创建用户数据
  {
    auto ev = std::make_shared<event::LoadUserDataFinishEvent>(uid27, event::EventType::kEventGrpcInput,
                                                               event::EventSourceType::kGrpc,
                                                               event::EventDispatchType::kBroadcastToUser);
    auto rpc_req = std::make_shared<models::dump::UserData>();
    ev->set_data(rpc_req);
    ev->set_ret_code(10002);
    ASSERT_EQ(ud_mgr.LoadUserDataFromEvent(ev, config::getTlsCfgMgrRaw(), &result_handler, &per_worker_store,
                                           &per_user_store, &msg),
              0);
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 2);
  }

  // 第一次请求加载
  {
    auto ev = std::make_shared<event::GrpcBizEvent>(
        uid17, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
        event::EventDispatchType::kBroadcastToUser, event::EventBizType::kCreateOrder,
        event::GrpcSoureType::kGrpcSourceNone, event::GrpcUriType::kGrpcUriNone, event::GrpcContext{});
    auto rpc_req = std::make_shared<svc::trading::TradingReq>();
    ev->set_data(rpc_req);
    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(uid17, ev, header, &result_handler, &per_user_store, &msg), false);
    auto biz_ev = std::dynamic_pointer_cast<event::LoadUserDataEvent>(result_handler.ev_);
    ASSERT_NE(biz_ev, nullptr);
    ASSERT_EQ(biz_ev->uid(), uid17);
    ASSERT_NE(per_user_store, nullptr);
    ASSERT_GT(per_user_store->req_time_ns_, 0);
    ASSERT_EQ(per_user_store->load_status_, ELoadDumpStatus::Loading);
  }

  // 数据回来后 无错误
  {
    auto ev = std::make_shared<event::LoadUserDataFinishEvent>(uid17, event::EventType::kEventGrpcInput,
                                                               event::EventSourceType::kGrpc,
                                                               event::EventDispatchType::kBroadcastToUser);
    auto rpc_req = std::make_shared<models::dump::UserData>();
    rpc_req->set_user_id(uid17);
    (*rpc_req->mutable_wallets())[5] = ::models::walletdto::UnifyWallet();
    (*rpc_req->mutable_future_symbol_data())[5] = ::models::dump::UserData_PerSymbolData();
    /*(*rpc_req->mutable_future_symbol_data())[5].mutable_working_positions()->operator[](0) =
        ::models::tradingdto::PositionDTO();*/
    //    rpc_req->mutable_option_symbol_data()->operator[](5) = ::models::dump::UserData_PerSymbolData();
    //    rpc_req->mutable_option_symbol_data()->operator[](5).mutable_working_positions()->operator[](0) =
    //        ::models::tradingdto::PositionDTO();
    rpc_req->mutable_spot_symbol_data()->operator[](5) = ::models::dump::UserData_PerSymbolData();
    auto pz_dto = ::models::tradingdto::PositionDTO();
    pz_dto.set_user_id(uid17);
    pz_dto.set_side(ESide::Buy);
    pz_dto.set_symbol(ESymbol::BTCUSDT);
    pz_dto.set_size_x(10);
    pz_dto.set_mark_price_x(16000000);
    auto coin_user_setting = ::models::usersettingdto::PerCoinUserSettingDTO();
    coin_user_setting.set_user_id(uid17);
    coin_user_setting.set_coin(ECoin::USDT);
    rpc_req->mutable_per_user_setting_data()->mutable_future_per_coin_setting()->insert({5, coin_user_setting});
    rpc_req->mutable_future_symbol_data()->operator[](5).mutable_working_positions()->insert({5, pz_dto});
    ev->set_data(rpc_req);
    std::string mm{};
    (void)google::protobuf::util::MessageToJsonString(*rpc_req, &mm);
    std::cout << mm << std::endl;
    ASSERT_EQ(ud_mgr.LoadUserDataFromEvent(ev, config::getTlsCfgMgrRaw(), &result_handler, &per_worker_store,
                                           &per_user_store, &msg),
              0);
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 1);
  }

  // 当前用户的新事件来了可以正常加载到用户数据
  {
    per_user_store = nullptr;
    auto ev = std::make_shared<event::BizEvent>(uid17, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
                                                event::EventDispatchType::kBroadcastToUser,
                                                event::EventBizType::kCreateOrder);
    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(ev->uid(), ev, header, &result_handler, &per_user_store, &msg), true);
    ASSERT_NE(per_user_store, nullptr);
    ASSERT_EQ(per_user_store->load_status_, ELoadDumpStatus::Loaded);
  }

  // 数据回来后 10002(不存在此用户)
  {
    auto ev = std::make_shared<event::LoadUserDataFinishEvent>(uid17, event::EventType::kEventGrpcInput,
                                                               event::EventSourceType::kGrpc,
                                                               event::EventDispatchType::kBroadcastToUser);
    auto rpc_req = std::make_shared<models::dump::UserData>();
    ev->set_data(rpc_req);
    ev->set_ret_code(10002);
    // 用户数据已经存在时又收到此用户的数据
    ASSERT_EQ(ud_mgr.LoadUserDataFromEvent(ev, nullptr, &result_handler, &per_worker_store, &per_user_store, &msg), -1);
  }

  // 数据回来后 10002(不存在此用户)
  {
    auto ev = std::make_shared<event::LoadUserDataFinishEvent>(uid37, event::EventType::kEventGrpcInput,
                                                               event::EventSourceType::kGrpc,
                                                               event::EventDispatchType::kBroadcastToUser);
    auto rpc_req = std::make_shared<models::dump::UserData>();
    ev->set_data(rpc_req);
    ev->set_ret_code(10002);
    ASSERT_EQ(ud_mgr.LoadUserDataFromEvent(ev, config::getTlsCfgMgrRaw(), &result_handler, &per_worker_store,
                                           &per_user_store, &msg),
              -1);
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 0);
  }
}

TEST_F(TestUserData, A2) {
  store::UserDataManager ud_mgr;
  const biz::user_id_t uid18 = 888888;

  application::GlobalVarManager::Instance().sharding_config_storage().AddUserWhiteList(uid18);

  MockResultHandler result_handler{};
  store::PerUserStore* per_user_store{nullptr};
  store::PerWorkerStore per_worker_store;
  std::string msg;
  auto header = std::make_shared<store::Header>();

  auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
  auto symbol_config = std::make_shared<biz::SymbolConfig>();
  symbol_config_mgr->risk_limits_map()[0] = std::make_shared<biz::sc::RiskLimitSet>();
  symbol_config_mgr->risk_limits_map()[0]->Add(std::make_shared<biz::RiskLimitCache>());
  symbol_config_mgr->risk_limits_map()[5] = std::make_shared<biz::sc::RiskLimitSet>();
  symbol_config_mgr->risk_limits_map()[5]->Add(std::make_shared<biz::RiskLimitCache>());
  symbol_config->value_scale_ = 8;
  symbol_config->price_scale_ = 8;
  symbol_config->symbol_ = ESymbol::BTCUSDT;
  symbol_config->value_scale_ = 8;
  symbol_config_mgr->AddSymbolConfig(symbol_config);
  auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
  new_config_mgr->set_symbol_config_mgr(symbol_config_mgr);
  config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

  // 第一次请求加载
  {
    auto ev2 = std::make_shared<event::BizEvent>(
        uid18, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
        event::EventDispatchType::kBroadcastToUser, event::EventBizType::kCreateOrder);
    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(ev2->uid(), ev2, header, &result_handler, &per_user_store, &msg), false);

    auto ev1 = std::make_shared<event::BizEvent>(
        uid18, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
        event::EventDispatchType::kBroadcastToUser, event::EventBizType::kMarginHdtsRequest);
    header->action = EAction::CreateAccount;
    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(uid18, ev1, header, &result_handler, &per_user_store, &msg), false);
    ASSERT_EQ(per_user_store->load_status_, ELoadDumpStatus::Loading);

    auto ev = std::make_shared<event::GrpcBizEvent>(
        uid18, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
        event::EventDispatchType::kBroadcastToUser, event::EventBizType::kDeposit,
        event::GrpcSoureType::kGrpcSourceNone, event::GrpcUriType::kGrpcUriNone, event::GrpcContext{});
    auto rpc_req = std::make_shared<svc::trading::TradingReq>();
    ev->set_data(rpc_req);
    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(uid18, ev, header, &result_handler, &per_user_store, &msg), false);
    auto biz_ev = std::dynamic_pointer_cast<event::LoadUserDataEvent>(result_handler.ev_);
    ASSERT_NE(biz_ev, nullptr);
    ASSERT_EQ(biz_ev->uid(), uid18);
    ASSERT_NE(per_user_store, nullptr);
    ASSERT_GT(per_user_store->req_time_ns_, 0);
    ASSERT_EQ(per_user_store->load_status_, ELoadDumpStatus::Loading);
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 3);
  }

  // 数据回来找不到此用户
  {
    auto ev = std::make_shared<event::LoadUserDataFinishEvent>(uid18, event::EventType::kEventGrpcInput,
                                                               event::EventSourceType::kGrpc,
                                                               event::EventDispatchType::kBroadcastToUser);
    ev->set_ret_code(10002);
    ASSERT_EQ(ud_mgr.LoadUserDataFromEvent(ev, config::getTlsCfgMgrRaw(), &result_handler, &per_worker_store,
                                           &per_user_store, &msg),
              0);
    ASSERT_EQ(per_user_store->load_status_, ELoadDumpStatus::Loaded);
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 2);
    ASSERT_EQ(per_user_store->pending_req_queue_.front().second->uid(), uid18);
    ASSERT_EQ(per_user_store->pending_req_queue_.front().second->biz_type(), event::EventBizType::kMarginHdtsRequest);
  }

  // 数据回来 错误码10004
  {
    auto ev = std::make_shared<event::LoadUserDataFinishEvent>(uid18, event::EventType::kEventGrpcInput,
                                                               event::EventSourceType::kGrpc,
                                                               event::EventDispatchType::kBroadcastToUser);
    ev->set_ret_code(10004);
    ASSERT_EQ(ud_mgr.LoadUserDataFromEvent(ev, config::getTlsCfgMgrRaw(), &result_handler, &per_worker_store,
                                           &per_user_store, &msg),
              10004);
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 2);
  }
}

// 场景T1
//
TEST_F(TestUserData, T1) {
  const biz::user_id_t uid0 = 0;
  const biz::user_id_t uid1 = 111111;
  const biz::user_id_t uid2 = 222222;
  const biz::user_id_t default_liq_uid = 1001;
  // const biz::user_id_t uid3 = 333333;

  store::UserDataManager ud_mgr;
  MockResultHandler result_handler{};
  store::PerUserStore* per_user_store{nullptr};
  store::PerWorkerStore per_worker_store;
  std::string msg;
  auto header = std::make_shared<store::Header>();
  header->coin = ECoin::USDT;

  auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
  auto symbol_config = std::make_shared<biz::SymbolConfig>();
  symbol_config_mgr->risk_limits_map()[0] = std::make_shared<biz::sc::RiskLimitSet>();
  symbol_config_mgr->risk_limits_map()[0]->Add(std::make_shared<biz::RiskLimitCache>());
  symbol_config_mgr->risk_limits_map()[5] = std::make_shared<biz::sc::RiskLimitSet>();
  symbol_config_mgr->risk_limits_map()[5]->Add(std::make_shared<biz::RiskLimitCache>());
  symbol_config->value_scale_ = 8;
  symbol_config->price_scale_ = 8;
  symbol_config->symbol_ = ESymbol::BTCUSDT;
  symbol_config->value_scale_ = 8;
  symbol_config_mgr->AddSymbolConfig(symbol_config);
  auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
  new_config_mgr->set_symbol_config_mgr(symbol_config_mgr);
  config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

  // 检查uid0, GrpcBizEvent => 预期报错返回
  {
    auto ev = std::make_shared<event::GrpcBizEvent>(
        uid0, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
        event::EventDispatchType::kBroadcastToUser, event::EventBizType::kCreateOrder,
        event::GrpcSoureType::kGrpcSourceNone, event::GrpcUriType::kGrpcUriNone, event::GrpcContext{});
    auto rpc_req = std::make_shared<svc::trading::TradingReq>();
    ev->set_data(rpc_req);

    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(ev->uid(), ev, header, &result_handler, &per_user_store, &msg), false);
    ASSERT_EQ(msg, "tradingReq.ReqHeader.UserId = 0");

    auto biz_result_ev = std::dynamic_pointer_cast<event::BizResultEvent>(result_handler.ev_);
    ASSERT_NE(biz_result_ev, nullptr);
    const auto& draft_pkg = biz_result_ev->draft_pkg();
    ASSERT_EQ(draft_pkg->GetLastError().error_code_, 30070);
    ASSERT_EQ(draft_pkg->GetLastError().msg_, "tradingReq.ReqHeader.UserId = 0");
  }

  // 检查uid0,BizEvent 不是GRPC请求不会回复GRPC => 预期报错返回
  {
    result_handler.ev_.reset();

    auto ev = std::make_shared<event::BizEvent>(uid0, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
                                                event::EventDispatchType::kBroadcastToUser,
                                                event::EventBizType::kCreateOrder);

    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(ev->uid(), ev, header, &result_handler, &per_user_store, &msg), false);
    ASSERT_EQ(msg, "tradingReq.ReqHeader.UserId = 0");

    auto biz_result_ev = std::dynamic_pointer_cast<event::BizResultEvent>(result_handler.ev_);
    ASSERT_EQ(biz_result_ev, nullptr);
  }

  // 检查uid1, GrpcBizEvent, 设置为强平托管用户 => 预期报错返回
  {
    application::GlobalVarManager::Instance().set_system_liq_user(static_cast<ECoin>(coin_), uid1);

    auto ev = std::make_shared<event::GrpcBizEvent>(
        uid1, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
        event::EventDispatchType::kBroadcastToUser, event::EventBizType::kCreateOrder,
        event::GrpcSoureType::kGrpcSourceNone, event::GrpcUriType::kGrpcUriNone, event::GrpcContext{});
    auto rpc_req = std::make_shared<svc::trading::TradingReq>();
    ev->set_data(rpc_req);

    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(ev->uid(), ev, header, &result_handler, &per_user_store, &msg), false);
    ASSERT_EQ(msg, "user id mismatching");

    auto biz_result_ev = std::dynamic_pointer_cast<event::BizResultEvent>(result_handler.ev_);
    ASSERT_NE(biz_result_ev, nullptr);
    const auto& draft_pkg = biz_result_ev->draft_pkg();
    ASSERT_EQ(draft_pkg->GetLastError().error_code_, 30070);
    ASSERT_EQ(draft_pkg->GetLastError().msg_, "user id mismatching");

    application::GlobalVarManager::Instance().set_system_liq_user(static_cast<ECoin>(coin_), default_liq_uid);
  }

  // 检查uid1,GrpcBizEvent,事件类型，kOnSymbolConfigUpdate不需要加载用户数据  => 预期报错返回
  {
    auto ev = std::make_shared<event::GrpcBizEvent>(
        uid1, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
        event::EventDispatchType::kBroadcastToUser, event::EventBizType::kOnSymbolConfigUpdate,
        event::GrpcSoureType::kGrpcSourceNone, event::GrpcUriType::kGrpcUriNone, event::GrpcContext{});
    auto rpc_req = std::make_shared<svc::trading::TradingReq>();
    ev->set_data(rpc_req);

    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(ev->uid(), ev, header, &result_handler, &per_user_store, &msg), false);
    ASSERT_EQ(msg, "broadcast msg");

    auto biz_result_ev = std::dynamic_pointer_cast<event::BizResultEvent>(result_handler.ev_);
    ASSERT_NE(biz_result_ev, nullptr);
    const auto& draft_pkg = biz_result_ev->draft_pkg();
    ASSERT_EQ(draft_pkg->GetLastError().error_code_, -1);
    ASSERT_EQ(draft_pkg->GetLastError().msg_, "broadcast msg");
  }

  // 检查uid1,GrpcBizEvent,事件类型，kOnMarkPriceUpdate不需要加载用户数据  => 预期报错返回
  {
    auto ev = std::make_shared<event::GrpcBizEvent>(
        uid1, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
        event::EventDispatchType::kBroadcastToUser, event::EventBizType::kOnMarkPriceUpdate,
        event::GrpcSoureType::kGrpcSourceNone, event::GrpcUriType::kGrpcUriNone, event::GrpcContext{});
    auto rpc_req = std::make_shared<svc::trading::TradingReq>();
    ev->set_data(rpc_req);

    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(ev->uid(), ev, header, &result_handler, &per_user_store, &msg), false);
    ASSERT_EQ(msg, "broadcast msg");

    auto biz_result_ev = std::dynamic_pointer_cast<event::BizResultEvent>(result_handler.ev_);
    ASSERT_NE(biz_result_ev, nullptr);
    const auto& draft_pkg = biz_result_ev->draft_pkg();
    ASSERT_EQ(draft_pkg->GetLastError().error_code_, -1);
    ASSERT_EQ(draft_pkg->GetLastError().msg_, "broadcast msg");
  }

  // 检查uid1,GrpcBizEvent,事件类型，kUnloadUserData不需要加载用户数据  => 预期报错返回
  {
    auto ev = std::make_shared<event::GrpcBizEvent>(
        uid1, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
        event::EventDispatchType::kBroadcastToUser, event::EventBizType::kUnloadUserData,
        event::GrpcSoureType::kGrpcSourceNone, event::GrpcUriType::kGrpcUriNone, event::GrpcContext{});
    auto rpc_req = std::make_shared<svc::trading::TradingReq>();
    ev->set_data(rpc_req);

    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(ev->uid(), ev, header, &result_handler, &per_user_store, &msg), false);
    ASSERT_EQ(msg, "user not unload");

    auto biz_result_ev = std::dynamic_pointer_cast<event::BizResultEvent>(result_handler.ev_);
    ASSERT_NE(biz_result_ev, nullptr);
    const auto& draft_pkg = biz_result_ev->draft_pkg();
    ASSERT_EQ(draft_pkg->GetLastError().error_code_, -1);
    ASSERT_EQ(draft_pkg->GetLastError().msg_, "user not unload");
  }

  // 检查uid1,GrpcBizEvent, => 预期正常返回
  {
    msg = "";

    auto ev = std::make_shared<event::GrpcBizEvent>(
        uid1, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
        event::EventDispatchType::kBroadcastToUser, event::EventBizType::kCreateOrder,
        event::GrpcSoureType::kGrpcSourceNone, event::GrpcUriType::kGrpcUriNone, event::GrpcContext{});
    auto rpc_req = std::make_shared<svc::trading::TradingReq>();
    ev->set_data(rpc_req);

    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(ev->uid(), ev, header, &result_handler, &per_user_store, &msg), false);
    ASSERT_EQ(per_user_store->load_status_, ELoadDumpStatus::Loading);
    ASSERT_EQ(msg, "");

    auto biz_ev = std::dynamic_pointer_cast<event::LoadUserDataEvent>(result_handler.ev_);
    ASSERT_NE(biz_ev, nullptr);
    ASSERT_EQ(biz_ev->uid(), uid1);
    ASSERT_NE(per_user_store, nullptr);
    ASSERT_GT(per_user_store->req_time_ns_, 0);
    ASSERT_EQ(per_user_store->load_status_, ELoadDumpStatus::Loading);
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 1);
  }

  return;
  // LoadAndCheckSuccess

  // 将用户uid1, uid2增加到UTA用户列表中
  application::GlobalVarManager::Instance().sharding_config_storage().AddUserWhiteList(uid1);
  application::GlobalVarManager::Instance().sharding_config_storage().AddUserWhiteList(uid2);

  const biz::user_id_t uid18 = 333333;
  // 第一次请求加载
  {
    auto ev2 = std::make_shared<event::BizEvent>(
        uid18, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
        event::EventDispatchType::kBroadcastToUser, event::EventBizType::kCreateOrder);
    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(ev2->uid(), ev2, header, &result_handler, &per_user_store, &msg), false);

    auto ev1 = std::make_shared<event::BizEvent>(
        uid18, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
        event::EventDispatchType::kBroadcastToUser, event::EventBizType::kMarginHdtsRequest);
    header->action = EAction::CreateAccount;
    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(uid18, ev1, header, &result_handler, &per_user_store, &msg), false);
    ASSERT_EQ(per_user_store->load_status_, ELoadDumpStatus::Loading);

    auto ev = std::make_shared<event::GrpcBizEvent>(
        uid18, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
        event::EventDispatchType::kBroadcastToUser, event::EventBizType::kDeposit,
        event::GrpcSoureType::kGrpcSourceNone, event::GrpcUriType::kGrpcUriNone, event::GrpcContext{});
    auto rpc_req = std::make_shared<svc::trading::TradingReq>();
    ev->set_data(rpc_req);
    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(uid18, ev, header, &result_handler, &per_user_store, &msg), false);
    auto biz_ev = std::dynamic_pointer_cast<event::LoadUserDataEvent>(result_handler.ev_);
    ASSERT_NE(biz_ev, nullptr);
    ASSERT_EQ(biz_ev->uid(), uid18);
    ASSERT_NE(per_user_store, nullptr);
    ASSERT_GT(per_user_store->req_time_ns_, 0);
    ASSERT_EQ(per_user_store->load_status_, ELoadDumpStatus::Loading);
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 3);
  }

  // 数据回来找不到此用户
  {
    auto ev = std::make_shared<event::LoadUserDataFinishEvent>(uid18, event::EventType::kEventGrpcInput,
                                                               event::EventSourceType::kGrpc,
                                                               event::EventDispatchType::kBroadcastToUser);
    ev->set_ret_code(10002);
    ASSERT_EQ(ud_mgr.LoadUserDataFromEvent(ev, config::getTlsCfgMgrRaw(), &result_handler, &per_worker_store,
                                           &per_user_store, &msg),
              0);
    ASSERT_EQ(per_user_store->load_status_, ELoadDumpStatus::Loaded);
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 2);
    ASSERT_EQ(per_user_store->pending_req_queue_.front().second->uid(), uid18);
    ASSERT_EQ(per_user_store->pending_req_queue_.front().second->biz_type(), event::EventBizType::kMarginHdtsRequest);
  }

  // 数据回来 错误码10004
  {
    auto ev = std::make_shared<event::LoadUserDataFinishEvent>(uid18, event::EventType::kEventGrpcInput,
                                                               event::EventSourceType::kGrpc,
                                                               event::EventDispatchType::kBroadcastToUser);
    ev->set_ret_code(10004);
    ASSERT_EQ(ud_mgr.LoadUserDataFromEvent(ev, config::getTlsCfgMgrRaw(), &result_handler, &per_worker_store,
                                           &per_user_store, &msg),
              0);
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 2);
  }
}

// 收到数据时返回错误场景
TEST_F(TestUserData, T2) {
  const biz::user_id_t uid1 = 111111;
  // const biz::user_id_t uid2 = 222222;
  // const biz::user_id_t default_liq_uid = 1001;
  // const biz::user_id_t uid3 = 333333;

  store::UserDataManager ud_mgr;
  MockResultHandler result_handler{};
  store::PerUserStore* per_user_store{nullptr};
  store::PerWorkerStore per_worker_store;
  std::string msg;
  auto header = std::make_shared<store::Header>();
  header->coin = ECoin::USDT;

  auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
  auto symbol_config = std::make_shared<biz::SymbolConfig>();
  symbol_config_mgr->risk_limits_map()[0] = std::make_shared<biz::sc::RiskLimitSet>();
  symbol_config_mgr->risk_limits_map()[0]->Add(std::make_shared<biz::RiskLimitCache>());
  symbol_config_mgr->risk_limits_map()[5] = std::make_shared<biz::sc::RiskLimitSet>();
  symbol_config_mgr->risk_limits_map()[5]->Add(std::make_shared<biz::RiskLimitCache>());
  symbol_config->value_scale_ = 8;
  symbol_config->price_scale_ = 8;
  symbol_config->symbol_ = ESymbol::BTCUSDT;
  symbol_config->value_scale_ = 8;
  symbol_config_mgr->AddSymbolConfig(symbol_config);
  auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
  new_config_mgr->set_symbol_config_mgr(symbol_config_mgr);
  config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

  {
    auto ev = std::make_shared<event::CrossPassThroughEvent>(
        uid1, event::EventType::kEventCrossInput, event::EventSourceType::kCross,
        event::EventDispatchType::kUnicastToUser, event::EventBizType::kOnRecvMatchingResult);
    ASSERT_EQ(ud_mgr.LoadAndCheckSuccess(ev->uid(), ev, header, &result_handler, &per_user_store, &msg), false);
  }

  // 数据回来 错误码10004
  {
    auto ev = std::make_shared<event::LoadUserDataFinishEvent>(uid1, event::EventType::kEventGrpcInput,
                                                               event::EventSourceType::kGrpc,
                                                               event::EventDispatchType::kBroadcastToUser);
    ev->set_ret_code(10004);
    ASSERT_DEATH(ud_mgr.LoadUserDataFromEvent(ev, config::getTlsCfgMgrRaw(), &result_handler, &per_worker_store,
                                              &per_user_store, &msg),
                 "");
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 1);
  }

  // 数据回来 错误码10005
  {
    auto ev = std::make_shared<event::LoadUserDataFinishEvent>(uid1, event::EventType::kEventGrpcInput,
                                                               event::EventSourceType::kGrpc,
                                                               event::EventDispatchType::kBroadcastToUser);
    ev->set_ret_code(10005);
    ASSERT_DEATH(ud_mgr.LoadUserDataFromEvent(ev, config::getTlsCfgMgrRaw(), &result_handler, &per_worker_store,
                                              &per_user_store, &msg),
                 "");
    ASSERT_EQ(per_user_store->pending_req_queue_.size(), 1);
  }
}

TEST_F(TestUserData, Debug) {
  biz::user_id_t user_data_move_in_uid = 65653;

  {
    // 1. 账户迁移
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user_data_move_in_uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::UserDataMoveIn);
    auto* user_data =
        unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_userdatamovereq()->mutable_user_data();
    user_data->set_user_id(user_data_move_in_uid);
    user_data->set_account_id(user_data_move_in_uid);
    auto* account_info = user_data->mutable_account_info();
    account_info->set_account_status(::enums::eaccountstatus::Normal);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user_data_move_in_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();

    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "迁移Result:" << jsonStr2 << std::endl;
  }

  {
    DebugUserBuilder user_data_move_in_builder(user_data_move_in_uid);
    stub user_data_move_in_user(te, user_data_move_in_uid);

    auto deposit_resp = user_data_move_in_user.process(user_data_move_in_builder.Build());
  }
}

TEST_F(TestUserData, LoadFromPb) {
  store::UserDataManager user_data_manager;

  const biz::user_id_t uid17 = 999999;

  application::GlobalVarManager::Instance().sharding_config_storage().AddUserWhiteList(uid17);

  MockResultHandler result_handler{};
  store::PerWorkerStore per_worker_store;

  auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
  auto symbol_config = std::make_shared<biz::SymbolConfig>();
  symbol_config_mgr->risk_limits_map()[0] = std::make_shared<biz::sc::RiskLimitSet>();
  symbol_config_mgr->risk_limits_map()[0]->Add(std::make_shared<biz::RiskLimitCache>());
  symbol_config_mgr->risk_limits_map()[5] = std::make_shared<biz::sc::RiskLimitSet>();
  symbol_config_mgr->risk_limits_map()[5]->Add(std::make_shared<biz::RiskLimitCache>());
  symbol_config->value_scale_ = 8;
  symbol_config->price_scale_ = 8;
  symbol_config->coin_ = ECoin::USDT;
  symbol_config->symbol_ = 5;
  symbol_config->value_scale_ = 8;
  symbol_config->cross_idx_ = 5;
  symbol_config_mgr->AddSymbolConfig(symbol_config);
  auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
  new_config_mgr->set_symbol_config_mgr(symbol_config_mgr);
  config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

  (*per_worker_store.future_symbol_mark_price_map_)[5] = biz::price_d_t("1200.678");

  store::PerUserStore* per_user_store = user_data_manager.GetUserData(999999, 999999, true);

  auto user_data = std::make_shared<models::dump::UserData>();
  user_data->set_user_id(999999);
  user_data->set_account_id(999999);
  user_data->mutable_per_user_setting_data()->set_lazy_liq(true);
  auto* user_future_symbol_data = user_data->mutable_future_symbol_data();
  models::dump::UserData::PerSymbolData user_per_symbol_data;
  auto* user_futureu_symbole_positions = user_per_symbol_data.mutable_working_positions();
  models::tradingdto::PositionDTO position_dto;
  position_dto.set_user_id(999999);
  position_dto.set_symbol(static_cast<ESymbol>(symbol_config->symbol_));
  position_dto.set_cross_seq(3356);
  position_dto.set_size_x(10000);
  position_dto.set_side(ESide::Buy);

  (*user_futureu_symbole_positions)[0] = position_dto;
  (*user_future_symbol_data)[symbol_config->symbol_] = user_per_symbol_data;

  // option
  auto option_symbol_config = std::make_shared<config::OptionSymbolService>();
  auto common_config_svc = std::make_shared<config::CommonConfigService>();
  auto precision_data = std::make_shared<config::CommonConfigData>();
  std::unordered_map<std::string, std::string> data;
  data.emplace("orderPrice", "8");
  data.emplace("orderSize", "4");
  precision_data->data.emplace("BTC", data);
  common_config_svc->set_precision_data(precision_data);
  new_config_mgr->set_common_config_svc(common_config_svc);

  auto symbol_full_data = std::make_shared<config::SymbolFullData>();
  auto option_symbol_dto = std::make_shared<config::SymbolDTO>();
  option_symbol_dto->symbol_name = "BTC-31DEC21-40000-C";
  option_symbol_dto->base_coin = "BTC";
  option_symbol_dto->delivery_time = 1841937600;
  option_symbol_dto->id = 102313;
  option_symbol_dto->status = "ONLINE";
  option_symbol_dto->cross_id = 10000;
  option_symbol_dto->settle_coin = "USDC";

  symbol_full_data->symbol_id_map.emplace(option_symbol_dto->id, option_symbol_dto);
  option_symbol_config->set_symbol_full_data(symbol_full_data);
  new_config_mgr->set_option_symbol_svc(option_symbol_config);

  auto* user_option_symbol_data = user_data->mutable_option_symbol_data();
  models::dump::UserData::PerSymbolData user_option_per_symbol_data;
  auto* user_option_symbol_positions = user_option_per_symbol_data.mutable_working_positions();
  models::tradingdto::PositionDTO option_position_dto;
  option_position_dto.set_user_id(999999);
  option_position_dto.set_symbol_name(option_symbol_dto->symbol_name);
  option_position_dto.set_cross_seq(3356);
  option_position_dto.set_price_scale(8);
  option_position_dto.set_qty_scale(4);
  option_position_dto.set_size_x(10000);
  option_position_dto.set_side(ESide::Buy);

  (*user_option_symbol_positions)[option_symbol_dto->id] = option_position_dto;
  (*user_option_symbol_data)[option_symbol_dto->id] = user_option_per_symbol_data;

  user_data->mutable_per_user_setting_data()
      ->mutable_spot_user_setting()
      ->mutable_fiat_taker_fee_rate()
      ->set_fee_rate_e8(1000);
  user_data->mutable_per_user_setting_data()
      ->mutable_spot_user_setting()
      ->mutable_fiat_maker_fee_rate()
      ->set_fee_rate_e8(-1000);
  models::usersettingdto::SpotPerCoinFeeRate spot_coin_fee_rate;
  spot_coin_fee_rate.mutable_taker_fee_rate()->set_fee_rate_e8(2000);
  spot_coin_fee_rate.mutable_maker_fee_rate()->set_fee_rate_e8(-2000);
  user_data->mutable_per_user_setting_data()->mutable_spot_user_setting()->mutable_per_settle_coin_fee_rate()->insert(
      {5, spot_coin_fee_rate});
  auto* account_info = user_data->mutable_account_info();
  account_info->set_account_status(::enums::eaccountstatus::Normal);
  account_info->set_liq_step(static_cast<int64_t>(ELiqStep::DelayLiqWait));
  account_info->set_last_trigger_time(11111);
  user_data_manager.LoadUserDataFromPb(user_data.get(), new_config_mgr.get(), &per_worker_store, &result_handler);
  per_user_store = user_data_manager.GetUserData(999999, 999999);
  ASSERT_NE(per_user_store, nullptr);
  auto delay_liq_trigger_time = per_user_store->cow_liquidation_.get()->Latest()->delay_liq_trigger_time;
  ASSERT_EQ(delay_liq_trigger_time, 11111);

  auto const& per_coin_store = per_user_store->GetPerCoinStore(symbol_config->coin_);
  ASSERT_NE(per_coin_store, nullptr);
  auto per_symbol_store = per_coin_store->working_future_symbols_[symbol_config->symbol_];
  ASSERT_NE(per_symbol_store, nullptr);
  ASSERT_EQ(per_symbol_store->last_applied_cross_seq_, position_dto.cross_seq());
  ASSERT_EQ(per_user_store->cross_seq_map_[symbol_config->cross_idx_], per_symbol_store->last_applied_cross_seq_);

  // option
  auto cur_coin = ECoin::USDC;
  auto const& option_per_coin_store = per_user_store->GetPerCoinStore(cur_coin);
  ASSERT_NE(option_per_coin_store, nullptr);
  auto option_per_symbol_store = option_per_coin_store->working_option_symbols_[option_symbol_dto->id];
  ASSERT_NE(option_per_symbol_store, nullptr);
  ASSERT_EQ(option_per_symbol_store->last_applied_cross_seq_, option_position_dto.cross_seq());
  ASSERT_EQ(per_user_store->cross_seq_map_[option_symbol_dto->cross_id],
            option_per_symbol_store->last_applied_cross_seq_);

  ASSERT_EQ(per_user_store->user_setting_->Latest()->spot_user_setting.fiat_taker_fee_rate->fee_rate_e8, 1000);
  ASSERT_EQ(per_user_store->user_setting_->Latest()->spot_user_setting.fiat_maker_fee_rate->fee_rate_e8, -1000);
  ASSERT_EQ(per_user_store->user_setting_->Latest()
                ->spot_user_setting.per_settle_coin_fee_rate.at(5)
                .taker_fee_rate->fee_rate_e8,
            2000);
  ASSERT_EQ(per_user_store->user_setting_->Latest()
                ->spot_user_setting.per_settle_coin_fee_rate.at(5)
                .maker_fee_rate->fee_rate_e8,
            -2000);

  ASSERT_TRUE(per_user_store->HasFuturePosition());
  ASSERT_TRUE(per_user_store->HasOptionPosition());
  ASSERT_FALSE(per_user_store->HasFutureOccupyCostOrder());
  ASSERT_FALSE(per_user_store->HasOptionOccupyCostOrder());
}

std::shared_ptr<tmock::CTradeAppMock> TestUserData::te;
