#include "test/biz/trade/future/position/set_leverage_test.hpp"

#include <string>

#include "margin_request/event/event.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

std::shared_ptr<tmock::CTradeAppMock> SetLeverageTest::te;

TEST_F(SetLeverageTest, set_leverage) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  biz::symbol_t symbol = 5;  // BTCUSDT
  biz::coin_t coin = 5;
  biz::leverage_e2_t buy_lv = 1500;
  biz::leverage_e2_t sell_lv = 1500;

  FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, uid, buy_lv, sell_lv);
  auto resp1 = user1.process(set_lv_build.Build());
  auto result1 = te->PopResult();
  ASSERT_NE(result1.m_msg.get(), nullptr);
  auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(positionCheck.m_msg, nullptr);
  positionCheck.CheckLv(1500, 6782667, 6790667, 616000, 624000);
}

TEST_F(SetLeverageTest, set_leverage_open_v5) {
  biz::user_id_t uid = 10001;
  stub user1(te, uid);

  OpenApiV5SetLeverageBuilder set_lv_build("linear", "BTCUSDT", "15", "15");
  auto resp = user1.set_leverage(set_lv_build.Build());
  auto result1 = te->PopResult();
  ASSERT_NE(result1.m_msg.get(), nullptr);
  auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(positionCheck.m_msg, nullptr);
  positionCheck.CheckLv(1500, 6782667, 6790667, 616000, 624000);
}

TEST_F(SetLeverageTest, set_leverage_isolated) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  // int32_t wait_time = 300;

  {
    // 账户开户
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::CreateAccount);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "开户Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换逐仓模式
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_margin_mode(true);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "切换逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    OpenApiV5SetLeverageBuilder set_lv_build("linear", "BTCUSDT", "16", "16");
    auto resp = user1.set_leverage(set_lv_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1600, 6366250, 6373750, 616250, 623750);
  }
  {
    // 切换到双仓模式
    SwitchPositionMode switch_position_mode(uid, ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::BothSide);
    auto resp = user1.process(switch_position_mode.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto positionCheck = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "切换持仓模式Result:" << jsonStr2 << std::endl;
    positionCheck.CheckLv(1600, 6366250, 6373750, 616250, 623750).CheckRiskId(1);
  }

  {
    OpenApiV5SetLeverageBuilder set_lv_build("linear", "BTCUSDT", "15", "16");
    auto resp = user1.set_leverage(set_lv_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    // std::cout << "position idx " << positionCheck.m_msg->position_idx << std::endl;
    positionCheck.CheckLv(1500, 6782667, 6790667, 616000, 624000);
    auto positionCheck2 = result1.RefFutureMarginResult().RefRelatedPosition(1);
    ASSERT_NE(positionCheck2.m_msg, nullptr);
    // std::cout << "position idx" << positionCheck2.m_msg->position_idx << std::endl;
    positionCheck2.CheckLv(1600, 6366250, 6373750, 616250, 623750);
  }
}

TEST_F(SetLeverageTest, exception_branch) {
  biz::user_id_t uid = 11111;
  stub user1(te, uid);

  biz::symbol_t symbol = 5;  // BTCUSDT
  biz::coin_t coin = 5;
  biz::leverage_e2_t buy_lv = 80;
  biz::leverage_e2_t sell_lv = 1500;
  {
    FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, uid, buy_lv, sell_lv);
    auto resp = user1.process(set_lv_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeNewLeverageInvalid);
  }

  {
    buy_lv = 1800;  // 恢复正常值, 但是buy lv != sell_lv
    FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, uid, buy_lv, sell_lv);
    auto resp = user1.process(set_lv_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeNewLeverageInvalid);
  }

  {
    buy_lv = 20000;  // 超过风险限额的最大杠杆限制
    sell_lv = 20000;
    FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, uid, buy_lv, sell_lv);
    auto resp = user1.process(set_lv_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeLeverageCannotGtRiskLimitMax);
  }

  {
    // XRPUSDT没有mock
    OpenApiV5SetLeverageBuilder set_lv_build("linear", "XRPUSDT", "15", "16");
    auto resp = user1.set_leverage(set_lv_build.Build());
    ASSERT_NE(resp, nullptr);
    // TODO(gd): 怎么判断返回值?
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }
}

void SetLeverageTest::openAccount(int64_t uid) {
  // 账户开户
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::CreateAccount);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult(1000);
  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  // std::cout << "开户 Result:" << jsonStr2 << std::endl;
}
void SetLeverageTest::depositAccount(int64_t uid, biz::coin_t coin, std::string amount) {
  stub user1(te, uid);
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "123";

  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

  auto resp1 = user1.process(deposit_build.Build());
  auto result1 = te->PopResult();
  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result1.m_msg.get(), &jsonStr2);
  // std::cout << "充值 Result:" << jsonStr2 << std::endl;
}
void SetLeverageTest::switchAccountMode(int64_t uid, EAccountMode accountMode) {
  // 切换逐仓模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_margin_mode(true);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(accountMode);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  result.RefAssetMarginResult().RefRelatedUserSetting().CheckAccountMode(accountMode);
  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  // std::cout << "切换逐仓 Result:" << jsonStr2 << std::endl;
}
void SetLeverageTest::switchBothSide(int64_t uid, EPositionMode positionMode) {
  stub user1(te, uid);
  FutureOneOfSwitchPositionModeBuilder switch_position_mode(ESymbol::BTCUSDT, ECoin::USDT, positionMode, uid);
  auto resp = user1.process(switch_position_mode.Build());
  ASSERT_EQ(0, resp->ret_code());
  auto result = te->PopResult();
  result.RefFutureMarginResult()
      .RefRelatedPosition(0)
      .CheckPzIndex(EPositionIndex::Single)
      .CheckPzMode(EPositionMode::MergedSingle)
      .CheckPzStatus(EPositionStatus::Inactive);
  result.RefFutureMarginResult()
      .RefRelatedPosition(1)
      .CheckPzIndex(EPositionIndex::Buy)
      .CheckPzMode(EPositionMode::BothSide)
      .CheckPzStatus(EPositionStatus::Normal);
  result.RefFutureMarginResult()
      .RefRelatedPosition(2)
      .CheckPzIndex(EPositionIndex::Sell)
      .CheckPzMode(EPositionMode::BothSide)
      .CheckPzStatus(EPositionStatus::Normal);
  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  // std::cout << "切换BothSide Result:" << jsonStr2 << std::endl;
}
void SetLeverageTest::buildBothSidePz(int64_t uid_1) {
  auto wait_time = 300;

  stub user1(te, uid_1);
  // 账户开户
  openAccount(uid_1);
  // 账户充值
  depositAccount(uid_1, ECoin::USDT, "2000");

  // UID2
  biz::user_id_t uid2 = 220000;
  stub user2(te, uid2);
  // 账户开户
  openAccount(uid2);
  // 账户充值
  depositAccount(uid2, ECoin::USDT, "2000");
  // 切换双仓模式
  switchBothSide(uid2, EPositionMode::BothSide);

  // user1 Buy持仓
  // ---------------
  {
    // user1 创建期货Buy订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", EPositionIndex::Buy, "Buy", "Limit", "0.4", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "期货Buy订单Maker Result:" << jsonStr2 << std::endl;
  }

  {
    // user2 创建期货Sell订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", EPositionIndex::Sell, "Sell", "Limit", "0.4", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "期货Sell订单Taker Result:" << jsonStr2 << std::endl;
  }

  {
    // 等待成交回报
    auto result = te->PopResult(wait_time);
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "BuyMaker成交回报 Result:" << jsonStr2 << std::endl;

    auto positionCheck = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckUid(uid_1)
        .CheckPzIndex(EPositionIndex::Buy)
        .CheckLv(1000, ********, ********, 614000, 626000)
        .CheckPb(***********);

    positionCheck = result.RefFutureMarginResult().RefRelatedPosition(1);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckUid(uid_1)
        .CheckPzIndex(EPositionIndex::Sell)
        .CheckLv(1000, ********, ********, 614000, 626000)
        .CheckPb(0);
  }

  // user1 Sell持仓
  // ---------------
  {
    // user1 创建期货Sell订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", EPositionIndex::Sell, "Sell", "Limit", "0.4", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "期货Sell订单Maker Result:" << jsonStr2 << std::endl;
  }

  {
    // user2 创建期货Buy订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", EPositionIndex::Buy, "Buy", "Limit", "0.4", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "期货Buy订单Taker Result:" << jsonStr2 << std::endl;
  }

  {
    // 等待成交回报
    auto result = te->PopResult(wait_time);
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "SellMaker成交回报 Result:" << jsonStr2 << std::endl;

    auto positionCheck = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckUid(uid_1)
        .CheckPzIndex(EPositionIndex::Sell)
        .CheckLv(1000, ********, ********, 614000, 626000)
        .CheckPb(80528000000);

    positionCheck = result.RefFutureMarginResult().RefRelatedPosition(1);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckUid(uid_1)
        .CheckPzIndex(EPositionIndex::Buy)
        .CheckLv(1000, ********, ********, 614000, 626000)
        .CheckPb(***********);
  }
}

// 双逐仓，Buy，Sell都追加过保证金A
// 问题: 调Buy或Sell仓杠杆另外一个仓追加的保证金A 被释放
// 预期: 用户追加的保证金不会因为调杠杆而释放
TEST_F(SetLeverageTest, SetLvCase_1) {
  // UID1
  biz::user_id_t uid1 = 120000;
  stub user1(te, uid1);

  // UID1切换逐仓
  switchAccountMode(uid1, EAccountMode::Isolated);

  // 切换双仓模式
  switchBothSide(uid1, EPositionMode::BothSide);

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  // 构建双仓
  buildBothSidePz(uid1);

  {
    // Buy仓追加保证金
    AddMargin add_margin(uid1, ESymbol::BTCUSDT, ECoin::USDT, EPositionIndex::Buy, **********);
    auto resp = user1.process(add_margin.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "Buy仓追加保证金Result:" << jsonStr2 << std::endl;
    auto positionCheck = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckPzIndex(EPositionIndex::Buy)
        .CheckLv(1000, ********, ********, 614000, 626000)
        .CheckPb(***********);
  }

  // 调整单个持仓杠杆，观察另外一个持仓保证金是否被释放
  // Sell lv 10->20, Buy lv不变（10）
  OpenApiV5SetLeverageBuilder set_lv_build("linear", "BTCUSDT", "10", "15");
  RpcContext ct{};
  auto resp = user1.set_leverage(set_lv_build.Build(), ct);
  ASSERT_EQ(ct.RetCode(), error::ErrorCode::kErrorCodeSuccess);
  ASSERT_EQ(ct.RetMsg(), "OK");
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  // std::cout << "调整Sell持仓杠杆Result:" << jsonStr2 << std::endl;

  for (int idx = 0; idx < 2; idx++) {
    auto positionCheck = result.RefFutureMarginResult().RefRelatedPosition(idx);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    // std::cout << "position idx " << positionCheck.m_msg->position_idx() << std::endl;

    if (positionCheck.m_msg->position_idx() == EPositionIndex::Buy) {
      positionCheck.CheckLv(1000, ********, ********, 614000, 626000);
      // 检查追加的mm没有因为调Sell仓杠杆减少
      // 不希望的: pb回退到了追加保证金之前的值【***********】
      // 根本原因: 杠杆没变时也调用SetLeverageBiz::TrySetLeverage中的RefreshCostBiz::RefreshPositionBalance导致
      // 修复方式: SetLeverageBiz::TrySetLeverage检查杠杆变化时才刷新
      // 代码位置: src/biz_worker/service/trade/futures/modules/positionbiz/set_leverage_biz.cpp:26
      positionCheck.CheckPb(***********);
    }
    if (positionCheck.m_msg->position_idx() == EPositionIndex::Sell) {
      positionCheck.CheckLv(1500, 6782667, 6790667, 616000, 624000);
      // 检查追加的mm没有因为调Sell仓杠杆减少
      positionCheck.CheckPb(53845336000);
    }
  }
}
