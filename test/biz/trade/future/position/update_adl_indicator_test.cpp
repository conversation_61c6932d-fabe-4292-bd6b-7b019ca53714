#include "test/biz/trade/future/position/update_adl_indicator_test.hpp"

#include "enums/ecrosslockedmode/cross_locked_mode.pb.h"
#include "lib/msg_builder.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/store/per_user_store.hpp"
#include "src/biz_worker/service/trade/store/per_worker_store.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "src/data/error/error.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

#ifdef __APPLE__
#define CONCAT_STR(x, y) x##_##y
#define MACRO_CONCAT(x, y) CONCAT_STR(x, y)
// #define CheckPbInDebug(rsp) auto MACRO_CONCAT(tmp, __COUNTER__) = utils::PbMessageToJsonString(rsp);
#define CheckPbInDebug(rsp) std::cerr << __LINE__ << ": " << utils::PbMessageToJsonString(rsp) << std::endl;
#else
#define CheckPbInDebug(rsp)
#endif

std::shared_ptr<tmock::CTradeAppMock> TestUpdateAdlIndicator::te;

void TestUpdateAdlIndicator::SetUpTestSuite() {
  te = std::make_shared<tmock::CTradeAppMock>();
  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

  te->Init(argument_count, argument_arrary);
  te->Start();
}

void TestUpdateAdlIndicator::TearDownTestSuite() {
  auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
  std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
  if (duration_us > 0 && duration_us < 2e6) {
    usleep(2e6 - duration_us);
  }
#endif
  te->Stop(true);
  bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
  te.reset();
}

namespace {
struct RespResult {
  std::string order_id;
  std::shared_ptr<svc::trading::TradingResp> r_rsp;
  UnifiedV2ResultDTOChecker h_rsp{nullptr};
};

void SetUserAccountModeAndPositionMode(stub& user) {
  tmock::CTradeAppMock* te = user.m_te.get();

  // 切换账户模式为全仓
  {
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user.m_uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(enums::eaction::Action::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        enums::eaccountmode::AccountMode::Cross);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user.m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  // 设置仓位模式为双仓
  {
    FutureOneOfSwitchPositionModeBuilder switch_position_mode(ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::BothSide,
                                                              user.m_uid);
    auto resp = user.process(switch_position_mode.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }
}

// void SetUserAccountModeAndPositionMode(stub& user, biz::symbol_t symbol, biz::coin_t coin,
//                                        enums::eaccountmode::AccountMode account_mode, EPositionMode position_mode) {
//   tmock::CTradeAppMock* te = user.m_te.get();
//
//   // 切换账户模式
//   {
//     auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
//     unified_v_2_request_dto->mutable_req_header()->set_user_id(user.m_uid);
//     unified_v_2_request_dto->mutable_req_header()->set_action(enums::eaction::Action::SwitchMarginMode);
//     unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(account_mode);
//     auto event = std::make_shared<event::MarginHdtsRequestEvent>(user.m_uid, unified_v_2_request_dto, -1);
//     te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
//     auto result = te->PopResult();
//     ASSERT_NE(result.m_msg.get(), nullptr);
//   }
//
//   // 设置仓位模式
//   {
//     FutureOneOfSwitchPositionModeBuilder switch_position_mode(symbol, coin, position_mode, user.m_uid);
//     auto resp = user.process(switch_position_mode.Build());
//     if (resp->ret_code() != error::ErrorCode::kErrorCodePositionModeNotModified) {
//       ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
//       auto result = te->PopResult();
//       ASSERT_NE(result.m_msg.get(), nullptr);
//     }
//   }
// }

void DepositToUser(stub& user, const std::string& amount) {
  const std::string req_id = user.m_te->GenUUID();
  const std::string trans_id = user.m_te->GenUUID();
  constexpr biz::coin_t coin = 5;  // 这里的硬编码值可以根据需要调整
  constexpr int wallet_record_type = 1;

  FutureDepositBuilder deposit_build(req_id, user.m_uid, trans_id, coin, wallet_record_type, amount, "0");
  const auto resp = user.process(deposit_build.Build());
  ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
  const auto result = user.m_te->PopResult();
  ASSERT_NE(result.m_msg, nullptr);
}

int32_t CreateLimitOrder(stub& user, EPositionIndex pz_index, ESide side, double qty, double price,
                         RespResult* res = nullptr) {
  auto order_id = user.m_te->GenUUID();
  const biz::coin_t coin = 5;      // 这里的硬编码值可以根据需要调整
  const biz::symbol_t symbol = 5;  // BTCUSDT
  const EOrderType order_type = EOrderType::Limit;

  FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty * 1e8, price * 1e4,
                                             ETimeInForce::GoodTillCancel, user.m_uid, coin);
  auto resp = user.process(create_build.Build());

  if (resp->ret_code() != error::ErrorCode::kErrorCodeSuccess) {
    if (res) {
      res->order_id = order_id;
      res->r_rsp = resp;
      res->h_rsp.m_msg.reset();
    }
    return resp->ret_code();
  }

  auto result = user.m_te->PopResult();
  EXPECT_NE(result.m_msg.get(), nullptr);

  if (res) {
    res->order_id = order_id;
    res->r_rsp = resp;
    res->h_rsp = result;
  }

  return resp->ret_code();
}
}  // namespace

TEST_F(TestUpdateAdlIndicator, T1) {
  stub u1(te, 10000);
  stub u2(te, 10002);

  auto symbol = ESymbol::BTCUSDT;

  SetUserAccountModeAndPositionMode(u1);
  SetUserAccountModeAndPositionMode(u2);

  DepositToUser(u1, "*********");
  DepositToUser(u2, "*********");

  te->AddMarkPrice(symbol, 40000, 40000, 40000);

  // 没有仓位时 更新全仓双仓中u1 buy 4
  {
    UpdateAdlRankIndicatorBuilder bld(u1.m_uid, ECoin::USDT, symbol, EPositionIndex::Buy, 4);
    auto resp = u1.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    auto result = u1.m_te->PopResult();
    ASSERT_EQ(resp->ret_code(), 0);
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    // 矮仓位 传入4但被修改为0
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 0);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 0);
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // 构造仓位
  {
    int hhh;
    (void)hhh;
    // u1 全仓双向下sell单 5 * 40000
    {
      RespResult res{};
      auto ret = CreateLimitOrder(u1, EPositionIndex::Sell, ESide::Sell, 5, 40000, &res);
      ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
      CheckPbInDebug(*res.h_rsp.m_msg);
      CheckPbInDebug(*res.r_rsp);

      ASSERT_EQ(res.r_rsp->result().affected_positions_size(), 2);
      const auto& rpc_pz = res.r_rsp->result().affected_positions(0);
      ASSERT_EQ(rpc_pz.position_idx(), EPositionIndex::Sell);

      ASSERT_EQ(res.r_rsp->result().related_orders_size(), 1);
      const auto& rpc_related_order = res.r_rsp->result().related_orders(0);
      ASSERT_EQ(rpc_related_order.qty_x(), 5 * 1e8);
      ASSERT_EQ(rpc_related_order.price_x(), static_cast<int64_t>(rpc_related_order.last_price_x() * 1.0));

      ASSERT_EQ(res.h_rsp.m_msg->futures_margin_result().affected_positions_size(), 2);
    }

    // u2 全仓双向下buy单 5 * 40000
    {
      RespResult res{};
      auto ret = CreateLimitOrder(u2, EPositionIndex::Buy, ESide::Buy, 5, 40000, &res);
      ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
      CheckPbInDebug(*res.h_rsp.m_msg);
      CheckPbInDebug(*res.r_rsp);

      ASSERT_EQ(res.r_rsp->result().affected_positions_size(), 2);
      // position_idx == 1
      const auto& rpc_pz = res.r_rsp->result().affected_positions(0);
      ASSERT_EQ(rpc_pz.position_idx(), EPositionIndex::Buy);

      ASSERT_EQ(res.r_rsp->result().related_orders_size(), 1);
      const auto& rpc_related_order = res.r_rsp->result().related_orders(0);
      ASSERT_EQ(rpc_related_order.qty_x(), 5 * 1e8);
      ASSERT_EQ(rpc_related_order.price_x(), static_cast<int64_t>(rpc_related_order.last_price_x() * 1.0));

      ASSERT_EQ(res.h_rsp.m_msg->futures_margin_result().affected_positions_size(), 2);
    }

    { CheckPbInDebug(*u1.m_te->PopResult().m_msg); }
  }

  // 更新双仓中size>0的sell仓位 1
  {
    UpdateAdlRankIndicatorBuilder bld(u1.m_uid, ECoin::USDT, symbol, EPositionIndex::Sell, 1);
    auto resp = u1.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    auto result = u1.m_te->PopResult();
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 1);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 0);
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // 更新双仓中size>0的sell仓位 3
  {
    UpdateAdlRankIndicatorBuilder bld(u1.m_uid, ECoin::USDT, symbol, EPositionIndex::Sell, 3);
    auto resp = u1.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    auto result = u1.m_te->PopResult();
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 3);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 0);
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // 更新双仓中size==0的buy仓位 3
  {
    UpdateAdlRankIndicatorBuilder bld(u1.m_uid, ECoin::USDT, symbol, EPositionIndex::Buy, 3);
    auto resp = u1.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    auto result = u1.m_te->PopResult();
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 0);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 3);
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // 构造对锁仓位 u1 buy 3, sell 5, u2 sell 3, buy 5
  {
    int hhh;
    (void)hhh;
    // u1 全仓双向下buy单 3 * 40000
    {
      RespResult res{};
      auto ret = CreateLimitOrder(u1, EPositionIndex::Buy, ESide::Buy, 3, 40000, &res);
      ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
      CheckPbInDebug(*res.h_rsp.m_msg);
      CheckPbInDebug(*res.r_rsp);

      ASSERT_EQ(res.r_rsp->result().affected_positions_size(), 2);
      const auto& rpc_pz = res.r_rsp->result().affected_positions(0);
      ASSERT_EQ(rpc_pz.position_idx(), EPositionIndex::Buy);

      ASSERT_EQ(res.r_rsp->result().related_orders_size(), 1);
      const auto& rpc_related_order = res.r_rsp->result().related_orders(0);
      ASSERT_EQ(rpc_related_order.qty_x(), 3 * 1e8);
      ASSERT_EQ(rpc_related_order.price_x(), static_cast<int64_t>(rpc_related_order.last_price_x() * 1.0));

      ASSERT_EQ(res.h_rsp.m_msg->futures_margin_result().affected_positions_size(), 2);
    }

    // u2 全仓双向下sell单 3 * 40000
    {
      RespResult res{};
      auto ret = CreateLimitOrder(u2, EPositionIndex::Sell, ESide::Sell, 3, 40000, &res);
      ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
      CheckPbInDebug(*res.h_rsp.m_msg);
      CheckPbInDebug(*res.r_rsp);

      ASSERT_EQ(res.r_rsp->result().affected_positions_size(), 2);
      // position_idx == 1
      const auto& rpc_pz = res.r_rsp->result().affected_positions(0);
      ASSERT_EQ(rpc_pz.position_idx(), EPositionIndex::Sell);

      ASSERT_EQ(res.r_rsp->result().related_orders_size(), 1);
      const auto& rpc_related_order = res.r_rsp->result().related_orders(0);
      ASSERT_EQ(rpc_related_order.qty_x(), 3 * 1e8);
      ASSERT_EQ(rpc_related_order.price_x(), static_cast<int64_t>(rpc_related_order.last_price_x() * 1.0));

      ASSERT_EQ(res.h_rsp.m_msg->futures_margin_result().affected_positions_size(), 2);
    }

    { CheckPbInDebug(*u1.m_te->PopResult().m_msg); }
  }

  // 更新全仓双仓中u1 buy 1
  {
    UpdateAdlRankIndicatorBuilder bld(u1.m_uid, ECoin::USDT, symbol, EPositionIndex::Buy, 1);
    auto resp = u1.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    auto result = u1.m_te->PopResult();
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 0);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 2);
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // 更新全仓双仓中u1 buy 4
  {
    UpdateAdlRankIndicatorBuilder bld(u1.m_uid, ECoin::USDT, symbol, EPositionIndex::Buy, 4);
    auto resp = u1.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    auto result = u1.m_te->PopResult();
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    // 矮仓位 传入4但被修改为0
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 0);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 2);
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // 更新全仓双仓中u2 buy 1
  {
    UpdateAdlRankIndicatorBuilder bld(u2.m_uid, ECoin::USDT, symbol, EPositionIndex::Buy, 1);
    auto resp = u2.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 1);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 0);
    auto result = u2.m_te->PopResult();
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // 更新全仓双仓中u2 buy 4
  {
    UpdateAdlRankIndicatorBuilder bld(u2.m_uid, ECoin::USDT, symbol, EPositionIndex::Buy, 4);
    auto resp = u2.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    // 矮仓位 传入4但被修改为0
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 4);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 0);
    auto result = u2.m_te->PopResult();
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // positionIdx 填错 预期失败
  {
    UpdateAdlRankIndicatorBuilder bld(u2.m_uid, ECoin::USDT, symbol, EPositionIndex::Buy, 4);
    auto req = bld.Build();
    req.mutable_update_adl_rank_indicator()->set_position_idx(5);
    auto resp = u2.process(req);
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodePositionNotExists);
  }
}

TEST_F(TestUpdateAdlIndicator, T2) {
  stub u1(te, 10000);
  stub u2(te, 10002);

  auto symbol = ESymbol::BTCUSDT;

  SetUserAccountModeAndPositionMode(u1);
  SetUserAccountModeAndPositionMode(u2);

  DepositToUser(u1, "*********");
  DepositToUser(u2, "*********");

  te->AddMarkPrice(symbol, 40000, 40000, 40000);

  // 没有仓位时 更新全仓双仓中u1 buy 4
  {
    UpdateAdlRankIndicatorBuilder bld(u1.m_uid, ECoin::USDT, symbol, EPositionIndex::Buy, 4);
    auto resp = u1.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    auto result = u1.m_te->PopResult();
    ASSERT_EQ(resp->ret_code(), 0);
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    // 矮仓位 传入4但被修改为0
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 0);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 0);
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // 构造仓位
  {  // u1 全仓双向下sell单 5 * 40000
    int hhh;
    (void)hhh;
    {
      RespResult res{};
      auto ret = CreateLimitOrder(u1, EPositionIndex::Sell, ESide::Sell, 5, 40000, &res);
      ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
      CheckPbInDebug(*res.h_rsp.m_msg);
      CheckPbInDebug(*res.r_rsp);

      ASSERT_EQ(res.r_rsp->result().affected_positions_size(), 2);
      const auto& rpc_pz = res.r_rsp->result().affected_positions(0);
      ASSERT_EQ(rpc_pz.position_idx(), EPositionIndex::Sell);

      ASSERT_EQ(res.r_rsp->result().related_orders_size(), 1);
      const auto& rpc_related_order = res.r_rsp->result().related_orders(0);
      ASSERT_EQ(rpc_related_order.qty_x(), 5 * 1e8);
      ASSERT_EQ(rpc_related_order.price_x(), static_cast<int64_t>(rpc_related_order.last_price_x() * 1.0));

      ASSERT_EQ(res.h_rsp.m_msg->futures_margin_result().affected_positions_size(), 2);
    }

    // u2 全仓双向下buy单 5 * 40000
    {
      RespResult res{};
      auto ret = CreateLimitOrder(u2, EPositionIndex::Buy, ESide::Buy, 5, 40000, &res);
      ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
      CheckPbInDebug(*res.h_rsp.m_msg);
      CheckPbInDebug(*res.r_rsp);

      ASSERT_EQ(res.r_rsp->result().affected_positions_size(), 2);
      // position_idx == 1
      const auto& rpc_pz = res.r_rsp->result().affected_positions(0);
      ASSERT_EQ(rpc_pz.position_idx(), EPositionIndex::Buy);

      ASSERT_EQ(res.r_rsp->result().related_orders_size(), 1);
      const auto& rpc_related_order = res.r_rsp->result().related_orders(0);
      ASSERT_EQ(rpc_related_order.qty_x(), 5 * 1e8);
      ASSERT_EQ(rpc_related_order.price_x(), static_cast<int64_t>(rpc_related_order.last_price_x() * 1.0));

      ASSERT_EQ(res.h_rsp.m_msg->futures_margin_result().affected_positions_size(), 2);
    }

    { CheckPbInDebug(*u1.m_te->PopResult().m_msg); }
  }

  // 更新双仓中size>0的sell仓位 1
  {
    UpdateAdlRankIndicatorBuilder bld(u1.m_uid, ECoin::USDT, symbol, EPositionIndex::Sell, 1);
    auto resp = u1.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    auto result = u1.m_te->PopResult();
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 1);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 0);
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // 更新双仓中size>0的sell仓位 3
  {
    UpdateAdlRankIndicatorBuilder bld(u1.m_uid, ECoin::USDT, symbol, EPositionIndex::Sell, 3);
    auto resp = u1.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    auto result = u1.m_te->PopResult();
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 3);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 0);
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // 更新双仓中size==0的buy仓位 3
  {
    UpdateAdlRankIndicatorBuilder bld(u1.m_uid, ECoin::USDT, symbol, EPositionIndex::Buy, 3);
    auto resp = u1.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    auto result = u1.m_te->PopResult();
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 0);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 3);
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // 构造对锁仓位 u1 buy 3, sell 5, u2 sell 3, buy 5
  {  // u1 全仓双向下buy单 3 * 40000
    int hhh;
    (void)hhh;
    {
      RespResult res{};
      auto ret = CreateLimitOrder(u1, EPositionIndex::Buy, ESide::Buy, 3, 40000, &res);
      ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
      CheckPbInDebug(*res.h_rsp.m_msg);
      CheckPbInDebug(*res.r_rsp);

      ASSERT_EQ(res.r_rsp->result().affected_positions_size(), 2);
      const auto& rpc_pz = res.r_rsp->result().affected_positions(0);
      ASSERT_EQ(rpc_pz.position_idx(), EPositionIndex::Buy);

      ASSERT_EQ(res.r_rsp->result().related_orders_size(), 1);
      const auto& rpc_related_order = res.r_rsp->result().related_orders(0);
      ASSERT_EQ(rpc_related_order.qty_x(), 3 * 1e8);
      ASSERT_EQ(rpc_related_order.price_x(), static_cast<int64_t>(rpc_related_order.last_price_x() * 1.0));

      ASSERT_EQ(res.h_rsp.m_msg->futures_margin_result().affected_positions_size(), 2);
    }

    // u2 全仓双向下sell单 3 * 40000
    {
      RespResult res{};
      auto ret = CreateLimitOrder(u2, EPositionIndex::Sell, ESide::Sell, 3, 40000, &res);
      ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
      CheckPbInDebug(*res.h_rsp.m_msg);
      CheckPbInDebug(*res.r_rsp);

      ASSERT_EQ(res.r_rsp->result().affected_positions_size(), 2);
      // position_idx == 1
      const auto& rpc_pz = res.r_rsp->result().affected_positions(0);
      ASSERT_EQ(rpc_pz.position_idx(), EPositionIndex::Sell);

      ASSERT_EQ(res.r_rsp->result().related_orders_size(), 1);
      const auto& rpc_related_order = res.r_rsp->result().related_orders(0);
      ASSERT_EQ(rpc_related_order.qty_x(), 3 * 1e8);
      ASSERT_EQ(rpc_related_order.price_x(), static_cast<int64_t>(rpc_related_order.last_price_x() * 1.0));

      ASSERT_EQ(res.h_rsp.m_msg->futures_margin_result().affected_positions_size(), 2);
    }

    { CheckPbInDebug(*u1.m_te->PopResult().m_msg); }
  }

  // 更新全仓双仓中u1 buy 1
  {
    UpdateAdlRankIndicatorBuilder bld(u1.m_uid, ECoin::USDT, symbol, EPositionIndex::Buy, 1);
    auto resp = u1.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    auto result = u1.m_te->PopResult();
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 0);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 2);
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // 更新全仓双仓中u1 buy 4
  {
    UpdateAdlRankIndicatorBuilder bld(u1.m_uid, ECoin::USDT, symbol, EPositionIndex::Buy, 4);
    auto resp = u1.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    auto result = u1.m_te->PopResult();
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    // 矮仓位 传入4但被修改为0
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 0);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 2);
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // 更新全仓双仓中u2 buy 1
  {
    UpdateAdlRankIndicatorBuilder bld(u2.m_uid, ECoin::USDT, symbol, EPositionIndex::Buy, 1);
    auto resp = u2.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 1);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 0);
    auto result = u2.m_te->PopResult();
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // 更新全仓双仓中u2 buy 4
  {
    UpdateAdlRankIndicatorBuilder bld(u2.m_uid, ECoin::USDT, symbol, EPositionIndex::Buy, 4);
    auto resp = u2.process(bld.Build());
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    ASSERT_EQ(resp->result().affected_positions().size(), 2);
    // 矮仓位 传入4但被修改为0
    EXPECT_EQ(resp->result().affected_positions(0).adl_rank_indicator(), 4);
    EXPECT_EQ(resp->result().affected_positions(1).adl_rank_indicator(), 0);
    auto result = u2.m_te->PopResult();
    EXPECT_NE(result.m_msg.get(), nullptr);
  }

  // positionIdx 填错 预期失败
  {
    UpdateAdlRankIndicatorBuilder bld(u2.m_uid, ECoin::USDT, symbol, EPositionIndex::Buy, 4);
    auto req = bld.Build();
    req.mutable_update_adl_rank_indicator()->set_position_idx(5);
    auto resp = u2.process(req);
    ASSERT_NE(resp, nullptr);
    CheckPbInDebug(resp->result());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodePositionNotExists);
  }

  // 平掉对锁仓位 u1 buy 3, sell 5, u2 sell 3, buy 5
  {
    // u1 全仓双向下buy单 3 * 40000
    {
      RespResult res{};
      auto ret = CreateLimitOrder(u1, EPositionIndex::Buy, ESide::Sell, 3, 40000, &res);
      ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
      CheckPbInDebug(*res.h_rsp.m_msg);
      CheckPbInDebug(*res.r_rsp);

      ASSERT_EQ(res.r_rsp->result().affected_positions_size(), 2);
      const auto& rpc_pz = res.r_rsp->result().affected_positions(0);
      ASSERT_EQ(rpc_pz.position_idx(), EPositionIndex::Buy);

      ASSERT_EQ(res.r_rsp->result().related_orders_size(), 1);
      const auto& rpc_related_order = res.r_rsp->result().related_orders(0);
      ASSERT_EQ(rpc_related_order.qty_x(), 3 * 1e8);
      ASSERT_EQ(rpc_related_order.price_x(), static_cast<int64_t>(rpc_related_order.last_price_x() * 1.0));

      ASSERT_EQ(res.h_rsp.m_msg->futures_margin_result().affected_positions_size(), 2);
    }

    // u2 全仓双向下sell单 3 * 40000
    {
      RespResult res{};
      auto ret = CreateLimitOrder(u2, EPositionIndex::Sell, ESide::Buy, 3, 40000, &res);
      ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
      CheckPbInDebug(*res.h_rsp.m_msg);
      CheckPbInDebug(*res.r_rsp);

      ASSERT_EQ(res.r_rsp->result().affected_positions_size(), 2);
      // position_idx == 1
      const auto& rpc_pz = res.r_rsp->result().affected_positions(0);
      ASSERT_EQ(rpc_pz.position_idx(), EPositionIndex::Sell);

      ASSERT_EQ(res.r_rsp->result().related_orders_size(), 1);
      const auto& rpc_related_order = res.r_rsp->result().related_orders(0);
      ASSERT_EQ(rpc_related_order.qty_x(), 3 * 1e8);
      ASSERT_EQ(rpc_related_order.price_x(), static_cast<int64_t>(rpc_related_order.last_price_x() * 1.0));

      ASSERT_EQ(res.h_rsp.m_msg->futures_margin_result().affected_positions_size(), 2);
    }

    {
      auto msg = *u1.m_te->PopResult().m_msg;
      CheckPbInDebug(msg);
      /*ASSERT_EQ(msg.futures_margin_result().affected_positions_size(), 2);
      ASSERT_EQ(msg.futures_margin_result().affected_positions(0).size_x(), 0);
      ASSERT_EQ(msg.futures_margin_result().affected_positions(0).adl_rank_indicator(), 0);
      ASSERT_EQ(msg.futures_margin_result().affected_positions(1).size_x(), 500000000);
      ASSERT_EQ(msg.futures_margin_result().affected_positions(1).adl_rank_indicator(), 2);*/
    }
  }
}
