#include "test/biz/trade/future/position/fee_rate_tag_id_update_test.hpp"

#include <unistd.h>

#include <bbase/common/decimal/decimal.hpp>
#include <bbase/common/object_manager/object_type.hpp>

#include "data/type/biz_type.hpp"
#include "lib/msg_builder.hpp"
#include "src/config/config_proxy.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/future/func/draft_pkg_builder.h"

std::shared_ptr<tmock::CTradeAppMock> FeeRateTagIdUpdateTest::te;

void FeeRateTagIdUpdateTest::InitFeeRate(const char* FeeRateConfig) {
  auto config_center = bbase::object_manager::ObjectManager::GetObject<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  auto nacos_client = std::dynamic_pointer_cast<bbase::nacos_client::NacosClient>(config_center);
  nacos_client->SetContent(config::ConstConfig::FEERATE_DATA_ID, config::ConstConfig::FEERATE_GROUP, FeeRateConfig);

  auto ret = application::GlobalVarManager::Instance().Init();
  ASSERT_EQ(ret, 0);
}

int32_t FeeRateTagIdUpdateTest::SwitchMarginMode(stub& user, enums::eaccountmode::AccountMode account_mode) {
  // stub user(PmTest::te, uid);
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(user.m_uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(enums::eaction::Action::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(account_mode);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(user.m_uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }
  return 0;
}

void FeeRateTagIdUpdateTest::UpdateFeeRateTagId(ESymbol symbol, const int new_fee_rate_tag_id) {
  auto sc_ev = std::make_shared<event::OnUpdateConfigEvent>(0, event::EventType::kEventSymbolConfig,
                                                            event::EventSourceType::kUnknown,
                                                            event::EventDispatchType::kBroadcastToWorker);
  // init symbol config manager
  config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr()->InitMockData();
  config::getTlsCfgMgrRaw()->spot_client_sptr()->InitMockData();
  sc_ev->set_symbol_config_mgr(config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr());

  // cache risk limit set, since sc_mgr update method would clear this value
  auto risk_limit_set = sc_ev->symbol_config_mgr()->risk_limits_map_[symbol];

  // init sc_dto
  models::symbolconfigdto::SymbolConfigDTO sc_dto;
  sc_ev->symbol_config_mgr()->GetSymbolConfig(symbol)->ToPB(sc_dto);

  // update fee_rate_tag_id
  sc_dto.set_fee_rate_tag_id(new_fee_rate_tag_id);
  sc_ev->symbol_config_mgr()->ClearChangedFeeRateTagIdsSymbolList();
  sc_ev->symbol_config_mgr()->Update(sc_dto);

  // reassign risk limit map
  for (auto& risk_limit : risk_limit_set->risk_limits()) {
    auto new_risk_limit = std::make_shared<biz::RiskLimitCache>(*risk_limit);
    sc_ev->symbol_config_mgr()->risk_limits_map_[symbol]->Add(new_risk_limit);
  }

  // write event to pre_worker
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(sc_ev, false);
}

int32_t FeeRateTagIdUpdateTest::SwitchBothSide(biz::user_id_t uid) {
  stub user(te, uid);
  FutureOneOfSwitchPositionModeBuilder switch_position_mode(ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::BothSide,
                                                            uid);
  auto resp = user.process(switch_position_mode.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }
  return 0;
}

TEST_F(FeeRateTagIdUpdateTest, test_fee_rate_tag_id_update_by_adjust_leverage) {
  biz::user_id_t uid = 10000;
  stub user(te, uid);
  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  {
    // tag_id(52)+vip(0) -> taker = 0.00165, maker = 0.0006
    // tag_id(60)+vip(0) -> taker = 0.0011 , maker = 0.0004
    const char* FeeRateConfig =
        R"({"data":[{"rule_type":4,"rule_priority":5,"region_rule":0,"rule_config":{"tag_ids":[52],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.00165","maker":"0.0006"},{"user_rule":"vip1","taker":"0.0015","maker":"0.00064"},{"user_rule":"vip2","taker":"0.0012","maker":"0.00048"}]}},{"rule_type":4,"rule_priority":6,"region_rule":0,"rule_config":{"tag_ids":[60],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0011","maker":"0.0004"},{"user_rule":"vip1","taker":"0.001","maker":"0.00036"},{"user_rule":"vip2","taker":"0.0008","maker":"0.00032"}]}}]})";
    InitFeeRate(FeeRateConfig);
  }

  {
    // deposit
    FutureDepositBuilder deposit_build("", uid, "", ECoin::USDT, 1, "*********", "0");
    auto resp = user.process(deposit_build.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();
    EXPECT_EQ(SwitchMarginMode(user, enums::eaccountmode::Cross), 0);
  }

  {
    FutureOneOfSetLeverageBuilder set_leverage(symbol, coin, uid, 500, 500);
    auto resp = user.process(set_leverage.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(pz_check.m_msg->leverage_e2(), 500);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), ********);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), ********);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 608000);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 632000);
  }

  {
    // tag_id(52)+vip(0) -> taker = 0.00165, maker = 0.0006
    UpdateFeeRateTagId(symbol, 52);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg, nullptr);
  }

  {
    FutureOneOfSetLeverageBuilder set_leverage(symbol, coin, uid, 1000, 1000);
    auto resp = user.process(set_leverage.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), 10313500);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), 10346500);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 813500);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 846500);
  }

  {
    // tag_id(60)+vip(0) -> taker = 0.0011 , maker = 0.0004
    UpdateFeeRateTagId(symbol, 60);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg, nullptr);
  }

  {
    FutureOneOfSetLeverageBuilder set_leverage(symbol, coin, uid, 1500, 1500);
    auto resp = user.process(set_leverage.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1500);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), 6879334);
    // (15-1)/15*0.0011 = 0.00102667
    EXPECT_EQ(pz_check.m_msg->buy_occ_fee_rate_e8(), 102667);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), 6894001);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 712667);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 727334);
    // (15+1)/15*0.0011 = 0.00117333
    EXPECT_EQ(pz_check.m_msg->sell_occ_fee_rate_e8(), 117334);
  }
}

TEST_F(FeeRateTagIdUpdateTest, test_fee_rate_tag_id_update_by_create_orders) {
  biz::user_id_t uid = 10000;
  stub user(te, uid);
  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;
  auto pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto order_type = EOrderType::Limit;
  biz::size_x_t qty = 100 * 1e4;
  biz::price_x_t price = 20000 * 1e4;
  auto order_id = te->GenUUID();

  {
    // init fee rate
    const char* FeeRateConfig =
        R"({"data":[{"rule_type":4,"rule_priority":5,"region_rule":0,"rule_config":{"tag_ids":[52],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.00165","maker":"0.0006"},{"user_rule":"vip1","taker":"0.0015","maker":"0.00064"},{"user_rule":"vip2","taker":"0.0012","maker":"0.00048"}]}},{"rule_type":4,"rule_priority":6,"region_rule":0,"rule_config":{"tag_ids":[60],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0011","maker":"0.0004"},{"user_rule":"vip1","taker":"0.001","maker":"0.00036"},{"user_rule":"vip2","taker":"0.0008","maker":"0.00032"}]}}]})";
    InitFeeRate(FeeRateConfig);
  }

  {
    // deposit
    FutureDepositBuilder deposit_build("", uid, "", coin, 1, "*********", "0");
    auto resp = user.process(deposit_build.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();
    EXPECT_EQ(SwitchMarginMode(user, enums::eaccountmode::Cross), 0);
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);

  {
    // create order and verify coefficient
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp = user.process(create_build.Build());
    ASSERT_EQ(resp->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);

    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    order_check.Check(uid, qty);

    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), ********);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), ********);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 614000);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 626000);

    auto pz_value = price / 1e4 * qty / 1e8;
    auto order_cost = pz_value * pz_check.m_msg->buy_value_to_cost_e8();
    auto order_mm_with_fee = pz_value * pz_check.m_msg->buy_value_to_mm_e8();
    EXPECT_EQ(pz_check.m_msg->order_cost_e8(), order_cost);
    EXPECT_EQ(pz_check.m_msg->order_mm_with_fee_e8(), order_mm_with_fee);
  }

  {
    // tag_id(52)+vip(0) -> taker = 0.00165, maker = 0.0006
    UpdateFeeRateTagId(symbol, 52);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::OnFeeRateTagIdUpdate);

    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), 10313500);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), 10346500);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 813500);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 846500);

    auto pz_value = price / 1e4 * qty / 1e8;
    auto order_cost = pz_value * pz_check.m_msg->buy_value_to_cost_e8();
    auto order_mm_with_fee = pz_value * pz_check.m_msg->buy_value_to_mm_e8();
    EXPECT_EQ(pz_check.m_msg->order_cost_e8(), order_cost);
    EXPECT_EQ(pz_check.m_msg->order_mm_with_fee_e8(), order_mm_with_fee);
  }

  {
    // tag_id(60)+vip(0) -> taker = 0.0011 , maker = 0.0004
    UpdateFeeRateTagId(symbol, 60);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::OnFeeRateTagIdUpdate);

    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), 10209000);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), 10231000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 709000);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 731000);

    auto pz_value = price / 1e4 * qty / 1e8;
    auto order_cost = pz_value * pz_check.m_msg->buy_value_to_cost_e8();
    auto order_mm_with_fee = pz_value * pz_check.m_msg->buy_value_to_mm_e8();
    EXPECT_EQ(pz_check.m_msg->order_cost_e8(), order_cost);
    EXPECT_EQ(pz_check.m_msg->order_mm_with_fee_e8(), order_mm_with_fee);
  }
}

TEST_F(FeeRateTagIdUpdateTest, test_fee_rate_tag_id_update_by_make_deals_at_oneway_mode) {
  biz::user_id_t uid1 = 10000, uid2 = 20000;
  stub user1(te, uid1);
  stub user2(te, uid2);
  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;
  auto pz_index = EPositionIndex::Single;
  auto buy_side = ESide::Buy;
  auto sell_side = ESide::Sell;
  auto order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.01 * 1e8;
  biz::price_x_t price = 20000 * 1e4;

  {
    // init fee_rate_config
    const char* FeeRateConfig =
        R"({"data":[{"rule_type":4,"rule_priority":5,"region_rule":0,"rule_config":{"tag_ids":[52],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.00165","maker":"0.0006"},{"user_rule":"vip1","taker":"0.0015","maker":"0.00064"},{"user_rule":"vip2","taker":"0.0012","maker":"0.00048"}]}},{"rule_type":4,"rule_priority":6,"region_rule":0,"rule_config":{"tag_ids":[60],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0011","maker":"0.0004"},{"user_rule":"vip1","taker":"0.001","maker":"0.00036"},{"user_rule":"vip2","taker":"0.0008","maker":"0.00032"}]}}]})";
    InitFeeRate(FeeRateConfig);
  }

  {
    // deposit
    FutureDepositBuilder deposit_build_1("", uid1, "", coin, 1, "*********", "0");
    auto resp = user1.process(deposit_build_1.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();

    FutureDepositBuilder deposit_build_2("", uid2, "", coin, 1, "*********", "0");
    resp = user2.process(deposit_build_2.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    result = te->PopResult();
  }

  {
    // switch to isolated mode
    EXPECT_EQ(SwitchMarginMode(user1, enums::eaccountmode::Isolated), 0);
    EXPECT_EQ(SwitchMarginMode(user2, enums::eaccountmode::Isolated), 0);
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);

  {
    // build positions
    {
      auto buy_order_id = te->GenUUID();
      FutureOneOfCreateOrderBuilder create_build(buy_order_id, symbol, pz_index, buy_side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, uid1, coin);
      auto resp = user1.process(create_build.Build());
      ASSERT_EQ(resp->ret_code(), 0);

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg, nullptr);

      auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(buy_order_id);
      ASSERT_NE(order_check.m_msg, nullptr);
      order_check.Check(uid1, qty);

      auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
      ASSERT_NE(pz_check.m_msg, nullptr);
      EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
      EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), ********);
      EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), ********);
      EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 614000);
      EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 626000);

      auto pz_value = price / 1e4 * qty / 1e8;
      auto order_cost = pz_value * pz_check.m_msg->buy_value_to_cost_e8();
      auto order_mm_with_fee = pz_value * pz_check.m_msg->buy_value_to_mm_e8();
      EXPECT_EQ(pz_check.m_msg->order_cost_e8(), order_cost);
      EXPECT_EQ(pz_check.m_msg->order_mm_with_fee_e8(), order_mm_with_fee);
    }

    {
      auto sell_order_id = te->GenUUID();
      FutureOneOfCreateOrderBuilder create_build(sell_order_id, symbol, pz_index, sell_side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, uid2, coin);
      auto resp = user2.process(create_build.Build());
      ASSERT_EQ(resp->ret_code(), 0);

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg, nullptr);

      auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(sell_order_id);
      ASSERT_NE(order_check.m_msg, nullptr);
      order_check.Check(uid2, qty);
      order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

      // 验证卖方持仓
      auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
      ASSERT_NE(pz_check.m_msg, nullptr);
      EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
      EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), ********);
      EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), ********);
      EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 614000);
      EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 626000);
      EXPECT_EQ(pz_check.m_msg->min_position_cost_e8(), ********00);
      EXPECT_EQ(pz_check.m_msg->position_mm_with_fee_e8(), 113200000);
      EXPECT_EQ(pz_check.m_msg->bust_price_x(), 220000000);
      EXPECT_EQ(pz_check.m_msg->liq_price_x(), 219000000);
    }

    {
      // 验证买方持仓
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg, nullptr);
      auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
      EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
      EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), ********);
      EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), ********);
      EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 614000);
      EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 626000);
      EXPECT_EQ(pz_check.m_msg->min_position_cost_e8(), ********00);
      EXPECT_EQ(pz_check.m_msg->position_mm_with_fee_e8(), 110800000);
      EXPECT_EQ(pz_check.m_msg->bust_price_x(), 180000000);
      EXPECT_EQ(pz_check.m_msg->liq_price_x(), 181000000);
    }
  }

  {
    // tag_id(52)+vip(0) -> taker = 0.00165, maker = 0.0006
    UpdateFeeRateTagId(symbol, 52);

    // 验证卖方持仓
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::OnFeeRateTagIdUpdate);

    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), 10313500);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), 10346500);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 813500);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 846500);
    EXPECT_EQ(pz_check.m_msg->min_position_cost_e8(), 2036300000);
    EXPECT_EQ(pz_check.m_msg->position_mm_with_fee_e8(), 136300000);
    EXPECT_EQ(pz_check.m_msg->bust_price_x(), 220000000);
    EXPECT_EQ(pz_check.m_msg->liq_price_x(), 219000000);

    // 验证买方持仓
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::OnFeeRateTagIdUpdate);

    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), 10313500);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), 10346500);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 813500);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 846500);
    EXPECT_EQ(pz_check.m_msg->min_position_cost_e8(), 2029700000);
    EXPECT_EQ(pz_check.m_msg->position_mm_with_fee_e8(), 129700000);
    EXPECT_EQ(pz_check.m_msg->bust_price_x(), 180000000);
    EXPECT_EQ(pz_check.m_msg->liq_price_x(), 181000000);
  }

  {
    // tag_id(60)+vip(0) -> taker = 0.0011 , maker = 0.0004
    UpdateFeeRateTagId(symbol, 60);

    // 验证卖方持仓
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::OnFeeRateTagIdUpdate);

    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), 10209000);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), 10231000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 709000);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 731000);
    EXPECT_EQ(pz_check.m_msg->min_position_cost_e8(), 2024200000);
    EXPECT_EQ(pz_check.m_msg->position_mm_with_fee_e8(), 124200000);
    EXPECT_EQ(pz_check.m_msg->bust_price_x(), 220000000);
    EXPECT_EQ(pz_check.m_msg->liq_price_x(), 219000000);

    // 验证买方持仓
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::OnFeeRateTagIdUpdate);

    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), 10209000);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), 10231000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 709000);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 731000);
    EXPECT_EQ(pz_check.m_msg->min_position_cost_e8(), 2019800000);
    EXPECT_EQ(pz_check.m_msg->position_mm_with_fee_e8(), 119800000);
    EXPECT_EQ(pz_check.m_msg->bust_price_x(), 180000000);
    EXPECT_EQ(pz_check.m_msg->liq_price_x(), 181000000);
  }
}

TEST_F(FeeRateTagIdUpdateTest, test_fee_rate_tag_id_update_by_make_deals_at_hedge_mode) {
  biz::user_id_t uid1 = 10000, uid2 = 20000;
  stub user1(te, uid1);
  stub user2(te, uid2);
  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;
  auto buy_pz_index = EPositionIndex::Buy;
  auto sell_pz_index = EPositionIndex::Sell;
  auto buy_side = ESide::Buy;
  auto sell_side = ESide::Sell;
  auto order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.01 * 1e8;
  biz::price_x_t price = 20000 * 1e4;

  {
    // init fee_rate_config
    const char* FeeRateConfig =
        R"({"data":[{"rule_type":4,"rule_priority":5,"region_rule":0,"rule_config":{"tag_ids":[52],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.00165","maker":"0.0006"},{"user_rule":"vip1","taker":"0.0015","maker":"0.00064"},{"user_rule":"vip2","taker":"0.0012","maker":"0.00048"}]}},{"rule_type":4,"rule_priority":6,"region_rule":0,"rule_config":{"tag_ids":[60],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0011","maker":"0.0004"},{"user_rule":"vip1","taker":"0.001","maker":"0.00036"},{"user_rule":"vip2","taker":"0.0008","maker":"0.00032"}]}}]})";
    InitFeeRate(FeeRateConfig);
  }

  {
    // deposit
    FutureDepositBuilder deposit_build_1("", uid1, "", coin, 1, "*********", "0");
    auto resp = user1.process(deposit_build_1.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();

    FutureDepositBuilder deposit_build_2("", uid2, "", coin, 1, "*********", "0");
    resp = user2.process(deposit_build_2.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    result = te->PopResult();
  }

  {
    // switch to cross mode
    EXPECT_EQ(SwitchMarginMode(user1, enums::eaccountmode::Cross), 0);
    EXPECT_EQ(SwitchMarginMode(user2, enums::eaccountmode::Cross), 0);
  }

  {
    // switch to hedge mode
    EXPECT_EQ(SwitchBothSide(uid1), 0);
    EXPECT_EQ(SwitchBothSide(uid2), 0);
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);

  {
    // create positions
    {
      auto buy_order_id = te->GenUUID();
      FutureOneOfCreateOrderBuilder create_build(buy_order_id, symbol, buy_pz_index, buy_side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, uid1, coin);
      auto resp = user1.process(create_build.Build());
      ASSERT_EQ(resp->ret_code(), 0);

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg, nullptr);

      auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(buy_order_id);
      ASSERT_NE(order_check.m_msg, nullptr);
      order_check.Check(uid1, qty);

      // 验证买方系数
      auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
      ASSERT_NE(pz_check.m_msg, nullptr);
      EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
      EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), ********);
      EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), ********);
      EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 614000);
      EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 626000);
      EXPECT_EQ(pz_check.m_msg->order_cost_e8(), 2022800000);
      EXPECT_EQ(pz_check.m_msg->order_mm_with_fee_e8(), 122800000);
    }

    {
      auto sell_order_id = te->GenUUID();
      FutureOneOfCreateOrderBuilder create_build(sell_order_id, symbol, sell_pz_index, sell_side, order_type, qty,
                                                 price, ETimeInForce::GoodTillCancel, uid2, coin);
      auto resp = user2.process(create_build.Build());
      ASSERT_EQ(resp->ret_code(), 0);

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg, nullptr);

      auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(sell_order_id);
      ASSERT_NE(order_check.m_msg, nullptr);
      order_check.Check(uid2, qty);
      order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

      // 验证卖方持仓
      auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
      ASSERT_NE(pz_check.m_msg, nullptr);
      EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
      EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), ********);
      EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), ********);
      EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 614000);
      EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 626000);
      EXPECT_EQ(pz_check.m_msg->min_position_cost_e8(), ********00);
      EXPECT_EQ(pz_check.m_msg->position_mm_with_fee_e8(), 113200000);
      EXPECT_EQ(pz_check.m_msg->bust_price_x(), 0);
      EXPECT_EQ(pz_check.m_msg->liq_price_x(), 0);
    }

    {
      // 验证买方持仓
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg, nullptr);
      auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
      EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
      EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), ********);
      EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), ********);
      EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 614000);
      EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 626000);
      EXPECT_EQ(pz_check.m_msg->min_position_cost_e8(), ********00);
      EXPECT_EQ(pz_check.m_msg->position_mm_with_fee_e8(), 110800000);
      EXPECT_EQ(pz_check.m_msg->bust_price_x(), 0);
      EXPECT_EQ(pz_check.m_msg->liq_price_x(), 0);
    }
  }

  {
    // tag_id(52)+vip(0) -> taker = 0.00165, maker = 0.0006
    UpdateFeeRateTagId(symbol, 52);

    // 验证卖方持仓
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::OnFeeRateTagIdUpdate);

    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(1);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), 10313500);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), 10346500);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 813500);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 846500);
    EXPECT_EQ(pz_check.m_msg->min_position_cost_e8(), 2036300000);
    EXPECT_EQ(pz_check.m_msg->position_mm_with_fee_e8(), 136300000);
    EXPECT_EQ(pz_check.m_msg->bust_price_x(), 0);
    EXPECT_EQ(pz_check.m_msg->liq_price_x(), 0);

    // 验证买方持仓
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::OnFeeRateTagIdUpdate);

    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), 10313500);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), 10346500);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 813500);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 846500);
    EXPECT_EQ(pz_check.m_msg->min_position_cost_e8(), 2029700000);
    EXPECT_EQ(pz_check.m_msg->position_mm_with_fee_e8(), 129700000);
    EXPECT_EQ(pz_check.m_msg->bust_price_x(), 0);
    EXPECT_EQ(pz_check.m_msg->liq_price_x(), 0);
  }

  {
    // tag_id(60)+vip(0) -> taker = 0.0011 , maker = 0.0004
    UpdateFeeRateTagId(symbol, 60);

    // 验证卖方持仓
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::OnFeeRateTagIdUpdate);

    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(1);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), 10209000);
    // (10-1)/10*0.0011=0.00099
    EXPECT_EQ(pz_check.m_msg->buy_occ_fee_rate_e8(), 99000);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), 10231000);
    // (10+1)/10*0.0011=0.00121
    EXPECT_EQ(pz_check.m_msg->sell_occ_fee_rate_e8(), 121000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 709000);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 731000);
    EXPECT_EQ(pz_check.m_msg->min_position_cost_e8(), 2024200000);
    EXPECT_EQ(pz_check.m_msg->position_mm_with_fee_e8(), 124200000);
    EXPECT_EQ(pz_check.m_msg->bust_price_x(), 0);
    EXPECT_EQ(pz_check.m_msg->liq_price_x(), 0);

    // 验证买方持仓
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::OnFeeRateTagIdUpdate);

    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(pz_check.m_msg->leverage_e2(), 1000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_cost_e8(), 10209000);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_cost_e8(), 10231000);
    EXPECT_EQ(pz_check.m_msg->buy_value_to_mm_e8(), 709000);
    EXPECT_EQ(pz_check.m_msg->sell_value_to_mm_e8(), 731000);
    EXPECT_EQ(pz_check.m_msg->min_position_cost_e8(), 2019800000);
    EXPECT_EQ(pz_check.m_msg->position_mm_with_fee_e8(), 119800000);
    EXPECT_EQ(pz_check.m_msg->bust_price_x(), 0);
    EXPECT_EQ(pz_check.m_msg->liq_price_x(), 0);
  }
}
