//
// Created by Gdso.li on 2023/3/28.
//

#pragma once

#ifndef UTA_ENGINE_RISK_LIMIT_UPDATE_TEST_HPP
#define UTA_ENGINE_RISK_LIMIT_UPDATE_TEST_HPP

#include <gtest/gtest.h>

#include <iostream>
#include <memory>

#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"
class RiskLimitUpdateTest : public testing::Test {
 public:
  void SetUp() override { config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr()->InitMockData(); }
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }

  static std::shared_ptr<tmock::CTradeAppMock> te;

  static constexpr const char* kTradeContent =
      R"({"data":{"BTC":{"orderPriceFromLastPrice":"0.03","liquidationFeeRate":"0.0035","bidAskMinIv":"0",
      "maxProportionOfDeliveryInOptionValue":"0.125","insuranceFeeRate":"0.000175","tickSize":"0.5","deviationOfMarkIV":"0.02",
      "entryId":"47","markMinIv":"0.1","bidAskMaxIv":"7.5","makerFee":"0.00025","minSellBasis":"0.0005","minImFactor":"0.1",
      "markMaxIv":"4.5","liquidationFeeRatePerp":"0.0006","baseCoin":"BTC","maxBuyFactor":"10","minSellFactor":"10",
      "liquidationTarget":"0.1","stepLiqSheet":"0-20:5;20-100:10;100-200:20;200-:1000","takerFee":"0.00073","minOrderSize":"0.01",
      "liquidationFeeRateFutures":"9.998","minOrderSizeIncrement":"0.01","liquidationFeeRateOption":"9.999","orderPriceFromMarkPrice":"0.1",
      "priceDeviationPercentage":"99.99","maxImFactor":"0.15","maxProportionOfTransactionInOrderPrice":"0.1251","mmFactor":"0.03",
      "minOrderPrice":"0.5","basicDeliveryFeeRate":"0.00015","maxOrderSize":"50000","takeoverTrigger":"1.6","stepLiqMaxOrderSize":"5",
      "maxOrderPrice":"10000000"}},"version":"32798883-f752-4222-b171-71a8ff43531f"})";

  std::shared_ptr<event::OnUpdateConfigEvent> MockConfig(bool gray, int64_t risk_limit_version);

  // 在原有的基础上每一档mmr和imr对应扩大缩小对应的百分比
  int64_t TriggerRiskLimitUpdate(biz::symbol_t symbol, bool gray, int32_t update_mmr_percent_e2,
                                 int32_t update_imr_percent_e2, int64_t risk_limit_version);
  void create_gray_with_order(stub& user1, stub& user2, bool is_cross);
  void create_gray_with_pz(stub& user1, stub& user2, stub& user3, bool is_cross);
};
#endif  // UTA_ENGINE_RISK_LIMIT_UPDATE_TEST_HPP
