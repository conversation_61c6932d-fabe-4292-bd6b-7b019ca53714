#include "test/biz/trade/future/position/set_risk_id_test.hpp"

#include "lib/msg_builder.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"

std::shared_ptr<tmock::CTradeAppMock> SetRiskIdTest::te;

TEST_F(SetRiskIdTest, set_risk_id) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  biz::symbol_t symbol = 5;  // BTCUSDT
  biz::coin_t coin = 5;
  biz::risk_id_t risk_id = 2;

  FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid, risk_id);
  auto resp1 = user1.process(set_risk_id_build.Build());
  auto result1 = te->PopResult();
  ASSERT_NE(result1.m_msg.get(), nullptr);
  auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(positionCheck.m_msg, nullptr);
  positionCheck.CheckLv(1000, ********, ********, 1114000, 1126000).CheckRiskId(2);
}

TEST_F(SetRiskIdTest, set_risk_id_open_v5) {
  biz::user_id_t uid = 10001;
  stub user1(te, uid);
  int64_t risk_id = 2;
  int64_t position_idx = 0;

  OpenApiV5SetRiskIdBuilder set_risk_id_build("linear", "BTCUSDT", risk_id, position_idx);

  RpcContext ct{};

  auto resp = user1.set_risk_id(set_risk_id_build.Build(), ct);
  ASSERT_NE(resp, nullptr);

  // std::cout << "RetCode: " << ct.RetCode() << ", RetMsg: " << ct.RetMsg() << std::endl;
  ASSERT_EQ(0, ct.RetCode());

  std::string msg{};
  // std::cout << utils::PbMessageToJsonString(*resp, &msg) << std::endl;
  ASSERT_EQ(resp->category(), "linear");
  ASSERT_EQ(resp->risk_id(), 2);
  ASSERT_EQ(resp->risk_limit_value(), "2000000");

  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  auto positionCheck = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(positionCheck.m_msg, nullptr);

  positionCheck.CheckLv(1000, ********, ********, 1114000, 1126000).CheckRiskId(2);
}

TEST_F(SetRiskIdTest, set_risk_id_isolated) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  // int32_t wait_time = 300;

  {
    // 账户开户
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::CreateAccount);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "开户Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换逐仓模式
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_margin_mode(true);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "切换逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 调整风险限额
    OpenApiV5SetRiskIdBuilder set_risk_id_build("linear", "BTCUSDT", 2, 0);

    RpcContext ct{};

    auto resp = user1.set_risk_id(set_risk_id_build.Build(), ct);
    ASSERT_NE(resp, nullptr);

    // std::cout << "RetCode: " << ct.RetCode() << ", RetMsg: " << ct.RetMsg() << std::endl;
    ASSERT_EQ(0, ct.RetCode());

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto positionCheck = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, ********, ********, 1114000, 1126000).CheckRiskId(2);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "调整风险限额Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到双仓模式
    SwitchPositionMode switch_position_mode(uid, ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::BothSide);
    auto resp = user1.process(switch_position_mode.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto positionCheck = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "切换持仓模式Result:" << jsonStr2 << std::endl;
    positionCheck.CheckLv(1000, ********, ********, 1114000, 1126000).CheckRiskId(2);
  }

  {
    // 调整风险限额
    OpenApiV5SetRiskIdBuilder set_risk_id_build("linear", "BTCUSDT", 2, 1);

    RpcContext ct{};

    auto resp = user1.set_risk_id(set_risk_id_build.Build(), ct);
    ASSERT_NE(resp, nullptr);

    // std::cout << "RetCode: " << ct.RetCode() << ", RetMsg: " << ct.RetMsg() << std::endl;
    ASSERT_EQ(0, ct.RetCode());

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto positionCheck = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    // std::cout << "调整风险限额Result:" << jsonStr2 << std::endl;
    positionCheck.CheckLv(1000, ********, ********, 1114000, 1126000).CheckRiskId(2);
  }

  //  {
  //    // 切换全仓模式
  //    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  //    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  //    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  //    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_margin_mode(true);
  //    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
  //        EAccountMode::Cross);
  //    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  //    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  //    auto result = te->PopResult();
  //    ASSERT_NE(result.m_msg.get(), nullptr);
  //    std::string jsonStr2;
  //    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  //    std::cout << "切换q全仓Result:" << jsonStr2 << std::endl;
  //  }
}
