//
// Created by SH00867ML on 2023/9/28.
//
#include "test/biz/trade/future/position/trading_performance.hpp"

#include <string>
#include <utility>

#include "data/type/biz_type.hpp"
#include "lib/msg_builder.hpp"
#include "proto/gen/enums/ebizcode/biz_code.pb.h"
#include "src/biz_worker/utils/pb_convertor/to_pb.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"

std::shared_ptr<tmock::CTradeAppMock> TradingPerformanceTest::te;

biz::CrossError BuildAndSendFundingMessage(biz::symbol_t symbol, biz::cross_idx_t cross_idx, biz::scale_t price_scale,
                                           biz::price_x_t price, biz::fee_rate_e8_t fee_rate,
                                           biz::time_e9_t exec_time) {
  biz::cross::xReqSender sender;
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();

  auto err = sender.BuildHeader(biz::cross::request::kMTSettleFundingFee, ECreateType::UNKNOWN);
  if (err.HasErr()) {
    return err;
  }

  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTContract;
  sender.req_mgr_.request_obj_->req_.symbol_ = symbol;
  sender.req_mgr_.request_obj_->req_.side_ = biz::cross::request::kSideBuy;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = biz::cross::request::kTIFGoodTillCancel;
  sender.req_mgr_.request_obj_->req_.order_type_ = biz::cross::request::kOTMarketOrder;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = static_cast<u_int64_t>(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = static_cast<u_int64_t>(sending_time % 1000000);

  store::FundingFee::Ptr const ct = std::make_shared<store::FundingFee>();
  ct->set_symbol(static_cast<biz::symbol_t>(sender.req_mgr_.request_obj_->req_.symbol_));
  ct->set_exec_time(exec_time);
  ct->set_price_scale(price_scale);
  ct->set_exec_price(price);
  ct->set_exec_id(biz::exec_id_t("qwe-qwe-qwe"));
  ct->set_fee_rate(fee_rate);
  ::convertor::ToPB::Convert(ct.get(), sender.req_mgr_.x_pass_through_.value().mutable_funding_fee());

  //  std::string msg{};
  //  std::cout << utils::PbMessageToJsonString(sender.req_mgr_.x_pass_through_.value(), &msg) << '\n';

  return sender.DoSend2Cross(cross_idx, EProductType::Futures);
}

TEST_F(TradingPerformanceTest, closed_pnl_and_funding_records) {
  biz::user_id_t const uid1 = 100000;
  biz::user_id_t const uid2 = 10111;
  biz::user_id_t const uid3 = 10222;

  stub user1(te, uid1);
  stub user2(te, uid2);
  // stub user3(te, uid3);

  {
    // user1充值
    FutureDepositBuilder deposit_build("deposit-req-id", uid1, "deposit-trans-id", ECoin::USDT,
                                       EWalletRecordType::Deposit, "*********", "123");
    auto resp = user1.process(deposit_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
  }

  {
    // user2充值
    FutureDepositBuilder deposit_build("deposit-req-id", uid2, "deposit-trans-id", ECoin::USDT,
                                       EWalletRecordType::Deposit, "*********", "123");
    auto resp = user2.process(deposit_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
  }

  //  {
  //    // user3充值
  //    FutureDepositBuilder deposit_build("deposit-req-id", uid3, "deposit-trans-id", ECoin::USDT,
  //                                       EWalletRecordType::Deposit, "*********", "123");
  //    auto resp = user3.process(deposit_build.Build());
  //    ASSERT_NE(resp, nullptr);
  //    EXPECT_EQ(resp->ret_code(), 0);
  //    auto result = te->PopResult();
  //    ASSERT_NE(result.m_msg, nullptr);
  //  }

  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  // user1 +100@20000
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                                EOrderType::Limit, 100 * 1e4, 20000 * 1e4, ETimeInForce::GoodTillCancel,
                                                uid1, ECoin::USDT);
    auto resp = user1.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  }

  // user2 -100@20000
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Sell,
                                                EOrderType::Limit, 100 * 1e4, 20000 * 1e4,
                                                ETimeInForce::ImmediateOrCancel, uid2, ECoin::USDT);
    auto resp = user2.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid2);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().size(), 0);
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 100 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term(), 0);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_open_fee_e8(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_funding_fee_e8(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_trade_fee_e8(), 0);

    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    EXPECT_EQ(result2.m_msg->header().user_id(), uid1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().size(), 0);
    ASSERT_EQ(result2.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 100 * 1e4);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term(), 0);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_open_fee_e8(), 0);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_funding_fee_e8(), 0);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_trade_fee_e8(), 0);
  }

  // 执行一次funding
  {
    auto err = BuildAndSendFundingMessage(ESymbol::BTCUSDT, ECrossIdx::BTCLP, 4, 20000 * 1e4, 2000,
                                          bbase::utils::Time::GetTimeNs());
    ASSERT_FALSE(err.HasErr());
    auto result = te->PopResult(10000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::settle_funding_fee);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_funding_records().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_funding_records().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_funding_records().at(0).after_pz_term(), 0);
    std::cout << "first pop result uid:" << result.m_msg->header().user_id() << '\n';
    if (result.m_msg->header().user_id() == uid1) {
      EXPECT_EQ(result.m_msg->futures_margin_result().related_funding_records().at(0).exec_fee_e8(),
                100 * 20000 * 0.2);  // Buy持仓fee_rate=req.fee_rate
    } else if (result.m_msg->header().user_id() == uid2) {
      EXPECT_EQ(result.m_msg->futures_margin_result().related_funding_records().at(0).exec_fee_e8(),
                100 * 20000 * -0.2);  // Sell持仓fee_rate = -req.fee_rate
    }
    auto result2 = te->PopResult(10000);
    ASSERT_NE(result2.m_msg.get(), nullptr);
    EXPECT_EQ(result2.m_msg->header().action(), EAction::settle_funding_fee);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_funding_records().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_funding_records().at(0).before_pz_term(), 0);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_funding_records().at(0).after_pz_term(), 0);
    if (result2.m_msg->header().user_id() == uid1) {
      EXPECT_EQ(result2.m_msg->futures_margin_result().related_funding_records().at(0).exec_fee_e8(),
                100 * 20000 * 0.2);  // Buy持仓fee_rate=req.fee_rate
    } else if (result2.m_msg->header().user_id() == uid2) {
      EXPECT_EQ(result2.m_msg->futures_margin_result().related_funding_records().at(0).exec_fee_e8(),
                100 * 20000 * -0.2);  // Sell持仓fee_rate = -req.fee_rate
    }
  }

  // user1 -50@20000, 成交后仓位:+100 -> +50
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Sell,
                                                EOrderType::Limit, 50 * 1e4, 20000 * 1e4, ETimeInForce::GoodTillCancel,
                                                uid1, ECoin::USDT);
    auto resp = user1.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  }

  int64_t cum_closed_fee_user1{0};
  int64_t cum_closed_fee_user2{0};

  // user2 +50@20000, 成交后仓位:-100 -> -50
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                                EOrderType::Limit, 50 * 1e4, 20000 * 1e4,
                                                ETimeInForce::ImmediateOrCancel, uid2, ECoin::USDT);
    auto resp = user2.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid2);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 50 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term(), 0);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_open_fee_e8(),
              50 * 20000 * 6);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_funding_fee_e8(),
              -200000);
    cum_closed_fee_user2 += result.m_msg->futures_margin_result().related_orders().at(0).exec_fee_e8();
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_trade_fee_e8(),
              cum_closed_fee_user2);  // 减仓单, 没有反向开仓

    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    EXPECT_EQ(result2.m_msg->header().user_id(), uid1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().size(), 1);
    ASSERT_EQ(result2.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 50 * 1e4);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term(), 0);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_open_fee_e8(),
              50 * 20000 * 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_funding_fee_e8(),
              200000);
    cum_closed_fee_user1 += result2.m_msg->futures_margin_result().related_orders().at(0).exec_fee_e8();
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_trade_fee_e8(),
              cum_closed_fee_user1);
  }

  // user1 -10@20000 x 3, 成交后仓位:+50 -> +20
  {
    for (int i = 0; i != 3; ++i) {
      FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Sell,
                                                  EOrderType::Limit, 10 * 1e4, 20000 * 1e4,
                                                  ETimeInForce::GoodTillCancel, uid1, ECoin::USDT);
      auto resp = user1.process(place_order_b.Build());
      ASSERT_NE(resp, nullptr);
      EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    }
  }

  // user2 +30@20000, 成交后仓位:-50 -> -20
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                                EOrderType::Limit, 30 * 1e4, 20000 * 1e4,
                                                ETimeInForce::ImmediateOrCancel, uid2, ECoin::USDT);
    auto resp = user2.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid2);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 3);
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 20 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term(), 0);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_open_fee_e8(),
              30 * 20000 * 6);  // 订单维度累计
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_funding_fee_e8(),
              -120000);  // 一次funding, 本次平30
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_trade_fee_e8(),
              result.m_msg->futures_margin_result().related_orders().at(0).cum_exec_fee_e8());  // 这笔订单是分3笔成交的

    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    EXPECT_EQ(result2.m_msg->header().user_id(), uid1);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_orders().size(), 3);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_fills().size(), 3);
    ASSERT_EQ(result2.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 20 * 1e4);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term(), 0);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().size(), 3);
    for (int i = 0; i < 3; ++i) {
      auto order_id = result2.m_msg->futures_margin_result().related_closed_pnl_records().at(i).order_id();
      for (const auto& ord : result2.m_msg->futures_margin_result().related_orders()) {
        if (ord.order_id() == order_id) {
          EXPECT_EQ(
              result2.m_msg->futures_margin_result().related_closed_pnl_records().at(i).cum_closed_pz_open_fee_e8(),
              ord.cum_closed_pz_open_fee_e8());  // 订单维度累计
          EXPECT_EQ(
              result2.m_msg->futures_margin_result().related_closed_pnl_records().at(i).cum_closed_pz_funding_fee_e8(),
              ord.cum_closed_pz_funding_fee_e8());
          EXPECT_EQ(
              result2.m_msg->futures_margin_result().related_closed_pnl_records().at(i).cum_closed_pz_trade_fee_e8(),
              ord.cum_closed_pz_trade_fee_e8());
        }
      }
    }
  }

  biz::money_e8_t uid1_term_open_fee{};
  biz::money_e8_t uid2_term_open_fee{};
  // user1: -80@20000, 成交后+20 -> -60
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Sell,
                                                EOrderType::Limit, 80 * 1e4, 20000 * 1e4, ETimeInForce::GoodTillCancel,
                                                uid1, ECoin::USDT);
    auto resp = user1.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  }

  // user2 +100@20000, 成交后仓位:-20 -> +60
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                                EOrderType::Limit, 100 * 1e4, 20000 * 1e4,
                                                ETimeInForce::ImmediateOrCancel, uid2, ECoin::USDT);
    auto resp = user2.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid2);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 60 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term(), 1);
    EXPECT_EQ(
        result.m_msg->futures_margin_result().affected_positions().at(0).term_open_fee_e8(),
        result.m_msg->futures_margin_result().related_orders().at(0).cum_exec_fee_e8() / 4 * 3);  // 累计开仓费成本
    uid2_term_open_fee = result.m_msg->futures_margin_result().affected_positions().at(0).term_open_fee_e8();
    ASSERT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_open_fee_e8(),
              20 * 20000 * 6);  // 订单维度累计, 这笔成交只有20是平仓,剩下20是反向开仓
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_funding_fee_e8(),
              -80000);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_trade_fee_e8(),
              result.m_msg->futures_margin_result().related_orders().at(0).cum_exec_fee_e8() * 1 /
                  4);  // 这笔订单1/4是closed,3/4是open
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(),
              EOrderStatus::Cancelled);  // 40成交,剩余20因为IOC取消

    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    EXPECT_EQ(result2.m_msg->header().user_id(), uid1);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_fills().size(), 1);
    ASSERT_EQ(result2.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 60 * 1e4);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term(), 1);
    EXPECT_EQ(
        result2.m_msg->futures_margin_result().affected_positions().at(0).term_open_fee_e8(),
        result2.m_msg->futures_margin_result().related_orders().at(0).cum_exec_fee_e8() / 4 * 3);  // 累计开仓费成本
    uid1_term_open_fee = result2.m_msg->futures_margin_result().affected_positions().at(0).term_open_fee_e8();
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_open_fee_e8(),
              20 * 20000 * 1);  // 订单维度累计, 这笔成交只有20是平仓,剩下60是反向开仓
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_funding_fee_e8(),
              80000);
    EXPECT_EQ(
        result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_trade_fee_e8(),
        result2.m_msg->futures_margin_result().related_orders().at(0).exec_fee_e8() / 4 * 1);  // 这笔订单1/4是closed
  }

  /*
   * 仓位翻转后, 执行一次funding
   */
  biz::money_e8_t uid1_term_funding_fee{};
  biz::money_e8_t uid2_term_funding_fee{};
  {
    auto err = BuildAndSendFundingMessage(ESymbol::BTCUSDT, ECrossIdx::BTCLP, 4, 20000 * 1e4, 2000,
                                          bbase::utils::Time::GetTimeNs());
    ASSERT_FALSE(err.HasErr());
    auto result = te->PopResult(10000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::settle_funding_fee);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_funding_records().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_funding_records().at(0).before_pz_term(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_funding_records().at(0).after_pz_term(), 1);
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    // 新反向开仓, term funding fee
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8(),
              result.m_msg->futures_margin_result().related_funding_records().at(0).exec_fee_e8());
    if (result.m_msg->header().user_id() == uid1) {
      uid1_term_funding_fee = result.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8();
    } else if (result.m_msg->header().user_id() == uid2) {
      uid2_term_funding_fee = result.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8();
    } else {
      std::cout << "who am i?" << result.m_msg->header().user_id() << '\n';
    }

    auto result2 = te->PopResult(10000);
    ASSERT_NE(result2.m_msg.get(), nullptr);
    EXPECT_EQ(result2.m_msg->header().action(), EAction::settle_funding_fee);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_funding_records().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_funding_records().at(0).before_pz_term(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_funding_records().at(0).after_pz_term(), 1);
    ASSERT_EQ(result2.m_msg->futures_margin_result().affected_positions().size(), 1);
    // 新反向开仓, term funding fee
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8(),
              result2.m_msg->futures_margin_result().related_funding_records().at(0).exec_fee_e8());
    if (result2.m_msg->header().user_id() == uid1) {
      uid1_term_funding_fee = result2.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8();
    } else if (result2.m_msg->header().user_id() == uid2) {
      uid2_term_funding_fee = result2.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8();
    } else {
      std::cout << "who am i?" << result2.m_msg->header().user_id() << '\n';
    }
  }

  /*
   * [bugfix] 当前平仓单为部分成交，已成交部分未完全平掉仓位，订单状态错误，应该是部分成交.
   */
  // user1: +40@20000, 成交后-60 -> -30(剩余30)
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                                EOrderType::Limit, 40 * 1e4, 20000 * 1e4, ETimeInForce::GoodTillCancel,
                                                uid1, ECoin::USDT);
    auto resp = user1.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  }

  biz::money_e8_t uid1_cum_closed_pz_open_fee{};
  biz::money_e8_t uid1_cum_closed_pz_funding_fee{};
  // user2 -30@20000, 成交后仓位:+60 -> +30
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Sell,
                                                EOrderType::Limit, 30 * 1e4, 20000 * 1e4, ETimeInForce::GoodTillCancel,
                                                uid2, ECoin::USDT);
    auto resp = user2.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid2);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 30 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term(), 1);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_open_fee_e8(),
              uid2_term_open_fee / 2);  // 订单维度累计, 这笔成交全部平仓, 且平了一半
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term_open_fee_e8(),
              uid2_term_open_fee / 2);
    uid2_term_open_fee = result.m_msg->futures_margin_result().affected_positions().at(0).term_open_fee_e8();
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_funding_fee_e8(),
              uid2_term_funding_fee / 2);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8(),
              uid2_term_funding_fee / 2);
    uid2_term_funding_fee = result.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8();
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_trade_fee_e8(),
              result.m_msg->futures_margin_result().related_orders().at(0).cum_exec_fee_e8());  // 这笔订单都是closed
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::Filled);

    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    EXPECT_EQ(result2.m_msg->header().user_id(), uid1);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_fills().size(), 1);
    ASSERT_EQ(result2.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 30 * 1e4);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term(), 1);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_open_fee_e8(),
              uid1_term_open_fee / 2);  // 平仓了一半
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term_open_fee_e8(),
              uid1_term_open_fee / 2);
    uid1_term_open_fee = result2.m_msg->futures_margin_result().affected_positions().at(0).term_open_fee_e8();
    uid1_cum_closed_pz_open_fee =
        result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_open_fee_e8();
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_funding_fee_e8(),
              uid1_term_funding_fee / 2);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8(),
              uid1_term_funding_fee / 2);
    uid1_term_funding_fee = result2.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8();
    uid1_cum_closed_pz_funding_fee =
        result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_funding_fee_e8();
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_trade_fee_e8(),
              result2.m_msg->futures_margin_result().related_orders().at(0).cum_exec_fee_e8());  // 这笔订单都是closed
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).order_status(),
              EOrderStatus::PartiallyFilled);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).leaves_qty_x(), 10 * 1e4);  // 40成交了30
  }
  // user2 -20@20000, 成交后仓位:+30 -> +20 (剩余10 cancelled)
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Sell,
                                                EOrderType::Limit, 20 * 1e4, 20000 * 1e4,
                                                ETimeInForce::ImmediateOrCancel, uid2, ECoin::USDT);
    auto resp = user2.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid2);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 20 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term(), 1);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_open_fee_e8(),
              uid2_term_open_fee / 3);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term_open_fee_e8(),
              uid2_term_open_fee / 3 * 2);
    uid2_term_open_fee = result.m_msg->futures_margin_result().affected_positions().at(0).term_open_fee_e8();
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_funding_fee_e8(),
              uid2_term_funding_fee / 3);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8(),
              uid2_term_funding_fee / 3 * 2);
    uid2_term_funding_fee = result.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8();
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_trade_fee_e8(),
              result.m_msg->futures_margin_result().related_orders().at(0).cum_exec_fee_e8());  // 这笔订单都是closed
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::Cancelled);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).leaves_qty_x(), 0);  // 剩余10因ioc被取消

    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    EXPECT_EQ(result2.m_msg->header().user_id(), uid1);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_fills().size(), 1);
    ASSERT_EQ(result2.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 20 * 1e4);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term(), 1);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_open_fee_e8(),
              uid1_term_open_fee / 3 + uid1_cum_closed_pz_open_fee);  // 这笔订单是分两笔成交的, 累计之前的cum
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term_open_fee_e8(),
              uid1_term_open_fee / 3 * 2);
    uid1_term_open_fee = result2.m_msg->futures_margin_result().affected_positions().at(0).term_open_fee_e8();
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_funding_fee_e8(),
              uid1_term_funding_fee / 3 + uid1_cum_closed_pz_funding_fee);  // 这笔订单是分两笔成交的, 累计之前的cum
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8(),
              uid1_term_funding_fee / 3 * 2);
    uid1_term_funding_fee = result2.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8();
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_trade_fee_e8(),
              result2.m_msg->futures_margin_result().related_orders().at(0).cum_exec_fee_e8());  // 这笔订单都是closed
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::Filled);
  }

  /*
   * closedPnl订单状态: 平仓到0,订单部分成交,剩余部分Cancelled
   */
  // uid2: -20@20000, +20 -> 0
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Sell,
                                                EOrderType::Limit, 20 * 1e4, 20000 * 1e4, ETimeInForce::GoodTillCancel,
                                                uid2, ECoin::USDT);
    auto resp = user2.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  }
  // uid1: +30@20000, -20 -> 0, 剩余10 canceled
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                                EOrderType::Limit, 30 * 1e4, 20000 * 1e4,
                                                ETimeInForce::ImmediateOrCancel, uid1, ECoin::USDT);
    auto resp = user1.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid1);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::None);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term(), 2);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().size(), 1);

    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_open_fee_e8(),
              uid1_term_open_fee);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term_open_fee_e8(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_funding_fee_e8(),
              uid1_term_funding_fee);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_trade_fee_e8(),
              result.m_msg->futures_margin_result().related_orders().at(0).cum_exec_fee_e8());  // 这笔订单都是closed
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::Cancelled);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).leaves_qty_x(), 0);  // 剩余10因ioc被取消

    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    EXPECT_EQ(result2.m_msg->header().user_id(), uid2);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_fills().size(), 1);
    ASSERT_EQ(result2.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::None);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term(), 2);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_open_fee_e8(),
              uid2_term_open_fee);  // 当前仓位被平到0
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term_open_fee_e8(), 0);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_funding_fee_e8(),
              uid2_term_funding_fee);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term_funding_fee_e8(), 0);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_trade_fee_e8(),
              result2.m_msg->futures_margin_result().related_orders().at(0).cum_exec_fee_e8());  // 这笔订单都是closed
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::Filled);
  }

  // 持仓升级
  {
    auto unified_v2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v2_request_dto->mutable_req_header()->set_user_id(uid3);
    unified_v2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v2_request_dto->mutable_req_header()->set_action(EAction::CreateAccount);
    unified_v2_request_dto->mutable_walletreqgroupbody()->mutable_createaccountreq()->set_pmmode(false);
    unified_v2_request_dto->mutable_walletreqgroupbody()->mutable_createaccountreq()->add_settlecoins(ECoin::USDT);
    unified_v2_request_dto->mutable_walletreqgroupbody()
        ->mutable_createaccountreq()
        ->mutable_perusersettingdata()
        ->set_accountmode(EAccountMode::Cross);
    unified_v2_request_dto->mutable_walletreqgroupbody()->mutable_createaccountreq()->set_upgradewithposition(true);
    ::models::tradingdto::PositionDTO with_position{};
    with_position.set_user_id(uid3);
    with_position.set_contract_type(2);
    with_position.set_coin(ECoin::USDT);
    with_position.set_symbol(ESymbol::BTCUSDT);
    with_position.set_mode(EPositionMode::MergedSingle);
    unified_v2_request_dto->mutable_walletreqgroupbody()->mutable_createaccountreq()->mutable_future_positions()->Add(
        std::move(with_position));
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid3, unified_v2_request_dto, -1);
    auto draft_pkg = GetDraftPkg(event, uid3);
    draft_pkg->GetOrAttachAccountInfo()->LazyDraftA()->account_status = ::enums::eaccountstatus::NotOpen;
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string msg{};
    std::cout << utils::PbMessageToJsonString(*result.m_msg.get(), &msg) << "\n";
  }
}

TEST_F(TradingPerformanceTest, replace_in_between_trades) {
  biz::user_id_t const uid1 = 100000;
  biz::user_id_t const uid2 = 10111;

  stub user1(te, uid1);
  stub user2(te, uid2);
  // stub user3(te, uid3);

  {
    // user1充值
    FutureDepositBuilder deposit_build("deposit-req-id", uid1, "deposit-trans-id", ECoin::USDT,
                                       EWalletRecordType::Deposit, "*********", "123");
    auto resp = user1.process(deposit_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
  }

  {
    // user2充值
    FutureDepositBuilder deposit_build("deposit-req-id", uid2, "deposit-trans-id", ECoin::USDT,
                                       EWalletRecordType::Deposit, "*********", "123");
    auto resp = user2.process(deposit_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
  }

  //  {
  //    // user3充值
  //    FutureDepositBuilder deposit_build("deposit-req-id", uid3, "deposit-trans-id", ECoin::USDT,
  //                                       EWalletRecordType::Deposit, "*********", "123");
  //    auto resp = user3.process(deposit_build.Build());
  //    ASSERT_NE(resp, nullptr);
  //    EXPECT_EQ(resp->ret_code(), 0);
  //    auto result = te->PopResult();
  //    ASSERT_NE(result.m_msg, nullptr);
  //  }

  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  // user1 +100@20000
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                                EOrderType::Limit, 100 * 1e4, 20000 * 1e4, ETimeInForce::GoodTillCancel,
                                                uid1, ECoin::USDT);
    auto resp = user1.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  }

  // user2 -100@20000
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Sell,
                                                EOrderType::Limit, 100 * 1e4, 20000 * 1e4,
                                                ETimeInForce::ImmediateOrCancel, uid2, ECoin::USDT);
    auto resp = user2.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid2);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_closed_pnl_records().size(), 0);
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 100 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).term(), 0);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_open_fee_e8(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_funding_fee_e8(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_trade_fee_e8(), 0);

    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    EXPECT_EQ(result2.m_msg->header().user_id(), uid1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().size(), 0);
    ASSERT_EQ(result2.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 100 * 1e4);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term(), 0);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_open_fee_e8(), 0);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_funding_fee_e8(), 0);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_trade_fee_e8(), 0);
  }

  // 执行一次funding
  {
    auto err = BuildAndSendFundingMessage(ESymbol::BTCUSDT, ECrossIdx::BTCLP, 4, 20000 * 1e4, 2000,
                                          bbase::utils::Time::GetTimeNs());
    ASSERT_FALSE(err.HasErr());
    auto result = te->PopResult(10000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::settle_funding_fee);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_funding_records().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_funding_records().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_funding_records().at(0).after_pz_term(), 0);
    std::cout << "first pop result uid:" << result.m_msg->header().user_id() << '\n';
    if (result.m_msg->header().user_id() == uid1) {
      EXPECT_EQ(result.m_msg->futures_margin_result().related_funding_records().at(0).exec_fee_e8(),
                100 * 20000 * 0.2);  // Buy持仓fee_rate=req.fee_rate
    } else if (result.m_msg->header().user_id() == uid2) {
      EXPECT_EQ(result.m_msg->futures_margin_result().related_funding_records().at(0).exec_fee_e8(),
                100 * 20000 * -0.2);  // Sell持仓fee_rate = -req.fee_rate
    }
    auto result2 = te->PopResult(10000);
    ASSERT_NE(result2.m_msg.get(), nullptr);
    EXPECT_EQ(result2.m_msg->header().action(), EAction::settle_funding_fee);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_funding_records().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_funding_records().at(0).before_pz_term(), 0);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_funding_records().at(0).after_pz_term(), 0);
    if (result2.m_msg->header().user_id() == uid1) {
      EXPECT_EQ(result2.m_msg->futures_margin_result().related_funding_records().at(0).exec_fee_e8(),
                100 * 20000 * 0.2);  // Buy持仓fee_rate=req.fee_rate
    } else if (result2.m_msg->header().user_id() == uid2) {
      EXPECT_EQ(result2.m_msg->futures_margin_result().related_funding_records().at(0).exec_fee_e8(),
                100 * 20000 * -0.2);  // Sell持仓fee_rate = -req.fee_rate
    }
  }

  // user1 -50@20000, 成交后仓位:+100 -> +50
  std::string replace_ord_id{};
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Sell,
                                                EOrderType::Limit, 40 * 1e4, 20100 * 1e4, ETimeInForce::GoodTillCancel,
                                                uid1, ECoin::USDT);
    auto resp = user1.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    replace_ord_id.append(result.m_msg->futures_margin_result().related_orders()[0].order_id());
  }

  // user2 +50@20000, 成交后仓位:-100 -> -50
  {
    FutureOneOfCreateOrderBuilder place_order_b(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                                EOrderType::Limit, 20 * 1e4, 20100 * 1e4,
                                                ETimeInForce::ImmediateOrCancel, uid2, ECoin::USDT);
    te->SuspendCross();
    auto resp = user2.process(place_order_b.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);

    // 成交之前发出改单
    FutureOneOfReplaceOrderBuilder replace_b(replace_ord_id, ESymbol::BTCUSDT, 50 * 1e4, 20100 * 1e4, uid1,
                                             ECoin::USDT);
    auto resp2 = user2.process(replace_b.Build());
    ASSERT_NE(resp2, nullptr);
    te->ResumeCross();
    // user2 下买单时的result
    te->PopResult();
    // user1 成交的result
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    EXPECT_EQ(result2.m_msg->header().user_id(), uid1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().size(), 1);
    ASSERT_EQ(result2.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 80 * 1e4);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().at(0).term(), 0);
    ASSERT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().size(), 1);
    auto cum_pnl1 = result2.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pnl_e8();
    // EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_open_fee_e8(),
    //           50 * 20000 * 1);
    // EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_funding_fee_e8(),
    //           200000);
    // cum_closed_fee_user1 += result2.m_msg->futures_margin_result().related_orders().at(0).exec_fee_e8();
    // EXPECT_EQ(result2.m_msg->futures_margin_result().related_closed_pnl_records().at(0).cum_closed_pz_trade_fee_e8(),
    //           cum_closed_fee_user1);
    // 改单回报的result
    auto result_replace = te->PopResult();
    ASSERT_NE(result_replace.m_msg.get(), nullptr);
    EXPECT_EQ(result_replace.m_msg->header().user_id(), uid1);
    EXPECT_EQ(result_replace.m_msg->futures_margin_result().related_orders().size(), 1);
    ASSERT_EQ(result_replace.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result_replace.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 80 * 1e4);
    EXPECT_EQ(result_replace.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result_replace.m_msg->futures_margin_result().affected_positions().at(0).term(), 0);
    auto cum_pnl2 = result_replace.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pnl_e8();
    EXPECT_EQ(cum_pnl1, cum_pnl2);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).fill_count(),
              result_replace.m_msg->futures_margin_result().related_orders().at(0).fill_count());
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cum_closed_size_x(),
              result_replace.m_msg->futures_margin_result().related_orders().at(0).cum_closed_size_x());
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cum_exit_value_e8(),
              result_replace.m_msg->futures_margin_result().related_orders().at(0).cum_exit_value_e8());
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cum_entry_value_e8(),
              result_replace.m_msg->futures_margin_result().related_orders().at(0).cum_entry_value_e8());
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pnl_e8(),
              result_replace.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pnl_e8());
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_open_fee_e8(),
              result_replace.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_open_fee_e8());
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_funding_fee_e8(),
              result_replace.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_funding_fee_e8());
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_trade_fee_e8(),
              result_replace.m_msg->futures_margin_result().related_orders().at(0).cum_closed_pz_trade_fee_e8());
  }
}
