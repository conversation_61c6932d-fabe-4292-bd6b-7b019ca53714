#include "test/biz/trade/future/position/contract_protect.hpp"

#include <string>

#include "src/biz_worker/service/trade/liquidation/event/liq_event.hpp"
#include "src/biz_worker/service/trade/store/passthrough/raw_contract.hpp"
#include "src/biz_worker/utils/pb_convertor/to_pb.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"

/*
 * 1. 反向单导致反向开仓
 * 2. 反向单导致平仓
 * 3. 手动平仓
 * 4. 仓位被接管
 */

// 反向单导致反向开仓
TEST_F(PerpProtect, perp_reverse_order_to_reverse_position) {
  // 1. 构造期货仓位
  // 2. 给对应的期货仓位下期权订单
  // 3. 期权订单成交, 期货仓位打标成功, 查看打标关系
  // 4. 期货仓位平仓

  // 构造test_user并充值
  biz::user_id_t const uid = 10000;
  stub user(te, uid);
  {
    // 设置该用户是Cross模式
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Cross);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->asset_margin_result().per_user_setting_data().accountmode(), EAccountMode::Cross);
  }
  {
    // 充值
    std::string const req_id = "";
    std::string const trans_id = "";
    biz::coin_t const coin = 5;
    std::int32_t const wallet_record_type = 1;
    std::string const amount = "*********";
    std::string const bonus_change = "123";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp = user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  // 构造other_user并充值
  biz::user_id_t const other_uid = 10001;
  stub other_user(te, other_uid);
  {
    std::string const req_id = "";
    std::string const trans_id = "";
    biz::coin_t const coin = 5;
    std::int32_t const wallet_record_type = 1;
    std::string const amount = "*********";
    std::string const bonus_change = "123";
    FutureDepositBuilder deposit_build(req_id, other_uid, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp = other_user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);
  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  // 1. 构造期货仓位
  {
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.4", "20000");
    auto order_linked_id = te->GenUUID();
    create_build.SetOrderLinkID(order_linked_id);
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    OpenApiV5CreateOrderBuilder create_build_2("linear", "BTCUSDT", 0, "Sell", "Limit", "0.4", "20000");
    order_linked_id = te->GenUUID();
    create_build_2.SetOrderLinkID(order_linked_id);
    resp = other_user.create_order(create_build_2.Build());
    ASSERT_NE(resp, nullptr);
    //    std::string msg{};
    //    std::cout << "Sell:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0.4 * 1e8);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 0);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid);
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0.4 * 1e8);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 0);
  }

  /*
   * Option Place Protected Order
   */
  {
    std::string const symbol = "BTC-31DEC21-40000-C";
    ESide side = ESide::Sell;
    EOrderType const order_type = EOrderType::Limit;
    std::string const qty = "0.01";
    std::string const price = "200";
    ETimeInForce time_in_force = ETimeInForce::GoodTillCancel;
    std::string order_link_id = te->GenUUID();
    std::int32_t const place_mode = EPlaceMode::Advanced;
    std::int32_t const place_type = EPlaceType::Price;
    {
      // 对手方先挂个sell单
      OptionSiteApiCreateOrderBuilder create_ao_builder(symbol, side, order_type, qty, price, time_in_force,
                                                        te->GenUUID(), place_mode, place_type);
      auto resp = other_user.OptionSiteApiCreateOrder(create_ao_builder.Build());
      ASSERT_EQ(resp->retmsg(), "OK");
      //      std::string msg = {};
      //      std::cout << "option other user resp:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      msg = {};
      //      std::cout << "option other user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg)
      //      <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    }

    // trading-block下保护合约
    {
      side = ESide::Buy;
      order_link_id = te->GenUUID();
      time_in_force = ETimeInForce::ImmediateOrCancel;  // 保护合约单都是IOC订单
      OptionSiteApiCreateOrderBuilder create_ao_builder(symbol, side, order_type, qty, price, time_in_force,
                                                        order_link_id, place_mode, place_type);
      svc::uta_engine::option::InnerOrderRequest place_option_protected_order_req;
      place_option_protected_order_req.mutable_createorderrequest()->CopyFrom(create_ao_builder.Build());
      place_option_protected_order_req.set_userid(uid);
      place_option_protected_order_req.set_accountid(uid);
      place_option_protected_order_req.set_tradesource("trading-block");
      place_option_protected_order_req.set_requesttype("contract_protect");
      place_option_protected_order_req.set_refsymbol("BTCUSDT");
      place_option_protected_order_req.set_refpzidx(0);
      place_option_protected_order_req.set_refside("Buy");

      auto resp = user.OptionInnerCreateOrder(place_option_protected_order_req);
      ASSERT_EQ(resp->retmsg(), "OK");
      //      std::string msg{};
      //      std::cout << "option user resp:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      msg = {};
      //      std::cout << "option user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg) <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), uid);
      ASSERT_EQ(result.m_msg->options_margin_result().related_orders().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).order_id(), resp->result().orderid());
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).ref_info_list().at(0).ref_symbol(),
                ESymbol::BTCUSDT);
      auto protect_option_order_id = result.m_msg->options_margin_result().related_orders().at(0).order_id();

      // 期权成交后, 校验打标结果
      ASSERT_EQ(result.m_msg->options_margin_result().related_fills().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().related_fills().at(0).exec_qty_x(), 100);

      ASSERT_EQ(result.m_msg->options_margin_result().affected_positions().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_symbol(),
                ESymbol::BTCUSDT);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_pz_idx(),
                EPositionIndex::Single);

      ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).symbol(), ESymbol::BTCUSDT);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);

      auto symbol_dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName(symbol);
      ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().size(), 1);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_symbol(),
                symbol_dto->id);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_order_id(),
                protect_option_order_id);
    }

    // other user 的 maker fill
    {
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      std::string msg{};
      //      std::cout << "option other user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg)
      //      <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    }
  }

  {
    // 反向下单, 反向开仓
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.6", "20000");
    auto order_linked_id = te->GenUUID();
    create_build.SetOrderLinkID(order_linked_id);
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    OpenApiV5CreateOrderBuilder create_build_2("linear", "BTCUSDT", 0, "Buy", "Limit", "0.6", "20000");
    order_linked_id = te->GenUUID();
    create_build_2.SetOrderLinkID(order_linked_id);
    resp = other_user.create_order(create_build_2.Build());
    ASSERT_NE(resp, nullptr);
    //    std::string msg{};
    //    std::cout << "Sell:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0.2 * 1e8);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 1);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    //    std::string msg{};
    //    std::cout << "Sell:" << utils::PbMessageToJsonString(*result.m_msg, &msg) << '\n';
    EXPECT_EQ(result.m_msg->header().user_id(), uid);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0.2 * 1e8);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().size(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 1);

    EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().size(), 1);
    auto symbol_dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName("BTC-31DEC21-40000-C");
    EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).symbol(), symbol_dto->id);
    EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().size(), 0);
  }
}

// 反向单导致平仓
TEST_F(PerpProtect, perp_reverse_order_to_close_position) {
  // 1. 构造期货仓位
  // 2. 给对应的期货仓位下期权订单
  // 3. 期权订单成交, 期货仓位打标成功, 查看打标关系
  // 4. 期货仓位平仓

  // 构造test_user并充值
  biz::user_id_t const uid = 10003;
  stub user(te, uid);
  {
    // 设置该用户是Cross模式
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Cross);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->asset_margin_result().per_user_setting_data().accountmode(), EAccountMode::Cross);
  }
  {
    // 充值
    std::string const req_id = "";
    std::string const trans_id = "";
    biz::coin_t const coin = 5;
    std::int32_t const wallet_record_type = 1;
    std::string const amount = "*********";
    std::string const bonus_change = "123";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp = user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  // 构造other_user并充值
  biz::user_id_t const other_uid = 10004;
  stub other_user(te, other_uid);
  {
    std::string const req_id = "";
    std::string const trans_id = "";
    biz::coin_t const coin = 5;
    std::int32_t const wallet_record_type = 1;
    std::string const amount = "*********";
    std::string const bonus_change = "123";
    FutureDepositBuilder deposit_build(req_id, other_uid, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp = other_user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);
  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  // 1. 构造期货仓位
  {
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.4", "20000");
    auto order_linked_id = te->GenUUID();
    create_build.SetOrderLinkID(order_linked_id);
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    OpenApiV5CreateOrderBuilder create_build_2("linear", "BTCUSDT", 0, "Sell", "Limit", "0.4", "20000");
    order_linked_id = te->GenUUID();
    create_build_2.SetOrderLinkID(order_linked_id);
    resp = other_user.create_order(create_build_2.Build());
    ASSERT_NE(resp, nullptr);
    //    std::string msg{};
    //    std::cout << "Sell:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0.4 * 1e8);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 0);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0.4 * 1e8);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 0);
  }

  /*
   * Option Place Protected Order
   */
  {
    std::string const symbol = "BTC-31DEC21-40000-C";
    ESide side = ESide::Sell;
    EOrderType const order_type = EOrderType::Limit;
    std::string const qty = "0.01";
    std::string const price = "200";
    ETimeInForce time_in_force = ETimeInForce::GoodTillCancel;
    std::string order_link_id = te->GenUUID();
    std::int32_t const place_mode = EPlaceMode::Advanced;
    std::int32_t const place_type = EPlaceType::Price;
    {
      // 对手方先挂个sell单
      OptionSiteApiCreateOrderBuilder create_ao_builder(symbol, side, order_type, qty, price, time_in_force,
                                                        te->GenUUID(), place_mode, place_type);
      auto resp = other_user.OptionSiteApiCreateOrder(create_ao_builder.Build());
      ASSERT_EQ(resp->retmsg(), "OK");
      //      std::string msg = {};
      //      std::cout << "option other user resp:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      msg = {};
      //      std::cout << "option other user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg)
      //      <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    }

    // trading-block下保护合约
    {
      side = ESide::Buy;
      order_link_id = te->GenUUID();
      time_in_force = ETimeInForce::ImmediateOrCancel;  // 保护合约单都是IOC订单
      OptionSiteApiCreateOrderBuilder create_ao_builder(symbol, side, order_type, qty, price, time_in_force,
                                                        order_link_id, place_mode, place_type);
      svc::uta_engine::option::InnerOrderRequest place_option_protected_order_req;
      place_option_protected_order_req.mutable_createorderrequest()->CopyFrom(create_ao_builder.Build());
      place_option_protected_order_req.set_userid(uid);
      place_option_protected_order_req.set_accountid(uid);
      place_option_protected_order_req.set_tradesource("trading-block");
      place_option_protected_order_req.set_requesttype("contract_protect");
      place_option_protected_order_req.set_refsymbol("BTCUSDT");
      place_option_protected_order_req.set_refpzidx(0);
      place_option_protected_order_req.set_refside("Buy");

      auto resp = user.OptionInnerCreateOrder(place_option_protected_order_req);
      ASSERT_EQ(resp->retmsg(), "OK");
      //      std::string msg{};
      //      std::cout << "option user resp:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      msg = {};
      //      std::cout << "option user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg) <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), uid);
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).order_id(), resp->result().orderid());
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).ref_info_list().at(0).ref_symbol(),
                ESymbol::BTCUSDT);
      auto protect_option_order_id = result.m_msg->options_margin_result().related_orders().at(0).order_id();

      // 期权成交后, 校验打标结果
      EXPECT_EQ(result.m_msg->options_margin_result().related_fills().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().related_fills().at(0).exec_qty_x(), 100);

      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_symbol(),
                ESymbol::BTCUSDT);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_pz_idx(),
                EPositionIndex::Single);

      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).symbol(), ESymbol::BTCUSDT);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);
      auto symbol_dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName(symbol);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_symbol(),
                symbol_dto->id);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_order_id(),
                protect_option_order_id);
    }

    // other user 的 maker fill
    {
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      std::string msg{};
      //      std::cout << "option other user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg)
      //      <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    }
  }

  {
    // 反向下单, 平仓
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.4", "20000");
    auto order_linked_id = te->GenUUID();
    create_build.SetOrderLinkID(order_linked_id);
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    OpenApiV5CreateOrderBuilder create_build_2("linear", "BTCUSDT", 0, "Buy", "Limit", "0.4", "20000");
    order_linked_id = te->GenUUID();
    create_build_2.SetOrderLinkID(order_linked_id);
    resp = other_user.create_order(create_build_2.Build());
    ASSERT_NE(resp, nullptr);
    //    std::string msg{};
    //    std::cout << "Sell:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::None);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 1);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::None);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().size(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 1);

    EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().size(), 1);
    auto symbol_dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName("BTC-31DEC21-40000-C");
    EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).symbol(), symbol_dto->id);
    EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().size(), 0);
  }
}

TEST_F(PerpProtect, perp_position_closed) {
  // 1. 构造期货仓位
  // 2. 给对应的期货仓位下期权订单
  // 3. 期权订单成交, 期货仓位打标成功, 查看打标关系
  // 4. 期货仓位平仓

  // 构造test_user并充值
  biz::user_id_t const uid = 10005;
  stub user(te, uid);
  {
    // 设置该用户是Cross模式
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Cross);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->asset_margin_result().per_user_setting_data().accountmode(), EAccountMode::Cross);
  }
  {
    // 充值
    std::string const req_id = "";
    std::string const trans_id = "";
    biz::coin_t const coin = 5;
    std::int32_t const wallet_record_type = 1;
    std::string const amount = "*********";
    std::string const bonus_change = "123";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp = user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  // 构造other_user并充值
  biz::user_id_t const other_uid = 10006;
  stub other_user(te, other_uid);
  {
    std::string const req_id = "";
    std::string const trans_id = "";
    biz::coin_t const coin = 5;
    std::int32_t const wallet_record_type = 1;
    std::string const amount = "*********";
    std::string const bonus_change = "123";
    FutureDepositBuilder deposit_build(req_id, other_uid, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp = other_user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);
  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  // last price 校准
  bool sync_trigger = true;
  auto lv0_px = "20000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(ESymbol::BTCUSDT, ECrossIdx::BTCLP, lv0_px, lv0_qty, sync_trigger);

  // 1. 构造期货仓位
  {
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.4", "20000");
    auto order_linked_id = te->GenUUID();
    create_build.SetOrderLinkID(order_linked_id);
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    OpenApiV5CreateOrderBuilder create_build_2("linear", "BTCUSDT", 0, "Sell", "Limit", "0.4", "20000");
    order_linked_id = te->GenUUID();
    create_build_2.SetOrderLinkID(order_linked_id);
    resp = other_user.create_order(create_build_2.Build());
    ASSERT_NE(resp, nullptr);
    //    std::string msg{};
    //    std::cout << "Sell:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0.4 * 1e8);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 0);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0.4 * 1e8);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 0);
  }

  /*
   * Option Place Protected Order
   */
  {
    std::string const symbol = "BTC-31DEC21-40000-C";
    ESide side = ESide::Sell;
    EOrderType const order_type = EOrderType::Limit;
    std::string const qty = "0.01";
    std::string const price = "200";
    ETimeInForce time_in_force = ETimeInForce::GoodTillCancel;
    std::string order_link_id = te->GenUUID();
    std::int32_t const place_mode = EPlaceMode::Advanced;
    std::int32_t const place_type = EPlaceType::Price;
    {
      // 对手方先挂个sell单
      OptionSiteApiCreateOrderBuilder create_ao_builder(symbol, side, order_type, qty, price, time_in_force,
                                                        te->GenUUID(), place_mode, place_type);
      auto resp = other_user.OptionSiteApiCreateOrder(create_ao_builder.Build());
      ASSERT_EQ(resp->retmsg(), "OK");
      //      std::string msg = {};
      //      std::cout << "option other user resp:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      msg = {};
      //      std::cout << "option other user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg)
      //      <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    }

    // trading-block下保护合约
    {
      side = ESide::Buy;
      order_link_id = te->GenUUID();
      time_in_force = ETimeInForce::ImmediateOrCancel;  // 保护合约单都是IOC订单
      OptionSiteApiCreateOrderBuilder create_ao_builder(symbol, side, order_type, qty, price, time_in_force,
                                                        order_link_id, place_mode, place_type);
      svc::uta_engine::option::InnerOrderRequest place_option_protected_order_req;
      place_option_protected_order_req.mutable_createorderrequest()->CopyFrom(create_ao_builder.Build());
      place_option_protected_order_req.set_userid(uid);
      place_option_protected_order_req.set_accountid(uid);
      place_option_protected_order_req.set_tradesource("trading-block");
      place_option_protected_order_req.set_requesttype("contract_protect");
      place_option_protected_order_req.set_refsymbol("BTCUSDT");
      place_option_protected_order_req.set_refpzidx(0);
      place_option_protected_order_req.set_refside("Buy");

      auto resp = user.OptionInnerCreateOrder(place_option_protected_order_req);
      ASSERT_EQ(resp->retmsg(), "OK");
      //      std::string msg{};
      //      std::cout << "option user resp:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      msg = {};
      //      std::cout << "option user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg) <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), uid);
      ASSERT_EQ(result.m_msg->options_margin_result().related_orders().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).order_id(), resp->result().orderid());
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).ref_info_list().at(0).ref_symbol(),
                ESymbol::BTCUSDT);
      auto protect_option_order_id = result.m_msg->options_margin_result().related_orders().at(0).order_id();

      // 期权成交后, 校验打标结果
      ASSERT_EQ(result.m_msg->options_margin_result().related_fills().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().related_fills().at(0).exec_qty_x(), 100);

      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_symbol(),
                ESymbol::BTCUSDT);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_pz_idx(),
                EPositionIndex::Single);

      ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).symbol(), ESymbol::BTCUSDT);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);
      auto symbol_dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName(symbol);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_symbol(),
                symbol_dto->id);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_order_id(),
                protect_option_order_id);
    }

    // other user 的 maker fill
    {
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      std::string msg{};
      //      std::cout << "option other user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg)
      //      <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    }
  }

  {
    // 先造ob, 后面执行CloseAllPosition
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.4", "20000");
    auto order_linked_id = te->GenUUID();
    create_build.SetOrderLinkID(order_linked_id);
    auto resp = other_user.create_order(create_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 平仓
    CloseAllPositionReqBuilder close_pz_build(ESymbol::BTCUSDT, uid, ECoin::USDT);
    auto resp2 = user.process(close_pz_build.Build());
    ASSERT_NE(resp2, nullptr);
    //    std::string msg{};
    //    std::cout << "close position resp:" << utils::PbMessageToJsonString(*resp2, &msg) << '\n';

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::None);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 1);
    EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().size(), 1);
    auto symbol_dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName("BTC-31DEC21-40000-C");
    EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).symbol(), symbol_dto->id);
    EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().size(), 0);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::None);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().size(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 1);
  }
}

TEST_F(PerpProtect, perp_position_taken_over) {
  // 构造test_user并充值
  biz::user_id_t const uid = 10007;
  stub user(te, uid);
  {
    // 设置该用户是Cross模式
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Cross);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->asset_margin_result().per_user_setting_data().accountmode(), EAccountMode::Cross);
  }
  {
    // 充值
    std::string const req_id = "";
    std::string const trans_id = "";
    biz::coin_t const coin = 5;
    std::int32_t const wallet_record_type = 1;
    std::string const amount = "*********";
    std::string const bonus_change = "123";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp = user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  // 构造other_user并充值
  biz::user_id_t const other_uid = 10008;
  stub other_user(te, other_uid);
  {
    std::string const req_id = "";
    std::string const trans_id = "";
    biz::coin_t const coin = 5;
    std::int32_t const wallet_record_type = 1;
    std::string const amount = "*********";
    std::string const bonus_change = "123";
    FutureDepositBuilder deposit_build(req_id, other_uid, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp = other_user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);
  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  // last price 校准
  bool sync_trigger = true;
  auto lv0_px = "20000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(ESymbol::BTCUSDT, ECrossIdx::BTCLP, lv0_px, lv0_qty, sync_trigger);

  // 1. 构造期货仓位
  {
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.4", "20000");
    auto order_linked_id = te->GenUUID();
    create_build.SetOrderLinkID(order_linked_id);
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    OpenApiV5CreateOrderBuilder create_build_2("linear", "BTCUSDT", 0, "Sell", "Limit", "0.4", "20000");
    order_linked_id = te->GenUUID();
    create_build_2.SetOrderLinkID(order_linked_id);
    resp = other_user.create_order(create_build_2.Build());
    ASSERT_NE(resp, nullptr);
    //    std::string msg{};
    //    std::cout << "Sell:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0.4 * 1e8);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 0);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0.4 * 1e8);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 0);
  }

  /*
   * Option Place Protected Order
   */
  {
    std::string const symbol = "BTC-31DEC21-40000-C";
    ESide side = ESide::Sell;
    EOrderType const order_type = EOrderType::Limit;
    std::string const qty = "0.01";
    std::string const price = "200";
    ETimeInForce time_in_force = ETimeInForce::GoodTillCancel;
    std::string order_link_id = te->GenUUID();
    std::int32_t const place_mode = EPlaceMode::Advanced;
    std::int32_t const place_type = EPlaceType::Price;
    {
      // 对手方先挂个sell单
      OptionSiteApiCreateOrderBuilder create_ao_builder(symbol, side, order_type, qty, price, time_in_force,
                                                        te->GenUUID(), place_mode, place_type);
      auto resp = other_user.OptionSiteApiCreateOrder(create_ao_builder.Build());
      ASSERT_EQ(resp->retmsg(), "OK");
      //      std::string msg = {};
      //      std::cout << "option other user resp:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      msg = {};
      //      std::cout << "option other user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg)
      //      <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    }

    // trading-block下保护合约
    {
      side = ESide::Buy;
      order_link_id = te->GenUUID();
      time_in_force = ETimeInForce::ImmediateOrCancel;  // 保护合约单都是IOC订单
      OptionSiteApiCreateOrderBuilder create_ao_builder(symbol, side, order_type, qty, price, time_in_force,
                                                        order_link_id, place_mode, place_type);
      svc::uta_engine::option::InnerOrderRequest place_option_protected_order_req;
      place_option_protected_order_req.mutable_createorderrequest()->CopyFrom(create_ao_builder.Build());
      place_option_protected_order_req.set_userid(uid);
      place_option_protected_order_req.set_accountid(uid);
      place_option_protected_order_req.set_tradesource("trading-block");
      place_option_protected_order_req.set_requesttype("contract_protect");
      place_option_protected_order_req.set_refsymbol("BTCUSDT");
      place_option_protected_order_req.set_refpzidx(0);
      place_option_protected_order_req.set_refside("Buy");

      auto resp = user.OptionInnerCreateOrder(place_option_protected_order_req);
      ASSERT_EQ(resp->retmsg(), "OK");
      //      std::string msg{};
      //      std::cout << "option user resp:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      msg = {};
      //      std::cout << "option user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg) <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), uid);
      ASSERT_EQ(result.m_msg->options_margin_result().related_orders().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).order_id(), resp->result().orderid());
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).ref_info_list().at(0).ref_symbol(),
                ESymbol::BTCUSDT);
      auto protect_option_order_id = result.m_msg->options_margin_result().related_orders().at(0).order_id();

      // 期权成交后, 校验打标结果
      ASSERT_EQ(result.m_msg->options_margin_result().related_fills().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().related_fills().at(0).exec_qty_x(), 100);

      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_symbol(),
                ESymbol::BTCUSDT);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_pz_idx(),
                EPositionIndex::Single);

      ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).symbol(), ESymbol::BTCUSDT);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);
      auto symbol_dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName(symbol);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_symbol(),
                symbol_dto->id);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_order_id(),
                protect_option_order_id);
    }

    // other user 的 maker fill
    {
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      std::string msg{};
      //      std::cout << "option other user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg)
      //      <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    }
  }

  {
    // 触发接管
    auto event = std::make_shared<event::OnLiqEvent>(uid);
    event->execute_action = ELiqStep::FutureTakeOver;
    event->execute_product = EProductType::Futures;
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    //    std::string msg{};
    //    std::cout << "taken over result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg) << '\n';
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    auto affected_position = result.m_msg->futures_margin_result().affected_positions().at(0);
    EXPECT_EQ(affected_position.size_x(), 0);
    EXPECT_EQ(affected_position.position_balance_e8(), 0);
    EXPECT_EQ(affected_position.term(), 1);
    EXPECT_EQ(affected_position.ref_info_list().size(), 0);
    ASSERT_EQ(result.m_msg->options_margin_result().affected_positions().size(), 1);
    auto symbol_dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName("BTC-31DEC21-40000-C");
    EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).symbol(), symbol_dto->id);
    EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().size(), 0);
  }
}

void MyBuildProduceMsg(biz::cross_idx_t cross_idx, biz::symbol_t symbol, biz::coin_t coin,
                       biz::cross::xReqSender& sender, bbase::hdts::Producer::Message& msg) {
  biz::CrossError err;
  /// 组装topic
  msg.topic = fmt::format("request_of_{}", cross_idx);

  /// 组装buf
  msg.value.resize(sizeof(biz::cross::RawXHeader) + sizeof(biz::cross::request::RawXRequest));
  memcpy(msg.value.data(), &sender.req_mgr_.header_obj_->header_, sizeof(biz::cross::RawXHeader));
  sender.req_mgr_.request_obj_->ToBinaryBuf(static_cast<char*>(msg.value.data()) + sizeof(biz::cross::RawXHeader));

  /// 处理透传数据 passthrough
  ::models::passthroughdto::PassThroughDTO passthrough;

  auto contract_info = passthrough.mutable_contract_info();
  contract_info->set_symbol(static_cast<ESymbol>(symbol));
  contract_info->set_coin(static_cast<ECoin>(coin));
  contract_info->set_expect_settle_price_x(20000 * 1e4);
  contract_info->set_settle_time_e9(1577808000);
  contract_info->set_settle_fee_rate_e8(0);
  contract_info->set_contract_status(EContractStatus::Settling);

  auto value = passthrough.SerializeAsString();
  msg.headers[biz::XComm::x_pass_through] = value;
}

/**
 * 发送交割信息
 */
void MySendSettleMessage() {
  bbase::hdts::Producer::Message msg;

  biz::cross::xReqSender sender;
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  auto contract_info = std::make_shared<store::Contract>();
  contract_info->set_contract_status(static_cast<EContractStatus>(EContractStatus::Settling));
  convertor::ToPB::Convert(contract_info.get(), sender.req_mgr_.x_pass_through_.value().mutable_contract_info());

  auto err = sender.BuildHeader(biz::cross::request::kMTContract, ECreateType::UNKNOWN);
  if (err.HasErr()) {
    return;
  }

  /// 组装req写入值确保下游不会panic
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTContract;
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.side_ = biz::cross::request::kSideBuy;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = biz::cross::request::kTIFGoodTillCancel;
  sender.req_mgr_.request_obj_->req_.order_type_ = biz::cross::request::kOTMarketOrder;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);

  bbase::hdts::Producer::Message produce_msg;
  MyBuildProduceMsg(5, 5, 5, sender, produce_msg);
  /// #3: 发送数据
  produce_msg.headers[biz::XComm::send_to_cross] = std::to_string(bbase::utils::Time::GetTimeNs());
  produce_msg.need_callback = true;
  produce_msg.callback_data.data = nullptr;
  produce_msg.callback_data.start_timestamp_ns = bbase::utils::Time::GetTimeNs();

  auto bRet = bbase::hdts::Hdts::ProduceMessage(produce_msg);
  EXPECT_EQ(bRet, 1);
}

TEST_F(PerpProtect, perp_position_settle) {
  // 构造test_user并充值
  biz::user_id_t const uid = 10009;
  stub user(te, uid);
  {
    // 设置该用户是Cross模式
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Cross);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->asset_margin_result().per_user_setting_data().accountmode(), EAccountMode::Cross);
  }
  {
    // 充值
    std::string const req_id = "";
    std::string const trans_id = "";
    biz::coin_t const coin = 5;
    std::int32_t const wallet_record_type = 1;
    std::string const amount = "*********";
    std::string const bonus_change = "123";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp = user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  // 构造other_user并充值
  biz::user_id_t const other_uid = 10010;
  stub other_user(te, other_uid);
  {
    std::string const req_id = "";
    std::string const trans_id = "";
    biz::coin_t const coin = 5;
    std::int32_t const wallet_record_type = 1;
    std::string const amount = "*********";
    std::string const bonus_change = "123";
    FutureDepositBuilder deposit_build(req_id, other_uid, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp = other_user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);
  te->AddOptionMarkPrice(static_cast<ESymbol>(102311), 20000, 20000, 20000);

  // last price 校准
  bool sync_trigger = true;
  auto lv0_px = "20000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(ESymbol::BTCUSDT, ECrossIdx::BTCLP, lv0_px, lv0_qty, sync_trigger);

  // 1. 构造期货仓位
  {
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.4", "20000");
    auto order_linked_id = te->GenUUID();
    create_build.SetOrderLinkID(order_linked_id);
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    OpenApiV5CreateOrderBuilder create_build_2("linear", "BTCUSDT", 0, "Sell", "Limit", "0.4", "20000");
    order_linked_id = te->GenUUID();
    create_build_2.SetOrderLinkID(order_linked_id);
    resp = other_user.create_order(create_build_2.Build());
    ASSERT_NE(resp, nullptr);
    //    std::string msg{};
    //    std::cout << "Sell:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';
    EXPECT_EQ(resp->order_link_id(), order_linked_id);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0.4 * 1e8);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 0);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), uid);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ESide::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 0.4 * 1e8);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).before_pz_term(), 0);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).after_pz_term(), 0);
  }

  /*
   * Option Place Protected Order
   */
  {
    std::string const symbol = "BTC-31DEC21-40000-C";
    ESide side = ESide::Sell;
    EOrderType const order_type = EOrderType::Limit;
    std::string const qty = "0.01";
    std::string const price = "200";
    ETimeInForce time_in_force = ETimeInForce::GoodTillCancel;
    std::string order_link_id = te->GenUUID();
    std::int32_t const place_mode = EPlaceMode::Advanced;
    std::int32_t const place_type = EPlaceType::Price;
    {
      // 对手方先挂个sell单
      OptionSiteApiCreateOrderBuilder create_ao_builder(symbol, side, order_type, qty, price, time_in_force,
                                                        te->GenUUID(), place_mode, place_type);
      auto resp = other_user.OptionSiteApiCreateOrder(create_ao_builder.Build());
      ASSERT_EQ(resp->retmsg(), "OK");
      //      std::string msg = {};
      //      std::cout << "option other user resp:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      msg = {};
      //      std::cout << "option other user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg)
      //      <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    }

    // trading-block下保护合约
    {
      side = ESide::Buy;
      order_link_id = te->GenUUID();
      time_in_force = ETimeInForce::ImmediateOrCancel;  // 保护合约单都是IOC订单
      OptionSiteApiCreateOrderBuilder create_ao_builder(symbol, side, order_type, qty, price, time_in_force,
                                                        order_link_id, place_mode, place_type);
      svc::uta_engine::option::InnerOrderRequest place_option_protected_order_req;
      place_option_protected_order_req.mutable_createorderrequest()->CopyFrom(create_ao_builder.Build());
      place_option_protected_order_req.set_userid(uid);
      place_option_protected_order_req.set_accountid(uid);
      place_option_protected_order_req.set_tradesource("trading-block");
      place_option_protected_order_req.set_requesttype("contract_protect");
      place_option_protected_order_req.set_refsymbol("BTCUSDT");
      place_option_protected_order_req.set_refpzidx(0);
      place_option_protected_order_req.set_refside("Buy");

      auto resp = user.OptionInnerCreateOrder(place_option_protected_order_req);
      ASSERT_EQ(resp->retmsg(), "OK");
      //      std::string msg{};
      //      std::cout << "option user resp:" << utils::PbMessageToJsonString(*resp, &msg) << '\n';

      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      msg = {};
      //      std::cout << "option user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg) <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), uid);
      ASSERT_EQ(result.m_msg->options_margin_result().related_orders().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).order_id(), resp->result().orderid());
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);
      EXPECT_EQ(result.m_msg->options_margin_result().related_orders().at(0).ref_info_list().at(0).ref_symbol(),
                ESymbol::BTCUSDT);
      auto protect_option_order_id = result.m_msg->options_margin_result().related_orders().at(0).order_id();

      // 期权成交后, 校验打标结果
      ASSERT_EQ(result.m_msg->options_margin_result().related_fills().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().related_fills().at(0).exec_qty_x(), 100);

      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().size(), 1);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_symbol(),
                ESymbol::BTCUSDT);
      EXPECT_EQ(result.m_msg->options_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_pz_idx(),
                EPositionIndex::Single);

      ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).symbol(), ESymbol::BTCUSDT);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_type(),
                ERefType::ContractProtect);
      auto symbol_dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName(symbol);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_symbol(),
                symbol_dto->id);
      EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).ref_info_list().at(0).ref_order_id(),
                protect_option_order_id);
    }

    // other user 的 maker fill
    {
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      //      std::string msg{};
      //      std::cout << "option other user result:" << utils::PbMessageToJsonString(*result.m_msg.get(), &msg)
      //      <<
      //      '\n';
      EXPECT_EQ(result.m_msg->header().user_id(), other_uid);
    }
  }

  // 发送交割请求, 会把user和other_user的仓位都给交割掉.
  MySendSettleMessage();
  for (std::int32_t i = 0; i != 10; ++i) {
    auto result = te->PopResult();
    if (result.m_msg == nullptr) {
      break;
    }
    ASSERT_NE(result.m_msg, nullptr);
    //    std::string msg{};
    //    std::cout << utils::PbMessageToJsonString(*result.m_msg, &msg) << '\n';
    if (result.m_msg->header().user_id() == uid) {                                      // 有合约保护仓位的
      ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);  // 仓位全部被清掉
      auto perp_pz = result.m_msg->futures_margin_result().affected_positions().at(0);
      EXPECT_EQ(perp_pz.size_x(), 0);
      EXPECT_EQ(perp_pz.ref_info_list().size(), 0);
      ASSERT_EQ(result.m_msg->options_margin_result().affected_positions().size(), 1);
      auto option_pz = result.m_msg->options_margin_result().affected_positions().at(0);
      auto symbol_dto = config::getTlsCfgMgrRaw()->option_symbol_svc()->GetSymbolByName("BTC-31DEC21-40000-C");
      EXPECT_EQ(option_pz.symbol(), symbol_dto->id);
      EXPECT_EQ(option_pz.ref_info_list().size(), 0);
    }
  }
}
std::shared_ptr<tmock::CTradeAppMock> PerpProtect::te;
