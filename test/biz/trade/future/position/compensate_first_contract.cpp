#include "test/biz/trade/future/position/compensate_first_contract.hpp"

#include "lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

std::shared_ptr<tmock::CTradeAppMock> CompensateFirstContractTest::te;

void CompensateFirstContractTest::Deposit(int64_t uid, biz::coin_t coin, std::string amount) {
  stub user1(te, uid);
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "123";

  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

  auto resp = user1.process(deposit_build.Build());
  auto result = te->PopResult();
  ASSERT_EQ(resp->ret_code(), 0);
  ASSERT_NE(result.m_msg, nullptr);
}

TEST_F(CompensateFirstContractTest, test_position_status_by_make_deals) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  // deposit
  Deposit(uid1, ECoin::USDT, "10000000");
  Deposit(uid2, ECoin::USDT, "10000000");

  // set lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  // adjust leverage: expect first_open_pz_time no change and is_already_close_pz no changed
  FutureOneOfSetLeverageBuilder set_lv_build(ESymbol::BTCUSDT, ECoin::USDT, user1.m_uid, 20 * 1e2, 20 * 1e2);
  auto resp = user1.process(set_lv_build.Build());
  ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg, nullptr);
  auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_check.m_msg, nullptr);
  EXPECT_EQ(pz_check.m_msg->term(), 0);
  EXPECT_EQ(pz_check.m_msg->first_open_pz_time(), 0);
  EXPECT_EQ(pz_check.m_msg->already_close_pz(), false);

  biz::size_x_t qty = 100 * 1e4;
  biz::price_x_t px = 20000 * 1e4;
  biz::coin_t coin = ECoin::USDT;
  biz::symbol_t symbol = ESymbol::BTCUSDT;
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  ETimeInForce time_in_force = ETimeInForce::GoodTillCancel;
  int64_t buy_side_first_open_pz_time = 0, sell_side_first_open_pz_time = 0;

  // open positions: expect first_open_pz_time change and already_close_pz no change
  {
    FutureOneOfCreateOrderBuilder order_build(te->GenUUID(), symbol, pz_index, ESide::Buy, order_type, qty, px,
                                              time_in_force, uid1, coin);
    resp = user1.process(order_build.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);

    FutureOneOfCreateOrderBuilder order_build2(te->GenUUID(), symbol, pz_index, ESide::Sell, order_type, qty, px,
                                               time_in_force, uid2, coin);
    resp = user2.process(order_build2.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    // verify sell side position status, first_open_pz_time change, already_close_pz no change
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->term(), 0);
    sell_side_first_open_pz_time = pz_check.m_msg->first_open_pz_time();
    EXPECT_NE(pz_check.m_msg->first_open_pz_time(), 0);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), false);

    // verify buy side position status, first_open_pz_time change, already_close_pz no change
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->term(), 0);
    buy_side_first_open_pz_time = pz_check.m_msg->first_open_pz_time();
    EXPECT_NE(pz_check.m_msg->first_open_pz_time(), 0);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), false);
  }

  // open positions: expect first_open_pz_time no change and already_close_pz no change
  {
    FutureOneOfCreateOrderBuilder order_build(te->GenUUID(), symbol, pz_index, ESide::Buy, order_type, qty, px,
                                              time_in_force, uid1, coin);
    resp = user1.process(order_build.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);

    FutureOneOfCreateOrderBuilder order_build2(te->GenUUID(), symbol, pz_index, ESide::Sell, order_type, qty, px,
                                               time_in_force, uid2, coin);
    resp = user2.process(order_build2.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    // verify sell side position status, first_open_pz_time change, already_close_pz no change
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->term(), 0);
    EXPECT_EQ(pz_check.m_msg->first_open_pz_time(), sell_side_first_open_pz_time);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), false);

    // verify buy side position status, first_open_pz_time change, already_close_pz no change
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->term(), 0);
    EXPECT_EQ(pz_check.m_msg->first_open_pz_time(), buy_side_first_open_pz_time);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), false);
  }

  // close positions: expect first_open_pz_time no change and already_close_pz change
  {
    FutureOneOfCreateOrderBuilder order_build(te->GenUUID(), symbol, pz_index, ESide::Sell, order_type, qty * 2, px,
                                              time_in_force, uid1, coin);
    resp = user1.process(order_build.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);

    FutureOneOfCreateOrderBuilder order_build2(te->GenUUID(), symbol, pz_index, ESide::Buy, order_type, qty * 2, px,
                                               time_in_force, uid2, coin);
    resp = user2.process(order_build2.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    // verify sell side position status, first_open_pz_time change, already_close_pz no change
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_NE(pz_check.m_msg->term(), 0);
    EXPECT_EQ(pz_check.m_msg->first_open_pz_time(), sell_side_first_open_pz_time);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), true);

    // verify buy side position status, first_open_pz_time change, already_close_pz no change
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_NE(pz_check.m_msg->term(), 0);
    buy_side_first_open_pz_time = pz_check.m_msg->first_open_pz_time();
    EXPECT_EQ(pz_check.m_msg->first_open_pz_time(), buy_side_first_open_pz_time);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), true);
  }
}

TEST_F(CompensateFirstContractTest, test_position_status_by_switch_from_single_side_to_both_side) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  // deposit
  Deposit(uid1, ECoin::USDT, "10000000");
  Deposit(uid2, ECoin::USDT, "10000000");

  biz::size_x_t qty = 100 * 1e4;
  biz::price_x_t px = 20000 * 1e4;
  biz::coin_t coin = ECoin::USDT;
  biz::symbol_t symbol = ESymbol::BTCUSDT;
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  ETimeInForce time_in_force = ETimeInForce::GoodTillCancel;
  int64_t buy_side_first_open_pz_time = 0, sell_side_first_open_pz_time = 0;

  // open positions
  {
    FutureOneOfCreateOrderBuilder order_build(te->GenUUID(), symbol, pz_index, ESide::Buy, order_type, qty, px,
                                              time_in_force, uid1, coin);
    auto resp = user1.process(order_build.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);

    FutureOneOfCreateOrderBuilder order_build2(te->GenUUID(), symbol, pz_index, ESide::Sell, order_type, qty, px,
                                               time_in_force, uid2, coin);
    resp = user2.process(order_build2.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    // verify sell side position status, first_open_pz_time change, already_close_pz no change
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->term(), 0);
    sell_side_first_open_pz_time = pz_check.m_msg->first_open_pz_time();
    EXPECT_NE(pz_check.m_msg->first_open_pz_time(), 0);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), false);

    // verify buy side position status, first_open_pz_time change, already_close_pz no change
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->term(), 0);
    buy_side_first_open_pz_time = pz_check.m_msg->first_open_pz_time();
    EXPECT_NE(pz_check.m_msg->first_open_pz_time(), 0);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), false);
  }

  // close positions
  {
    FutureOneOfCreateOrderBuilder order_build(te->GenUUID(), symbol, pz_index, ESide::Sell, order_type, qty, px,
                                              time_in_force, uid1, coin);
    auto resp = user1.process(order_build.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);

    FutureOneOfCreateOrderBuilder order_build2(te->GenUUID(), symbol, pz_index, ESide::Buy, order_type, qty, px,
                                               time_in_force, uid2, coin);
    resp = user2.process(order_build2.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    // verify buy side position status, first_open_pz_time no change, already_close_pz change
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_NE(pz_check.m_msg->term(), 0);
    EXPECT_EQ(pz_check.m_msg->first_open_pz_time(), sell_side_first_open_pz_time);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), true);

    // verify sell side position status, first_open_pz_time no change, already_close_pz change
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_NE(pz_check.m_msg->term(), 0);
    EXPECT_EQ(pz_check.m_msg->first_open_pz_time(), buy_side_first_open_pz_time);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), true);
  }

  // switch to hedge mode
  {
    SwitchPositionMode switch_position_mode(uid1, ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::BothSide);
    auto resp = user1.process(switch_position_mode.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), true);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(1);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), true);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(2);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), true);

    SwitchPositionMode switch_position_mode2(uid2, ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::BothSide);
    resp = user2.process(switch_position_mode2.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), true);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(1);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), true);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(2);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), true);
  }
}

TEST_F(CompensateFirstContractTest, test_position_status_by_switch_from_both_side_to_single_side) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  // deposit
  Deposit(uid1, ECoin::USDT, "10000000");
  Deposit(uid2, ECoin::USDT, "10000000");

  // switch to both side
  {
    SwitchPositionMode switch_position_mode(uid1, ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::BothSide);
    auto resp = user1.process(switch_position_mode.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), false);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(1);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), false);

    SwitchPositionMode switch_position_mode2(uid2, ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::BothSide);
    resp = user2.process(switch_position_mode2.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), false);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(1);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), false);
  }

  biz::size_x_t qty = 100 * 1e4;
  biz::price_x_t px = 20000 * 1e4;
  biz::coin_t coin = ECoin::USDT;
  biz::symbol_t symbol = ESymbol::BTCUSDT;
  EPositionIndex buy_pz_index = EPositionIndex::Buy, sell_pz_index = EPositionIndex::Sell;
  EOrderType order_type = EOrderType::Limit;
  ETimeInForce time_in_force = ETimeInForce::GoodTillCancel;
  int64_t buy_side_first_open_pz_time = 0, sell_side_first_open_pz_time = 0;

  // open positions
  {
    FutureOneOfCreateOrderBuilder order_build(te->GenUUID(), symbol, buy_pz_index, ESide::Buy, order_type, qty, px,
                                              time_in_force, uid1, coin);
    auto resp = user1.process(order_build.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);

    FutureOneOfCreateOrderBuilder order_build2(te->GenUUID(), symbol, sell_pz_index, ESide::Sell, order_type, qty, px,
                                               time_in_force, uid2, coin);
    resp = user2.process(order_build2.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    // verify sell side position status, first_open_pz_time change, already_close_pz no change
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->term(), 0);
    sell_side_first_open_pz_time = pz_check.m_msg->first_open_pz_time();
    EXPECT_NE(pz_check.m_msg->first_open_pz_time(), 0);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), false);

    // verify buy side position status, first_open_pz_time change, already_close_pz no change
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->term(), 0);
    buy_side_first_open_pz_time = pz_check.m_msg->first_open_pz_time();
    EXPECT_NE(pz_check.m_msg->first_open_pz_time(), 0);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), false);
  }

  // close positions
  {
    FutureOneOfCreateOrderBuilder order_build(te->GenUUID(), symbol, buy_pz_index, ESide::Sell, order_type, qty, px,
                                              time_in_force, uid1, coin);
    auto resp = user1.process(order_build.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);

    FutureOneOfCreateOrderBuilder order_build2(te->GenUUID(), symbol, sell_pz_index, ESide::Buy, order_type, qty, px,
                                               time_in_force, uid2, coin);
    resp = user2.process(order_build2.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    // verify buy side position status, first_open_pz_time no change, already_close_pz change
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_NE(pz_check.m_msg->term(), 0);
    EXPECT_EQ(pz_check.m_msg->first_open_pz_time(), sell_side_first_open_pz_time);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), true);

    // verify sell side position status, first_open_pz_time no change, already_close_pz change
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_NE(pz_check.m_msg->term(), 0);
    EXPECT_EQ(pz_check.m_msg->first_open_pz_time(), buy_side_first_open_pz_time);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), true);
  }

  // switch to single side
  {
    SwitchPositionMode switch_position_mode(uid1, ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::MergedSingle);
    auto resp = user1.process(switch_position_mode.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    // ref single side position
    auto pz_check = result.RefFutureMarginResult().RefRelatedPosition(2);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), true);

    SwitchPositionMode switch_position_mode2(uid2, ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::MergedSingle);
    resp = user2.process(switch_position_mode2.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    // ref single side position
    pz_check = result.RefFutureMarginResult().RefRelatedPosition(2);
    ASSERT_NE(pz_check.m_msg, nullptr);
    EXPECT_EQ(pz_check.m_msg->mode(), EPositionMode::MergedSingle);
    EXPECT_EQ(pz_check.m_msg->already_close_pz(), true);
  }
}
