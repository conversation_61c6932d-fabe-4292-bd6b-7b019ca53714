#pragma once

#include <gtest/gtest.h>

#include <iostream>
#include <memory>

#include "biz_worker/service/trade/store/per_worker_store.hpp"
#include "biz_worker/service/trade/store/user_data_manager.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "test/biz/trade/main.hpp"
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class TradingPerformanceTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }

  biz::DraftPkg* GetDraftPkg(const event::BizEvent::Ptr& event, biz::user_id_t user_id) {
    auto* per_worker_store = new store::PerWorkerStore();
    auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
    auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
    auto symbol_config = std::make_shared<biz::SymbolConfig>();
    symbol_config->value_scale_ = 4;
    symbol_config->price_scale_ = 4;
    symbol_config->symbol_ = ESymbol::BTCUSDT;
    symbol_config->coin_ = ECoin::USDT;
    symbol_config->value_scale_ = 4;
    symbol_config->tick_size_x_ = 5;
    symbol_config->lot_size_x_ = 1000;
    symbol_config->price_limit_pnt_e6_ = 10;
    symbol_config->min_price_x_ = 100;
    symbol_config->min_qty_x_ = 100;
    symbol_config->max_price_x_ = 1000000000;
    symbol_config->max_order_book_qty_x_ = 10000'0000;
    symbol_config->max_new_order_qty_x_ = 10000'0000;
    symbol_config->max_new_market_order_qty_x_ = 1000'0000;
    symbol_config->contract_status_ = EContractStatus::Trading;
    symbol_config->contract_type_ = EContractType::LinearPerpetual;
    symbol_config_mgr->AddSymbolConfig(symbol_config);
    new_config_mgr->set_symbol_config_mgr(symbol_config_mgr);
    config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

    per_worker_store->UpdateContractInfo();

    store::UserDataManager* user_data_manager_ = new store::UserDataManager();
    store::PerUserStore* per_user_store = user_data_manager_->GetUserData(user_id);
    // 初始化
    if (per_user_store == nullptr) {
      user_data_manager_->CreateUserData(user_id);
      per_user_store = user_data_manager_->GetUserData(user_id);
    }
    per_user_store->working_coins_[ECoin::USDC] = {};
    auto header = std::make_shared<store::Header>();
    header->uid = user_id;
    auto draft_pkg = std::make_shared<biz::DraftPkg>(event, header, per_worker_store, per_user_store);
    return draft_pkg.get();
  }
  static std::shared_ptr<tmock::CTradeAppMock> te;
};
