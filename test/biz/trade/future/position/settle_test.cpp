#include "test/biz/trade/future/position/settle_test.hpp"

#include "biz_worker/utils/pb_convertor/to_pb.hpp"
#include "lib/msg_builder.hpp"
#include "src/biz_worker/utils/pb_convertor/to_pb.hpp"
#include "src/cross_worker/cross_common/util/x_comm.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "test/biz/trade/lib/stub.hpp"
std::shared_ptr<tmock::CTradeAppMock> SettleTest::te;

void BuildProduceMsg(biz::cross_idx_t cross_idx, biz::symbol_t symbol, biz::coin_t coin, biz::cross::xReqSender& sender,
                     bbase::hdts::Producer::Message& msg) {
  biz::CrossError err;
  /// 组装topic
  msg.topic = fmt::format("request_of_{}", cross_idx);

  /// 组装buf
  msg.value.resize(sizeof(biz::cross::RawXHeader) + sizeof(biz::cross::request::RawXRequest));
  memcpy(msg.value.data(), &sender.req_mgr_.header_obj_->header_, sizeof(biz::cross::RawXHeader));
  sender.req_mgr_.request_obj_->ToBinaryBuf(static_cast<char*>(msg.value.data()) + sizeof(biz::cross::RawXHeader));

  /// 处理透传数据 passthrough
  ::models::passthroughdto::PassThroughDTO passthrough;

  auto contract_info = passthrough.mutable_contract_info();
  contract_info->set_symbol(static_cast<ESymbol>(symbol));
  contract_info->set_coin(static_cast<ECoin>(coin));
  contract_info->set_expect_settle_price_x(20000 * 1e4);
  contract_info->set_settle_time_e9(1577808000);
  contract_info->set_settle_fee_rate_e8(95000);
  contract_info->set_contract_status(EContractStatus::Settling);

  auto value = passthrough.SerializeAsString();
  msg.headers[biz::XComm::x_pass_through] = value;
}

/**
 * 发送交割信息
 */
void SendSettleMessage() {
  bbase::hdts::Producer::Message msg;

  biz::cross::xReqSender sender;
  sender.req_mgr_.x_pass_through_ = ::models::passthroughdto::PassThroughDTO();
  auto contract_info = std::make_shared<store::Contract>();
  contract_info->set_contract_status(static_cast<EContractStatus>(EContractStatus::Settling));
  convertor::ToPB::Convert(contract_info.get(), sender.req_mgr_.x_pass_through_.value().mutable_contract_info());

  auto err = sender.BuildHeader(biz::cross::request::kMTContract, ECreateType::UNKNOWN);
  if (err.HasErr()) {
    return;
  }

  /// 组装req写入值确保下游不会panic
  sender.req_mgr_.request_obj_->req_.msg_type_ = biz::cross::request::kMTContract;
  sender.req_mgr_.request_obj_->req_.symbol_ = 5;
  sender.req_mgr_.request_obj_->req_.side_ = biz::cross::request::kSideBuy;
  sender.req_mgr_.request_obj_->req_.time_in_force_ = biz::cross::request::kTIFGoodTillCancel;
  sender.req_mgr_.request_obj_->req_.order_type_ = biz::cross::request::kOTMarketOrder;
  auto sending_time = bbase::utils::Time::GetTimeUs();
  sender.req_mgr_.request_obj_->req_.sending_time_sec_ = u_int64_t(sending_time / 1000000);
  sender.req_mgr_.request_obj_->req_.sending_time_usec_ = u_int64_t(sending_time % 1000000);

  bbase::hdts::Producer::Message produce_msg;
  BuildProduceMsg(5, 5, 5, sender, produce_msg);
  /// #3: 发送数据
  produce_msg.headers[biz::XComm::send_to_cross] = std::to_string(bbase::utils::Time::GetTimeNs());
  produce_msg.need_callback = true;
  produce_msg.callback_data.data = nullptr;
  produce_msg.callback_data.start_timestamp_ns = bbase::utils::Time::GetTimeNs();

  auto bRet = bbase::hdts::Hdts::ProduceMessage(produce_msg);
  EXPECT_EQ(bRet, 1);
}

/**
 * 构造仓位信息，总共4个uid
 * 10000 没有挂单，没有持仓
 * 10001 没有挂单，有持仓
 * 10002 有挂单，有持仓
 * 10003 有挂单，没有持仓
 */
void SettleTest::InitUserPositionInfo() {
  biz::user_id_t uid13 = 10000;
  stub user1(te, uid13);
  biz::user_id_t uid23 = 10001;
  stub user2(te, uid23);
  biz::user_id_t uid33 = 10002;
  stub user3(te, uid33);
  biz::user_id_t uid43 = 10003;
  stub user4(te, uid43);
  biz::user_id_t uid53 = 10004;
  stub user5(te, uid53);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid13, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }
  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid23, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user2.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }
  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid33, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user3.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }
  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid43, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user4.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid53, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user5.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);

  ////////////////////////////////////////////////////////////////////
  // 第一轮
  // uid1:10000 position qty:100000   Sell    order qty:0
  // uid2:10001 position qty:100000   Buy     order qty:0
  //
  auto order_id1 = te->GenUUID();
  {
    // uid1挂单，方向Sell，限价单
    FutureOneOfCreateOrderBuilder create_build(order_id1, symbol, pz_index, ESide::Sell, order_type, 100000, price,
                                               ETimeInForce::GoodTillCancel, uid13, coin);

    auto resp1 = user1.process(create_build.Build());

    // 撮合回包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id1);
    ASSERT_NE(order_check.m_msg, nullptr);
    order_check.Check(uid13, 100000);
    // auto &affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
    EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 1);
  }

  auto order_id2 = te->GenUUID();
  {
    FutureOneOfCreateOrderBuilder create_build(order_id2, symbol, pz_index, ESide::Buy, order_type, 100000, price,
                                               ETimeInForce::GoodTillCancel, uid23, coin);

    auto resp1 = user2.process(create_build.Build());

    // 撮合回包，这个为uid2的撮合回包处理
    {
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id2);
      ASSERT_NE(order_check.m_msg, nullptr);
      EXPECT_EQ(order_check.m_msg->qty_x(), 100000);
      EXPECT_EQ(order_check.m_msg->order_status(), EOrderStatus::Filled);
      EXPECT_EQ(order_check.m_msg->side(), ESide::Buy);
      auto& affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
      EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 1);
      auto& position_1 = affected_positions[0];
      EXPECT_EQ(position_1.size_x(), 100000);
      EXPECT_EQ(position_1.side(), ESide::Buy);
      EXPECT_EQ(position_1.user_id(), uid23);
    }

    // uid1的撮合回包处理
    {
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id1);
      auto order_check_data = order_check.m_msg;
      EXPECT_EQ(order_check_data->side(), ESide::Sell);
      auto& affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
      EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 1);
      auto& position_1 = affected_positions[0];
      EXPECT_EQ(position_1.size_x(), 100000);
      EXPECT_EQ(position_1.side(), ESide::Sell);
      EXPECT_EQ(position_1.user_id(), uid13);
    }
  }

  ////////////////////////////////////////////////////////////////////
  // 第二轮
  // uid1:10000 position qty:0            order qty:0
  // uid2:10001 position qty:100000 Buy   order qty:0
  // uid3:10002 position qty:100000 Sell  order qty:100000 Sell

  auto order_id3 = te->GenUUID();
  {
    // uid1挂单，方向Buy，限价单，执行平仓操作
    FutureOneOfCreateOrderBuilder create_build(order_id3, symbol, pz_index, ESide::Buy, order_type, 100000, price,
                                               ETimeInForce::GoodTillCancel, uid13, coin);

    auto resp1 = user1.process(create_build.Build());

    // 撮合回包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id3);
    ASSERT_NE(order_check.m_msg, nullptr);
    EXPECT_EQ(order_check.m_msg->qty_x(), 100000);
    // auto &affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
    EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 1);
  }
  // uid3挂单，方向sell，限价单，执行部分开仓
  auto order_id4 = te->GenUUID();
  {
    FutureOneOfCreateOrderBuilder create_build(order_id4, symbol, pz_index, ESide::Sell, order_type, 200000, price,
                                               ETimeInForce::GoodTillCancel, uid33, coin);

    auto resp1 = user3.process(create_build.Build());

    // 撮合回包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id4);
    ASSERT_NE(order_check.m_msg, nullptr);
    EXPECT_EQ(order_check.m_msg->qty_x(), 200000);
    auto& affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
    EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 1);
    auto& position_1 = affected_positions[0];
    EXPECT_EQ(position_1.user_id(), uid33);
    EXPECT_EQ(position_1.side(), ESide::Sell);
    EXPECT_EQ(position_1.size_x(), 100000);
  }

  // uid1的撮合回包处理
  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id3);
    auto order_check_data = order_check.m_msg;
    EXPECT_EQ(order_check_data->side(), ESide::Buy);
    EXPECT_EQ(order_check_data->leaves_qty_x(), 0);
    EXPECT_EQ(order_check_data->qty_x(), 100000);
    auto& affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
    EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 1);
    auto& position_1 = affected_positions[0];
    EXPECT_EQ(position_1.size_x(), 0);
    EXPECT_EQ(position_1.side(), ESide::None);
    EXPECT_EQ(position_1.user_id(), uid13);
  }

  ////////////////////////////////////////////////////////////////////
  // 第三轮
  // uid1:10000 position qty:0            order qty:0
  // uid2:10001 position qty:100000 Buy   order qty:0
  // uid3:10002 position qty:100000 Sell  order qty:100000 Sell
  // uid4:10003 position qty:0            order qty:100000 Sell   21000
  //                                      order qty:100000 Sell   22000
  //                                      order qty:100000 Sell   23000

  // uid4 挂卖单
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, ESide::Sell, order_type, 200000, 21000 * 1e4,
                                               ETimeInForce::GoodTillCancel, uid43, coin);

    auto resp1 = user4.process(create_build.Build());

    // 撮合回包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    EXPECT_EQ(order_check.m_msg->qty_x(), 200000);
    EXPECT_EQ(order_check.m_msg->leaves_qty_x(), 200000);
    EXPECT_EQ(order_check.m_msg->side(), ESide::Sell);
    auto& affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
    EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 1);
    auto& position_1 = affected_positions[0];
    EXPECT_EQ(position_1.size_x(), 0);
  }
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, ESide::Sell, order_type, 200000, 22000 * 1e4,
                                               ETimeInForce::GoodTillCancel, uid43, coin);

    auto resp1 = user4.process(create_build.Build());

    // 撮合回包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    EXPECT_EQ(order_check.m_msg->qty_x(), 200000);
    EXPECT_EQ(order_check.m_msg->leaves_qty_x(), 200000);
    EXPECT_EQ(order_check.m_msg->side(), ESide::Sell);
    auto& affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
    EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 1);
    auto& position_1 = affected_positions[0];
    EXPECT_EQ(position_1.size_x(), 0);
  }
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, ESide::Sell, order_type, 200000, 23000 * 1e4,
                                               ETimeInForce::GoodTillCancel, uid43, coin);

    auto resp1 = user4.process(create_build.Build());

    // 撮合回包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    EXPECT_EQ(order_check.m_msg->qty_x(), 200000);
    EXPECT_EQ(order_check.m_msg->leaves_qty_x(), 200000);
    EXPECT_EQ(order_check.m_msg->side(), ESide::Sell);
    auto& affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
    EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 1);
    auto& position_1 = affected_positions[0];
    EXPECT_EQ(position_1.size_x(), 0);
  }

  ////////////////////////////////////////////////////////////////////
  // 第三轮
  // uid1:10000 position qty:0            order qty:0
  // uid2:10001 position qty:100000 Buy   order qty:0
  // uid3:10002 position qty:100000 Sell  order qty:100000 Sell
  // uid4:10003 position qty:0            order qty:100000 Sell   21000
  //                                      order qty:100000 Sell   22000
  //                                      order qty:100000 Sell   23000
  // uid5:10004  buy-position qty:0       order qty:100000
  //            sell-position qty:0       order qty:100000

  {
    FutureOneOfSwitchPositionModeBuilder switch_position_mode(ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::BothSide,
                                                              uid53);
    auto resp = user5.process(switch_position_mode.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto pz_checker_0 = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker_0.m_msg, nullptr);
    EXPECT_EQ(EPositionMode::MergedSingle, static_cast<EPositionMode>(pz_checker_0.m_msg->mode()));
    EXPECT_EQ(ESide::None, pz_checker_0.m_msg->side());
    EXPECT_EQ(EPositionStatus::Inactive, pz_checker_0.m_msg->position_status());
    auto pz_checker_1 = result.RefFutureMarginResult().RefRelatedPosition(1);
    ASSERT_NE(pz_checker_1.m_msg, nullptr);
    EXPECT_EQ(EPositionMode::BothSide, static_cast<EPositionMode>(pz_checker_1.m_msg->mode()));
    EXPECT_EQ(ESide::None, pz_checker_1.m_msg->side());
    EXPECT_EQ(EPositionStatus::Normal, pz_checker_1.m_msg->position_status());
    auto pz_checker_2 = result.RefFutureMarginResult().RefRelatedPosition(2);
    ASSERT_NE(pz_checker_2.m_msg, nullptr);
    EXPECT_EQ(EPositionMode::BothSide, static_cast<EPositionMode>(pz_checker_2.m_msg->mode()));
    EXPECT_EQ(ESide::None, pz_checker_2.m_msg->side());
    EXPECT_EQ(EPositionStatus::Normal, pz_checker_2.m_msg->position_status());
  }
  // uid5 挂卖单
  {
    auto order_id = te->GenUUID();
    // // 创建期货订单
    // OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 1, "Buy", "Limit", "1", "30000");
    // build.SetOrderLinkID(te->GenUUID());
    // auto resp1 = user1.create_order(build.Build());
    // ASSERT_NE(resp1->order_id(), "");

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, EPositionIndex::Buy, ESide::Buy, order_type, 200000,
                                               19000 * 1e4, ETimeInForce::GoodTillCancel, uid53, coin);

    auto resp1 = user5.process(create_build.Build());

    // 撮合回包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    EXPECT_EQ(order_check.m_msg->qty_x(), 200000);
    EXPECT_EQ(order_check.m_msg->leaves_qty_x(), 200000);
    EXPECT_EQ(order_check.m_msg->side(), ESide::Buy);
    auto& affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
    EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 2);
    auto& position_1 = affected_positions[0];
    EXPECT_EQ(position_1.size_x(), 0);
  }

  // uid5 挂卖单
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, EPositionIndex::Sell, ESide::Sell, order_type, 200000,
                                               22000 * 1e4, ETimeInForce::GoodTillCancel, uid53, coin);

    auto resp1 = user5.process(create_build.Build());

    // 撮合回包
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    EXPECT_EQ(order_check.m_msg->qty_x(), 200000);
    EXPECT_EQ(order_check.m_msg->leaves_qty_x(), 200000);
    EXPECT_EQ(order_check.m_msg->side(), ESide::Sell);
    auto& affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
    EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 2);
    auto& position_1 = affected_positions[0];
    EXPECT_EQ(position_1.size_x(), 0);
  }
}

void SettleTest::CheckResult() {
  auto uid15 = 10000;
  auto uid25 = 10001;
  auto uid35 = 10002;
  auto uid45 = 10003;
  auto uid55 = 10004;
  int exec_count = 0;
  for (int i = 0; i < 5; ++i) {
    auto result = te->PopResult(100);
    if (result.m_msg == nullptr) {
      break;
    }

    // ASSERT_NE(result.m_msg.get(), nullptr);
    // uid1:10000 position qty:0            order qty:0
    // uid2:10002 position qty:100000 Buy   order qty:0
    // uid2:10003 position qty:100000 Sell  order qty:100000 Sell
    // uid3:10004 position qty:0            order qty:100000 Sell   21000
    //                                      order qty:100000 Sell   22000
    //                                      order qty:100000 Sell   23000
    // std::cout << "check uid:" << result.m_msg->header().user_id() << std::endl;
    if (result.m_msg->header().user_id() == uid15) {
      ++exec_count;  // 精准广播会跳过该用户 理论上不应该走到这里
      EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 0);
      EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->related_orders_size(), 0);
    } else if (result.m_msg->header().user_id() == uid25) {
      ++exec_count;
      EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 1);
      EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->related_orders_size(), 0);
      auto& affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
      EXPECT_EQ(affected_positions[0].size_x(), 0);
      EXPECT_EQ(affected_positions[0].settle_price_x(), 200000000);
      EXPECT_EQ(affected_positions[0].settle_fee_rate_e8(), 95000);
    } else if (result.m_msg->header().user_id() == uid35) {
      ++exec_count;
      EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 1);
      EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->related_orders_size(), 1);
      auto& affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
      EXPECT_EQ(affected_positions[0].size_x(), 0);
      EXPECT_EQ(affected_positions[0].settle_price_x(), 200000000);
      EXPECT_EQ(affected_positions[0].settle_fee_rate_e8(), 95000);

      auto& related_orders = result.m_msg->mutable_futures_margin_result()->related_orders();
      for (auto& orders : related_orders) {
        EXPECT_EQ(orders.order_status(), EOrderStatus::Cancelled);
        EXPECT_EQ(orders.cross_status(), ECrossStatus::Canceled);
        EXPECT_EQ(orders.cancel_type(), ECancelType::CancelBySettle);
      }
    } else if (result.m_msg->header().user_id() == uid45) {
      ++exec_count;
      EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 1);
      EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->related_orders_size(), 3);
      auto& affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
      auto position = affected_positions[0];
      EXPECT_EQ(position.size_x(), 0);
      EXPECT_EQ(position.order_cost_e8(), 0);
      EXPECT_EQ(position.order_mm_with_fee_e8(), 0);
      EXPECT_EQ(position.min_position_cost_e8(), 0);
      EXPECT_EQ(position.position_mm_with_fee_e8(), 0);
      EXPECT_EQ(position.unrealised_pnl_e8(), 0);
      EXPECT_EQ(position.free_cost_e8(), 0);
      EXPECT_EQ(position.order_cost_e8(), 0);
      EXPECT_EQ(position.order_mm_with_fee_e8(), 0);
      EXPECT_EQ(position.sell_leaves_qty_x(), 0);
      EXPECT_EQ(position.sell_leaves_value_e8(), 0);
      EXPECT_EQ(position.buy_leaves_qty_x(), 0);
      EXPECT_EQ(position.buy_leaves_value_e8(), 0);
      EXPECT_EQ(position.unrealised_order_loss_e8(), 0);
      EXPECT_EQ(position.settle_price_x(), 0);  // 无持仓 交割代码不会更新交割价格到仓位
      EXPECT_EQ(position.settle_fee_rate_e8(), 0);

      auto& related_orders = result.m_msg->mutable_futures_margin_result()->related_orders();
      for (auto& orders : related_orders) {
        EXPECT_EQ(orders.order_status(), EOrderStatus::Cancelled);
        EXPECT_EQ(orders.cross_status(), ECrossStatus::Canceled);
        EXPECT_EQ(orders.cancel_type(), ECancelType::CancelBySettle);
      }
    } else if (result.m_msg->header().user_id() == uid55) {
      ++exec_count;
      EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->affected_positions_size(), 2);
      EXPECT_EQ(result.m_msg->mutable_futures_margin_result()->related_orders_size(), 2);
      auto& affected_positions = result.m_msg->mutable_futures_margin_result()->affected_positions();
      {
        auto position = affected_positions[0];
        EXPECT_EQ(position.size_x(), 0);
        EXPECT_EQ(position.order_cost_e8(), 0);
        EXPECT_EQ(position.order_mm_with_fee_e8(), 0);
        EXPECT_EQ(position.min_position_cost_e8(), 0);
        EXPECT_EQ(position.position_mm_with_fee_e8(), 0);
        EXPECT_EQ(position.unrealised_pnl_e8(), 0);
        EXPECT_EQ(position.free_cost_e8(), 0);
        EXPECT_EQ(position.order_cost_e8(), 0);
        EXPECT_EQ(position.order_mm_with_fee_e8(), 0);
        EXPECT_EQ(position.sell_leaves_qty_x(), 0);
        EXPECT_EQ(position.sell_leaves_value_e8(), 0);
        EXPECT_EQ(position.buy_leaves_qty_x(), 0);
        EXPECT_EQ(position.buy_leaves_value_e8(), 0);
        EXPECT_EQ(position.unrealised_order_loss_e8(), 0);
        EXPECT_EQ(position.settle_price_x(), 0);  // 无持仓 交割代码不会更新交割价格到仓位
        EXPECT_EQ(position.settle_fee_rate_e8(), 0);
      }
      {
        auto position = affected_positions[1];
        EXPECT_EQ(position.size_x(), 0);
        EXPECT_EQ(position.order_cost_e8(), 0);
        EXPECT_EQ(position.order_mm_with_fee_e8(), 0);
        EXPECT_EQ(position.min_position_cost_e8(), 0);
        EXPECT_EQ(position.position_mm_with_fee_e8(), 0);
        EXPECT_EQ(position.unrealised_pnl_e8(), 0);
        EXPECT_EQ(position.free_cost_e8(), 0);
        EXPECT_EQ(position.order_cost_e8(), 0);
        EXPECT_EQ(position.order_mm_with_fee_e8(), 0);
        EXPECT_EQ(position.sell_leaves_qty_x(), 0);
        EXPECT_EQ(position.sell_leaves_value_e8(), 0);
        EXPECT_EQ(position.buy_leaves_qty_x(), 0);
        EXPECT_EQ(position.buy_leaves_value_e8(), 0);
        EXPECT_EQ(position.unrealised_order_loss_e8(), 0);
        EXPECT_EQ(position.settle_price_x(), 0);  // 无持仓 交割代码不会更新交割价格到仓位
        EXPECT_EQ(position.settle_fee_rate_e8(), 0);
      }
      auto& related_orders = result.m_msg->mutable_futures_margin_result()->related_orders();
      for (auto& orders : related_orders) {
        EXPECT_EQ(orders.order_status(), EOrderStatus::Cancelled);
        EXPECT_EQ(orders.cross_status(), ECrossStatus::Canceled);
        EXPECT_EQ(orders.cancel_type(), ECancelType::CancelBySettle);
      }
    } else {
      ++exec_count;
    }
  }
  EXPECT_EQ(exec_count, 4);
}

void SettleTest::CheckCannotTrade() {
  biz::user_id_t uid17 = 10000;
  stub user1(te, uid17);

  auto order_id1 = te->GenUUID();
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;

  // uid1挂单，被拒绝
  FutureOneOfCreateOrderBuilder create_build(order_id1, symbol, pz_index, ESide::Sell, order_type, 100000, price,
                                             ETimeInForce::GoodTillCancel, uid17, coin);
  auto resp1 = user1.process(create_build.Build());
  ASSERT_EQ(resp1->ret_code(), error::kErrorCodeSymbolNotInTradingStatus);
}
TEST_F(SettleTest, settle_test) {  // 初始化数据
  // GTEST_SKIP();
  InitUserPositionInfo();
  // 发送交割请求
  SendSettleMessage();
  // 检查结果
  CheckResult();
  // 无法下单
  CheckCannotTrade();
}
