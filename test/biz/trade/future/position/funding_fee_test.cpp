//
// Created by SH00678ML on 2023/3/28.
//

#include "test/biz/trade/future/position/funding_fee_test.hpp"

#include <memory>
#include <string>

#include "biz_worker/service/trade/futures/modules/commonbiz/lazy_init_biz.hpp"
#include "cross_worker/cross_receiver/xresp/x_future_resp_pkg.hpp"
#include "data/type/biz_type.hpp"
#include "seq_mark_worker/utils.hpp"
#include "src/biz_worker/engine/pre_worker/pre_engine.hpp"
#include "src/biz_worker/service/trade/futures/event_handler/funding_biz.hpp"
#include "src/biz_worker/service/trade/store/per_symbol_store.hpp"
#include "src/biz_worker/service/trade/store/position/raw_position.hpp"
#include "src/config/const.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"

TEST_F(funding_fee_test, fundingRateExceedLimit) {
  biz::user_id_t account_id(102);
  biz::symbol_t symbol(ESymbol::BTCUSDT);
  biz::coin_t coin(ECoin::USDT);
  biz::cross_idx_t cross_idx(ECrossIdx::BTCLP);
  biz::seq_t cross_seq(1000);

  // 设置资金费率限制为 2%
  int64_t max_funding_rate_clamp = 2000000;
  auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  nacos_client->SetStringVar(config::ConstConfig::FUNDING_CONFIG_DATA_ID, config::ConstConfig::FUNDING_GROUP,
                             "max_funding_rate_clamp", std::to_string(max_funding_rate_clamp));

  // 创建资金费率事件
  auto settleFundingEvent = std::make_shared<event::CrossPassThroughEvent>(
      user_id, event::EventType::kEventCrossInput, event::EventSourceType::kCross,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kSettleFundingFee);
  settleFundingEvent->set_cross_seq(cross_seq);
  settleFundingEvent->set_cross_idx(cross_idx);
  settleFundingEvent->max_funding_fee_rate_clamp_e8 = nacos_client->GetInt64Var(
      config::ConstConfig::FUNDING_CONFIG_DATA_ID, config::ConstConfig::FUNDING_GROUP, "max_funding_rate_clamp", 0);
  settleFundingEvent->pass_through = std::make_shared<models::passthroughdto::PassThroughDTO>();

  auto funding_fee = settleFundingEvent->pass_through->mutable_funding_fee();
  funding_fee->set_exec_time_e9(bbase::utils::Time::GetTimeNs());
  funding_fee->set_symbol(static_cast<ESymbol>(symbol));
  funding_fee->set_price_scale(4);
  funding_fee->set_exec_price_x(10000 * 1e4);
  funding_fee->set_exec_id("5536d899-75df-474e-88d8-597b5454674b");
  funding_fee->set_fee_rate_e8(3000000);  // 设置资金费率为 3%，超过 2% 的限制

  settleFundingEvent->header().action = enums::eaction::settle_funding_fee;
  settleFundingEvent->header().uid = user_id;
  settleFundingEvent->header().account_id = account_id;
  settleFundingEvent->header().coin = coin;
  settleFundingEvent->header().symbol = symbol;

  auto pkg = ResetDraftPkg();
  pkg->req_ev = settleFundingEvent;

  // 创建持仓
  auto pz_p = std::make_shared<store::Position>();
  pz_p->coin = coin;
  pz_p->symbol = symbol;
  pz_p->size = 1;
  pz_p->side = ESide::Buy;
  pz_p->user_id = user_id;
  pz_p->entry_price = 10000 * 1e4;
  pz_p->user_id = user_id;
  auto cow_position = std::make_shared<store::CowPosition>(pz_p, EProductType::Futures);

  auto per_coin_store = std::make_shared<store::PerCoinStore>(user_id, coin);
  auto per_symbol_store = std::make_shared<store::PerSymbolStore>(symbol, EPositionMode::MergedSingle);
  per_symbol_store->all_positions_[EPositionIndex::Single] = cow_position;
  per_symbol_store->mode_ = EPositionMode::MergedSingle;
  per_coin_store->working_future_symbols_[symbol] = per_symbol_store;
  pkg->u_store->working_coins_[coin] = per_coin_store;

  // 执行资金费率结算
  biz::FundingBiz::SettleFundingFee(pkg);

  // 验证结果
  ASSERT_EQ(pkg->related_funding_records_.size(), 1);
  ASSERT_EQ(pkg->related_funding_records_[0]->fee_rate_e8, max_funding_rate_clamp);  // 验证资金费率被限制在最大值
}

std::shared_ptr<tmock::CTradeAppMock> funding_fee_test::te;
