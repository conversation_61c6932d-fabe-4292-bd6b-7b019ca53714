#include "test/biz/trade/future/feature/floating_order_price/floating_order_price_by_create_order/floating_order_price_by_create_order.hpp"

#include <algorithm>
#include <bbase/common/decimal/decimal.hpp>
#include <memory>
#include <string>
#include <unordered_map>

#include "enums/ebizcode/biz_code.pb.h"
#include "src/biz_worker/engine/pre_worker/pre_engine.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/store/per_symbol_store.hpp"
#include "src/biz_worker/service/trade/store/position/cow_position.hpp"
#include "src/biz_worker/service/trade/store/position/raw_position.hpp"
#include "src/biz_worker/utils/calc_util.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/lib/stub.hpp"

int32_t FloatingOrderPricingByCreateOrderTest::Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t FloatingOrderPricingByCreateOrderTest::SwitchMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户挂市价单
预期结果：用户挂单成功，订单价格为最优价，但是由于盘口没吃到被取消
*/
TEST_F(FloatingOrderPricingByCreateOrderTest, floating_order_open_position_in_cross_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->UpdateQuoteData(symbol, 5, "9000", "5000000", false);

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  // 用户1挂买单
  // mmrate = 0.005
  // bv2c = ********
  // bv2mm = 614000
  // lv = 10
  // ticksize = 1000
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(94500000, order_checker.m_msg->price_x());  // 订单价格为most price
  EXPECT_EQ(********, order_checker.m_msg->qty_x());
  EXPECT_EQ(EOrderStatus::Cancelled, order_checker.m_msg->order_status());
  EXPECT_EQ(ECrossStatus::Canceled, order_checker.m_msg->cross_status());

  // 用户1挂卖单
  // mmrate = 0.005
  // sv2c = 10126000
  // sv2mm = 626000
  // lv = 10
  order_id = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(85500000, order_checker.m_msg->price_x());  // 订单价格为most price
  EXPECT_EQ(********, order_checker.m_msg->qty_x());
  EXPECT_EQ(EOrderStatus::Cancelled, order_checker.m_msg->order_status());
  EXPECT_EQ(ECrossStatus::Canceled, order_checker.m_msg->cross_status());

  // 使用slippage下单
  {
    auto order_id2 = te->GenUUID();
    side = ESide::Sell;
    price = 0;
    qty = 0.2 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user1_sell2(order_id2, symbol, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
    create_build_user1_sell2.msg.mutable_create_order()->set_max_slippage_type(ESlippageType::TickSize);
    create_build_user1_sell2.msg.mutable_create_order()->set_max_slippage_e2(1000);
    auto resp2 = user1.process(create_build_user1_sell2.Build());
    ASSERT_EQ(resp2->ret_code(), error::kErrorCodeSuccess);
    auto result2 = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id2);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 8999 * 1e4);
    EXPECT_EQ(orderCheck.m_msg->max_slippage_e2(), 1000);
    EXPECT_EQ(orderCheck.m_msg->max_slippage_type(), ESlippageType::TickSize);
  }
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户挂市价单开仓，平仓
预期结果：用户平仓成功
*/
TEST_F(FloatingOrderPricingByCreateOrderTest, floating_order_close_position_in_cross_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  EXPECT_EQ(0, Deposit(uid_2, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Cross));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->UpdateQuoteData(symbol, 5, "9000", "5000000", false);

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  ESide side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  auto resp = user2.process(create_build_user2_sell.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());

  // 用户1挂买单
  auto order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);
  EXPECT_EQ(94500000, order_checker.m_msg->price_x());  // 订单价格为most price
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(********, pz_checker.m_msg->size_x());
  result = te->PopResult();
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户2挂买单
  order_id_2 = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Limit;
  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(85500000, order_checker.m_msg->price_x());
  EXPECT_EQ(********, order_checker.m_msg->qty_x());
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(0, pz_checker.m_msg->size_x());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户挂市价单开仓，平仓 滑点
预期结果：用户平仓成功
*/
TEST_F(FloatingOrderPricingByCreateOrderTest, floating_slippage_order_close_position_in_cross_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  EXPECT_EQ(0, Deposit(uid_2, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Cross));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->UpdateQuoteData(symbol, 5, "9000", "5000000", false);

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 100000;
  sc->price_limit_pnt_y_e6_ = 200000;

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  ESide side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  auto resp = user2.process(create_build_user2_sell.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());

  // 用户1挂买单
  auto order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);

  resp = user1.process(create_build_user1_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);
  EXPECT_EQ(99000000, order_checker.m_msg->price_x());  // 订单价格为most price
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(********, pz_checker.m_msg->size_x());
  result = te->PopResult();
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户2挂买单
  order_id_2 = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Limit;
  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());

  // 使用slippage下单 用户1挂卖单
  {
    order_id = te->GenUUID();
    side = ESide::Sell;
    order_type = EOrderType::Market;
    FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_1, coin);
    create_build_user1_sell.msg.mutable_create_order()->set_max_slippage_type(ESlippageType::TickSize);
    create_build_user1_sell.msg.mutable_create_order()->set_max_slippage_e2(1000);
    resp = user1.process(create_build_user1_sell.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
    order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(100000, order_checker.m_msg->user_id());
    EXPECT_EQ(8999 * 1e4, order_checker.m_msg->price_x());
    EXPECT_EQ(********, order_checker.m_msg->qty_x());
    EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(100000, pz_checker.m_msg->user_id());
    EXPECT_EQ(0, pz_checker.m_msg->size_x());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户挂市价单反向开仓
预期结果：用户反向开仓失败，成本不足
*/
TEST_F(FloatingOrderPricingByCreateOrderTest, floating_order_open_oppsite_position_fail_in_cross_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "200"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  EXPECT_EQ(0, Deposit(uid_2, "200"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Cross));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->UpdateQuoteData(symbol, 5, "9000", "5000000", false);

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  ESide side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  auto resp = user2.process(create_build_user2_sell.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());

  // 用户1挂买单
  auto order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);
  EXPECT_EQ(94500000, order_checker.m_msg->price_x());  // 订单价格为most price
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(********, pz_checker.m_msg->size_x());
  result = te->PopResult();
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户2挂买单
  order_id_2 = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Limit;
  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  qty = 1 * 1e8;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  EXPECT_EQ(30031, resp->ret_code());

  // 使用slippage下单
  {
    auto order_id2 = te->GenUUID();
    side = ESide::Sell;
    price = 0;
    qty = 3 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user1_sell2(order_id2, symbol, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
    create_build_user1_sell2.msg.mutable_create_order()->set_max_slippage_type(ESlippageType::Percent);
    create_build_user1_sell2.msg.mutable_create_order()->set_max_slippage_e2(10);
    auto resp2 = user1.process(create_build_user1_sell2.Build());
    ASSERT_EQ(resp2->ret_code(), 30031);
  }
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户挂市价单开仓
预期结果：用户开仓失败，成本不足
*/
TEST_F(FloatingOrderPricingByCreateOrderTest, floating_order_open_position_fail_in_cross_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "20"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  EXPECT_EQ(0, Deposit(uid_2, "20"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Cross));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->UpdateQuoteData(symbol, 5, "9000", "5000000", false);

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  // 用户1挂卖单
  auto order_id = te->GenUUID();
  auto side = ESide::Sell;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_sell.Build());
  EXPECT_EQ(30031, resp->ret_code());
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 用户挂市价单
预期结果：用户挂单成功，订单价格为最优价，但是由于盘口没吃到被取消
*/
TEST_F(FloatingOrderPricingByCreateOrderTest, floating_order_open_position_in_isolate_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Isolated));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->UpdateQuoteData(symbol, 5, "9000", "5000000", false);

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(94500000, order_checker.m_msg->price_x());  // 订单价格为most price
  EXPECT_EQ(********, order_checker.m_msg->qty_x());
  EXPECT_EQ(EOrderStatus::Cancelled, order_checker.m_msg->order_status());
  EXPECT_EQ(ECrossStatus::Canceled, order_checker.m_msg->cross_status());

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(85500000, order_checker.m_msg->price_x());  // notliqprice
  EXPECT_EQ(********, order_checker.m_msg->qty_x());
  EXPECT_EQ(EOrderStatus::Cancelled, order_checker.m_msg->order_status());
  EXPECT_EQ(ECrossStatus::Canceled, order_checker.m_msg->cross_status());

  // 使用slippage下单
  {
    auto order_id2 = te->GenUUID();
    side = ESide::Sell;
    price = 0;
    qty = 0.2 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user1_sell2(order_id2, symbol, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
    create_build_user1_sell2.msg.mutable_create_order()->set_max_slippage_type(ESlippageType::TickSize);
    create_build_user1_sell2.msg.mutable_create_order()->set_max_slippage_e2(1000);
    auto resp2 = user1.process(create_build_user1_sell2.Build());
    ASSERT_EQ(resp2->ret_code(), error::kErrorCodeSuccess);
    auto result2 = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id2);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 8999 * 1e4);
    EXPECT_EQ(orderCheck.m_msg->max_slippage_e2(), 1000);
    EXPECT_EQ(orderCheck.m_msg->max_slippage_type(), ESlippageType::TickSize);
  }
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 用户挂市价单开仓，平仓
预期结果：用户平仓成功
*/
TEST_F(FloatingOrderPricingByCreateOrderTest, floating_order_close_position_in_isolate_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Isolated));

  EXPECT_EQ(0, Deposit(uid_2, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Isolated));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->UpdateQuoteData(symbol, 5, "9000", "5000000", false);

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  ESide side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  auto resp = user2.process(create_build_user2_sell.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());

  // 用户1挂买单
  auto order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);
  EXPECT_EQ(94500000, order_checker.m_msg->price_x());  // 订单价格为most price
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(********, pz_checker.m_msg->size_x());
  result = te->PopResult();
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户2挂买单
  order_id_2 = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Limit;
  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(85500000, order_checker.m_msg->price_x());
  EXPECT_EQ(********, order_checker.m_msg->qty_x());
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(0, pz_checker.m_msg->size_x());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 用户挂市价单开仓，平仓 slippage
预期结果：用户平仓成功
*/
TEST_F(FloatingOrderPricingByCreateOrderTest, floating_slippage_order_close_position_in_isolate_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Isolated));

  EXPECT_EQ(0, Deposit(uid_2, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Isolated));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->UpdateQuoteData(symbol, 5, "9000", "5000000", false);

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);
  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 100000;
  sc->price_limit_pnt_y_e6_ = 200000;

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  ESide side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  auto resp = user2.process(create_build_user2_sell.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());

  // 用户1挂买单
  auto order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);
  EXPECT_EQ(99000000, order_checker.m_msg->price_x());  // 订单价格为most price
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(********, pz_checker.m_msg->size_x());
  result = te->PopResult();
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户2挂买单
  order_id_2 = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Limit;
  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());

  // 用户1挂卖单 slippage
  {
    order_id = te->GenUUID();
    side = ESide::Sell;
    order_type = EOrderType::Market;
    FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_1, coin);
    create_build_user1_sell.msg.mutable_create_order()->set_max_slippage_type(ESlippageType::TickSize);
    create_build_user1_sell.msg.mutable_create_order()->set_max_slippage_e2(1000);
    resp = user1.process(create_build_user1_sell.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
    order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(order_checker.m_msg, nullptr);
    EXPECT_EQ(100000, order_checker.m_msg->user_id());
    EXPECT_EQ(89990000, order_checker.m_msg->price_x());
    EXPECT_EQ(********, order_checker.m_msg->qty_x());
    EXPECT_EQ(order_checker.m_msg->max_slippage_e2(), 1000);
    EXPECT_EQ(order_checker.m_msg->max_slippage_type(), ESlippageType::TickSize);
    EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(100000, pz_checker.m_msg->user_id());
    EXPECT_EQ(0, pz_checker.m_msg->size_x());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 用户挂市价单开仓，ab几乎为0，先挂限价平仓单，再挂普通（非COT）市价平仓单
预期结果：用户市价单定价失败
*/
TEST_F(FloatingOrderPricingByCreateOrderTest, limit_order_and_floating_order_close_position_in_isolate_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "200"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Isolated));

  EXPECT_EQ(0, Deposit(uid_2, "200"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Isolated));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->UpdateQuoteData(symbol, 5, "9000", "5000000", false);

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  ESide side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  auto resp = user2.process(create_build_user2_sell.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());

  // 用户1挂买单
  auto order_id = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(********, pz_checker.m_msg->size_x());
  result = te->PopResult();
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_limit(order_id, symbol, pz_index, side, order_type, qty, price,
                                                              ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_limit.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(********, order_checker.m_msg->qty_x());
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户1挂卖单
  order_id = te->GenUUID();
  qty = 0.15 * 1e8;
  side = ESide::Sell;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_market(order_id, symbol, pz_index, side, order_type, qty, price,
                                                               ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_market.Build());
  result = te->PopResult();
  EXPECT_EQ(30031, resp->ret_code());
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 用户挂市价单开仓，ab几乎为0，先挂限价平仓单，再挂COT市价平仓单
预期结果：用户市价单定价成功
*/
TEST_F(FloatingOrderPricingByCreateOrderTest, limit_order_and_cot_floating_order_close_position_in_isolate_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "200"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Isolated));

  EXPECT_EQ(0, Deposit(uid_2, "200"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Isolated));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->UpdateQuoteData(symbol, 5, "9000", "5000000", false);

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  ESide side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  auto resp = user2.process(create_build_user2_sell.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());

  // 用户1挂买单
  auto order_id = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(********, pz_checker.m_msg->size_x());
  result = te->PopResult();
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_limit(order_id, symbol, pz_index, side, order_type, qty, price,
                                                              ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_limit.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(********, order_checker.m_msg->qty_x());
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  qty = 0.15 * 1e8;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_market(order_id, symbol, pz_index, side, order_type, qty, price,
                                                               ETimeInForce::GoodTillCancel, uid_1, coin);
  create_build_user1_sell_market.msg.mutable_create_order()->set_close_on_trigger(true);
  resp = user1.process(create_build_user1_sell_market.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(90000000, order_checker.m_msg->price_x());
  EXPECT_EQ(********, order_checker.m_msg->qty_x());
  EXPECT_EQ(EOrderStatus::Cancelled, order_checker.m_msg->order_status());
  EXPECT_EQ(ECrossStatus::Canceled, order_checker.m_msg->cross_status());
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(85500000, order_checker.m_msg->price_x());
  EXPECT_EQ(15000000, order_checker.m_msg->qty_x());
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(********, pz_checker.m_msg->size_x());
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 用户挂市价买单
预期结果: mostprice为notliqprice，盘口leastprice > mostprice,定价失败
*/
TEST_F(FloatingOrderPricingByCreateOrderTest, floating_order_leastprice_mostprice) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Isolated));

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Isolated));

  EXPECT_EQ(0, Deposit(uid_2, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Isolated));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->UpdateQuoteData(symbol, 5, "9500", "5000000", false);

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodePriceGtMaxBuyPrice);

  te->UpdateQuoteData(symbol, 5, "8500", "5000000", false);
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodePriceLtMinSellPrice);

  te->UpdateQuoteData(symbol, 5, "9000", "5000000", false);
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);
  side = ESide::Buy;
  order_type = EOrderType::Limit;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  side = ESide::Sell;
  order_id = te->GenUUID();
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  EXPECT_EQ(100002, pz_checker.m_msg->user_id());
  EXPECT_EQ(99000000, pz_checker.m_msg->bust_price_x());

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(81000000, pz_checker.m_msg->bust_price_x());

  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 150000;
  sc->price_limit_pnt_y_e6_ = 300000;

  te->UpdateQuoteData(symbol, 5, "8000", "5000000", false);
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 8500, 8500, 8500);
  side = ESide::Sell;
  order_type = EOrderType::Market;
  order_id = te->GenUUID();
  qty = 0.3 * 1e8;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_3(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_3.Build());
  EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeEstFillPriceLtBuyLiqPrice);

  te->UpdateQuoteData(symbol, 5, "10000", "5000000", false);
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);
  side = ESide::Buy;
  order_id = te->GenUUID();
  FutureOneOfCreateOrderBuilder create_build_user2_buy_3(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_3.Build());
  EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeEstFillPriceGtSellLiqPrice);
}

std::shared_ptr<tmock::CTradeAppMock> FloatingOrderPricingByCreateOrderTest::te;
