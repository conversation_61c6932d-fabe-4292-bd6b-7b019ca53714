#include "test/biz/trade/future/feature/floating_order_price/bust_price/bust_price.hpp"

#include <algorithm>
#include <bbase/common/decimal/decimal.hpp>
#include <memory>
#include <string>
#include <unordered_map>

#include "enums/ebizcode/biz_code.pb.h"
#include "src/biz_worker/engine/pre_worker/pre_engine.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/store/per_symbol_store.hpp"
#include "src/biz_worker/service/trade/store/position/cow_position.hpp"
#include "src/biz_worker/service/trade/store/position/raw_position.hpp"
#include "src/biz_worker/utils/calc_util.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/lib/stub.hpp"

int32_t BustPriceCm::Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t BustPriceCm::SwitchMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

TEST_F(BustPriceCm, bust_price_in_cross_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "200"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  EXPECT_EQ(0, Deposit(uid_2, "200"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Cross));

  biz::coin_t coin = 5;
  biz::symbol_t symbol_btc = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  biz::symbol_t symbol_btc_inverse = ESymbol::BTCUSD;
  biz::coin_t coin_btc = 1;

  te->UpdateQuoteData(symbol_btc, 5, "9000", "5000000", false);

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 9000, 9000, 9000},
                           {static_cast<ESymbol>(symbol_btc_inverse), 9000, 9000, 9000}});
  }

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  ESide side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol_btc, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  auto resp = user2.process(create_build_user2_sell.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());

  // 用户1挂买单
  auto order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, 20000000);
  EXPECT_EQ(94500000, order_checker.m_msg->price_x());  // 订单价格为most price
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(20000000, pz_checker.m_msg->size_x());
  result = te->PopResult();
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(20000000, pz_checker.m_msg->size_x());

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(85500000, order_checker.m_msg->price_x());
  EXPECT_EQ(20000000, order_checker.m_msg->qty_x());

  // 用户2挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());
  EXPECT_EQ(94500000, order_checker.m_msg->price_x());
  EXPECT_EQ(20000000, order_checker.m_msg->qty_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 8500, 8500, 8500},
                           {static_cast<ESymbol>(symbol_btc_inverse), 9000, 9000, 9000}});
  }

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_2(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(80750000, order_checker.m_msg->price_x());
  EXPECT_EQ(20000000, order_checker.m_msg->qty_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 8300, 8300, 8300},
                           {static_cast<ESymbol>(symbol_btc_inverse), 9000, 9000, 9000}});
  }

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_3(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(80103000, order_checker.m_msg->price_x());
  EXPECT_EQ(20000000, order_checker.m_msg->qty_x());
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 8050, 8050, 8050},
                           {static_cast<ESymbol>(symbol_btc_inverse), 9000, 9000, 9000}});
  }

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_4(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_1, coin);
  create_build_user1_sell_4.msg.mutable_create_order()->set_close_on_trigger(true);
  resp = user1.process(create_build_user1_sell_4.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(80103000, order_checker.m_msg->price_x());
  EXPECT_EQ(20000000, order_checker.m_msg->qty_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 8000, 8000, 8000},
                           {static_cast<ESymbol>(symbol_btc_inverse), 9000, 9000, 9000}});
  }

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_5(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_1, coin);
  create_build_user1_sell_5.msg.mutable_create_order()->set_close_on_trigger(true);
  resp = user1.process(create_build_user1_sell_5.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(80105000, order_checker.m_msg->price_x());
  EXPECT_EQ(20000000, order_checker.m_msg->qty_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 9500, 9500, 9500},
                           {static_cast<ESymbol>(symbol_btc_inverse), 9000, 9000, 9000}});
  }

  // 用户2挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user2_buy_3(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());
  EXPECT_EQ(99750000, order_checker.m_msg->price_x());
  EXPECT_EQ(20000000, order_checker.m_msg->qty_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 9700, 9700, 9700},
                           {static_cast<ESymbol>(symbol_btc_inverse), 9000, 9000, 9000}});
  }

  // 用户2挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user2_buy_2(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());
  EXPECT_EQ(99931000, order_checker.m_msg->price_x());
  EXPECT_EQ(20000000, order_checker.m_msg->qty_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 9950, 9950, 9950},
                           {static_cast<ESymbol>(symbol_btc_inverse), 9000, 9000, 9000}});
  }

  // 用户2挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user2_buy_4(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_2, coin);
  create_build_user2_buy_4.msg.mutable_create_order()->set_close_on_trigger(true);
  resp = user2.process(create_build_user2_buy_4.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());
  EXPECT_EQ(99931000, order_checker.m_msg->price_x());
  EXPECT_EQ(20000000, order_checker.m_msg->qty_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 10000, 10000, 10000},
                           {static_cast<ESymbol>(symbol_btc_inverse), 9000, 9000, 9000}});
  }

  // 用户2挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user2_buy_5(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_2, coin);
  create_build_user2_buy_5.msg.mutable_create_order()->set_close_on_trigger(true);
  resp = user2.process(create_build_user2_buy_5.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());
  EXPECT_EQ(99930000, order_checker.m_msg->price_x());
  EXPECT_EQ(20000000, order_checker.m_msg->qty_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 9000, 9000, 9000},
                           {static_cast<ESymbol>(symbol_btc_inverse), 9000, 9000, 9000}});
  }

  // 刷新标记价格
  order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user2_buy_6(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_2, coin);
  create_build_user2_buy_6.msg.mutable_create_order()->set_close_on_trigger(true);
  resp = user2.process(create_build_user2_buy_6.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 刷新标记价格
  order_id = te->GenUUID();
  side = ESide::Sell;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_6(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_1, coin);
  create_build_user1_sell_6.msg.mutable_create_order()->set_close_on_trigger(true);
  resp = user1.process(create_build_user1_sell_6.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  EXPECT_EQ(0, Deposit(uid_1, "200"));
  EXPECT_EQ(0, Deposit(uid_2, "200"));

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  qty = 500;
  order_type = EOrderType::Limit;
  FutureOneOfCreateOrderBuilder create_build_user2_sell_inverse(order_id_2, symbol_btc_inverse, pz_index, side,
                                                                order_type, qty, price, ETimeInForce::GoodTillCancel,
                                                                uid_2, coin_btc);
  resp = user2.process(create_build_user2_sell_inverse.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_inverse(order_id, symbol_btc_inverse, pz_index, side, order_type,
                                                               qty, price, ETimeInForce::GoodTillCancel, uid_1,
                                                               coin_btc);
  resp = user1.process(create_build_user1_buy_inverse.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, 500);
  EXPECT_EQ(90000000, order_checker.m_msg->price_x());  // 订单价格为most price
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(100000, pz_checker.m_msg->user_id());
  EXPECT_EQ(500, pz_checker.m_msg->size_x());
  result = te->PopResult();
  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(500, pz_checker.m_msg->size_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 9000, 9000, 9000},
                           {static_cast<ESymbol>(symbol_btc_inverse), 8000, 8000, 8000}});
  }

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_inverse_1(order_id, symbol_btc_inverse, pz_index, side,
                                                                  order_type, qty, price, ETimeInForce::GoodTillCancel,
                                                                  uid_1, coin_btc);
  create_build_user1_sell_inverse_1.msg.mutable_create_order()->set_close_on_trigger(true);
  resp = user1.process(create_build_user1_sell_inverse_1.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(76000000, order_checker.m_msg->price_x());
  EXPECT_EQ(500, order_checker.m_msg->qty_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 9000, 9000, 9000},
                           {static_cast<ESymbol>(symbol_btc_inverse), 7100, 7100, 7100}});
  }

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_inverse_2(order_id, symbol_btc_inverse, pz_index, side,
                                                                  order_type, qty, price, ETimeInForce::GoodTillCancel,
                                                                  uid_1, coin_btc);
  create_build_user1_sell_inverse_2.msg.mutable_create_order()->set_close_on_trigger(true);
  resp = user1.process(create_build_user1_sell_inverse_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(67450000, order_checker.m_msg->price_x());
  EXPECT_EQ(500, order_checker.m_msg->qty_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 9000, 9000, 9000},
                           {static_cast<ESymbol>(symbol_btc_inverse), 6500, 6500, 6500}});
  }

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_inverse_3(order_id, symbol_btc_inverse, pz_index, side,
                                                                  order_type, qty, price, ETimeInForce::GoodTillCancel,
                                                                  uid_1, coin_btc);
  create_build_user1_sell_inverse_3.msg.mutable_create_order()->set_close_on_trigger(true);
  resp = user1.process(create_build_user1_sell_inverse_3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100000, order_checker.m_msg->user_id());
  EXPECT_EQ(67505000, order_checker.m_msg->price_x());
  EXPECT_EQ(500, order_checker.m_msg->qty_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 9000, 9000, 9000},
                           {static_cast<ESymbol>(symbol_btc_inverse), 10000, 10000, 10000}});
  }

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user2_buy_inverse(order_id, symbol_btc_inverse, pz_index, side, order_type,
                                                               qty, price, ETimeInForce::GoodTillCancel, uid_2,
                                                               coin_btc);
  create_build_user2_buy_inverse.msg.mutable_create_order()->set_close_on_trigger(true);
  resp = user2.process(create_build_user2_buy_inverse.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());
  EXPECT_EQ(105000000, order_checker.m_msg->price_x());
  EXPECT_EQ(500, order_checker.m_msg->qty_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 9000, 9000, 9000},
                           {static_cast<ESymbol>(symbol_btc_inverse), 11600, 11600, 11600}});
  }

  order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user2_buy_inverse_2(order_id, symbol_btc_inverse, pz_index, side,
                                                                 order_type, qty, price, ETimeInForce::GoodTillCancel,
                                                                 uid_2, coin_btc);
  create_build_user2_buy_inverse_2.msg.mutable_create_order()->set_close_on_trigger(true);
  resp = user2.process(create_build_user2_buy_inverse_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());
  EXPECT_EQ(121800000, order_checker.m_msg->price_x());
  EXPECT_EQ(500, order_checker.m_msg->qty_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 9000, 9000, 9000},
                           {static_cast<ESymbol>(symbol_btc_inverse), 13000, 13000, 13000}});
  }

  order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user2_buy_inverse_3(order_id, symbol_btc_inverse, pz_index, side,
                                                                 order_type, qty, price, ETimeInForce::GoodTillCancel,
                                                                 uid_2, coin_btc);
  create_build_user2_buy_inverse_3.msg.mutable_create_order()->set_close_on_trigger(true);
  resp = user2.process(create_build_user2_buy_inverse_3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());
  EXPECT_EQ(135920000, order_checker.m_msg->price_x());
  EXPECT_EQ(500, order_checker.m_msg->qty_x());

  {
    te->BatchAddMarkPrice({{static_cast<ESymbol>(symbol_btc), 9000, 9000, 9000},
                           {static_cast<ESymbol>(symbol_btc_inverse), 14500, 14500, 14500}});
  }

  order_id = te->GenUUID();
  side = ESide::Buy;
  order_type = EOrderType::Market;
  FutureOneOfCreateOrderBuilder create_build_user2_buy_inverse_4(order_id, symbol_btc_inverse, pz_index, side,
                                                                 order_type, qty, price, ETimeInForce::GoodTillCancel,
                                                                 uid_2, coin_btc);
  create_build_user2_buy_inverse_4.msg.mutable_create_order()->set_close_on_trigger(true);
  resp = user2.process(create_build_user2_buy_inverse_4.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(100002, order_checker.m_msg->user_id());
  EXPECT_EQ(135335000, order_checker.m_msg->price_x());
  EXPECT_EQ(500, order_checker.m_msg->qty_x());
}

std::shared_ptr<tmock::CTradeAppMock> BustPriceCm::te;
