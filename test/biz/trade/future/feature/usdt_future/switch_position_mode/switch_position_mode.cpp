#include "test/biz/trade/future/feature/usdt_future/switch_position_mode/switch_position_mode.hpp"

#include <memory>
#include <string>

#include "biz_worker/utils/pb_convertor/to_pb.hpp"
#include "lib/msg_builder.hpp"
#include "src/biz_worker/utils/pb_convertor/to_pb.hpp"
#include "src/cross_worker/cross_common/util/x_comm.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"

int32_t UsdtFutureSwitchPositionModeTest::SwitchAccountMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t UsdtFutureSwitchPositionModeTest::Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

TEST_F(UsdtFutureSwitchPositionModeTest, switch_position_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);
  {
    FutureOneOfSwitchPositionModeBuilder switch_position_mode(ESymbol(300), ECoin::USDT, EPositionMode::BothSide,
                                                              uid_1);
    auto resp = user1.process(switch_position_mode.Build());
    EXPECT_EQ(10001, resp->ret_code());
    EXPECT_EQ(resp->ret_msg(), "symbol is not support switch position mode");
  }
}

TEST_F(UsdtFutureSwitchPositionModeTest, switch_position_mode_with_order) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  auto symbol = ESymbol(300);
  auto coin = ECoin::USDT;

  EXPECT_EQ(SwitchAccountMode(uid_1, EAccountMode::Isolated), 0);

  EXPECT_EQ(Deposit(uid_1, "100000", coin), 0);

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 9000000;
  biz::price_x_t price = 100000 * 1e4;
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 9000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  FutureOneOfSwitchPositionModeBuilder switch_position_mode(symbol, coin, EPositionMode::BothSide, uid_1);
  resp = user1.process(switch_position_mode.Build());
  EXPECT_EQ(10001, resp->ret_code());
  EXPECT_EQ(resp->ret_msg(), "symbol is not support switch position mode");
}

std::shared_ptr<tmock::CTradeAppMock> UsdtFutureSwitchPositionModeTest::te;
