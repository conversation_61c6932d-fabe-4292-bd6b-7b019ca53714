#include "test/biz/trade/future/feature/usdt_future/ins_loan/ins_loan.hpp"

#include <map>
#include <memory>
#include <set>
#include <string>

#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/futures/modules/orderbiz/activeorderbiz/active_order_biz.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/future/func/draft_pkg_builder.h"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

int32_t UsdtFutureInsLoanTest::Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t UsdtFutureInsLoanTest::SwitchAccountMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

/*
测试场景：设置机构借贷白名单 - USDT交割，开启USDT交割白名单，白名单没有配置USDT交割，USDT交割只能平仓
预期结果：开仓平仓符合预期
*/
TEST_F(UsdtFutureInsLoanTest, set_customer_symbol_list) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol(300);
  auto coin = ECoin::USDT;

  EXPECT_EQ(Deposit(uid_1, "100000", coin), 0);
  EXPECT_EQ(Deposit(uid_2, "100000", coin), 0);

  EXPECT_EQ(SwitchAccountMode(uid_1, EAccountMode::Cross), 0);
  EXPECT_EQ(SwitchAccountMode(uid_2, EAccountMode::Cross), 0);

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  biz::leverage_e2_t buy_lv = 5000;
  biz::leverage_e2_t sell_lv = 5000;

  FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, uid_1, buy_lv, sell_lv);
  auto resp = user1.process(set_lv_build.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->leverage_e2(), 5000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = ********;
  biz::price_x_t price = 100000 * 1e4;

  std::set<int64_t> usdt_list;
  std::set<int64_t> usdc_list;
  std::set<std::string> usdc_future_list;
  {
    FutureSetSymbolListBuilder set_req_build(uid_1, false, false, usdt_list, false, usdt_list, false, usdc_future_list);

    set_req_build.msg.mutable_set_customer_symbol_list()->set_usdt_future_symbol_enabled(true);
    set_req_build.msg.mutable_set_customer_symbol_list()->add_support_usdc_future("BTC");

    resp = user1.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    result = te->PopResult();
    auto setting_checker = result.RefAssetMarginResult().RefRelatedUserSetting();
    EXPECT_EQ(setting_checker.m_msg->inverse_symbol_banned(), false);
    setting_checker.CheckSymbolList(ECoin::USDT, false, true, 0);
    setting_checker.CheckSymbolList(ECoin::USDC, false, false, 0);
  }

  // 开仓
  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeInstLoanNotInWhiteList);

  {
    FutureSetSymbolListBuilder set_req_build(uid_1, false, false, usdt_list, false, usdt_list, false, usdc_future_list);

    set_req_build.msg.mutable_set_customer_symbol_list()->set_usdt_future_symbol_enabled(true);
    set_req_build.msg.mutable_set_customer_symbol_list()->add_support_usdt_future("BTC");

    resp = user1.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    result = te->PopResult();
    auto setting_checker = result.RefAssetMarginResult().RefRelatedUserSetting();
    EXPECT_EQ(setting_checker.m_msg->inverse_symbol_banned(), false);
    setting_checker.CheckSymbolList(ECoin::USDT, false, true, 1);
    setting_checker.CheckSymbolList(ECoin::USDC, false, false, 0);
  }

  FutureOneOfCreateOrderBuilder create_build_user1_buy_1(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_1.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), ********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), ********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), ********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), ********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);

  {
    FutureSetSymbolListBuilder set_req_build(uid_1, false, false, usdt_list, false, usdt_list, false, usdc_future_list);

    set_req_build.msg.mutable_set_customer_symbol_list()->set_usdt_future_symbol_enabled(true);
    set_req_build.msg.mutable_set_customer_symbol_list()->add_support_usdc_future("BTC");

    resp = user1.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    result = te->PopResult();
    auto setting_checker = result.RefAssetMarginResult().RefRelatedUserSetting();
    EXPECT_EQ(setting_checker.m_msg->inverse_symbol_banned(), false);
    setting_checker.CheckSymbolList(ECoin::USDT, false, true, 0);
    setting_checker.CheckSymbolList(ECoin::USDC, false, false, 0);
  }

  // 加仓
  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = ********;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeInstLoanNotInWhiteList);

  // 减仓，普通平仓单
  // 用户1挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeInstLoanNotInWhiteList);

  // 用户1挂RO卖单
  FutureOneOfCreateOrderBuilder create_build_user1_sell_ro(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
  create_build_user1_sell_ro.msg.mutable_create_order()->set_reduce_only(true);
  resp = user1.process(create_build_user1_sell_ro.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), ********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 清理白名单
  {
    FutureSetSymbolListBuilder set_req_build(uid_1, false, false, usdt_list, false, usdt_list, false, usdc_future_list);
    set_req_build.msg.mutable_set_customer_symbol_list()->set_clear_all(true);
    resp = user1.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    result = te->PopResult();
    auto setting_checker = result.RefAssetMarginResult().RefRelatedUserSetting();
    EXPECT_EQ(setting_checker.m_msg->inverse_symbol_banned(), false);
    setting_checker.CheckSymbolList(ECoin::USDT, false, false, 0);
    setting_checker.CheckSymbolList(ECoin::USDC, false, false, 0);
  }

  // 可以下开仓单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 5000000;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_3(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_3.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 5000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
}

/*
测试场景：USDT交割没有仓位设置symbol最大杠杆，自动调整仓位计算系数；USDT交割有持仓设置symbol最大杠杆小于当前杠杆，不能开仓，只能平仓
预期结果：开仓平仓符合预期
*/
TEST_F(UsdtFutureInsLoanTest, set_symbol_max_lv) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol(300);
  auto coin = ECoin::USDT;

  EXPECT_EQ(Deposit(uid_1, "100000", coin), 0);
  EXPECT_EQ(Deposit(uid_2, "100000", coin), 0);

  EXPECT_EQ(SwitchAccountMode(uid_1, EAccountMode::Cross), 0);
  EXPECT_EQ(SwitchAccountMode(uid_2, EAccountMode::Cross), 0);

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  biz::leverage_e2_t buy_lv = 5000;
  biz::leverage_e2_t sell_lv = 5000;

  FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, uid_1, buy_lv, sell_lv);
  auto resp = user1.process(set_lv_build.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->leverage_e2(), 5000);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_cost_e8(), 2118800);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 618800);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_cost_e8(), 2121200);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 621200);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 9000000;
  biz::price_x_t price = 100000 * 1e4;
  std::map<int64_t, int64_t> symbol_max_leverage_map;

  {
    FutureSetDefaultMaxLeverageBuilder set_req_build(uid_1, true, 0, 0, 0, 0);
    set_req_build.msg.mutable_set_customer_default_max_leverage()->set_usdt_future_btc(20 * 1e2);
    resp = user1.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    result = te->PopResult();
    auto setting = result.m_msg->asset_margin_result().per_user_setting_data();
    auto user_setting = setting.future_per_coin_setting().find(coin);
    ASSERT_TRUE(user_setting != setting.future_per_coin_setting().end());
    EXPECT_EQ(user_setting->second.future_btc_max_leverage_e2(), 20 * 1e2);
  }

  buy_lv = 3000;
  sell_lv = 3000;

  FutureOneOfSetLeverageBuilder set_lv_build_2(symbol, coin, uid_1, buy_lv, sell_lv);
  resp = user1.process(set_lv_build_2.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeInstLoanCanNotSetLeverageGTLimit);

  // 开仓
  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 9000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 9000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 9000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 9000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);

  {
    FutureSetDefaultMaxLeverageBuilder set_req_build(uid_1, false, 0, 0, 0, 0);
    set_req_build.msg.mutable_set_customer_default_max_leverage()->set_usdt_future_btc(20 * 1e2);
    resp = user1.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    result = te->PopResult();
    auto setting = result.m_msg->asset_margin_result().per_user_setting_data();
    auto user_setting = setting.future_per_coin_setting().find(coin);
    ASSERT_TRUE(user_setting != setting.future_per_coin_setting().end());
    EXPECT_EQ(user_setting->second.future_btc_max_leverage_e2(), 0);
  }

  buy_lv = 5000;
  sell_lv = 5000;

  FutureOneOfSetLeverageBuilder set_lv_build_3(symbol, coin, uid_1, buy_lv, sell_lv);
  resp = user1.process(set_lv_build_3.Build());
  resp = user1.process(set_lv_build.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->leverage_e2(), 5000);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_cost_e8(), 2118800);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 618800);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_cost_e8(), 2121200);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 621200);

  {
    FutureSetDefaultMaxLeverageBuilder set_req_build(uid_1, true, 0, 0, 0, 0);
    set_req_build.msg.mutable_set_customer_default_max_leverage()->set_usdt_future_btc(20 * 1e2);
    resp = user1.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    result = te->PopResult();
    auto setting = result.m_msg->asset_margin_result().per_user_setting_data();
    auto user_setting = setting.future_per_coin_setting().find(coin);
    ASSERT_TRUE(user_setting != setting.future_per_coin_setting().end());
    EXPECT_EQ(user_setting->second.future_btc_max_leverage_e2(), 20 * 1e2);
  }

  // 加仓
  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = ********;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeInstLoanCanNotSetLeverageGTLimit);

  // 减仓，普通平仓单
  // 用户1挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeInstLoanCanNotSetLeverageGTLimit);

  // 用户1挂RO卖单
  FutureOneOfCreateOrderBuilder create_build_user1_sell_ro(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
  create_build_user1_sell_ro.msg.mutable_create_order()->set_reduce_only(true);
  resp = user1.process(create_build_user1_sell_ro.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 9000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
}

/*
测试场景：LTV超限后USDT交割禁止开仓或交易
预期结果：封禁解禁符合预期
*/
TEST_F(UsdtFutureInsLoanTest, disable_open_or_trade) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol(300);
  auto coin = ECoin::USDT;

  EXPECT_EQ(Deposit(uid_1, "********", coin), 0);
  EXPECT_EQ(Deposit(uid_2, "********", coin), 0);

  EXPECT_EQ(SwitchAccountMode(uid_1, EAccountMode::Cross), 0);
  EXPECT_EQ(SwitchAccountMode(uid_2, EAccountMode::Cross), 0);

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 9000000;
  biz::price_x_t price = 100000 * 1e4;

  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 9000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 9000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 9000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 9000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);

  {
    // 设置封禁
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid_1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstBanOrUnban);
    auto* setting = unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_instbanorunbanreq();
    setting->set_future_usdt_open_disabled(true);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid_1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  // 加仓
  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 1000000;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeMarginNotAllowOpen);

  // 减仓，普通平仓单
  // 用户1挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeMarginNotAllowOpen);

  // 用户1挂RO卖单
  FutureOneOfCreateOrderBuilder create_build_user1_sell_ro(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
  create_build_user1_sell_ro.msg.mutable_create_order()->set_reduce_only(true);
  resp = user1.process(create_build_user1_sell_ro.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  {
    // 设置封禁
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid_1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstBanOrUnban);
    auto* setting = unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_instbanorunbanreq();
    setting->set_future_usdt_trade_disabled(true);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid_1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  FutureOneOfCancelOrderBuilder cancel_build(order_id_2, symbol, uid_1, coin);
  resp = user1.process(cancel_build.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户1挂RO卖单
  order_id_2 = te->GenUUID();
  FutureOneOfCreateOrderBuilder create_build_user1_sell_ro_2(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                             ETimeInForce::GoodTillCancel, uid_1, coin);
  create_build_user1_sell_ro_2.msg.mutable_create_order()->set_reduce_only(true);
  resp = user1.process(create_build_user1_sell_ro_2.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeMarginNotAllowTrade);

  FutureOneOfFixUserSettingBuilder fix_user_setting_user1(uid_1);
  fix_user_setting_user1.msg.mutable_fix_user_setting()->set_modify_coin_setting(true);
  fix_user_setting_user1.msg.mutable_fix_user_setting()->set_coin(ECoin::USDT);
  fix_user_setting_user1.msg.mutable_fix_user_setting()->set_modify_trade_disabled(true);
  fix_user_setting_user1.msg.mutable_fix_user_setting()->set_new_trade_disabled(false);

  resp = user1.process(fix_user_setting_user1.Build());
  result = te->PopResult();
  EXPECT_NE(result.m_msg.get(), nullptr);

  // 用户1挂RO卖单
  FutureOneOfCreateOrderBuilder create_build_user1_sell_ro_3(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                             ETimeInForce::GoodTillCancel, uid_1, coin);
  create_build_user1_sell_ro_3.msg.mutable_create_order()->set_reduce_only(true);
  resp = user1.process(create_build_user1_sell_ro_3.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  FutureOneOfFixUserSettingBuilder fix_user_setting_user1_2(uid_1);
  fix_user_setting_user1_2.msg.mutable_fix_user_setting()->set_modify_coin_setting(true);
  fix_user_setting_user1_2.msg.mutable_fix_user_setting()->set_coin(ECoin::USDT);
  fix_user_setting_user1_2.msg.mutable_fix_user_setting()->set_modify_open_disabled(true);
  fix_user_setting_user1_2.msg.mutable_fix_user_setting()->set_new_open_disabled(false);

  resp = user1.process(fix_user_setting_user1_2.Build());
  result = te->PopResult();
  EXPECT_NE(result.m_msg.get(), nullptr);

  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 2000000;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_3(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_3.Build());
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 2000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
}

std::shared_ptr<tmock::CTradeAppMock> UsdtFutureInsLoanTest::te;
