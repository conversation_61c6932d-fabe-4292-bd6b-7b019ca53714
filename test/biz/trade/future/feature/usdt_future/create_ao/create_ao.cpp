#include "test/biz/trade/future/feature/usdt_future/create_ao/create_ao.hpp"

#include <memory>
#include <string>

#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/futures/modules/orderbiz/activeorderbiz/active_order_biz.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/future/func/draft_pkg_builder.h"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

int32_t UsdtFutureCreateAoTest::Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t UsdtFutureCreateAoTest::SwitchAccountMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

/*
测试场景：逐仓单仓模式，
预期结果：挂单成交符合预期
*/
TEST_F(UsdtFutureCreateAoTest, isolated_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol(300);
  auto coin = ECoin::USDT;

  EXPECT_EQ(SwitchAccountMode(uid_1, EAccountMode::Isolated), 0);
  EXPECT_EQ(SwitchAccountMode(uid_2, EAccountMode::Isolated), 0);

  EXPECT_EQ(Deposit(uid_1, "10000", coin), 0);
  EXPECT_EQ(Deposit(uid_2, "10000", coin), 0);

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  // 用户1开仓挂单, 没有浮亏, 成本<可用余额, 挂单成功
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = ********;
  biz::price_x_t price = 100000 * 1e4;
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), ********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(order_checker.m_msg->order_status(), EOrderStatus::New);
  ASSERT_EQ(result.RefFutureMarginResult().RefRelatedPositionSize(), 1);
  auto position_checker = result.RefFutureMarginResult().RefRelatedPositionBySymbol(symbol, EPositionIndex::Single);
  EXPECT_EQ(position_checker.m_msg->is_isolated(), true);
  EXPECT_EQ(position_checker.m_msg->buy_value_to_cost_e8(), 10114000);
  EXPECT_EQ(position_checker.m_msg->sell_value_to_cost_e8(), 10126000);
  // 0.1 * 100000 * 10114000
  EXPECT_EQ(position_checker.m_msg->order_cost_e8(), 101140000000);
  EXPECT_EQ(position_checker.m_msg->unrealised_order_loss_e8(), 0);
  auto wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(coin);
  EXPECT_EQ(wallet_checker.m_msg->wallet().cash_balance(), "10000.000000000000000000");
  EXPECT_EQ(wallet_checker.m_msg->wallet().total_order_im(), "1011.400000000000000000");
  EXPECT_EQ(wallet_checker.m_msg->wallet().available_balance(), "8988.600000000000000000");

  // 限价单开仓挂单，没有浮亏，成本 < 可用余额，调大杠杆
  FutureOneOfSetLeverageBuilder user1_set_leverage_1(symbol, coin, uid_1, 2000, 2000);
  resp = user1.process(user1_set_leverage_1.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.RefFutureMarginResult().RefRelatedPositionSize(), 1);
  position_checker = result.RefFutureMarginResult().RefRelatedPositionBySymbol(symbol, EPositionIndex::Single);
  EXPECT_EQ(position_checker.m_msg->is_isolated(), true);
  // 1/20 + 0.0006 + 19/20*0.0006 = 0.05117
  // 1/20 + 0.0006 + 21/20*0.0006 = 0.05123
  EXPECT_EQ(position_checker.m_msg->buy_value_to_cost_e8(), 5117000);
  EXPECT_EQ(position_checker.m_msg->sell_value_to_cost_e8(), 5123000);
  EXPECT_EQ(position_checker.m_msg->order_cost_e8(), 51170000000);
  wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(coin);
  EXPECT_EQ(wallet_checker.m_msg->wallet().cash_balance(), "10000.000000000000000000");
  EXPECT_EQ(wallet_checker.m_msg->wallet().total_order_im(), "511.************000000");
  EXPECT_EQ(wallet_checker.m_msg->wallet().available_balance(), "9488.300000000000000000");

  // 限价单开仓挂单，没有浮亏，成本 < 可用余额，调小杠杆，且调整成功  10x -> 15x
  FutureOneOfSetLeverageBuilder user1_set_leverage_2(symbol, coin, uid_1, 1500, 1500);
  resp = user1.process(user1_set_leverage_2.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.RefFutureMarginResult().RefRelatedPositionSize(), 1);
  position_checker = result.RefFutureMarginResult().RefRelatedPositionBySymbol(symbol, EPositionIndex::Single);
  EXPECT_EQ(position_checker.m_msg->is_isolated(), true);
  // 1/15 + 0.0006 + 14/15*0.0006 = 0.06782667
  EXPECT_EQ(position_checker.m_msg->buy_value_to_cost_e8(), 6782667);
  EXPECT_EQ(position_checker.m_msg->sell_value_to_cost_e8(), 6790667);
  EXPECT_EQ(position_checker.m_msg->order_cost_e8(), 67826670000);
  wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(coin);
  EXPECT_EQ(wallet_checker.m_msg->wallet().cash_balance(), "10000.000000000000000000");
  EXPECT_EQ(wallet_checker.m_msg->wallet().total_order_im(), "678.266************000");
  EXPECT_EQ(wallet_checker.m_msg->wallet().available_balance(), "9321.733300000000000000");

  // 继续调小杠杆, 15x -> 10x
  FutureOneOfSetLeverageBuilder user1_set_leverage_3(symbol, coin, uid_1, 1000, 1000);
  resp = user1.process(user1_set_leverage_3.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.RefFutureMarginResult().RefRelatedPositionSize(), 1);
  position_checker = result.RefFutureMarginResult().RefRelatedPositionBySymbol(symbol, EPositionIndex::Single);
  EXPECT_EQ(position_checker.m_msg->is_isolated(), true);
  EXPECT_EQ(position_checker.m_msg->buy_value_to_cost_e8(), 10114000);
  EXPECT_EQ(position_checker.m_msg->sell_value_to_cost_e8(), 10126000);
  EXPECT_EQ(position_checker.m_msg->order_mm_with_fee_e8(), 6140000000);
  EXPECT_EQ(position_checker.m_msg->order_cost_e8(), 101140000000);
  wallet_checker = result.RefAssetMarginResult().RefRelatedWallet(coin);
  EXPECT_EQ(wallet_checker.m_msg->wallet().cash_balance(), "10000.000000000000000000");
  EXPECT_EQ(wallet_checker.m_msg->wallet().total_order_im(), "1011.400000000000000000");
  EXPECT_EQ(wallet_checker.m_msg->wallet().available_balance(), "8988.600000000000000000");

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), ********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), ********);
  EXPECT_EQ(pz_checker.m_msg->value_e8(), ********00000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), ********00000);
  EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), ********00);
  EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ********000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 0.005 + 9/10*0.0006 = 0.00614
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 0.1 * 100000 * (0.00626000 - 0.0006) = 56.6
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 5660000000);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 0.1 * 100000 * (0.10126 - 0.0006) = 1006.6
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 100660000000);
  // bust_value = ((pz_value + pb) / (1 + defalut_taker_fee_rate))
  // bust_value = (0.1 * 100000 + 1006.6)/(1 + 0.0006) = 11000
  // 11000/(0.1) = 110000
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1********0);
  // liq_value = bust_value - mm
  // mm = value * mmrate = 0.1 * 100000 * 0.005 = 50
  // liq_value = 11000 - 50 = 10950
  // 10950 / 0.1 = 109500
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1095000000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 1094527000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), ********);
  EXPECT_EQ(pz_checker.m_msg->value_e8(), ********00000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), ********00000);
  EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), ********00);
  EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ********000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 0.1 * 100000 * (0.00614000 - 0.0006) = 55.4
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 5540000000);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 0.1 * 100000 * (0.10114 - 0.0006) = 1005.4
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 100540000000);
  // bust_value = ((pz_value - pb) / (1 - defalut_taker_fee_rate))
  // bust_value = (0.1 * 100000 - 1005.4)/(1 - 0.0006) = 9000
  // 9000/(0.1) = 90000
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 900000000);
  // liq_value = bust_value + mm
  // mm = value * mmrate = 0.1 * 100000 * 0.005 = 50
  // liq_value = 9000 + 50 = 9050
  // 9050 / 0.1 = 90500
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 905000000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 904523000);
}

/*
测试场景：逐仓全仓模式，用户挂反向订单，开仓，加仓，减仓，反向开仓，完全平仓
预期结果：挂单成交符合预期
*/
TEST_F(UsdtFutureCreateAoTest, cross_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol(300);
  auto coin = ECoin::USDT;

  EXPECT_EQ(Deposit(uid_1, "10000", coin), 0);
  EXPECT_EQ(Deposit(uid_2, "10000", coin), 0);

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = ********;
  biz::price_x_t price = 100000 * 1e4;

  // 开仓
  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), ********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), ********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), ********);
  EXPECT_EQ(pz_checker.m_msg->value_e8(), ********00000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), ********00000);
  EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), ********00);
  EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ********000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 0.1 * 100000 * (0.00626000 - 0.0006) = 56.6
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 5660000000);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 0.1 * 100000 * (0.10126000 - 0.0006) = 1006.6
  EXPECT_EQ(pz_checker.m_msg->min_position_cost_e8(), 100660000000);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_cost_e8(), 10114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_cost_e8(), 10126000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), ********);
  EXPECT_EQ(pz_checker.m_msg->value_e8(), ********00000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), ********00000);
  EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), ********00);
  EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ********000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 0.1 * 100000 * (0.00614000 - 0.0006) = 55.4
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 5540000000);
  // 0.1 * 100000 * (0.10114000 - 0.0006) = 1005.4
  EXPECT_EQ(pz_checker.m_msg->min_position_cost_e8(), 100540000000);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_cost_e8(), 10114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_cost_e8(), 10126000);

  // 加仓
  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 4000000;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 4000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell_2(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 4000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 14000000);
  EXPECT_EQ(pz_checker.m_msg->value_e8(), 1400000000000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 1400000000000);
  EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), ********00);
  EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ********000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  // 14000 * (0.00626000 - 0.0006) = 79.24
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 7924000000);
  // 14000 * (0.10126 - 0.0006) = 1409.24
  EXPECT_EQ(pz_checker.m_msg->min_position_cost_e8(), 140924000000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 14000000);
  EXPECT_EQ(pz_checker.m_msg->value_e8(), 1400000000000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 1400000000000);
  EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), ********00);
  EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ********000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  // 14000 * (0.00614000 - 0.0006) = 77.56
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 7756000000);
  // 14000 * (0.10114 - 0.0006) = 1407.56
  EXPECT_EQ(pz_checker.m_msg->min_position_cost_e8(), 140756000000);

  // 减仓
  // 用户2挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 5000000;
  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 5000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);

  // 用户1挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 5000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 9000000);
  // value = 5000 / 100000 = 0.1
  EXPECT_EQ(pz_checker.m_msg->value_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), ********00);
  EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ********000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  // 9000 * (0.00614000 - 0.0006) = 49.86
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 4986000000);
  // 9000 * (0.10114 - 0.0006) = 904.86
  EXPECT_EQ(pz_checker.m_msg->min_position_cost_e8(), 90486000000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 9000000);
  EXPECT_EQ(pz_checker.m_msg->value_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), ********00);
  EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ********000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  // 9000 * (0.10126 - 0.0006) = 905.94
  EXPECT_EQ(pz_checker.m_msg->min_position_cost_e8(), 90594000000);

  // 反向开仓
  // 用户2挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 16000000;
  FutureOneOfCreateOrderBuilder create_build_user2_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 16000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);

  // 用户1挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_2(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 16000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 7000000);
  EXPECT_EQ(pz_checker.m_msg->value_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), ********00);
  EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ********000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  // 7000 * (0.00626000 - 0.0006) = 39.62
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3962000000);
  // 7000 * (0.10126 - 0.0006) = 704.62
  EXPECT_EQ(pz_checker.m_msg->min_position_cost_e8(), 70462000000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 7000000);
  EXPECT_EQ(pz_checker.m_msg->value_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), ********00);
  EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ********000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  // 7000 * (0.10114 - 0.0006) = 703.78
  EXPECT_EQ(pz_checker.m_msg->min_position_cost_e8(), 70378000000);

  // 完全平仓
  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 7000000;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_3(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 7000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell_3(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 7000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->value_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->min_position_cost_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->value_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->session_average_price_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->min_position_cost_e8(), 0);
}

std::shared_ptr<tmock::CTradeAppMock> UsdtFutureCreateAoTest::te;
