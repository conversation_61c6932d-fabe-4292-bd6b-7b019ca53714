#include <gtest/gtest.h>

#include <memory>

#include "src/biz_worker/service/trade/store/order/cow_order.hpp"
#include "src/biz_worker/service/trade/store/order_manager/half_side_order_book.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/future/feature/inverse/halfob/check_half_ob_test.hpp"
#include "test/biz/trade/future/feature/inverse/halfob/make_order_test.hpp"
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class InverseHalfObSellClosingTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
};

TEST_F(InverseHalfObSellClosingTest, TestHalfObSellClosing) {
  auto sell_ob = std::make_shared<store::HalfSideOrderBook>(ESymbol::BTCUSD, ESide::Sell, EProductType::Futures);

  sell_ob->UpdateCachedPzSideSize(ESide::Buy, 500 * 1e4);
  sell_ob->UpdateCachedMarkPrice(10000 * 1e4);

  // 初始状态halfOB为空
  {
    EXPECT_EQ(sell_ob->TryGetNextFloatingOrder(nullptr), nullptr);
    InverseCheckHalfOB(sell_ob, 0, 0, 0, 500 * 1e4, store::kTopSellPriceX, std::numeric_limits<std::int64_t>::min(), 0,
                       0, 0, 0, 10000 * 1e4, 0, 0, 0, 0);
  }

  // 添加第1笔sell
  auto o1 =
      MakeLimitSell(ESymbol::BTCUSD, EContractType::InversePerpetual, 9970 * 1e4, 110 * 1e4);  // value = 110.33099297
  o1->exec_inst = EExecInst::Close;
  auto cow_ord1 = store::CowOrder::CreateCowOrder(o1);

  sell_ob->InsertOrder(cow_ord1.get());
  EXPECT_EQ(cow_ord1->item.close_on_trigger, true);
  EXPECT_EQ(cow_ord1->item.reduce_only, true);
  // 边界等于o1
  InverseCheckHalfOB(sell_ob, 1, 110 * 1e4, 11033099297, 500 * 1e4, 9970 * 1e4, cow_ord1->item.key.added_op_seq,
                     110 * 1e4, 11033099297, 0, 0, 10000 * 1e4, 0, 0, 0, 1);

  // 添加第2笔sell
  auto o2 =
      MakeLimitSell(ESymbol::BTCUSD, EContractType::InversePerpetual, 9980 * 1e4, 220 * 1e4);  // value = 220.44088176
  o2->exec_inst = EExecInst::bit_reduce_only;
  auto cow_ord2 = store::CowOrder::CreateCowOrder(o2);

  sell_ob->InsertOrder(cow_ord2.get());

  EXPECT_EQ(cow_ord2->item.reduce_only, true);
  // 边界等于o2
  InverseCheckHalfOB(sell_ob, 2, 330 * 1e4, 11033099297 + 22044088176, 500 * 1e4, 9980 * 1e4,
                     cow_ord2->item.key.added_op_seq, 110 * 1e4 + 220 * 1e4, 11033099297 + 22044088176, 0, 0,
                     10000 * 1e4, 0, 0, 0, 1);

  // 添加第3笔sell
  auto o3 =
      MakeLimitSell(ESymbol::BTCUSD, EContractType::InversePerpetual, 9990 * 1e4, 330 * 1e4);  // value = 330.33033033
  auto cow_ord3 = store::CowOrder::CreateCowOrder(o3);
  sell_ob->InsertOrder(cow_ord3.get());

  // 边界等于o3
  InverseCheckHalfOB(sell_ob, 3, 660 * 1e4, 11033099297 + 22044088176 + 33033033033, 500 * 1e4, 9990 * 1e4,
                     cow_ord3->item.key.added_op_seq, 500 * 1e4, 11033099297 + 22044088176 + 17017017017, 160 * 1e4,
                     16016016016, 10000 * 1e4, 160 * 1e4, 16016016016, 0, 1);

  // 添加第4笔sell
  auto o4 =
      MakeLimitSell(ESymbol::BTCUSD, EContractType::InversePerpetual, 10001 * 1e4, 440 * 1e4);  // value = 439.95600439
  auto cow_ord4 = store::CowOrder::CreateCowOrder(o4);

  sell_ob->InsertOrder(cow_ord4.get());

  // 边界等于o3
  InverseCheckHalfOB(sell_ob, 4, 1100 * 1e4, 11033099297 + 22044088176 + 33033033033 + 43995600439, 500 * 1e4,
                     9990 * 1e4, cow_ord3->item.key.added_op_seq, 500 * 1e4, 11033099297 + 22044088176 + 17017017017,
                     600 * 1e4, 16016016016 + 43995600439, 10000 * 1e4, 160 * 1e4, 16016016016, 0, 1);

  ////////////////////////////////////////////////////////////////////////////////
  // 移除第3笔sell
  sell_ob->EraseOrder(cow_ord3.get());
  // 边界等于o4
  InverseCheckHalfOB(sell_ob, 3, 770 * 1e4, 11033099297 + 22044088176 + 43995600439, 500 * 1e4, 10001 * 1e4,
                     cow_ord4->item.key.added_op_seq, 500 * 1e4, 11033099297 + 22044088176 + 16998300169, 270 * 1e4,
                     26997300270, 10000 * 1e4, 0, 0, 0, 1);

  ////////////////////////////////////////////////////////////////////////////////
  // 再把第3笔sell添加回去
  sell_ob->InsertOrder(cow_ord3.get());
  // 边界等于o3
  InverseCheckHalfOB(sell_ob, 4, 1100 * 1e4, 11033099297 + 22044088176 + 33033033033 + 43995600439, 500 * 1e4,
                     9990 * 1e4, cow_ord3->item.key.added_op_seq, 500 * 1e4, 11033099297 + 22044088176 + 17017017017,
                     600 * 1e4, 16016016016 + 43995600439, 10000 * 1e4, 160 * 1e4, 16016016016, 0, 1);

  ////////////////////////////////////////////////////////////////////////////////
  // 强制将持仓更新为0
  sell_ob->EraseOrder(cow_ord1.get());
  sell_ob->InsertOrder(cow_ord1.get());
  sell_ob->EraseOrder(cow_ord2.get());
  sell_ob->InsertOrder(cow_ord2.get());
  sell_ob->UpdateCachedPzSideSize(ESide::None, 0);
  sell_ob->EraseOrder(cow_ord1.get());
  sell_ob->InsertOrder(cow_ord1.get());
  sell_ob->EraseOrder(cow_ord2.get());
  sell_ob->InsertOrder(cow_ord2.get());

  // 边界重置到最小
  InverseCheckHalfOB(sell_ob, 4, 1100 * 1e4, 11033099297 + 22044088176 + 33033033033 + 43995600439, 0,
                     store::kTopSellPriceX, std::numeric_limits<std::int64_t>::min(), 0, 0, 330 * 1e4 + 440 * 1e4,
                     33033033033 + 43995600439, 10000 * 1e4, 330 * 1e4, 33033033033, 2, 0);

  ////////////////////////////////////////////////////////////////////////////////
  // 强制将持仓更新为400
  sell_ob->UpdateCachedPzSideSize(ESide::Buy, 500 * 1e4);
  sell_ob->UpdateCachedPzSideSize(ESide::Buy, 330 * 1e4);
  sell_ob->UpdateCachedPzSideSize(ESide::Buy, 400 * 1e4);

  // 边界等于o3
  InverseCheckHalfOB(sell_ob, 4, 1100 * 1e4, 11033099297 + 22044088176 + 33033033033 + 43995600439, 400 * 1e4,
                     9990 * 1e4, cow_ord3->item.key.added_op_seq, 400 * 1e4, 11033099297 + 22044088176 + 7007007007,
                     700 * 1e4, 26026026026 + 43995600439, 10000 * 1e4, 260 * 1e4, 26026026026, 0, 1);

  ////////////////////////////////////////////////////////////////////////////////
  // 模拟标记价格浮动
  sell_ob->UpdateCachedMarkPrice(9900 * 1e4);

  // 边界等于o3
  InverseCheckHalfOB(sell_ob, 4, 1100 * 1e4, 11033099297 + 22044088176 + 33033033033 + 43995600439, 400 * 1e4,
                     9990 * 1e4, cow_ord3->item.key.added_op_seq, 400 * 1e4, 11033099297 + 22044088176 + 7007007007,
                     700 * 1e4, 26026026026 + 43995600439, 9900 * 1e4, 0, 0, 0, 1);

  sell_ob->UpdateCachedMarkPrice(10100 * 1e4);

  // 边界等于o3
  InverseCheckHalfOB(sell_ob, 4, 1100 * 1e4, 11033099297 + 22044088176 + 33033033033 + 43995600439, 400 * 1e4,
                     9990 * 1e4, cow_ord3->item.key.added_op_seq, 400 * 1e4, 11033099297 + 22044088176 + 7007007007,
                     700 * 1e4, 26026026026 + 43995600439, 10100 * 1e4, 700 * 1e4, 26026026026 + 43995600439, 0, 1);

  sell_ob->UpdateCachedMarkPrice(10000 * 1e4);

  // 边界等于o3
  InverseCheckHalfOB(sell_ob, 4, 1100 * 1e4, 11033099297 + 22044088176 + 33033033033 + 43995600439, 400 * 1e4,
                     9990 * 1e4, cow_ord3->item.key.added_op_seq, 400 * 1e4, 11033099297 + 22044088176 + 7007007007,
                     700 * 1e4, 26026026026 + 43995600439, 10000 * 1e4, 260 * 1e4, 26026026026, 0, 1);
}
