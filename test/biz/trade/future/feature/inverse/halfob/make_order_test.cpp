#include "test/biz/trade/future/feature/inverse/halfob/make_order_test.hpp"

#include <fmt/core.h>

#include "src/biz_worker/service/trade/store/order/cow_order.hpp"
#include "src/biz_worker/utils/calc_util.hpp"
#include "src/data/enum.hpp"

store::Order::Ptr CreateActiveOrder(ESymbol symbol, EContractType contract_type, ESide side, EOrderType order_type,
                                    ETimeInForce time_in_force, int32_t exec_inst, biz::price_x_t price,
                                    biz::size_x_t qty) {
  static std::atomic<int64_t> global_req_num = 0;
  static std::atomic<int64_t> global_wallet_ver = 0;

  auto ao = std::make_shared<store::Order>();
  ao->id = 0;
  ao->op_req_num = global_req_num++;
  ao->op_seq = global_wallet_ver++;
  ao->op_time = bbase::utils::Time::GetTimeNs();
  ao->user_id = 123456;
  ao->coin = ECoin::BTC;
  ao->symbol = symbol;
  ao->position_idx = EPositionIndex::Single;
  ao->action = EAction::CreateOrder;
  ao->create_type = ECreateType::CreateByUser;
  ao->cancel_type = ECancelType::UNKNOWN;
  ao->op_platform = EOpPlatform::FromSys;
  ao->user_ip = "";
  ao->remark = "";
  ao->order_link_id = "";
  ao->order_id = boost::uuids::to_string(boost::uuids::random_generator()()).data();
  ao->side = side;
  ao->order_type = order_type;
  ao->time_in_force = time_in_force;
  ao->exec_inst = exec_inst;
  ao->price = price;
  ao->qty = qty;
  ao->leaves_qty = qty;
  ao->contract_type = contract_type;

  if (ao->order_type == EOrderType::Limit || ao->price != 0) {
    ao->leaves_value = biz::CalcUtil::CalcExecValue(ao->leaves_qty, ao->price, 4, 8, 8, ao->contract_type);
  } else {
    ao->leaves_value = 0;  // MarketOrder的剩余价值无法计算, 需要等om进行排列&定价后
  }

  ao->added_cross_seq = ao->op_seq;
  ao->cum_qty = 0;
  ao->cum_value = 0;
  ao->cross_seq = 0;
  ao->transact_time = 0;
  ao->order_status = EOrderStatus::Created;
  ao->cross_status = ECrossStatus::WaitToSendNew;
  ao->cxl_rej_reason = ECxlRejReason::EC_NoError;
  ao->create_at = ao->op_time;
  ao->update_at = ao->op_time;

  return ao;
}

store::Order::Ptr MakeLimitBuy(ESymbol symbol, EContractType contract_type, biz::price_x_t price, biz::size_x_t qty) {
  return CreateActiveOrder(symbol, contract_type, ESide::Buy, EOrderType::Limit, ETimeInForce::GoodTillCancel, 0, price,
                           qty);
}

store::Order::Ptr MakeLimitSell(ESymbol symbol, EContractType contract_type, biz::price_x_t price, biz::size_x_t qty) {
  return CreateActiveOrder(symbol, contract_type, ESide::Sell, EOrderType::Limit, ETimeInForce::GoodTillCancel, 0,
                           price, qty);
}

store::Order::Ptr MakeMarketBuy(ESymbol symbol, EContractType contract_type, biz::price_x_t price, biz::size_x_t qty) {
  return CreateActiveOrder(symbol, contract_type, ESide::Buy, EOrderType::Market, ETimeInForce::GoodTillCancel, 0,
                           price, qty);
}

store::Order::Ptr MakeMarketSell(ESymbol symbol, EContractType contract_type, biz::price_x_t price, biz::size_x_t qty) {
  return CreateActiveOrder(symbol, contract_type, ESide::Sell, EOrderType::Market, ETimeInForce::GoodTillCancel, 0,
                           price, qty);
}
