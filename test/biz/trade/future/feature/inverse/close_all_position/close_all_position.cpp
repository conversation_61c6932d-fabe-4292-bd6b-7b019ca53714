#include "test/biz/trade/future/feature/inverse/close_all_position/close_all_position.hpp"

#include <string>

#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/futures/modules/orderbiz/activeorderbiz/active_order_biz.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/future/func/draft_pkg_builder.h"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

int32_t InverseCloseAllPositionTest::Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t InverseCloseAllPositionTest::SwitchAccountMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}
int32_t InverseCloseAllPositionTest::OpenAccount(int64_t uid) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::MergeSettingPosition);

  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t InverseCloseAllPositionTest::SetSpotCollateralCoin(int64_t uid, ECoin coin) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SetSpotCollateralCoin);
  unified_v_2_request_dto->mutable_req_header()->set_req_id("set_collateral_coin_mode_1001_1");
  unified_v_2_request_dto->mutable_req_header()->set_coin(coin);
  auto* req =
      unified_v_2_request_dto->mutable_settingsreqgroupbody()->mutable_set_spot_collateral_coin()->add_coinlist();
  req->set_coin(coin);
  req->set_enable(true);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

/*
测试场景：逐仓全仓模式，用户挂反向订单，开仓，加仓，减仓，反向开仓，完全平仓
预期结果：挂单成交符合预期
*/
TEST_F(InverseCloseAllPositionTest, cross_mode) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto btcusd_symbol = ESymbol::BTCUSD;
  auto btc_coin = ECoin::BTC;

  EXPECT_EQ(Deposit(uid_1, "1", btc_coin), 0);
  EXPECT_EQ(0, OpenAccount(uid_1));
  EXPECT_EQ(0, SetSpotCollateralCoin(uid_1, static_cast<enums::ecoin::Coin>(btc_coin)));

  EXPECT_EQ(Deposit(uid_2, "1", btc_coin), 0);
  EXPECT_EQ(0, OpenAccount(uid_2));
  EXPECT_EQ(0, SetSpotCollateralCoin(uid_2, static_cast<enums::ecoin::Coin>(btc_coin)));

  te->AddMarkPrice(btcusd_symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 90000;
  biz::price_x_t price = 100000 * 1e4;

  auto order_id_btc = te->GenUUID();
  ESide side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id_btc, btcusd_symbol, pz_index, side, order_type, qty,
                                                        price, ETimeInForce::GoodTillCancel, uid_1, btc_coin);
  auto resp = user1.process(create_build_user1_sell.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_btc);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 90000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 90000);

  side = ESide::Buy;
  auto order_id_btc_2 = te->GenUUID();
  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id_btc_2, btcusd_symbol, pz_index, side, order_type, qty,
                                                       price, ETimeInForce::GoodTillCancel, uid_2, btc_coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_btc_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 90000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 90000);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_btc);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 90000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);

  auto btcusdt_symbol = ESymbol::BTCUSDT;
  auto usdt_coin = ECoin::USDT;
  side = ESide::Sell;
  te->AddMarkPrice(btcusdt_symbol, 100000, 100000, 100000);

  auto order_id_usdt = te->GenUUID();
  qty = 10000000;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_usdt(order_id_usdt, btcusdt_symbol, pz_index, side, order_type,
                                                             qty, price, ETimeInForce::GoodTillCancel, uid_1,
                                                             usdt_coin);
  resp = user1.process(create_build_user1_sell_usdt.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_usdt);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 10000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  auto order_id_usdt_2 = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user2_buy_usdt(order_id_usdt_2, btcusdt_symbol, pz_index, side, order_type,
                                                            qty, price, ETimeInForce::GoodTillCancel, uid_2, usdt_coin);
  resp = user2.process(create_build_user2_buy_usdt.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_usdt_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 10000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_usdt);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 10000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  auto price_x = std::make_shared<biz::FuturePriceData>();
  price_x->symbol_mark_price_map_->emplace(btcusd_symbol, bbase::decimal::Decimal<>(100000));
  price_x->updated_symbol_mark_price()->push_back(btcusd_symbol);
  price_x->symbol_index_price_map_->emplace(btcusd_symbol, bbase::decimal::Decimal<>(100000));
  price_x->updated_symbol_index_price()->push_back(btcusd_symbol);
  price_x->symbol_last_price_map_->emplace(btcusd_symbol, bbase::decimal::Decimal<>(100000));
  price_x->updated_symbol_last_price()->push_back(btcusd_symbol);

  price_x->symbol_mark_price_map_->emplace(btcusdt_symbol, bbase::decimal::Decimal<>(100000));
  price_x->updated_symbol_mark_price()->push_back(btcusdt_symbol);
  price_x->symbol_index_price_map_->emplace(btcusdt_symbol, bbase::decimal::Decimal<>(100000));
  price_x->updated_symbol_index_price()->push_back(btcusdt_symbol);
  price_x->symbol_last_price_map_->emplace(btcusdt_symbol, bbase::decimal::Decimal<>(100000));
  price_x->updated_symbol_last_price()->push_back(btcusdt_symbol);
  auto ev =
      std::make_shared<event::OnMarkPriceEvent>(0, event::EventType::kEventMarkPrice, event::EventSourceType::kUnknown,
                                                event::EventDispatchType::kBroadcastToWorker);
  ev->set_future_updated_symbols({btcusd_symbol, btcusdt_symbol});
  ev->set_future_price(price_x);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(ev, false);

  CloseAllPositionReqBuilder close_all_position(ESymbol::UNKNOWN, uid_1, ECoin::UNKNOWN);
  close_all_position.msg.mutable_close_all_position()->add_coin_list(static_cast<enums::ecoin::Coin>(btc_coin));
  close_all_position.msg.mutable_close_all_position()->add_coin_list(static_cast<enums::ecoin::Coin>(usdt_coin));
  resp = user1.process(close_all_position.Build());
  for (auto i = 0; i < 2; i++) {
    result = te->PopResult();
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  }
}

std::shared_ptr<tmock::CTradeAppMock> InverseCloseAllPositionTest::te;
