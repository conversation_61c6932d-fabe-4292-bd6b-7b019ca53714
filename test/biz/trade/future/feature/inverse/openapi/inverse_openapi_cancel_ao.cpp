#include "test/biz/trade/future/feature/inverse/openapi/inverse_openapi_cancel_ao.hpp"

#include <string>

#include "lib/msg_builder.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/futures/modules/orderbiz/activeorderbiz/active_order_biz.hpp"
#include "test/biz/trade/future/func/draft_pkg_builder.h"
#include "test/biz/trade/lib/stub.hpp"

int32_t InverseOpenApiCancelAoTest::OpenAccount(int64_t uid) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::MergeSettingPosition);

  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t InverseOpenApiCancelAoTest::SetSpotCollateralCoin(int64_t uid, ECoin coin) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SetSpotCollateralCoin);
  unified_v_2_request_dto->mutable_req_header()->set_req_id("set_collateral_coin_mode_1001_1");
  unified_v_2_request_dto->mutable_req_header()->set_coin(coin);
  auto* req =
      unified_v_2_request_dto->mutable_settingsreqgroupbody()->mutable_set_spot_collateral_coin()->add_coinlist();
  req->set_coin(coin);
  req->set_enable(true);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

TEST_F(InverseOpenApiCancelAoTest, cancel_order_open_v5) {
  biz::user_id_t uid = ********;
  stub user(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = ECoin::BTC;
    int wallet_record_type = 1;
    std::string amount = "5";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  EXPECT_EQ(0, OpenAccount(uid));
  EXPECT_EQ(0, SetSpotCollateralCoin(uid, static_cast<enums::ecoin::Coin>(ECoin::BTC)));

  biz::symbol_t symbol_id = ESymbol::BTCUSD;  // BTCUSD
  te->AddMarkPrice(static_cast<ESymbol>(symbol_id), 20000, 20000, 20000);

  auto order_link_id = te->GenUUID();
  OpenApiV5CreateOrderBuilder create_build("inverse", "BTCUSD", 0, "Sell", "Limit", "1", "20000");
  create_build.SetOrderLinkID(order_link_id);
  RpcContext ct{};
  auto resp = user.create_order(create_build.Build(), ct);
  std::cout << ct.DebugMetaData() << std::endl << "--------------------------" << std::endl;
  EXPECT_EQ(ct.RetCode(), error::kErrorCodeSuccess);
  EXPECT_EQ(ct.RetMsg(), "OK");
  EXPECT_NE(resp->order_id(), "");
  EXPECT_STREQ(resp->order_link_id().c_str(), order_link_id.c_str());

  auto result = te->PopResult();
  EXPECT_NE(result.m_msg.get(), nullptr);
  auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
  EXPECT_NE(orderCheck.m_msg, nullptr);
  orderCheck.Check(uid, 1);
  orderCheck.CheckPrice(uid, 20000 * 1e4);
  auto order_id = resp->order_id();

  // cancel by order id
  OpenApiV5CancelOrderBuilder cancel_build("inverse", "BTCUSD", order_id, "", "Order");
  RpcContext ct2{};
  auto cancel_resp = user.cancel_order(cancel_build.Build(), ct2);
  std::cout << ct2.DebugMetaData() << std::endl << "--------------------------" << std::endl;
  EXPECT_EQ(ct2.RetCode(), error::kErrorCodeSuccess);
  EXPECT_EQ(ct2.RetMsg(), "OK");
  result = te->PopResult();
  EXPECT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
  EXPECT_NE(orderCheck.m_msg, nullptr);

  order_link_id = te->GenUUID();
  OpenApiV5CreateOrderBuilder create_build2("inverse", "BTCUSD", 0, "Buy", "Limit", "1", "20000");
  create_build2.SetOrderLinkID(order_link_id);
  resp = user.create_order(create_build2.Build());
  EXPECT_NE(resp->order_id(), "");
  EXPECT_STREQ(resp->order_link_id().c_str(), order_link_id.c_str());
  result = te->PopResult();
  EXPECT_NE(result.m_msg.get(), nullptr);

  // cancel by order link id
  OpenApiV5CancelOrderBuilder cancel_build3("inverse", "BTCUSD", "", order_link_id, "Order");
  RpcContext ct3{};
  auto cancel_resp3 = user.cancel_order(cancel_build3.Build(), ct3);
  std::cout << ct3.DebugMetaData() << std::endl << "--------------------------" << std::endl;
  EXPECT_EQ(ct3.RetCode(), error::kErrorCodeSuccess);
  EXPECT_EQ(ct3.RetMsg(), "OK");
  result = te->PopResult();
  EXPECT_NE(result.m_msg.get(), nullptr);
  orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
  EXPECT_NE(orderCheck.m_msg, nullptr);
}

std::shared_ptr<tmock::CTradeAppMock> InverseOpenApiCancelAoTest::te;
