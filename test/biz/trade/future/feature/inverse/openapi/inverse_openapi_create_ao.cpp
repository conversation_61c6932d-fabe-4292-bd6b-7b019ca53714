#include "test/biz/trade/future/feature/inverse/openapi/inverse_openapi_create_ao.hpp"

#include <gtest/gtest.h>

#include <string>
#include <unordered_map>

#include "data/enum.hpp"
#include "data/type/biz_type.hpp"
#include "enums/eaccountmode/account_mode.pb.h"
#include "enums/ebizcode/biz_code.pb.h"
#include "enums/ecreatetype/create_type.pb.h"
#include "enums/ecrossstatus/cross_status.pb.h"
#include "enums/eorderstatus/order_status.pb.h"
#include "enums/etimeinforce/time_in_force.pb.h"
#include "lib/msg_builder.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "src/data/error/error.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

void InverseOpenApiCreateAoTest::BuildProduceMsg(biz::cross_idx_t cross_idx, biz::symbol_t symbol,
                                                 std::int64_t exec_price, std::int64_t exec_time, std::int64_t fee_rate,
                                                 biz::cross::xReqSender& sender, bbase::hdts::Producer::Message& msg) {
  biz::CrossError err;
  /// 组装topic
  msg.topic = fmt::format("request_of_{}", cross_idx);

  /// 组装buf
  msg.value.resize(sizeof(biz::cross::RawXHeader) + sizeof(biz::cross::request::RawXRequest));
  memcpy(msg.value.data(), &sender.req_mgr_.header_obj_->header_, sizeof(biz::cross::RawXHeader));
  sender.req_mgr_.request_obj_->ToBinaryBuf(static_cast<char*>(msg.value.data()) + sizeof(biz::cross::RawXHeader));

  /// 处理透传数据 passthrough
  ::models::passthroughdto::PassThroughDTO passthrough;

  auto funding_req = passthrough.mutable_funding_fee();
  funding_req->set_symbol(static_cast<ESymbol>(symbol));
  funding_req->set_exec_price_x(exec_price);
  funding_req->set_exec_time_e9(exec_time);
  funding_req->set_fee_rate_e8(fee_rate);
  auto uuid = boost::uuids::to_string(boost::uuids::random_generator()());
  funding_req->set_exec_id(uuid.data());
  auto value = passthrough.SerializeAsString();
  msg.headers[biz::XComm::x_pass_through] = value;
}

int32_t InverseOpenApiCreateAoTest::SwitchMarginMode(stub& user, enums::eaccountmode::AccountMode account_mode) {
  // stub user(PmTest::te, uid);
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(user.m_uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(enums::eaction::Action::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(account_mode);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(user.m_uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }
  return 0;
}

int32_t InverseOpenApiCreateAoTest::OpenAccount(int64_t uid) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::MergeSettingPosition);

  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t InverseOpenApiCreateAoTest::SetSpotCollateralCoin(int64_t uid, ECoin coin) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SetSpotCollateralCoin);
  unified_v_2_request_dto->mutable_req_header()->set_req_id("set_collateral_coin_mode_1001_1");
  unified_v_2_request_dto->mutable_req_header()->set_coin(coin);
  auto* req =
      unified_v_2_request_dto->mutable_settingsreqgroupbody()->mutable_set_spot_collateral_coin()->add_coinlist();
  req->set_coin(coin);
  req->set_enable(true);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

TEST_F(InverseOpenApiCreateAoTest, create_order_open_v5) {
  biz::user_id_t uid = *********;
  stub user(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = ECoin::BTC;
    int wallet_record_type = 1;
    std::string amount = "5";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  EXPECT_EQ(0, OpenAccount(uid));
  EXPECT_EQ(0, SetSpotCollateralCoin(uid, static_cast<enums::ecoin::Coin>(ECoin::BTC)));

  biz::symbol_t symbol_id = ESymbol::BTCUSD;  // BTCUSD
  te->AddMarkPrice(static_cast<ESymbol>(symbol_id), 20000, 20000, 20000);

  {
    auto fake_order_link_id = te->GenUUID() + te->GenUUID();
    OpenApiV5CreateOrderBuilder build("inverse", "BTCUSD", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(fake_order_link_id);
    RpcContext ct{};
    auto resp = user.create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), error::kErrorCodeParamsError);
    ASSERT_EQ(resp->order_id(), "");
    ASSERT_EQ(resp->order_link_id(), "");
  }

  {
    auto order_id = te->GenUUID();
    OpenApiV5CreateOrderBuilder build("inverse", "BTCUSD", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(order_id);
    RpcContext ct{};
    auto resp = user.create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetMsg(), "OK");
    ASSERT_EQ(ct.RetCode(), error::kErrorCodeSuccess);
    ASSERT_NE(resp->order_id(), "");
    ASSERT_STREQ(resp->order_link_id().c_str(), order_id.c_str());

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(uid, 1);
    orderCheck.CheckPrice(uid, 20000 * 1e4);
  }

  // qty数量不对校验
  {
    auto order_id = te->GenUUID();
    OpenApiV5CreateOrderBuilder build("inverse", "BTCUSD", 0, "Sell", "Limit", "1.5", "20000");
    build.SetOrderLinkID(order_id);
    RpcContext ct{};
    auto resp = user.create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), error::kErrorCodeParamsError);
    std::cout << resp->DebugString() << std::endl << "--------------------------" << std::endl;
    auto result = te->PopResult(100);
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    auto order_id = te->GenUUID();
    OpenApiV5CreateOrderBuilder build("inverse", "BTCUSD", 0, "Sell", "Limit", "0.00000000001", "20000");
    build.SetOrderLinkID(order_id);
    RpcContext ct{};
    auto resp = user.create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), error::kErrorCodeParamsError);
    std::cout << resp->DebugString() << std::endl << "--------------------------" << std::endl;
    auto result = te->PopResult(100);
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    auto order_id = te->GenUUID();
    OpenApiV5CreateOrderBuilder build("inverse", "BTCUSD", 0, "Sell", "Limit", "-0.0001", "20000");
    build.SetOrderLinkID(order_id);
    RpcContext ct{};
    auto resp = user.create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), error::kErrorCodeParamsError);
    std::cout << resp->DebugString() << std::endl << "--------------------------" << std::endl;
    auto result = te->PopResult(100);
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    auto order_id = te->GenUUID();
    OpenApiV5CreateOrderBuilder build("inverse", "BTCUSD", 0, "Sell", "Limit", "1.0001", "20000");
    build.SetOrderLinkID(order_id);
    RpcContext ct{};
    auto resp = user.create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), error::kErrorCodeParamsError);
    std::cout << resp->DebugString() << std::endl << "--------------------------" << std::endl;
    auto result = te->PopResult(100);
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    auto order_id = te->GenUUID();
    OpenApiV5CreateOrderBuilder build("inverse", "BTCUSD", 0, "Sell", "Limit", "10", "20000");
    build.SetOrderLinkID(order_id);
    RpcContext ct{};
    auto resp = user.create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), error::kErrorCodeSuccess);
    std::cout << resp->DebugString() << std::endl << "--------------------------" << std::endl;
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::cout << result.m_msg->DebugString() << std::endl << "--------------------------" << std::endl;
  }
}

std::shared_ptr<tmock::CTradeAppMock> InverseOpenApiCreateAoTest::te;
