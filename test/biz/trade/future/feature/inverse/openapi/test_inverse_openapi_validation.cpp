//
// <NAME_EMAIL> on 25/8/2023.
//

#include "test/biz/trade/future/feature/inverse/openapi/test_inverse_openapi_validation.hpp"

#include <gtest/gtest.h>

#include <string>
#include <unordered_map>

#include "lib/stub.hpp"
#include "src/biz_worker/service/trade/futures/modules/commonbiz/validation_biz.hpp"
#include "src/grpc_worker/event/event.hpp"

std::shared_ptr<tmock::CTradeAppMock> TestInverseOpenApiValidationForSettle::te;

TEST_F(TestInverseOpenApiValidationForSettle, OpenApiV5ConvertAndCheckForCreate) {
  // SiteAPI 请求
  svc::trading::req::CreateOrderReq create_order_req;

  // OpenAPI 请求
  auto v5_req = std::make_shared<svc::uta_engine::req::CreateOrderReqV5>();
  v5_req->set_category("inverse");
  v5_req->set_symbol("BTCUSDZ20");

  auto ret =
      biz::commonbiz::ValidationBiz::OpenApiV5ConvertAndCheckForCreate(draft_pkg.get(), {}, &create_order_req, v5_req);

  EXPECT_EQ(ret, error::kErrorCodeSymbolNotInTradingStatus);
}

TEST_F(TestInverseOpenApiValidationForSettle, OpenApiV5CheckForCancel) {
  // OpenAPI 请求
  auto v5_req = std::make_shared<svc::uta_engine::req::CancelOrderReqV5>();
  v5_req->set_category("inverse");
  v5_req->set_symbol("BTCUSDZ20");
  v5_req->set_order_link_id("1111");

  svc::trading::req::CancelOrderReq cancel_order_req;
  auto ret =
      biz::commonbiz::ValidationBiz::OpenApiV5ConvertAndCheckForCancel(draft_pkg.get(), &cancel_order_req, v5_req);
  std::cout << "error: " << draft_pkg->GetLastError().ToString() << "\n----------------------\n" << std::endl;
  // 因为撤单没有symbol是否过期检查，所以是success
  EXPECT_EQ(ret, error::kErrorCodeSuccess);
}

TEST_F(TestInverseOpenApiValidationForSettle, OpenApiV5ConvertAndCheckForReplace) {
  // SiteAPI 请求
  svc::trading::req::ReplaceOrderReq replace_order_req;

  // OpenAPI 请求
  auto v5_req = std::make_shared<svc::uta_engine::req::ReplaceOrderReqV5>();
  v5_req->set_category("inverse");
  v5_req->set_symbol("BTCUSDZ20");

  auto ret = biz::commonbiz::ValidationBiz::OpenApiV5ConvertAndCheckForReplace(draft_pkg.get(), {}, &replace_order_req,
                                                                               v5_req);
  std::cout << "error: " << draft_pkg->GetLastError().ToString() << "\n----------------------\n" << std::endl;

  EXPECT_EQ(ret, error::kErrorCodeSymbolNotInTradingStatus);
}

TEST_F(TestInverseOpenApiValidationForSettle, OpenApiV5ConvertAndCheckForAddMargin) {
  // OpenAPI 请求
  auto v5_req = std::make_shared<svc::uta_engine::req::AddMarginRequestV5>();
  v5_req->set_category("inverse");
  v5_req->set_symbol("BTCUSDZ20");
  biz::value_e8_t new_add_margin_e8 = 100000000;
  auto ret = biz::commonbiz::ValidationBiz::OpenApiV5ConvertAndCheckForAddMargin(draft_pkg.get(), v5_req.get(),
                                                                                 new_add_margin_e8);
  std::cout << "error: " << draft_pkg->GetLastError().ToString() << "\n----------------------\n" << std::endl;

  EXPECT_EQ(ret, error::kErrorCodeSymbolNotInTradingStatus);
}

TEST_F(TestInverseOpenApiValidationForSettle, OpenApiV5ConvertAndCheckForSetAutoAddMargin) {
  // OpenAPI 请求
  auto v5_req = std::make_shared<svc::uta_engine::req::SetAutoAddMarginRequestV5>();
  v5_req->set_category("inverse");
  v5_req->set_symbol("BTCUSDZ20");

  biz::symbol_t symbol{};
  EPositionIndex position_index{0};
  bool new_auto_add_margin{true};

  auto ret = biz::commonbiz::ValidationBiz::OpenApiV5ConvertAndCheckForSetAutoAddMargin(
      draft_pkg.get(), v5_req.get(), symbol, position_index, new_auto_add_margin);
  std::cout << "error: " << draft_pkg->GetLastError().ToString() << "\n----------------------\n" << std::endl;

  EXPECT_EQ(ret, error::kErrorCodeSymbolNotInTradingStatus);
}

TEST_F(TestInverseOpenApiValidationForSettle, OpenApiV5ConvertAndCheckForSetLeverage) {
  // OpenAPI 请求
  auto v5_req = std::make_shared<svc::uta_engine::req::SetLeverageReqV5>();
  v5_req->set_category("inverse");
  v5_req->set_symbol("BTCUSDZ20");

  biz::symbol_t symbol{};
  biz::leverage_e2_t new_buy_leverage_e2{100};
  biz::leverage_e2_t new_sell_leverage_e2{100};

  auto ret = biz::commonbiz::ValidationBiz::OpenApiV5ConvertAndCheckForSetLeverage(
      draft_pkg.get(), v5_req.get(), symbol, new_buy_leverage_e2, new_sell_leverage_e2);
  std::cout << "error: " << draft_pkg->GetLastError().ToString() << "\n----------------------\n" << std::endl;

  EXPECT_EQ(ret, error::kErrorCodeSymbolNotInTradingStatus);
}

TEST_F(TestInverseOpenApiValidationForSettle, OpenApiV5ConvertAndCheckForSetRiskId) {
  // OpenAPI 请求
  auto v5_req = std::make_shared<svc::uta_engine::req::SetRiskIdReqV5>();
  v5_req->set_category("inverse");
  v5_req->set_symbol("BTCUSDZ20");

  biz::symbol_t symbol{};
  EPositionIndex position_index{0};
  int64_t new_risk_id{2};

  auto ret = biz::commonbiz::ValidationBiz::OpenApiV5ConvertAndCheckForSetRiskId(draft_pkg.get(), v5_req.get(), symbol,
                                                                                 position_index, new_risk_id);
  std::cout << "error: " << draft_pkg->GetLastError().ToString() << "\n----------------------\n" << std::endl;

  EXPECT_EQ(ret, error::kErrorCodeSymbolNotInTradingStatus);
}

TEST_F(TestInverseOpenApiValidationForSettle, OpenApiV5ConvertAndCheckForSetTpSlTs) {
  // SiteAPI 请求
  svc::trading::req::SetTpSlTsReq tp_sl_ts_req;

  // OpenAPI 请求
  auto v5_req = std::make_shared<svc::uta_engine::req::SetTpSlTsReqV5>();
  v5_req->set_category("inverse");
  v5_req->set_symbol("BTCUSDZ20");

  auto ret =
      biz::commonbiz::ValidationBiz::OpenApiV5ConvertAndCheckForSetTpSlTs(draft_pkg.get(), &tp_sl_ts_req, v5_req);
  std::cout << "error: " << draft_pkg->GetLastError().ToString() << "\n----------------------\n" << std::endl;

  EXPECT_EQ(ret, error::kErrorCodeSymbolNotInTradingStatus);
}

TEST_F(TestInverseOpenApiValidationForSettle, OpenApiV5ConvertAndCheckForSetTpSlMode) {
  // SiteAPI 请求
  svc::trading::req::SwitchTpSlModeReq tp_sl_mode_req{};

  // OpenAPI 请求
  auto v5_req = std::make_shared<svc::uta_engine::req::SetTpSlModeReqV5>();
  v5_req->set_category("inverse");
  v5_req->set_symbol("BTCUSDZ20");

  auto ret =
      biz::commonbiz::ValidationBiz::OpenApiV5ConvertAndCheckForSetTpSlMode(draft_pkg.get(), &tp_sl_mode_req, v5_req);
  std::cout << "error: " << draft_pkg->GetLastError().ToString() << "\n----------------------\n" << std::endl;

  EXPECT_EQ(ret, error::kErrorCodeSymbolNotInTradingStatus);
}

TEST_F(TestInverseOpenApiValidationForSettle, OpenApiV5ConvertAndCheckForCancelAll) {
  std::unordered_map<ECancelAllType, bool> cancel_all_type_map;

  // SiteAPI 请求
  svc::trading::req::CancelAllReq cancel_all_req;

  // OpenAPI 请求
  auto v5_req = std::make_shared<svc::uta_engine::req::CancelAllOrderReqV5>();
  v5_req->set_category("inverse");
  v5_req->set_symbol("BTCUSDZ20");
  v5_req->set_settle_coin("BTC");
  auto settle_coin_p = std::make_shared<biz::coin_t>();

  event::GrpcContext grpc_context;

  auto ret = biz::commonbiz::ValidationBiz::OpenApiV5ConvertAndCheckForCancelAll(
      draft_pkg.get(), v5_req, cancel_all_type_map, grpc_context, &cancel_all_req, settle_coin_p.get());
  std::cout << "error: " << draft_pkg->GetLastError().ToString() << "\n----------------------\n" << std::endl;

  EXPECT_EQ(ret, error::kErrorCodeSymbolNotInTradingStatus);
}
