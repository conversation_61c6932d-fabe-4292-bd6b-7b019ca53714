#pragma once

#include <gtest/gtest.h>

#include <iostream>
#include <memory>
#include <string>
#include <vector>

#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class InverseMmAccumulationTest : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestSuite() {}
  static void TearDownTestSuite() {}
  static int32_t Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin = 5);
  static int32_t SwitchAccountMode(biz::user_id_t uid, EAccountMode mode);
  static int32_t SwitchPositionMode(biz::user_id_t uid, EPositionMode mode);
  static std::shared_ptr<tmock::CTradeAppMock> te;
};
