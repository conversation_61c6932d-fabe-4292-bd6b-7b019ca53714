#include "test/biz/trade/future/feature/inverse/tpsl/one_cancel_other_test.hpp"

#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>

#include "data/error/error.hpp"
#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "lib/stub.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "src/data/enum.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"
std::shared_ptr<tmock::CTradeAppMock> iOneCancelOtherTest::te;
int32_t iOneCancelOtherTest::Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t iOneCancelOtherTest::SwitchAccountMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t iOneCancelOtherTest::OpenAccount(int64_t uid) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::MergeSettingPosition);

  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t iOneCancelOtherTest::SetSpotCollateralCoin(int64_t uid, ECoin coin) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SetSpotCollateralCoin);
  unified_v_2_request_dto->mutable_req_header()->set_req_id("set_collateral_coin_mode_1001_1");
  unified_v_2_request_dto->mutable_req_header()->set_coin(coin);
  auto* req =
      unified_v_2_request_dto->mutable_settingsreqgroupbody()->mutable_set_spot_collateral_coin()->add_coinlist();
  req->set_coin(coin);
  req->set_enable(true);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

// 携带partial活动限价tpsl，成交后生成tpsl订单成功
TEST_F(iOneCancelOtherTest, LimitPartialTpSl) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  11100'0000, 9800'0000, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 9800'0000);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 11100'0000);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  {
    auto underlying_price = bbase::decimal::Decimal<>("10010");
    auto exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);

      auto ao_result = te->PopResult();
      if (ao_result.m_msg == nullptr) {
        ++try_times;
        continue;
      }
      std::cout << "active ao result:" << std::endl << ao_result.m_msg->DebugString() << std::endl;
      ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 2);
      for (auto& order : ao_result.RefFutureMarginResult().m_msg->related_orders()) {
        TransactDTOChecker orderCheck(&order);
        auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
        if (stop_order_type == EStopOrderType::PartialStopLoss) {
          EXPECT_EQ(order.order_status(), EOrderStatus::Deactivated);
          EXPECT_EQ(order.cross_status(), ECrossStatus::Deactivated);
          EXPECT_EQ(order.cancel_type(), ECancelType::CancelByOCOSlCanceledByTpTriggered);
          EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialStopLoss);
        } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
          EXPECT_EQ(order.order_status(), EOrderStatus::New);
          EXPECT_EQ(order.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);
        } else {
          EXPECT_TRUE(false);
        }
      }

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

// 携带市价partial tpsl，成交后生成tpsl成功
TEST_F(iOneCancelOtherTest, MarketPartialTpSl) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, {}, {}, {}, {}, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Market);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Market);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Market, 100);
        EXPECT_EQ(order.price_x(), 0);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Market, 100);
        EXPECT_EQ(order.price_x(), 0);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  {
    auto underlying_price = bbase::decimal::Decimal<>("10010");
    auto exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);

      auto ao_result = te->PopResult();
      if (ao_result.m_msg == nullptr) {
        ++try_times;
        continue;
      }
      std::cout << "active ao result:" << std::endl << ao_result.m_msg->DebugString() << std::endl;
      ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 3);
      // auto ao = ao_result.m_msg->futures_margin_result().related_orders().at(0);
      // // 活动单触发
      // EXPECT_EQ(ao.order_status(), EOrderStatus::Deactivated);
      // EXPECT_EQ(ao.cross_status(), ECrossStatus::Deactivated);
      // EXPECT_EQ(ao.cancel_type(), ECancelType::CancelByOCOSlCanceledByTpTriggered);
      // EXPECT_EQ(ao.stop_order_type(), EStopOrderType::PartialStopLoss);

      // ao = ao_result.m_msg->futures_margin_result().related_orders().at(1);

      // // 触发后是市价单，无法成交直接cancel
      // EXPECT_EQ(ao.order_status(), EOrderStatus::Cancelled);
      // EXPECT_EQ(ao.cross_status(), ECrossStatus::Canceled);
      // EXPECT_EQ(ao.stop_order_type(), EStopOrderType::PartialTakeProfit);

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

// 携带full市价tpsl，成交后生成tpsl订单成功
TEST_F(iOneCancelOtherTest, FullTpSl) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Full, {}, {}, {}, {}, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Full);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 0);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Market);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Market);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 9900'0000, EStopOrderType::StopLoss,
                                  EOrderType::Market, 100);
        EXPECT_EQ(order.price_x(), 0);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 10010'0000, EStopOrderType::TakeProfit,
                                  EOrderType::Market, 100);
        EXPECT_EQ(order.price_x(), 0);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 触发
  {
    auto underlying_price = bbase::decimal::Decimal<>("10010");
    auto exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);

      auto ao_result = te->PopResult();
      if (ao_result.m_msg == nullptr) {
        ++try_times;
        continue;
      }
      std::cout << "active ao result:" << std::endl << ao_result.m_msg->DebugString() << std::endl;
      EXPECT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 3);
      // 活动单触发
      for (auto& ao : ao_result.RefFutureMarginResult().m_msg->related_orders()) {
        auto stop_order_type = static_cast<EStopOrderType>(ao.stop_order_type());
        if (stop_order_type == EStopOrderType::StopLoss) {
          EXPECT_EQ(ao.order_status(), EOrderStatus::Deactivated);
          EXPECT_EQ(ao.cross_status(), ECrossStatus::Deactivated);
          EXPECT_EQ(ao.cancel_type(), ECancelType::CancelByOCOSlCanceledByTpTriggered);
          EXPECT_EQ(ao.stop_order_type(), EStopOrderType::StopLoss);
        } else if (stop_order_type == EStopOrderType::TakeProfit) {
          if (ao.order_status() == EOrderStatus::Triggered) {
            EXPECT_EQ(ao.stop_order_type(), EStopOrderType::TakeProfit);
          } else if (ao.order_status() == EOrderStatus::Rejected) {
            EXPECT_EQ(ao.stop_order_type(), EStopOrderType::TakeProfit);
          }
        } else {
          EXPECT_TRUE(false);
        }
      }

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

// 取消partial sl的同时取消partial tp
TEST_F(iOneCancelOtherTest, CancelslCancelOtherTp) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  11100'0000, 9800'0000, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  std::string to_cancel_order_id{};
  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 9800'0000);
        to_cancel_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 11100'0000);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 取消sl，tp同时取消
  {
    FutureOneOfCancelOrderBuilder cancel_builder(to_cancel_order_id, symbol, user_id_test, coin);
    auto resp_test = user_test.process(cancel_builder.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    EXPECT_EQ(result_test.RefFutureMarginResult().m_msg->related_orders().size(), 2);
    for (auto& order : result_test.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        EXPECT_EQ(order.order_status(), EOrderStatus::Deactivated);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Deactivated);
        EXPECT_EQ(order.cancel_type(), ECancelType::CancelByUser);
        EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialStopLoss);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        EXPECT_EQ(order.order_status(), EOrderStatus::Deactivated);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Deactivated);
        EXPECT_EQ(order.cancel_type(), ECancelType::CancelByUser);
        EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);

      } else {
        EXPECT_TRUE(false);
      }
    }
  }
}

TEST_F(iOneCancelOtherTest, CancelTpCancelOtherSl) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  11100'0000, 9800'0000, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  std::string to_cancel_order_id{};
  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 9800'0000);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 11100'0000);
        to_cancel_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 取消tp，sl同时取消
  {
    FutureOneOfCancelOrderBuilder cancel_builder(to_cancel_order_id, symbol, user_id_test, coin);
    auto resp_test = user_test.process(cancel_builder.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    EXPECT_EQ(result_test.RefFutureMarginResult().m_msg->related_orders().size(), 2);
    for (auto& order : result_test.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        EXPECT_EQ(order.order_status(), EOrderStatus::Deactivated);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Deactivated);
        EXPECT_EQ(order.cancel_type(), ECancelType::CancelByUser);
        EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialStopLoss);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        EXPECT_EQ(order.order_status(), EOrderStatus::Deactivated);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Deactivated);
        EXPECT_EQ(order.cancel_type(), ECancelType::CancelByUser);
        EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
}

// 修改partial tp的qty同时修改partial sl的qty
TEST_F(iOneCancelOtherTest, ReplaceTpQtyCancelOtherSlQty) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  11100'0000, 9800'0000, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  std::string to_replace_order_id{};
  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 9800'0000);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 11100'0000);
        to_replace_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 修改tp订单的qty为60，sl的也自动改到60
  {
    FutureOneOfReplaceOrderBuilder replace_builder(to_replace_order_id, symbol, 60, {}, user_id_test, coin);
    replace_builder.msg.mutable_replace_order()->set_need_generate_pid(true);
    auto resp_test = user_test.process(replace_builder.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    EXPECT_EQ(result_test.RefFutureMarginResult().m_msg->related_orders().size(), 2);
    for (auto& order : result_test.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        EXPECT_EQ(order.order_status(), EOrderStatus::Untriggered);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
        EXPECT_EQ(order.cancel_type(), ECancelType::UNKNOWN);
        EXPECT_EQ(order.qty_x(), 60);
        EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialStopLoss);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        EXPECT_EQ(order.order_status(), EOrderStatus::Untriggered);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
        EXPECT_EQ(order.cancel_type(), ECancelType::UNKNOWN);
        EXPECT_EQ(order.qty_x(), 60);
        EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
}

// 修改partial sl的qty同时修改partial tp的qty
TEST_F(iOneCancelOtherTest, ReplaceSlQtyCancelOtherTpQty) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  11100'0000, 9800'0000, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  std::string to_replace_order_id{};
  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 9800'0000);
        to_replace_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 11100'0000);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 修改sl订单的qty为60，tp的也自动改到60
  {
    FutureOneOfReplaceOrderBuilder replace_builder(to_replace_order_id, symbol, 60, {}, user_id_test, coin);
    replace_builder.msg.mutable_replace_order()->set_need_generate_pid(true);
    auto resp_test = user_test.process(replace_builder.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    EXPECT_EQ(result_test.RefFutureMarginResult().m_msg->related_orders().size(), 2);

    for (auto& order : result_test.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        EXPECT_EQ(order.order_status(), EOrderStatus::Untriggered);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
        EXPECT_EQ(order.cancel_type(), ECancelType::UNKNOWN);
        EXPECT_EQ(order.qty_x(), 60);
        EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialStopLoss);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        EXPECT_EQ(order.order_status(), EOrderStatus::Untriggered);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
        EXPECT_EQ(order.cancel_type(), ECancelType::UNKNOWN);
        EXPECT_EQ(order.qty_x(), 60);
        EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
}

// parital fill的订单携带tpsl参数，则这些tpsl参数不能修改
TEST_F(iOneCancelOtherTest, PartialFillTpsl) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  11100'0000, 9800'0000, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 50;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  std::string to_replace_order_id = buy_order_id;
  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Limit, 50);
        EXPECT_EQ(order.price_x(), 9800'0000);
        // to_replace_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Limit, 50);
        EXPECT_EQ(order.price_x(), 11100'0000);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
  // parital fill的订单携带tpsl参数，则这些tpsl参数不能修改
  {
    FutureOneOfReplaceOrderBuilder replace_builder(to_replace_order_id, symbol, {}, {}, user_id_test, coin, {}, {}, {},
                                                   {}, 10020'0000, {}, 9890'0000);
    auto resp_test = user_test.process(replace_builder.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeCanNotModifyPartiallyFilledOrderTpSl);
  }
}

// 使用settpsl修改tp订单的qty为60，sl的也自动改到60
TEST_F(iOneCancelOtherTest, ReplaceTpReplaceOtherSl_set) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  11100'0000, 9800'0000, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  std::string to_replace_order_id{};
  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 9800'0000);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 11100'0000);
        to_replace_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 使用settpsl修改tp订单的qty为60，sl的也自动改到60
  {
    FutureOneOfSetTpSlTsBuilder settpsl_builder;
    auto& msg = settpsl_builder.msg;
    auto header = msg.mutable_req_header();
    header->set_coin(ECoin(coin));
    header->set_symbol_id(symbol);
    header->set_user_id(user_id_test);
    auto req = msg.mutable_set_tp_sl_ts();
    req->set_symbol(ESymbol(symbol));
    req->set_tp_sl_mode(ETpSlMode::Partial);
    req->set_tp_stop_order_id(to_replace_order_id);
    req->set_new_take_profit_x(10010'0000);
    req->set_position_idx(pz_index);
    req->set_new_tp_size_x(60);
    req->set_need_generate_pid(true);
    auto resp_test = user_test.process(settpsl_builder.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    EXPECT_EQ(result_test.RefFutureMarginResult().m_msg->related_orders().size(), 2);

    for (auto& order : result_test.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        EXPECT_EQ(order.order_status(), EOrderStatus::Untriggered);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
        EXPECT_EQ(order.cancel_type(), ECancelType::UNKNOWN);
        EXPECT_EQ(order.qty_x(), 60);
        EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialStopLoss);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        EXPECT_EQ(order.order_status(), EOrderStatus::Untriggered);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
        EXPECT_EQ(order.cancel_type(), ECancelType::UNKNOWN);
        EXPECT_EQ(order.qty_x(), 60);
        EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
}

// 使用settpsl修改sl订单的qty为60，tp的也自动改到60
TEST_F(iOneCancelOtherTest, ReplaceSlReplaceOtherTp_set) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  11100'0000, 9800'0000, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  std::string to_replace_order_id{};
  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 9800'0000);
        to_replace_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 11100'0000);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 使用settpsl修改sl订单的qty为60，tp的也自动改到60
  {
    FutureOneOfSetTpSlTsBuilder settpsl_builder;
    auto& msg = settpsl_builder.msg;
    auto header = msg.mutable_req_header();
    header->set_coin(ECoin(coin));
    header->set_symbol_id(symbol);
    header->set_user_id(user_id_test);
    auto req = msg.mutable_set_tp_sl_ts();
    req->set_symbol(ESymbol(symbol));
    req->set_tp_sl_mode(ETpSlMode::Partial);
    req->set_sl_stop_order_id(to_replace_order_id);
    req->set_new_stop_loss_x(9900'0000);
    req->set_position_idx(pz_index);
    req->set_new_sl_size_x(60);
    req->set_need_generate_pid(true);
    auto resp_test = user_test.process(settpsl_builder.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    EXPECT_EQ(result_test.RefFutureMarginResult().m_msg->related_orders().size(), 2);
    for (auto& order : result_test.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        EXPECT_EQ(order.order_status(), EOrderStatus::Untriggered);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
        EXPECT_EQ(order.cancel_type(), ECancelType::UNKNOWN);
        EXPECT_EQ(order.qty_x(), 60);
        EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialStopLoss);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        EXPECT_EQ(order.order_status(), EOrderStatus::Untriggered);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
        EXPECT_EQ(order.cancel_type(), ECancelType::UNKNOWN);
        EXPECT_EQ(order.qty_x(), 60);
        EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
}

// 使用settpsl请求取消tp，对应绑定的sl不会取消
TEST_F(iOneCancelOtherTest, CancelTpCancelOtherSl_set) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  11100'0000, 9800'0000, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  std::string to_replace_order_id{};
  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 9800'0000);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 11100'0000);
        to_replace_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 使用settpsl取消tp，sl不自动取消
  {
    FutureOneOfSetTpSlTsBuilder settpsl_builder;
    auto& msg = settpsl_builder.msg;
    auto header = msg.mutable_req_header();
    header->set_coin(ECoin(coin));
    header->set_symbol_id(symbol);
    header->set_user_id(user_id_test);
    auto req = msg.mutable_set_tp_sl_ts();
    req->set_symbol(ESymbol(symbol));
    req->set_tp_sl_mode(ETpSlMode::Partial);
    req->set_tp_stop_order_id(to_replace_order_id);
    req->set_new_take_profit_x(0);
    auto resp_test = user_test.process(settpsl_builder.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    EXPECT_EQ(result_test.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    auto tp = result_test.RefFutureMarginResult().m_msg->related_orders().at(0);
    EXPECT_EQ(tp.order_status(), EOrderStatus::Deactivated);
    EXPECT_EQ(tp.cross_status(), ECrossStatus::Deactivated);
    EXPECT_EQ(tp.cancel_type(), ECancelType::CancelByUser);
    EXPECT_EQ(tp.qty_x(), 100);
    EXPECT_EQ(tp.stop_order_type(), EStopOrderType::PartialTakeProfit);
  }
}

// 使用settpsl请求取消sl，对应绑定的tp不会取消
TEST_F(iOneCancelOtherTest, CancelSlCancelOtherTp_set) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  11100'0000, 9800'0000, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  std::string to_replace_order_id{};
  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 9800'0000);
        to_replace_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 11100'0000);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 使用settpsl取消sl，tp不自动取消
  {
    FutureOneOfSetTpSlTsBuilder settpsl_builder;
    auto& msg = settpsl_builder.msg;
    auto header = msg.mutable_req_header();
    header->set_coin(ECoin(coin));
    header->set_symbol_id(symbol);
    header->set_user_id(user_id_test);
    auto req = msg.mutable_set_tp_sl_ts();
    req->set_symbol(ESymbol(symbol));
    req->set_tp_sl_mode(ETpSlMode::Partial);
    req->set_sl_stop_order_id(to_replace_order_id);
    req->set_new_stop_loss_x(0);
    auto resp_test = user_test.process(settpsl_builder.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    EXPECT_EQ(result_test.RefFutureMarginResult().m_msg->related_orders().size(), 1);
    auto sl = result_test.RefFutureMarginResult().m_msg->related_orders().at(0);
    EXPECT_EQ(sl.order_status(), EOrderStatus::Deactivated);
    EXPECT_EQ(sl.cross_status(), ECrossStatus::Deactivated);
    EXPECT_EQ(sl.cancel_type(), ECancelType::CancelByUser);
    EXPECT_EQ(sl.qty_x(), 100);
    EXPECT_EQ(sl.stop_order_type(), EStopOrderType::PartialStopLoss);
  }
}

// 使用settpsl请求同时取消tpsl
TEST_F(iOneCancelOtherTest, CancelBothTpSl) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  11100'0000, 9800'0000, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  std::string tp_replace_order_id{};
  std::string sl_replace_order_id{};
  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 9800'0000);
        sl_replace_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 11100'0000);
        tp_replace_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 使用settpsl同时取消tp和sl
  {
    FutureOneOfSetTpSlTsBuilder settpsl_builder;
    auto& msg = settpsl_builder.msg;
    auto header = msg.mutable_req_header();
    header->set_coin(ECoin(coin));
    header->set_symbol_id(symbol);
    header->set_user_id(user_id_test);
    auto req = msg.mutable_set_tp_sl_ts();
    req->set_symbol(ESymbol(symbol));
    req->set_tp_sl_mode(ETpSlMode::Partial);
    req->set_sl_stop_order_id(sl_replace_order_id);
    req->set_tp_stop_order_id(tp_replace_order_id);
    req->set_new_stop_loss_x(0);
    req->set_new_take_profit_x(0);
    auto resp_test = user_test.process(settpsl_builder.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    EXPECT_EQ(result_test.RefFutureMarginResult().m_msg->related_orders().size(), 2);
    for (auto& order : result_test.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        EXPECT_EQ(order.order_status(), EOrderStatus::Deactivated);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Deactivated);
        EXPECT_EQ(order.cancel_type(), ECancelType::CancelByUser);
        EXPECT_EQ(order.qty_x(), 100);
        EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialStopLoss);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        EXPECT_EQ(order.order_status(), EOrderStatus::Deactivated);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Deactivated);
        EXPECT_EQ(order.cancel_type(), ECancelType::CancelByUser);
        EXPECT_EQ(order.qty_x(), 100);
        EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
}

// 触发失败，绑定的订单取消
TEST_F(iOneCancelOtherTest, LimitPartialTpsl_TriggerFail) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  11100'0000, 9800'0000, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  std::string tp_replace_order_id{};
  std::string sl_replace_order_id{};
  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 9800'0000);
        sl_replace_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 11100'0000);
        tp_replace_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  {
    // 下reduceonly订单占住ob
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price, {},
                                               user_id_test, coin, {}, {}, true);
    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->price_x(), price);
    EXPECT_EQ(orderCheck.m_msg->qty_x(), qty);
    EXPECT_EQ(orderCheck.m_msg->exec_inst(), 1);
  }

  // 触发tp但是因reduceonly失败，这时tp和绑定的sl一起被取消
  {
    auto underlying_price = bbase::decimal::Decimal<>("10010");
    auto exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 2);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      // EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
      // EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);

      // auto ao_result = te->PopResult();
      // if (ao_result.m_msg == nullptr) {
      //   ++try_times;
      //   continue;
      // }
      // std::cout << "active ao result:" << std::endl << ao_result.m_msg->DebugString() << std::endl;
      // ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 2);
      for (auto& order : triggered_result.RefFutureMarginResult().m_msg->related_orders()) {
        TransactDTOChecker orderCheck(&order);
        auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
        if (stop_order_type == EStopOrderType::PartialStopLoss) {
          EXPECT_EQ(order.order_status(), EOrderStatus::Deactivated);
          EXPECT_EQ(order.cross_status(), ECrossStatus::Deactivated);
          EXPECT_EQ(order.cancel_type(), ECancelType::CancelByTpSlTsClear);
          EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialStopLoss);
        } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
          EXPECT_EQ(order.order_status(), EOrderStatus::Rejected);
          EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
          EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);
        } else {
          EXPECT_TRUE(false);
        }
      }

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

// 两次触发，如果绑定的订单已触发，则自己不能触发, tp先触发
TEST_F(iOneCancelOtherTest, LimitPartialTpsl_DoubleTriggerFail) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  11100'0000, 9800'0000, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  std::string tp_replace_order_id{};
  std::string sl_replace_order_id{};
  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 9800'0000);
        sl_replace_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 11100'0000);
        tp_replace_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 先触发tp后触发sl，后一个订单不能触发
  {
    auto underlying_price = bbase::decimal::Decimal<>("10010");
    auto exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次
    auto sl_underlying_price = bbase::decimal::Decimal<>("9900");

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
      usleep(500 * 1000);
      te->UpdateMarketData(symbol, sl_underlying_price, exchange_rate);
      sl_underlying_price = sl_underlying_price.Sub(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      auto time_event2 = std::make_shared<event::TimerEvent>(0);
      time_event2->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event2->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret2 = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event2, true);
      ASSERT_EQ(ret2, error::kErrorCodeSuccess);
      auto ao_result = te->PopResult();
      // if (ao_result.m_msg == nullptr) {
      //   ++try_times;
      //   continue;
      // }
      // std::cout << "active ao result:" << std::endl << ao_result.m_msg->DebugString() << std::endl;
      ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 2);
      auto ao = ao_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(ao.stop_order_type(), EStopOrderType::PartialStopLoss);
      EXPECT_EQ(ao.order_status(), EOrderStatus::Deactivated);
      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

// 两次触发，如果绑定的订单已触发，则自己不能触发, sl先触发
TEST_F(iOneCancelOtherTest, LimitPartialTpsl_DoubleTriggerFail2) {
  biz::user_id_t user_id_other = 1;
  biz::user_id_t user_id_test = 2;

  stub user_other(te, user_id_other);
  stub user_test(te, user_id_test);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id_other), 0);
  EXPECT_EQ(Deposit(user_id_other, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_other, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_other, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id_test), 0);
  EXPECT_EQ(Deposit(user_id_test, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id_test, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id_test, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id{};
  {
    auto order_id = te->GenUUID();
    buy_order_id = order_id;
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_test, coin, ETriggerBy::MarkPrice, 10010'0000,
                                  ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  11100'0000, 9800'0000, {}, true);

    auto resp_test = user_test.process(create_build.Build());
    auto result_test = te->PopResult();
    EXPECT_EQ(resp_test->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    ASSERT_NE(result_test.m_msg.get(), nullptr);
    auto orderCheck = result_test.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 10010'0000);
    EXPECT_EQ(static_cast<ETpSlMode>(orderCheck.m_msg->tp_sl_mode()), ETpSlMode::Partial);
    EXPECT_EQ(orderCheck.m_msg->tp_trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(orderCheck.m_msg->take_profit_limit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_limit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->tp_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->sl_order_type(), EOrderType::Limit);
    EXPECT_EQ(orderCheck.m_msg->need_generate_pid(), true);
  }

  {
    // user_other下卖单成交
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id_other, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user_other.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  std::string tp_replace_order_id{};
  std::string sl_replace_order_id{};
  {
    // user_test成交
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    NewLine();
    std::cout << "user1: " << user_test.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 9800'0000);
        sl_replace_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 10010'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Limit, 100);
        EXPECT_EQ(order.price_x(), 11100'0000);
        tp_replace_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 先触发sl后触发tp，后一个订单不能触发
  {
    auto underlying_price = bbase::decimal::Decimal<>("9900");
    auto exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次
    auto sl_underlying_price = bbase::decimal::Decimal<>("10010");

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
      usleep(500 * 1000);
      te->UpdateMarketData(symbol, sl_underlying_price, exchange_rate);
      sl_underlying_price = sl_underlying_price.Sub(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      auto time_event2 = std::make_shared<event::TimerEvent>(0);
      time_event2->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event2->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret2 = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event2, true);
      ASSERT_EQ(ret2, error::kErrorCodeSuccess);
      auto ao_result = te->PopResult();
      // if (ao_result.m_msg == nullptr) {
      //   ++try_times;
      //   continue;
      // }
      // std::cout << "active ao result:" << std::endl << ao_result.m_msg->DebugString() << std::endl;
      ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 2);
      for (auto& order : ao_result.RefFutureMarginResult().m_msg->related_orders()) {
        TransactDTOChecker orderCheck(&order);
        auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
        if (stop_order_type == EStopOrderType::PartialTakeProfit) {
          EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);
          EXPECT_EQ(order.order_status(), EOrderStatus::Deactivated);
        } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
          (void)(order);
        } else {
          EXPECT_TRUE(false);
        }
      }

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}
