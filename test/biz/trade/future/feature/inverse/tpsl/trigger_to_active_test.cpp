#include "test/biz/trade/future/feature/inverse/tpsl/trigger_to_active_test.hpp"

#include <string>

#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(TriggerToActiveTpSlTs, test_trigger_to_active_tp_sl_v1) {
  biz::user_id_t uid1 = 1;
  biz::user_id_t uid2 = 2;

  stub user1(te, uid1);
  stub user2(te, uid2);

  Deposit(user1);
  SwitchAccountMode(uid1, EAccountMode::Cross);
  Deposit(user2);
  SwitchAccountMode(uid2, EAccountMode::Cross);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(uid1), 0);
  EXPECT_EQ(SetSpotCollateralCoin(uid1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(OpenAccount(uid2), 0);
  EXPECT_EQ(SetSpotCollateralCoin(uid2, static_cast<enums::ecoin::Coin>(coin)), 0);

  biz::size_x_t qty = 1000;
  biz::price_x_t price = 10000 * 1e4;
  biz::price_x_t take_profit_price = price;
  biz::price_x_t stop_loss_price = price;
  biz::price_x_t trailing_stop_price = -1;
  biz::price_x_t activation_price = -1;
  biz::size_x_t take_profit_qty = qty;
  biz::size_x_t stop_loss_qty = qty;
  std::string take_profit_order_id = "";
  std::string stop_loss_order_id = "";
  ETriggerBy take_profit_trigger_by = ETriggerBy::LastPrice;
  ETriggerBy stop_loss_trigger_by = ETriggerBy::LastPrice;
  ETriggerBy trailing_stop_trigger_by = ETriggerBy::UNKNOWN;
  ETpSlMode tp_sl_mode = ETpSlMode::Partial;
  int32_t price_scale = 0;

  auto order_id1 = te->GenUUID();
  auto order_id2 = te->GenUUID();

  {
    // user1挂单,Buy方向,限价单
    FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id1, symbol, pz_index, ESide::Buy, order_type, qty,
                                                         price, ETimeInForce::GoodTillCancel, uid1, coin);
    auto resp = user1.process(create_build_user1_buy.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id1);
    ASSERT_NE(order_checker.m_msg, nullptr);
  }

  {
    // user2挂单,Sell方向,限价单
    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id2, symbol, pz_index, ESide::Sell, order_type, qty,
                                                          price, ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id2);
    ASSERT_NE(order_checker.m_msg, nullptr);
  }

  {
    // user1成交记录
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->header().user_id(), uid1);
    ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    auto checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    checker.CheckPrice(uid1, price);
    checker.CheckQty(uid1, qty);
    checker.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);

    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckSize(qty);
    position.CheckValue(1000 * 10000);
  }

  {
    // 测试场景:部分仓位tp和sl价格合适,tp优于仓位均价,sl劣于仓位均价
    // 预期结果:设置成功
    take_profit_price = 11000 * 1e4;
    stop_loss_price = 9000 * 1e4;
    take_profit_qty = 800;
    stop_loss_qty = 800;
    FutureOneOfSetTpSlTsBuilder builder;
    builder.SetValue(uid1, coin, symbol, pz_index, take_profit_trigger_by, take_profit_price, stop_loss_trigger_by,
                     stop_loss_price, trailing_stop_trigger_by, trailing_stop_price, activation_price, take_profit_qty,
                     stop_loss_qty, take_profit_order_id, stop_loss_order_id, price_scale, tp_sl_mode,
                     EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp = user1.process(builder.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    for (const auto& order : result.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker checker(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        checker
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, stop_loss_price, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, stop_loss_qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        checker
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, take_profit_price,
                            EStopOrderType::PartialTakeProfit, EOrderType::Market, take_profit_qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
  }

  {
    // 测试场景:部分仓位tp和sl价格合适,tp优于仓位均价,sl劣于仓位均价
    // 预期结果:设置成功
    take_profit_price = 12000 * 1e4;
    stop_loss_price = 8000 * 1e4;
    take_profit_qty = 800;
    stop_loss_qty = 800;
    FutureOneOfSetTpSlTsBuilder builder;
    builder.SetValue(uid1, coin, symbol, pz_index, take_profit_trigger_by, take_profit_price, stop_loss_trigger_by,
                     stop_loss_price, trailing_stop_trigger_by, trailing_stop_price, activation_price, take_profit_qty,
                     stop_loss_qty, take_profit_order_id, stop_loss_order_id, price_scale, tp_sl_mode,
                     EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp = user1.process(builder.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    for (const auto& order : result.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker checker(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        checker
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, stop_loss_price, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, stop_loss_qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        checker
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, take_profit_price,
                            EStopOrderType::PartialTakeProfit, EOrderType::Market, take_profit_qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
  }

  {
    // 测试场景:触发止盈止损订单
    // 预期结果:触发成功
    underlying_price = bbase::decimal::Decimal<>("11000");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;
    while (!checked && try_times < 4) {
      usleep(50 * 1000);
      // 更新市场价格
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截
      te->UpdateQuoteData(symbol, 5, underlying_price.String(), "10000000");

      // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto result = te->PopResult();
      if (result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = result.m_msg->futures_margin_result().related_orders().at(0);
      ASSERT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
      ASSERT_EQ(triggered_so.cross_status(), ECrossStatus::Init);

      result = te->PopResult();
      ASSERT_NE(result.m_msg, nullptr);
      ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 2);
      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

TEST_F(TriggerToActiveTpSlTs, test_trigger_to_active_tp_sl_v2) {
  // 构建test user stub
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(user);

  // 构建other user stub
  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(other_user);

  // 行情校准: 校准mark price($10,000)
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 行情校准: 校准last price($10,000)
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // user挂单
  biz::order_id_t buy_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;
    biz::size_x_t qty = 9000;
    biz::price_x_t price = 10000 * 1e4;
    buy_order_id = order_id;
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id, coin);
    auto resp = user.process(create_build.Build());
    ASSERT_NE(resp, nullptr);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    order_check.CheckPrice(user_id, 10000 * 1e4);
    order_check.CheckQty(user_id, 9000);
    order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
  }

  // other user挂单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;
    biz::size_x_t qty = 9000;
    biz::price_x_t price = 10000 * 1e4;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, other_user_id, coin);
    auto resp = other_user.process(create_build.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    order_check.CheckPrice(other_user_id, 10000 * 1e4);
    order_check.CheckQty(other_user_id, 9000);
    order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);  // taker fill

    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    auto position_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    position_check.CheckSize(9000);
    position_check.CheckSide(ESide::Sell);
    position_check.CheckValue(9000 * 10000);

    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
  }

  // user的成交记录
  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), user_id);

    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    order_check.CheckPrice(user_id, 10000 * 1e4);
    order_check.CheckQty(user_id, 9000);
    order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);  // make fill

    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    auto position_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    position_check.CheckSize(9000);  // 形成持仓
    position_check.CheckSide(ESide::Buy);
    position_check.CheckValue(9000 * 10000);

    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
  }

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  {
    FutureOneOfSwitchTpSlModeBuilder create_build;
    create_build.SetValue(user_id, coin, symbol, ETpSlMode::Partial);
    auto resp = user.process(create_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).tp_sl_mode(),
              ETpSlMode::Partial);  // 仓位上的止盈止损模式
  }

  // 仓位上挂部分止盈订单并触发
  {
    auto order_id = te->GenUUID();

    FutureOneOfSetTpSlTsBuilder set_tp_sl_build;
    set_tp_sl_build.SetValue(user_id, coin, symbol, pz_index, ETriggerBy::MarkPrice, 11000 * 1e4, ETriggerBy::UNKNOWN,
                             {}, ETriggerBy::UNKNOWN, {}, 0, 9000, {}, "", "", {}, {}, {}, {}, {}, {}, true);
    auto resp = user.process(set_tp_sl_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).tp_order_num(),
              1);  // 仓位上tp_order数量

    ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).trigger_price_x(), 11000 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).qty_x(), 9000);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).stop_order_type(),
              EStopOrderType::PartialTakeProfit);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).expected_direction(),
              EPriceDirection::Rising);

    underlying_price = bbase::decimal::Decimal<>("11000");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新市场价格
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件, 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格,尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
      const auto& old_order_id = triggered_so.order_id();

      auto ao_result = te->PopResult();
      if (ao_result.m_msg == nullptr) {
        ++try_times;
        continue;
      }

      // 部分止盈止损市价单也会循环执行,所以此处会有2笔订单,一笔原先被取消的订单,一笔新生成的订单
      ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 2);
      for (auto& order : ao_result.m_msg->futures_margin_result().related_orders()) {
        if (order.order_id() == old_order_id) {
          // 活动单触发后是IOC订单,部分成交后被取消
          EXPECT_EQ(order.order_status(), EOrderStatus::Cancelled);
          EXPECT_EQ(order.cross_status(), ECrossStatus::Canceled);
        } else {
          EXPECT_EQ(order.order_status(), EOrderStatus::Triggered);
          EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
        }
      }
      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

/*
 * 用户持仓110000
 * 部分止盈止损订单110000
 * 触发后,执行拆单操作,一笔订单数量100000,一笔订单数量10000
 */
TEST_F(TriggerToActiveTpSlTs, test_trigger_to_active_tp_sl_v3) {
  // 构建test user stub
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(user);

  // 构建other user stub
  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(other_user);

  ExecSetRiskId(user);
  ExecSetRiskId(other_user);

  // 行情校准: 校准mark price($10,000)
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 行情校准: 校准last price($10,000)
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // user挂单
  biz::order_id_t buy_order_id;
  BaseUserCreateOrder(user, user_id, ESide::Buy, 90 * 1e3, 10000 * 1e4, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 90 * 1e3, 10000 * 1e4, 90 * 1e3);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 90 * 1e3, 10000 * 1e4, 90 * 1e3);
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Buy, 20 * 1e3, 10000 * 1e4, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 20 * 1e3, 10000 * 1e4, 110 * 1e3);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 20 * 1e3, 10000 * 1e4, 110 * 1e3);

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  BaseUserSwithcTpslMode(user, user_id);

  // 仓位上挂一个部分止盈订单并触发
  std::string tpsl_parent_order_id;
  BaseCreatePartialTakeProfitOrder(user, user_id, 11000'0000, 11000'0000, 110 * 1e3, tpsl_parent_order_id);
  {
    underlying_price = bbase::decimal::Decimal<>("11000");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);
      // 更新市场价格
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);

      // 给Trigger发一个timer事件, 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格,尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发,这里总共3笔订单,2笔拆单的订单,1笔母订单被取消了,订单数量分别为：110000,100000,10000
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 3);
      for (auto& triggered_so : triggered_result.m_msg->futures_margin_result().related_orders()) {
        auto qty = triggered_so.qty_x();
        EXPECT_EQ(triggered_so.order_type(), EOrderType::Limit);
        EXPECT_EQ(triggered_so.stop_order_type(), EStopOrderType::PartialTakeProfit);
        EXPECT_EQ(triggered_so.tpsl_parent_order_id(), tpsl_parent_order_id);
        if (qty == 110 * 1e3) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Deactivated);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Aborted);
          EXPECT_EQ(triggered_so.order_id(), tpsl_parent_order_id);
        } else if (qty == 100 * 1e3) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
          EXPECT_NE(triggered_so.order_id(), tpsl_parent_order_id);
          EXPECT_EQ(triggered_so.tpsl_leaves_qty(), 100 * 1e3);
        } else if (qty == 10 * 1e3) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
          EXPECT_NE(triggered_so.order_id(), tpsl_parent_order_id);
          EXPECT_EQ(triggered_so.tpsl_leaves_qty(), 10 * 1e3);
        } else {
          EXPECT_TRUE(false);
        }
      }

      {
        auto ao_result = te->PopResult();
        ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 1);
        auto ao = ao_result.m_msg->futures_margin_result().related_orders().at(0);
        EXPECT_EQ(ao.order_status(), EOrderStatus::New);
        EXPECT_EQ(ao.cross_status(), ECrossStatus::NewAccepted);
      }

      {
        auto ao_result = te->PopResult();
        ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 1);
        auto ao = ao_result.m_msg->futures_margin_result().related_orders().at(0);
        EXPECT_EQ(ao.order_status(), EOrderStatus::New);
        EXPECT_EQ(ao.cross_status(), ECrossStatus::NewAccepted);
      }
      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

/*
 * 用户持仓110000
 * 部分止盈止损订单110000
 * 用户平仓10000
 * 触发后,执行拆单操作,一笔订单数量100000,一笔订单数量10000,另一笔应RO规则,被裁剪为0
 */
TEST_F(TriggerToActiveTpSlTs, test_trigger_to_active_tp_sl_v4) {
  // 构建test user stub
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(user);

  // 构建other user stub
  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(other_user);

  ExecSetRiskId(user);
  ExecSetRiskId(other_user);

  // 行情校准: 校准mark price($10,000)
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 行情校准: 校准last price($10,000)
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // user挂单
  biz::order_id_t buy_order_id;
  BaseUserCreateOrder(user, user_id, ESide::Buy, 90'000, 10000 * 1e4, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 90'000, 10000'0000, 90'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 90'000, 10000'0000, 90'000);
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Buy, 20'000, 10000'0000, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 20'000, 10000'0000, 110'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 20'000, 10000'0000, 110'000);

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  BaseUserSwithcTpslMode(user, user_id);

  std::string tpsl_parent_order_id;
  // 仓位上挂一个部分止盈订单并触发
  BaseCreatePartialTakeProfitOrder(user, user_id, 11000'0000, 11000'0000, 110'000, tpsl_parent_order_id);

  // -------------------------------平仓
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Sell, 10'000, 10000'0000, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Buy, 10'000, 10000'0000, 100'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 10'000, 10000'0000, 100'000);

  // 触发部分止盈止损条件单
  {
    underlying_price = bbase::decimal::Decimal<>("11000");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新市场价格
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件, 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格,尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发,这里总共3笔订单,2笔拆单的订单,1笔母订单被取消了,订单数量分别时：110‘000,100‘000,10‘000
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 3);
      for (auto& triggered_so : triggered_result.m_msg->futures_margin_result().related_orders()) {
        auto qty = triggered_so.qty_x();
        EXPECT_EQ(triggered_so.tpsl_parent_order_id(), tpsl_parent_order_id);
        EXPECT_EQ(triggered_so.order_type(), EOrderType::Limit);
        EXPECT_EQ(triggered_so.stop_order_type(), EStopOrderType::PartialTakeProfit);
        if (qty == 110'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Deactivated);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Aborted);
          EXPECT_EQ(triggered_so.order_id(), tpsl_parent_order_id);
          EXPECT_EQ(triggered_so.tpsl_leaves_qty(), qty);
        } else if (qty == 100'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
          EXPECT_NE(triggered_so.order_id(), tpsl_parent_order_id);
          EXPECT_EQ(triggered_so.tpsl_leaves_qty(), 100'000);
          EXPECT_TRUE(triggered_so.parent_order_id().empty());
        } else if (qty == 10'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Rejected);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
          EXPECT_NE(triggered_so.order_id(), tpsl_parent_order_id);
          EXPECT_EQ(triggered_so.tpsl_leaves_qty(), 10'000);
          EXPECT_TRUE(triggered_so.parent_order_id().empty());
        } else {
          EXPECT_TRUE(false);
        }
      }

      {
        auto ao_result = te->PopResult();
        ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 1);
        auto ao = ao_result.m_msg->futures_margin_result().related_orders().at(0);

        EXPECT_EQ(ao.order_status(), EOrderStatus::New);
        EXPECT_EQ(ao.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_EQ(ao.qty_x(), 100'000);
      }

      {
        auto ao_result = te->PopResult();
        ASSERT_EQ(ao_result.m_msg, nullptr);
      }
      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

/*
 * 用户持仓110000
 * 部分止盈止损订单110000
 * 用户平仓5000
 * 触发后,执行拆单操作,一笔订单数量100000,一笔订单数量5000（应RO规则,由10000被裁剪为5000）
 */
TEST_F(TriggerToActiveTpSlTs, test_trigger_to_active_tp_sl_v5) {
  // 构建test user stub
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(user);

  // 构建other user stub
  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(other_user);

  ExecSetRiskId(user);
  ExecSetRiskId(other_user);

  // 行情校准: 校准mark price($10,000)
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 行情校准: 校准last price($10,000)
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // user挂单
  biz::order_id_t buy_order_id;
  BaseUserCreateOrder(user, user_id, ESide::Buy, 90'000, 10000 * 1e4, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 90'000, 10000'0000, 90'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 90'000, 10000'0000, 90'000);
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Buy, 20'000, 10000'0000, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 20'000, 10000'0000, 110'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 20'000, 10000'0000, 110'000);

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  BaseUserSwithcTpslMode(user, user_id);

  std::string tpsl_parent_order_id;
  // 仓位上挂一个部分止盈订单并触发
  BaseCreatePartialTakeProfitOrder(user, user_id, 11000'0000, 11000'0000, 110'000, tpsl_parent_order_id);

  // -------------------------------平仓
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Sell, 5'000, 10000'0000, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Buy, 5'000, 10000'0000, 105'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 5'000, 10000'0000, 105'000);

  // 触发部分止盈止损条件单
  {
    underlying_price = bbase::decimal::Decimal<>("11000");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新市场价格
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件, 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格,尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发,这里总共3笔订单,2笔拆单的订单,1笔母订单被取消了,订单数量分别时：110‘000,100‘000,10‘000
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 3);
      for (auto& triggered_so : triggered_result.m_msg->futures_margin_result().related_orders()) {
        auto qty = triggered_so.qty_x();
        if (qty == 110'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Deactivated);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Aborted);
          EXPECT_EQ(triggered_so.order_id(), tpsl_parent_order_id);
        } else if (qty == 100'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
          EXPECT_EQ(triggered_so.tpsl_parent_order_id(), tpsl_parent_order_id);
        } else if (qty == 10'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
          EXPECT_EQ(triggered_so.tpsl_parent_order_id(), tpsl_parent_order_id);
        } else {
          EXPECT_TRUE(false);
        }
      }

      {
        auto ao_result = te->PopResult();
        ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 1);
        auto ao = ao_result.m_msg->futures_margin_result().related_orders().at(0);

        EXPECT_EQ(ao.order_status(), EOrderStatus::New);
        EXPECT_EQ(ao.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_NE(ao.order_id(), tpsl_parent_order_id);
        EXPECT_TRUE(ao.qty_x() == 100'000 || ao.qty_x() == 5'000);
        EXPECT_TRUE(ao.tpsl_leaves_qty() == 100'000 || ao.tpsl_leaves_qty() == 10'000);
      }

      {
        auto ao_result = te->PopResult();
        ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 1);
        auto ao = ao_result.m_msg->futures_margin_result().related_orders().at(0);

        EXPECT_EQ(ao.order_status(), EOrderStatus::New);
        EXPECT_EQ(ao.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_NE(ao.order_id(), tpsl_parent_order_id);
        EXPECT_TRUE(ao.qty_x() == 100'000 || ao.qty_x() == 5'000);
        EXPECT_TRUE(ao.tpsl_leaves_qty() == 100'000 || ao.tpsl_leaves_qty() == 10'000);
      }
      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

/*
 * 用户持仓110
 * 部分止盈止损订单110
 * 用户平仓15
 * 触发后,执行拆单操作,一笔订单数量100（应RO规则,被裁剪为95）,一笔订单数量10（应RO规则,被裁剪为0）
 */
TEST_F(TriggerToActiveTpSlTs, test_trigger_to_active_tp_sl_v6) {
  // 构建test user stub
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(user);

  // 构建other user stub
  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(other_user);

  ExecSetRiskId(user);
  ExecSetRiskId(other_user);

  // 行情校准: 校准mark price($10,000)
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 行情校准: 校准last price($10,000)
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // user挂单
  biz::order_id_t buy_order_id;
  BaseUserCreateOrder(user, user_id, ESide::Buy, 90'000, 10000 * 1e4, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 90'000, 10000'0000, 90'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 90'000, 10000'0000, 90'000);
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Buy, 20'000, 10000'0000, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 20'000, 10000'0000, 110'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 20'000, 10000'0000, 110'000);

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  BaseUserSwithcTpslMode(user, user_id);

  std::string tpsl_parent_order_id;
  // 仓位上挂一个部分止盈订单并触发
  BaseCreatePartialTakeProfitOrder(user, user_id, 11000'0000, 11000'0000, 110'000, tpsl_parent_order_id);

  // -------------------------------平仓
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Sell, 15'000, 10000'0000, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Buy, 15'000, 10000'0000, 95'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 15'000, 10000'0000, 95'000);

  // 触发部分止盈止损条件单
  {
    underlying_price = bbase::decimal::Decimal<>("11000");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新市场价格
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件, 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格,尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发,这里总共3笔订单,2笔拆单的订单,1笔母订单被取消了,订单数量分别时：110‘000,100‘000,10‘000
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 3);
      for (auto& triggered_so : triggered_result.m_msg->futures_margin_result().related_orders()) {
        auto qty = triggered_so.qty_x();
        EXPECT_EQ(triggered_so.tpsl_parent_order_id(), tpsl_parent_order_id);
        EXPECT_EQ(triggered_so.order_type(), EOrderType::Limit);
        EXPECT_EQ(triggered_so.stop_order_type(), EStopOrderType::PartialTakeProfit);
        if (qty == 110'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Deactivated);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Aborted);
          EXPECT_EQ(triggered_so.order_id(), tpsl_parent_order_id);
        } else if (qty == 100'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
          EXPECT_NE(triggered_so.order_id(), tpsl_parent_order_id);
          EXPECT_EQ(triggered_so.tpsl_leaves_qty(), 100'000);
        } else if (qty == 10'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Rejected);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
          EXPECT_NE(triggered_so.order_id(), tpsl_parent_order_id);
          EXPECT_EQ(triggered_so.tpsl_leaves_qty(), 10'000);
        } else {
          EXPECT_TRUE(false);
        }
      }

      {
        auto ao_result = te->PopResult();
        ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 1);
        auto ao = ao_result.m_msg->futures_margin_result().related_orders().at(0);

        EXPECT_EQ(ao.order_status(), EOrderStatus::New);
        EXPECT_EQ(ao.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_NE(ao.order_id(), tpsl_parent_order_id);
        EXPECT_EQ(ao.qty_x(), 95'000);
        EXPECT_EQ(ao.tpsl_leaves_qty(), 100'000);
      }

      {
        auto ao_result = te->PopResult();
        ASSERT_EQ(ao_result.m_msg, nullptr);
      }

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

/*
 * 用户持仓110
 * 部分止盈止损订单110
 * 用户挂平仓单110,价格更优
 * 触发后,执行拆单操作,一笔订单数量100（应RO规则,被裁剪为0）,然后不再拆单
 */
TEST_F(TriggerToActiveTpSlTs, test_trigger_to_active_tp_sl_v7) {
  // 构建test user stub
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(user);

  // 构建other user stub
  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(other_user);

  ExecSetRiskId(user);
  ExecSetRiskId(other_user);

  // 行情校准: 校准mark price($10,000)
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 行情校准: 校准last price($10,000)
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // user挂单
  biz::order_id_t buy_order_id;
  BaseUserCreateOrder(user, user_id, ESide::Buy, 90'000, 10000 * 1e4, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 90'000, 10000'0000, 90'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 90'000, 10000'0000, 90'000);
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Buy, 20'000, 10000'0000, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 20'000, 10000'0000, 110'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 20'000, 10000'0000, 110'000);

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  BaseUserSwithcTpslMode(user, user_id);

  std::string tpsl_parent_order_id;
  // 仓位上挂一个部分止盈订单并触发
  BaseCreatePartialTakeProfitOrder(user, user_id, 11000'0000, 11000'0000, 110'000, tpsl_parent_order_id);

  // -------------------------------平仓挂单
  BaseUserCreateOrder(user, user_id, ESide::Sell, 75'000, 10000'0000, buy_order_id);
  BaseUserCreateOrder(user, user_id, ESide::Sell, 35'000, 10000'0000, buy_order_id);

  // 触发部分止盈止损条件单
  {
    underlying_price = bbase::decimal::Decimal<>("11000");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新市场价格
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件, 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格,尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发,这里总共2笔订单,1笔拆单的订单,1笔母订单被取消了,订单数量分别时：110‘000,100‘000
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 2);
      for (auto& triggered_so : triggered_result.m_msg->futures_margin_result().related_orders()) {
        auto qty = triggered_so.qty_x();
        EXPECT_EQ(triggered_so.tpsl_parent_order_id(), tpsl_parent_order_id);
        EXPECT_EQ(triggered_so.order_type(), EOrderType::Limit);
        EXPECT_EQ(triggered_so.stop_order_type(), EStopOrderType::PartialTakeProfit);
        if (qty == 110'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Deactivated);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Aborted);
          EXPECT_EQ(triggered_so.order_id(), tpsl_parent_order_id);
        } else if (qty == 100'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Rejected);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
          EXPECT_NE(triggered_so.order_id(), tpsl_parent_order_id);
        } else {
          EXPECT_TRUE(false);
        }
      }

      // 这里不应该有订单发撮合,所以数据应该为空
      {
        auto ao_result = te->PopResult();
        EXPECT_EQ(ao_result.m_msg, nullptr);
      }
      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

/*
 * 用户持仓180
 * 部分止盈止损订单110
 * 修改部分止盈止损订单数量为250,但因为仓位size只有180,所以订单数量被修正为180
 * 触发拆单成两笔,订单数量分别为100与80
 */
TEST_F(TriggerToActiveTpSlTs, test_trigger_to_active_tp_sl_v8) {
  // 构建test user stub
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  // 构建other user stub
  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(user);
  // 设置逐仓10x杠杆+充钱
  Deposit(other_user);

  ExecSetRiskId(user);
  ExecSetRiskId(other_user);

  // 行情校准: 校准mark price($10,000)
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 行情校准: 校准last price($10,000)
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // user挂单
  biz::order_id_t buy_order_id;
  BaseUserCreateOrder(user, user_id, ESide::Buy, 90'000, 10000 * 1e4, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 90'000, 10000'0000, 90'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 90'000, 10000'0000, 90'000);
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Buy, 90'000, 10000'0000, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 90'000, 10000'0000, 180'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 90'000, 10000'0000, 180'000);

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  BaseUserSwithcTpslMode(user, user_id);

  std::string tpsl_parent_order_id;
  // 仓位上挂一个部分止盈订单并触发
  BaseCreatePartialTakeProfitOrder(user, user_id, 11000'0000, 11000'0000, 110'000, tpsl_parent_order_id);

  // 修改tpsl订单
  {
    auto order_id = te->GenUUID();
    FutureOneOfSetTpSlTsBuilder set_tp_sl_build;
    set_tp_sl_build.SetValue(user_id, coin, symbol, pz_index, ETriggerBy::MarkPrice, 11000 * 1e4, ETriggerBy::UNKNOWN,
                             {}, ETriggerBy::UNKNOWN, {}, 0, 250'000, {}, tpsl_parent_order_id, "", {},
                             ETpSlMode::Partial, EOrderType::Limit, {}, 11000'0000, {}, true);
    auto resp = user.process(set_tp_sl_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).tp_order_num(),
              1);  // 仓位上tp_order数量

    ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    EXPECT_EQ(order_check.m_msg->trigger_by(), ETriggerBy::MarkPrice);
    EXPECT_EQ(order_check.m_msg->trigger_price_x(), 11000 * 1e4);
    EXPECT_EQ(order_check.m_msg->qty_x(), 180'000);
    EXPECT_EQ(order_check.m_msg->order_status(), EOrderStatus::Untriggered);
    EXPECT_EQ(order_check.m_msg->stop_order_type(), EStopOrderType::PartialTakeProfit);
    EXPECT_EQ(order_check.m_msg->expected_direction(), EPriceDirection::Rising);
    EXPECT_EQ(order_check.m_msg->tpsl_leaves_qty(), 180'000);
    tpsl_parent_order_id = order_check.m_msg->order_id();
  }

  // 触发部分止盈止损条件单
  {
    underlying_price = bbase::decimal::Decimal<>("11000");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新市场价格
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件, 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格,尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发,这里总共2笔订单,2笔拆单的订单,1笔母订单被取消了,订单数量分别时：180‘000,,100‘000, 80‘000
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 3);
      for (auto& triggered_so : triggered_result.m_msg->futures_margin_result().related_orders()) {
        auto qty = triggered_so.qty_x();
        EXPECT_EQ(triggered_so.tpsl_parent_order_id(), tpsl_parent_order_id);
        EXPECT_EQ(triggered_so.order_type(), EOrderType::Limit);
        EXPECT_EQ(triggered_so.stop_order_type(), EStopOrderType::PartialTakeProfit);
        if (qty == 180'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Deactivated);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Aborted);
          EXPECT_EQ(triggered_so.order_id(), tpsl_parent_order_id);
        } else if (qty == 100'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
          EXPECT_NE(triggered_so.order_id(), tpsl_parent_order_id);
        } else if (qty == 80'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
          EXPECT_NE(triggered_so.order_id(), tpsl_parent_order_id);
        } else {
          EXPECT_TRUE(false);
        }
      }

      {
        auto ao_result = te->PopResult();
        ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 1);
        auto ao = ao_result.m_msg->futures_margin_result().related_orders().at(0);

        EXPECT_EQ(ao.order_status(), EOrderStatus::New);
        EXPECT_EQ(ao.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_NE(ao.order_id(), tpsl_parent_order_id);
        EXPECT_TRUE(ao.qty_x() == 80'000 || ao.qty_x() == 100'000);
        EXPECT_TRUE(ao.tpsl_leaves_qty() == 80'000 || ao.tpsl_leaves_qty() == 100'000);
      }

      {
        auto ao_result = te->PopResult();
        ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 1);
        auto ao = ao_result.m_msg->futures_margin_result().related_orders().at(0);

        EXPECT_EQ(ao.order_status(), EOrderStatus::New);
        EXPECT_EQ(ao.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_NE(ao.order_id(), tpsl_parent_order_id);
        EXPECT_TRUE(ao.qty_x() == 80'000 || ao.qty_x() == 100'000);
        EXPECT_TRUE(ao.tpsl_leaves_qty() == 80'000 || ao.tpsl_leaves_qty() == 100'000);
      }
      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

/*
 * 市价循环平仓
 * 用户持仓180
 * 市价部分止盈止损订单110
 * 部分成交,成交数量50,此时生成循环平仓操作,订单剩余数量60
 * 由于没有挂单,循环平仓失败,再次生成循环平仓单,剩余订单数量60
 * 剩余60全部成交,此时订单剩余数量0,不再执行循环平仓操作
 */
TEST_F(TriggerToActiveTpSlTs, test_trigger_to_active_tp_sl_market_loop) {
  // 构建test user stub
  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 500000;
  sc->price_limit_pnt_y_e6_ = 510000;
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  // 构建other user stub
  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(user);
  // 设置逐仓10x杠杆+充钱
  Deposit(other_user);

  ExecSetRiskId(user);
  ExecSetRiskId(other_user);

  // 行情校准: 校准mark price($10,000)
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 行情校准: 校准last price($10,000)
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // user挂单
  biz::order_id_t buy_order_id;
  BaseUserCreateOrder(user, user_id, ESide::Buy, 90'000, 10000 * 1e4, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 90'000, 10000'0000, 90'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 90'000, 10000'0000, 90'000);
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Buy, 90'000, 10000'0000, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 90'000, 10000'0000, 180'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 90'000, 10000'0000, 180'000);

  // 对手方挂单,与基准user_id 部分成交
  biz::order_id_t other_order_id;
  OtherUserCreateOrder(other_user, other_user_id, ESide::Buy, 50'000, 10000'0000, other_order_id);

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  BaseUserSwithcTpslMode(user, user_id);

  std::string tpsl_parent_order_id;
  // 仓位上挂一个部分止盈订单并触发
  BaseCreatePartialTakeProfitMarketOrder(user, user_id, 11000'0000, 110'000, tpsl_parent_order_id);

  // 触发部分止盈止损条件单
  {
    underlying_price = bbase::decimal::Decimal<>("11000");
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    // 给Trigger发一个timer事件, 把Triggered订单发给PreWorker
    auto time_event = std::make_shared<event::TimerEvent>(0);
    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新市场价格
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截

      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格,尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发,生成一笔市价平仓单
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      for (auto& triggered_so : triggered_result.m_msg->futures_margin_result().related_orders()) {
        auto qty = triggered_so.qty_x();
        EXPECT_EQ(triggered_so.tpsl_parent_order_id(), "");
        EXPECT_EQ(triggered_so.order_type(), EOrderType::Market);
        EXPECT_EQ(triggered_so.stop_order_type(), EStopOrderType::PartialTakeProfit);
        if (qty == 110'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
        } else {
          EXPECT_TRUE(false);
        }
      }
    }

    std::string new_tpsl_order_id;
    // base user 市价单部分成交
    {
      auto ao_result = te->PopResult();
      ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 2);
      for (auto& order : ao_result.m_msg->futures_margin_result().related_orders()) {
        EXPECT_EQ(order.order_type(), EOrderType::Market);
        if (order.order_status() == EOrderStatus::Cancelled) {
          // 市价单,部分成交
          EXPECT_EQ(order.tpsl_leaves_qty(), 60'000);
          EXPECT_EQ(order.cum_qty_x(), 50'000);
          EXPECT_EQ(order.qty_x(), 100'000);
          EXPECT_EQ(order.order_id(), tpsl_parent_order_id);
          EXPECT_EQ(order.cross_status(), ECrossStatus::Canceled);
        } else if (order.order_status() == EOrderStatus::Triggered) {
          // 循环平仓生成的市价单
          EXPECT_EQ(order.tpsl_leaves_qty(), 60'000);
          EXPECT_EQ(order.cum_qty_x(), 0);
          EXPECT_EQ(order.qty_x(), 60'000);
          EXPECT_NE(order.order_id(), tpsl_parent_order_id);
          new_tpsl_order_id = order.order_id();
          EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
        } else {
          EXPECT_TRUE(false);
        }
      }
      auto position = ao_result.m_msg->futures_margin_result().affected_positions()[0];
      EXPECT_EQ(position.size_x(), 130'000);
    }

    // 对手方挂单成交
    {
      auto ao_result = te->PopResult();
      ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto ao = ao_result.m_msg->futures_margin_result().related_orders().at(0);

      EXPECT_EQ(ao.order_status(), EOrderStatus::Filled);
      EXPECT_EQ(ao.cross_status(), ECrossStatus::MakerFill);
      EXPECT_NE(ao.order_id(), buy_order_id.GetValue());
      EXPECT_EQ(ao.qty_x(), 50'000);
      EXPECT_EQ(ao.cum_qty_x(), 50'000);
      auto position = ao_result.m_msg->futures_margin_result().affected_positions()[0];
      EXPECT_EQ(position.size_x(), 130'000);
    }

    // 循环平仓
    {
      // 循环平仓操作,需要每隔1秒触发一次
      usleep(1'100'000);
      int try_count = 0;
      while (try_count++ < 5) {
        time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
        auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
        ASSERT_EQ(ret, error::kErrorCodeSuccess);
        auto ao_result1 = te->PopResult();
        if (ao_result1.m_msg == nullptr) {
          continue;
        }
        // 订单发撮合
        { ASSERT_EQ(ao_result1.m_msg->futures_margin_result().related_orders().size(), 1); }
        // 撮合回报
        {
          auto ao_result = te->PopResult();
          ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 2);
          for (auto& order : ao_result.m_msg->futures_margin_result().related_orders()) {
            EXPECT_EQ(order.order_type(), EOrderType::Market);
            if (order.order_status() == EOrderStatus::Cancelled) {
              // 市价单,无挂单可以成交,被撤单
              EXPECT_EQ(order.tpsl_leaves_qty(), 60'000);
              EXPECT_EQ(order.cum_qty_x(), 0);
              EXPECT_EQ(order.qty_x(), 60'000);
              EXPECT_EQ(order.order_id(), new_tpsl_order_id);
              EXPECT_EQ(order.cross_status(), ECrossStatus::Canceled);
            } else if (order.order_status() == EOrderStatus::Triggered) {
              // 循环平仓生成的市价单
              EXPECT_EQ(order.tpsl_leaves_qty(), 60'000);
              EXPECT_EQ(order.cum_qty_x(), 0);
              EXPECT_EQ(order.qty_x(), 60'000);
              EXPECT_NE(order.order_id(), new_tpsl_order_id);
              EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
            } else {
              EXPECT_TRUE(false);
            }
          }
        }
        break;
      }
    }
    // 新挂单,剩余的订单循环平仓完成
    OtherUserCreateOrder(other_user, other_user_id, ESide::Buy, 60'000, 10000'0000, other_order_id);
    {
      // 循环平仓操作,需要每隔1秒触发一次
      usleep(1'100'000);
      int try_count = 0;
      while (try_count++ < 5) {
        time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
        auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
        ASSERT_EQ(ret, error::kErrorCodeSuccess);
        auto ao_result1 = te->PopResult();
        if (ao_result1.m_msg == nullptr) {
          continue;
        }
        // 订单发撮合
        {
          ASSERT_EQ(ao_result1.m_msg->futures_margin_result().related_orders().size(), 1);
          auto order = ao_result1.m_msg->futures_margin_result().related_orders().at(0);
          new_tpsl_order_id = order.order_id();
        }
        // 撮合回报
        {
          auto ao_result = te->PopResult();
          ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 1);
          for (auto& order : ao_result.m_msg->futures_margin_result().related_orders()) {
            if (order.order_status() == EOrderStatus::Filled) {
              EXPECT_EQ(order.tpsl_leaves_qty(), 0);
              EXPECT_EQ(order.cum_qty_x(), 60'000);
              EXPECT_EQ(order.qty_x(), 60'000);
              EXPECT_EQ(order.order_id(), new_tpsl_order_id);
              EXPECT_EQ(order.cross_status(), ECrossStatus::TakerFill);
              EXPECT_EQ(order.order_type(), EOrderType::Market);
              EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);
            } else {
              EXPECT_TRUE(false);
            }
          }
          auto position = ao_result.m_msg->futures_margin_result().affected_positions()[0];
          EXPECT_EQ(position.size_x(), 70'000);
        }
        break;
      }
    }

    // 对手方挂单成交
    {
      auto ao_result = te->PopResult();
      ASSERT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto ao = ao_result.m_msg->futures_margin_result().related_orders().at(0);

      EXPECT_EQ(ao.order_status(), EOrderStatus::Filled);
      EXPECT_EQ(ao.cross_status(), ECrossStatus::MakerFill);
      EXPECT_NE(ao.order_id(), buy_order_id.GetValue());
      EXPECT_EQ(ao.order_id(), other_order_id.GetValue());
      EXPECT_EQ(ao.qty_x(), 60'000);
      EXPECT_EQ(ao.cum_qty_x(), 60'000);
      auto position = ao_result.m_msg->futures_margin_result().affected_positions()[0];
      EXPECT_EQ(position.size_x(), 70'000);
    }
  }
}

/*
 * 本UT用于模拟部分止盈止损限价单拆单后,部分订单发撮合成功,但未dump成功,Trading发生重启的场景
 * 用户持仓110000
 * 创建一个部分止盈止损限价单,数量110000
 * 收到一个撮合回报,订单下单成功100,此时会触发逻辑修复
 * 收到另一个撮合回报,订单下单成功10,此时母订单会被取消
 */
TEST_F(TriggerToActiveTpSlTs, test_restart_limit_order_v1) {
  // 构建test user stub
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(user);

  // 构建other user stub
  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(other_user);

  ExecSetRiskId(user);
  ExecSetRiskId(other_user);

  // 行情校准: 校准mark price($10,000)
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 行情校准: 校准last price($10,000)
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // user挂单
  biz::order_id_t buy_order_id;
  BaseUserCreateOrder(user, user_id, ESide::Buy, 90'000, 10000 * 1e4, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 90'000, 10000'0000, 90'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 90'000, 10000'0000, 90'000);
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Buy, 20'000, 10000'0000, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 20'000, 10000'0000, 110'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 20'000, 10000'0000, 110'000);

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  BaseUserSwithcTpslMode(user, user_id);

  std::string tpsl_parent_order_id;
  // 仓位上挂一个部分止盈订单并触发
  BaseCreatePartialTakeProfitOrder(user, user_id, 11000'0000, 11000'0000, 110'000, tpsl_parent_order_id);
  // 设置标志位,处于重启回追数据阶段
  application::GlobalVarManager::Instance().is_all_recovery_ = false;

  // 模拟一笔撮合成交回报,订单状态 new,订单qty 100
  {
    SendOrderToCross(user_id, 100'000, 11000'0000, 100, tpsl_parent_order_id, 100'000);
    auto result = te->PopResult();
    EXPECT_NE(result.m_msg, nullptr);

    auto related_order = result.m_msg->futures_margin_result().related_orders();
    EXPECT_EQ(related_order.size(), 2);
    for (auto& order : related_order) {
      auto order_id = order.order_id();
      if (order_id == tpsl_parent_order_id) {
        EXPECT_EQ(order.tpsl_leaves_qty(), 10'000);
        EXPECT_EQ(order.order_status(), EOrderStatus::Triggered);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
        EXPECT_EQ(order.tpsl_parent_order_id(), tpsl_parent_order_id);
        EXPECT_EQ(order.qty_x(), 110'000);
      } else if (order.tpsl_parent_order_id() == tpsl_parent_order_id) {
        EXPECT_EQ(order.order_status(), EOrderStatus::New);
        EXPECT_EQ(order.cross_status(), ECrossStatus::NewAccepted);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 模拟一笔撮合成交回报,订单状态 new,订单qty 10
  {
    SendOrderToCross(user_id, 10'000, 11000'0000, 101, tpsl_parent_order_id, 10'000);
    auto result = te->PopResult();
    EXPECT_NE(result.m_msg, nullptr);

    auto related_order = result.m_msg->futures_margin_result().related_orders();
    EXPECT_EQ(related_order.size(), 2);
    for (auto& order : related_order) {
      auto order_id = order.order_id();
      if (order_id == tpsl_parent_order_id) {
        EXPECT_EQ(order.tpsl_leaves_qty(), 0);
        EXPECT_EQ(order.order_status(), EOrderStatus::Deactivated);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Aborted);
        EXPECT_EQ(order.tpsl_parent_order_id(), tpsl_parent_order_id);
        EXPECT_EQ(order.qty_x(), 110'000);
      } else if (order.tpsl_parent_order_id() == tpsl_parent_order_id) {
        EXPECT_EQ(order.order_status(), EOrderStatus::New);
        EXPECT_EQ(order.cross_status(), ECrossStatus::NewAccepted);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
  application::GlobalVarManager::Instance().is_all_recovery_ = true;
}

/*
 * 本UT用于模拟部分止盈止损限价单拆单后,部分订单发撮合成功,但未dump成功,Trading发生了重启的场景
 * 用户持仓110
 * 创建一个部分止盈止损限价单,数量 110
 * 收到一个撮合回报,订单下单成功 100,此时会触发逻辑修复
 * 母订单触发,拆分1笔子订单,数量10
 */
TEST_F(TriggerToActiveTpSlTs, test_restart_limit_order_v2) {
  // 构建test user stub
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(user);

  // 构建other user stub
  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(other_user);

  ExecSetRiskId(user);
  ExecSetRiskId(other_user);

  // 行情校准: 校准mark price($10,000)
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 行情校准: 校准last price($10,000)
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // user挂单
  biz::order_id_t buy_order_id;
  BaseUserCreateOrder(user, user_id, ESide::Buy, 90'000, 10000 * 1e4, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 90'000, 10000'0000, 90'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 90'000, 10000'0000, 90'000);
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Buy, 20'000, 10000'0000, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 20'000, 10000'0000, 110'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 20'000, 10000'0000, 110'000);

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  BaseUserSwithcTpslMode(user, user_id);

  std::string tpsl_parent_order_id;
  // 仓位上挂一个部分止盈订单并触发
  BaseCreatePartialTakeProfitOrder(user, user_id, 11000'0000, 11000'0000, 110'000, tpsl_parent_order_id);
  // 设置标志位,处于重启回追数据阶段
  application::GlobalVarManager::Instance().is_all_recovery_ = false;

  // 模拟一笔撮合成交回报,订单状态 new,订单qty 100
  SendOrderToCross(user_id, 100'000, 11000'0000, 100, tpsl_parent_order_id, 100'000);
  {
    auto result = te->PopResult();
    EXPECT_NE(result.m_msg, nullptr);

    auto related_order = result.m_msg->futures_margin_result().related_orders();
    EXPECT_EQ(related_order.size(), 2);
    for (auto& order : related_order) {
      auto order_id = order.order_id();
      if (order_id == tpsl_parent_order_id) {
        EXPECT_EQ(order.tpsl_leaves_qty(), 10'000);
        EXPECT_EQ(order.qty_x(), 110'000);
        EXPECT_EQ(order.order_status(), EOrderStatus::Triggered);
        EXPECT_EQ(order.tpsl_parent_order_id(), tpsl_parent_order_id);
      } else if (order.tpsl_parent_order_id() == tpsl_parent_order_id) {
        EXPECT_EQ(order.order_status(), EOrderStatus::New);
        EXPECT_EQ(order.cross_status(), ECrossStatus::NewAccepted);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  application::GlobalVarManager::Instance().is_all_recovery_ = true;
  // 母订单触发
  {
    auto time_event = std::make_shared<event::TimerEvent>(0);
    int try_count = 0;
    while (try_count++ < 5) {
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);
      auto ao_result1 = te->PopResult();
      if (ao_result1.m_msg == nullptr) {
        continue;
      }
      // 订单发撮合
      {
        ASSERT_EQ(ao_result1.m_msg->futures_margin_result().related_orders().size(), 2);
        for (auto& order : ao_result1.m_msg->futures_margin_result().related_orders()) {
          auto order_status = order.order_status();
          if (order_status == EOrderStatus::Deactivated) {
            EXPECT_EQ(order.order_id(), tpsl_parent_order_id);
            EXPECT_EQ(order.tpsl_parent_order_id(), tpsl_parent_order_id);
            EXPECT_EQ(order.tpsl_leaves_qty(), 10'000);
            EXPECT_EQ(order.qty_x(), 110'000);
          } else if (order_status == EOrderStatus::Triggered) {
            EXPECT_EQ(order.tpsl_parent_order_id(), tpsl_parent_order_id);
            EXPECT_NE(order.order_id(), tpsl_parent_order_id);
            EXPECT_EQ(order.qty_x(), 10'000);
          } else {
            EXPECT_TRUE(false);
          }
        }
      }
      // 撮合回报
      {
        auto ao_result = te->PopResult();
        EXPECT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 1);
        for (auto& order : ao_result.m_msg->futures_margin_result().related_orders()) {
          EXPECT_EQ(order.order_type(), EOrderType::Limit);
          EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);
          EXPECT_EQ(order.tpsl_parent_order_id(), tpsl_parent_order_id);
          EXPECT_NE(order.order_id(), tpsl_parent_order_id);
          EXPECT_EQ(order.qty_x(), 10'000);
          EXPECT_EQ(order.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(order.order_status(), EOrderStatus::New);
        }
      }
      break;
    }
  }
}

/*
 * 本UT用于模拟部分止盈止损限价单拆单后,部分订单发撮合成功,但未dump成功,Trading发生了重启的场景
 * 用户持仓110
 * 创建一个部分止盈止损限价单,数量 110
 * 收到一个撮合回报,订单下单成功 100, 此时会触发逻辑修复
 * Trading已经恢复完成
 * 收到另一个撮合回报,订单下单成功 10, 就是该订单的母订单存在,也不做任何处理
 * 母订单随后被触发
 */
TEST_F(TriggerToActiveTpSlTs, test_restart_limit_order_v3) {
  // 构建test user stub
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(user);

  // 构建other user stub
  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(other_user);

  ExecSetRiskId(user);
  ExecSetRiskId(other_user);

  // 行情校准: 校准mark price($10,000)
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 行情校准: 校准last price($10,000)
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // user挂单
  biz::order_id_t buy_order_id;
  BaseUserCreateOrder(user, user_id, ESide::Buy, 90'000, 10000 * 1e4, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 90'000, 10000'0000, 90'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 90'000, 10000'0000, 90'000);
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Buy, 20'000, 10000'0000, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 20'000, 10000'0000, 110'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 20'000, 10000'0000, 110'000);

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  BaseUserSwithcTpslMode(user, user_id);

  std::string tpsl_parent_order_id;
  // 仓位上挂一个部分止盈订单并触发
  BaseCreatePartialTakeProfitOrder(user, user_id, 11000'0000, 11000'0000, 110'000, tpsl_parent_order_id);
  // 设置标志位,处于重启回追数据阶段
  application::GlobalVarManager::Instance().is_all_recovery_ = false;

  // 模拟一笔撮合成交回报,订单状态 new,订单qty 100
  {
    SendOrderToCross(user_id, 100'000, 11000'0000, 100, tpsl_parent_order_id, 100'000);
    auto result = te->PopResult();
    EXPECT_NE(result.m_msg, nullptr);

    auto related_order = result.m_msg->futures_margin_result().related_orders();
    EXPECT_EQ(related_order.size(), 2);
    for (auto& order : related_order) {
      auto order_id = order.order_id();
      if (order_id == tpsl_parent_order_id) {
        EXPECT_EQ(order.tpsl_leaves_qty(), 10'000);
        EXPECT_EQ(order.order_status(), EOrderStatus::Triggered);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
        EXPECT_EQ(order.tpsl_parent_order_id(), tpsl_parent_order_id);
        EXPECT_EQ(order.qty_x(), 110'000);
      } else if (order.tpsl_parent_order_id() == tpsl_parent_order_id) {
        EXPECT_EQ(order.order_status(), EOrderStatus::New);
        EXPECT_EQ(order.cross_status(), ECrossStatus::NewAccepted);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
  application::GlobalVarManager::Instance().is_all_recovery_ = true;
  // 模拟一笔撮合成交回报,订单状态 new,订单qty 10,因为已经恢复完成了,不再校验母订单是否存在,所以此处只有一笔订单
  {
    SendOrderToCross(user_id, 10'000, 11000'0000, 101, tpsl_parent_order_id, 10'000);
    auto result = te->PopResult();
    EXPECT_NE(result.m_msg, nullptr);

    auto related_order = result.m_msg->futures_margin_result().related_orders();
    EXPECT_EQ(related_order.size(), 1);
    for (auto& order : related_order) {
      auto order_id = order.order_id();
      if (order.tpsl_parent_order_id() == tpsl_parent_order_id) {
        EXPECT_EQ(order.order_status(), EOrderStatus::New);
        EXPECT_EQ(order.cross_status(), ECrossStatus::NewAccepted);
        EXPECT_EQ(order.qty_x(), 10'000);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
  // 母订单触发
  {
    auto time_event = std::make_shared<event::TimerEvent>(0);
    int try_count = 0;
    while (try_count++ < 5) {
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);
      auto ao_result1 = te->PopResult();
      if (ao_result1.m_msg == nullptr) {
        continue;
      }
      // 订单发撮合
      {
        ASSERT_EQ(ao_result1.m_msg->futures_margin_result().related_orders().size(), 2);
        for (auto& order : ao_result1.m_msg->futures_margin_result().related_orders()) {
          auto order_status = order.order_status();
          if (order_status == EOrderStatus::Deactivated) {
            EXPECT_EQ(order.order_id(), tpsl_parent_order_id);
            EXPECT_EQ(order.tpsl_parent_order_id(), tpsl_parent_order_id);
            EXPECT_EQ(order.tpsl_leaves_qty(), 10'000);
            EXPECT_EQ(order.qty_x(), 110'000);
          } else if (order_status == EOrderStatus::Triggered) {
            EXPECT_EQ(order.tpsl_parent_order_id(), tpsl_parent_order_id);
            EXPECT_NE(order.order_id(), tpsl_parent_order_id);
            EXPECT_EQ(order.qty_x(), 10'000);
          } else {
            EXPECT_TRUE(false);
          }
        }
      }
      // 撮合回报
      {
        auto ao_result = te->PopResult();
        EXPECT_EQ(ao_result.m_msg->futures_margin_result().related_orders().size(), 1);
        for (auto& order : ao_result.m_msg->futures_margin_result().related_orders()) {
          EXPECT_EQ(order.order_type(), EOrderType::Limit);
          EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);
          EXPECT_EQ(order.tpsl_parent_order_id(), tpsl_parent_order_id);
          EXPECT_NE(order.order_id(), tpsl_parent_order_id);
          EXPECT_EQ(order.qty_x(), 10'000);
          EXPECT_EQ(order.cross_status(), ECrossStatus::NewAccepted);
          EXPECT_EQ(order.order_status(), EOrderStatus::New);
        }
      }
      break;
    }
  }
  sleep(1);
}

/*
 * 本UT用于模拟部分止盈止损限价单拆单后,部分订单发撮合成功,但未dump成功,Trading发生了重启的场景
 * 用户持仓110
 * 创建一个部分止盈止损限价单,数量 110
 * 收到一个撮合回报,订单下单成功 100, 此时会触发逻辑修复,并且触发循环止盈止损
 */
TEST_F(TriggerToActiveTpSlTs, test_restart_market_order) {
  // 构建test user stub
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(user);

  // 构建other user stub
  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(other_user);

  ExecSetRiskId(user);
  ExecSetRiskId(other_user);

  // 行情校准: 校准mark price($10,000)
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 行情校准: 校准last price($10,000)
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // user挂单
  biz::order_id_t buy_order_id;
  BaseUserCreateOrder(user, user_id, ESide::Buy, 90'000, 10000 * 1e4, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 90'000, 10000'0000, 90'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 90'000, 10000'0000, 90'000);
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Buy, 20'000, 10000'0000, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 20'000, 10000'0000, 110'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 20'000, 10000'0000, 110'000);

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  BaseUserSwithcTpslMode(user, user_id);

  std::string tpsl_parent_order_id;
  // 仓位上挂一个部分止盈订单并触发
  BaseCreatePartialTakeProfitMarketOrder(user, user_id, 11000'0000, 110'000, tpsl_parent_order_id);

  // 模拟一笔撮合成交回报,订单状态 new,订单qty 100
  {
    SendOrderToCross(user_id, 100'000, 11000'0000, 100, tpsl_parent_order_id, 110'000, EOrderType::Market,
                     ETimeInForce::ImmediateOrCancel, tpsl_parent_order_id);
    auto result = te->PopResult();
    EXPECT_NE(result.m_msg, nullptr);

    auto related_order = result.m_msg->futures_margin_result().related_orders();
    EXPECT_EQ(related_order.size(), 2);
    std::string old_tpsl_parent_order_id = tpsl_parent_order_id;
    for (auto& order : related_order) {
      auto order_id = order.order_id();
      if (order_id != old_tpsl_parent_order_id) {
        // 新生成的循环止盈止损订单
        EXPECT_EQ(order.tpsl_leaves_qty(), 110'000);
        EXPECT_EQ(order.order_status(), EOrderStatus::Triggered);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
        EXPECT_EQ(order.qty_x(), 110'000);
        tpsl_parent_order_id = order_id;
      } else {
        // 原先的订单被取消
        EXPECT_EQ(order.order_status(), EOrderStatus::Cancelled);
        EXPECT_EQ(order.cross_status(), ECrossStatus::Canceled);
        EXPECT_EQ(order.qty_x(), 100'000);
        EXPECT_EQ(order.tpsl_leaves_qty(), 110'000);
      }
    }
  }

  // 新的循环订单触发
  {
    auto time_event = std::make_shared<event::TimerEvent>(0);
    int try_count = 0;
    while (try_count++ < 5) {
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);
      auto ao_result1 = te->PopResult();
      if (ao_result1.m_msg == nullptr) {
        continue;
      }
      // 订单发撮合
      {
        ASSERT_EQ(ao_result1.m_msg->futures_margin_result().related_orders().size(), 1);
        for (auto& order : ao_result1.m_msg->futures_margin_result().related_orders()) {
          auto order_status = order.order_status();
          if (order_status == EOrderStatus::Triggered) {
            EXPECT_NE(order.tpsl_parent_order_id(), tpsl_parent_order_id);
            EXPECT_EQ(order.order_id(), tpsl_parent_order_id);
            EXPECT_EQ(order.qty_x(), 110'000);
            EXPECT_EQ(order.tpsl_leaves_qty(), 110'000);
            EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
            EXPECT_EQ(order.order_type(), EOrderType::Market);
            EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);
          } else {
            EXPECT_TRUE(false);
          }
        }
      }
      // 撮合回报
      {
        auto ao_result = te->PopResult();
        auto related_order = ao_result.m_msg->futures_margin_result().related_orders();
        EXPECT_EQ(related_order.size(), 2);
        std::string old_tpsl_parent_order_id = tpsl_parent_order_id;
        for (auto& order : related_order) {
          auto order_id = order.order_id();
          EXPECT_EQ(order.order_type(), EOrderType::Market);
          EXPECT_EQ(order.stop_order_type(), EStopOrderType::PartialTakeProfit);
          if (order_id != old_tpsl_parent_order_id) {
            // 新生成的循环止盈止损订单
            EXPECT_EQ(order.tpsl_leaves_qty(), 110'000);
            EXPECT_EQ(order.order_status(), EOrderStatus::Triggered);
            EXPECT_EQ(order.cross_status(), ECrossStatus::Init);
            EXPECT_EQ(order.qty_x(), 110'000);
            tpsl_parent_order_id = order_id;
          } else {
            // 原先的订单被取消
            EXPECT_EQ(order.order_status(), EOrderStatus::Cancelled);
            EXPECT_EQ(order.cross_status(), ECrossStatus::Canceled);
            EXPECT_EQ(order.qty_x(), 100'000);
            EXPECT_EQ(order.tpsl_leaves_qty(), 110'000);
          }
        }
      }
      break;
    }
  }
}

/*
 * 本UT用于模拟,拆单发生触发失败的场景
 * 用户切换到逐仓
 * 用户创建仓位110
 * 用户设置一个部分止盈订单,触发价格差异仓位的破产价格
 * 止盈订单触发,因为订单价格差异破产价格,所以触发失败,导致母订单被取消
 */
TEST_F(TriggerToActiveTpSlTs, test_limit_order_trigger_failed) {
  // 构建test user stub
  biz::user_id_t user_id = 653106;
  stub user(te, user_id);
  SwitchAccountMode(user_id, EAccountMode::Isolated);
  // 设置逐仓10x杠杆+充钱
  Deposit(user);

  // 构建other user stub
  biz::user_id_t other_user_id = 111111;
  stub other_user(te, other_user_id);
  // 设置逐仓10x杠杆+充钱
  Deposit(other_user);

  ExecSetRiskId(user);
  ExecSetRiskId(other_user);

  // 行情校准: 校准mark price($10,000)
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 行情校准: 校准last price($10,000)
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // user挂单
  biz::order_id_t buy_order_id;
  BaseUserCreateOrder(user, user_id, ESide::Buy, 90'000, 10000 * 1e4, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 90'000, 10000'0000, 90'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 90'000, 10000'0000, 90'000);
  // user挂单
  BaseUserCreateOrder(user, user_id, ESide::Buy, 20'000, 10000'0000, buy_order_id);
  // other user挂单
  OtherUserCreateOrderAndDeal(other_user, other_user_id, ESide::Sell, 20'000, 10000'0000, 110'000);
  // user的成交记录
  BaseUserMakeFill(user_id, ESide::Buy, 20'000, 10000'0000, 110'000);

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  BaseUserSwithcTpslMode(user, user_id);

  std::string tpsl_parent_order_id;
  // 仓位上挂一个部分止盈订单并触发
  BaseCreatePartialTakeProfitOrder(user, user_id, 11000'0000, 1100'0000, 110'000, tpsl_parent_order_id);

  // 触发部分止盈止损条件单
  {
    underlying_price = bbase::decimal::Decimal<>("11000");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新市场价格
      te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截

      // 给Trigger发一个timer事件, 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格,尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发,由于第一笔触发失败,这里连同母订单被取消,总共2笔订单
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 2);
      for (auto& triggered_so : triggered_result.m_msg->futures_margin_result().related_orders()) {
        auto qty = triggered_so.qty_x();
        if (qty == 110'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Deactivated);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Aborted);
          EXPECT_EQ(triggered_so.order_id(), tpsl_parent_order_id);
        } else if (qty == 100'000) {
          EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Rejected);
          EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
          EXPECT_EQ(triggered_so.tpsl_parent_order_id(), tpsl_parent_order_id);
        } else {
          EXPECT_TRUE(false);
        }
      }
      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}
