#include "test/biz/trade/future/feature/inverse/tpsl/tpsl_protect_test.hpp"

#include <bbase/common/decimal/decimal.hpp>
#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>
#include <unordered_map>

#include "application/global_var_manager.hpp"
#include "data/error/error.hpp"
#include "data/type/biz_type.hpp"
#include "enums/ecrossstatus/cross_status.pb.h"
#include "enums/eorderstatus/order_status.pb.h"
#include "lib/msg_builder.hpp"
#include "lib/stub.hpp"
#include "src/biz_worker/service/trade/futures/modules/tpsltsbiz/adjust_tp_sl_ts_biz.h"
#include "src/biz_worker/service/trade/futures/modules/tpsltsbiz/entity/raw_tp_sl_ts.h"
#include "src/biz_worker/utils/calc_util.hpp"
#include "src/data/enum.hpp"

std::shared_ptr<tmock::CTradeAppMock> iTpslProtectTest::te;
/*
设置tpsl价差保护，然后尝试触发，不会触发条件单，推送条件单信息
*/
std::shared_ptr<event::OnUpdateConfigEvent> iTpslProtectTest::MockConfig(bool gray, int risk_limit_version) {
  auto sc_ev = std::make_shared<event::OnUpdateConfigEvent>(0, event::EventType::kEventSymbolConfig,
                                                            event::EventSourceType::kUnknown,
                                                            event::EventDispatchType::kBroadcastToWorker);

  config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr()->InitMockData(gray, risk_limit_version);
  config::getTlsCfgMgrRaw()->spot_client_sptr()->InitMockData();
  sc_ev->set_symbol_config_mgr(config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr());

  return sc_ev;
}

int32_t iTpslProtectTest::Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t iTpslProtectTest::SwitchAccountMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t iTpslProtectTest::OpenAccount(int64_t uid) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::MergeSettingPosition);

  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t iTpslProtectTest::SetSpotCollateralCoin(int64_t uid, ECoin coin) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SetSpotCollateralCoin);
  unified_v_2_request_dto->mutable_req_header()->set_req_id("set_collateral_coin_mode_1001_1");
  unified_v_2_request_dto->mutable_req_header()->set_coin(coin);
  auto* req =
      unified_v_2_request_dto->mutable_settingsreqgroupbody()->mutable_set_spot_collateral_coin()->add_coinlist();
  req->set_coin(coin);
  req->set_enable(true);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

TEST_F(iTpslProtectTest, tpsl_protect_test) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);
  // 使用设置接口开启请求
  {
    FutureOneOfSetTpslProtectBuilder set_tpsl_protect(user_id1, coin, symbol, true, false);
    auto resp1 = user1.process(set_tpsl_protect.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto ret_setting = result1.m_msg->asset_margin_result().per_user_setting_data();
    NewLine();
    ASSERT_EQ(ret_setting.is_tpsl_price_protect(), false);
    ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).has_tpsl_price_protect(),
              true);
    ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).tpsl_price_protect(), true);
  }

  auto user_store1 = te->GetUserStore(user_id1);

  // 成交一个仓位，然后设置止盈
  biz::order_id_t po{};
  {
    // uid 1
    {
      auto order_id = te->GenUUID();
      ESide side = ESide::Buy;

      biz::size_x_t qty = 10;
      biz::price_x_t price = *********;

      FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user_id1, coin);

      auto resp1 = user1.process(create_build.Build());
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
      ASSERT_NE(orderCheck.m_msg, nullptr);

      EXPECT_TRUE(user_store1->HasFutureOccupyCostOrder());
      EXPECT_FALSE(user_store1->HasFuturePosition());
      EXPECT_TRUE(user_store1->all_active_future_order_symbol_.contains(symbol));
    }
    NewLine();
    // uid2
    {
      auto order_id = te->GenUUID();
      ESide side = ESide::Sell;
      biz::size_x_t qty = 10;
      biz::price_x_t price = 10000'0000;

      FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user_id2, coin);
      auto resp1 = user2.process(create_build.Build());
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
    }
    NewLine();
    // 成交回报
    {
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      EXPECT_EQ(result1.m_msg->header().user_id(), user_id1);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto positon = orderCheck2.RefRelatedPosition(0);
      positon.CheckSize(10);
      EXPECT_EQ(positon.m_msg->side(), ESide::Buy);

      EXPECT_FALSE(user_store1->HasFutureOccupyCostOrder());
      EXPECT_TRUE(user_store1->HasFuturePosition());
      EXPECT_TRUE(user_store1->all_active_future_position_symbol_.contains(symbol));
    }

    // 新建止盈
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 10001'0000, ETriggerBy::UNKNOWN,
                            -1, ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", "", 0, ETpSlMode::Full, EOrderType::UNKNOWN,
                            EOrderType::UNKNOWN, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);

      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      po = order.m_msg->order_id();
      order.ChecckTpSlOrder(EStopOrderType::TakeProfit, ETriggerBy::LastPrice, EOrderType::Market, 10001'0000);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::LastPrice, 10001'0000, ETriggerBy::UNKNOWN, 0, 0, 0);

      // 止盈止损单不占成本
      EXPECT_FALSE(user_store1->HasFutureOccupyCostOrder());
      EXPECT_TRUE(user_store1->HasFuturePosition());
      EXPECT_TRUE(user_store1->all_active_future_position_symbol_.contains(symbol));
    }
  }

  // 尝试触发
  {
    auto underlying_price = bbase::decimal::Decimal<>("10151");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      // te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      // underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截
      auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
      auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
      trigger_info->symbol_ = symbol;
      trigger_info->trigger_by_ = ETriggerBy::LastPrice;
      trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
      trigger_info->trigger_src_seq_ = 1;
      trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
      quote_update_event->SetRawTriggerInfo(trigger_info);
      quote_update_event->set_type(event::kEventSyncFuturesTrigger);
      std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      // auto time_event = std::make_shared<event::TimerEvent>(0);
      // time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      // time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      // ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      // ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Untriggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
      EXPECT_NE(triggered_so.tpsl_price_protect_time_e9(), 0);
      //   const auto& old_order_id = triggered_so.order_id();

      NewLine();
      std::cout << triggered_result.m_msg->DebugString() << std::endl;
      NewLine();

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }

  // 价保关闭
  {
    FutureOneOfSetTpslProtectBuilder set_tpsl_protect(user_id1, coin, symbol, false, false);
    auto resp1 = user1.process(set_tpsl_protect.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto ret_setting = result1.m_msg->asset_margin_result().per_user_setting_data();
    NewLine();
    ASSERT_EQ(ret_setting.is_tpsl_price_protect(), false);
    ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).has_tpsl_price_protect(),
              true);
    ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).tpsl_price_protect(),
              false);
  }

  // 可以触发
  {
    auto underlying_price = bbase::decimal::Decimal<>("10152");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      // te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      // underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截
      auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
      auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
      trigger_info->symbol_ = symbol;
      trigger_info->trigger_by_ = ETriggerBy::LastPrice;
      trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
      trigger_info->trigger_src_seq_ = 1;
      trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
      quote_update_event->SetRawTriggerInfo(trigger_info);
      quote_update_event->set_type(event::kEventSyncFuturesTrigger);
      std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      // auto time_event = std::make_shared<event::TimerEvent>(0);
      // time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      // time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      // ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      // ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
      EXPECT_EQ(triggered_so.tpsl_price_protect_time_e9(), 0);
      //   const auto& old_order_id = triggered_so.order_id();

      NewLine();
      std::cout << triggered_result.m_msg->DebugString() << std::endl;
      NewLine();

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }

  // 触发条件单生成占成本活动单
  EXPECT_TRUE(user_store1->HasFutureOccupyCostOrder());
  EXPECT_TRUE(user_store1->HasFuturePosition());
  EXPECT_TRUE(user_store1->all_active_future_position_symbol_.contains(symbol));
  EXPECT_TRUE(user_store1->all_active_future_order_symbol_.contains(symbol));
}

/*
account setting设置tpsl价差保护，然后尝试触发，不会触发条件单，推送条件单信息
*/
TEST_F(iTpslProtectTest, tpsl_protect_test_account) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);
  // 使用设置接口开启请求
  {
    FutureOneOfSetTpslProtectBuilder set_tpsl_protect(user_id1, coin, symbol, true, false);
    auto resp1 = user1.process(set_tpsl_protect.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto ret_setting = result1.m_msg->asset_margin_result().per_user_setting_data();
    NewLine();
    ASSERT_EQ(ret_setting.is_tpsl_price_protect(), false);
  }

  // 成交一个仓位，然后设置止盈
  biz::order_id_t po{};
  {
    // uid 1
    {
      auto order_id = te->GenUUID();
      ESide side = ESide::Buy;

      biz::size_x_t qty = 10;
      biz::price_x_t price = *********;

      FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user_id1, coin);

      auto resp1 = user1.process(create_build.Build());
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
      ASSERT_NE(orderCheck.m_msg, nullptr);
    }
    NewLine();
    // uid2
    {
      auto order_id = te->GenUUID();
      ESide side = ESide::Sell;
      biz::size_x_t qty = 10;
      biz::price_x_t price = 10000'0000;

      FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user_id2, coin);
      auto resp1 = user2.process(create_build.Build());
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
    }
    NewLine();
    // 成交回报
    {
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      EXPECT_EQ(result1.m_msg->header().user_id(), user_id1);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto positon = orderCheck2.RefRelatedPosition(0);
      positon.CheckSize(10);
      EXPECT_EQ(positon.m_msg->side(), ESide::Buy);
    }

    // 新建止盈
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 10001'0000, ETriggerBy::UNKNOWN,
                            -1, ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", "", 0, ETpSlMode::Full, EOrderType::UNKNOWN,
                            EOrderType::UNKNOWN, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);

      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      po = order.m_msg->order_id();
      order.ChecckTpSlOrder(EStopOrderType::TakeProfit, ETriggerBy::LastPrice, EOrderType::Market, 10001'0000);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::LastPrice, 10001'0000, ETriggerBy::UNKNOWN, 0, 0, 0);
    }
  }

  // 尝试触发
  {
    auto underlying_price = bbase::decimal::Decimal<>("10151");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 1) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      // te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      // underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截
      auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
      auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
      trigger_info->symbol_ = symbol;
      trigger_info->trigger_by_ = ETriggerBy::LastPrice;
      trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
      trigger_info->trigger_src_seq_ = 1;
      trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
      quote_update_event->SetRawTriggerInfo(trigger_info);
      quote_update_event->set_type(event::kEventSyncFuturesTrigger);
      std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      // auto time_event = std::make_shared<event::TimerEvent>(0);
      // time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      // time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      // ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      // ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Untriggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
      EXPECT_NE(triggered_so.tpsl_price_protect_time_e9(), 0);
      //   const auto& old_order_id = triggered_so.order_id();

      NewLine();
      std::cout << triggered_result.m_msg->DebugString() << std::endl;
      NewLine();

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

TEST_F(iTpslProtectTest, tpsl_protect_test_batch) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);
  // 使用设置接口开启请求
  // {
  //   FutureOneOfSetTpslProtectBuilder set_tpsl_protect(user_id1, coin, symbol, true, false);
  //   auto resp1 = user1.process(set_tpsl_protect.Build());
  //   auto result1 = te->PopResult();
  //   ASSERT_NE(result1.m_msg.get(), nullptr);
  //   auto ret_setting = result1.m_msg->asset_margin_result().per_user_setting_data();
  //   NewLine();
  //   ASSERT_EQ(ret_setting.is_tpsl_price_protect(), false);
  //   ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).has_tpsl_price_protect(),
  //             true);
  //   ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).tpsl_price_protect(),
  //   true);
  // }

  // 成交一个仓位，然后设置止盈
  biz::order_id_t po{};
  {
    // uid 1
    {
      auto order_id = te->GenUUID();
      ESide side = ESide::Buy;

      biz::size_x_t qty = 10;
      biz::price_x_t price = *********;

      FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user_id1, coin);

      auto resp1 = user1.process(create_build.Build());
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
      ASSERT_NE(orderCheck.m_msg, nullptr);
    }
    NewLine();
    // uid2
    {
      auto order_id = te->GenUUID();
      ESide side = ESide::Sell;
      biz::size_x_t qty = 10;
      biz::price_x_t price = 10000'0000;

      FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user_id2, coin);
      auto resp1 = user2.process(create_build.Build());
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
    }
    NewLine();
    // 成交回报
    {
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      EXPECT_EQ(result1.m_msg->header().user_id(), user_id1);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto positon = orderCheck2.RefRelatedPosition(0);
      positon.CheckSize(10);
      EXPECT_EQ(positon.m_msg->side(), ESide::Buy);
    }

    // 新建止盈
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 10001'0000, ETriggerBy::UNKNOWN,
                            -1, ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", "", 0, ETpSlMode::Full, EOrderType::UNKNOWN,
                            EOrderType::UNKNOWN, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);

      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      po = order.m_msg->order_id();
      order.ChecckTpSlOrder(EStopOrderType::TakeProfit, ETriggerBy::LastPrice, EOrderType::Market, 10001'0000);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::LastPrice, 10001'0000, ETriggerBy::UNKNOWN, 0, 0, 0);
    }
  }

  // 尝试触发
  {
    auto underlying_price = bbase::decimal::Decimal<>("10151");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次
    std::unordered_map<ESymbol, bool> batch_setting_map;
    batch_setting_map.emplace(static_cast<ESymbol>(symbol), true);
    auto&& batch_symbol_e =
        std::make_shared<event::BatchSymbolProtectSettingUpdateEvent>(0, user_id1, batch_setting_map);
    std::int32_t rt1 = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(batch_symbol_e);
    ASSERT_EQ(rt1, error::kErrorCodeSuccess);

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      // te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      // underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截
      auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
      auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
      trigger_info->symbol_ = symbol;
      trigger_info->trigger_by_ = ETriggerBy::LastPrice;
      trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
      trigger_info->trigger_src_seq_ = 1;
      trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
      quote_update_event->SetRawTriggerInfo(trigger_info);
      quote_update_event->set_type(event::kEventSyncFuturesTrigger);
      std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      // auto time_event = std::make_shared<event::TimerEvent>(0);
      // time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      // time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      // ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      // ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Untriggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
      EXPECT_NE(triggered_so.tpsl_price_protect_time_e9(), 0);
      //   const auto& old_order_id = triggered_so.order_id();

      NewLine();
      std::cout << triggered_result.m_msg->DebugString() << std::endl;
      NewLine();

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }

  NewLine();
  // 再次触发，由于在间隔内，无事件
  {
    auto underlying_price = bbase::decimal::Decimal<>("10152");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 1) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      // te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      // underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截
      auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
      auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
      trigger_info->symbol_ = symbol;
      trigger_info->trigger_by_ = ETriggerBy::LastPrice;
      trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
      trigger_info->trigger_src_seq_ = 1;
      trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
      quote_update_event->SetRawTriggerInfo(trigger_info);
      quote_update_event->set_type(event::kEventSyncFuturesTrigger);
      std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      // auto time_event = std::make_shared<event::TimerEvent>(0);
      // time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      // time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      // ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      // ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Untriggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
      EXPECT_NE(triggered_so.tpsl_price_protect_time_e9(), 0);
      //   const auto& old_order_id = triggered_so.order_id();

      NewLine();
      std::cout << triggered_result.m_msg->DebugString() << std::endl;
      NewLine();

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res == false);
  }
  // 再次触发，超出间隔，有事件
  {
    auto underlying_price = bbase::decimal::Decimal<>("10153");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次
    application::GlobalVarManager::Instance().set_futures_tpsl_protect_interval_s(1);
    while (!checked && try_times < 4) {
      sleep(1);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      // te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      // underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截
      auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
      auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
      trigger_info->symbol_ = symbol;
      trigger_info->trigger_by_ = ETriggerBy::LastPrice;
      trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
      trigger_info->trigger_src_seq_ = 1;
      trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
      quote_update_event->SetRawTriggerInfo(trigger_info);
      quote_update_event->set_type(event::kEventSyncFuturesTrigger);
      std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      // auto time_event = std::make_shared<event::TimerEvent>(0);
      // time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      // time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      // ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      // ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Untriggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
      EXPECT_NE(triggered_so.tpsl_price_protect_time_e9(), 0);
      //   const auto& old_order_id = triggered_so.order_id();

      NewLine();
      std::cout << triggered_result.m_msg->DebugString() << std::endl;
      NewLine();

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

TEST_F(iTpslProtectTest, calcdiff) {
  ASSERT_EQ(biz::CalcUtil::CalcDiffPercent(25357, 25744).GreaterThan(bbase::decimal::Decimal<>(150, -4)), true);
}

// 价差超过时进行保护，价差恢复时，触发
TEST_F(iTpslProtectTest, tpsl_protect_test_diff) {
  auto sc_ev = MockConfig(false, 2);
  te->pipeline()->ring_buffers(worker::WorkerType::kPreWorker)->Write(sc_ev, true);
  // 这里之后触发的订单应该要小于1.5%

  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);
  // 使用设置接口开启请求
  {
    FutureOneOfSetTpslProtectBuilder set_tpsl_protect(user_id1, coin, symbol, true, false);
    auto resp1 = user1.process(set_tpsl_protect.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto ret_setting = result1.m_msg->asset_margin_result().per_user_setting_data();
    NewLine();
    ASSERT_EQ(ret_setting.is_tpsl_price_protect(), false);
    ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).has_tpsl_price_protect(),
              true);
    ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).tpsl_price_protect(), true);
  }

  // 成交一个仓位，然后设置止盈
  biz::order_id_t po{};
  {
    // uid 1
    {
      auto order_id = te->GenUUID();
      ESide side = ESide::Buy;

      biz::size_x_t qty = 10;
      biz::price_x_t price = *********;

      FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user_id1, coin);

      auto resp1 = user1.process(create_build.Build());
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
      ASSERT_NE(orderCheck.m_msg, nullptr);
    }
    NewLine();
    // uid2
    {
      auto order_id = te->GenUUID();
      ESide side = ESide::Sell;
      biz::size_x_t qty = 10;
      biz::price_x_t price = 10000'0000;

      FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user_id2, coin);
      auto resp1 = user2.process(create_build.Build());
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
    }
    NewLine();
    // 成交回报
    {
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      EXPECT_EQ(result1.m_msg->header().user_id(), user_id1);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto positon = orderCheck2.RefRelatedPosition(0);
      positon.CheckSize(10);
      EXPECT_EQ(positon.m_msg->side(), ESide::Buy);
    }

    // 新建止盈
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 10001'0000, ETriggerBy::UNKNOWN,
                            -1, ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", "", 0, ETpSlMode::Full, EOrderType::UNKNOWN,
                            EOrderType::UNKNOWN, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);

      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      po = order.m_msg->order_id();
      order.ChecckTpSlOrder(EStopOrderType::TakeProfit, ETriggerBy::LastPrice, EOrderType::Market, 10001'0000);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::LastPrice, 10001'0000, ETriggerBy::UNKNOWN, 0, 0, 0);
    }
  }

  // 尝试触发
  {
    auto underlying_price = bbase::decimal::Decimal<>("10151");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      // te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      // underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截
      auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
      auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
      trigger_info->symbol_ = symbol;
      trigger_info->trigger_by_ = ETriggerBy::LastPrice;
      trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
      trigger_info->trigger_src_seq_ = 1;
      trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
      quote_update_event->SetRawTriggerInfo(trigger_info);
      quote_update_event->set_type(event::kEventSyncFuturesTrigger);
      std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      // auto time_event = std::make_shared<event::TimerEvent>(0);
      // time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      // time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      // ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      // ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Untriggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
      EXPECT_NE(triggered_so.tpsl_price_protect_time_e9(), 0);
      //   const auto& old_order_id = triggered_so.order_id();

      NewLine();
      std::cout << triggered_result.m_msg->DebugString() << std::endl;
      NewLine();

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }

  // 尝试触发, 价格回来了能触发
  {
    auto underlying_price = bbase::decimal::Decimal<>("10001");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      // te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      // underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截
      auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
      auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
      trigger_info->symbol_ = symbol;
      trigger_info->trigger_by_ = ETriggerBy::LastPrice;
      trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
      trigger_info->trigger_src_seq_ = 1;
      trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
      quote_update_event->SetRawTriggerInfo(trigger_info);
      quote_update_event->set_type(event::kEventSyncFuturesTrigger);
      std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      // auto time_event = std::make_shared<event::TimerEvent>(0);
      // time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      // time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      // ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      // ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
      EXPECT_EQ(triggered_so.tpsl_price_protect_time_e9(), 0);
      //   const auto& old_order_id = triggered_so.order_id();

      NewLine();
      std::cout << triggered_result.m_msg->DebugString() << std::endl;
      NewLine();

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

// 使用别的触发类型（markprice）不受保护
TEST_F(iTpslProtectTest, tpsl_protect_test_mark) {
  auto sc_ev = MockConfig(false, 2);
  te->pipeline()->ring_buffers(worker::WorkerType::kPreWorker)->Write(sc_ev, true);
  // 这里之后触发的订单应该要小于1.5%

  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);
  // 使用设置接口开启请求
  {
    FutureOneOfSetTpslProtectBuilder set_tpsl_protect(user_id1, coin, symbol, true, false);
    auto resp1 = user1.process(set_tpsl_protect.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto ret_setting = result1.m_msg->asset_margin_result().per_user_setting_data();
    NewLine();
    ASSERT_EQ(ret_setting.is_tpsl_price_protect(), false);
    ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).has_tpsl_price_protect(),
              true);
    ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).tpsl_price_protect(), true);
  }

  // 成交一个仓位，然后设置止盈
  biz::order_id_t po{};
  {
    // uid 1
    {
      auto order_id = te->GenUUID();
      ESide side = ESide::Buy;

      biz::size_x_t qty = 10;
      biz::price_x_t price = *********;

      FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user_id1, coin);

      auto resp1 = user1.process(create_build.Build());
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
      ASSERT_NE(orderCheck.m_msg, nullptr);
    }
    NewLine();
    // uid2
    {
      auto order_id = te->GenUUID();
      ESide side = ESide::Sell;
      biz::size_x_t qty = 10;
      biz::price_x_t price = 10000'0000;

      FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user_id2, coin);
      auto resp1 = user2.process(create_build.Build());
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
    }
    NewLine();
    // 成交回报
    {
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      EXPECT_EQ(result1.m_msg->header().user_id(), user_id1);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto positon = orderCheck2.RefRelatedPosition(0);
      positon.CheckSize(10);
      EXPECT_EQ(positon.m_msg->side(), ESide::Buy);
    }

    // 新建止盈
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::MarkPrice, 10001'0000, ETriggerBy::UNKNOWN,
                            -1, ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", "", 0, ETpSlMode::Full, EOrderType::UNKNOWN,
                            EOrderType::UNKNOWN, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);

      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      po = order.m_msg->order_id();
      order.ChecckTpSlOrder(EStopOrderType::TakeProfit, ETriggerBy::MarkPrice, EOrderType::Market, 10001'0000);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::MarkPrice, 10001'0000, ETriggerBy::UNKNOWN, 0, 0, 0);
    }
  }

  // 尝试触发, 能触发
  {
    auto underlying_price = bbase::decimal::Decimal<>("10151");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      // te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      // underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截
      auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
      auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
      trigger_info->symbol_ = symbol;
      trigger_info->trigger_by_ = ETriggerBy::MarkPrice;
      trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
      trigger_info->trigger_src_seq_ = 1;
      trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
      quote_update_event->SetRawTriggerInfo(trigger_info);
      quote_update_event->set_type(event::kEventSyncFuturesTrigger);
      std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      // auto time_event = std::make_shared<event::TimerEvent>(0);
      // time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      // time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      // ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      // ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Triggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
      EXPECT_EQ(triggered_so.tpsl_price_protect_time_e9(), 0);
      //   const auto& old_order_id = triggered_so.order_id();

      NewLine();
      std::cout << triggered_result.m_msg->DebugString() << std::endl;
      NewLine();

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }
}

/**
 * 改单撤单时，price protect time重置
 */
TEST_F(iTpslProtectTest, tpsl_protect_time_reset) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);
  // 使用设置接口开启请求
  {
    FutureOneOfSetTpslProtectBuilder set_tpsl_protect(user_id1, coin, symbol, true, false);
    auto resp1 = user1.process(set_tpsl_protect.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto ret_setting = result1.m_msg->asset_margin_result().per_user_setting_data();
    NewLine();
    ASSERT_EQ(ret_setting.is_tpsl_price_protect(), false);
    ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).has_tpsl_price_protect(),
              true);
    ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).tpsl_price_protect(), true);
  }

  // 成交一个仓位，然后设置止盈
  biz::order_id_t po{};
  {
    // uid 1
    {
      auto order_id = te->GenUUID();
      ESide side = ESide::Buy;

      biz::size_x_t qty = 10;
      biz::price_x_t price = *********;

      FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user_id1, coin);

      auto resp1 = user1.process(create_build.Build());
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
      ASSERT_NE(orderCheck.m_msg, nullptr);
    }
    NewLine();
    // uid2
    {
      auto order_id = te->GenUUID();
      ESide side = ESide::Sell;
      biz::size_x_t qty = 10;
      biz::price_x_t price = 10000'0000;

      FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user_id2, coin);
      auto resp1 = user2.process(create_build.Build());
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
    }
    NewLine();
    // 成交回报
    {
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      EXPECT_EQ(result1.m_msg->header().user_id(), user_id1);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto positon = orderCheck2.RefRelatedPosition(0);
      positon.CheckSize(10);
      EXPECT_EQ(positon.m_msg->side(), ESide::Buy);
    }

    // 新建止盈
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 10001'0000, ETriggerBy::UNKNOWN,
                            -1, ETriggerBy::UNKNOWN, -1, -1, 100000, -1, "", "", 0, ETpSlMode::Partial,
                            EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);

      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      po = order.m_msg->order_id();
      order.ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::LastPrice, EOrderType::Market, 10001'0000);
    }
  }

  // 尝试触发
  {
    auto underlying_price = bbase::decimal::Decimal<>("10151");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      // te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      // underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截
      auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
      auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
      trigger_info->symbol_ = symbol;
      trigger_info->trigger_by_ = ETriggerBy::LastPrice;
      trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
      trigger_info->trigger_src_seq_ = 1;
      trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
      quote_update_event->SetRawTriggerInfo(trigger_info);
      quote_update_event->set_type(event::kEventSyncFuturesTrigger);
      std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      // auto time_event = std::make_shared<event::TimerEvent>(0);
      // time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      // time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      // ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      // ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Untriggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Init);
      EXPECT_NE(triggered_so.tpsl_price_protect_time_e9(), 0);
      //   const auto& old_order_id = triggered_so.order_id();

      NewLine();
      std::cout << triggered_result.m_msg->DebugString() << std::endl;
      NewLine();

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }

  // 调回不能触发的价格
  {
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
    auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
    trigger_info->symbol_ = symbol;
    trigger_info->trigger_by_ = ETriggerBy::LastPrice;
    trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
    trigger_info->trigger_src_seq_ = 1;
    trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
    quote_update_event->SetRawTriggerInfo(trigger_info);
    quote_update_event->set_type(event::kEventSyncFuturesTrigger);
    std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);
  }

  // 改下订单，保护时间重置
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 10002'0000, ETriggerBy::UNKNOWN, -1,
                          ETriggerBy::UNKNOWN, -1, -1, 100000, -1, std::string{po}, "", 0, ETpSlMode::Partial,
                          EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);

    auto orderCheck2 = result1.RefFutureMarginResult();
    auto order = orderCheck2.RefRelatedOrderByIndex(0);
    EXPECT_EQ(order.m_msg->tpsl_price_protect_time_e9(), 0);
    order.ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::LastPrice, EOrderType::Market, 10002'0000);
  }

  // 重置保护时间后又能保护
  {
    auto underlying_price = bbase::decimal::Decimal<>("10151");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次

    while (!checked && try_times < 4) {
      usleep(50 * 1000);

      // 更新标记价格
      // 模拟上涨触发(mark_price)9980 <= (trigger_price)9980
      // te->UpdateMarketData(symbol, underlying_price, exchange_rate);
      // underlying_price = underlying_price.Add(1);  // 每次需要变一下价格,不然会被trigger拦截
      auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
      auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
      trigger_info->symbol_ = symbol;
      trigger_info->trigger_by_ = ETriggerBy::LastPrice;
      trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
      trigger_info->trigger_src_seq_ = 1;
      trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
      quote_update_event->SetRawTriggerInfo(trigger_info);
      quote_update_event->set_type(event::kEventSyncFuturesTrigger);
      std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
      // auto time_event = std::make_shared<event::TimerEvent>(0);
      // time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      // time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      // ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, false);
      // ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 正确情况下order被触发
      auto triggered_result = te->PopResult();
      if (triggered_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续发比较价格，尝试触发
        ++try_times;
        continue;
      }

      // 拿到active_so的result, 说明触发成功, 直接check订单, 不需要更新行情数据了
      checked = true;

      // 活动单被触发
      ASSERT_EQ(triggered_result.m_msg->futures_margin_result().related_orders().size(), 1);
      auto triggered_so = triggered_result.m_msg->futures_margin_result().related_orders().at(0);
      EXPECT_EQ(triggered_so.order_status(), EOrderStatus::Untriggered);
      EXPECT_EQ(triggered_so.cross_status(), ECrossStatus::Replaced);
      EXPECT_NE(triggered_so.tpsl_price_protect_time_e9(), 0);
      //   const auto& old_order_id = triggered_so.order_id();

      NewLine();
      std::cout << triggered_result.m_msg->DebugString() << std::endl;
      NewLine();

      to_check_res = true;
    }
    ASSERT_TRUE(to_check_res);
  }

  // 取消订单，保护时间也重置
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 0, ETriggerBy::UNKNOWN, -1,
                          ETriggerBy::UNKNOWN, -1, -1, 100000, -1, std::string{po}, "", 0, ETpSlMode::Partial,
                          EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);

    auto orderCheck2 = result1.RefFutureMarginResult();
    auto order = orderCheck2.RefRelatedOrderByIndex(0);
    EXPECT_EQ(order.m_msg->tpsl_price_protect_time_e9(), 0);
    EXPECT_EQ(order.m_msg->order_status(), EOrderStatus::Deactivated);
    order.ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::LastPrice, EOrderType::Market, 10002'0000);
  }
}
