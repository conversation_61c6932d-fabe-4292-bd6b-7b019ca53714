#include "test/biz/trade/future/feature/inverse/tpsl/order_filled_carry_tpsl_test.hpp"

#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>

#include "data/error/error.hpp"
#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "lib/stub.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "src/data/enum.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

#define TestCrateAoCarryTpSl_PrintResult
std::shared_ptr<tmock::CTradeAppMock> iOrderFilledCarryTpslTest::te;
int32_t iOrderFilledCarryTpslTest::Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t iOrderFilledCarryTpslTest::SwitchAccountMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t iOrderFilledCarryTpslTest::OpenAccount(int64_t uid) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::MergeSettingPosition);

  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t iOrderFilledCarryTpslTest::SetSpotCollateralCoin(int64_t uid, ECoin coin) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SetSpotCollateralCoin);
  unified_v_2_request_dto->mutable_req_header()->set_req_id("set_collateral_coin_mode_1001_1");
  unified_v_2_request_dto->mutable_req_header()->set_coin(coin);
  auto* req =
      unified_v_2_request_dto->mutable_settingsreqgroupbody()->mutable_set_spot_collateral_coin()->add_coinlist();
  req->set_coin(coin);
  req->set_enable(true);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}
// internal/tests/tpslts/fulltpslts/createordercarrytpsl/create_order_carry_tp_sl_test.go
TEST_F(iOrderFilledCarryTpslTest, TestCrateAoCarryTpSl) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::UNKNOWN, 11000'0000,
                                  ETriggerBy::UNKNOWN, 9900'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 11000'0000, EOrderType::Market);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 9900'0000, EOrderType::Market);
    buy_order_id = orderCheck.m_msg->order_id();
  }

  // user_id2开仓 sell ，携带止盈止损，立即成交
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id2, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 11000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == order_id) {
        orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 9900'0000, EOrderType::Market);
        orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 11000'0000, EOrderType::Market)
            .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 11000'0000, EStopOrderType::StopLoss,
                                  EOrderType::Market);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 9900'0000, EStopOrderType::TakeProfit,
                                  EOrderType::Market);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 11000'0000, EOrderType::Market);
        orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 9900'0000, EOrderType::Market)
            .CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 9900'0000, EStopOrderType::StopLoss,
                                  EOrderType::Market, 100);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 11000'0000, EStopOrderType::TakeProfit,
                                  EOrderType::Market, 100);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 开仓做多: +100@$10,000 (第二次设置止盈止损)
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::UNKNOWN, 12000'0000,
                                  ETriggerBy::UNKNOWN, 9950'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 12000'0000, EOrderType::Market);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 9950'0000, EOrderType::Market);
    buy_order_id = orderCheck.m_msg->order_id();
  }

  // uid2，方向sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }

  // uid1 成交, 止盈止损会被第二次成交订单上携带的止盈止损更新
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 12000'0000, EOrderType::Market);
        orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 9950'0000, EOrderType::Market)
            .CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 9950'0000, EStopOrderType::StopLoss,
                                  EOrderType::Market, 200);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 12000'0000, EStopOrderType::TakeProfit,
                                  EOrderType::Market, 200);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
  // 平仓100@$10,000 检查是否follow qty
  // user_id1，方向sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2，方向Buy，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }

  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 9950'0000, EStopOrderType::StopLoss,
                                  EOrderType::Market, 100);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 12000'0000, EStopOrderType::TakeProfit,
                                  EOrderType::Market, 100);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 平仓100@$10,000 平完 检查是否取消止盈止损订单
  // user_id1，方向sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2，方向Buy，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }

  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 9950'0000, EStopOrderType::StopLoss,
                            EOrderType::Market, 100)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 12000'0000, EStopOrderType::TakeProfit,
                            EOrderType::Market, 100)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 开仓做多: +30 + 40 + 30 @$10,000 开仓单携带止盈止损 被一次性吃完，观察是否正确生成最后一笔成交订单的止盈止损设置
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 30;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::UNKNOWN, 11000'0000,
                                  ETriggerBy::UNKNOWN, 9900'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 11000'0000, EOrderType::Market);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 9900'0000, EOrderType::Market);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 40;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::UNKNOWN, 11001'0000,
                                  ETriggerBy::UNKNOWN, 9901'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 11001'0000, EOrderType::Market);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 9901'0000, EOrderType::Market);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 30;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::UNKNOWN, 11002'0000,
                                  ETriggerBy::UNKNOWN, 9902'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 11002'0000, EOrderType::Market);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 9902'0000, EOrderType::Market);
    buy_order_id = orderCheck.m_msg->order_id();
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
  }
  // user_id2，方向Sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 5);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_status() == EOrderStatus::Filled) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 9902'0000, EStopOrderType::StopLoss,
                            EOrderType::Market, 100)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 11002'0000, EStopOrderType::TakeProfit,
                            EOrderType::Market, 100)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::LastPrice, 11002'0000, ETriggerBy::LastPrice, 9902'0000, 0, 0);
  }

  // 平仓100@$10,000
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    buy_order_id = orderCheck.m_msg->order_id();
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::LastPrice, 11002'0000, ETriggerBy::LastPrice, 9902'0000, 0, 0);
  }
  // user_id2，方向Buy，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }

  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 9902'0000, EStopOrderType::StopLoss,
                            EOrderType::Market, 100)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 11002'0000, EStopOrderType::TakeProfit,
                            EOrderType::Market, 100)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
  }

  // // 开仓做多: +100@$10,000 开仓单携带止盈止损
  // {
  //   ASSERT_RH(r2.Req(r2.GenEvCreateOrderLimitBuyCarryTpSl(10000 * 1e4, 100, 11000 * 1e4, 9900 * 1e4),
  //                    error::ErrorCode::kEcNoError));
  //   ASSERT_RH(r2.GetResp().Expect(true, false, 1, 101575, 99999899925));
  //   ASSERT_RH(r2.SendToCrossAndProcessOneXReq());
  //   ASSERT_EQ(r2.ReqMatchingResultFromXResp(), 0);
  //   ASSERT_RH(r2.Report(false, 1, 1, 0));

  //   ASSERT_RH(r1.Req(r1.GenEvCreateOrderLimitSellCarryTpSl(10000 * 1e4, 100, 9900 * 1e4, 11000 * 1e4),
  //                    error::ErrorCode::kEcNoError));
  //   ASSERT_RH(r1.GetResp().Expect(true, false, 1, 101425, 99999894075));

  //   ASSERT_RH(r1.SendToCrossAndProcessOneXReq());
  //   ASSERT_EQ(r1.ReqMatchingResultFromXResp(), 0);
  //   ASSERT_EQ(r2.ReqMatchingResultFromXResp(), 0);

  //   ASSERT_RH(r1.Report(false, 1, 3, 1));
  //   ASSERT_RH(r1.RefPz(0, true, r1.symbol(), EPositionIndex::Single)
  //                 .PCheckPzSizeValue(ESide::Sell, 100, 1000000)
  //                 .PCheckPzPc(100675, 0, 100675, 99999, 11049.5 * 1e4, 11111 * 1e4));

  //   ASSERT_RH(r2.Report(false, 1, 3, 1));
  //   ASSERT_RH(r2.RefPz(0, true, r2.symbol(), EPositionIndex::Single)
  //                 .PCheckPzSizeValue(ESide::Buy, 100, 1000000)
  //                 .PCheckPzPc(100825, 0, 100825, 100000, 9132.5 * 1e4, 9091 * 1e4));
  // }

  // // 平仓100@$10,000
  // {
  //   ASSERT_RH(r2.Req(r2.GenEvQueryPosition(), error::ErrorCode::kEcNoError));
  //   auto base_v = r2.GetResp().GetW()->wallet_version();
  //   ASSERT_RH(r2.Req(r2.GenEvCreateOrderLimitSell(10000 * 1e4, 100), error::ErrorCode::kEcNoError));
  //   ASSERT_RH(r2.GetResp().Expect(true, false, 1, 0, 99999900925));
  //   ASSERT_RH(r2.SendToCrossAndProcessOneXReq());
  //   ASSERT_EQ(r2.ReqMatchingResultFromXResp(), 0);
  //   ASSERT_RH(r2.Report(false, 1, 1, 0));

  //   ASSERT_RH(r1.Req(r1.GenEvCreateOrderLimitBuy(10000 * 1e4, 100), error::ErrorCode::kEcNoError));
  //   ASSERT_RH(r1.GetResp().Expect(true, false, 1, 0, 99999894075));
  //   ASSERT_RH(r1.SendToCrossAndProcessOneXReq());

  //   ASSERT_EQ(r1.ReqMatchingResultFromXResp(), 0);
  //   ASSERT_RH(r1.Report(false, 1, 3, 1));
  //   ASSERT_RH(r1.RefPz(0, true, r1.symbol(), EPositionIndex::Single)
  //                 .PCheckPzSizeValue(ESide::None, 0, 0));

  //   ASSERT_EQ(r2.ReqMatchingResultFromXResp(), 0);
  //   ASSERT_RH(r2.Report(false, 1, 3, 1));
  //   ASSERT_RH(r2.RefPz(0, true, r2.symbol(), EPositionIndex::Single)
  //                 .PCheckPzSizeValue(ESide::None, 0, 0));
  //   ASSERT_RH(r2.RefOrd(2, true, base_v + 6).OCheckOrdPxQty(0, 100, 0));
  // }

  // 止损价校验
  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 40;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::UNKNOWN, 11001'0000,
                                  ETriggerBy::UNKNOWN, 10010'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), error::ErrorCode::kErrorCodeSlShouldLtBase);
    auto result1 = te->PopResult(1000);
    ASSERT_EQ(result1.m_msg.get(), nullptr);
  }
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 40;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 9900'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), error::ErrorCode::kErrorCodeTpShouldGtBase);
    auto result1 = te->PopResult(1000);
    ASSERT_EQ(result1.m_msg.get(), nullptr);
  }
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 40;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::UNKNOWN, 10000'0000,
                                  ETriggerBy::UNKNOWN, 10000'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 40;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::UNKNOWN, 9900'0000,
                                  ETriggerBy::UNKNOWN, 9999'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), error::ErrorCode::kErrorCodeSlShouldGtBase);
    auto result1 = te->PopResult(1000);
    ASSERT_EQ(result1.m_msg.get(), nullptr);
  }

  std::cout << "TestCrateAoCarryTpSl exec finish  " << std::endl;
}

TEST_F(iOrderFilledCarryTpslTest, TestCreateActiveOrderCarryTpSl_v2) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 11000'0000, EOrderType::Limit, 11100'0000);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9900'0000, EOrderType::Limit, 9800'0000);
    buy_order_id = orderCheck.m_msg->order_id();
  }

  // uid2，方向sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Limit, 100)
            .CheckPrice(user_id1, 9800'0000);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Limit, 100)
            .CheckPrice(user_id1, 11100'0000);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
}

TEST_F(iOrderFilledCarryTpslTest, TestCreateActiveOrderCarryFullTpSl_v2) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    // 因为全量止盈止损，虽然携带了 止盈(止损)订单类型与价格，但没用，实际触发时，都是市价单
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Full, EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 11000'0000, EOrderType::Limit, 11100'0000);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9900'0000, EOrderType::Limit, 9800'0000);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // uid2，方向sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);

    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        // 全部止盈止损订单，都是市价单
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 9900'0000, EStopOrderType::StopLoss,
                            EOrderType::Market, 100)
            .CheckPrice(user_id1, 0);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        // 全部止盈止损订单，都是市价单
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Full, 11000'0000, EStopOrderType::TakeProfit,
                            EOrderType::Market, 100)
            .CheckPrice(user_id1, 0);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }
}

// partial tpsl和full tpsl，混合设置
TEST_F(iOrderFilledCarryTpslTest, TestCreateActiveOrderCarryMixedTpSl_v2) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);

  // 开仓做多: +50@$10,000 开仓单携带partial止盈止损
  std::string buy_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 50;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 11000'0000, EOrderType::Limit, 11100'0000);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9900'0000, EOrderType::Limit, 9800'0000);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // 开仓做多: +50@$10,000 开仓单携带full止盈止损
  std::string buy_order_id_full;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 50;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    // 因为全量止盈止损，虽然携带了 止盈(止损)订单类型与价格，但没用，实际触发时，都是市价单
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Full, EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 11000'0000, EOrderType::Limit, 11100'0000);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9900'0000, EOrderType::Limit, 9800'0000);
    buy_order_id_full = orderCheck.m_msg->order_id();
  }
  // uid2，方向sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }

  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 6);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (order.order_id() == buy_order_id_full) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Limit, 50)
            .CheckPrice(user_id1, 9800'0000);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Limit, 50)
            .CheckPrice(user_id1, 11100'0000);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        // 全部止盈止损订单，都是市价单
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 9900'0000, EStopOrderType::StopLoss,
                            EOrderType::Market, 100)
            .CheckPrice(user_id1, 0);
        EXPECT_EQ(order.parent_order_id(), buy_order_id_full);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        // 全部止盈止损订单，都是市价单
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Full, 11000'0000, EStopOrderType::TakeProfit,
                            EOrderType::Market, 100)
            .CheckPrice(user_id1, 0);
        EXPECT_EQ(order.parent_order_id(), buy_order_id_full);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, 0, 0);
  }

  // 仓位共100手
  // 平仓, 先平40手，full的量伴随变化为60，这时partial量(50)不变，
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 40;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    buy_order_id = orderCheck.m_msg->order_id();

    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, 0, 0);
  }
  // user_id2，方向Buy，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 40;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 9900'0000, EStopOrderType::StopLoss,
                            EOrderType::Market, 60)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Full, 11000'0000, EStopOrderType::TakeProfit,
                            EOrderType::Market, 60)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, 0, 0);
    position.CheckSize(60);
  }

  // 当前仓位60，再平20，那么full继续伴随变化，partial虽然size不足50，也不会变化
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    buy_order_id = orderCheck.m_msg->order_id();

    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, 0, 0);
  }
  // user_id2，方向Buy，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 20;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        // 全部止盈止损订单，都是市价单
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 9900'0000, EStopOrderType::StopLoss,
                            EOrderType::Market, 40)
            .CheckPrice(user_id1, 0);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        // 全部止盈止损订单，都是市价单
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Full, 11000'0000, EStopOrderType::TakeProfit,
                            EOrderType::Market, 40)
            .CheckPrice(user_id1, 0);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, 0, 0);
    position.CheckSize(40);
  }

  // 当前仓位40，平10，造成减仓的订单tpsl参数(partial)不生效
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 10;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 9800'0000, ETriggerBy::MarkPrice, 10500'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 9700'0000, 10300'0000);

    auto resp1 = user1.process(create_build.Build());
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 9800'0000, EOrderType::Limit, 9700'0000);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 10500'0000, EOrderType::Limit, 10300'0000);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 10;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    // 这里只有3笔order信息，原先平仓单携带的止盈止损信息没有生效
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 9900'0000, EStopOrderType::StopLoss,
                                  EOrderType::Market, 30);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Full, 11000'0000, EStopOrderType::TakeProfit,
                                  EOrderType::Market, 30);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, 0, 0);
    position.CheckSize(30);
  }

  // 当前仓位30，平10，造成减仓的订单tpsl参数(full)不生效
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 10;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::UNKNOWN, 9800'0000,
                                  ETriggerBy::UNKNOWN, 10600'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, 0, 0);

    auto resp1 = user1.process(create_build.Build());
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::LastPrice, 9800'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::LastPrice, 10600'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 10;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    // 这里有3笔order信息，原先平仓单携带的止盈止损信息没有生效，所以trigger price没有变化
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 9900'0000, EStopOrderType::StopLoss,
                            EOrderType::Market, 20)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Full, 11000'0000, EStopOrderType::TakeProfit,
                            EOrderType::Market, 20)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, 0, 0);
    position.CheckSize(20);
  }

  // 剩余20的仓位全平，所有tpsl订单取消
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    buy_order_id = orderCheck.m_msg->order_id();
  }
  // user_id2，方向Buy，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 20;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }

  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 5);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Limit, 50)
            .CheckPrice(user_id1, 9800'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Limit, 50)
            .CheckPrice(user_id1, 11100'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 9900'0000, EStopOrderType::StopLoss,
                            EOrderType::Market, 20)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Full, 11000'0000, EStopOrderType::TakeProfit,
                            EOrderType::Market, 20)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
  }
}

// partial fill加仓跟进exec qty. 同时full tpsl，正常对齐仓位，并使用最新的参数覆盖
TEST_F(iOrderFilledCarryTpslTest, TestCreateActiveOrderCarryTpSl_v2_partial_fill) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);

  // 开仓做多: +50@$10,000 开仓单携带partial止盈止损
  std::string buy_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 50;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 11000'0000, EOrderType::Limit, 11100'0000);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9900'0000, EOrderType::Limit, 9800'0000);
    buy_order_id = orderCheck.m_msg->order_id();
  }

  // partially fill qty=30
  // uid2，方向sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 30;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);
        EXPECT_EQ(order.cum_qty_x(), 30);
        EXPECT_EQ(order.leaves_qty_x(), 20);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Limit, 30)
            .CheckPrice(user_id1, 9800'0000);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Limit, 30)
            .CheckPrice(user_id1, 11100'0000);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(30);
  }

  // set full tp sl
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000'0000, ETriggerBy::LastPrice,
                          9900'0000, ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", "", 0, ETpSlMode::Full,
                          EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp1 = user1.process(create_build.Build());
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
#endif
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    // NewLine();
    // std::cout << "user1: " << user1.m_uid << ": " << resp1->DebugString() << std::endl;
    NewLine();
    std::cout << "user1 mod tpsl order: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(orderCheck2.m_msg->related_orders().size(), 2);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::TakeProfit) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 11000'0000, EStopOrderType::TakeProfit,
                          EOrderType::Market)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        cc.CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 9900'0000, EStopOrderType::StopLoss,
                          EOrderType::Market)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::LastPrice, 11000'0000, ETriggerBy::LastPrice, 9900'0000, 0, 0).CheckSize(30);
  }
  // fully fill qty = 50
  // uid2，方向sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 5);
    std::string full_tp_parent_order_id, full_sl_parent_order_id;
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
        EXPECT_EQ(order.cum_qty_x(), 50);
        EXPECT_EQ(order.leaves_qty_x(), 0);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Limit, 50)
            .CheckPrice(user_id1, 9800'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Limit, 50)
            .CheckPrice(user_id1, 11100'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 11000'0000, EStopOrderType::TakeProfit,
                            EOrderType::Market, 50)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
        full_tp_parent_order_id = order.parent_order_id();
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, 9900'0000, EStopOrderType::StopLoss,
                            EOrderType::Market, 50)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
        full_sl_parent_order_id = order.parent_order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::LastPrice, 11000'0000, ETriggerBy::LastPrice, 9900'0000, 0, 0);
    position.CheckSize(50);
    EXPECT_EQ(full_tp_parent_order_id, full_sl_parent_order_id);
  }

  // 最新的fulltpsl成交, 参数覆盖, 不影响partial tpsl
  std::string buy_order_id_full;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 50;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    // 因为全量止盈止损，虽然携带了 止盈(止损)订单类型与价格，但没用，实际触发时，都是市价单
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 10500'0000, ETriggerBy::MarkPrice, 9500'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Full, EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 10500'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9500'0000, EOrderType::Market, 0);
    buy_order_id_full = orderCheck.m_msg->order_id();
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::LastPrice, 11000'0000, ETriggerBy::LastPrice, 9900'0000, 0, 0);
    position.CheckSize(50);
  }
  // uid2，方向sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 50;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    std::string full_tp_parent_order_id, full_sl_parent_order_id;
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id_full) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
        EXPECT_EQ(order.cum_qty_x(), 50);
        EXPECT_EQ(order.leaves_qty_x(), 0);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Full, 10500'0000, EStopOrderType::TakeProfit,
                            EOrderType::Market, 100)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
        full_tp_parent_order_id = order.parent_order_id();
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 9500'0000, EStopOrderType::StopLoss,
                            EOrderType::Market, 100)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
        full_sl_parent_order_id = order.parent_order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::IndexPrice, 10500'0000, ETriggerBy::MarkPrice, 9500'0000, 0, 0);
    position.CheckSize(100);
    EXPECT_EQ(full_tp_parent_order_id, full_sl_parent_order_id);
  }
}

/**
 * 该UT来源于验证一个bug，https://jira.yijin.io/browse/BTE-3374
 * 该UT场景：一个仓位，携带全量止盈止损订单，此时一笔订单携带止损，成交了
 * 预期结果：仓位的止盈订单被取消了，止损订单被更新
 * 实际结果（有问题的场景）：此时 止盈止损订单都被取消了
 * UT 执行过程：
 * 1. 用户形成仓位，携带 full 的止盈与止损订单
 * 2. 该用户一笔携带 full 止损订单成交，增加仓位
 * 3. 验证结果是否符合预期
 */
TEST_F(iOrderFilledCarryTpslTest, TestCreateActiveOrderCarryTpSl_FullSl_Fill) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Full, EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 11000'0000, EOrderType::Limit, 11100'0000);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9900'0000, EOrderType::Limit, 9800'0000);
    buy_order_id = orderCheck.m_msg->order_id();
  }

  // uid2，方向sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 9900'0000, stop_order_type,
                                  EOrderType::Market, 100);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Full, 11000'0000, stop_order_type,
                                  EOrderType::Market, 100);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 10;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::UNKNOWN, -1, ETriggerBy::MarkPrice, 9800'0000, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
        ETpSlMode::Full, EOrderType::Limit, EOrderType::Market, -1, -1, 0, true);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9800'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }

  // uid2，方向sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 10;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }

  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user22222: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 9800'0000, stop_order_type, EOrderType::Market, 110)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Full, 11000'0000, stop_order_type, EOrderType::Market,
                            100)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
        EXPECT_NE(order.parent_order_id(), buy_order_id);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::MarkPrice, 9800'0000, 0, 0);
    position.CheckTpSlInfo(0, 1, 110, 110);
  }
}

/**
 * 该UT来源于验证一个bug，https://jira.yijin.io/browse/BTE-3374
 * 该UT场景：一个仓位，携带全量止盈止损订单，此时一笔订单携带止损，成交了
 * 预期结果：仓位的止盈订单被取消了，止损订单被更新
 * 实际结果（有问题的场景）：此时 止盈止损订单都被取消了
 * UT 执行过程：
 * 1. 用户形成仓位，携带 full 的止盈与止损订单
 * 2. 该用户一笔携带 full 止盈订单成交，增加仓位
 * 3. 验证结果是否符合预期
 */
TEST_F(iOrderFilledCarryTpslTest, TestCreateActiveOrderCarryTpSl_FullTp_Fill) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Full, EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 11000'0000, EOrderType::Limit, 11100'0000);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9900'0000, EOrderType::Limit, 9800'0000);
    buy_order_id = orderCheck.m_msg->order_id();
  }

  // uid2，方向sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 9900'0000, stop_order_type,
                                  EOrderType::Market, 100);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Full, 11000'0000, stop_order_type,
                                  EOrderType::Market, 100);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else {
        EXPECT_TRUE(false);
      }
    }
  }

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 10;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::MarkPrice, 12110'0000,
                                  ETriggerBy::UNKNOWN, -1, ETriggerBy::UNKNOWN, -1, -1, EStopOrderType::UNKNOWN,
                                  ETpSlMode::Full, EOrderType::Market, EOrderType::Market, -1, -1, 0, true);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user1: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::MarkPrice, 12110'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }

  // uid2，方向sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 10;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);

#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user2: " << user2.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
  }

  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
#ifdef TestCrateAoCarryTpSl_PrintResult
    NewLine();
    std::cout << "user22222: " << user1.m_uid << ": " << result1.m_msg->DebugString() << std::endl;
#endif
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 9900'0000, stop_order_type, EOrderType::Market, 100)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
        EXPECT_NE(order.parent_order_id(), buy_order_id);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 12110'0000, stop_order_type, EOrderType::Market,
                            110)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::MarkPrice, 12110'0000, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckTpSlInfo(1, 0, 110, 110);
  }
}

// 基础case 做空止盈价不合法，下单失败
TEST_F(iOrderFilledCarryTpslTest, Sell_invalid_tp) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, order_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::LastPrice, 10600'0000,
                                  ETriggerBy::MarkPrice, 11100'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  9800'0000, 11100'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    // auto result1 = te->PopResult();
    // ASSERT_NE(result1.m_msg.get(), nullptr);
    // auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    // ASSERT_NE(orderCheck.m_msg, nullptr);
    EXPECT_EQ(resp1->ret_code(), ::error::ErrorCode::kErrorCodeTpShouldLtBase);
  }
}

// 市价单携带止盈止损

TEST_F(iOrderFilledCarryTpslTest, Market_Ao_carry_tpsl) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);

  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    EOrderType cur_ord_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(order_id, symbol, pz_index, side, cur_ord_type, qty, price,
                                  ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::LastPrice, 9800'0000,
                                  ETriggerBy::MarkPrice, 11100'0000, ETriggerBy::UNKNOWN, -1, -1,
                                  EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                  9800'0000, 11100'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), ::error::ErrorCode::kErrorCodeSuccess);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 11100'0000);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }
}

// 条件市价单携带tpsl
TEST_F(iOrderFilledCarryTpslTest, Market_So_carry_tpsl) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);

  std::string buy_order_id;
  // 做空价格校验 tp触发价和委托价
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    EOrderType cur_ord_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 10600'0000, ETriggerBy::MarkPrice, 11100'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 9800'0000, 11100'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), ::error::ErrorCode::kErrorCodeTpShouldLtBase);
    // te->PopResult();

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2;
    create_build2.SetValueWithTpSl(order_id, symbol, pz_index, side, cur_ord_type, qty, price,
                                   ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::LastPrice, 9800'0000,
                                   ETriggerBy::MarkPrice, 11100'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
                                   EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                   10600'0000, 11100'0000, 0, true);
    auto resp2 = user1.process(create_build2.Build());
    EXPECT_EQ(resp2->ret_code(), ::error::ErrorCode::kErrorCodeSuccess);
    te->PopResult();
  }

  // 做空价格校验 sl触发价和委托价
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    EOrderType cur_ord_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9800'0000, ETriggerBy::MarkPrice, 9800'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 9800'0000, 11100'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), ::error::ErrorCode::kErrorCodeSlShouldGtBase);
    // te->PopResult();

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2;
    create_build2.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9800'0000, ETriggerBy::MarkPrice, 11100'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 9800'0000, 9800'0000, 0, true);
    auto resp2 = user1.process(create_build2.Build());
    EXPECT_EQ(resp2->ret_code(), ::error::ErrorCode::kErrorCodeSuccess);
    te->PopResult();
  }

  // 做空下单成功
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    EOrderType cur_ord_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9800'0000, ETriggerBy::MarkPrice, 11100'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 9800'0000, 11100'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), ::error::ErrorCode::kErrorCodeSuccess);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 11100'0000);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }

  // 做多价格校验 tp触发价和委托价
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    EOrderType cur_ord_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 10400'0000, ETriggerBy::MarkPrice, 9800'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), ::error::ErrorCode::kErrorCodeTpShouldGtBase);

    // 目前下单阶段不校验tpsl委托价

    FutureOneOfCreateOrderBuilder create_build2;
    create_build2.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 11100'0000, ETriggerBy::MarkPrice, 9800'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 10400'0000, 9800'0000, 0, true);
    auto resp2 = user1.process(create_build2.Build());
    EXPECT_EQ(resp2->ret_code(), ::error::ErrorCode::kErrorCodeSuccess);
    te->PopResult();
  }

  // 做多价格校验 sl触发价和委托价
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    EOrderType cur_ord_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 11100'0000, ETriggerBy::MarkPrice, 10600'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), ::error::ErrorCode::kErrorCodeSlShouldLtBase);
    FutureOneOfCreateOrderBuilder create_build2;
    create_build2.SetValueWithTpSl(order_id, symbol, pz_index, side, cur_ord_type, qty, price,
                                   ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::LastPrice, 11100'0000,
                                   ETriggerBy::MarkPrice, 9800'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
                                   EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                   11000'0000, 10600'0000, 0, true);
    auto resp2 = user1.process(create_build2.Build());
    EXPECT_EQ(resp2->ret_code(), ::error::ErrorCode::kErrorCodeSuccess);
    te->PopResult();
  }

  // 做多下单成功
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    EOrderType cur_ord_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 11100'0000, ETriggerBy::MarkPrice, 9800'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), ::error::ErrorCode::kErrorCodeSuccess);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 9800'0000);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }
}

// 条件市价单携带tpsl
TEST_F(iOrderFilledCarryTpslTest, Limit_So_carry_tpsl) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);

  std::string buy_order_id;
  // 做空价格校验 tp触发价和委托价
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    EOrderType cur_ord_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 10600'0000, ETriggerBy::MarkPrice, 11100'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 9800'0000, 11100'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), ::error::ErrorCode::kErrorCodeTpShouldLtBase);
    // te->PopResult();

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2;
    create_build2.SetValueWithTpSl(order_id, symbol, pz_index, side, cur_ord_type, qty, price,
                                   ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::LastPrice, 9800'0000,
                                   ETriggerBy::MarkPrice, 11100'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
                                   EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                   10600'0000, 11100'0000, 0, true);
    auto resp2 = user1.process(create_build2.Build());
    EXPECT_EQ(resp2->ret_code(), ::error::ErrorCode::kErrorCodeSuccess);
    te->PopResult();
  }

  // 做空价格校验 sl触发价和委托价
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    EOrderType cur_ord_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9800'0000, ETriggerBy::MarkPrice, 9800'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 9800'0000, 11100'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), ::error::ErrorCode::kErrorCodeSlShouldGtBase);
    // te->PopResult();

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2;
    create_build2.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9800'0000, ETriggerBy::MarkPrice, 11100'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 9800'0000, 9800'0000, 0, true);
    auto resp2 = user1.process(create_build2.Build());
    EXPECT_EQ(resp2->ret_code(), ::error::ErrorCode::kErrorCodeSuccess);
    te->PopResult();
  }

  // 做空下单成功
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    EOrderType cur_ord_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9800'0000, ETriggerBy::MarkPrice, 11100'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 9800'0000, 11100'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), ::error::ErrorCode::kErrorCodeSuccess);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 9800'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 11100'0000);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }

  // 做多价格校验 tp触发价和委托价
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    EOrderType cur_ord_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 9900'0000, ETriggerBy::MarkPrice, 9800'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), ::error::ErrorCode::kErrorCodeTpShouldGtBase);

    // 目前下单阶段不校验tpsl委托价

    FutureOneOfCreateOrderBuilder create_build2;
    create_build2.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 11100'0000, ETriggerBy::MarkPrice, 9800'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 10400'0000, 9800'0000, 0, true);
    auto resp2 = user1.process(create_build2.Build());
    EXPECT_EQ(resp2->ret_code(), ::error::ErrorCode::kErrorCodeSuccess);
    te->PopResult();
  }

  // 做多价格校验 sl触发价和委托价
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    EOrderType cur_ord_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 11100'0000, ETriggerBy::MarkPrice, 10600'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), ::error::ErrorCode::kErrorCodeSlShouldLtBase);
    FutureOneOfCreateOrderBuilder create_build2;
    create_build2.SetValueWithTpSl(order_id, symbol, pz_index, side, cur_ord_type, qty, price,
                                   ETimeInForce::GoodTillCancel, user_id1, coin, ETriggerBy::LastPrice, 11100'0000,
                                   ETriggerBy::MarkPrice, 9800'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
                                   EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit,
                                   11000'0000, 10600'0000, 0, true);
    auto resp2 = user1.process(create_build2.Build());
    EXPECT_EQ(resp2->ret_code(), ::error::ErrorCode::kErrorCodeSuccess);
    te->PopResult();
  }

  // 做多下单成功
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    EOrderType cur_ord_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, cur_ord_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::LastPrice, 11100'0000, ETriggerBy::MarkPrice, 9800'0000, ETriggerBy::MarkPrice, -1, 10500'0000,
        EStopOrderType::Stop, ETpSlMode::Partial, EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000, 0, true);

    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), ::error::ErrorCode::kErrorCodeSuccess);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->take_profit_x(), 11100'0000);
    EXPECT_EQ(orderCheck.m_msg->stop_loss_x(), 9800'0000);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }
}

/*
 * 该UT用于验证一笔订单，携带止盈止损信息，发撮合后，trading重启，走Taker补单逻辑的正确性
 */
TEST_F(iOrderFilledCarryTpslTest, RecoveryFromCross) {
  biz::user_id_t user_id1 = 1;
  biz::user_id_t user_id2 = 2;

  stub user1(te, user_id1);
  stub user2(te, user_id2);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(user_id1), 0);
  EXPECT_EQ(Deposit(user_id1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(user_id2), 0);
  EXPECT_EQ(Deposit(user_id2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(user_id2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(user_id2, EAccountMode::Cross), 0);
  // 模拟一笔订单直接发撮合，这样trading收到这边订单后，会走taker补单逻辑
  {
    store::Order::Ptr order = std::make_shared<store::Order>();
    auto& transact = *order;
    transact.user_id = user_id1;
    transact.symbol = symbol;

    transact.order_id = te->GenUUID();

    transact.qty = 10;
    transact.side = ESide::Sell;
    transact.order_type = EOrderType::Limit;
    transact.time_in_force = ETimeInForce::GoodTillCancel;
    transact.leaves_qty = transact.qty;
    transact.create_type = ECreateType::CreateByUser;
    transact.added_op_seq = 100;
    transact.price = 10000'0000;
    transact.price_scale = 4;
    transact.cross_status = ECrossStatus::PendingNew;
    transact.stop_order_type = EStopOrderType::PartialTakeProfit;
    transact.order_tpsl_mode = ETpSlMode::Partial;
    transact.tp_order_price = 11000'0000;
    transact.tp_order_type = EOrderType::Limit;
    transact.sl_order_price = 9000'0000;
    transact.sl_order_type = EOrderType::Limit;
    transact.need_generate_pid = true;
    biz::cross::xReqSender::SendPendingOrderToCross({}, nullptr, {}, nullptr, false, order, EProductType::Futures,
                                                    nullptr, EAction::CreateOrder, bbase::utils::Time::GetTimeNs(),
                                                    nullptr);
  }
  {
    auto result = te->PopResult();
    EXPECT_NE(result.m_msg, nullptr);
    NewLine();
    NewLine();
    std::cout << result.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders_size(), 1);
    for (auto& order : result.m_msg->futures_margin_result().related_orders()) {
      EXPECT_EQ(order.tp_sl_mode(), ETpSlMode::Partial);
      EXPECT_EQ(order.take_profit_limit_x(), 11000'0000);
      EXPECT_EQ(order.tp_order_type(), EOrderType::Limit);
      EXPECT_EQ(order.stop_loss_limit_x(), 9000'0000);
      EXPECT_EQ(order.sl_order_type(), EOrderType::Limit);
      EXPECT_TRUE(order.need_generate_pid());
    }
  }

  {
    store::Order::Ptr order = std::make_shared<store::Order>();
    auto& transact = *order;
    transact.user_id = user_id1;
    transact.symbol = symbol;

    transact.order_id = te->GenUUID();

    transact.qty = 10;
    transact.side = ESide::Sell;
    transact.order_type = EOrderType::Limit;
    transact.time_in_force = ETimeInForce::GoodTillCancel;
    transact.leaves_qty = transact.qty;
    transact.create_type = ECreateType::CreateByUser;
    transact.added_op_seq = 101;
    transact.price = 10000'0000;
    transact.price_scale = 4;
    transact.cross_status = ECrossStatus::PendingNew;
    transact.stop_order_type = EStopOrderType::PartialTakeProfit;
    transact.order_tpsl_mode = ETpSlMode::Partial;
    transact.tp_order_price = 11100'0000;
    transact.tp_order_type = EOrderType::Market;
    transact.sl_order_price = 9100'0000;
    transact.sl_order_type = EOrderType::Limit;
    transact.need_generate_pid = false;
    biz::cross::xReqSender::SendPendingOrderToCross({}, nullptr, {}, nullptr, false, order, EProductType::Futures,
                                                    nullptr, EAction::CreateOrder, bbase::utils::Time::GetTimeNs(),
                                                    nullptr);
  }
  {
    auto result = te->PopResult();
    EXPECT_NE(result.m_msg, nullptr);
    NewLine();
    NewLine();
    std::cout << result.m_msg->DebugString() << std::endl;
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders_size(), 1);
    for (auto& order : result.m_msg->futures_margin_result().related_orders()) {
      EXPECT_EQ(order.tp_sl_mode(), ETpSlMode::Partial);
      EXPECT_EQ(order.take_profit_limit_x(), 11100'0000);
      EXPECT_EQ(order.tp_order_type(), EOrderType::Market);
      EXPECT_EQ(order.stop_loss_limit_x(), 9100'0000);
      EXPECT_EQ(order.sl_order_type(), EOrderType::Limit);
      EXPECT_FALSE(order.need_generate_pid());
    }
  }
}
