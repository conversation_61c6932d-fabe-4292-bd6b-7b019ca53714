#include "test/biz/trade/future/feature/inverse/tpsl/set_tp_sl_ts_test.hpp"

#include <string>

#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(SetTpSlTsTest, test_set_partial_tp_sl_v1) {
  biz::user_id_t user_id1 = 653106;
  // 构建客户端
  stub user1(te, user_id1);
  // 设置逐仓10x杠杆+充钱
  Deposit(user1.m_uid);

  UpdateMarketData(10000);

  biz::user_id_t user_id2 = 111111;
  stub user2(te, user_id2);
  // 设置逐仓10x杠杆+充钱
  Deposit(user2.m_uid);

  // uid1挂单，方向Buy，限价单
  biz::order_id_t sell_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;
    sell_order_id = order_id;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
    ASSERT_EQ(resp1->ret_code(), error::kErrorCodeSuccess);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }

  // uid2挂单，方向Sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;
    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);
    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  // uid1成交记录
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    EXPECT_EQ(result1.m_msg->header().user_id(), user_id1);
    auto orderCheck2 = result1.RefFutureMarginResult();
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckSize(100);
    EXPECT_EQ(position.m_msg->side(), ESide::Buy);
  }

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  {
    FutureOneOfSwitchTpSlModeBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, ETpSlMode::Partial);
    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(position.m_msg->tp_sl_mode(), static_cast<ETpSlMode>(ETpSlMode::Partial));
  }

  // 设置部分止盈10+部分止损20
  // 单独修改10部分止盈的triggerBy
  // 单独修改10部分止盈的triggerPrice
  // 单独修改10部分止盈的slSize -> 9
  // 同时修改10部分止盈的triggerBy，triggerPrice,slSize
  // 取消修改10部分止盈

  // 设置部分止盈10(triggerPrice:11000)+部分止损20(triggerPrice:9000
  {
    // 设置部分止盈10+部分止损20
    std::string tp_order_id;
    std::string sl_order_id;
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000'0000, ETriggerBy::UNKNOWN,
                            -1, ETriggerBy::UNKNOWN, -1, -1, 10, -1, "", "", 0, ETpSlMode::Partial, EOrderType::Market,
                            EOrderType::UNKNOWN, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order.ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::LastPrice, EOrderType::Market, 11000'0000);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
      tp_order_id = order.m_msg->order_id();
    }

    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::MarkPrice, 9900'0000,
                            ETriggerBy::UNKNOWN, -1, -1, -1, 20, "", "", 0, ETpSlMode::Partial, EOrderType::UNKNOWN,
                            EOrderType::Market, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);

      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order.ChecckTpSlOrder(EStopOrderType::PartialStopLoss, ETriggerBy::MarkPrice, EOrderType::Market, 9900'0000, 20);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
      sl_order_id = order.m_msg->order_id();
    }
    // 单独修改10部分止盈的triggerBy
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::MarkPrice, 11000'0000, ETriggerBy::UNKNOWN,
                            -1, ETriggerBy::UNKNOWN, -1, -1, 10, -1, tp_order_id, "", 0, ETpSlMode::Partial,
                            EOrderType::Market, EOrderType::UNKNOWN, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order.ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::MarkPrice, EOrderType::Market, 11000'0000);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    }

    // 单独修改10部分止盈的triggerPrice
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::MarkPrice, 11001'0000, ETriggerBy::UNKNOWN,
                            -1, ETriggerBy::UNKNOWN, -1, -1, 10, -1, tp_order_id, "", 0, ETpSlMode::Partial,
                            EOrderType::Market, EOrderType::UNKNOWN, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order.ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::MarkPrice, EOrderType::Market, 11001'0000,
                            10);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    }

    // 单独修改10部分止盈的tpSize -> 11
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::MarkPrice, 11001'0000, ETriggerBy::UNKNOWN,
                            -1, ETriggerBy::UNKNOWN, -1, -1, 11, -1, tp_order_id, "", 0, ETpSlMode::Partial,
                            EOrderType::Market, EOrderType::UNKNOWN, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());

      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);

      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order.ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::MarkPrice, EOrderType::Market, 11001'0000,
                            11);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    }
    // 同时修改10部分止盈的triggerBy,triggerPrice,slSize
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 0, ETriggerBy::UNKNOWN, -1,
                            ETriggerBy::UNKNOWN, -1, -1, 0, -1, tp_order_id, "", 0, ETpSlMode::Partial,
                            EOrderType::Market, EOrderType::UNKNOWN, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order
          .ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::MarkPrice, EOrderType::Market, 11001'0000, 11)
          .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    }
    // 新增10部分止盈
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000 * 1e4, ETriggerBy::UNKNOWN,
                            -1, ETriggerBy::UNKNOWN, -1, -1, 10, -1, tp_order_id, "", 0, ETpSlMode::Partial,
                            EOrderType::Market, EOrderType::UNKNOWN, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order
          .ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::LastPrice, EOrderType::Market, 11000'0000, 10)
          .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
      auto new_order = order.m_msg->order_id();
      EXPECT_NE(new_order, tp_order_id);
    }
  }

  // 设置部分止损90
  // 单独修改80部分止损的triggerBy
  // 单独修改80部分止损的triggerPrice
  // 单独修改80部分止损的slSize -> 79
  // 同时修改80部分止损的triggerBy，triggerPrice,slSize
  // 取消80的部分止损
  {
    // 设置部分止损90
    std::string sl_order_id;
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::IndexPrice,
                            9901'0000, ETriggerBy::UNKNOWN, -1, -1, -1, 90, "", "", 0, ETpSlMode::Partial,
                            EOrderType::UNKNOWN, EOrderType::Market, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order.ChecckTpSlOrder(EStopOrderType::PartialStopLoss, ETriggerBy::IndexPrice, EOrderType::Market, 9901'0000, 90)
          .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
      sl_order_id = order.m_msg->order_id();
    }

    // 单独修改80部分止损的triggerBy
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::MarkPrice, 9901'0000,
                            ETriggerBy::UNKNOWN, -1, -1, -1, 80, "", sl_order_id, 0, ETpSlMode::Partial,
                            EOrderType::UNKNOWN, EOrderType::Market, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order.ChecckTpSlOrder(EStopOrderType::PartialStopLoss, ETriggerBy::MarkPrice, EOrderType::Market, 9901'0000, 80)
          .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
      EXPECT_EQ(sl_order_id, order.m_msg->order_id());
    }

    // 单独修改80部分止损的triggerPrice
    // 单独修改80部分止损的slSize -> 79
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::MarkPrice, 9902'0000,
                            ETriggerBy::UNKNOWN, -1, -1, -1, 79, "", sl_order_id, 0, ETpSlMode::Partial,
                            EOrderType::UNKNOWN, EOrderType::Market, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order.ChecckTpSlOrder(EStopOrderType::PartialStopLoss, ETriggerBy::MarkPrice, EOrderType::Market, 9902'0000, 79)
          .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
      EXPECT_EQ(sl_order_id, order.m_msg->order_id());
    }

    // 同时修改80部分止损的triggerBy，triggerPrice,slSize
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::LastPrice, 9903'0000,
                            ETriggerBy::UNKNOWN, -1, -1, -1, 78, "", sl_order_id, 0, ETpSlMode::Partial,
                            EOrderType::UNKNOWN, EOrderType::Market, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order.ChecckTpSlOrder(EStopOrderType::PartialStopLoss, ETriggerBy::LastPrice, EOrderType::Market, 9903'0000, 78)
          .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
      EXPECT_EQ(sl_order_id, order.m_msg->order_id());
    }

    // 一些合法设置检查，return err 34040 TpSlNotModified
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::LastPrice, 9200'0000,
                            ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", sl_order_id, 0, ETpSlMode::Partial,
                            EOrderType::UNKNOWN, EOrderType::Market, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), error::ErrorCode::kErrorCodeTpSlNotModified);
      auto result1 = te->PopResult();
      ASSERT_EQ(result1.m_msg.get(), nullptr);
    }

    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::LastPrice, -1,
                            ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", sl_order_id, 0, ETpSlMode::Partial,
                            EOrderType::UNKNOWN, EOrderType::Market, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), error::ErrorCode::kErrorCodeTpSlNotModified);
      auto result1 = te->PopResult();
      ASSERT_EQ(result1.m_msg.get(), nullptr);
    }

    // 取消80的部分止损
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::LastPrice, 0,
                            ETriggerBy::UNKNOWN, -1, -1, -1, 78, "", sl_order_id, 0, ETpSlMode::Partial,
                            EOrderType::UNKNOWN, EOrderType::Market, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order.ChecckTpSlOrder(EStopOrderType::PartialStopLoss, ETriggerBy::LastPrice, EOrderType::Market, 9903'0000, 78)
          .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
      EXPECT_EQ(sl_order_id, order.m_msg->order_id());
    }

    // 再次设置
    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::LastPrice, 9901'0000,
                            ETriggerBy::UNKNOWN, -1, -1, -1, 90, "", "", 0, ETpSlMode::Partial, EOrderType::UNKNOWN,
                            EOrderType::Market, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order.ChecckTpSlOrder(EStopOrderType::PartialStopLoss, ETriggerBy::LastPrice, EOrderType::Market, 9901'0000, 90)
          .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    }
  }

  // 设置部分止盈16条（每条1usd），在同时设置止盈止损，再只设置止盈
  {
    for (int i = 1; i <= 16; ++i) {
      biz::price_x_t tmp_price = 11001 * 1e4 + i * 1e4;
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, tmp_price, ETriggerBy::UNKNOWN, -1,
                            ETriggerBy::UNKNOWN, -1, -1, 10, -1, "", "", 0, ETpSlMode::Partial, EOrderType::Market,
                            EOrderType::UNKNOWN, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order.ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::LastPrice, EOrderType::Market, tmp_price, 10)
          .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
      usleep(10000);
    }

    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11002'0000, ETriggerBy::MarkPrice,
                            9920'0000, ETriggerBy::UNKNOWN, -1, -1, 10, 10, "", "", 0, ETpSlMode::Partial,
                            EOrderType::Market, EOrderType::Market, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), error::ErrorCode::kErrorCodeTpSLStopOrderNoMoreThan20);
      auto result1 = te->PopResult();
      ASSERT_EQ(result1.m_msg.get(), nullptr);
    }

    {
      FutureOneOfSetTpSlTsBuilder create_build;
      create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 10101'0000, ETriggerBy::UNKNOWN,
                            -1, ETriggerBy::UNKNOWN, -1, -1, 10, -1, "", "", 0, ETpSlMode::Partial, EOrderType::Market,
                            EOrderType::UNKNOWN, -1, -1, false);
      auto resp1 = user1.process(create_build.Build());
      EXPECT_EQ(resp1->ret_code(), 0);
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      auto orderCheck2 = result1.RefFutureMarginResult();
      auto order = orderCheck2.RefRelatedOrderByIndex(0);
      order
          .ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::LastPrice, EOrderType::Market, 10101'0000, 10)
          .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      auto position = orderCheck2.RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    }
  }

  // 新建追踪止损
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::UNKNOWN, -1,
                          ETriggerBy::UNKNOWN, 100'0000, -1, -1, -1, "", "", 0, ETpSlMode::Full, EOrderType::UNKNOWN,
                          EOrderType::UNKNOWN, -1, -1, false);
    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck2 = result1.RefFutureMarginResult();
    auto order = orderCheck2.RefRelatedOrderByIndex(0);
    order.ChecckTpSlOrder(EStopOrderType::TrailingStop, ETriggerBy::LastPrice, EOrderType::Market, 9900'0000, 100)
        .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 100'0000, 0);
  }

  // 将追踪止损修改成追踪止盈(多仓追踪止盈激活价格参数大于市价)
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::UNKNOWN, -1,
                          ETriggerBy::UNKNOWN, 100'0000, 12000'0000, -1, -1, "", "", 0, ETpSlMode::Full,
                          EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck2 = result1.RefFutureMarginResult();
    auto order = orderCheck2.RefRelatedOrderByIndex(0);
    order.ChecckTpSlOrder(EStopOrderType::TrailingProfit, ETriggerBy::LastPrice, EOrderType::Market, 11900'0000, 100)
        .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 100'0000, 12000'0000);
  }

  // 取消止盈止损追踪止盈
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, -1, ETriggerBy::UNKNOWN, -1,
                          ETriggerBy::UNKNOWN, 0, 12000'0000, -1, -1, "", "", 0, ETpSlMode::Full, EOrderType::UNKNOWN,
                          EOrderType::UNKNOWN, -1, -1, false);
    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck2 = result1.RefFutureMarginResult();
    auto order = orderCheck2.RefRelatedOrderByIndex(0);
    order.ChecckTpSlOrder(EStopOrderType::TrailingProfit, ETriggerBy::LastPrice, EOrderType::Market, 11900'0000, 100)
        .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
  }
}

TEST_F(SetTpSlTsTest, test_set_partial_tp_sl_v2) {
  biz::user_id_t user_id1 = 653106;
  // 构建客户端
  stub user1(te, user_id1);
  // 设置逐仓10x杠杆+充钱
  Deposit(user1.m_uid);

  UpdateMarketData(10000);

  biz::user_id_t user_id2 = 111111;
  stub user2(te, user_id2);
  // 设置逐仓10x杠杆+充钱
  Deposit(user2.m_uid);

  // uid1挂单，方向Buy，限价单
  biz::order_id_t sell_order_id;
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 100000000;
    sell_order_id = order_id;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }

  // uid2挂单，方向Sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;
    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);
    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  // uid1成交记录
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    EXPECT_EQ(result1.m_msg->header().user_id(), user_id1);
    auto orderCheck2 = result1.RefFutureMarginResult();
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckSize(100);
    EXPECT_EQ(position.m_msg->side(), ESide::Buy);
  }

  // 直接切换止盈止损模式
  // 切换到部分止盈止损模式
  {
    FutureOneOfSwitchTpSlModeBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, ETpSlMode::Partial);
    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(position.m_msg->tp_sl_mode(), static_cast<ETpSlMode>(ETpSlMode::Partial));
  }

  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000'0000, ETriggerBy::MarkPrice,
                          9900'0000, ETriggerBy::UNKNOWN, -1, -1, 20, 20, "", "", 0, ETpSlMode::Partial,
                          EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000, false);
    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck2 = result1.RefFutureMarginResult();
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::LastPrice, EOrderType::Limit, 11000'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init)
            .CheckPrice(user1.m_uid, 11100'0000);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        cc.ChecckTpSlOrder(EStopOrderType::PartialStopLoss, ETriggerBy::MarkPrice, EOrderType::Limit, 9900'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init)
            .CheckPrice(user1.m_uid, 9800'0000);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
  }

  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000'0000, ETriggerBy::MarkPrice,
                          9900'0000, ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", "", 0, ETpSlMode::Full,
                          EOrderType::UNKNOWN, EOrderType::UNKNOWN, 11100'0000, 9800'0000, false);
    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck2 = result1.RefFutureMarginResult();
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::TakeProfit) {
        cc.ChecckTpSlOrder(EStopOrderType::TakeProfit, ETriggerBy::LastPrice, EOrderType::Market, 11000'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init)
            .CheckPrice(user1.m_uid, 0);
      } else if (stop_order_type == EStopOrderType::StopLoss) {
        cc.ChecckTpSlOrder(EStopOrderType::StopLoss, ETriggerBy::MarkPrice, EOrderType::Market, 9900'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init)
            .CheckPrice(user1.m_uid, 0);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::LastPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, 0, 0);
  }

  {
    std::string tp_parent_order_id;
    std::string sl_parent_order_id;
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000'0000, ETriggerBy::MarkPrice,
                          9900'0000, ETriggerBy::UNKNOWN, -1, -1, 100'0000, 100'0000, "", "", 0, ETpSlMode::Partial,
                          EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000, false);
    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck2 = result1.RefFutureMarginResult();
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::LastPrice, EOrderType::Limit, 11000'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init)
            .CheckPrice(user1.m_uid, 11100'0000);
        tp_parent_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        cc.ChecckTpSlOrder(EStopOrderType::PartialStopLoss, ETriggerBy::MarkPrice, EOrderType::Limit, 9900'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init)
            .CheckPrice(user1.m_uid, 9800'0000);
        sl_parent_order_id = order.order_id();
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::LastPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, 0, 0);
  }
}

TEST_F(SetTpSlTsTest, test_set_partial_tp_sl_v3) {
  biz::user_id_t uid1 = 1;
  biz::user_id_t uid2 = 2;

  stub user1(te, uid1);
  stub user2(te, uid2);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(uid1), 0);
  EXPECT_EQ(Deposit(uid1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(uid1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(uid1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(uid2), 0);
  EXPECT_EQ(Deposit(uid2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(uid2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(uid2, EAccountMode::Cross), 0);

  biz::size_x_t qty = 1000;
  biz::price_x_t price = 10000 * 1e4;
  biz::price_x_t take_profit_price = price;
  biz::price_x_t stop_loss_price = price;
  biz::price_x_t trailing_stop_price = -1;
  biz::price_x_t activation_price = -1;
  biz::size_x_t take_profit_qty = qty;
  biz::size_x_t stop_loss_qty = qty;
  std::string take_profit_order_id = "";
  std::string stop_loss_order_id = "";
  ETriggerBy take_profit_trigger_by = ETriggerBy::LastPrice;
  ETriggerBy stop_loss_trigger_by = ETriggerBy::LastPrice;
  ETriggerBy trailing_stop_trigger_by = ETriggerBy::UNKNOWN;
  ETpSlMode tp_sl_mode = ETpSlMode::Partial;
  int32_t price_scale = 0;

  auto order_id1 = te->GenUUID();
  auto order_id2 = te->GenUUID();

  {
    // user1挂单,Buy方向,限价单
    FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id1, symbol, pz_index, ESide::Buy, order_type, qty,
                                                         price, ETimeInForce::GoodTillCancel, uid1, coin);
    auto resp = user1.process(create_build_user1_buy.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id1);
    ASSERT_NE(order_checker.m_msg, nullptr);
  }

  {
    // user2挂单,Sell方向,限价单
    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id2, symbol, pz_index, ESide::Sell, order_type, qty,
                                                          price, ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id2);
    ASSERT_NE(order_checker.m_msg, nullptr);
  }

  {
    // user1成交记录
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->header().user_id(), uid1);
    auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckSize(qty);
  }

  {
    // 测试场景:部分仓位tp和sl价格不合适,tp劣于仓位均价,sl优于仓位均价
    // 预期结果:设置失败
    take_profit_price = 9000 * 1e4;
    stop_loss_price = 11000 * 1e4;
    take_profit_qty = 200;
    stop_loss_qty = 200;
    FutureOneOfSetTpSlTsBuilder builder;
    builder.SetValue(uid1, coin, symbol, pz_index, take_profit_trigger_by, take_profit_price, stop_loss_trigger_by,
                     stop_loss_price, trailing_stop_trigger_by, trailing_stop_price, activation_price, take_profit_qty,
                     stop_loss_qty, take_profit_order_id, stop_loss_order_id, price_scale, tp_sl_mode,
                     EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp = user1.process(builder.Build());
    ASSERT_NE(resp->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    // 测试场景:部分仓位tp和sl价格合适,tp优于仓位均价,sl劣于仓位均价
    // 预期结果:设置成功
    take_profit_price = 11000 * 1e4;
    stop_loss_price = 9000 * 1e4;
    take_profit_qty = 200;
    stop_loss_qty = 200;
    FutureOneOfSetTpSlTsBuilder builder;
    builder.SetValue(uid1, coin, symbol, pz_index, take_profit_trigger_by, take_profit_price, stop_loss_trigger_by,
                     stop_loss_price, trailing_stop_trigger_by, trailing_stop_price, activation_price, take_profit_qty,
                     stop_loss_qty, take_profit_order_id, stop_loss_order_id, price_scale, tp_sl_mode,
                     EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp = user1.process(builder.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    for (const auto& order : result.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker checker(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        checker
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, stop_loss_price, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, stop_loss_qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        stop_loss_order_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        checker
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, take_profit_price,
                            EStopOrderType::PartialTakeProfit, EOrderType::Market, take_profit_qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        take_profit_order_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
  }

  {
    // 测试场景:部分仓位有tp和sl的情况下,更新tp和sp至合适价格
    // 预期结果:设置成功,覆盖更新仓位的tp和sl
    take_profit_price = 10500 * 1e4;
    stop_loss_price = 9500 * 1e4;
    take_profit_qty = 100;
    stop_loss_qty = 100;
    take_profit_trigger_by = ETriggerBy::MarkPrice;
    stop_loss_trigger_by = ETriggerBy::MarkPrice;
    FutureOneOfSetTpSlTsBuilder builder;
    builder.SetValue(uid1, coin, symbol, pz_index, take_profit_trigger_by, take_profit_price, stop_loss_trigger_by,
                     stop_loss_price, trailing_stop_trigger_by, trailing_stop_price, activation_price, take_profit_qty,
                     stop_loss_qty, take_profit_order_id, stop_loss_order_id, price_scale, ETpSlMode::Partial,
                     EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp = user1.process(builder.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    for (const auto& order : result.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker checker(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialStopLoss) {
        checker
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, stop_loss_price, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, stop_loss_qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        checker
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, take_profit_price,
                            EStopOrderType::PartialTakeProfit, EOrderType::Market, take_profit_qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
  }

  {
    // 测试场景:多次设置部分tp和sl,超过仓位大小
    // 预期结果:多次设置全部成功
    take_profit_price = 10500 * 1e4;
    stop_loss_price = 9500 * 1e4;
    take_profit_qty = 1000;
    stop_loss_qty = 1000;
    take_profit_trigger_by = ETriggerBy::LastPrice;
    stop_loss_trigger_by = ETriggerBy::LastPrice;
    for (int i = 0; i < 5; i++) {
      FutureOneOfSetTpSlTsBuilder builder;
      builder.SetValue(uid1, coin, symbol, pz_index, take_profit_trigger_by, take_profit_price, stop_loss_trigger_by,
                       stop_loss_price, trailing_stop_trigger_by, trailing_stop_price, activation_price,
                       take_profit_qty, stop_loss_qty, "", "", price_scale, ETpSlMode::Partial, EOrderType::UNKNOWN,
                       EOrderType::UNKNOWN, -1, -1, false);
      auto resp = user1.process(builder.Build());
      ASSERT_EQ(resp->ret_code(), 0);
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      for (const auto& order : result.RefFutureMarginResult().m_msg->related_orders()) {
        TransactDTOChecker checker(&order);
        auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
        if (stop_order_type == EStopOrderType::PartialStopLoss) {
          checker
              .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, stop_loss_price,
                              EStopOrderType::PartialStopLoss, EOrderType::Market, stop_loss_qty)
              .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
          checker
              .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, take_profit_price,
                              EStopOrderType::PartialTakeProfit, EOrderType::Market, take_profit_qty)
              .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        } else {
          EXPECT_TRUE(false);
        }
      }
      auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    }
  }

  {
    // 测试场景:取消所有部分tp和sl订单
    // 预期结果:设置成功
    FutureCancelAllBuilder builder(symbol, coin, uid1, ECancelType::CancelByUser, ECancelAllType::PartialTpSlStopOrder,
                                   "from_ut");
    auto resp = user1.process(builder.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    for (const auto& order : result.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker checker(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit || stop_order_type == EStopOrderType::PartialStopLoss) {
        checker.CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
  }

  {
    // 测试场景:设置部分tp和sl,使得部分tp和sl总数为19
    // 预期结果:设置成功
    take_profit_price = 10500 * 1e4;
    stop_loss_price = -1;
    take_profit_qty = 500;
    stop_loss_qty = -1;
    take_profit_trigger_by = ETriggerBy::LastPrice;
    stop_loss_trigger_by = ETriggerBy::UNKNOWN;
    for (int i = 0; i < 19; i++) {
      FutureOneOfSetTpSlTsBuilder builder;
      builder.SetValue(uid1, coin, symbol, pz_index, take_profit_trigger_by, take_profit_price, stop_loss_trigger_by,
                       stop_loss_price, trailing_stop_trigger_by, trailing_stop_price, activation_price,
                       take_profit_qty, stop_loss_qty, "", "", price_scale, ETpSlMode::Partial, EOrderType::UNKNOWN,
                       EOrderType::UNKNOWN, -1, -1, false);
      auto resp = user1.process(builder.Build());
      ASSERT_EQ(resp->ret_code(), 0);
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      for (const auto& order : result.RefFutureMarginResult().m_msg->related_orders()) {
        TransactDTOChecker checker(&order);
        auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
        if (stop_order_type == EStopOrderType::PartialTakeProfit) {
          checker
              .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, take_profit_price,
                              EStopOrderType::PartialTakeProfit, EOrderType::Market, take_profit_qty)
              .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        } else {
          EXPECT_TRUE(false);
        }
      }
      auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
      position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    }
  }

  {
    // 测试场景:19+2
    // 预期结果:设置失败
    take_profit_price = 10500 * 1e4;
    stop_loss_price = 10500 * 1e4;
    take_profit_qty = 500;
    stop_loss_qty = 500;
    take_profit_trigger_by = ETriggerBy::LastPrice;
    stop_loss_trigger_by = ETriggerBy::LastPrice;
    FutureOneOfSetTpSlTsBuilder builder;
    builder.SetValue(uid1, coin, symbol, pz_index, take_profit_trigger_by, take_profit_price, stop_loss_trigger_by,
                     stop_loss_price, trailing_stop_trigger_by, trailing_stop_price, activation_price, take_profit_qty,
                     stop_loss_qty, "", "", price_scale, ETpSlMode::Partial, EOrderType::UNKNOWN, EOrderType::UNKNOWN,
                     -1, -1, false);
    auto resp = user1.process(builder.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeTpSLStopOrderNoMoreThan20);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    // 测试场景:19+1
    // 预测结果:设置成功
    take_profit_price = 10500 * 1e4;
    stop_loss_price = -1;
    take_profit_qty = 500;
    stop_loss_qty = -1;
    take_profit_trigger_by = ETriggerBy::LastPrice;
    stop_loss_trigger_by = ETriggerBy::UNKNOWN;
    FutureOneOfSetTpSlTsBuilder builder;
    builder.SetValue(uid1, coin, symbol, pz_index, take_profit_trigger_by, take_profit_price, stop_loss_trigger_by,
                     stop_loss_price, trailing_stop_trigger_by, trailing_stop_price, activation_price, take_profit_qty,
                     stop_loss_qty, "", "", price_scale, ETpSlMode::Partial, EOrderType::UNKNOWN, EOrderType::UNKNOWN,
                     -1, -1, false);
    auto resp = user1.process(builder.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    for (const auto& order : result.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker checker(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        checker
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Partial, take_profit_price,
                            EStopOrderType::PartialTakeProfit, EOrderType::Market, take_profit_qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
  }

  {
    // 测试场景:20+1
    // 预期结果:设置失败
    take_profit_price = 10500 * 1e4;
    stop_loss_price = -1;
    take_profit_qty = 500;
    stop_loss_qty = -1;
    take_profit_trigger_by = ETriggerBy::LastPrice;
    stop_loss_trigger_by = ETriggerBy::UNKNOWN;
    FutureOneOfSetTpSlTsBuilder builder;
    builder.SetValue(uid1, coin, symbol, pz_index, take_profit_trigger_by, take_profit_price, stop_loss_trigger_by,
                     stop_loss_price, trailing_stop_trigger_by, trailing_stop_price, activation_price, take_profit_qty,
                     stop_loss_qty, "", "", price_scale, ETpSlMode::Partial, EOrderType::UNKNOWN, EOrderType::UNKNOWN,
                     -1, -1, false);
    auto resp = user1.process(builder.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeTpSLStopOrderNoMoreThan20);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    // 测试场景:20+2
    // 预期结果:设置失败
    take_profit_price = 10500 * 1e4;
    stop_loss_price = 9500 * 1e4;
    take_profit_qty = 500;
    stop_loss_qty = 500;
    take_profit_trigger_by = ETriggerBy::LastPrice;
    stop_loss_trigger_by = ETriggerBy::LastPrice;
    FutureOneOfSetTpSlTsBuilder builder;
    builder.SetValue(uid1, coin, symbol, pz_index, take_profit_trigger_by, take_profit_price, stop_loss_trigger_by,
                     stop_loss_price, trailing_stop_trigger_by, trailing_stop_price, activation_price, take_profit_qty,
                     stop_loss_qty, "", "", price_scale, ETpSlMode::Partial, EOrderType::UNKNOWN, EOrderType::UNKNOWN,
                     -1, -1, false);
    auto resp = user1.process(builder.Build());
    ASSERT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeTpSLStopOrderNoMoreThan20);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }
}

TEST_F(SetTpSlTsTest, test_set_partial_tp_sl_replace_when_partially_filled) {
  biz::user_id_t user_id1 = 653106;
  // 构建客户端
  stub user1(te, user_id1);
  // 设置逐仓10x杠杆+充钱
  Deposit(user1.m_uid);

  UpdateMarketData(10000);

  biz::user_id_t user_id2 = 111111;
  stub user2(te, user_id2);
  // 设置逐仓10x杠杆+充钱
  Deposit(user2.m_uid);

  // 开仓做多: +100@$10,000
  // 开仓做多: +100@$10,000 开仓单携带止盈止损
  std::string buy_order_id;
  std::string parent_ord_id{}, tp_ord_id{}, sl_ord_id{};
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 100;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build;
    create_build.SetValueWithTpSl(
        order_id, symbol, pz_index, side, order_type, qty, price, ETimeInForce::GoodTillCancel, user_id1, coin,
        ETriggerBy::IndexPrice, 11000'0000, ETriggerBy::MarkPrice, 9900'0000, ETriggerBy::UNKNOWN, -1, -1,
        EStopOrderType::UNKNOWN, ETpSlMode::Partial, EOrderType::Market, EOrderType::Market, 0, 0);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.CheckOrderWithTp(ETriggerBy::IndexPrice, 11000'0000, EOrderType::Market, 0);
    orderCheck.CheckOrderWithSl(ETriggerBy::MarkPrice, 9900'0000, EOrderType::Market, 0);
    buy_order_id = orderCheck.m_msg->order_id();
  }

  // 目前是部分成交状态
  // uid2，方向sell，限价单
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 20;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }

  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);
        parent_ord_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Market, 20);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
        sl_ord_id = order.order_id();
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Market, 20);
        EXPECT_EQ(order.parent_order_id(), buy_order_id);
        tp_ord_id = order.order_id();
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(20);
  }

  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000'0000, ETriggerBy::MarkPrice,
                          9900'0000, ETriggerBy::UNKNOWN, -1, -1, 20, 20, tp_ord_id, sl_ord_id, 0, ETpSlMode::Partial,
                          EOrderType::Limit, EOrderType::Limit, 11100'0000, 9800'0000, false);
    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck2 = result1.RefFutureMarginResult();
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::LastPrice, EOrderType::Limit, 11000'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced)
            .CheckPrice(user1.m_uid, 11100'0000);
        EXPECT_NE(parent_ord_id, order.parent_order_id());
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        cc.ChecckTpSlOrder(EStopOrderType::PartialStopLoss, ETriggerBy::MarkPrice, EOrderType::Limit, 9900'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced)
            .CheckPrice(user1.m_uid, 9800'0000);
        EXPECT_NE(parent_ord_id, order.parent_order_id());
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
  }
  // 继续成交
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 40;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }
  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                                  EOrderType::Market, 40);
        EXPECT_EQ(parent_ord_id, order.parent_order_id());
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck.CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000,
                                  EStopOrderType::PartialTakeProfit, EOrderType::Market, 40);
        EXPECT_EQ(parent_ord_id, order.parent_order_id());
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(60);
  }

  // 通过settpsl接口撤销tp或sl订单，不会同时cancel
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, 0, ETriggerBy::LastPrice, 9998'0000,
                          ETriggerBy::UNKNOWN, -1, -1, 60, 60, tp_ord_id, sl_ord_id, 0, ETpSlMode::Partial,
                          EOrderType::Market, EOrderType::Limit, 11000'0000, 9000'0000, false);
    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(orderCheck2.m_msg->related_orders().size(), 2);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::LastPrice, EOrderType::Limit, 11000'0000)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated)
            .CheckPrice(user1.m_uid, 11100'0000);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        cc.ChecckTpSlOrder(EStopOrderType::PartialStopLoss, ETriggerBy::LastPrice, EOrderType::Limit, 9998'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced)
            .CheckPrice(user1.m_uid, 9000'0000);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
  }

  // 再把tp设上
  {
    FutureOneOfSetTpSlTsBuilder create_build;
    create_build.SetValue(user_id1, coin, symbol, pz_index, ETriggerBy::LastPrice, 11000'0000, ETriggerBy::LastPrice,
                          9998'0000, ETriggerBy::UNKNOWN, -1, -1, 60, 60, "", sl_ord_id, 0, ETpSlMode::Partial,
                          EOrderType::Market, EOrderType::Limit, 11000'0000, 9000'0000, false);
    auto resp1 = user1.process(create_build.Build());
    EXPECT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck2 = result1.RefFutureMarginResult();
    EXPECT_EQ(orderCheck2.m_msg->related_orders().size(), 2);
    for (auto& order : orderCheck2.m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        cc.ChecckTpSlOrder(EStopOrderType::PartialTakeProfit, ETriggerBy::LastPrice, EOrderType::Market, 11000'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init)
            .CheckPrice(user1.m_uid, 0);
        EXPECT_NE(order.parent_order_id(), parent_ord_id);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        cc.ChecckTpSlOrder(EStopOrderType::PartialStopLoss, ETriggerBy::LastPrice, EOrderType::Limit, 9998'0000)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced)
            .CheckPrice(user1.m_uid, 9000'0000);
        EXPECT_NE(order.parent_order_id(), parent_ord_id);
      } else {
        EXPECT_FALSE(true);
      }
    }
    auto position = orderCheck2.RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
  }

  // 继续成交
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;

    biz::size_x_t qty = 40;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);

    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }

  // user_id1 成交
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    EXPECT_EQ(result1.RefFutureMarginResult().m_msg->related_orders().size(), 3);
    for (auto& order : result1.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker orderCheck(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (order.order_id() == buy_order_id) {
        orderCheck.CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill);
      } else if (stop_order_type == EStopOrderType::PartialStopLoss) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Partial, 9900'0000, EStopOrderType::PartialStopLoss,
                            EOrderType::Market, 80)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_EQ(parent_ord_id, order.parent_order_id());
      } else if (stop_order_type == EStopOrderType::PartialTakeProfit) {
        orderCheck
            .CheckTpSLOrder(ETriggerBy::IndexPrice, ETpSlMode::Partial, 11000'0000, EStopOrderType::PartialTakeProfit,
                            EOrderType::Market, 80)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
        EXPECT_EQ(parent_ord_id, order.parent_order_id());
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result1.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, 0, ETriggerBy::UNKNOWN, 0, 0, 0);
    position.CheckSize(100);
  }
}

TEST_F(SetTpSlTsTest, test_set_full_tp_sl_ts) {
  biz::user_id_t uid1 = 1;
  biz::user_id_t uid2 = 2;

  stub user1(te, uid1);
  stub user2(te, uid2);

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(uid1), 0);
  EXPECT_EQ(Deposit(uid1, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(uid1, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(uid1, EAccountMode::Cross), 0);

  EXPECT_EQ(OpenAccount(uid2), 0);
  EXPECT_EQ(Deposit(uid2, "10", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(uid2, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(uid2, EAccountMode::Cross), 0);

  biz::size_x_t qty = 1000;
  biz::price_x_t price = 10000 * 1e4;
  biz::price_x_t take_profit_price = price;
  biz::price_x_t stop_loss_price = price;

  auto order_id1 = te->GenUUID();
  auto order_id2 = te->GenUUID();

  {
    // user1挂单,Buy方向,限价单
    FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id1, symbol, pz_index, ESide::Buy, order_type, qty,
                                                         price, ETimeInForce::GoodTillCancel, uid1, coin);
    auto resp = user1.process(create_build_user1_buy.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id1);
    ASSERT_NE(order_checker.m_msg, nullptr);
  }

  {
    // user2挂单,Sell方向,限价单
    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id2, symbol, pz_index, ESide::Sell, order_type, qty,
                                                          price, ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id2);
    ASSERT_NE(order_checker.m_msg, nullptr);
  }

  {
    // user1成交记录
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg->header().user_id(), uid1);
    auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckSize(qty);
  }

  {
    // 测试场景:全仓tp和sl价格不合适,tp劣于仓位均价,sl优于仓位均价
    // 预期结果:设置失败
    take_profit_price = 9000 * 1e4;
    stop_loss_price = 11000 * 1e4;
    FutureOneOfSetTpSlTsBuilder builder;
    builder.SetValue(uid1, coin, symbol, pz_index, ETriggerBy::LastPrice, take_profit_price, ETriggerBy::LastPrice,
                     stop_loss_price, ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", "", 0, ETpSlMode::Full,
                     EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp = user1.process(builder.Build());
    ASSERT_NE(resp->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    // 测试场景:全仓tp和sl价格合适,tp优于仓位均价,sl劣于仓位均价
    // 预期结果:设置成功
    take_profit_price = 11000 * 1e4;
    stop_loss_price = 9000 * 1e4;
    FutureOneOfSetTpSlTsBuilder builder;
    builder.SetValue(uid1, coin, symbol, pz_index, ETriggerBy::LastPrice, take_profit_price, ETriggerBy::LastPrice,
                     stop_loss_price, ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", "", 0, ETpSlMode::Full,
                     EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp = user1.process(builder.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    for (const auto& order : result.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker checker(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::StopLoss) {
        checker
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, stop_loss_price, EStopOrderType::StopLoss,
                            EOrderType::Market, qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        checker
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, take_profit_price, EStopOrderType::TakeProfit,
                            EOrderType::Market, qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::LastPrice, take_profit_price, ETriggerBy::LastPrice, stop_loss_price, 0, 0);
  }

  {
    // 测试场景:全仓有tp和sl的情况下,更新tp和sl至合适价格
    // 预期结果:设置成功,覆盖更新仓位的tp和sl
    take_profit_price = 12000 * 1e4;
    stop_loss_price = 8000 * 1e4;
    FutureOneOfSetTpSlTsBuilder builder;
    builder.SetValue(uid1, coin, symbol, pz_index, ETriggerBy::LastPrice, take_profit_price, ETriggerBy::LastPrice,
                     stop_loss_price, ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", "", 0, ETpSlMode::Full,
                     EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp = user1.process(builder.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    for (const auto& order : result.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker checker(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::StopLoss) {
        checker
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, stop_loss_price, EStopOrderType::StopLoss,
                            EOrderType::Market, qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        checker
            .CheckTpSLOrder(ETriggerBy::LastPrice, ETpSlMode::Full, take_profit_price, EStopOrderType::TakeProfit,
                            EOrderType::Market, qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::LastPrice, take_profit_price, ETriggerBy::LastPrice, stop_loss_price, 0, 0);
  }

  {
    // 测试场景:使用设置tp和sl接口改单,修改触发价和触发类型
    // 预期结果:设置成功,校验价格
    take_profit_price = 10500 * 1e4;
    stop_loss_price = 9500 * 1e4;
    FutureOneOfSetTpSlTsBuilder builder;
    builder.SetValue(uid1, coin, symbol, pz_index, ETriggerBy::MarkPrice, take_profit_price, ETriggerBy::MarkPrice,
                     stop_loss_price, ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", "", 0, ETpSlMode::Full,
                     EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp = user1.process(builder.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    for (const auto& order : result.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker checker(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::StopLoss) {
        checker
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, stop_loss_price, EStopOrderType::StopLoss,
                            EOrderType::Market, qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
      } else if (stop_order_type == EStopOrderType::TakeProfit) {
        checker
            .CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, take_profit_price, EStopOrderType::TakeProfit,
                            EOrderType::Market, qty)
            .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::MarkPrice, take_profit_price, ETriggerBy::MarkPrice, stop_loss_price, 0, 0);
  }

  {
    // 测试场景:使用设置tp和sl接口取消订单,将触发价修改为0
    // 预期结果:订单被取消
    take_profit_price = 0;
    stop_loss_price = 0;
    FutureOneOfSetTpSlTsBuilder builder;
    builder.SetValue(uid1, coin, symbol, pz_index, ETriggerBy::UNKNOWN, take_profit_price, ETriggerBy::UNKNOWN,
                     stop_loss_price, ETriggerBy::UNKNOWN, -1, -1, -1, -1, "", "", 0, ETpSlMode::Full,
                     EOrderType::UNKNOWN, EOrderType::UNKNOWN, -1, -1, false);
    auto resp = user1.process(builder.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    for (const auto& order : result.RefFutureMarginResult().m_msg->related_orders()) {
      TransactDTOChecker cc(&order);
      auto stop_order_type = static_cast<EStopOrderType>(order.stop_order_type());
      if (stop_order_type == EStopOrderType::TakeProfit) {  // 此处为什么还是MarkPrice和10500触发
        cc.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 10500 * 1e4, EStopOrderType::TakeProfit,
                          EOrderType::Market, qty)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else if (stop_order_type == EStopOrderType::StopLoss) {  // 此处为什么还是MarkPrice和9500触发
        cc.CheckTpSLOrder(ETriggerBy::MarkPrice, ETpSlMode::Full, 9500 * 1e4, EStopOrderType::StopLoss,
                          EOrderType::Market, qty)
            .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
      } else {
        EXPECT_TRUE(false);
      }
    }
    auto position = result.RefFutureMarginResult().RefRelatedPosition(0);
    position.CheckTpSlTs(ETriggerBy::UNKNOWN, take_profit_price, ETriggerBy::UNKNOWN, stop_loss_price, 0, 0);
  }
}
