#include "test/biz/trade/future/feature/inverse/set_auto_add_margin/set_auto_add_margin.hpp"

#include <string>

#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/futures/modules/orderbiz/activeorderbiz/active_order_biz.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/future/func/draft_pkg_builder.h"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

int32_t InverseSetAutoAddMarginTest::SwitchAccountMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}
int32_t InverseSetAutoAddMarginTest::OpenAccount(int64_t uid) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::MergeSettingPosition);

  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

/*
测试场景：逐仓单仓模式，用户设置自动追加保证金
预期结果：设置自动追加保证金模式失败
*/
TEST_F(InverseSetAutoAddMarginTest, set_auto_add_margin) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  auto symbol = ESymbol::BTCUSD;
  auto coin = ECoin::BTC;

  EXPECT_EQ(0, OpenAccount(uid_1));

  EXPECT_EQ(SwitchAccountMode(uid_1, EAccountMode::Isolated), 0);

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;

  FutureSetAutoAddMarginBuilder build("", uid_1, coin, symbol, true, pz_index);
  auto resp = user1.process(build.Build());
  EXPECT_EQ(10001, resp->ret_code());
  EXPECT_EQ(resp->ret_msg(), "inverse symbol cannot set auto add margin");
}

std::shared_ptr<tmock::CTradeAppMock> InverseSetAutoAddMarginTest::te;
