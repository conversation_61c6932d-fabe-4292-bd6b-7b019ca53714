#include "test/biz/trade/future/feature/inverse/replace_so/replace_so.hpp"

#include <string>

#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/futures/modules/orderbiz/activeorderbiz/active_order_biz.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/future/func/draft_pkg_builder.h"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/mocks/hdts/message.hpp"

int32_t InverseReplaceSoTest::Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t InverseReplaceSoTest::SwitchAccountMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t InverseReplaceSoTest::OpenAccount(int64_t uid) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::MergeSettingPosition);

  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t InverseReplaceSoTest::SetSpotCollateralCoin(int64_t uid, ECoin coin) {
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SetSpotCollateralCoin);
  unified_v_2_request_dto->mutable_req_header()->set_req_id("set_collateral_coin_mode_1001_1");
  unified_v_2_request_dto->mutable_req_header()->set_coin(coin);
  auto* req =
      unified_v_2_request_dto->mutable_settingsreqgroupbody()->mutable_set_spot_collateral_coin()->add_coinlist();
  req->set_coin(coin);
  req->set_enable(true);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

TEST_F(InverseReplaceSoTest, test_ticksize_and_lotsize) {
  biz::user_id_t uid = 10000;
  stub user1(te, uid);

  auto const coin = ECoin::BTC;
  auto const symbol = ESymbol::BTCUSD;

  auto underlying_price = bbase::decimal::Decimal<>("10150");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(uid), 0);
  EXPECT_EQ(Deposit(uid, "0.1", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(uid, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(uid, EAccountMode::Cross), 0);

  double qty = 1000.5;
  biz::price_x_t price = 10100.25 * 1e4;
  biz::price_x_t trigger_price = 10050.25 * 1e4;

  // 条件单挂单
  // 测试场景:下条件单,订单价格不是TickSize整数倍,订单数量不是LotSize整数倍
  // 预期结果:下单成功,订单价格被修正,校验价格和数量
  FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
      uid, coin, symbol, te->GenUUID(), EPositionIndex::Single, ESide::Sell, EOrderType::Limit, qty, price,
      ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice, trigger_price,
      EPriceDirection::Falling, 0);
  auto resp = user1.process(create_so_build.Build());
  ASSERT_EQ(resp->result().related_orders_size(), 1);
  auto order_id = resp->result().related_orders().at(0).order_id();
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto related_order = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  EXPECT_EQ(related_order.m_msg->order_id(), order_id);
  EXPECT_EQ(related_order.m_msg->order_status(), EOrderStatus::Untriggered);
  EXPECT_EQ(related_order.m_msg->cross_status(), ECrossStatus::Init);
  ASSERT_EQ(related_order.m_msg->trigger_price_x(), 10050 * 1e4);
  ASSERT_EQ(related_order.m_msg->price_x(), 10100.5 * 1e4);
  ASSERT_EQ(related_order.m_msg->qty_x(), 1000);

  // 条件单改单
  // 测试场景:改条件单,订单价格不是TickSize整数倍,订单数量不是LotSize整数倍
  // 预期结果:改单成功,订单价格被修正,订单数量被修正,校验价格和数量
  qty = 900.6;
  price = 10080.75 * 1e4;
  trigger_price = 10040.75 * 1e4;
  FutureOneOfReplaceOrderBuilder replace_build(order_id, symbol, qty, price, user1.m_uid, coin, trigger_price);
  resp = user1.process(replace_build.Build());
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  related_order = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_EQ(related_order.m_msg->trigger_price_x(), 10040.5 * 1e4);
  ASSERT_EQ(related_order.m_msg->price_x(), 10081 * 1e4);
  ASSERT_EQ(related_order.m_msg->qty_x(), 900);
}

TEST_F(InverseReplaceSoTest, trigger_by_last_price) {
  biz::user_id_t uid = 10000;
  stub user1(te, uid);

  auto const coin = ECoin::BTC;
  auto const symbol = ESymbol::BTCUSD;

  auto underlying_price = bbase::decimal::Decimal<>("10150");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(uid), 0);
  EXPECT_EQ(Deposit(uid, "0.1", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(uid, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(uid, EAccountMode::Cross), 0);

  biz::size_x_t qty = 1000;
  biz::price_x_t price = 10100 * 1e4;
  biz::price_x_t trigger_price = 10050 * 1e4;

  // 测试场景:下两笔条件单,触发类型都是LastPrice,调整LastPrice,两笔同时触发
  // 预期结果:两笔条件单触发成功,校验撮合状态
  auto order_id_1 = te->GenUUID();
  FutureOneOfCreateOrderWithTriggerBuilder create_so_build_1(
      uid, coin, symbol, order_id_1, EPositionIndex::Single, ESide::Sell, EOrderType::Limit, qty, price,
      ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::LastPrice, trigger_price,
      EPriceDirection::Falling, 0);
  auto resp = user1.process(create_so_build_1.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  auto order_id_2 = te->GenUUID();
  FutureOneOfCreateOrderWithTriggerBuilder create_so_build_2(
      uid, coin, symbol, order_id_2, EPositionIndex::Single, ESide::Sell, EOrderType::Limit, qty, price,
      ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::LastPrice, trigger_price,
      EPriceDirection::Falling, 0);
  resp = user1.process(create_so_build_2.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 触发条件单
  bool has_checked_1 = false;
  bool has_checked_2 = false;
  bool triggered_to_ao = false;
  int try_times = 0;  // 最多尝试1次, 最多2.5s
  while (!triggered_to_ao && try_times < 5) {
    usleep(500 * 1000);
    // 只更新市价
    // 模拟下跌触发(last_price)10040 < (trigger_price)10050
    te->UpdateQuoteData(symbol, 5, "10040", "5000000");

    // 给Trigger发一个timer事件, 把Triggered订单发给PreWorker
    auto time_event = std::make_shared<event::TimerEvent>(0);
    time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
    time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
    std::int32_t ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);

    // 条件单触发转为活动单
    result = te->PopResult();
    if (result.m_msg == nullptr) {
      ++try_times;
      continue;
    }
    // 拿到active_so的result, 说明触发成功, 不需要循环发标记和timer了, 走完下面的检查流程即可
    // 获取订单数据
    auto related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_1);
    ASSERT_NE(related_order.m_msg, nullptr);
    related_order.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);
    related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::LastPrice, 10050 * 1e4, EPriceDirection::Falling);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
    ASSERT_NE(related_order.m_msg, nullptr);
    related_order.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);
    related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::LastPrice, 10050 * 1e4, EPriceDirection::Falling);

    // 条件单触发转为活动单, 撮合回报
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_1);
    ASSERT_NE(related_order.m_msg, nullptr);
    related_order.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::LastPrice, 10050 * 1e4, EPriceDirection::Falling);
    has_checked_1 = true;

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
    ASSERT_NE(related_order.m_msg, nullptr);
    related_order.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::LastPrice, 10050 * 1e4, EPriceDirection::Falling);
    has_checked_2 = true;

    triggered_to_ao = true;
  }
  ASSERT_TRUE(has_checked_1);
  ASSERT_TRUE(has_checked_2);
}

TEST_F(InverseReplaceSoTest, trigger_by_mark_price_v1) {
  biz::user_id_t uid = 10000;
  stub user1(te, uid);

  auto const coin = ECoin::BTC;
  auto const symbol = ESymbol::BTCUSD;

  auto underlying_price = bbase::decimal::Decimal<>("10150");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(uid), 0);
  EXPECT_EQ(Deposit(uid, "0.1", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(uid, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(uid, EAccountMode::Cross), 0);

  biz::size_x_t qty = 1000;
  biz::price_x_t price = 10100 * 1e4;
  biz::price_x_t trigger_price = 10050 * 1e4;

  {
    // 测试场景:下两笔条件单,触发类型都是MarkPrice,调整MarkPrice,两笔同时触发
    // 预期结果:两笔条件单触发成功,校验撮合状态
    auto order_id_1 = te->GenUUID();
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build_1(
        uid, coin, symbol, order_id_1, EPositionIndex::Single, ESide::Sell, EOrderType::Limit, qty, price,
        ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice, trigger_price,
        EPriceDirection::Falling, 0);
    auto resp = user1.process(create_so_build_1.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto order_id_2 = te->GenUUID();
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build_2(
        uid, coin, symbol, order_id_2, EPositionIndex::Single, ESide::Sell, EOrderType::Limit, qty, price,
        ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice, trigger_price,
        EPriceDirection::Falling, 0);
    resp = user1.process(create_so_build_2.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 触发条件单
    underlying_price = bbase::decimal::Decimal<>("10040");
    bool has_checked_1 = false;
    bool has_checked_2 = false;
    bool triggered_to_ao = false;
    int try_times = 0;  // 最多尝试1次, 最多2.5s
    while (!triggered_to_ao && try_times < 5) {
      usleep(500 * 1000);
      // 更新标记价格
      // 模拟下跌触发(mark_price)10040 < (trigger_price)10050
      underlying_price = underlying_price.Sub(1);  // 每次需要变一下价格, 不然会被trigger拦截
      te->AddMarkPriceOnly(symbol, underlying_price.IntPart());
      // 给Trigger发一个timer事件, 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      std::int32_t ret =
          te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 条件单触发转为活动单
      result = te->PopResult();
      if (result.m_msg == nullptr) {
        ++try_times;
        continue;
      }
      // 拿到active_so的result, 说明触发成功, 不需要循环发标记和timer了, 走完下面的检查流程即可
      // 获取订单数据
      auto related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_1);
      ASSERT_NE(related_order.m_msg, nullptr);
      related_order.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);
      related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::MarkPrice, 10050 * 1e4, EPriceDirection::Falling);

      result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
      ASSERT_NE(related_order.m_msg, nullptr);
      related_order.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);
      related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::MarkPrice, 10050 * 1e4, EPriceDirection::Falling);

      // 条件单触发转为活动单, 撮合回报
      result = te->PopResult();
      related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_1);
      ASSERT_NE(related_order.m_msg, nullptr);
      related_order.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
      related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::MarkPrice, 10050 * 1e4, EPriceDirection::Falling);
      has_checked_1 = true;

      // 条件单触发转为活动单, 撮合回报
      result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
      ASSERT_NE(related_order.m_msg, nullptr);
      related_order.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
      related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::MarkPrice, 10050 * 1e4, EPriceDirection::Falling);
      has_checked_2 = true;

      triggered_to_ao = true;
    }
    ASSERT_TRUE(has_checked_1);
    ASSERT_TRUE(has_checked_2);
  }

  underlying_price = bbase::decimal::Decimal<>("10150");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  {
    // 测试场景:下两笔条件单,一笔触发类型是MarkPrice,一笔触发类型是LastPrice,调整MarkPrice触发,LastPrice不变
    // 预期结果:触发类型为MarkPrice订单触发成功,另一笔没有触发,校验撮合状态
    auto order_id_1 = te->GenUUID();
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build_1(
        uid, coin, symbol, order_id_1, EPositionIndex::Single, ESide::Sell, EOrderType::Limit, qty, price,
        ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice, trigger_price,
        EPriceDirection::Falling, 0);
    auto resp = user1.process(create_so_build_1.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto order_id_2 = te->GenUUID();
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build_2(
        uid, coin, symbol, order_id_2, EPositionIndex::Single, ESide::Sell, EOrderType::Limit, qty, price,
        ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::LastPrice, trigger_price,
        EPriceDirection::Falling, 0);
    resp = user1.process(create_so_build_2.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 触发条件单
    underlying_price = bbase::decimal::Decimal<>("10040");
    bool has_checked_1 = false;
    bool triggered_to_ao = false;
    int try_times = 0;  // 最多尝试1次, 最多2.5s
    while (!triggered_to_ao && try_times < 5) {
      usleep(500 * 1000);
      // 只更新MarkPrice
      // 模拟下跌触发(mark_price)10040 < (trigger_price)10050
      underlying_price = underlying_price.Sub(1);
      te->AddMarkPriceOnly(symbol, underlying_price.IntPart());

      // 给Trigger发一个timer事件, 把Triggered订单发给PreWorker
      auto time_event = std::make_shared<event::TimerEvent>(0);
      time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
      time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
      std::int32_t ret =
          te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
      ASSERT_EQ(ret, error::kErrorCodeSuccess);

      // 条件单触发转为活动单
      result = te->PopResult();
      if (result.m_msg == nullptr) {
        ++try_times;
        continue;
      }
      // 拿到active_so的result, 说明触发成功, 不需要循环发标记和timer了, 走完下面的检查流程即可
      // 获取订单数据
      auto related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_1);
      ASSERT_NE(related_order.m_msg, nullptr);
      related_order.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);
      related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::MarkPrice, 10050 * 1e4, EPriceDirection::Falling);

      result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_1);
      ASSERT_NE(related_order.m_msg, nullptr);
      related_order.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
      related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::MarkPrice, 10050 * 1e4, EPriceDirection::Falling);

      result = te->PopResult();
      ASSERT_EQ(result.m_msg.get(), nullptr);

      has_checked_1 = true;
      triggered_to_ao = true;
    }
    ASSERT_TRUE(has_checked_1);
  }
}

TEST_F(InverseReplaceSoTest, trigger_by_mark_price_v2) {
  biz::user_id_t uid = 653106;
  biz::coin_t const coin = ECoin::BTC;
  biz::symbol_t const symbol = ESymbol::BTCUSD;
  std::string order_id = te->GenUUID();
  EPositionIndex const position_idx = EPositionIndex::Single;
  ESide const side = ESide::Sell;
  EOrderType const order_type = EOrderType::Limit;
  biz::size_x_t qty = 1 * 1e4;
  biz::price_x_t const price = 10100 * 1e4;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;
  EStopOrderType const stop_order_type = EStopOrderType::Stop;
  ETriggerBy const trigger_by = ETriggerBy::MarkPrice;
  biz::price_x_t const trigger_price = 10050 * 1e4;
  EPriceDirection const expected_direction = EPriceDirection::Falling;
  biz::price_x_t base_price = 0;
  bool const close_on_trigger = false;
  biz::price_x_t const tp_x = 0;
  biz::price_x_t const sl_x = 0;
  ESmpType const smp_type = ESmpType::None;
  int32_t const smp_group = 0;

  // 校准标记价格 (mark_price)10150 > (trigger_price)10050
  auto underlying_price = bbase::decimal::Decimal<>("10150");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 构建客户端
  stub user1(te, uid);

  // 充值
  auto req_id = te->GenUUID();
  auto trans_id = te->GenUUID();
  auto bonus_change = "0";  // 赠金
  EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                     std::to_string(50000), bonus_change);
  // 发rpc请求
  auto deposit_resp = user1.process(deposit_build.Build());
  // 校验rpc回报
  ASSERT_EQ(deposit_resp->ret_code(), error::kErrorCodeSuccess);
  // 取trading result
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg, nullptr);

  // ============================================================================================================
  // 报单
  // ============================================================================================================
  FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
      uid, coin, symbol, order_id, position_idx, side, order_type, qty, price, time_in_force, stop_order_type,
      trigger_by, trigger_price, expected_direction, base_price, close_on_trigger, tp_x, sl_x, smp_type, smp_group);

  // 发rpc请求
  auto create_so_resp = user1.process(create_so_build.Build());
  // 校验rpc回报
  ASSERT_EQ(create_so_resp->ret_code(), error::kErrorCodeSuccess);

  // 取trading result
  auto create_so_result = te->PopResult();
  ASSERT_NE(create_so_result.m_msg, nullptr);

  // 获取订单数据
  auto order_check = create_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  // 校验订单状态
  order_check.CheckQty(uid, qty);
  order_check.CheckPrice(uid, price);
  order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
  order_check.CheckExecInst(uid, EExecInst::None);
  order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);

  // 模拟下跌触发 (mark_price)10040 < (trigger_price)10050
  underlying_price = bbase::decimal::Decimal<>("10040");
  bool to_check_res = false;
  bool checked = false;
  int try_times = 0;  // 最多尝试1次，最多2.5s
  while (!checked && try_times < 5) {
    usleep(500 * 1000);

    // 更新标记价格
    // 模拟下跌触发 (mark_price)10040- < (trigger_price)10050
    underlying_price = underlying_price.Sub(1);  // 每次需要变一下价格，不然会被trigger拦截
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
    auto time_event = std::make_shared<event::TimerEvent>(0);
    time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
    time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
    std::int32_t ret = te->pipeline_->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);

    // 取trading result
    auto active_so_result = te->PopResult();
    if (active_so_result.m_msg == nullptr) {
      // 没拿到active_so的result, 继续发标记价格，尝试触发
      try_times++;
      continue;
    }

    // 验证header内容
    ASSERT_NE(active_so_result.m_msg->header().req_init_at_e9(), 0);
    ASSERT_NE(active_so_result.m_msg->header().req_recv_at_e9(), 0);
    ASSERT_NE(active_so_result.m_msg->header().req_expire_at_e9(), 0);
    ASSERT_EQ(active_so_result.m_msg->header().user_id(), uid);
    ASSERT_EQ(active_so_result.m_msg->header().action(), EAction::SyncStopOrder);
    ASSERT_EQ(active_so_result.m_msg->header().op_from(), "trigger");

    // 拿到active_so的result, 说明触发成功，不需要循环发标记价和timer了，走完下面的检查流程即可
    checked = true;

    // 获取订单数据
    order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckQty(uid, qty);
    order_check.CheckPrice(uid, price);
    order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);

    // 取trading result
    active_so_result = te->PopResult();
    ASSERT_NE(active_so_result.m_msg, nullptr);

    // 获取订单数据
    order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckQty(uid, qty);
    order_check.CheckPrice(uid, price);
    order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    to_check_res = true;
  }
  ASSERT_EQ(to_check_res, true);
}

TEST_F(InverseReplaceSoTest, cancel_stop_order) {
  biz::user_id_t uid = 10000;
  stub user1(te, uid);

  auto const coin = ECoin::BTC;
  auto const symbol = ESymbol::BTCUSD;

  auto underlying_price = bbase::decimal::Decimal<>("10150");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(uid), 0);
  EXPECT_EQ(Deposit(uid, "0.1", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(uid, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(uid, EAccountMode::Cross), 0);

  biz::size_x_t qty = 1000;
  biz::price_x_t price = 10100 * 1e4;
  biz::price_x_t trigger_price = 10050 * 1e4;
  {
    // 测试场景:下一笔条件单,取消改条件单
    // 预期结果:取消成功,校验订单状态
    // 条件单挂单
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
        uid, coin, symbol, order_id, EPositionIndex::Single, ESide::Sell, EOrderType::Limit, qty, price,
        ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice, trigger_price,
        EPriceDirection::Falling, 0);
    auto resp = user1.process(create_so_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 取消条件单
    FutureOneOfCancelOrderBuilder cancel_builder(order_id, symbol, uid, coin);
    resp = user1.process(cancel_builder.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Deactivated);
  }

  {
    // 测试场景:下两笔条件单,CancelAll,类型是StopOrder
    // 预期结果:取消两笔条件单成功,校验订单状态
    // 条件单挂单
    auto order_id_1 = te->GenUUID();
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build_1(
        uid, coin, symbol, order_id_1, EPositionIndex::Single, ESide::Sell, EOrderType::Limit, qty, price,
        ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice, trigger_price,
        EPriceDirection::Falling, 0);
    auto resp = user1.process(create_so_build_1.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto order_id_2 = te->GenUUID();
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build_2(
        uid, coin, symbol, order_id_2, EPositionIndex::Single, ESide::Sell, EOrderType::Limit, qty, price,
        ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice, trigger_price,
        EPriceDirection::Falling, 0);
    resp = user1.process(create_so_build_2.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 取消所有条件单
    ECancelType cancelType = ECancelType::CancelByUser;
    ECancelAllType cancelAllType = ECancelAllType::StopOrder;
    biz::remark_t remark{};
    FutureCancelAllBuilder cancelAllBuilder(symbol, coin, uid, cancelType, cancelAllType, remark.GetValue());
    resp = user1.process(cancelAllBuilder.Build());
    result = te->PopResult(50000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_1);
    ASSERT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Deactivated);
    orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
    ASSERT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Deactivated);
  }

  {
    // 测试场景:下一笔条件单,一笔活动单,CancelAll, 类型是Unknown
    // 预期结果:活动单取消成功,条件单没有取消
    // 条件单挂单
    auto order_id_1 = te->GenUUID();
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
        uid, coin, symbol, order_id_1, EPositionIndex::Single, ESide::Sell, EOrderType::Limit, qty, price,
        ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice, trigger_price,
        EPriceDirection::Falling, 0);
    auto resp = user1.process(create_so_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 限价单挂单
    auto order_id_2 = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_ao_build(order_id_2, symbol, EPositionIndex::Single, ESide::Sell,
                                                  EOrderType::Limit, qty, price, ETimeInForce::GoodTillCancel, uid,
                                                  coin);
    resp = user1.process(create_ao_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 取消订单
    ECancelType cancelType = ECancelType::CancelByUser;
    ECancelAllType cancelAllType = ECancelAllType::UNKNOWN;
    biz::remark_t remark{};
    FutureCancelAllBuilder cancelAllBuilder(symbol, coin, uid, cancelType, cancelAllType, remark.GetValue());
    resp = user1.process(cancelAllBuilder.Build());
    result = te->PopResult(50000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_1);
    ASSERT_EQ(orderCheck.m_msg, nullptr);
    orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
    ASSERT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Cancelled);
  }
}

// 测试场景:逐仓+反向条件单+改单+触发
TEST_F(InverseReplaceSoTest, isolated_mode) {
  biz::user_id_t uid = 10000;
  stub user1(te, uid);

  auto const coin = ECoin::BTC;
  auto const symbol = ESymbol::BTCUSD;

  auto underlying_price = bbase::decimal::Decimal<>("10150");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 逐仓
  EXPECT_EQ(OpenAccount(uid), 0);
  EXPECT_EQ(Deposit(uid, "0.1", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(uid, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(uid, EAccountMode::Isolated), 0);

  biz::size_x_t qty = 1000;
  biz::price_x_t price = 10100 * 1e4;
  biz::price_x_t trigger_price = 10050 * 1e4;

  // 条件单挂单
  FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
      uid, coin, symbol, te->GenUUID(), EPositionIndex::Single, ESide::Sell, EOrderType::Limit, qty, price,
      ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice, trigger_price,
      EPriceDirection::Falling, 0);
  auto resp = user1.process(create_so_build.Build());
  ASSERT_EQ(resp->result().related_orders_size(), 1);
  auto order_id = resp->result().related_orders().at(0).order_id();
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto related_order = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  EXPECT_EQ(related_order.m_msg->order_id(), order_id);
  EXPECT_EQ(related_order.m_msg->order_status(), EOrderStatus::Untriggered);
  EXPECT_EQ(related_order.m_msg->cross_status(), ECrossStatus::Init);
  ASSERT_EQ(related_order.m_msg->trigger_price_x(), trigger_price);
  ASSERT_EQ(related_order.m_msg->price_x(), price);
  ASSERT_EQ(related_order.m_msg->qty_x(), qty);

  // 条件单改单
  qty = 900;
  price = 10080 * 1e4;
  trigger_price = 10040 * 1e4;
  FutureOneOfReplaceOrderBuilder replace_build(order_id, symbol, qty, price, user1.m_uid, coin, trigger_price);
  resp = user1.process(replace_build.Build());
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  related_order = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_EQ(related_order.m_msg->trigger_price_x(), trigger_price);
  ASSERT_EQ(related_order.m_msg->price_x(), price);
  ASSERT_EQ(related_order.m_msg->qty_x(), qty);

  // 触发条件单
  underlying_price = bbase::decimal::Decimal<>("10030");
  bool has_checked = false;
  bool triggered_to_ao = false;
  int try_times = 0;  // 最多尝试1次, 最多2.5s
  while (!triggered_to_ao && try_times < 5) {
    usleep(500 * 1000);
    // 更新标记价格
    // 模拟下跌触发(mark_price)10040 < (trigger_price)10050
    underlying_price = underlying_price.Sub(1);  // 每次需要变一下价格, 不然会被trigger拦截
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 给Trigger发一个timer事件, 把Triggered订单发给PreWorker
    auto time_event = std::make_shared<event::TimerEvent>(0);
    time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
    time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
    std::int32_t ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);

    // 条件单触发转为活动单
    result = te->PopResult();
    if (result.m_msg == nullptr) {
      ++try_times;
      continue;
    }
    // 拿到active_so的result, 说明触发成功, 不需要循环发标记和timer了, 走完下面的检查流程即可
    triggered_to_ao = true;
    // 获取订单数据
    related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(related_order.m_msg, nullptr);
    related_order.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);
    related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::MarkPrice, 10040 * 1e4, EPriceDirection::Falling);

    // 条件单触发转为活动单, 撮合回报
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    related_order.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
    related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::MarkPrice, 10040 * 1e4, EPriceDirection::Falling);
    has_checked = true;
  }
  ASSERT_TRUE(has_checked);
}

// 测试场景:全仓+反向条件单+改单+触发
TEST_F(InverseReplaceSoTest, cross_mode) {
  biz::user_id_t uid = 10000;
  stub user1(te, uid);

  auto const coin = ECoin::BTC;
  auto const symbol = ESymbol::BTCUSD;

  auto underlying_price = bbase::decimal::Decimal<>("10050");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 全仓
  EXPECT_EQ(OpenAccount(uid), 0);
  EXPECT_EQ(Deposit(uid, "0.1", coin), 0);
  EXPECT_EQ(SetSpotCollateralCoin(uid, static_cast<enums::ecoin::Coin>(coin)), 0);
  EXPECT_EQ(SwitchAccountMode(uid, EAccountMode::Cross), 0);

  biz::size_x_t qty = 1000;
  biz::price_x_t price = 10080 * 1e4;
  biz::price_x_t trigger_price = 10100 * 1e4;

  // 条件单挂单
  FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
      uid, coin, symbol, te->GenUUID(), EPositionIndex::Single, ESide::Buy, EOrderType::Limit, qty, price,
      ETimeInForce::GoodTillCancel, EStopOrderType::Stop, ETriggerBy::MarkPrice, trigger_price, EPriceDirection::Rising,
      0);
  auto resp = user1.process(create_so_build.Build());
  ASSERT_EQ(resp->result().related_orders_size(), 1);
  auto order_id = resp->result().related_orders().at(0).order_id();
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);

  // 挂单回报
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto related_order = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  EXPECT_EQ(related_order.m_msg->order_id(), order_id);
  EXPECT_EQ(related_order.m_msg->order_status(), EOrderStatus::Untriggered);
  EXPECT_EQ(related_order.m_msg->cross_status(), ECrossStatus::Init);
  ASSERT_EQ(related_order.m_msg->trigger_price_x(), trigger_price);
  ASSERT_EQ(related_order.m_msg->price_x(), price);
  ASSERT_EQ(related_order.m_msg->qty_x(), qty);

  // 条件单改单
  qty = 900;
  price = 10070 * 1e4;
  trigger_price = 10150 * 1e4;
  FutureOneOfReplaceOrderBuilder replace_build_1(order_id, symbol, qty, price, user1.m_uid, coin, trigger_price);
  resp = user1.process(replace_build_1.Build());
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  related_order = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_EQ(related_order.m_msg->trigger_price_x(), trigger_price);
  ASSERT_EQ(related_order.m_msg->price_x(), price);
  ASSERT_EQ(related_order.m_msg->qty_x(), qty);

  // 改触发价格触发条件单
  trigger_price = 10000 * 1e4;
  FutureOneOfReplaceOrderBuilder replace_build_2(order_id, symbol, qty, price, user1.m_uid, coin, trigger_price);
  resp = user1.process(replace_build_2.Build());
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);

  // 改单回报
  result = te->PopResult();
  related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(related_order.m_msg, nullptr);
  related_order.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);
  related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::MarkPrice, 10000 * 1e4, EPriceDirection::Rising);

  // 条件单触发回报
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  related_order.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);
  related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::MarkPrice, 10000 * 1e4, EPriceDirection::Rising);

  // 活动单回报
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  related_order = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  related_order.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
  related_order.CheckStopOrder(EStopOrderType::Stop, ETriggerBy::MarkPrice, 10000 * 1e4, EPriceDirection::Rising);
}

TEST_F(InverseReplaceSoTest, create_replace_cancel) {
  biz::user_id_t uid = 653106;
  biz::coin_t const coin = ECoin::BTC;
  biz::symbol_t const symbol = ESymbol::BTCUSD;
  std::string order_id = te->GenUUID();
  EPositionIndex const position_idx = EPositionIndex::Single;
  ESide const side = ESide::Sell;
  EOrderType const order_type = EOrderType::Limit;
  biz::size_x_t qty = 1 * 1e4;
  biz::price_x_t const price = 10100 * 1e4;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;
  EStopOrderType const stop_order_type = EStopOrderType::Stop;
  ETriggerBy trigger_by = ETriggerBy::MarkPrice;
  biz::price_x_t trigger_price = 10050 * 1e4;
  EPriceDirection const expected_direction = EPriceDirection::Falling;
  biz::price_x_t base_price = 0;
  bool const close_on_trigger = false;
  biz::price_x_t const tp_x = 0;
  biz::price_x_t const sl_x = 0;
  ESmpType const smp_type = ESmpType::None;
  int32_t const smp_group = 0;

  // 校准标记价格 (mark_price)10150 > (trigger_price)10050
  auto const underlying_price = bbase::decimal::Decimal<>("10150");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 构建客户端
  stub user1(te, uid);

  // 充值
  auto req_id = te->GenUUID();
  auto trans_id = te->GenUUID();
  auto bonus_change = "0";  // 赠金
  EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                     std::to_string(100000), bonus_change);
  // 发rpc请求
  auto deposit_resp = user1.process(deposit_build.Build());
  // 校验rpc回报
  ASSERT_EQ(deposit_resp->ret_code(), error::kErrorCodeSuccess);
  // 取trading result
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg, nullptr);

  // ============================================================================================================
  // 报单
  // ============================================================================================================
  FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
      uid, coin, symbol, order_id, position_idx, side, order_type, qty, price, time_in_force, stop_order_type,
      trigger_by, trigger_price, expected_direction, base_price, close_on_trigger, tp_x, sl_x, smp_type, smp_group);

  // 发rpc请求
  auto create_so_resp = user1.process(create_so_build.Build());
  // 校验rpc回报
  ASSERT_EQ(create_so_resp->ret_code(), error::kErrorCodeSuccess);

  // 取trading result
  auto create_so_result = te->PopResult();
  ASSERT_NE(create_so_result.m_msg, nullptr);

  // 获取订单数据
  auto order_check = create_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  // 校验订单状态
  order_check.CheckQty(uid, qty);
  order_check.CheckPrice(uid, price);
  order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
  order_check.CheckExecInst(uid, EExecInst::None);
  order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);

  // ============================================================================================================
  // 改单
  // qty + 1 * 1e4-> 2 * 1e4
  // trigger_price + 50 -> 10100 * 1e4
  // trigger_by mark_price -> last_price
  // ============================================================================================================
  qty = 2 * 1e4;
  trigger_price = 10100 * 1e4;
  trigger_by = ETriggerBy::LastPrice;
  FutureOneOfReplaceOrderBuilder replace_so_build(order_id, symbol, qty, price, uid, coin, trigger_price, trigger_by);

  // 发rpc请求
  auto replace_so_resp = user1.process(replace_so_build.Build());
  // 校验rpc回报
  ASSERT_EQ(replace_so_resp->ret_code(), error::kErrorCodeSuccess);

  // 取trading result
  auto replace_so_result = te->PopResult();
  ASSERT_NE(replace_so_result.m_msg, nullptr);

  // 获取订单数据
  order_check = replace_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  // 校验订单状态
  order_check.CheckQty(uid, qty);
  order_check.CheckPrice(uid, price);
  order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
  order_check.CheckExecInst(uid, EExecInst::None);
  order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced);

  // ============================================================================================================
  // 撤单
  // ============================================================================================================
  FutureOneOfCancelOrderBuilder cancel_so_build(order_id, symbol, uid, coin);

  // 发rpc请求
  auto cancel_so_resp = user1.process(cancel_so_build.Build());
  // 校验rpc回报
  ASSERT_EQ(cancel_so_resp->ret_code(), error::kErrorCodeSuccess);

  // 取trading result
  auto cancel_so_result = te->PopResult();
  ASSERT_NE(cancel_so_result.m_msg, nullptr);

  // 获取订单数据
  order_check = cancel_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  // 校验订单状态
  order_check.CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
}

TEST_F(InverseReplaceSoTest, create_max_limit) {
  biz::user_id_t uid = 653106;
  biz::coin_t const coin = ECoin::BTC;
  biz::symbol_t const symbol = ESymbol::BTCUSD;
  EPositionIndex const position_idx = EPositionIndex::Single;
  ESide const side = ESide::Sell;
  EOrderType const order_type = EOrderType::Limit;
  biz::size_x_t qty = 1 * 1e4;
  biz::price_x_t const price = 10100 * 1e4;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;
  EStopOrderType const stop_order_type = EStopOrderType::Stop;
  ETriggerBy trigger_by = ETriggerBy::MarkPrice;
  biz::price_x_t trigger_price = 10050 * 1e4;
  EPriceDirection const expected_direction = EPriceDirection::Falling;
  biz::price_x_t base_price = 0;
  bool const close_on_trigger = false;
  biz::price_x_t const tp_x = 0;
  biz::price_x_t const sl_x = 0;
  ESmpType const smp_type = ESmpType::None;
  int32_t const smp_group = 0;

  // 校准标记价格 (mark_price)10150 > (trigger_price)10050
  auto const underlying_price = bbase::decimal::Decimal<>("10150");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 构建客户端
  stub user1(te, uid);

  // 充值
  auto req_id = te->GenUUID();
  auto trans_id = te->GenUUID();
  auto bonus_change = "0";  // 赠金
  EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                     std::to_string(500000), bonus_change);
  // 发rpc请求
  auto deposit_resp = user1.process(deposit_build.Build());
  // 校验rpc回报
  ASSERT_EQ(deposit_resp->ret_code(), error::kErrorCodeSuccess);
  // 取trading result
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg, nullptr);

  // ============================================================================================================
  // 报单 不能超过10个
  // ============================================================================================================
  for (int i = 0; i <= 10; i++) {
    std::string order_id = te->GenUUID();
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
        uid, coin, symbol, order_id, position_idx, side, order_type, qty, price, time_in_force, stop_order_type,
        trigger_by, trigger_price, expected_direction, base_price, close_on_trigger, tp_x, sl_x, smp_type, smp_group);

    // 发rpc请求
    auto create_so_resp = user1.process(create_so_build.Build());
    // 校验rpc回报
    if (i == 10) {
      ASSERT_EQ(create_so_resp->ret_code(), error::kErrorCodeNumOfSoExceeded);
    } else {
      ASSERT_EQ(create_so_resp->ret_code(), error::kErrorCodeSuccess);
    }
  }
}

TEST_F(InverseReplaceSoTest, pre_create_stop_order) {
  biz::user_id_t uid = 653106;
  biz::coin_t const coin = ECoin::BTC;
  biz::symbol_t const symbol = ESymbol::BTCUSD;
  std::string order_id = te->GenUUID();
  EPositionIndex const position_idx = EPositionIndex::Single;
  ESide const side = ESide::Sell;
  EOrderType const order_type = EOrderType::Limit;
  biz::size_x_t qty = 1 * 1e4;
  biz::price_x_t const price = 10040 * 1e4;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;
  EStopOrderType const stop_order_type = EStopOrderType::Stop;
  ETriggerBy trigger_by = ETriggerBy::MarkPrice;
  biz::price_x_t trigger_price = 10100 * 1e4;
  EPriceDirection const expected_direction = EPriceDirection::Falling;
  biz::price_x_t base_price = 0;
  bool const close_on_trigger = false;
  biz::price_x_t const tp_x = 0;
  biz::price_x_t const sl_x = 0;
  ESmpType const smp_type = ESmpType::None;
  int32_t const smp_group = 0;

  // 校准标记价格 (mark_price)10150 > (trigger_price)10050
  auto const underlying_price = bbase::decimal::Decimal<>("10150");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 构建客户端
  stub user1(te, uid);

  // 充值
  auto req_id = te->GenUUID();
  auto trans_id = te->GenUUID();
  auto bonus_change = "0";  // 赠金
  EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                     std::to_string(50000), bonus_change);
  // 发rpc请求
  auto deposit_resp = user1.process(deposit_build.Build());
  // 校验rpc回报
  ASSERT_EQ(deposit_resp->ret_code(), 0);
  // 取trading result
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg, nullptr);

  // ============================================================================================================
  // 预报单
  // ============================================================================================================
  FutureOneOfPreCreateOrderWithTriggerBuilder pre_create_so_build_1(
      uid, coin, symbol, order_id, position_idx, side, order_type, qty, price, time_in_force, stop_order_type,
      trigger_by, trigger_price, expected_direction, base_price, close_on_trigger, tp_x, sl_x, smp_type, smp_group);

  // 发rpc请求
  auto create_so_resp = user1.process(pre_create_so_build_1.Build());
  // 校验rpc回报
  ASSERT_EQ(create_so_resp->ret_code(), error::kErrorCodeSuccess);

  trigger_price = 0.0001 * 1e4;
  // ============================================================================================================
  // 预报单
  // ============================================================================================================
  FutureOneOfPreCreateOrderWithTriggerBuilder pre_create_so_build_2(
      uid, coin, symbol, order_id, position_idx, side, order_type, qty, price, time_in_force, stop_order_type,
      trigger_by, trigger_price, expected_direction, base_price, close_on_trigger, tp_x, sl_x, smp_type, smp_group);

  // 发rpc请求
  create_so_resp = user1.process(pre_create_so_build_2.Build());
  // 校验rpc回报
  ASSERT_EQ(create_so_resp->ret_code(), error::kErrorCodePriceOutOfRange);
}

TEST_F(InverseReplaceSoTest, trigger_to_active_by_quote_falling_v1) {
  biz::user_id_t uid = 653106;
  biz::coin_t const coin = ECoin::BTC;
  biz::cross_idx_t const cross_idx = 5;
  biz::symbol_t const symbol = ESymbol::BTCUSD;

  // 构建客户端
  stub user1(te, uid);

  // 充值
  auto req_id = te->GenUUID();
  auto trans_id = te->GenUUID();
  auto bonus_change = "0";  // 赠金
  EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                     std::to_string(50000), bonus_change);
  // 发rpc请求
  auto deposit_resp = user1.process(deposit_build.Build());
  // 校验rpc回报
  ASSERT_EQ(deposit_resp->ret_code(), error::kErrorCodeSuccess);
  // 取trading result
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg, nullptr);

  // ============================================================================================================
  // 校准行情
  // ============================================================================================================
  // 校准标记价格 (mark_price)29709.32 < (trigger_price)30982
  auto underlying_price = bbase::decimal::Decimal<>("29709.32");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 校准最新价格 (last_price)30990 > (trigger_price)30982
  bool sync_trigger = true;
  auto lv0_px = "30990";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // ============================================================================================================
  // 报单
  // ============================================================================================================
  std::string order_id = "6602f5ed-05fe-45e6-9c7e-ee675280d612";
  EPositionIndex const position_idx = EPositionIndex::Single;
  ESide const side = ESide::Sell;
  biz::size_x_t qty = 1 * 1e4;
  biz::price_x_t const price = 0;
  EOrderType const order_type = EOrderType::Market;
  ETimeInForce const time_in_force = ETimeInForce::ImmediateOrCancel;
  EStopOrderType const stop_order_type = EStopOrderType::Stop;
  ETriggerBy const trigger_by = ETriggerBy::LastPrice;
  biz::price_x_t const trigger_price = 30982 * 1e4;
  biz::price_x_t base_price = 30990 * 1e4;
  EPriceDirection const expected_direction = EPriceDirection::Falling;
  bool const close_on_trigger = false;
  biz::price_x_t const tp_x = 0;
  biz::price_x_t const sl_x = 0;
  ESmpType const smp_type = ESmpType::None;
  int32_t const smp_group = 0;
  FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
      uid, coin, symbol, order_id, position_idx, side, order_type, qty, price, time_in_force, stop_order_type,
      trigger_by, trigger_price, expected_direction, base_price, close_on_trigger, tp_x, sl_x, smp_type, smp_group);

  // 发rpc请求
  auto create_so_resp = user1.process(create_so_build.Build());
  // 校验rpc回报
  ASSERT_EQ(create_so_resp->ret_code(), error::kErrorCodeSuccess);

  // 取trading result
  auto create_so_result = te->PopResult();
  ASSERT_NE(create_so_result.m_msg, nullptr);

  // 获取订单数据
  auto order_check = create_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  // 校验订单状态
  order_check.CheckQty(uid, qty);
  order_check.CheckPrice(uid, price);
  order_check.CheckTimeInForce(time_in_force);
  order_check.CheckExecInst(uid, EExecInst::None);
  order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);

  bool to_check_res = false;
  bool checked = false;
  int try_times = 0;  // 最多尝试1次，最多2.5s
  while (!checked && try_times < 2) {
    usleep(500 * 1000);

    // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
    auto time_event = std::make_shared<event::TimerEvent>(0);
    time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
    time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
    auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);

    // 正确的情况是下面的逻辑都不会走到， 条件单不会被触发
    // 取trading result
    auto active_so_result = te->PopResult();
    if (active_so_result.m_msg == nullptr) {
      // 没拿到active_so的result, 继续发标记价格，尝试触发
      try_times++;
      continue;
    }

    // 拿到active_so的result, 说明触发成功，不需要循环发标记价和timer了，走完下面的检查流程即可
    checked = true;

    // 获取订单数据
    order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckQty(uid, qty);
    order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);

    // 取trading result
    active_so_result = te->PopResult();
    ASSERT_NE(active_so_result.m_msg, nullptr);

    // 获取订单数据
    order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckQty(uid, qty);
    order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);

    to_check_res = true;
  }
  ASSERT_EQ(to_check_res, false);
}

TEST_F(InverseReplaceSoTest, trigger_to_active_by_quote_falling_v2) {
  biz::user_id_t uid = 653106;
  biz::coin_t const coin = ECoin::BTC;
  biz::cross_idx_t const cross_idx = 5;
  biz::symbol_t const symbol = ESymbol::BTCUSD;
  std::string order_id = te->GenUUID();
  EPositionIndex const position_idx = EPositionIndex::Single;
  ESide const side = ESide::Sell;
  EOrderType const order_type = EOrderType::Limit;
  biz::size_x_t qty = 1 * 1e4;
  biz::price_x_t const price = 10100 * 1e4;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;
  EStopOrderType const stop_order_type = EStopOrderType::Stop;
  ETriggerBy const trigger_by = ETriggerBy::LastPrice;
  biz::price_x_t const trigger_price = 10050 * 1e4;
  EPriceDirection const expected_direction = EPriceDirection::Falling;
  biz::price_x_t base_price = 0;
  bool const close_on_trigger = false;
  biz::price_x_t const tp_x = 0;
  biz::price_x_t const sl_x = 0;
  ESmpType const smp_type = ESmpType::None;
  int32_t const smp_group = 0;

  // 校准标记价格 (mark_price)10150 > (trigger_price)10050
  auto underlying_price = bbase::decimal::Decimal<>("10150");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 校准最新价格 (last_price)10150 > (trigger_price)10050
  auto lv0_px = "10150";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty);

  // 构建客户端
  stub user1(te, uid);

  // 充值
  auto req_id = te->GenUUID();
  auto trans_id = te->GenUUID();
  auto bonus_change = "0";  // 赠金
  EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                     std::to_string(5000000), bonus_change);
  // 发rpc请求
  auto deposit_resp = user1.process(deposit_build.Build());
  // 校验rpc回报
  ASSERT_EQ(deposit_resp->ret_code(), error::kErrorCodeSuccess);
  // 取trading result
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg, nullptr);

  // ============================================================================================================
  // 报单
  // ============================================================================================================
  FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
      uid, coin, symbol, order_id, position_idx, side, order_type, qty, price, time_in_force, stop_order_type,
      trigger_by, trigger_price, expected_direction, base_price, close_on_trigger, tp_x, sl_x, smp_type, smp_group);

  // 发rpc请求
  auto create_so_resp = user1.process(create_so_build.Build());
  // 校验rpc回报
  ASSERT_EQ(create_so_resp->ret_code(), error::kErrorCodeSuccess);

  // 取trading result
  auto create_so_result = te->PopResult();
  ASSERT_NE(create_so_result.m_msg, nullptr);

  // 获取订单数据
  auto order_check = create_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  // 校验订单状态
  order_check.CheckQty(uid, qty);
  order_check.CheckPrice(uid, price);
  order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
  order_check.CheckExecInst(uid, EExecInst::None);
  order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);

  // 模拟下跌触发 (mark_price)10040 < (trigger_price)10050
  underlying_price = bbase::decimal::Decimal<>("10040");
  bool to_check_res = false;
  bool checked = false;
  int try_times = 0;  // 最多尝试1次，最多2.5s
  while (!checked && try_times < 5) {
    usleep(500 * 1000);

    // 更新最新价格
    underlying_price = underlying_price.Sub(1);  // 每次需要变一下价格，不然会被trigger拦截
    auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
    auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
    trigger_info->symbol_ = symbol;
    trigger_info->trigger_by_ = ETriggerBy::LastPrice;
    trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
    trigger_info->trigger_src_seq_ = 1;
    trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
    quote_update_event->SetRawTriggerInfo(trigger_info);
    quote_update_event->set_type(event::kEventSyncFuturesTrigger);
    std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);

    // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
    auto time_event = std::make_shared<event::TimerEvent>(0);
    time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
    time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
    ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);

    // 取trading result
    auto active_so_result = te->PopResult();
    if (active_so_result.m_msg == nullptr) {
      // 没拿到active_so的result, 继续发标记价格，尝试触发
      try_times++;
      continue;
    }

    // 拿到active_so的result, 说明触发成功，不需要循环发标记价和timer了，走完下面的检查流程即可
    checked = true;

    // 获取订单数据
    order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckQty(uid, qty);
    order_check.CheckPrice(uid, price);
    order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);

    // 取trading result
    active_so_result = te->PopResult();
    ASSERT_NE(active_so_result.m_msg, nullptr);

    // 获取订单数据
    order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckQty(uid, qty);
    order_check.CheckPrice(uid, price);
    order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    to_check_res = true;
  }
  ASSERT_EQ(to_check_res, true);
}

TEST_F(InverseReplaceSoTest, trigger_to_active_by_quote_rising) {
  biz::user_id_t uid = 653106;
  biz::coin_t const coin = ECoin::BTC;
  biz::cross_idx_t const cross_idx = 5;
  biz::symbol_t const symbol = ESymbol::BTCUSD;
  std::string order_id = te->GenUUID();
  EPositionIndex const position_idx = EPositionIndex::Single;
  ESide const side = ESide::Buy;
  EOrderType const order_type = EOrderType::Limit;
  biz::size_x_t qty = 1 * 1e4;
  biz::price_x_t const price = 29500 * 1e4;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;
  EStopOrderType const stop_order_type = EStopOrderType::Stop;
  ETriggerBy const trigger_by = ETriggerBy::LastPrice;
  biz::price_x_t const trigger_price = 29508 * 1e4;
  EPriceDirection const expected_direction = EPriceDirection::Rising;
  biz::price_x_t base_price = 29496 * 1e4;
  bool const close_on_trigger = false;
  biz::price_x_t const tp_x = 0;
  biz::price_x_t const sl_x = 0;
  ESmpType const smp_type = ESmpType::None;
  int32_t const smp_group = 0;

  // 校准标记价格 (mark_price)10150 > (trigger_price)10050
  auto underlying_price = bbase::decimal::Decimal<>("29498.8");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 校准最新价格 (last_price)10150 > (trigger_price)10050
  auto lv0_px = "29496";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty);

  // 构建客户端
  stub user1(te, uid);

  // 充值
  auto req_id = te->GenUUID();
  auto trans_id = te->GenUUID();
  auto bonus_change = "0";  // 赠金
  EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                     std::to_string(500000), bonus_change);
  // 发rpc请求
  auto deposit_resp = user1.process(deposit_build.Build());
  // 校验rpc回报
  ASSERT_EQ(deposit_resp->ret_code(), error::kErrorCodeSuccess);
  // 取trading result
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg, nullptr);

  // ============================================================================================================
  // 报单
  // ============================================================================================================
  FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
      uid, coin, symbol, order_id, position_idx, side, order_type, qty, price, time_in_force, stop_order_type,
      trigger_by, trigger_price, expected_direction, base_price, close_on_trigger, tp_x, sl_x, smp_type, smp_group);

  // 发rpc请求
  auto create_so_resp = user1.process(create_so_build.Build());
  // 校验rpc回报
  ASSERT_EQ(create_so_resp->ret_code(), error::kErrorCodeSuccess);

  // 取trading result
  auto create_so_result = te->PopResult();
  ASSERT_NE(create_so_result.m_msg, nullptr);

  // 获取订单数据
  auto order_check = create_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  // 校验订单状态
  order_check.CheckQty(uid, qty);
  order_check.CheckPrice(uid, price);
  order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
  order_check.CheckExecInst(uid, EExecInst::None);
  order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);

  // 更新实时价格
  // 模拟上涨触发 (last_price)29510 > (trigger_price)29496
  underlying_price = bbase::decimal::Decimal<>("29510");
  bool to_check_res = false;
  bool checked = false;
  int try_times = 0;  // 最多尝试1次，最多2.5s
  while (!checked && try_times < 5) {
    usleep(500 * 1000);

    // 更新最新价格
    underlying_price = underlying_price.Sub(1);  // 每次需要变一下价格，不然会被trigger拦截
    auto quote_update_event = std::make_shared<event::TriggerByInfoUpdateEvent>(symbol);
    auto trigger_info = std::make_shared<trigger_worker::futures::RawTriggerInfo>();
    trigger_info->symbol_ = symbol;
    trigger_info->trigger_by_ = ETriggerBy::LastPrice;
    trigger_info->trigger_src_time_ = bbase::utils::Time::GetTimeNs();
    trigger_info->trigger_src_seq_ = 1;
    trigger_info->trigger_src_price_ = underlying_price.Shift(4).IntPart();
    quote_update_event->SetRawTriggerInfo(trigger_info);
    quote_update_event->set_type(event::kEventSyncFuturesTrigger);
    std::int32_t ret = te->pipeline()->ring_buffers(worker::kFuturesTriggerWorker)->Write(quote_update_event, true);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);

    // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
    auto time_event = std::make_shared<event::TimerEvent>(0);
    time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
    time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
    ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);

    // 取trading result
    auto active_so_result = te->PopResult();
    if (active_so_result.m_msg == nullptr) {
      // 没拿到active_so的result, 继续发标记价格，尝试触发
      try_times++;
      continue;
    }

    // 拿到active_so的result, 说明触发成功，不需要循环发标记价和timer了，走完下面的检查流程即可
    checked = true;

    // 获取订单数据
    order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckQty(uid, qty);
    order_check.CheckPrice(uid, price);
    order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);

    // 取trading result
    active_so_result = te->PopResult();
    ASSERT_NE(active_so_result.m_msg, nullptr);

    // 获取订单数据
    order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckQty(uid, qty);
    order_check.CheckPrice(uid, price);
    order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    to_check_res = true;
  }
  ASSERT_EQ(to_check_res, true);
}

TEST_F(InverseReplaceSoTest, trigger_to_active_by_quote_after_restart_trading_service) {
  biz::user_id_t uid = 653106;
  biz::coin_t const coin = ECoin::BTC;
  biz::cross_idx_t const cross_idx = 5;
  biz::symbol_t const symbol = ESymbol::BTCUSD;
  std::string order_id = te->GenUUID();
  EPositionIndex const position_idx = EPositionIndex::Single;
  ESide const side = ESide::Sell;
  EOrderType const order_type = EOrderType::Limit;
  biz::size_x_t qty = 1 * 1e4;
  biz::price_x_t const price = 10100 * 1e4;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;
  EStopOrderType const stop_order_type = EStopOrderType::Stop;
  ETriggerBy const trigger_by = ETriggerBy::LastPrice;
  biz::price_x_t const trigger_price = 10050 * 1e4;
  EPriceDirection const expected_direction = EPriceDirection::Falling;
  biz::price_x_t base_price = 0;
  bool const close_on_trigger = false;
  biz::price_x_t const tp_x = 0;
  biz::price_x_t const sl_x = 0;
  ESmpType const smp_type = ESmpType::None;
  int32_t const smp_group = 0;

  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 校准最新价格 (last_price)11000 > (trigger_price)10050,不让订单触发
  auto lv0_px = "11000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty);

  // 构建客户端
  stub user1(te, uid);

  // 充值
  auto req_id = te->GenUUID();
  auto trans_id = te->GenUUID();
  auto bonus_change = "0";  // 赠金
  EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                     std::to_string(500000), bonus_change);
  // 发rpc请求
  auto deposit_resp = user1.process(deposit_build.Build());
  // 校验rpc回报
  ASSERT_EQ(deposit_resp->ret_code(), error::kErrorCodeSuccess);
  // 取trading result
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg, nullptr);

  // ============================================================================================================
  // 报单
  // ============================================================================================================
  FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
      uid, coin, symbol, order_id, position_idx, side, order_type, qty, price, time_in_force, stop_order_type,
      trigger_by, trigger_price, expected_direction, base_price, close_on_trigger, tp_x, sl_x, smp_type, smp_group);

  // 发rpc请求
  auto create_so_resp = user1.process(create_so_build.Build());
  // 校验rpc回报
  ASSERT_EQ(create_so_resp->ret_code(), error::kErrorCodeSuccess);

  // 取trading result
  auto create_so_result = te->PopResult();
  ASSERT_NE(create_so_result.m_msg, nullptr);

  // 获取订单数据
  auto order_check = create_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  // 校验订单状态
  order_check.CheckQty(uid, qty);
  order_check.CheckPrice(uid, price);
  order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
  order_check.CheckExecInst(uid, EExecInst::None);
  order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);

  // 模拟重启场景,设置市价为10000,订单能被触发
  auto quote_data_synchronizer = te->quote_data_synchronizer();
  mock::HdtsMessage produce_msg;
  dtssdk::Headers* ptr_hdr = dtssdk::Headers::Create();
  ptr_hdr->Add("type", "1");
  ptr_hdr->Add("snap", "1");
  produce_msg.headers_.assign(reinterpret_cast<char*>(ptr_hdr->Cptr()), ptr_hdr->Size());

  ::models::quote::Orderbooks orderbooks;
  orderbooks.set_biztype(::models::quote::Orderbooks::FUTURE);
  orderbooks.set_geartype(::models::quote::Orderbooks::GEAR1);
  orderbooks.set_crossidx(5);
  auto& depth_map = *orderbooks.mutable_depths();
  ::models::quote::OrderbookInfo info;
  info.set_symbolid(5);
  info.set_crossseq(791);
  info.set_transacttimeus(1680074868193508);
  auto& price_map = *info.mutable_price();
  ::models::quote::PriceStat pricestat;
  auto openPrice = new ::models::quote::PriceAttribute;
  openPrice->set_price("10000");
  openPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_openprice(openPrice);

  auto highPrice = new ::models::quote::PriceAttribute;
  highPrice->set_price("10000");
  highPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_highprice(highPrice);

  auto lowPrice = new ::models::quote::PriceAttribute;
  lowPrice->set_price("10000");
  lowPrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_lowprice(lowPrice);

  auto closePrice = new ::models::quote::PriceAttribute;
  closePrice->set_price("10000");
  closePrice->set_transacttimeus(1680074868193508);
  pricestat.set_allocated_closeprice(closePrice);

  price_map[1] = pricestat;
  depth_map[ESymbol::BTCUSD] = info;

  produce_msg.topic_ = "quote2trading.ob.5";
  produce_msg.pay_load_ = orderbooks.SerializeAsString();

  auto receiver = quote_data_synchronizer->GetReceiver(5);
  EXPECT_NE(receiver, nullptr);
  auto handle = receiver->msg_handler();
  EXPECT_NE(handle, nullptr);

  handle->OnConsumeCallback(produce_msg);
  dtssdk::Headers::DestroyHeaders(ptr_hdr);

  bool to_check_res = false;
  bool checked = false;
  int try_times = 0;  // 最多尝试1次，最多2.5s
  while (!checked && try_times < 5) {
    usleep(500 * 1000);

    // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
    auto time_event = std::make_shared<event::TimerEvent>(0);
    time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
    time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
    std::int32_t ret = te->pipeline_->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);

    // 取trading result
    auto active_so_result = te->PopResult();
    if (active_so_result.m_msg == nullptr) {
      // 没拿到active_so的result, 继续发标记价格，尝试触发
      try_times++;
      continue;
    }

    // 拿到active_so的result, 说明触发成功，不需要循环发标记价和timer了，走完下面的检查流程即可
    checked = true;

    // 获取订单数据
    order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckQty(uid, qty);
    order_check.CheckPrice(uid, price);
    order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);

    // 取trading result
    active_so_result = te->PopResult();
    ASSERT_NE(active_so_result.m_msg, nullptr);

    // 获取订单数据
    order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckQty(uid, qty);
    order_check.CheckPrice(uid, price);
    order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    to_check_res = true;
  }
  ASSERT_EQ(to_check_res, true);
}

TEST_F(InverseReplaceSoTest, trigger_immediate_fail) {
  biz::user_id_t const uid1 = 100000;
  stub user1(te, uid1);
  {
    // user1充值
    FutureDepositBuilder deposit_build("deposit-req-id", uid1, "deposit-trans-id", ECoin::BTC,
                                       EWalletRecordType::Deposit, "100000000", "0");
    auto resp = user1.process(deposit_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
  }

  te->AddMarkPrice(ESymbol::BTCUSD, 20000, 20000, 20000);

  {
    // siteAPI不受影响
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
        uid1, ECoin::BTC, ESymbol::BTCUSD, te->GenUUID(), EPositionIndex::Single, ESide::Buy, EOrderType::Market,
        1 * 1e4, 0, ETimeInForce::ImmediateOrCancel, EStopOrderType::Stop, ETriggerBy::LastPrice, 18000 * 1e4,
        EPriceDirection::Rising, 18500 * 1e4, false, 0, 0, ESmpType::None, 0);
    auto resp = user1.process(create_so_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::RisingWouldTriggerImmediately);
  }

  {
    // OpenApiV5: 30074->110092，30075->110093
    OpenApiV5CreateOrderBuilder create_build("inverse", "BTCUSD", 0, "Sell", "Limit", "10000", "18500");
    create_build.SetOrderLinkID(te->GenUUID());
    create_build.SetTrigger("MarkPrice", 1, "18000");
    RpcContext ct{};
    auto resp = user1.create_order(create_build.Build(), ct);
    ASSERT_NE(resp.get(), nullptr);
    EXPECT_EQ(ct.RetCode(), 110092);

    OpenApiV5CreateOrderBuilder create_build2("inverse", "BTCUSD", 0, "Sell", "Limit", "10000", "18500");
    create_build2.SetOrderLinkID(te->GenUUID());
    create_build2.SetTrigger("MarkPrice", 2, "20050");
    RpcContext ct2{};
    auto resp2 = user1.create_order(create_build2.Build(), ct2);
    ASSERT_NE(resp2.get(), nullptr);
    EXPECT_EQ(ct2.RetCode(), 110093);
  }
}

std::shared_ptr<tmock::CTradeAppMock> InverseReplaceSoTest::te;
