#ifndef TEST_BIZ_TRADE_FUTURE_FEATURE_FUTURE_SPREAD_HALFOB_MORE_COST_MAKE_ORDER_TEST_HPP_
#define TEST_BIZ_TRADE_FUTURE_FEATURE_FUTURE_SPREAD_HALFOB_MORE_COST_MAKE_ORDER_TEST_HPP_

#include <gtest/gtest.h>

#include <atomic>
#include <boost/uuid/random_generator.hpp>
#include <boost/uuid/uuid_io.hpp>

#include "src/biz_worker/service/trade/store/order/raw_order.hpp"

store::Order::Ptr CreateActiveOrderMoreCost(ESide side, EOrderType order_type, ETimeInForce time_in_force,
                                            int32_t exec_inst, biz::price_x_t price, biz::size_x_t qty,
                                            ECreateType create_type = ECreateType::CreateByUser);

store::Order::Ptr MakeLimitBuyMoreCost(biz::price_x_t price, biz::size_x_t qty,
                                       ECreateType create_type = ECreateType::CreateByUser);

store::Order::Ptr MakeLimitSellMoreCost(biz::price_x_t price, biz::size_x_t qty,
                                        ECreateType create_type = ECreateType::CreateByUser);

store::Order::Ptr MakeMarketBuyMoreCost(biz::price_x_t price, biz::size_x_t qty,
                                        ECreateType create_type = ECreateType::CreateByUser);

store::Order::Ptr MakeMarketSellMoreCost(biz::price_x_t price, biz::size_x_t qty,
                                         ECreateType create_type = ECreateType::CreateByUser);

#endif  // TEST_BIZ_TRADE_FUTURE_FEATURE_FUTURE_SPREAD_HALFOB_MORE_COST_MAKE_ORDER_TEST_HPP_
