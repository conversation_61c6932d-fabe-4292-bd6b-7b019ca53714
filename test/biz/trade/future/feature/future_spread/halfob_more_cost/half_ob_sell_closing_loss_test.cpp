#include <gtest/gtest.h>

#include <memory>

#include "main.hpp"  // NOLINT
#include "src/biz_worker/service/trade/store/order/cow_order.hpp"
#include "src/biz_worker/service/trade/store/order_manager/half_side_order_book.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/future/feature/future_spread/halfob_more_cost/check_half_ob_test.hpp"
#include "test/biz/trade/future/feature/future_spread/halfob_more_cost/make_order_test.hpp"
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class HalfObMoreCostSellClosingLossTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static std::shared_ptr<tmock::CTradeAppMock> te;
};

std::shared_ptr<tmock::CTradeAppMock> HalfObMoreCostSellClosingLossTest::te;

TEST_F(HalfObMoreCostSellClosingLossTest, TestHalfObMoreCostSellClosingLoss) {
  auto sell_ob = std::make_shared<store::HalfSideOrderBookMoreCost>(ESymbol::BTCUSDT, ESide::Sell);

  sell_ob->UpdateCachedPzSideSize(ESide::Buy, 500 * 1e4);
  sell_ob->UpdateCachedMarkPrice(10000 * 1e4);

  // 添加第1笔sell
  auto o1 = MakeLimitSellMoreCost(9950 * 1e4, 110 * 1e4);
  o1->exec_inst = EExecInst::Close;
  auto cow_ord1 = store::CowOrder::CreateCowOrder(o1);
  sell_ob->InsertOrder(cow_ord1.get());
  EXPECT_EQ(cow_ord1->item.close_on_trigger, true);
  EXPECT_EQ(cow_ord1->item.reduce_only, true);
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4);

  // 添加第2笔sell
  auto o2 = MakeLimitSellMoreCost(9960 * 1e4, 220 * 1e4);
  o2->exec_inst = EExecInst::bit_reduce_only;
  auto cow_ord2 = store::CowOrder::CreateCowOrder(o2);

  sell_ob->InsertOrder(cow_ord2.get());

  EXPECT_EQ(cow_ord2->item.reduce_only, true);
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4 + 9960 * 220 * 1e4);

  // 添加第3笔sell
  auto o3 = MakeLimitSellMoreCost(9970 * 1e4, 170 * 1e4);
  auto cow_ord3 = store::CowOrder::CreateCowOrder(o3);
  sell_ob->InsertOrder(cow_ord3.get());
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 170 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4 + 9960 * 220 * 1e4 + 9970 * 170 * 1e4);

  // 移除第2笔sell
  sell_ob->EraseOrder(cow_ord2.get());
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4 + 170 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4 + 9970 * 170 * 1e4);

  // 移除第3笔sell
  sell_ob->EraseOrder(cow_ord3.get());
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4);

  // 再把第3笔sell添加回去
  sell_ob->InsertOrder(cow_ord3.get());
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4 + 170 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4 + 9970 * 170 * 1e4);

  // 再把第2笔sell添加回去
  sell_ob->InsertOrder(cow_ord2.get());
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 170 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4 + 9960 * 220 * 1e4 + 9970 * 170 * 1e4);

  sell_ob->UpdateCachedPzSideSize(ESide::Buy, 400 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 70 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4 + 9960 * 220 * 1e4 + 9970 * 70 * 1e4);

  // 模拟标记价格浮动
  sell_ob->UpdateCachedMarkPrice(11000 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 70 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4 + 9960 * 220 * 1e4 + 9970 * 70 * 1e4);

  sell_ob->UpdateCachedMarkPrice(9900 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 0);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 0);

  sell_ob->UpdateCachedMarkPrice(9955 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4);

  sell_ob->UpdateCachedMarkPrice(9965 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4 + 9960 * 220 * 1e4);

  sell_ob->UpdateCachedMarkPrice(10000 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 70 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4 + 9960 * 220 * 1e4 + 9970 * 70 * 1e4);

  sell_ob->UpdateCachedPzSideSize(ESide::Buy, 600 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 170 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4 + 9960 * 220 * 1e4 + 9970 * 170 * 1e4);

  // 添加一笔没有定价的市价单o4
  auto o4 = MakeMarketSellMoreCost(0, 100 * 1e4);
  auto cow_ord4 = store::CowOrder::CreateCowOrder(o4);
  sell_ob->InsertOrder(cow_ord4.get());
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 170 * 1e4 + 100 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_market_qty_x(), 100 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4 + 9960 * 220 * 1e4 + 9970 * 170 * 1e4);

  // 移出o4
  sell_ob->EraseOrder(cow_ord4.get());
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 170 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_market_qty_x(), 0);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4 + 9960 * 220 * 1e4 + 9970 * 170 * 1e4);

  // 添加一笔定好价的市价单o5
  auto o5 = MakeMarketSellMoreCost(9980 * 1e4, 100 * 1e4);
  auto cow_ord5 = store::CowOrder::CreateCowOrder(o5);
  sell_ob->InsertOrder(cow_ord5.get());
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 170 * 1e4 + 100 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_market_qty_x(), 0);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(),
            9950 * 110 * 1e4 + 9960 * 220 * 1e4 + 9970 * 170 * 1e4 + 9980 * 100 * 1e4);

  // 移出o4
  sell_ob->EraseOrder(cow_ord5.get());
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 110 * 1e4 + 220 * 1e4 + 170 * 1e4);
  EXPECT_EQ(sell_ob->loss_closing_market_qty_x(), 0);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 9950 * 110 * 1e4 + 9960 * 220 * 1e4 + 9970 * 170 * 1e4);
}
