#include <gtest/gtest.h>

#include <iostream>
#include <memory>
#include <vector>

#include "main.hpp"  // NOLINT
#include "src/biz_worker/service/trade/store/order/cow_order.hpp"
#include "src/biz_worker/service/trade/store/order_manager/half_side_order_book.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/future/feature/future_spread/halfob_more_cost/check_half_ob_test.hpp"
#include "test/biz/trade/future/feature/future_spread/halfob_more_cost/make_order_test.hpp"
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class HalfObMoreCostBuyTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static std::shared_ptr<tmock::CTradeAppMock> te;
};

TEST_F(HalfObMoreCostBuyTest, go_TestHalfObBuy) {
  auto buy_ob = std::make_shared<store::HalfSideOrderBookMoreCost>(ESymbol::BTCUSDT, ESide::Buy);
  buy_ob->UpdateCachedPzSideSize(ESide::None, 0);
  buy_ob->UpdateCachedMarkPrice(10000 * 1e4);

  // 添加第1笔buy
  auto o1 = MakeLimitBuyMoreCost(10010 * 1e4, 100);
  auto cow_ord1 = store::CowOrder::CreateCowOrder(o1);

  buy_ob->InsertOrder(cow_ord1.get());

  /*
  int32_t btree_len, biz::size_x_t totol_cost_qty,
                         biz::value_e8_t totol_cost_value, biz::size_x_t cached_closing_size, biz::price_x_t edge_price,
                         biz::seq_t edge_added_op_seq, biz::size_x_t closing_box_sum_qty,
                         biz::size_x_t closing_box_sum_value, biz::size_x_t open_extra_qty,
                         biz::value_e8_t open_extra_value, biz::price_x_t mark_price, biz::size_x_t loss_extra_qty,
                         biz::value_e8_t loss_extra_value
  */

  CheckHalfOBMoreCost(buy_ob, 1, o1->leaves_qty, o1->leaves_value, 0, 0, 0, 0, 0, o1->leaves_qty, o1->leaves_value,
                      10000 * 1e4, o1->leaves_qty, o1->leaves_value);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 0);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 0);

  // 添加第2笔buy
  auto o2 = MakeLimitBuyMoreCost(9999 * 1e4, 100);
  auto cow_ord2 = store::CowOrder::CreateCowOrder(o2);

  buy_ob->InsertOrder(cow_ord2.get());
  CheckHalfOBMoreCost(buy_ob, 2, o1->leaves_qty + o2->leaves_qty, o1->leaves_value + o2->leaves_value, 0, 0, 0, 0, 0,
                      o1->leaves_qty + o2->leaves_qty, o1->leaves_value + o2->leaves_value, 10000 * 1e4, o1->leaves_qty,
                      o1->leaves_value);  // o2 不算浮亏
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 0);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 0);

  std::vector<biz::price_x_t> list;
  buy_ob->AscendRange([&list](store::CowOrder* o) -> bool {
    list.push_back(o->GetItemMoreCost().key.price);
    return true;
  });
  EXPECT_EQ(list.size(), 2);
  EXPECT_EQ(list[0], 99990000);
  EXPECT_EQ(list[1], 100100000);
  list.clear();

  store::ObKey ob_key1, ob_key2;
  ob_key1.side = ESide::Buy;
  ob_key1.price = 9000 * 1e4;
  ob_key1.added_op_seq = cow_ord1->GetItemMoreCost().key.added_op_seq;
  ob_key2.side = ESide::Buy;
  ob_key2.price = 11000 * 1e4;
  ob_key2.added_op_seq = cow_ord1->GetItemMoreCost().key.added_op_seq;
  buy_ob->DescendRange(ob_key2, ob_key1, [&list](store::CowOrder* o) -> bool {
    list.push_back(o->GetItemMoreCost().key.price);
    return true;
  });
  // 9000 9999 10010 11000
  EXPECT_EQ(list.size(), 2);
  EXPECT_EQ(list[0], 100100000);
  EXPECT_EQ(list[1], 99990000);
  list.clear();
  buy_ob->DescendRange(ob_key2, [&list](store::CowOrder* o) -> bool {
    list.push_back(o->GetItemMoreCost().key.price);
    return true;
  });
  EXPECT_EQ(list.size(), 2);
  EXPECT_EQ(list[0], 100100000);
  EXPECT_EQ(list[1], 99990000);
  // 移除第2笔buy
  buy_ob->EraseOrder(cow_ord2.get());
  CheckHalfOBMoreCost(buy_ob, 1, o1->leaves_qty, o1->leaves_value, 0, 0, 0, 0, 0, o1->leaves_qty, o1->leaves_value,
                      10000 * 1e4, o1->leaves_qty, o1->leaves_value);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 0);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 0);

  // 移除第1笔buy
  buy_ob->EraseOrder(cow_ord1.get());
  CheckHalfOBMoreCost(buy_ob, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10000 * 1e4, 0, 0);
  EXPECT_EQ(buy_ob->loss_closing_qty_x(), 0);
  EXPECT_EQ(buy_ob->loss_closing_value_e8(), 0);
}

std::shared_ptr<tmock::CTradeAppMock> HalfObMoreCostBuyTest::te;
