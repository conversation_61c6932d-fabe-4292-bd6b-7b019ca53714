#include <gtest/gtest.h>

#include <memory>

#include "main.hpp"  // NOLINT
#include "src/biz_worker/service/trade/store/order/cow_order.hpp"
#include "src/biz_worker/service/trade/store/order_manager/half_side_order_book.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/future/feature/future_spread/halfob_more_cost/check_half_ob_test.hpp"
#include "test/biz/trade/future/feature/future_spread/halfob_more_cost/make_order_test.hpp"
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class HalfObMoreCostSellTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static std::shared_ptr<tmock::CTradeAppMock> te;
};

std::shared_ptr<tmock::CTradeAppMock> HalfObMoreCostSellTest::te;

TEST_F(HalfObMoreCostSellTest, go_TestHalfObSell) {
  auto sell_ob = std::make_shared<store::HalfSideOrderBookMoreCost>(ESymbol::BTCUSDT, ESide::Sell);

  sell_ob->UpdateCachedPzSideSize(ESide::None, 0);
  sell_ob->UpdateCachedMarkPrice(10000 * 1e4);

  // 添加第1笔sell
  auto o1 = MakeLimitSellMoreCost(9993 * 1e4, 100);
  auto cow_ord1 = store::CowOrder::CreateCowOrder(o1);

  sell_ob->InsertOrder(cow_ord1.get());

  CheckHalfOBMoreCost(sell_ob, 1, o1->leaves_qty, o1->leaves_value, 0, 0, 0, 0, 0, o1->leaves_qty, o1->leaves_value,
                      10000 * 1e4, o1->leaves_qty, o1->leaves_value);
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 0);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 0);

  // 添加第2笔sell
  auto o2 = MakeLimitSellMoreCost(10005 * 1e4, 100);
  auto cow_ord2 = store::CowOrder::CreateCowOrder(o2);

  sell_ob->InsertOrder(cow_ord2.get());
  CheckHalfOBMoreCost(sell_ob, 2, o1->leaves_qty + o2->leaves_qty, o1->leaves_value + o2->leaves_value, 0, 0, 0, 0, 0,
                      o1->leaves_qty + o2->leaves_qty, o1->leaves_value + o2->leaves_value, 10000 * 1e4, o1->leaves_qty,
                      o1->leaves_value);  // o2 不算浮亏
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 0);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 0);

  // 移除第2笔sell
  sell_ob->EraseOrder(cow_ord2.get());
  CheckHalfOBMoreCost(sell_ob, 1, o1->leaves_qty, o1->leaves_value, 0, 0, 0, 0, 0, o1->leaves_qty, o1->leaves_value,
                      10000 * 1e4, o1->leaves_qty, o1->leaves_value);
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 0);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 0);

  // 移除第1笔sell
  sell_ob->EraseOrder(cow_ord1.get());
  CheckHalfOBMoreCost(sell_ob, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10000 * 1e4, 0, 0);
  EXPECT_EQ(sell_ob->loss_closing_qty_x(), 0);
  EXPECT_EQ(sell_ob->loss_closing_value_e8(), 0);
}
