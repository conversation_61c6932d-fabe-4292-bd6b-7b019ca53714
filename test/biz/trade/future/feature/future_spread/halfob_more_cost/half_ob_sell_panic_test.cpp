#include <gtest/gtest.h>

#include <memory>

#include "main.hpp"  // NOLINT
#include "src/biz_worker/service/trade/store/order/cow_order.hpp"
#include "src/biz_worker/service/trade/store/order_manager/half_side_order_book.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/future/feature/future_spread/halfob_more_cost/check_half_ob_test.hpp"
#include "test/biz/trade/future/feature/future_spread/halfob_more_cost/make_order_test.hpp"
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class HalfObMoreCostSellPanicTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static std::shared_ptr<tmock::CTradeAppMock> te;
};

std::shared_ptr<tmock::CTradeAppMock> HalfObMoreCostSellPanicTest::te;

TEST_F(HalfObMoreCostSellPanicTest, go_TestHalfObSellPanic) {
  auto sell_ob = std::make_shared<store::HalfSideOrderBookMoreCost>(ESymbol::BTCUSDT, ESide::Sell);

  sell_ob->UpdateCachedPzSideSize(ESide::Buy, 1 * 1e8);
  sell_ob->UpdateCachedMarkPrice(10000 * 1e4);

  auto o4 = MakeLimitSellMoreCost(11488 * 1e4, 48 * 1e8);
  o4->op_seq = 438820;
  o4->added_op_seq = 437274;
  o4->user_id = 161122;
  o4->order_id = "8d60b509-5862-4fa3-92f0-913088bd824d";
  o4->order_status = EOrderStatus::New;
  o4->cross_status = ECrossStatus::Replaced;
  o4->cross_seq = 952140;
  o4->added_cross_seq = 950459;

  auto [cow_ord4, version4] = store::CowOrder::RecoverCowOrderFromDump(o4, 0);

  auto o3 = MakeLimitSellMoreCost(11487.5 * 1e4, 154 * 1e8);
  o3->op_seq = 442855;
  o3->added_op_seq = 437264;
  o3->user_id = 161122;
  o3->order_id = ("a8aa14e7-1a40-4df0-afe8-e0451fa928fc");
  o3->order_status = EOrderStatus::New;
  o3->cross_status = ECrossStatus::Replaced;
  o3->cross_seq = 956432;
  o3->added_cross_seq = 950449;

  auto [cow_ord3, version3] = store::CowOrder::RecoverCowOrderFromDump(o3, 0);

  auto o2 = MakeLimitSellMoreCost(11486.5 * 1e4, 267 * 1e8);
  o2->op_seq = 450690;
  o2->added_op_seq = 437303;
  o2->user_id = 161122;
  o2->order_id = "e84ed80e-e870-4561-b69f-87ed221f3a2b";
  o2->order_status = EOrderStatus::New;
  o2->cross_status = ECrossStatus::Replaced;
  o2->cross_seq = 964481;
  o2->added_cross_seq = 950503;

  auto [cow_ord2, version2] = store::CowOrder::RecoverCowOrderFromDump(o2, 0);

  auto o1 = MakeLimitSellMoreCost(11486 * 1e4, 1 * 1e8);
  o1->op_seq = (451019);
  o1->added_op_seq = 437302;
  o1->user_id = 161122;
  o1->order_id = "cb4a1595-8c18-4b6a-bf25-f91d3b122ac4";
  o1->order_status = EOrderStatus::New;
  o1->cross_status = ECrossStatus::Replaced;
  o1->cross_seq = 964800;
  o1->added_cross_seq = 950502;

  auto [cow_ord1, version1] = store::CowOrder::RecoverCowOrderFromDump(o1, 0);

  sell_ob->InsertOrder(cow_ord3.get());
  sell_ob->InsertOrder(cow_ord4.get());
  sell_ob->InsertOrder(cow_ord1.get());
  sell_ob->InsertOrder(cow_ord2.get());

  CheckHalfOBMoreCost(sell_ob, 4, o1->leaves_qty + o2->leaves_qty + o3->leaves_qty + o4->leaves_qty,
                      o1->leaves_value + o2->leaves_value + o3->leaves_value + o4->leaves_value, 100000000, 0, 0,
                      100000000, 1148600000000, 46900000000, 538739450000000, 10000 * 1e4, 0, 0);
}
