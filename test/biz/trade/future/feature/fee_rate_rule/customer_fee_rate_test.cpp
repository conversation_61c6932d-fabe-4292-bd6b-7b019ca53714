#include "customer_fee_rate_test.hpp"  // NOLINT

#include "data/enum.hpp"
#include "data/type/biz_type.hpp"
#include "lib/msg_builder.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"
std::shared_ptr<tmock::CTradeAppMock> TestCustomerFeeRate::te;
void TestCustomerFeeRate::InitFeeRate(const char* FeeRateConfig) {
  auto config_center = bbase::object_manager::ObjectManager::GetObject<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  auto nacos_client = std::dynamic_pointer_cast<bbase::nacos_client::NacosClient>(config_center);
  nacos_client->SetContent(config::ConstConfig::FEERATE_DATA_ID, config::ConstConfig::FEERATE_GROUP, FeeRateConfig);

  auto ret = application::GlobalVarManager::Instance().Init();
  ASSERT_EQ(ret, 0);
}
/**
 * taker fee rate range: [20000, 60000]
 * maker fee rate range: [-15000, 20000]
 */

void create_order_and_pz(stub& user1, stub& user2, ::enums::eaccountmode::AccountMode account_mode,
                         std::shared_ptr<tmock::CTradeAppMock> te) {
  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;
  {
    // 充值、调杠杆、切双仓
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;

    // user1
    FutureDepositBuilder u1_deposit_build(req_id, user1.m_uid, trans_id, coin, wallet_record_type, "1500", "0");
    auto u1_resp = user1.process(u1_deposit_build.Build());
    auto u1_result = te->PopResult();

    FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, user1.m_uid, 100 * 1e2, 100 * 1e2);
    auto resp1 = user1.process(set_lv_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(100 * 1e2, 1119400, 1120600, 619400, 620600);

    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user1.m_uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(account_mode);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user1.m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    // user2
    FutureDepositBuilder u2_deposit_build(req_id, user2.m_uid, trans_id, coin, wallet_record_type, "1500", "0");
    auto u2_resp = user2.process(u2_deposit_build.Build());
    auto u2_result = te->PopResult();

    FutureOneOfSwitchPositionModeBuilder switch_position_mode(symbol, coin, EPositionMode::BothSide, user2.m_uid);
    auto resp = user2.process(switch_position_mode.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    FutureOneOfSetLeverageBuilder set_lv_build2(symbol, coin, user2.m_uid, 100 * 1e2, 100 * 1e2);
    auto resp2 = user2.process(set_lv_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto positionCheck2 = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck2.m_msg, nullptr);
    positionCheck2.CheckLv(100 * 1e2, 1119400, 1120600, 619400, 620600);

    // 切到全仓
    unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user2.m_uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(account_mode);
    event = std::make_shared<event::MarginHdtsRequestEvent>(user2.m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);

  {
    // user1  单仓买
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
  }

  {
    // user2  双仓卖
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Sell;
    ESide side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user2.m_uid, coin);
    auto pz_buy_order_resp = user2.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);
    auto user2_order_check = user2_result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(user2_order_check.m_msg, nullptr);
    user2_order_check.CheckQty(user2.m_uid, qty);
    ASSERT_EQ(user2_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user2_order_check.m_msg->side(), ESide::Sell);

    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(2);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user2_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 2);

    auto user2_trade_check = user2_result.RefFutureMarginResult().RefRelatedFillByIndex(0);
    ASSERT_EQ(user2_trade_check.m_msg->fee_rate_e8(), 60000);
    ASSERT_EQ(user2_trade_check.m_msg->exec_qty_x(), qty);
    ASSERT_EQ(user2_trade_check.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_trade_check.m_msg->position_idx(), 2);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::Filled);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);

    auto user1_trade_check = user1_result.RefFutureMarginResult().RefRelatedFillByIndex(0);
    ASSERT_EQ(user1_trade_check.m_msg->fee_rate_e8(), 10000);
    ASSERT_EQ(user1_trade_check.m_msg->exec_qty_x(), qty);
    ASSERT_EQ(user1_trade_check.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_trade_check.m_msg->position_idx(), 0);
  }

  // 造订单
  {
    // user1  单仓买
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 2 * 1e8;
    biz::price_x_t price = 19000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 4 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    user1_positionCheck.CheckLv(100 * 1e2, 1119400, 1120600, 619400, 620600);
    ASSERT_EQ(user1_positionCheck.m_msg->value_e8(), 4 * 20000 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->position_mm_with_fee_e8(), 447.52 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->order_mm_with_fee_e8(), 235.372 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->min_position_cost_e8(), 847.52 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->order_cost_e8(), 425.372 * 1e8);
  }

  {
    // user2  双仓卖
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Sell;
    ESide side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 2 * 1e8;
    biz::price_x_t price = 21000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user2.m_uid, coin);
    auto pz_buy_order_resp = user2.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);
    auto user2_order_check = user2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user2_order_check.m_msg, nullptr);
    user2_order_check.CheckQty(user2.m_uid, qty);
    ASSERT_EQ(user2_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user2_order_check.m_msg->side(), ESide::Sell);

    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(2);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user2_positionCheck.m_msg->size_x(), 4 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 2);
    user2_positionCheck.CheckLv(100 * 1e2, 1119400, 1120600, 619400, 620600);
    ASSERT_EQ(user2_positionCheck.m_msg->value_e8(), 4 * 20000 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->position_mm_with_fee_e8(), 448.48 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->order_mm_with_fee_e8(), 260.652 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->min_position_cost_e8(), 848.48 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->order_cost_e8(), 470.652 * 1e8);
  }
}

TEST_F(TestCustomerFeeRate, SetAndRevokeSingleFeeRate) {
  auto config_center = bbase::object_manager::ObjectManager::GetObject<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("min_maker_fee_rate", "-15000");
  config_center->SetStringVar("max_maker_fee_rate", "20000");
  config_center->SetStringVar("min_taker_fee_rate", "20000");
  config_center->SetStringVar("max_taker_fee_rate", "60000");

  biz::user_id_t uid = 100000;
  stub user(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  biz::coin_t coin = ECoin::USDT;
  ESymbol symbol = ESymbol::BTCUSDT;
  biz::fee_rate_e8_t tkfr_value = 30000;
  std::string tkfr_desc = "new_taker_fee_rate_desc";
  biz::fee_rate_e8_t mkfr_value = 15000;
  std::string mkfr_desc = "new_maker_fee_rate_desc";

  // case1: 没有设置过, 初次设置symbol维度的taker fee rate和maker fee rate.
  FutureSetSingleFeeRateBuilder set_req_build(coin, uid, symbol, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  auto resp = user.process(set_req_build.Build());
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).taker_fee_rate_e8(),
            tkfr_value);
  ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).maker_fee_rate_e8(),
            mkfr_value);
  ASSERT_STREQ(
      ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).taker_fee_rate_desc().c_str(),
      tkfr_desc.c_str());
  ASSERT_STREQ(
      ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).maker_fee_rate_desc().c_str(),
      mkfr_desc.c_str());
  EXPECT_EQ(ret_setting.setting_version(), 2);
  auto last_update_at = ret_setting.updated_at_e9();

  tkfr_value = 35000;
  mkfr_value = 10000;
  // case2: 已经有设置, 设一个不同的taker fee rate和maker fee rate.
  FutureSetSingleFeeRateBuilder set_req_build_2(coin, uid, symbol, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  auto resp2 = user.process(set_req_build_2.Build());
  ASSERT_EQ(error::kErrorCodeSuccess, resp2->ret_code());
  auto result2 = te->PopResult();
  ASSERT_NE(result2.m_msg.get(), nullptr);
  ret_setting = result2.m_msg->asset_margin_result().per_user_setting_data();
  ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).taker_fee_rate_e8(),
            tkfr_value);
  ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).maker_fee_rate_e8(),
            mkfr_value);
  EXPECT_EQ(ret_setting.setting_version(), 3);
  EXPECT_GE(ret_setting.updated_at_e9(), last_update_at);
  last_update_at = ret_setting.updated_at_e9();

  tkfr_value = 40000;
  mkfr_value = 30000;
  // case3: 已经有设置, 设一个超过范围的maker fee rate.
  FutureSetSingleFeeRateBuilder set_req_build_3(coin, uid, symbol, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  auto resp3 = user.process(set_req_build_3.Build());
  ASSERT_EQ(error::kErrorCodeParamsError, resp3->ret_code());
  ASSERT_STREQ("maker fee rate must in range of [-15000,20000]", resp3->ret_msg().c_str());
  auto result3 = te->PopResult();
  ASSERT_EQ(result3.m_msg.get(), nullptr);

  tkfr_value = 0;  // builder里因为optional的原因, hard code了超过0的才能设进去.
  mkfr_value = 15000;
  // case4: 已经有设置, 设一个超过范围的taker fee rate.
  FutureSetSingleFeeRateBuilder set_req_build_4(coin, uid, symbol, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  auto resp4 = user.process(set_req_build_4.Build());
  ASSERT_EQ(error::kErrorCodeParamsError, resp4->ret_code());
  ASSERT_STREQ("taker fee rate must in range of [20000,60000]", resp4->ret_msg().c_str());
  auto result4 = te->PopResult();
  ASSERT_EQ(result4.m_msg.get(), nullptr);

  // case5: 回滚, liquidity_ind设置错误
  auto liquidity_ind = ELastLiquidityInd::LiquidityIndNA;
  FutureRevokeSingleFeeRateBuilder revoke_req_build(coin, uid, symbol, liquidity_ind);
  auto revoke_resp = user.process(revoke_req_build.Build());
  ASSERT_EQ(error::kErrorCodeParamsError, revoke_resp->ret_code());
  ASSERT_STREQ("liquidity ind must in range of [1,3]", revoke_resp->ret_msg().c_str());

  // case5: 回滚, 没有对应coin的setting
  coin = ECoin::USDC;
  liquidity_ind = ELastLiquidityInd::AddedLiquidity;
  FutureRevokeSingleFeeRateBuilder revoke_req_build2(coin, uid, ESymbol(45), liquidity_ind);
  auto revoke_resp2 = user.process(revoke_req_build2.Build());
  ASSERT_EQ(error::kErrorCodeSymbolFeeRateNotExists, revoke_resp2->ret_code());
  ASSERT_STREQ(fmt::format("symbol fee rate not exists").c_str(), revoke_resp2->ret_msg().c_str());

  coin = ECoin::USDT;
  // case6: 回滚, taker
  liquidity_ind = ELastLiquidityInd::RemovedLiquidity;
  FutureRevokeSingleFeeRateBuilder revoke_req_build3(coin, uid, symbol, liquidity_ind);
  auto revoke_resp3 = user.process(revoke_req_build3.Build());
  ASSERT_EQ(error::kErrorCodeSuccess, revoke_resp3->ret_code());
  auto revoke_result3 = te->PopResult();
  ASSERT_NE(revoke_result3.m_msg.get(), nullptr);
  ret_setting = revoke_result3.m_msg->asset_margin_result().per_user_setting_data();
  ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).taker_fee_rate_e8(), 0);
  ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).maker_fee_rate_e8(), 10000);
  EXPECT_EQ(ret_setting.setting_version(), 4);
  EXPECT_GE(ret_setting.updated_at_e9(), last_update_at);

  // case7: 回滚, maker
  liquidity_ind = ELastLiquidityInd::AddedLiquidity;
  FutureRevokeSingleFeeRateBuilder revoke_req_build4(coin, uid, symbol, liquidity_ind);
  auto revoke_resp4 = user.process(revoke_req_build4.Build());
  ASSERT_EQ(error::kErrorCodeSuccess, revoke_resp4->ret_code());
  te->PopResult();

  // case8: 回滚 taker和maker
  liquidity_ind = ELastLiquidityInd::LiquidityRoutedOut;
  FutureRevokeSingleFeeRateBuilder revoke_req_build5(coin, uid, symbol, liquidity_ind);
  auto revoke_resp5 = user.process(revoke_req_build5.Build());
  ASSERT_EQ(error::kErrorCodeSuccess, revoke_resp5->ret_code());
  te->PopResult();

  // case9: 只设置symbol上的make费率
  coin = ECoin::USDC;
  symbol = ESymbol(45);
  tkfr_value = -1;
  mkfr_value = 10000;
  FutureSetSingleFeeRateBuilder set_req_build_9(coin, uid, symbol, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  resp = user.process(set_req_build_9.Build());
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  std::string msg{};
  // std::cout << "case9 resp:" << utils::PbMessageToJsonString(*resp, &msg) << std::endl;
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  msg = {};
  // std::cout << "case9 result:" << utils::PbMessageToJsonString(*result.m_msg, &msg) << std::endl;

  ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).maker_fee_rate_e8(),
            mkfr_value);
  ASSERT_STREQ(
      ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).maker_fee_rate_desc().c_str(),
      mkfr_desc.c_str());
  // case 10: 设置一个未知的symbol费率
  symbol = ESymbol(52);
  FutureSetSingleFeeRateBuilder set_req_build_10(coin, uid, symbol, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  resp = user.process(set_req_build_10.Build());
  ASSERT_EQ(error::kErrorCodeSymbolNotExists, resp->ret_code());
  // case 11: 回滚一个未知的symbol费率
  FutureRevokeSingleFeeRateBuilder revoke_req_build11(coin, uid, symbol, liquidity_ind);
  resp = user.process(revoke_req_build11.Build());
  ASSERT_EQ(error::kErrorCodeSymbolNotExists, resp->ret_code());
}

TEST_F(TestCustomerFeeRate, SetAndRevokeCustomerFeeRate) {
  biz::user_id_t uid = 100000;
  stub user(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  biz::coin_t coin = ECoin::USDT;
  biz::fee_rate_e8_t tkfr_value = 30000;
  std::string tkfr_desc = "new_taker_fee_rate_desc";
  biz::fee_rate_e8_t mkfr_value = 15000;
  std::string mkfr_desc = "new_maker_fee_rate_desc";

  // case1: 设置coin费率成功
  FutureSetCustomerFeeRateBuilder set_req_build(coin, uid, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  auto resp = user.process(set_req_build.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  EXPECT_EQ(ret_setting.setting_version(), 2);
  auto last_update_at = ret_setting.updated_at_e9();

  // case2: 回滚失败, liquidity_ind错误
  auto liquidity_ind = ELastLiquidityInd::LiquidityIndNA;
  FutureRevokeCustomerFeeRateBuilder revoke_req_build_2(coin, uid, liquidity_ind);
  resp = user.process(revoke_req_build_2.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(error::kErrorCodeParamsError, resp->ret_code());
  ASSERT_STREQ("liquidity ind must in range of [1,7]", resp->ret_msg().c_str());

  // case3: 回滚失败, 没有对应coin的setting
  coin = ECoin::USDC;
  liquidity_ind = ELastLiquidityInd::AddedLiquidity;
  FutureRevokeCustomerFeeRateBuilder revoke_req_build_3(coin, uid, liquidity_ind);
  resp = user.process(revoke_req_build_3.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(error::kErrorCodeParamsError, resp->ret_code());
  ASSERT_STREQ(fmt::format("no setting on future coin[{}]", coin).c_str(), resp->ret_msg().c_str());

  // case4: 回滚成功, maker
  coin = ECoin::USDT;
  liquidity_ind = ELastLiquidityInd::AddedLiquidity;
  FutureRevokeCustomerFeeRateBuilder revoke_req_build_4(coin, uid, liquidity_ind);
  resp = user.process(revoke_req_build_4.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), 0);
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), "");
  EXPECT_EQ(ret_setting.setting_version(), 3);
  EXPECT_GE(ret_setting.updated_at_e9(), last_update_at);
  last_update_at = ret_setting.updated_at_e9();

  // case5: 回滚成功, taker
  liquidity_ind = ELastLiquidityInd::RemovedLiquidity;
  FutureRevokeCustomerFeeRateBuilder revoke_req_build_5(coin, uid, liquidity_ind);
  resp = user.process(revoke_req_build_5.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), 0);
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), 0);
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), "");
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), "");
  EXPECT_EQ(ret_setting.setting_version(), 4);
  EXPECT_GE(ret_setting.updated_at_e9(), last_update_at);
  last_update_at = ret_setting.updated_at_e9();

  // case6: 回滚成功, taker&maker, 先重新设置下taker&maker fee rate.
  resp = user.process(set_req_build.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  EXPECT_EQ(ret_setting.setting_version(), 5);
  EXPECT_GE(ret_setting.updated_at_e9(), last_update_at);
  last_update_at = ret_setting.updated_at_e9();

  liquidity_ind = ELastLiquidityInd::LiquidityRoutedOut;
  FutureRevokeCustomerFeeRateBuilder revoke_req_build_6(coin, uid, liquidity_ind);
  resp = user.process(revoke_req_build_6.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), 0);
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), 0);
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), "");
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), "");
  EXPECT_EQ(ret_setting.setting_version(), 6);
  EXPECT_GE(ret_setting.updated_at_e9(), last_update_at);
  last_update_at = ret_setting.updated_at_e9();

  // case7: 设置USDC费率成功
  coin = ECoin::USDC;
  FutureSetCustomerFeeRateBuilder set_req_build_7(coin, uid, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  resp = user.process(set_req_build_7.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  EXPECT_EQ(ret_setting.setting_version(), 7);
  EXPECT_GE(ret_setting.updated_at_e9(), last_update_at);
  last_update_at = ret_setting.updated_at_e9();

  // case8: 给USDC设置一个不同的费率
  tkfr_value = 32000;
  tkfr_desc = "usdc_fake_tkfr";
  mkfr_value = 10000;
  mkfr_desc = "usdc_fake_mkfr";
  FutureSetCustomerFeeRateBuilder set_req_build_8(coin, uid, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  resp = user.process(set_req_build_8.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  EXPECT_EQ(ret_setting.setting_version(), 8);
  EXPECT_GE(ret_setting.updated_at_e9(), last_update_at);
}

TEST_F(TestCustomerFeeRate, SetCustomerFeeRateFail) {
  auto config_center = bbase::object_manager::ObjectManager::GetObject<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("min_maker_fee_rate", "-15000");
  config_center->SetStringVar("max_maker_fee_rate", "20000");
  config_center->SetStringVar("min_taker_fee_rate", "20000");
  config_center->SetStringVar("max_taker_fee_rate", "60000");

  biz::user_id_t uid = 100000;
  stub user(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  biz::coin_t coin = ECoin::USDT;
  biz::fee_rate_e8_t tkfr_value = 70000;
  std::string tkfr_desc = "new_taker_fee_rate_desc";
  biz::fee_rate_e8_t mkfr_value = 15000;
  std::string mkfr_desc = "new_maker_fee_rate_desc";

  // case1: 设置一个超过范围的taker fee.
  FutureSetCustomerFeeRateBuilder req_build(coin, uid, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  auto resp = user.process(req_build.Build());
  // if (resp != nullptr) {
  //   std::cout << "resp: " << resp->ret_code() << "-" << resp->ret_msg() << std::endl;
  // }
  ASSERT_EQ(error::kErrorCodeParamsError, resp->ret_code());
  ASSERT_STREQ("taker fee rate must in range of [20000,60000]", resp->ret_msg().c_str());

  // case2: 设置一个超过范围的maker fee.
  tkfr_value = 30000;
  mkfr_value = 25000;
  FutureSetCustomerFeeRateBuilder req_build_2(coin, uid, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  resp = user.process(req_build_2.Build());
  // if (resp != nullptr) {
  //   std::cout << "resp: " << resp->ret_code() << "-" << resp->ret_msg() << std::endl;
  // }
  ASSERT_EQ(error::kErrorCodeParamsError, resp->ret_code());
  ASSERT_STREQ("maker fee rate must in range of [-15000,20000]", resp->ret_msg().c_str());

  // TODO(weiwei): 这里留待check, 拿不到result
  // auto result = te->PopResult();
  // ASSERT_NE(result.m_msg.get(), nullptr);

  // case3: 请求body里没有带费率参数
  FutureSetCustomerFeeRateBuilder req_build_3(coin, uid);
  resp = user.process(req_build_3.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(error::kErrorCodeParamsError, resp->ret_code());
}

TEST_F(TestCustomerFeeRate, MakerAndTakerFeeRate) {
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user2.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  // Maker Long
  auto u1_buy_oid = te->GenUUID();
  FutureOneOfCreateOrderBuilder u1_buy_build(u1_buy_oid, ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                             EOrderType::Limit, 0.01 * 1e8, 20000 * 1e4, ETimeInForce::GoodTillCancel,
                                             uid18, ECoin::USDT);
  auto u1_buy_resp = user1.process(u1_buy_build.Build());
  auto u1_new_result = te->PopResult();
  ASSERT_NE(u1_new_result.m_msg.get(), nullptr);
  auto o_check = u1_new_result.RefFutureMarginResult().RefRelatedOrderByOrderId(u1_buy_oid);
  ASSERT_NE(o_check.m_msg, nullptr);
  o_check.Check(uid18, 0.01 * 1e8);

  // Taker Short
  auto u2_sell_oid = te->GenUUID();
  FutureOneOfCreateOrderBuilder u2_sell_build(u2_sell_oid, ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Sell,
                                              EOrderType::Limit, 0.01 * 1e8, 20000 * 1e4, ETimeInForce::GoodTillCancel,
                                              uid28, ECoin::USDT);
  auto u2_sell_resp = user2.process(u2_sell_build.Build());
  auto u2_new_taker_result = te->PopResult();
  ASSERT_NE(u2_new_taker_result.m_msg.get(), nullptr);
  auto o2_check = u2_new_taker_result.RefFutureMarginResult().RefRelatedOrderByOrderId(u2_sell_oid);
  ASSERT_NE(o2_check.m_msg, nullptr);
  o2_check.Check(uid28, 0.01 * 1e8);
  auto p2_check = u2_new_taker_result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(p2_check.m_msg, nullptr);
  p2_check.CheckSize(0.01 * 1e8);
  auto related_fills = u2_new_taker_result.RefFutureMarginResult().m_msg->related_fills();
  ASSERT_NE(related_fills.size(), 0);
  // 此时应该是默认费率，taker_fee_rate:60000
  // std::cout << "fee_rate:" << related_fills[0].fee_rate_e8() << "exe_fee:" << related_fills[0].exec_fee_e8()
  //           << std::endl;
  EXPECT_EQ(related_fills[0].fee_rate_e8(), 60000);
  EXPECT_EQ(related_fills[0].exec_fee_e8(), 60000 * 20000 * 0.01);

  auto u1_maker_result = te->PopResult();
  ASSERT_NE(u1_maker_result.m_msg.get(), nullptr);
  related_fills = u1_maker_result.RefFutureMarginResult().m_msg->related_fills();
  ASSERT_NE(related_fills.size(), 0);
  // 此时应该是默认费率，maker_fee_rate:10000
  // std::cout << "fee_rate:" << related_fills[0].fee_rate_e8() << "exe_fee:" << related_fills[0].exec_fee_e8()
  //           << std::endl;
  EXPECT_EQ(related_fills[0].fee_rate_e8(), 10000);
  EXPECT_EQ(related_fills[0].exec_fee_e8(), 10000 * 20000 * 0.01);
}

TEST_F(TestCustomerFeeRate, MyFeeRate) {
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user2.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  // u1设置一个自定义coin费率
  biz::coin_t coin = ECoin::USDT;
  biz::fee_rate_e8_t tkfr_value = 30000;
  std::string tkfr_desc = "new_coin_tkfr_desc";
  biz::fee_rate_e8_t mkfr_value = 15000;
  std::string mkfr_desc = "new_coin_tkfr_desc";
  FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  auto u1_set_resp = user1.process(set_req_build.Build());
  ASSERT_NE(u1_set_resp, nullptr);
  ASSERT_EQ(error::kErrorCodeSuccess, u1_set_resp->ret_code());
  auto u1_set_result = te->PopResult();
  ASSERT_NE(u1_set_result.m_msg.get(), nullptr);
  auto ret_setting = u1_set_result.m_msg->asset_margin_result().per_user_setting_data();
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
  ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());

  // u2设置一个自定义symbol费率
  ESymbol symbol = ESymbol::BTCUSDT;
  biz::fee_rate_e8_t symbol_tkfr_value = 40000;
  std::string symbol_tkfr_desc = "new_symbol_tkfr_desc";
  biz::fee_rate_e8_t symbol_mkfr_value = 13000;
  std::string symbol_mkfr_desc = "new_symbol_mkfr_desc";
  FutureSetSingleFeeRateBuilder set_req_build2(coin, uid28, symbol, symbol_tkfr_value, symbol_tkfr_desc,
                                               symbol_mkfr_value, symbol_mkfr_desc);
  auto u2_set_resp = user2.process(set_req_build2.Build());
  ASSERT_EQ(error::kErrorCodeSuccess, u2_set_resp->ret_code());
  auto u2_set_result = te->PopResult();
  ASSERT_NE(u2_set_result.m_msg.get(), nullptr);
  ret_setting = u2_set_result.m_msg->asset_margin_result().per_user_setting_data();
  ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).taker_fee_rate_e8(),
            symbol_tkfr_value);
  ASSERT_EQ(ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).maker_fee_rate_e8(),
            symbol_mkfr_value);
  ASSERT_STREQ(
      ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).taker_fee_rate_desc().c_str(),
      symbol_tkfr_desc.c_str());
  ASSERT_STREQ(
      ret_setting.future_symbol_setting_dto().future_symbol_config_map().at(symbol).maker_fee_rate_desc().c_str(),
      symbol_mkfr_desc.c_str());

  // u2 sell
  auto u2_sell_oid = te->GenUUID();
  FutureOneOfCreateOrderBuilder u2_sell_build(u2_sell_oid, ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Sell,
                                              EOrderType::Limit, 0.01 * 1e8, 20000 * 1e4, ETimeInForce::GoodTillCancel,
                                              uid28, ECoin::USDT);
  auto u2_sell_resp = user2.process(u2_sell_build.Build());
  auto u2_new_result = te->PopResult();
  ASSERT_NE(u2_new_result.m_msg.get(), nullptr);
  auto o_check = u2_new_result.RefFutureMarginResult().RefRelatedOrderByOrderId(u2_sell_oid);
  ASSERT_NE(o_check.m_msg, nullptr);
  o_check.Check(uid28, 0.01 * 1e8);

  // u1 buy more, taker
  auto u1_buy_oid = te->GenUUID();
  FutureOneOfCreateOrderBuilder u1_buy_build(u1_buy_oid, ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                             EOrderType::Limit, 0.02 * 1e8, 20000 * 1e4, ETimeInForce::GoodTillCancel,
                                             uid18, ECoin::USDT);
  auto u1_buy_resp = user1.process(u1_buy_build.Build());
  auto u1_new_taker_result = te->PopResult();
  ASSERT_NE(u1_new_taker_result.m_msg.get(), nullptr);
  auto p1_check = u1_new_taker_result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(p1_check.m_msg, nullptr);
  p1_check.CheckSize(0.01 * 1e8);
  auto related_fills = u1_new_taker_result.RefFutureMarginResult().m_msg->related_fills();
  ASSERT_NE(related_fills.size(), 0);
  // 此时应该是默认费率，taker_fee_rate:30000
  EXPECT_EQ(related_fills[0].fee_rate_e8(), 30000);
  EXPECT_EQ(related_fills[0].exec_fee_e8(), 30000 * 20000 * 0.01);
  EXPECT_STREQ(related_fills[0].fee_rate_desc().c_str(), tkfr_desc.c_str());

  // u2 response, maker
  auto u2_maker_result = te->PopResult();
  ASSERT_NE(u2_maker_result.m_msg.get(), nullptr);
  related_fills = u2_maker_result.RefFutureMarginResult().m_msg->related_fills();
  ASSERT_NE(related_fills.size(), 0);
  // 此时应该是默认费率，maker_fee_rate:10000
  EXPECT_EQ(related_fills[0].fee_rate_e8(), 13000);
  EXPECT_EQ(related_fills[0].exec_fee_e8(), 13000 * 20000 * 0.01);
  EXPECT_STREQ(related_fills[0].fee_rate_desc().c_str(), symbol_mkfr_desc.c_str());
}

extern void SendSettleMessage();
/*
1. 存在挂单，和仓位，试算通过设置成功，检查dump下去的系数和成本都正确，全仓 单/双仓
  a. 设置成功后，coin维度个性化费率设置，大于默认费率，被拒绝
  b. 设置成功后，合约维度个性化费率设置，大于默认费率，被拒绝
  c. revoke费率
  d. 设置默认费率低于交割费率，永续交割时，使用较低的进行交割
2. 存在挂单，和仓位，试算通过设置成功，检查dump下去的系数和成本都正确，逐仓 单/双仓
3. 存在挂单，和仓位，试算通过设置成功，检查dump下去的系数和成本都正确，组合模式
*/

// #1. 存在挂单，和仓位，试算通过设置成功，检查dump下去的系数和成本都正确，全仓 单/双仓
TEST_F(TestCustomerFeeRate, SetCustomerFeeRate_Success_Cross) {
  // user1为单仓
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  // user2为双仓
  biz::user_id_t const uid_2 = 658002;
  stub user2(te, uid_2);

  create_order_and_pz(user1, user2, ::enums::eaccountmode::AccountMode::Cross, this->te);
  auto coin = ECoin::USDT;
  auto symbol = ESymbol::BTCUSDT;

  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "300000");
  config_center->SetStringVar("max_maker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "300000");

  std::string tkr_desc = "";
  std::string mkr_desc = "";
  {
    // 改单仓默认费率
    FutureSetCustomerFeeRateBuilder req_build(coin, uid_1, 90000, tkr_desc, 10000, mkr_desc, 90000);
    auto resp = user1.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());

    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);

    // 查看有仓位订单的postion
    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, symbol);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    user1_positionCheck.CheckLv(100 * 1e2, 1179100, 1180900, 679100, 680900);
    ASSERT_EQ(user1_positionCheck.m_msg->value_e8(), 4 * 20000 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->position_mm_with_fee_e8(), 471.28 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->order_mm_with_fee_e8(), 258.058 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->min_position_cost_e8(), 871.28 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->order_cost_e8(), 448.058 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->is_isolated(), false);
  }

  {
    // 改双仓默认费率
    FutureSetCustomerFeeRateBuilder req_build(coin, uid_2, 90000, tkr_desc, 10000, mkr_desc, 90000);
    auto resp = user2.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());

    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);

    // 查看有仓位订单的postion
    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(2, symbol);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    user2_positionCheck.CheckLv(100 * 1e2, 1179100, 1180900, 679100, 680900);
    ASSERT_EQ(user2_positionCheck.m_msg->value_e8(), 4 * 20000 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->position_mm_with_fee_e8(), 472.72 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->order_mm_with_fee_e8(), 285.978 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->min_position_cost_e8(), 872.72 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->order_cost_e8(), 495.978 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 2);
    ASSERT_EQ(user2_positionCheck.m_msg->is_isolated(), false);

    auto user2_positionCheck2 =
        user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(1, symbol);
    ASSERT_NE(user2_positionCheck2.m_msg, nullptr);
    user2_positionCheck2.CheckLv(100 * 1e2, 1179100, 1180900, 679100, 680900);
    ASSERT_EQ(user2_positionCheck2.m_msg->position_idx(), 1);
    ASSERT_EQ(user2_positionCheck2.m_msg->is_isolated(), false);
  }

  // a.设置成功后，coin维度个性化费率设置，大于默认费率，被拒绝
  {
    FutureSetCustomerFeeRateBuilder req_build(coin, uid_1, 100000, tkr_desc, 10000, mkr_desc);
    auto resp = user1.process(req_build.Build());
    ASSERT_EQ(error::kEcInvalidNewTakerFeeRate, resp->ret_code());
  }

  {
    FutureSetCustomerFeeRateBuilder req_build(coin, uid_1, 90000, tkr_desc, 100000, mkr_desc);
    auto resp = user1.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeInvalidNewMakerFeeRate, resp->ret_code());
  }

  std::string tkfr_desc = "";
  std::string mkfr_desc = "";
  // b.设置成功后，合约维度个性化费率设置，大于默认费率，被拒绝
  {
    FutureSetSingleFeeRateBuilder set_req_build(coin, uid_1, ESymbol::BTCUSDT, 100000, tkfr_desc, 10000, mkfr_desc);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_EQ(error::kEcInvalidNewTakerFeeRate, resp->ret_code());
  }

  {
    FutureSetSingleFeeRateBuilder set_req_build(coin, uid_2, ESymbol::BTCUSDT, 50000, tkfr_desc, 100000, mkfr_desc);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_EQ(error::kErrorCodeInvalidNewMakerFeeRate, resp->ret_code());
  }

  {
    // c.revoke 默认费率
    FutureRevokeCustomerFeeRateBuilder revoke_req_build(coin, uid_1, 4);
    auto resp = user1.process(revoke_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kEcInvalidNewTakerFeeRate, resp->ret_code());

    FutureRevokeCustomerFeeRateBuilder revoke_req_build_1(coin, uid_1, 2);
    resp = user1.process(revoke_req_build_1.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    FutureRevokeCustomerFeeRateBuilder revoke_req_build_2(coin, uid_1, 4);
    resp = user1.process(revoke_req_build_2.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto positionCheck = result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, symbol);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(100 * 1e2, 1119400, 1120600, 619400, 620600);
    ASSERT_EQ(positionCheck.m_msg->value_e8(), 4 * 20000 * 1e8);
    ASSERT_EQ(positionCheck.m_msg->position_mm_with_fee_e8(), 447.52 * 1e8);
    ASSERT_EQ(positionCheck.m_msg->order_mm_with_fee_e8(), 235.372 * 1e8);
    ASSERT_EQ(positionCheck.m_msg->min_position_cost_e8(), 847.52 * 1e8);
    ASSERT_EQ(positionCheck.m_msg->order_cost_e8(), 425.372 * 1e8);
    ASSERT_EQ(positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(positionCheck.m_msg->position_idx(), 0);
    ASSERT_EQ(positionCheck.m_msg->is_isolated(), false);
  }

  //  d. 设置默认费率低于交割费率，永续交割时，使用较低的进行交割
  {
    // 交割
    SendSettleMessage();

    auto check_func = [](UnifiedV2ResultDTOChecker& result) {
      ASSERT_NE(result.m_msg.get(), nullptr);
      // 检查持仓mm
      auto trade_check = result.RefFutureMarginResult().RefRelatedFillByIndex(0);
      ASSERT_NE(trade_check.m_msg, nullptr);
      if (trade_check.m_msg->user_id() == uid_1) {
        ASSERT_EQ(trade_check.m_msg->fee_rate_e8(), 60000);
      } else {
        ASSERT_EQ(trade_check.m_msg->fee_rate_e8(), 90000);
      }
    };

    auto user_result = te->PopResult();
    check_func(user_result);

    auto user_result2 = te->PopResult();
    check_func(user_result2);
  }
}

// #2. 存在挂单，和仓位，试算通过设置成功，检查dump下去的系数和成本都正确，逐仓 单/双仓
TEST_F(TestCustomerFeeRate, SetCustomerFeeRate_Success_Isolated) {
  // user1为单仓
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  // user2为双仓
  biz::user_id_t const uid_2 = 658002;
  stub user2(te, uid_2);

  create_order_and_pz(user1, user2, ::enums::eaccountmode::AccountMode::Isolated, this->te);

  auto coin = ECoin::USDT;
  auto symbol = ESymbol::BTCUSDT;

  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "300000");
  config_center->SetStringVar("max_maker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "300000");

  std::string tkr_desc = "";
  std::string mkr_desc = "";
  {
    // 改单仓默认费率
    FutureSetCustomerFeeRateBuilder req_build(coin, uid_1, 90000, tkr_desc, 10000, mkr_desc, 90000);
    auto resp = user1.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());

    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);

    // 查看有仓位订单的postion
    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, symbol);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    user1_positionCheck.CheckLv(100 * 1e2, 1179100, 1180900, 679100, 680900);
    ASSERT_EQ(user1_positionCheck.m_msg->value_e8(), 4 * 20000 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->position_mm_with_fee_e8(), 471.28 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->order_mm_with_fee_e8(), 258.058 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->min_position_cost_e8(), 871.28 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->order_cost_e8(), 448.058 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->position_balance_e8(), 871.28 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->is_isolated(), true);
    ASSERT_EQ(user1_positionCheck.m_msg->liq_price_x(), 19900 * 1e4);
  }

  {  // 充值、调杠杆、切双仓
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;

    // user1
    FutureDepositBuilder u1_deposit_build(req_id, uid_1, trans_id, coin, wallet_record_type, "500", "0");
    auto u1_resp = user1.process(u1_deposit_build.Build());
    auto u1_result = te->PopResult();

    // 增加保证金
    AddMargin add_margin(uid_1, ESymbol::BTCUSDT, ECoin::USDT, EPositionIndex::Single, 400 * 1e8);
    auto resp = user1.process(add_margin.Build());
    auto result = te->PopResult();

    auto user1_positionCheck = result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, symbol);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    user1_positionCheck.CheckLv(100 * 1e2, 1179100, 1180900, 679100, 680900);
    ASSERT_EQ(user1_positionCheck.m_msg->value_e8(), 4 * 20000 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_balance_e8(), 1271.28 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->is_isolated(), true);
    ASSERT_EQ(user1_positionCheck.m_msg->liq_price_x(), 19800 * 1e4);

    // 改单仓默认费率
    FutureSetCustomerFeeRateBuilder req_build(coin, uid_1, 90000, tkr_desc, 10000, mkr_desc, 100000);
    resp = user1.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());

    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);

    // 查看有仓位订单的postion
    user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, symbol);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    user1_positionCheck.CheckLv(100 * 1e2, 1199000, 1201000, 699000, 701000);
    ASSERT_EQ(user1_positionCheck.m_msg->value_e8(), 4 * 20000 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->position_mm_with_fee_e8(), 479.2 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->order_mm_with_fee_e8(), 265.62 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->min_position_cost_e8(), 879.2 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->position_balance_e8(), 1279.2 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->order_cost_e8(), 455.62 * 1e8);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->is_isolated(), true);
    ASSERT_EQ(user1_positionCheck.m_msg->liq_price_x(), 19799.9 * 1e4);
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
  {
    // 改双仓默认费率
    FutureSetCustomerFeeRateBuilder req_build(coin, uid_2, 90000, tkr_desc, 10000, mkr_desc, 90000);
    auto resp = user2.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());

    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);

    // 查看有仓位订单的postion
    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(2, symbol);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    user2_positionCheck.CheckLv(100 * 1e2, 1179100, 1180900, 679100, 680900);
    ASSERT_EQ(user2_positionCheck.m_msg->value_e8(), 4 * 20000 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->position_mm_with_fee_e8(), 472.72 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->order_mm_with_fee_e8(), 285.978 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->min_position_cost_e8(), 872.72 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->order_cost_e8(), 495.978 * 1e8);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 2);
    ASSERT_EQ(user2_positionCheck.m_msg->is_isolated(), true);

    auto user2_positionCheck2 =
        user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(1, symbol);
    ASSERT_NE(user2_positionCheck2.m_msg, nullptr);
    user2_positionCheck2.CheckLv(100 * 1e2, 1179100, 1180900, 679100, 680900);
    ASSERT_EQ(user2_positionCheck2.m_msg->position_idx(), 1);
    ASSERT_EQ(user2_positionCheck2.m_msg->is_isolated(), true);
  }
}

// #3. 存在挂单，和仓位，试算通过设置成功，检查dump下去的系数和成本都正确，组合模式
TEST_F(TestCustomerFeeRate, SetCustomerFeeRate_Success_pm) {
  // user1为单仓
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  // user2为双仓
  biz::user_id_t const uid_2 = 658002;
  stub user2(te, uid_2);

  create_order_and_pz(user1, user2, ::enums::eaccountmode::Isolated, this->te);

  std::string req_id = "";
  std::string trans_id = "";
  auto coin = ECoin::USDT;
  int wallet_record_type = 1;

  FutureDepositBuilder u1_deposit_build(req_id, user1.m_uid, trans_id, coin, wallet_record_type, "3000000", "0");
  auto u1_resp = user1.process(u1_deposit_build.Build());
  auto u1_result = te->PopResult();

  // 切换PM模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid_1);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      EAccountMode::Portfolio);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid_1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "切换PMResult:" << jsonStr2 << std::endl;

  result.RefAssetMarginResult().RefRelatedUserSetting().CheckAccountMode(EAccountMode::Portfolio);

  // auto coin = ECoin::USDT;

  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "300000");
  config_center->SetStringVar("max_maker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "300000");
  std::string tkr_desc = "";
  std::string mkr_desc = "";
  {
    // 改单仓默认费率

    FutureSetCustomerFeeRateBuilder req_build(coin, uid_1, 90000, tkr_desc, 10000, mkr_desc, 100000);
    auto resp = user1.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());

    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);

    user1_result.CheckUid(uid_1);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*user1_result.m_msg.get(), &jsonStr);
    std::cout << "费率设置结果:" << jsonStr << std::endl;
    user1_result.RefAssetMarginResult().RefRelatedUserSetting().CheckAccountMode(EAccountMode::Portfolio);
    ASSERT_EQ(100000, user1_result.RefAssetMarginResult()
                          .RefRelatedUserSetting()
                          .m_msg->future_per_coin_setting()
                          .find(coin)
                          ->second.default_taker_fee_rate_e8());
  }
}

/*
1.存在挂单和仓位，试算不通过，设置失败，逐仓触发强平价试算不过
2.存在挂单和仓位，试算不通过，设置失败，全仓MMR超100%试算不过
3.存在挂单和仓位，试算不通过，设置失败，组合模式，试算不过
*/

// #1. 存在挂单和仓位，试算不通过，设置失败，逐仓触发强平价试算不过
TEST_F(TestCustomerFeeRate, SetCustomerFeeRate_failed_isolated) {
  // user1为单仓
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  // user2为双仓
  biz::user_id_t const uid_2 = 658002;
  stub user2(te, uid_2);

  create_order_and_pz(user1, user2, ::enums::eaccountmode::AccountMode::Isolated, this->te);
  auto coin = ECoin::USDT;
  auto symbol = ESymbol::BTCUSDT;

  std::string tkr_desc = "";
  std::string mkr_desc = "";
  // 双仓强平价为 20100     单仓强平价为19900
  // 1.因触发强平价，拒绝
  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "300000");
  {
    te->AddMarkPrice(static_cast<ESymbol>(symbol), 15000, 15000, 15000);
    // 改单仓默认费率
    FutureSetCustomerFeeRateBuilder req_build(coin, uid_1, 300000, tkr_desc, 10000, mkr_desc, 300000);
    auto resp = user1.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeFeeRateTooHigh, resp->ret_code());
  }

  {
    te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);
    // 改双仓默认费率
    FutureSetCustomerFeeRateBuilder req_build(coin, uid_2, 300000, tkr_desc, 10000, mkr_desc, 300000);
    auto resp = user2.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeFeeRateTooHigh, resp->ret_code());
  }
  // 2.ab不够不足minpc，强平价变化，拒绝
  {
    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    // 改单仓默认费率
    FutureSetCustomerFeeRateBuilder req_build(coin, uid_1, 300000, tkr_desc, 10000, mkr_desc, 300000);
    auto resp = user1.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeFeeRateTooHigh, resp->ret_code());
  }
}

// #2. 存在挂单和仓位，试算不通过，设置失败，全仓MMR超100%试算不过
TEST_F(TestCustomerFeeRate, SetCustomerFeeRate_failed_cross) {
  // user1为单仓
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  // user2为双仓
  biz::user_id_t const uid_2 = 658002;
  stub user2(te, uid_2);

  create_order_and_pz(user1, user2, ::enums::eaccountmode::AccountMode::Cross, this->te);
  auto coin = ECoin::USDT;
  auto symbol = ESymbol::BTCUSDT;

  std::string tkr_desc = "";
  std::string mkr_desc = "";
  {
    te->AddMarkPrice(static_cast<ESymbol>(symbol), 15000, 15000, 15000);
    // 改单仓默认费率
    auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
        bbase::object_manager::ObjectType::kObjectConfigCenter);
    config_center->SetStringVar("max_taker_fee_rate", "***********");
    config_center->SetStringVar("max_maker_fee_rate", "300000");
    config_center->SetStringVar("max_default_taker_fee_rate", "***********");

    FutureSetCustomerFeeRateBuilder req_build(coin, uid_1, 300000, tkr_desc, 10000, mkr_desc, 300000);
    auto resp = user1.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeFeeRateTooHigh, resp->ret_code());
  }

  {
    te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);
    // 改双仓默认费率
    FutureSetCustomerFeeRateBuilder req_build(coin, uid_2, 300000, tkr_desc, 10000, mkr_desc, 300000);
    auto resp = user2.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeFeeRateTooHigh, resp->ret_code());
  }
}

// #3. 存在挂单和仓位，试算不通过，设置失败，组合模式，试算不过

TEST_F(TestCustomerFeeRate, SetCustomerFeeRate_failed_pm) {
  // user1为单仓
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  // user2为双仓
  biz::user_id_t const uid_2 = 658002;
  stub user2(te, uid_2);

  create_order_and_pz(user1, user2, ::enums::eaccountmode::Isolated, this->te);

  std::string req_id = "";
  std::string trans_id = "";
  auto coin = ECoin::USDT;
  int wallet_record_type = 1;

  FutureDepositBuilder u1_deposit_build(req_id, user1.m_uid, trans_id, coin, wallet_record_type, "3000000", "0");
  auto u1_resp = user1.process(u1_deposit_build.Build());
  auto u1_result = te->PopResult();

  // 切换PM模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid_1);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      EAccountMode::Portfolio);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid_1, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  std::string jsonStr2;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
  std::cout << "切换PMResult:" << jsonStr2 << std::endl;

  result.RefAssetMarginResult().RefRelatedUserSetting().CheckAccountMode(EAccountMode::Portfolio);

  // auto coin = ECoin::USDT;

  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "***********");
  config_center->SetStringVar("max_maker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "***********");
  std::string tkr_desc = "";
  std::string mkr_desc = "";
  {
    // 改单仓默认费率
    FutureSetCustomerFeeRateBuilder req_build(coin, uid_1, 90000, tkr_desc, 10000, mkr_desc, ***********);
    auto resp = user1.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeFeeRateTooHigh, resp->ret_code());
  }
}

TEST_F(TestCustomerFeeRate, SetCustomerFeeRate_failed_isolated_only_with_order) {
  // user1为单仓
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;
  {
    // 充值、调杠杆、切双仓
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;

    // user1
    FutureDepositBuilder u1_deposit_build(req_id, user1.m_uid, trans_id, coin, wallet_record_type, "1000", "0");
    auto u1_resp = user1.process(u1_deposit_build.Build());
    auto u1_result = te->PopResult();

    FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, user1.m_uid, 100 * 1e2, 100 * 1e2);
    auto resp1 = user1.process(set_lv_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(100 * 1e2, 1119400, 1120600, 619400, 620600);

    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user1.m_uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user1.m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);

  {
    // user1  单仓买
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
  }
  // 2.ab为负，不够补oc
  {
    std::string tkr_desc = "";
    std::string mkr_desc = "";
    auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
        bbase::object_manager::ObjectType::kObjectConfigCenter);
    config_center->SetStringVar("max_taker_fee_rate", "300000");
    config_center->SetStringVar("max_default_taker_fee_rate", "300000");
    // 改单仓默认费率
    FutureSetCustomerFeeRateBuilder req_build(coin, uid_1, 300000, tkr_desc, 10000, mkr_desc, 300000);
    auto resp = user1.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeFeeRateTooHigh, resp->ret_code());
  }
}

// 设置了合约维度的费率，当预占小于结算时报错
TEST_F(TestCustomerFeeRate, SetCustomerFeeRate_failed_of_single_feerate) {
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);
  auto coin = ECoin::USDT;
  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "300000");
  config_center->SetStringVar("max_maker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "300000");

  // 1.设置预占费率
  std::string tkr_desc = "";
  std::string mkr_desc = "";
  FutureSetCustomerFeeRateBuilder req_build(coin, uid_1, 300000, tkr_desc, 300000, mkr_desc, 300000);
  auto resp = user1.process(req_build.Build());
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());

  // 2.设置合约维度taker和maker结算费率
  enums::esymbol::Symbol symbol = enums::esymbol::BTCUSDT;
  biz::fee_rate_e8_t tkfr_value = 60000;
  std::string tkfr_desc = "new_taker_fee_rate_desc";
  biz::fee_rate_e8_t mkfr_value = 60000;
  std::string mkfr_desc = "new_maker_fee_rate_desc";
  FutureSetSingleFeeRateBuilder set_req_build(coin, uid_1, symbol, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  resp = user1.process(set_req_build.Build());
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());

  return;
  // 合约维度改为sc.defaulttakerfeerate校验
  // 3.降低预占费率，因为存在合约维度taker结算费率大于预占被拒绝
  FutureSetCustomerFeeRateBuilder req_build2(coin, uid_1, 200000, tkr_desc, 200000, mkr_desc, 200000);
  resp = user1.process(req_build2.Build());
  ASSERT_EQ(error::kEcInvalidNewTakerFeeRate, resp->ret_code());

  // 4.移除合约维度taker费率
  FutureRevokeSingleFeeRateBuilder revoke_req_build1(coin, uid_1, symbol, enums::elastliquidityind::RemovedLiquidity);
  resp = user1.process(revoke_req_build1.Build());
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());

  // 5.降低预占费率，因为存在合约维度maker结算费率大于预占被拒绝
  FutureSetCustomerFeeRateBuilder req_build3(coin, uid_1, 200000, tkr_desc, 200000, mkr_desc, 200000);
  resp = user1.process(req_build3.Build());
  ASSERT_EQ(error::kErrorCodeInvalidNewMakerFeeRate, resp->ret_code());
}

TEST_F(TestCustomerFeeRate, SetCustomerFeeRate_check_dumpresult) {
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);
  auto coin = ECoin::USDT;
  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "300000");
  config_center->SetStringVar("max_maker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "300000");

  // 1.设置预占费率
  std::string tkr_desc = "";
  std::string mkr_desc = "";
  FutureSetCustomerFeeRateBuilder req_build(coin, uid_1, 300000, tkr_desc, 300000, mkr_desc, 300000);
  auto resp = user1.process(req_build.Build());
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  te->PopResult();

  // 2.设置合约维度taker和maker结算费率
  enums::esymbol::Symbol symbol = enums::esymbol::BTCUSDT;
  biz::fee_rate_e8_t tkfr_value = 60000;
  std::string tkfr_desc = "new_taker_fee_rate_desc";
  biz::fee_rate_e8_t mkfr_value = 60000;
  std::string mkfr_desc = "new_maker_fee_rate_desc";
  FutureSetSingleFeeRateBuilder set_req_build(coin, uid_1, symbol, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  resp = user1.process(set_req_build.Build());
  ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  auto u1_result = te->PopResult();
  ASSERT_NE(u1_result.m_msg.get(), nullptr);
  auto setting_check = u1_result.RefAssetMarginResult().RefRelatedUserSetting();
  ASSERT_EQ(setting_check.m_msg->future_symbol_setting_dto().future_symbol_config_map_size(), 1);

  {
    // 充值、调杠杆、切双仓
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;

    // user1
    FutureDepositBuilder u1_deposit_build(req_id, user1.m_uid, trans_id, coin, wallet_record_type, "1000", "0");
    auto u1_resp = user1.process(u1_deposit_build.Build());
    u1_result = te->PopResult();
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);

  {
    // user1  单仓买
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 0.1 * 1e8;
    biz::price_x_t price = 20000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    setting_check = user1_result.RefAssetMarginResult().RefRelatedUserSetting();
    ASSERT_EQ(setting_check.m_msg->future_symbol_setting_dto().future_symbol_config_map_size(), 0);
  }
}

// 1.a 设置了coin维度个性化费率，同时也设置了user维度个性化费率；个性化费率低于sc.default_tkr_feerate）
// 结算使用了coin维度个性化费率，预占为sc.default_tkr_feerate
TEST_F(TestCustomerFeeRate, SetAndRevokeCustomerFeeRateForUser1) {
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, "");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, "");

    auto resp2 = user2.process(deposit_build.Build());
    auto result2 = te->PopResult();
  }

  // user1 case1: 设置coin费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    auto resp_c_setting = resp->user_setting().future_per_coin_setting().at(coin);
    ASSERT_EQ(resp_c_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(resp_c_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(resp_c_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(resp_c_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user1 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    auto resp_f_setting = resp->user_setting().future_user_setting_dto();
    ASSERT_EQ(resp_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(resp_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(resp_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(resp_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // user1  单仓买
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
  }

  // user2 case1: 设置coin费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user2 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // user2  单仓卖
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid28, coin);
    auto pz_buy_order_resp = user2.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);
    auto user2_order_check = user2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user2_order_check.m_msg, nullptr);
    user2_order_check.CheckQty(user2.m_uid, qty);
    ASSERT_EQ(user2_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user2_order_check.m_msg->side(), ESide::Sell);

    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user2_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user2_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill2 = user2_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill2.fee_rate_e8(), 30000);

    // 收user1 buy单result 验证结算费率
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill1 = user1_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill1.fee_rate_e8(), 15000);
  }

  // user1 revoke user级别费率
  {
    auto coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    (void)mkfr_value;
    std::string mkfr_desc = "new_maker_fee_rate_desc";
    auto liquidity_ind = ELastLiquidityInd::AddedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_4(coin, uid18, liquidity_ind);
    revoke_req_build_4.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(revoke_req_build_4.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");

    liquidity_ind = ELastLiquidityInd::RemovedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_5(coin, uid18, liquidity_ind);
    revoke_req_build_5.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    resp = user1.process(revoke_req_build_5.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto resp_f_setting = resp->user_setting().future_user_setting_dto();
    ASSERT_EQ(resp_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(resp_f_setting.taker_fee_rate_e8(), 0);
    ASSERT_STREQ(resp_f_setting.taker_fee_rate_desc().c_str(), "");
    ASSERT_STREQ(resp_f_setting.maker_fee_rate_desc().c_str(), "");
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), 0);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), "");
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");
  }
}

// 1.b 设置了coin维度个性化费率，同时也设置了user维度个性化费率；个性化费率低于sc.default_tkr_feerate）
// 验证在设置了user维度最终费率时，在能够匹配到symbol规则或者coin规则时，结算使用了user维度个性化费率，预占为sc.default_tkr_feerate
TEST_F(TestCustomerFeeRate, SetAndRevokeCustomerFeeRateForUser1_is_final) {
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, "");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, "");

    auto resp2 = user2.process(deposit_build.Build());
    auto result2 = te->PopResult();
  }

  // user1 case1: 设置coin费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user1 user费率，is_final为true
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc, {}, true);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_f_setting.is_final(), true);
  }

  // user1  单仓买，挂单验证bv2c，w6
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
  }

  // user2 case1: 设置coin费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user2 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc, {}, true);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_f_setting.is_final(), true);
  }

  // user2  单仓卖
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid28, coin);
    auto pz_buy_order_resp = user2.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);
    auto user2_order_check = user2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user2_order_check.m_msg, nullptr);
    user2_order_check.CheckQty(user2.m_uid, qty);
    ASSERT_EQ(user2_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user2_order_check.m_msg->side(), ESide::Sell);

    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user2_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user2_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill2 = user2_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill2.fee_rate_e8(), 40000);

    // 收user1 buy单result，验证结算费率
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill1 = user1_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill1.fee_rate_e8(), 20000);
  }

  // revoke user级别费率
  {
    auto coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    (void)mkfr_value;
    std::string mkfr_desc = "new_maker_fee_rate_desc";
    auto liquidity_ind = ELastLiquidityInd::AddedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_4(coin, uid18, liquidity_ind);
    revoke_req_build_4.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(revoke_req_build_4.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");

    liquidity_ind = ELastLiquidityInd::RemovedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_5(coin, uid18, liquidity_ind);
    revoke_req_build_5.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    resp = user1.process(revoke_req_build_5.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), 0);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), "");
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");
  }
}

// 2.a 设置了coin维度个性化费率，同时也设置了user维度个性化费率；此处费率使用超阈值费率，费率高于sc.default_tkr_feerate
// 验证在无法匹配到任何规则费率时，预占和结算使用了coin维度个性化费率
TEST_F(TestCustomerFeeRate, SetAndRevokeCustomerFeeRateForUser2) {
  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "300000");
  config_center->SetStringVar("max_maker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "300000");
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, "");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, "");

    auto resp2 = user2.process(deposit_build.Build());
    auto result2 = te->PopResult();
  }

  // user1 case1: 设置coin费率成功，超阈值 w7 > default w6
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 60000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 50000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc, 70000);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user1 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";
    biz::fee_rate_e8_t default_taker_value = 70000;

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc,
                                                  default_taker_value);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_EQ(ret_f_setting.default_taker_fee_rate_e8(), default_taker_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // user1  单仓买
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10133000);
  }

  // user2 case1: 设置coin费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user2 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // user2  单仓卖
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid28, coin);
    auto pz_buy_order_resp = user2.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);
    auto user2_order_check = user2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user2_order_check.m_msg, nullptr);
    user2_order_check.CheckQty(user2.m_uid, qty);
    ASSERT_EQ(user2_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user2_order_check.m_msg->side(), ESide::Sell);

    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user2_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user2_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill2 = user2_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill2.fee_rate_e8(), 30000);

    // 收user1 buy单result 使用coin级费率
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10133000);
    auto related_fill1 = user1_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill1.fee_rate_e8(), 50000);
  }

  // revoke user级别费率
  {
    auto coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    (void)mkfr_value;
    std::string mkfr_desc = "new_maker_fee_rate_desc";
    auto liquidity_ind = ELastLiquidityInd::AddedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_4(coin, uid18, liquidity_ind);
    revoke_req_build_4.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(revoke_req_build_4.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");

    liquidity_ind = ELastLiquidityInd::RemovedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_5(coin, uid18, liquidity_ind);
    revoke_req_build_5.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    resp = user1.process(revoke_req_build_5.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), 0);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), "");
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");
  }
}

// 2. 设置了coin维度个性化费率，同时也设置了user维度个性化费率；此处费率使用超阈值费率，费率高于sc.default_tkr_feerate
// 验证在设置了user维度最终费率时，在能够匹配到symbol规则或者coin规则时，预占和结算使用了user维度个性化费率
TEST_F(TestCustomerFeeRate, SetAndRevokeCustomerFeeRateForUser2_is_final) {
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, "");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, "");

    auto resp2 = user2.process(deposit_build.Build());
    auto result2 = te->PopResult();
  }

  // user1 case1: 设置coin费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user1 user费率 is_final为true
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 70000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 70000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";
    biz::fee_rate_e8_t default_tkfr_value = 70000;

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc,
                                                  default_tkfr_value, true);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_f_setting.is_final(), true);
  }

  // user1 单仓买 使用user的final费率
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10133000);
  }

  // user2 case1: 设置coin费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user2 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc, {}, true);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_f_setting.is_final(), true);
  }

  // user2  单仓卖
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid28, coin);
    auto pz_buy_order_resp = user2.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);
    auto user2_order_check = user2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user2_order_check.m_msg, nullptr);
    user2_order_check.CheckQty(user2.m_uid, qty);
    ASSERT_EQ(user2_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user2_order_check.m_msg->side(), ESide::Sell);

    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user2_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user2_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill2 = user2_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill2.fee_rate_e8(), 40000);

    // 收user1 buy单result，使用user级别final费率
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10133000);
    auto related_fill1 = user1_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill1.fee_rate_e8(), 70000);
  }

  // revoke user级别费率
  {
    auto coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 70000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 70000;
    (void)mkfr_value;
    std::string mkfr_desc = "new_maker_fee_rate_desc";
    auto liquidity_ind = ELastLiquidityInd::AddedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_4(coin, uid18, liquidity_ind);
    revoke_req_build_4.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(revoke_req_build_4.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");

    liquidity_ind = ELastLiquidityInd::RemovedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_5(coin, uid18, liquidity_ind);
    revoke_req_build_5.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    resp = user1.process(revoke_req_build_5.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), 0);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), "");
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");
  }
}

// 3. 设置了user维度个性化费率，同时也能匹配到user维度规则费率
// a. 验证在能够匹配到任何symbol和coin规则时，预占和结算都使用了symbol或者coin规则费率
TEST_F(TestCustomerFeeRate, SetAndRevokeCustomerFeeRateRuleForUser1) {
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":2,"rule_priority":5,"region_rule":0,"rule_config":{"coins":["USDT","BTC"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.00055","maker":"0.0002"},{"user_rule":"vip1","taker":"0.0005","maker":"0.00018"},{"user_rule":"vip2","taker":"0.0004","maker":"0.00016"}]}},{"rule_type":1,"rule_priority":5,"region_rule":0,"rule_config":{"symbols":["BTCUSDT"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0011","maker":"0.0004"},{"user_rule":"vip1","taker":"0.001","maker":"0.00036"},{"user_rule":"vip2","taker":"0.0008","maker":"0.00032"}]}},{"rule_type":4,"rule_priority":5,"region_rule":0,"rule_config":{"tag_ids":[52],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.00165","maker":"0.0006"},{"user_rule":"vip1","taker":"0.0015","maker":"0.00064"},{"user_rule":"vip2","taker":"0.0012","maker":"0.00048"}]}},{"rule_type":2,"rule_priority":5,"region_rule":1,"rule_config":{"coins":["USDT","BTC"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.001","maker":"0.0005"},{"user_rule":"vip1","taker":"0.0009","maker":"0.00045"},{"user_rule":"vip2","taker":"0.0008","maker":"0.0004"}]}},{"rule_type":1,"rule_priority":5,"region_rule":1,"rule_config":{"symbols":["XXXUSDT"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.002","maker":"0.001"},{"user_rule":"vip1","taker":"0.0018","maker":"0.0009"},{"user_rule":"vip2","taker":"0.0016","maker":"0.0008"}]}},{"rule_type":4,"rule_priority":5,"region_rule":1,"rule_config":{"tag_ids":[52],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.003","maker":"0.0015"},{"user_rule":"vip1","taker":"0.0027","maker":"0.00135"},{"user_rule":"vip2","taker":"0.0024","maker":"0.00012"}]}}]})";
  InitFeeRate(FeeRateConfig);
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, "");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, "");

    auto resp2 = user2.process(deposit_build.Build());
    auto result2 = te->PopResult();
  }

  // user1 case1: 设置coin费率成功
  // {
  //   biz::coin_t coin = ECoin::USDT;
  //   biz::fee_rate_e8_t tkfr_value = 30000;
  //   std::string tkfr_desc = "new_taker_fee_rate_desc";
  //   biz::fee_rate_e8_t mkfr_value = 15000;
  //   std::string mkfr_desc = "new_maker_fee_rate_desc";

  //   FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  //   auto resp = user1.process(set_req_build.Build());
  //   ASSERT_NE(resp, nullptr);
  //   auto resp_c_setting = resp->user_setting().future_per_coin_setting().at(coin);
  //   ASSERT_EQ(resp_c_setting.maker_fee_rate_e8(), mkfr_value);
  //   ASSERT_EQ(resp_c_setting.taker_fee_rate_e8(), tkfr_value);
  //   ASSERT_STREQ(resp_c_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  //   ASSERT_STREQ(resp_c_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  //   ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  //   auto result = te->PopResult();
  //   ASSERT_NE(result.m_msg.get(), nullptr);

  //   auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  //   ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
  //   ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
  //   ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  //   ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  //   EXPECT_EQ(ret_setting.setting_version(), 2);
  // }

  // 设置user1 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    auto resp_f_setting = resp->user_setting().future_user_setting_dto();
    ASSERT_EQ(resp_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(resp_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(resp_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(resp_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // 设置user规则
  {
    std::string user_rule = "vip2";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    set_fee_rate_user_rule.msg.mutable_set_fee_rate_user_rule_req()->set_for_user(true);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_f_setting = u1_resp->user_setting().future_user_setting_dto();
    EXPECT_EQ(resp_f_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_f_setting = u1_result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    EXPECT_EQ(ret_f_setting.fee_rate_user_rule(), user_rule.data());
  }

  // 设置coin规则
  {
    std::string user_rule = "vip1";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_coin_setting = u1_resp->user_setting().future_per_coin_setting().at(ECoin::USDT);
    EXPECT_EQ(resp_coin_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_coin_setting =
        u1_result.m_msg->asset_margin_result().per_user_setting_data().future_per_coin_setting().at(ECoin::USDT);
    EXPECT_EQ(ret_coin_setting.fee_rate_user_rule(), user_rule.data());
  }

  // user1  单仓买
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10152000);
  }

  // user2 case1: 设置coin费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user2 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // user2  单仓卖
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid28, coin);
    auto pz_buy_order_resp = user2.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);
    auto user2_order_check = user2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user2_order_check.m_msg, nullptr);
    user2_order_check.CheckQty(user2.m_uid, qty);
    ASSERT_EQ(user2_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user2_order_check.m_msg->side(), ESide::Sell);

    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user2_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user2_positionCheck.m_msg->buy_value_to_cost_e8(), 10209000);
    auto related_fill2 = user2_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill2.fee_rate_e8(), 110000);

    // 收user1 buy单result 验证使用coin级别的rule费率
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10152000);
    auto related_fill1 = user1_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill1.fee_rate_e8(), 32000);
  }

  // revoke user费率
  {
    auto coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    (void)mkfr_value;
    std::string mkfr_desc = "new_maker_fee_rate_desc";
    auto liquidity_ind = ELastLiquidityInd::AddedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_4(coin, uid18, liquidity_ind);
    revoke_req_build_4.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(revoke_req_build_4.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");

    liquidity_ind = ELastLiquidityInd::RemovedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_5(coin, uid18, liquidity_ind);
    revoke_req_build_5.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    resp = user1.process(revoke_req_build_5.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto resp_f_setting = resp->user_setting().future_user_setting_dto();
    ASSERT_EQ(resp_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(resp_f_setting.taker_fee_rate_e8(), 0);
    ASSERT_STREQ(resp_f_setting.taker_fee_rate_desc().c_str(), "");
    ASSERT_STREQ(resp_f_setting.maker_fee_rate_desc().c_str(), "");
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), 0);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), "");
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");
  }
}

// 3. 设置了user维度个性化费率，同时也能匹配到user维度规则费率
// b.
// 验证在无法匹配到任何symbol和coin规则时，个性化费率低于sc.default_tkr_feerate，结算使用了user维度个性化费率，预占使用了sc.default_tkr_feerate
TEST_F(TestCustomerFeeRate, SetAndRevokeCustomerFeeRateRuleForUser2) {
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":3,"rule_priority":1,"region_rule":0,"rule_config":{"config":[{"user_rule":"vip1","taker":"0.0004","maker":"0.0003"}]}}]})";

  InitFeeRate(FeeRateConfig);
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, "");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, "");

    auto resp2 = user2.process(deposit_build.Build());
    auto result2 = te->PopResult();
  }

  // user1 case1: 设置coin费率成功
  // {
  //   biz::coin_t coin = ECoin::USDT;
  //   biz::fee_rate_e8_t tkfr_value = 30000;
  //   std::string tkfr_desc = "new_taker_fee_rate_desc";
  //   biz::fee_rate_e8_t mkfr_value = 15000;
  //   std::string mkfr_desc = "new_maker_fee_rate_desc";

  //   FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
  //   auto resp = user1.process(set_req_build.Build());
  //   ASSERT_NE(resp, nullptr);
  //   auto resp_c_setting = resp->user_setting().future_per_coin_setting().at(coin);
  //   ASSERT_EQ(resp_c_setting.maker_fee_rate_e8(), mkfr_value);
  //   ASSERT_EQ(resp_c_setting.taker_fee_rate_e8(), tkfr_value);
  //   ASSERT_STREQ(resp_c_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  //   ASSERT_STREQ(resp_c_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  //   ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  //   auto result = te->PopResult();
  //   ASSERT_NE(result.m_msg.get(), nullptr);

  //   auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  //   ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
  //   ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
  //   ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  //   ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  //   EXPECT_EQ(ret_setting.setting_version(), 2);
  // }

  // 设置user1 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    auto resp_f_setting = resp->user_setting().future_user_setting_dto();
    ASSERT_EQ(resp_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(resp_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(resp_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(resp_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // 设置user规则
  {
    std::string user_rule = "vip2";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    set_fee_rate_user_rule.msg.mutable_set_fee_rate_user_rule_req()->set_for_user(true);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_f_setting = u1_resp->user_setting().future_user_setting_dto();
    EXPECT_EQ(resp_f_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_f_setting = u1_result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    EXPECT_EQ(ret_f_setting.fee_rate_user_rule(), user_rule.data());
  }

  // 设置coin规则
  {
    std::string user_rule = "vip1";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_coin_setting = u1_resp->user_setting().future_per_coin_setting().at(ECoin::USDT);
    EXPECT_EQ(resp_coin_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_coin_setting =
        u1_result.m_msg->asset_margin_result().per_user_setting_data().future_per_coin_setting().at(ECoin::USDT);
    EXPECT_EQ(ret_coin_setting.fee_rate_user_rule(), user_rule.data());
  }

  // user1  单仓买
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
  }

  // user2 case1: 设置coin费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user2 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // user2  单仓卖
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid28, coin);
    auto pz_buy_order_resp = user2.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);
    auto user2_order_check = user2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user2_order_check.m_msg, nullptr);
    user2_order_check.CheckQty(user2.m_uid, qty);
    ASSERT_EQ(user2_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user2_order_check.m_msg->side(), ESide::Sell);

    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user2_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user2_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill2 = user2_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill2.fee_rate_e8(), 30000);

    // 收user1 buy单result, 使用user级个性化费率
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill1 = user1_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill1.fee_rate_e8(), 20000);
  }
  // revoke user费率
  {
    auto coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    (void)mkfr_value;
    std::string mkfr_desc = "new_maker_fee_rate_desc";
    auto liquidity_ind = ELastLiquidityInd::AddedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_4(coin, uid18, liquidity_ind);
    revoke_req_build_4.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(revoke_req_build_4.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");

    liquidity_ind = ELastLiquidityInd::RemovedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_5(coin, uid18, liquidity_ind);
    revoke_req_build_5.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    resp = user1.process(revoke_req_build_5.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto resp_f_setting = resp->user_setting().future_user_setting_dto();
    ASSERT_EQ(resp_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(resp_f_setting.taker_fee_rate_e8(), 0);
    ASSERT_STREQ(resp_f_setting.taker_fee_rate_desc().c_str(), "");
    ASSERT_STREQ(resp_f_setting.maker_fee_rate_desc().c_str(), "");
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), 0);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), "");
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");
  }
}

// 3. 设置了user维度个性化费率，同时也能匹配到user维度规则费率
// c. 验证在无法匹配到任何symbol和coin规则时，个性化费率高于sc.default_tkr_feerate，预占和结算都使用了user维度个性化费率
TEST_F(TestCustomerFeeRate, SetAndRevokeCustomerFeeRateRuleForUser3) {
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":3,"rule_priority":1,"region_rule":0,"rule_config":{"config":[{"user_rule":"vip1","taker":"0.0004","maker":"0.0003"}]}}]})";

  InitFeeRate(FeeRateConfig);
  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "300000");
  config_center->SetStringVar("max_maker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "300000");
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, "");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, "");

    auto resp2 = user2.process(deposit_build.Build());
    auto result2 = te->PopResult();
  }

  // // user1 case1: 设置coin费率成功
  // {
  //   biz::coin_t coin = ECoin::USDT;
  //   biz::fee_rate_e8_t tkfr_value = 60000;
  //   std::string tkfr_desc = "new_taker_fee_rate_desc";
  //   biz::fee_rate_e8_t mkfr_value = 50000;
  //   std::string mkfr_desc = "new_maker_fee_rate_desc";

  //   FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc, 70000);
  //   auto resp = user1.process(set_req_build.Build());
  //   ASSERT_NE(resp, nullptr);
  //   ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  //   auto result = te->PopResult();
  //   ASSERT_NE(result.m_msg.get(), nullptr);

  //   auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  //   ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
  //   ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
  //   ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  //   ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  //   EXPECT_EQ(ret_setting.setting_version(), 2);
  // }

  // 设置user1 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 70000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 70000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";
    biz::fee_rate_e8_t default_taker_value = 70000;

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc,
                                                  default_taker_value);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_EQ(ret_f_setting.default_taker_fee_rate_e8(), default_taker_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // user1 设置user规则
  {
    std::string user_rule = "vip1";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    set_fee_rate_user_rule.msg.mutable_set_fee_rate_user_rule_req()->set_for_user(true);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_f_setting = u1_resp->user_setting().future_user_setting_dto();
    EXPECT_EQ(resp_f_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_f_setting = u1_result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    EXPECT_EQ(ret_f_setting.fee_rate_user_rule(), user_rule.data());
  }

  // user1  单仓买
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10133000);
  }

  // user2 case1: 设置coin费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user2 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // user2  单仓卖
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid28, coin);
    auto pz_buy_order_resp = user2.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);
    auto user2_order_check = user2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user2_order_check.m_msg, nullptr);
    user2_order_check.CheckQty(user2.m_uid, qty);
    ASSERT_EQ(user2_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user2_order_check.m_msg->side(), ESide::Sell);

    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user2_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user2_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill2 = user2_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill2.fee_rate_e8(), 30000);

    // 收user1 buy单result, 使用user个性化费率
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10133000);
    auto related_fill1 = user1_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill1.fee_rate_e8(), 70000);
  }

  {
    auto coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 70000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 70000;
    (void)mkfr_value;
    std::string mkfr_desc = "new_maker_fee_rate_desc";
    auto liquidity_ind = ELastLiquidityInd::AddedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_4(coin, uid18, liquidity_ind);
    revoke_req_build_4.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(revoke_req_build_4.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");

    liquidity_ind = ELastLiquidityInd::RemovedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_5(coin, uid18, liquidity_ind);
    revoke_req_build_5.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    resp = user1.process(revoke_req_build_5.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), 0);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), "");
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");
  }
}

// 3. 设置了user维度个性化费率，同时也能匹配到user维度规则费率
// d. 移除user维度个性化费率，验证在无法匹配到任何symbol和coin规则时，预占和结算都使用了user维度规则费率
TEST_F(TestCustomerFeeRate, SetAndRevokeCustomerFeeRateRuleForUser4) {
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":3,"rule_priority":1,"region_rule":0,"rule_config":{"config":[{"user_rule":"vip1","taker":"0.0008","maker":"0.0008"}]}}]})";

  InitFeeRate(FeeRateConfig);
  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "300000");
  config_center->SetStringVar("max_maker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "300000");
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, "");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, "");

    auto resp2 = user2.process(deposit_build.Build());
    auto result2 = te->PopResult();
  }

  // // user1 case1: 设置coin费率成功
  // {
  //   biz::coin_t coin = ECoin::USDT;
  //   biz::fee_rate_e8_t tkfr_value = 60000;
  //   std::string tkfr_desc = "new_taker_fee_rate_desc";
  //   biz::fee_rate_e8_t mkfr_value = 50000;
  //   std::string mkfr_desc = "new_maker_fee_rate_desc";

  //   FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc, 70000);
  //   auto resp = user1.process(set_req_build.Build());
  //   ASSERT_NE(resp, nullptr);
  //   ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  //   auto result = te->PopResult();
  //   ASSERT_NE(result.m_msg.get(), nullptr);

  //   auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  //   ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
  //   ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
  //   ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  //   ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  //   EXPECT_EQ(ret_setting.setting_version(), 2);
  // }

  // 设置user1 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 40000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";
    // biz::fee_rate_e8_t default_taker_value = 70000;

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    // ASSERT_EQ(ret_f_setting.default_taker_fee_rate_e8(), default_taker_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // 移除user个性化费率
  {
    auto coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 40000;
    (void)mkfr_value;
    std::string mkfr_desc = "new_maker_fee_rate_desc";
    auto liquidity_ind = ELastLiquidityInd::AddedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_4(coin, uid18, liquidity_ind);
    revoke_req_build_4.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(revoke_req_build_4.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");

    liquidity_ind = ELastLiquidityInd::RemovedLiquidity;
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_5(coin, uid18, liquidity_ind);
    revoke_req_build_5.msg.mutable_revoke_customer_fee_rate()->set_for_user(true);
    resp = user1.process(revoke_req_build_5.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();

    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), 0);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), 0);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), "");
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), "");
  }

  // 设置user规则
  {
    std::string user_rule = "vip1";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    set_fee_rate_user_rule.msg.mutable_set_fee_rate_user_rule_req()->set_for_user(true);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_f_setting = u1_resp->user_setting().future_user_setting_dto();
    EXPECT_EQ(resp_f_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_f_setting = u1_result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    EXPECT_EQ(ret_f_setting.fee_rate_user_rule(), user_rule.data());
  }

  // user1  单仓买
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10152000);
  }

  // user2 case1: 设置coin费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user2 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // user2  单仓卖
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid28, coin);
    auto pz_buy_order_resp = user2.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);
    auto user2_order_check = user2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user2_order_check.m_msg, nullptr);
    user2_order_check.CheckQty(user2.m_uid, qty);
    ASSERT_EQ(user2_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user2_order_check.m_msg->side(), ESide::Sell);

    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user2_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user2_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill2 = user2_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill2.fee_rate_e8(), 30000);

    // 收user1 buy单result，使用user规则费率
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10152000);
    auto related_fill1 = user1_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill1.fee_rate_e8(), 80000);
  }
}

// 4. 设置了coin维度等级标签，同时也设置了user维度等级标签
// a. 验证symbol维度、tag维度、coin维度规则费率，使用coin维度等级标签，user维度规则费率，使用user维度等级标签
TEST_F(TestCustomerFeeRate, SetAndRevokeCustomerFeeRateRuleForUser_UserRule1) {
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":2,"rule_priority":5,"region_rule":1,"rule_config":{"coins":["USDT","BTC"],"config":[{"user_rule":"vip1","taker":"0.0009","maker":"0.00045"},{"user_rule":"vip2","taker":"0.0008","maker":"0.0004"}]}},{"rule_type":1,"rule_priority":5,"region_rule":2,"rule_config":{"symbols":["BTCUSDT"],"config":[{"user_rule":"vip3","taker":"0.0018","maker":"0.0009"},{"user_rule":"vip4","taker":"0.0016","maker":"0.0008"}]}},{"rule_type":3,"rule_priority":5,"region_rule":3,"rule_config":{"config":[{"user_rule":"vip5","taker":"0.0027","maker":"0.00135"},{"user_rule":"vip6","taker":"0.0024","maker":"0.00012"}]}}]})";
  InitFeeRate(FeeRateConfig);
  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "300000");
  config_center->SetStringVar("max_maker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "300000");
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, "");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, "");

    auto resp2 = user2.process(deposit_build.Build());
    auto result2 = te->PopResult();
  }

  // // user1 case1: 设置coin费率成功
  // {
  //   biz::coin_t coin = ECoin::USDT;
  //   biz::fee_rate_e8_t tkfr_value = 60000;
  //   std::string tkfr_desc = "new_taker_fee_rate_desc";
  //   biz::fee_rate_e8_t mkfr_value = 50000;
  //   std::string mkfr_desc = "new_maker_fee_rate_desc";

  //   FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc, 70000);
  //   auto resp = user1.process(set_req_build.Build());
  //   ASSERT_NE(resp, nullptr);
  //   ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  //   auto result = te->PopResult();
  //   ASSERT_NE(result.m_msg.get(), nullptr);

  //   auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  //   ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
  //   ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
  //   ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  //   ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  //   EXPECT_EQ(ret_setting.setting_version(), 2);
  // }

  // 设置user规则，设置user标签
  {
    std::string user_rule = "vip5";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    set_fee_rate_user_rule.msg.mutable_set_fee_rate_user_rule_req()->set_for_user(true);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_f_setting = u1_resp->user_setting().future_user_setting_dto();
    EXPECT_EQ(resp_f_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_f_setting = u1_result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    EXPECT_EQ(ret_f_setting.fee_rate_user_rule(), user_rule.data());

    FutureUpdateVerifyUserInfoBuilder update_verify_user_info(uid18, ECoin::USDT, 3);
    update_verify_user_info.msg.mutable_update_verify_user_info_req()->set_for_user(true);
    u1_resp = user1.process(update_verify_user_info.Build());
    ASSERT_EQ(u1_resp->ret_code(), 0);
    u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
  }

  // 设置coin规则，设置user标签
  {
    std::string user_rule = "vip3";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_c_setting = u1_resp->user_setting().future_per_coin_setting().at(ECoin::USDT);
    EXPECT_EQ(resp_c_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_c_setting =
        u1_result.m_msg->asset_margin_result().per_user_setting_data().future_per_coin_setting().at(ECoin::USDT);
    EXPECT_EQ(ret_c_setting.fee_rate_user_rule(), user_rule.data());

    FutureUpdateVerifyUserInfoBuilder update_verify_user_info(uid18, ECoin::USDT, 2);
    update_verify_user_info.msg.mutable_update_verify_user_info_req()->set_for_user(true);
    u1_resp = user1.process(update_verify_user_info.Build());
    ASSERT_EQ(u1_resp->ret_code(), 0);
    u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
  }

  // user1  单仓买
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
  }

  // user2 case1: 设置coin费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user2 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // user2  单仓卖
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid28, coin);
    auto pz_buy_order_resp = user2.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);
    auto user2_order_check = user2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user2_order_check.m_msg, nullptr);
    user2_order_check.CheckQty(user2.m_uid, qty);
    ASSERT_EQ(user2_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user2_order_check.m_msg->side(), ESide::Sell);

    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user2_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user2_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill2 = user2_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill2.fee_rate_e8(), 30000);

    // 收user1 buy单result 使用coin级别的规则和标签，获取得到symbol的费率
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill1 = user1_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill1.fee_rate_e8(), 10000);
  }
}

// 4. 设置了coin维度等级标签，同时也设置了user维度等级标签
// b. 移除coin维度等级标签，除了user维度之外所有规则费率无匹配上
TEST_F(TestCustomerFeeRate, SetAndRevokeCustomerFeeRateRuleForUser_UserRule2) {
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":2,"rule_priority":5,"region_rule":1,"rule_config":{"coins":["USDT","BTC"],"config":[{"user_rule":"vip1","taker":"0.0009","maker":"0.00045"},{"user_rule":"vip2","taker":"0.0008","maker":"0.0004"}]}},{"rule_type":1,"rule_priority":5,"region_rule":2,"rule_config":{"symbols":["BTCUSDT"],"config":[{"user_rule":"vip3","taker":"0.0018","maker":"0.0009"},{"user_rule":"vip4","taker":"0.0016","maker":"0.0008"}]}},{"rule_type":3,"rule_priority":5,"region_rule":3,"rule_config":{"config":[{"user_rule":"vip5","taker":"0.0027","maker":"0.00135"},{"user_rule":"vip6","taker":"0.0024","maker":"0.00012"}]}}]})";
  InitFeeRate(FeeRateConfig);
  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "300000");
  config_center->SetStringVar("max_maker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "300000");
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, "");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, "");

    auto resp2 = user2.process(deposit_build.Build());
    auto result2 = te->PopResult();
  }

  // // user1 case1: 设置coin费率成功
  // {
  //   biz::coin_t coin = ECoin::USDT;
  //   biz::fee_rate_e8_t tkfr_value = 60000;
  //   std::string tkfr_desc = "new_taker_fee_rate_desc";
  //   biz::fee_rate_e8_t mkfr_value = 50000;
  //   std::string mkfr_desc = "new_maker_fee_rate_desc";

  //   FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc, 70000);
  //   auto resp = user1.process(set_req_build.Build());
  //   ASSERT_NE(resp, nullptr);
  //   ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  //   auto result = te->PopResult();
  //   ASSERT_NE(result.m_msg.get(), nullptr);

  //   auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  //   ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
  //   ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
  //   ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  //   ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  //   EXPECT_EQ(ret_setting.setting_version(), 2);
  // }

  // 设置user规则，设置user标签
  {
    std::string user_rule = "vip5";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    set_fee_rate_user_rule.msg.mutable_set_fee_rate_user_rule_req()->set_for_user(true);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_f_setting = u1_resp->user_setting().future_user_setting_dto();
    EXPECT_EQ(resp_f_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_f_setting = u1_result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    EXPECT_EQ(ret_f_setting.fee_rate_user_rule(), user_rule.data());

    FutureUpdateVerifyUserInfoBuilder update_verify_user_info(uid18, ECoin::USDT, 3);
    update_verify_user_info.msg.mutable_update_verify_user_info_req()->set_for_user(true);
    u1_resp = user1.process(update_verify_user_info.Build());
    ASSERT_EQ(u1_resp->ret_code(), 0);
    u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
  }

  // 设置coin规则，设置coin标签
  {
    std::string user_rule = "vip3";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_c_setting = u1_resp->user_setting().future_per_coin_setting().at(ECoin::USDT);
    EXPECT_EQ(resp_c_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_c_setting =
        u1_result.m_msg->asset_margin_result().per_user_setting_data().future_per_coin_setting().at(ECoin::USDT);
    EXPECT_EQ(ret_c_setting.fee_rate_user_rule(), user_rule.data());

    FutureUpdateVerifyUserInfoBuilder update_verify_user_info(uid18, ECoin::USDT, 2);
    update_verify_user_info.msg.mutable_update_verify_user_info_req()->set_for_user(true);
    u1_resp = user1.process(update_verify_user_info.Build());
    ASSERT_EQ(u1_resp->ret_code(), 0);
    u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
  }

  // 把symbol维度下的vip3 user rule删掉
  {
    std::string user_rule = "";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_c_setting = u1_resp->user_setting().future_per_coin_setting().at(ECoin::USDT);
    EXPECT_EQ(resp_c_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_c_setting =
        u1_result.m_msg->asset_margin_result().per_user_setting_data().future_per_coin_setting().at(ECoin::USDT);
    EXPECT_EQ(ret_c_setting.fee_rate_user_rule(), user_rule.data());
  }

  // user1  单仓买
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
  }

  // user2 case1: 设置coin费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user2 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // user2  单仓卖
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid28, coin);
    auto pz_buy_order_resp = user2.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);
    auto user2_order_check = user2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user2_order_check.m_msg, nullptr);
    user2_order_check.CheckQty(user2.m_uid, qty);
    ASSERT_EQ(user2_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user2_order_check.m_msg->side(), ESide::Sell);

    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user2_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user2_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill2 = user2_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill2.fee_rate_e8(), 30000);

    // 收user1 buy单result，由于coin级别的规则无匹配，使用user级别规则费率
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill1 = user1_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill1.fee_rate_e8(), 10000);
  }
}

// 5. 设置coin维度区域标签，同时也设置了区域维度等级标签
// a. 验证symbol维度、tag维度、coin维度规则费率，使用coin维度区域标签，user维度规则费率，使用user维度区域标签
// (上面已经覆盖) b. 移除coin维度区域标签，除了user维度之外所有规则费率无匹配上
TEST_F(TestCustomerFeeRate, SetAndRevokeCustomerFeeRateRuleForUser_RegionId) {
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":2,"rule_priority":5,"region_rule":1,"rule_config":{"coins":["USDT","BTC"],"config":[{"user_rule":"vip1","taker":"0.0009","maker":"0.00045"},{"user_rule":"vip2","taker":"0.0008","maker":"0.0004"}]}},{"rule_type":1,"rule_priority":5,"region_rule":2,"rule_config":{"symbols":["BTCUSDT"],"config":[{"user_rule":"vip3","taker":"0.0018","maker":"0.0009"},{"user_rule":"vip4","taker":"0.0016","maker":"0.0008"}]}},{"rule_type":3,"rule_priority":5,"region_rule":3,"rule_config":{"config":[{"user_rule":"vip5","taker":"0.0027","maker":"0.00135"},{"user_rule":"vip6","taker":"0.0024","maker":"0.00012"}]}}]})";
  InitFeeRate(FeeRateConfig);
  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "300000");
  config_center->SetStringVar("max_maker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "300000");
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, "");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, "");

    auto resp2 = user2.process(deposit_build.Build());
    auto result2 = te->PopResult();
  }

  // // user1 case1: 设置coin费率成功
  // {
  //   biz::coin_t coin = ECoin::USDT;
  //   biz::fee_rate_e8_t tkfr_value = 60000;
  //   std::string tkfr_desc = "new_taker_fee_rate_desc";
  //   biz::fee_rate_e8_t mkfr_value = 50000;
  //   std::string mkfr_desc = "new_maker_fee_rate_desc";

  //   FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc, 70000);
  //   auto resp = user1.process(set_req_build.Build());
  //   ASSERT_NE(resp, nullptr);
  //   ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
  //   auto result = te->PopResult();
  //   ASSERT_NE(result.m_msg.get(), nullptr);

  //   auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
  //   ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
  //   ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
  //   ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
  //   ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  //   EXPECT_EQ(ret_setting.setting_version(), 2);
  // }

  // 设置user规则，设置user标签
  {
    std::string user_rule = "vip5";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    set_fee_rate_user_rule.msg.mutable_set_fee_rate_user_rule_req()->set_for_user(true);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_f_setting = u1_resp->user_setting().future_user_setting_dto();
    EXPECT_EQ(resp_f_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_f_setting = u1_result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    EXPECT_EQ(ret_f_setting.fee_rate_user_rule(), user_rule.data());

    FutureUpdateVerifyUserInfoBuilder update_verify_user_info(uid18, ECoin::USDT, 3);
    update_verify_user_info.msg.mutable_update_verify_user_info_req()->set_for_user(true);
    u1_resp = user1.process(update_verify_user_info.Build());
    resp_f_setting = u1_resp->user_setting().future_user_setting_dto();
    ASSERT_EQ(resp_f_setting.region_rule(), 3);
    ASSERT_EQ(u1_resp->ret_code(), 0);
    u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    ret_f_setting = u1_result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    EXPECT_EQ(ret_f_setting.region_rule(), 3);
  }

  // 设置coin规则，设置coin标签
  {
    std::string user_rule = "vip3";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_c_setting = u1_resp->user_setting().future_per_coin_setting().at(ECoin::USDT);
    EXPECT_EQ(resp_c_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_c_setting =
        u1_result.m_msg->asset_margin_result().per_user_setting_data().future_per_coin_setting().at(ECoin::USDT);
    EXPECT_EQ(ret_c_setting.fee_rate_user_rule(), user_rule.data());

    FutureUpdateVerifyUserInfoBuilder update_verify_user_info(uid18, ECoin::USDT, 2);
    update_verify_user_info.msg.mutable_update_verify_user_info_req()->set_for_user(true);
    u1_resp = user1.process(update_verify_user_info.Build());
    ASSERT_EQ(u1_resp->ret_code(), 0);
    u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
  }

  // 把symbol维度下的region id删掉
  {
    FutureUpdateVerifyUserInfoBuilder update_verify_user_info(uid18, ECoin::USDT, 0);
    update_verify_user_info.msg.mutable_update_verify_user_info_req()->set_for_user(true);
    auto u1_resp = user1.process(update_verify_user_info.Build());
    ASSERT_EQ(u1_resp->ret_code(), 0);
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
  }

  // user1  单仓买
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
  }

  // user2 case1: 设置coin费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // 设置user2 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid28, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user2.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
  }

  // user2  单仓卖
  {
    biz::coin_t coin = ECoin::USDT;
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Sell;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 4 * 1e8;
    biz::price_x_t price = 20000 * 1e4;
    ESymbol symbol = ESymbol::BTCUSDT;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid28, coin);
    auto pz_buy_order_resp = user2.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto user2_result = te->PopResult();
    ASSERT_NE(user2_result.m_msg.get(), nullptr);
    auto user2_order_check = user2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user2_order_check.m_msg, nullptr);
    user2_order_check.CheckQty(user2.m_uid, qty);
    ASSERT_EQ(user2_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user2_order_check.m_msg->side(), ESide::Sell);

    auto user2_positionCheck = user2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user2_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user2_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user2_positionCheck.m_msg->side(), ESide::Sell);
    ASSERT_EQ(user2_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user2_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill2 = user2_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill2.fee_rate_e8(), 30000);

    // 收user1 buy单result，coin维度区域标签无匹配，使用user级别区域标签下的规则费率
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::Filled);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), qty);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), ESide::Buy);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    EXPECT_EQ(user1_positionCheck.m_msg->buy_value_to_cost_e8(), 10114000);
    auto related_fill1 = user1_result.RefFutureMarginResult().m_msg->related_fills().at(0);
    EXPECT_EQ(related_fill1.fee_rate_e8(), 10000);
  }
}

TEST_F(TestCustomerFeeRate, QueryUserSetting) {
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":2,"rule_priority":5,"region_rule":1,"rule_config":{"coins":["USDT","BTC"],"config":[{"user_rule":"vip1","taker":"0.0009","maker":"0.00045"},{"user_rule":"vip2","taker":"0.0008","maker":"0.0004"}]}},{"rule_type":1,"rule_priority":5,"region_rule":2,"rule_config":{"symbols":["BTCUSDT"],"config":[{"user_rule":"vip3","taker":"0.0018","maker":"0.0009"},{"user_rule":"vip4","taker":"0.0016","maker":"0.0008"}]}},{"rule_type":3,"rule_priority":5,"region_rule":3,"rule_config":{"config":[{"user_rule":"vip5","taker":"0.0027","maker":"0.00135"},{"user_rule":"vip6","taker":"0.0024","maker":"0.00012"}]}}]})";
  InitFeeRate(FeeRateConfig);
  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "300000");
  config_center->SetStringVar("max_maker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "300000");
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, "");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, "");

    auto resp2 = user2.process(deposit_build.Build());
    auto result2 = te->PopResult();
  }

  // user1 设置USDC费率成功
  {
    biz::coin_t coin = ECoin::USDC;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // user1 设置USDT费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 3);
  }

  // 设置user1 user费率
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc, {}, true);
    set_req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto ret_f_setting = result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    ASSERT_EQ(ret_f_setting.maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_f_setting.taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_f_setting.taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_f_setting.maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_f_setting.is_final(), true);
  }

  // 设置user规则
  {
    std::string user_rule = "vip5";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    set_fee_rate_user_rule.msg.mutable_set_fee_rate_user_rule_req()->set_for_user(true);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_f_setting = u1_resp->user_setting().future_user_setting_dto();
    EXPECT_EQ(resp_f_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_f_setting = u1_result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    EXPECT_EQ(ret_f_setting.fee_rate_user_rule(), user_rule.data());

    FutureUpdateVerifyUserInfoBuilder update_verify_user_info(uid18, ECoin::USDT, 3);
    update_verify_user_info.msg.mutable_update_verify_user_info_req()->set_for_user(true);
    u1_resp = user1.process(update_verify_user_info.Build());
    resp_f_setting = u1_resp->user_setting().future_user_setting_dto();
    ASSERT_EQ(resp_f_setting.region_rule(), 3);
    ASSERT_EQ(u1_resp->ret_code(), 0);
    u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    ret_f_setting = u1_result.m_msg->asset_margin_result().per_user_setting_data().future_user_setting_dto();
    EXPECT_EQ(ret_f_setting.region_rule(), 3);
  }

  // 设置coin规则
  {
    std::string user_rule = "vip3";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid18, ECoin::USDT, user_rule);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto resp_c_setting = u1_resp->user_setting().future_per_coin_setting().at(ECoin::USDT);
    EXPECT_EQ(resp_c_setting.fee_rate_user_rule(), user_rule.data());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_c_setting =
        u1_result.m_msg->asset_margin_result().per_user_setting_data().future_per_coin_setting().at(ECoin::USDT);
    EXPECT_EQ(ret_c_setting.fee_rate_user_rule(), user_rule.data());

    FutureUpdateVerifyUserInfoBuilder update_verify_user_info(uid18, ECoin::USDT, 2);
    u1_resp = user1.process(update_verify_user_info.Build());
    ASSERT_EQ(u1_resp->ret_code(), 0);
    u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
  }

  // 设置symbol级别费率
  {
    biz::coin_t coin = ECoin::USDT;
    ESymbol symbol = ESymbol::XRPUSDT;
    biz::fee_rate_e8_t tkfr_value = 40000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 20000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";
    FutureSetSingleFeeRateBuilder set_single_fee(coin, uid18, symbol, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto u1_resp = user1.process(set_single_fee.Build());
    ASSERT_EQ(u1_resp->ret_code(), 0);
  }

  // query user setting，验证以上所有字段
  {
    FutureQueryUserSettingBuilder query_setting(ECoin::USDT, uid18);
    auto u1_resp = user1.process(query_setting.Build());
    auto resp_c_setting = u1_resp->user_setting().future_per_coin_setting().at(ECoin::USDT);
    ASSERT_EQ(resp_c_setting.fee_rate_user_rule(), "vip3");
    ASSERT_EQ(resp_c_setting.verify_user_rule_id(), 2);
    ASSERT_EQ(resp_c_setting.taker_fee_rate_e8(), 30000);
    ASSERT_EQ(resp_c_setting.maker_fee_rate_e8(), 15000);

    auto resp_dc_setting = u1_resp->user_setting().future_per_coin_setting().at(ECoin::USDC);
    ASSERT_EQ(resp_dc_setting.taker_fee_rate_e8(), 30000);
    ASSERT_EQ(resp_dc_setting.maker_fee_rate_e8(), 15000);

    auto resp_xrp_setting =
        u1_resp->user_setting().future_symbol_setting_dto().future_symbol_config_map().at(ESymbol::XRPUSDT);
    ASSERT_EQ(resp_xrp_setting.taker_fee_rate_e8(), 40000);
    ASSERT_EQ(resp_xrp_setting.maker_fee_rate_e8(), 20000);

    auto resp_f_setting = u1_resp->user_setting().future_user_setting_dto();
    ASSERT_EQ(resp_f_setting.fee_rate_user_rule(), "vip5");
    ASSERT_EQ(resp_f_setting.region_rule(), 3);
    ASSERT_EQ(resp_f_setting.taker_fee_rate_e8(), 40000);
    ASSERT_EQ(resp_f_setting.maker_fee_rate_e8(), 20000);
  }
}

TEST_F(TestCustomerFeeRate, revoke4) {
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":2,"rule_priority":5,"region_rule":1,"rule_config":{"coins":["USDT","BTC"],"config":[{"user_rule":"vip1","taker":"0.0009","maker":"0.00045"},{"user_rule":"vip2","taker":"0.0008","maker":"0.0004"}]}},{"rule_type":1,"rule_priority":5,"region_rule":2,"rule_config":{"symbols":["BTCUSDT"],"config":[{"user_rule":"vip3","taker":"0.0018","maker":"0.0009"},{"user_rule":"vip4","taker":"0.0016","maker":"0.0008"}]}},{"rule_type":3,"rule_priority":5,"region_rule":3,"rule_config":{"config":[{"user_rule":"vip5","taker":"0.0027","maker":"0.00135"},{"user_rule":"vip6","taker":"0.0024","maker":"0.00012"}]}}]})";
  InitFeeRate(FeeRateConfig);
  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("max_taker_fee_rate", "300000");
  config_center->SetStringVar("max_maker_fee_rate", "300000");
  config_center->SetStringVar("max_default_taker_fee_rate", "300000");
  biz::user_id_t uid18 = 100000;
  biz::user_id_t uid28 = 200000;
  stub user1(te, uid18);
  stub user2(te, uid28);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid18, trans_id, coin, wallet_record_type, amount, "");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";

    FutureDepositBuilder deposit_build(req_id, uid28, trans_id, coin, wallet_record_type, amount, "");

    auto resp2 = user2.process(deposit_build.Build());
    auto result2 = te->PopResult();
  }

  // user1 设置USDC费率成功
  {
    biz::coin_t coin = ECoin::USDC;
    biz::fee_rate_e8_t tkfr_value = 30000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 2);
  }

  // user1 设置USDT费率成功
  {
    biz::coin_t coin = ECoin::USDT;
    biz::fee_rate_e8_t tkfr_value = 70000;
    std::string tkfr_desc = "new_taker_fee_rate_desc";
    biz::fee_rate_e8_t mkfr_value = 15000;
    std::string mkfr_desc = "new_maker_fee_rate_desc";

    FutureSetCustomerFeeRateBuilder set_req_build(coin, uid18, tkfr_value, tkfr_desc, mkfr_value, mkfr_desc, 70000);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto ret_setting = result.m_msg->asset_margin_result().per_user_setting_data();
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_e8(), mkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_e8(), tkfr_value);
    ASSERT_EQ(ret_setting.future_per_coin_setting().at(coin).default_taker_fee_rate_e8(), 70000);
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).taker_fee_rate_desc().c_str(), tkfr_desc.c_str());
    ASSERT_STREQ(ret_setting.future_per_coin_setting().at(coin).maker_fee_rate_desc().c_str(), mkfr_desc.c_str());
    EXPECT_EQ(ret_setting.setting_version(), 3);
  }

  {
    biz::coin_t coin = ECoin::USDT;
    ELastLiquidityInd liquidity_ind = static_cast<ELastLiquidityInd>(4);
    FutureRevokeCustomerFeeRateBuilder revoke_req_build_4(coin, uid18, liquidity_ind);
    auto resp = user1.process(revoke_req_build_4.Build());
    ASSERT_NE(resp, nullptr);
    ASSERT_EQ(30137, resp->ret_code());
  }
}

TEST_F(TestCustomerFeeRate, SetCustomerFeeRate_failed_isolated_only_with_order_BTC) {
  // user1为单仓
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  auto symbol = ESymbol::BTCUSD;
  auto coin = ECoin::BTC;
  {
    // 充值、调杠杆、切双仓
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;

    // user1
    FutureDepositBuilder u1_deposit_build(req_id, user1.m_uid, trans_id, coin, wallet_record_type, "0.0057", "0");
    auto u1_resp = user1.process(u1_deposit_build.Build());
    auto u1_result = te->PopResult();

    FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, user1.m_uid, 100 * 1e2, 100 * 1e2);
    auto resp1 = user1.process(set_lv_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    // positionCheck.CheckLv(100 * 1e2, 1119400, 1120600, 619400, 620600);

    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user1.m_uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);

    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user1.m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);

  {
    // user1  单仓买
    auto order_id = te->GenUUID();
    EPositionIndex pz_index = EPositionIndex::Single;
    ESide side = ESide::Buy;
    EOrderType order_type = EOrderType::Limit;
    biz::size_x_t qty = 10000;
    biz::price_x_t price = 20000 * 1e4;

    te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user1.m_uid, coin);
    auto pz_buy_order_resp = user1.process(create_build.Build());
    ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto user1_result = te->PopResult();
    ASSERT_NE(user1_result.m_msg.get(), nullptr);
    auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(user1_order_check.m_msg, nullptr);
    user1_order_check.CheckQty(user1.m_uid, qty);
    ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
    ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);

    auto user1_positionCheck = user1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdx(0);
    ASSERT_NE(user1_positionCheck.m_msg, nullptr);
    ASSERT_EQ(user1_positionCheck.m_msg->size_x(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->side(), 0);
    ASSERT_EQ(user1_positionCheck.m_msg->position_idx(), 0);
    user1_result.m_msg->PrintDebugString();
  }
  // 2.ab为负，不够补oc
  {
    std::string tkr_desc = "";
    std::string mkr_desc = "";
    auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
        bbase::object_manager::ObjectType::kObjectConfigCenter);
    config_center->SetStringVar("max_taker_fee_rate", "300000");
    config_center->SetStringVar("max_default_taker_fee_rate", "300000");
    // 改单仓默认费率
    FutureSetCustomerFeeRateBuilder req_build(coin, uid_1, 300000, tkr_desc, 10000, mkr_desc, 300000);
    req_build.msg.mutable_set_customer_fee_rate()->set_for_user(true);
    auto resp = user1.process(req_build.Build());
    ASSERT_EQ(error::kErrorCodeFeeRateTooHigh, resp->ret_code());
  }
}
