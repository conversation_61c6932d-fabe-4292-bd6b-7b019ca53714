#include "customer_fee_rate_test.hpp"  // NOLINT
#include "lib/msg_builder.hpp"
#include "src/biz_worker/service/trade/futures/modules/feeratebiz/fee_rate_biz.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"
class TestFeeRateRule : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestSuite() {}
  static void TearDownTestSuite() {}
  static void InitFeeRate(const char* FeeRateConfig);
  static void UserDeposit(stub& user1, stub& user2);
  static std::shared_ptr<tmock::CTradeAppMock> te;
};

void TestFeeRateRule::UserDeposit(stub& user1, stub& user2) {
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  auto coin = ECoin::USDT;

  FutureDepositBuilder u1_deposit_build(req_id, user1.m_uid, trans_id, coin, wallet_record_type, "15000", "0");
  auto u1_resp = user1.process(u1_deposit_build.Build());
  ASSERT_EQ(u1_resp->ret_code(), 0);
  auto u1_result = te->PopResult();

  FutureDepositBuilder u2_deposit_build(req_id, user2.m_uid, trans_id, coin, wallet_record_type, "150", "0");
  auto u2_resp = user2.process(u2_deposit_build.Build());
  ASSERT_EQ(u2_resp->ret_code(), 0);
  auto u2_result = te->PopResult();
}

void TestFeeRateRule::InitFeeRate(const char* FeeRateConfig) {
  auto config_center = bbase::object_manager::ObjectManager::GetObject<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  auto nacos_client = std::dynamic_pointer_cast<bbase::nacos_client::NacosClientImpl>(config_center);
  nacos_client->SetContent(config::ConstConfig::FEERATE_DATA_ID, config::ConstConfig::FEERATE_GROUP, FeeRateConfig);

  auto ret = application::GlobalVarManager::Instance().Init();
  ASSERT_EQ(ret, 0);
}

TEST_F(TestFeeRateRule, check_config_sort) {
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":1,"rule_priority":5,"region_rule":0,"rule_config":{"symbols":["BTCUSDT"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0011","maker":"0.0004"},{"user_rule":"vip1","taker":"0.001","maker":"0.00036"},{"user_rule":"vip2","taker":"0.0008","maker":"0.00032"}]}},{"rule_type":4,"rule_priority":5,"region_rule":0,"rule_config":{"tag_ids":[52],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.00165","maker":"0.0006"},{"user_rule":"vip1","taker":"0.0015","maker":"0.00064"},{"user_rule":"vip2","taker":"0.0012","maker":"0.00048"}]}}]})";
  InitFeeRate(FeeRateConfig);
  const auto& fee_rate_config = application::GlobalVarManager::Instance().future_feerate_config();
  for (auto& itor_priority_fee_rate : fee_rate_config->data[0]->region_fee_rate_conf["vip1"]->priority_fee_rate_conf) {
    ASSERT_EQ(itor_priority_fee_rate->rule_type, 1);
    break;
  }

  FeeRateConfig =
      R"({"data":[{"rule_type":4,"rule_priority":5,"region_rule":0,"rule_config":{"tag_ids":[52],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.00165","maker":"0.0006"},{"user_rule":"vip1","taker":"0.0015","maker":"0.00064"},{"user_rule":"vip2","taker":"0.0012","maker":"0.00048"}]}},{"rule_type":1,"rule_priority":5,"region_rule":0,"rule_config":{"symbols":["BTCUSDT"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0011","maker":"0.0004"},{"user_rule":"vip1","taker":"0.001","maker":"0.00036"},{"user_rule":"vip2","taker":"0.0008","maker":"0.00032"}]}}]})";
  InitFeeRate(FeeRateConfig);
  const auto& fee_rate_config2 = application::GlobalVarManager::Instance().future_feerate_config();
  for (auto& itor_priority_fee_rate2 :
       fee_rate_config2->data[0]->region_fee_rate_conf["vip1"]->priority_fee_rate_conf) {
    ASSERT_EQ(itor_priority_fee_rate2->rule_type, 1);
    break;
  }
}

/*
以下验证均正对主站
1. 验证vip0 规则费率，预占
   主站vip0，coin维度配置费率为w55，低于默认费率w6，用w6预占
   主站vip0，symbol维度配置费率为w11，高于默认费率w6，用w11预占
2. 验证vip0 规则费率，结算
   主站vip0，symbol维度配置费率 taker用w11结算，maker用w4结算
   主站vip0，coin维度配置费率 taker用w55结算，maker用w2结算
3. 什么也没改变，没有dump
4. 验证vip+ 规则费率，预占
   vip1 coin维度费率为w5，低于默认费率w6，用w6预占;symbol维度 btcusdt费率为w10，预占使用w10
   vip2 coin维度费率为w4，低于默认费率w6，用w6预占;symbol维度 btcusdt费率为w8，预占使用w8
5. 验证vip+ 规则费率，结算
   vip1，coin维度费率 taker w5结算 maker w1.8结算;symbol维度费率 taker w10结算 maker w3.6结算
   vip2，coin维度费率 taker w4结算 maker w1.6结算；symbol维度费率 taker w8结算 maker w3.2结算
6. 验证试算失败
*/
TEST_F(TestFeeRateRule, set_fee_rate_user_rule) {
  // 分为主站和cis两个站点费率
  // coin费率最低，symbol为coin的2倍，tag为coin的3倍
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":2,"rule_priority":5,"region_rule":0,"rule_config":{"coins":["USDT","BTC"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.00055","maker":"0.0002"},{"user_rule":"vip1","taker":"0.0005","maker":"0.00018"},{"user_rule":"vip2","taker":"0.0004","maker":"0.00016"}]}},{"rule_type":1,"rule_priority":5,"region_rule":0,"rule_config":{"symbols":["BTCUSDT"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0011","maker":"0.0004"},{"user_rule":"vip1","taker":"0.001","maker":"0.00036"},{"user_rule":"vip2","taker":"0.0008","maker":"0.00032"}]}},{"rule_type":4,"rule_priority":5,"region_rule":0,"rule_config":{"tag_ids":[52],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.00165","maker":"0.0006"},{"user_rule":"vip1","taker":"0.0015","maker":"0.00064"},{"user_rule":"vip2","taker":"0.0012","maker":"0.00048"}]}},{"rule_type":2,"rule_priority":5,"region_rule":1,"rule_config":{"coins":["USDT","BTC"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.001","maker":"0.0005"},{"user_rule":"vip1","taker":"0.0009","maker":"0.00045"},{"user_rule":"vip2","taker":"0.0008","maker":"0.0004"}]}},{"rule_type":1,"rule_priority":5,"region_rule":1,"rule_config":{"symbols":["XXXUSDT"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.002","maker":"0.001"},{"user_rule":"vip1","taker":"0.0018","maker":"0.0009"},{"user_rule":"vip2","taker":"0.0016","maker":"0.0008"}]}},{"rule_type":4,"rule_priority":5,"region_rule":1,"rule_config":{"tag_ids":[52],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.003","maker":"0.0015"},{"user_rule":"vip1","taker":"0.0027","maker":"0.00135"},{"user_rule":"vip2","taker":"0.0024","maker":"0.00012"}]}}]})";
  InitFeeRate(FeeRateConfig);

  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  biz::user_id_t const uid_2 = 658002;
  stub user2(te, uid_2);

  UserDeposit(user1, user2);
  /// #1. 验证vip0 规则费率，预占
  {
    // 主站vip0，coin维度配置费率为w55，低于默认费率w6，用w6预占
    FutureOneOfSetLeverageBuilder set_lv_build(ESymbol::XRPUSDT, ECoin::USDT, user1.m_uid, 20 * 1e2, 20 * 1e2);
    auto resp1 = user1.process(set_lv_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(20 * 1e2, 5117000, 5123000, 617000, 623000);

    // 主站vip0，coin维度配置费率为w55，低于默认费率w6，用w6预占
    FutureOneOfSetLeverageBuilder set_lv_build2(ESymbol::XRPUSDT, ECoin::USDT, user2.m_uid, 20 * 1e2, 20 * 1e2);
    resp1 = user2.process(set_lv_build2.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
    result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(20 * 1e2, 5117000, 5123000, 617000, 623000);
  }
  {
    // 主站vip0，symbol维度配置费率为w11，高于默认费率w6，用w11预占
    FutureOneOfSetLeverageBuilder set_lv_build(ESymbol::BTCUSDT, ECoin::USDT, user1.m_uid, 20 * 1e2, 20 * 1e2);
    auto resp1 = user1.process(set_lv_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(20 * 1e2, 5214500, 5225500, 714500, 725500);

    FutureOneOfSetLeverageBuilder set_lv_build2(ESymbol::BTCUSDT, ECoin::USDT, user2.m_uid, 20 * 1e2, 20 * 1e2);
    resp1 = user2.process(set_lv_build2.Build());
    ASSERT_EQ(resp1->ret_code(), 0);
    result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(20 * 1e2, 5214500, 5225500, 714500, 725500);
  }

  // #2. 验证vip0 规则费率，结算
  {
    // 主站vip0，symbol维度配置费率 taker用w11结算，maker用w4结算
    {
      // user1买
      auto order_id = te->GenUUID();
      EPositionIndex pz_index = EPositionIndex::Single;
      ESide side = ESide::Buy;
      EOrderType order_type = EOrderType::Limit;
      biz::size_x_t qty = 0.1 * 1e8;
      biz::price_x_t price = 20000 * 1e4;

      te->AddMarkPrice(static_cast<ESymbol>(ESymbol::BTCUSDT), 20000, 20000, 20000);
      FutureOneOfCreateOrderBuilder create_build(order_id, ESymbol::BTCUSDT, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user1.m_uid, ECoin::USDT);
      auto pz_buy_order_resp = user1.process(create_build.Build());
      ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

      // 收user1result
      auto user1_result = te->PopResult();
      ASSERT_NE(user1_result.m_msg.get(), nullptr);
      auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
      ASSERT_NE(user1_order_check.m_msg, nullptr);
      ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
      ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);
    }

    {
      // user2卖
      auto order_id = te->GenUUID();
      EPositionIndex pz_index = EPositionIndex::Single;
      ESide side = ESide::Sell;
      EOrderType order_type = EOrderType::Limit;
      biz::size_x_t qty = 0.1 * 1e8;
      biz::price_x_t price = 20000 * 1e4;

      te->AddMarkPrice(static_cast<ESymbol>(ESymbol::BTCUSDT), 20000, 20000, 20000);
      FutureOneOfCreateOrderBuilder create_build(order_id, ESymbol::BTCUSDT, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user2.m_uid, ECoin::USDT);
      auto pz_buy_order_resp = user2.process(create_build.Build());
      ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

      // 收user2 sell单result
      auto user2_result = te->PopResult();
      ASSERT_NE(user2_result.m_msg.get(), nullptr);
      auto user2_trade_check = user2_result.RefFutureMarginResult().RefRelatedFillByIndex(0);
      ASSERT_NE(user2_trade_check.m_msg, nullptr);
      // taker symbol维度规则费率为w11
      ASSERT_EQ(user2_trade_check.m_msg->fee_rate_e8(), 110000);
      ASSERT_EQ(user2_trade_check.m_msg->cross_status(), ECrossStatus::TakerFill);
      ASSERT_EQ(user2_trade_check.m_msg->side(), ESide::Sell);

      // 收user1 buy单result
      auto user1_result = te->PopResult();
      ASSERT_NE(user1_result.m_msg.get(), nullptr);

      auto user1_trade_check = user1_result.RefFutureMarginResult().RefRelatedFillByIndex(0);
      ASSERT_NE(user1_trade_check.m_msg, nullptr);
      // maker symbol维度规则费率为w4
      ASSERT_EQ(user1_trade_check.m_msg->fee_rate_e8(), 40000);
      ASSERT_EQ(user1_trade_check.m_msg->cross_status(), ECrossStatus::MakerFill);
      ASSERT_EQ(user1_trade_check.m_msg->side(), ESide::Buy);
    }

    //  主站vip0，coin维度配置费率 taker用w55结算，maker用w2结算
    {
      // user1  买
      auto order_id = te->GenUUID();
      EPositionIndex pz_index = EPositionIndex::Single;
      ESide side = ESide::Buy;
      EOrderType order_type = EOrderType::Limit;
      biz::size_x_t qty = 0.1 * 1e8;
      biz::price_x_t price = 1000 * 1e4;

      te->AddMarkPrice(static_cast<ESymbol>(ESymbol::XRPUSDT), 1000, 1000, 1000);
      FutureOneOfCreateOrderBuilder create_build(order_id, ESymbol::XRPUSDT, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user1.m_uid, ECoin::USDT);
      auto pz_buy_order_resp = user1.process(create_build.Build());
      ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

      // 收user1 buy单result
      auto user1_result = te->PopResult();
      ASSERT_NE(user1_result.m_msg.get(), nullptr);
      auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
      ASSERT_NE(user1_order_check.m_msg, nullptr);
      ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
      ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);
    }

    {
      // user2  卖
      auto order_id = te->GenUUID();
      EPositionIndex pz_index = EPositionIndex::Single;
      ESide side = ESide::Sell;
      EOrderType order_type = EOrderType::Limit;
      biz::size_x_t qty = 0.1 * 1e8;
      biz::price_x_t price = 1000 * 1e4;

      te->AddMarkPrice(static_cast<ESymbol>(ESymbol::XRPUSDT), 1000, 1000, 1000);
      FutureOneOfCreateOrderBuilder create_build(order_id, ESymbol::XRPUSDT, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user2.m_uid, ECoin::USDT);
      auto pz_buy_order_resp = user2.process(create_build.Build());
      ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

      // 收user2 sell单result
      auto user2_result = te->PopResult();
      ASSERT_NE(user2_result.m_msg.get(), nullptr);
      auto user2_trade_check = user2_result.RefFutureMarginResult().RefRelatedFillByIndex(0);
      ASSERT_NE(user2_trade_check.m_msg, nullptr);
      // taker coin维度规则费率为w55
      ASSERT_EQ(user2_trade_check.m_msg->fee_rate_e8(), 55000);
      ASSERT_EQ(user2_trade_check.m_msg->cross_status(), ECrossStatus::TakerFill);
      ASSERT_EQ(user2_trade_check.m_msg->side(), ESide::Sell);

      // 收user1 buy单result
      auto user1_result = te->PopResult();
      ASSERT_NE(user1_result.m_msg.get(), nullptr);

      auto user1_trade_check = user1_result.RefFutureMarginResult().RefRelatedFillByIndex(0);
      ASSERT_NE(user1_trade_check.m_msg, nullptr);
      // maker coin维度规则费率为w2
      ASSERT_EQ(user1_trade_check.m_msg->fee_rate_e8(), 20000);
      ASSERT_EQ(user1_trade_check.m_msg->cross_status(), ECrossStatus::MakerFill);
      ASSERT_EQ(user1_trade_check.m_msg->side(), ESide::Buy);
    }
  }

  /// #3. 什么也没改变，没有dump
  {
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid_1, ECoin::USDT, "");
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), 0);
    auto u1_result = te->PopResult();
    ASSERT_EQ(u1_result.m_msg.get(), nullptr);
  }

  /// #4. 验证vip+ 规则费率，预占
  {
    // vip1 coin维度费率为w5，低于默认费率w6，用w6预占;symbol维度 btcusdt费率为w10，预占使用w10
    std::string user_rule = "vip1";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid_1, ECoin::USDT, user_rule);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), 0);
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_setting = u1_result.m_msg->asset_margin_result().per_user_setting_data();
    auto itor = ret_setting.future_per_coin_setting().find(ECoin::USDT);
    ASSERT_TRUE(itor != ret_setting.future_per_coin_setting().end());
    ASSERT_EQ(itor->second.fee_rate_user_rule(), user_rule);
    auto other_positionCheck =
        u1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, ESymbol::XRPUSDT);
    ASSERT_NE(other_positionCheck.m_msg, nullptr);
    // vip1 coin维度费率为w5，低于默认费率w6，用w6预占
    other_positionCheck.CheckLv(20 * 1e2, 5117000, 5123000, 617000, 623000);
    auto btcusdt_positionCheck =
        u1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, ESymbol::BTCUSDT);
    ASSERT_NE(btcusdt_positionCheck.m_msg, nullptr);
    // vip1 symbol维度 btcusdt费率为w10，预占使用w10
    btcusdt_positionCheck.CheckLv(20 * 1e2, 5214500, 5225500, 714500, 725500);

    // vip2 coin维度费率为w4，低于默认费率w6，用w6预占;symbol维度 btcusdt费率为w8，预占使用w8
    std::string user_rule2 = "vip2";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule2(uid_2, ECoin::USDT, user_rule2);
    auto u2_resp = user2.process(set_fee_rate_user_rule2.Build());
    ASSERT_EQ(u2_resp->ret_code(), 0);
    auto u2_result = te->PopResult();
    ASSERT_NE(u2_result.m_msg.get(), nullptr);
    ret_setting = u2_result.m_msg->asset_margin_result().per_user_setting_data();
    itor = ret_setting.future_per_coin_setting().find(ECoin::USDT);
    ASSERT_TRUE(itor != ret_setting.future_per_coin_setting().end());
    ASSERT_EQ(itor->second.fee_rate_user_rule(), user_rule2);
    other_positionCheck =
        u2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, ESymbol::XRPUSDT);
    ASSERT_NE(other_positionCheck.m_msg, nullptr);
    // vip2 coin维度费率为w4，低于默认费率w6，用w6预占
    other_positionCheck.CheckLv(20 * 1e2, 5117000, 5123000, 617000, 623000);
    btcusdt_positionCheck =
        u2_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, ESymbol::BTCUSDT);
    ASSERT_NE(btcusdt_positionCheck.m_msg, nullptr);
    // vip2 symbol维度 btcusdt费率为w8，预占使用w8
    btcusdt_positionCheck.CheckLv(20 * 1e2, 5214500, 5225500, 714500, 725500);
  }

  /// #5. 验证vip+ 规则费率，结算
  {
    // 主站vip1，symbol维度费率 taker w10结算 maker w3.6结算
    // 主站vip2，symbol维度费率 taker w8结算 maker w3.2结算
    {
      // user1
      auto order_id = te->GenUUID();
      EPositionIndex pz_index = EPositionIndex::Single;
      ESide side = ESide::Buy;
      EOrderType order_type = EOrderType::Limit;
      biz::size_x_t qty = 0.1 * 1e8;
      biz::price_x_t price = 20000 * 1e4;

      te->AddMarkPrice(static_cast<ESymbol>(ESymbol::BTCUSDT), 20000, 20000, 20000);
      FutureOneOfCreateOrderBuilder create_build(order_id, ESymbol::BTCUSDT, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user1.m_uid, ECoin::USDT);
      auto pz_buy_order_resp = user1.process(create_build.Build());
      ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

      // 收user1result
      auto user1_result = te->PopResult();
      ASSERT_NE(user1_result.m_msg.get(), nullptr);
      auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
      ASSERT_NE(user1_order_check.m_msg, nullptr);
      ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
      ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);
    }

    {
      // user2
      auto order_id = te->GenUUID();
      EPositionIndex pz_index = EPositionIndex::Single;
      ESide side = ESide::Sell;
      EOrderType order_type = EOrderType::Limit;
      biz::size_x_t qty = 0.01 * 1e8;
      biz::price_x_t price = 20000 * 1e4;

      te->AddMarkPrice(static_cast<ESymbol>(ESymbol::BTCUSDT), 20000, 20000, 20000);
      FutureOneOfCreateOrderBuilder create_build(order_id, ESymbol::BTCUSDT, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user2.m_uid, ECoin::USDT);
      auto pz_buy_order_resp = user2.process(create_build.Build());
      ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

      // 收user2result
      auto user2_result = te->PopResult();
      ASSERT_NE(user2_result.m_msg.get(), nullptr);
      auto user2_trade_check = user2_result.RefFutureMarginResult().RefRelatedFillByIndex(0);
      ASSERT_NE(user2_trade_check.m_msg, nullptr);
      // vip2 taker symbol维度规则费率为w8
      ASSERT_EQ(user2_trade_check.m_msg->fee_rate_e8(), 110000);
      ASSERT_EQ(user2_trade_check.m_msg->cross_status(), ECrossStatus::TakerFill);
      ASSERT_EQ(user2_trade_check.m_msg->side(), ESide::Sell);

      // 收user1 buy单result
      auto user1_result = te->PopResult();
      ASSERT_NE(user1_result.m_msg.get(), nullptr);

      auto user1_trade_check = user1_result.RefFutureMarginResult().RefRelatedFillByIndex(0);
      ASSERT_NE(user1_trade_check.m_msg, nullptr);
      // vip1 maker symbol维度规则费率为w3.6
      ASSERT_EQ(user1_trade_check.m_msg->fee_rate_e8(), 40000);
      ASSERT_EQ(user1_trade_check.m_msg->cross_status(), ECrossStatus::MakerFill);
      ASSERT_EQ(user1_trade_check.m_msg->side(), ESide::Buy);
    }

    // 主站vip1，coin维度费率 taker w5结算 maker w1.8结算
    // 主站vip2，coin维度费率 taker w4结算 maker w1.6结算
    {
      // user1
      auto order_id = te->GenUUID();
      EPositionIndex pz_index = EPositionIndex::Single;
      ESide side = ESide::Buy;
      EOrderType order_type = EOrderType::Limit;
      biz::size_x_t qty = 1 * 1e8;
      biz::price_x_t price = 1000 * 1e4;

      te->AddMarkPrice(static_cast<ESymbol>(ESymbol::XRPUSDT), 1000, 1000, 1000);
      FutureOneOfCreateOrderBuilder create_build(order_id, ESymbol::XRPUSDT, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user1.m_uid, ECoin::USDT);
      auto pz_buy_order_resp = user1.process(create_build.Build());
      ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

      // 收user1result
      auto user1_result = te->PopResult();
      ASSERT_NE(user1_result.m_msg.get(), nullptr);
      auto user1_order_check = user1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
      ASSERT_NE(user1_order_check.m_msg, nullptr);
      ASSERT_EQ(user1_order_check.m_msg->order_status(), EOrderStatus::New);
      ASSERT_EQ(user1_order_check.m_msg->side(), ESide::Buy);
    }

    {
      // user2
      auto order_id = te->GenUUID();
      EPositionIndex pz_index = EPositionIndex::Single;
      ESide side = ESide::Sell;
      EOrderType order_type = EOrderType::Limit;
      biz::size_x_t qty = 0.58 * 1e8;
      biz::price_x_t price = 1000 * 1e4;

      std::vector<tmock::MockPrice> mp_vec{{ESymbol::BTCUSDT, 200000, 200000, 200000},
                                           {ESymbol::XRPUSDT, 1000, 1000, 1000}};
      te->BatchAddMarkPrice(mp_vec);
      FutureOneOfCreateOrderBuilder create_build(order_id, ESymbol::XRPUSDT, pz_index, side, order_type, qty, price,
                                                 ETimeInForce::GoodTillCancel, user2.m_uid, ECoin::USDT);
      auto pz_buy_order_resp = user2.process(create_build.Build());
      ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);

      // 收user2 result
      auto user2_result = te->PopResult();
      ASSERT_NE(user2_result.m_msg.get(), nullptr);
      auto user2_trade_check = user2_result.RefFutureMarginResult().RefRelatedFillByIndex(0);
      ASSERT_NE(user2_trade_check.m_msg, nullptr);
      // vip2 taker coin维度规则费率为w4
      ASSERT_EQ(user2_trade_check.m_msg->fee_rate_e8(), 55000);
      ASSERT_EQ(user2_trade_check.m_msg->cross_status(), ECrossStatus::TakerFill);
      ASSERT_EQ(user2_trade_check.m_msg->side(), ESide::Sell);

      // 收user1 result
      auto user1_result = te->PopResult();
      ASSERT_NE(user1_result.m_msg.get(), nullptr);

      auto user1_trade_check = user1_result.RefFutureMarginResult().RefRelatedFillByIndex(0);
      ASSERT_NE(user1_trade_check.m_msg, nullptr);
      // vip1 maker coin维度规则费率为w1.8
      ASSERT_EQ(user1_trade_check.m_msg->fee_rate_e8(), 20000);
      ASSERT_EQ(user1_trade_check.m_msg->cross_status(), ECrossStatus::MakerFill);
      ASSERT_EQ(user1_trade_check.m_msg->side(), ESide::Buy);
    }
  }

  {
    // #6. 验证试算失败
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid_2, ECoin::USDT, "");
    auto u1_resp = user2.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeFeeRateTooHigh);
  }
}

/*
以下验证均针对主站
配置规则费率 symbol维度和tag维度为w11，w16.5；
1.验证设置symbol维度费率是按照sc.defaultkrfrate进行校验
2.验证设置coin维度费率是按照sc.defaultkrfrate进行校验
3.存在较高的规则费率变更vip等级成功
4.设置超阈值费率
  匹配到symbol维度规则费率，预占使用tag和symbol规则费率
  匹配到coin维度规则费率，优先级低于超阈值费率，使用超阈费率进行预占
*/
TEST_F(TestFeeRateRule, set_fee_rate_with_feerate_user_rule) {
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":2,"rule_priority":5,"region_rule":0,"rule_config":{"coins":["USDT","USDC"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0011","maker":"0.0004"},{"user_rule":"vip1","taker":"0.001","maker":"0.00036"},{"user_rule":"vip2","taker":"0.0008","maker":"0.00032"}]}},{"rule_type":1,"rule_priority":5,"region_rule":0,"rule_config":{"symbols":["BTCUSDT"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0011","maker":"0.0004"},{"user_rule":"vip1","taker":"0.001","maker":"0.00036"},{"user_rule":"vip2","taker":"0.0008","maker":"0.00032"}]}},{"rule_type":4,"rule_priority":5,"region_rule":0,"rule_config":{"tag_ids":[54],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.00165","maker":"0.0006"},{"user_rule":"vip1","taker":"0.0015","maker":"0.00064"},{"user_rule":"vip2","taker":"0.0012","maker":"0.00048"}]}}]})";
  InitFeeRate(FeeRateConfig);

  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  {
    // tag维度匹配上，使用w16.5进行预占
    FutureOneOfSetLeverageBuilder set_lv_build(ESymbol::XRPUSDT, ECoin::USDT, user1.m_uid, 20 * 1e2, 20 * 1e2);
    auto resp = user1.process(set_lv_build.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto positionCheck = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(20 * 1e2, 5321750, 5338250, 821750, 838250);

    // symbol维度匹配上，使用w11进行预占
    FutureOneOfSetLeverageBuilder set_lv_build2(ESymbol::BTCUSDT, ECoin::USDT, user1.m_uid, 20 * 1e2, 20 * 1e2);
    resp = user1.process(set_lv_build2.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    positionCheck = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(20 * 1e2, 5214500, 5225500, 714500, 725500);

    // coin维度匹配上，使用w11进行预占
    FutureOneOfSetLeverageBuilder set_lv_build3(ESymbol(180), ECoin::USDT, user1.m_uid, 20 * 1e2, 20 * 1e2);
    resp = user1.process(set_lv_build3.Build());
    ASSERT_EQ(resp->ret_code(), 0);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    positionCheck = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(20 * 1e2, 5214500, 5225500, 714500, 725500);
  }

  // #1. 验证设置symbol维度费率是按照sc.defaultkrfrate进行校验
  std::string desc = "desc";
  {
    // 设置费率w17，被拒绝，w6则成功
    FutureSetSingleFeeRateBuilder set_req_build(ECoin::USDT, uid_1, enums::esymbol::XRPUSDT, 170000, desc, 170000,
                                                desc);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_EQ(error::kEcInvalidNewTakerFeeRate, resp->ret_code());

    FutureSetSingleFeeRateBuilder set_req_build2(ECoin::USDT, uid_1, enums::esymbol::XRPUSDT, 60000, desc, 60000, desc);
    resp = user1.process(set_req_build2.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
  }

  {
    // 设置费率w12，被拒绝
    FutureSetSingleFeeRateBuilder set_req_build(ECoin::USDT, uid_1, enums::esymbol::BTCUSDT, 120000, desc, 120000,
                                                desc);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_EQ(error::kEcInvalidNewTakerFeeRate, resp->ret_code());

    FutureSetSingleFeeRateBuilder set_req_build2(ECoin::USDT, uid_1, enums::esymbol::BTCUSDT, 60000, desc, 60000, desc);
    resp = user1.process(set_req_build2.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
  }

  {
    // 设置费率w12，被拒绝
    FutureSetSingleFeeRateBuilder set_req_build(ECoin::USDT, uid_1, ESymbol(180), 120000, desc, 120000, desc);
    auto resp = user1.process(set_req_build.Build());
    ASSERT_EQ(error::kEcInvalidNewTakerFeeRate, resp->ret_code());

    // todo(edward.yang)
    // 此处会触发一个bug，如果当前支持的所有合约都设置了合约维度费率，则设置coin维度w17也能成功，等新上合约时就会出现预占是w6，但是结算是w17的场景
    // FutureSetSingleFeeRateBuilder set_req_build2(ECoin::USDT, uid_1, enums::ESymbol(180), 60000, desc, 60000,
    //                                              desc);
    // resp = user1.process(set_req_build2.Build());
    // ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    // auto u1_result = te->PopResult();
    // ASSERT_NE(u1_result.m_msg.get(), nullptr);
  }

  // #2. 验证设置coin维度费率是按照sc.defaultkrfrate进行校验
  {
    // 设置费率w17，被拒绝，w5则成功
    FutureSetCustomerFeeRateBuilder req_build(ECoin::USDT, uid_1, 170000, desc, 170000, desc);
    auto resp = user1.process(req_build.Build());
    ASSERT_EQ(error::kEcInvalidNewTakerFeeRate, resp->ret_code());

    FutureSetSingleFeeRateBuilder req_build2(ECoin::USDT, uid_1, enums::esymbol::BTCUSDT, 120000, desc, 120000, desc);
    resp = user1.process(req_build2.Build());
    ASSERT_EQ(error::kEcInvalidNewTakerFeeRate, resp->ret_code());

    FutureSetCustomerFeeRateBuilder req_build3(ECoin::USDT, uid_1, 50000, desc, 50000, desc);
    resp = user1.process(req_build3.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
  }

  // #3. 存在较高的规则费率变更vip等级
  {
    std::string user_rule = "vip1";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid_1, ECoin::USDT, user_rule);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_setting = u1_result.m_msg->asset_margin_result().per_user_setting_data();
    auto itor = ret_setting.future_per_coin_setting().find(ECoin::USDT);
    ASSERT_TRUE(itor != ret_setting.future_per_coin_setting().end());
    ASSERT_EQ(itor->second.fee_rate_user_rule(), user_rule);
    auto other_positionCheck =
        u1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, ESymbol::XRPUSDT);
    ASSERT_NE(other_positionCheck.m_msg, nullptr);
    // vip1 tag维度费率为w15，预占使用w15
    other_positionCheck.CheckLv(20 * 1e2, 5321750, 5338250, 821750, 838250);
    auto btcusdt_positionCheck =
        u1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, ESymbol::BTCUSDT);
    ASSERT_NE(btcusdt_positionCheck.m_msg, nullptr);
    // vip1 symbol维度 btcusdt费率为w10，预占使用w10
    btcusdt_positionCheck.CheckLv(20 * 1e2, 5214500, 5225500, 714500, 725500);
    auto ALPHAUSDT_positionCheck =
        u1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, ESymbol(180));
    ASSERT_NE(ALPHAUSDT_positionCheck.m_msg, nullptr);
    // vip1 coin维度费率为w10，预占使用w10
    ALPHAUSDT_positionCheck.CheckLv(20 * 1e2, 5214500, 5225500, 714500, 725500);
  }

  /// #4. 设置超阈值费率，由于匹配到symbol维度规则费率，预占使用tag和symbol规则费率
  {
    FutureSetCustomerFeeRateBuilder req_build4(ECoin::USDT, uid_1, 170000, desc, 170000, desc, 170000);
    auto resp = user1.process(req_build4.Build());
    ASSERT_EQ(error::kErrorCodeSuccess, resp->ret_code());
    auto u1_result = te->PopResult();
    auto other_positionCheck =
        u1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, ESymbol::XRPUSDT);
    ASSERT_NE(other_positionCheck.m_msg, nullptr);
    // vip1 tag维度费率为w15，预占使用w15
    other_positionCheck.CheckLv(20 * 1e2, 5321750, 5338250, 821750, 838250);
    auto btcusdt_positionCheck =
        u1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, ESymbol::BTCUSDT);
    ASSERT_NE(btcusdt_positionCheck.m_msg, nullptr);
    // vip1 symbol维度 btcusdt费率为w10，预占使用w10
    btcusdt_positionCheck.CheckLv(20 * 1e2, 5214500, 5225500, 714500, 725500);
    auto ALPHAUSDT_positionCheck =
        u1_result.RefFutureMarginResult().RefRelatedPositionByPositionIdxAndSymbol(0, ESymbol(180));
    ASSERT_NE(ALPHAUSDT_positionCheck.m_msg, nullptr);
    // coin维度规则费率优先级低于coin维度特殊费率，使用w17进行预占
    ALPHAUSDT_positionCheck.CheckLv(20 * 1e2, 5331500, 5348500, 831500, 848500);
  }
}

// 预占费率
TEST_F(TestFeeRateRule, test_pre_occupy_fee_rate_priority) {
  // init symbol config
  biz::fee_rate_e8_t default_taker_fee_rate = 55000;
  biz::fee_rate_e8_t default_maker_fee_rate = 20000;
  biz::fee_rate_e8_t default_settle_fee_rate = 55000;
  auto symbol_config = std::make_shared<biz::SymbolConfig>();
  symbol_config->symbol_ = ESymbol::BTCUSDT;
  symbol_config->symbol_name_ = "BTCUSDT";
  symbol_config->coin_ = ECoin::USDT;
  symbol_config->coin_name_ = "USDT";
  symbol_config->default_taker_fee_rate_e8_ = default_taker_fee_rate;
  symbol_config->default_maker_fee_rate_e8_ = default_maker_fee_rate;
  symbol_config->default_settle_fee_rate_e8_ = default_settle_fee_rate;

  // init setting
  auto user_setting = std::make_shared<store::CowSetting>();
  user_setting->draft_setting = std::make_shared<store::Setting>();
  user_setting->cur_setting = user_setting->draft_setting;

  // 0. user_setting为空,使用symbol_config的预占费率
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetDefaultTakerFeeRateE8(symbol_config.get(), nullptr),
            symbol_config->default_taker_fee_rate_e8_);

  // 1. 不做任何配置,使用symbol_config的预占费率
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetDefaultTakerFeeRateE8(symbol_config.get(), user_setting.get()),
            symbol_config->default_taker_fee_rate_e8_);

  // 2. 设置coin维度规则费率,使用coin维度的预占费率
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":2,"rule_priority":1,"region_rule":0,"rule_config":{"coins":["USDT","BTC"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0006","maker":"0.0001"}]}}]})";
  InitFeeRate(FeeRateConfig);
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetDefaultTakerFeeRateE8(symbol_config.get(), user_setting.get()), 60000);

  // 3. 设置coin_setting的预占费率,使用新设置费率
  user_setting->draft_setting->future_per_coin_setting.emplace(ECoin::USDT, store::PerCoinUserSetting{0, ECoin::USDT});
  user_setting->draft_setting->future_per_coin_setting.at(ECoin::USDT).optional_default_tkfr = 65000;
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetDefaultTakerFeeRateE8(symbol_config.get(), user_setting.get()), 65000);

  // 4. 设置symbol维度规则费率,使用新设置费率
  FeeRateConfig =
      R"({"data":[{"rule_type":1,"rule_priority":1,"region_rule":0,"rule_config":{"symbols":["BTCUSDT"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0007","maker":"0.0001"}]}}]})";
  InitFeeRate(FeeRateConfig);
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetDefaultTakerFeeRateE8(symbol_config.get(), user_setting.get()), 70000);

  // 5. 设置coin维度最终费率
  // 不设置coin_setting预占费率使用symbol_config的预占费率
  user_setting->draft_setting->future_per_coin_setting.at(ECoin::USDT).is_final = true;
  user_setting->draft_setting->future_per_coin_setting.at(ECoin::USDT).optional_default_tkfr.reset();
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetDefaultTakerFeeRateE8(symbol_config.get(), user_setting.get()),
            symbol_config->default_taker_fee_rate_e8_);
  // 设置coin_setting预占费率使用新设置费率
  user_setting->draft_setting->future_per_coin_setting.at(ECoin::USDT).optional_default_tkfr = 75000;
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetDefaultTakerFeeRateE8(symbol_config.get(), user_setting.get()), 75000);
}

// 结算费率
TEST_F(TestFeeRateRule, test_settlement_fee_rate_priority) {
  // init symbol config
  biz::fee_rate_e8_t default_taker_fee_rate = 55000;
  biz::fee_rate_e8_t default_maker_fee_rate = 20000;
  biz::fee_rate_e8_t default_settle_fee_rate = 55000;
  auto symbol_config = std::make_shared<biz::SymbolConfig>();
  symbol_config->symbol_ = ESymbol::BTCUSDT;
  symbol_config->symbol_name_ = "BTCUSDT";
  symbol_config->coin_ = ECoin::USDT;
  symbol_config->coin_name_ = "USDT";
  symbol_config->default_taker_fee_rate_e8_ = default_taker_fee_rate;
  symbol_config->default_maker_fee_rate_e8_ = default_maker_fee_rate;
  symbol_config->default_settle_fee_rate_e8_ = default_settle_fee_rate;

  // init setting
  auto user_setting = std::make_shared<store::CowSetting>();
  user_setting->draft_setting = std::make_shared<store::Setting>();
  user_setting->cur_setting = user_setting->draft_setting;

  // 0. user_setting为空,使用symbol_config预占费率
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomMkfrE8(symbol_config.get(), nullptr), default_maker_fee_rate);
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomTkfrE8(symbol_config.get(), nullptr), default_taker_fee_rate);

  // 1. user_setting不做设置,使用symbol_config预占费率
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomMkfrE8(symbol_config.get(), user_setting.get()),
            default_maker_fee_rate);
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomTkfrE8(symbol_config.get(), user_setting.get()),
            default_taker_fee_rate);

  // 2. 设置coin维度规则费率,使用新设置费率
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":2,"rule_priority":1,"region_rule":0,"rule_config":{"coins":["USDT","BTC"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0006","maker":"0.00025"}]}}]})";
  InitFeeRate(FeeRateConfig);
  user_setting->draft_setting->future_per_coin_setting.emplace(ECoin::USDT, store::PerCoinUserSetting{0, ECoin::USDT});
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomMkfrE8(symbol_config.get(), user_setting.get()), 25000);
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomTkfrE8(symbol_config.get(), user_setting.get()), 60000);

  // 3. 设置coin维度个性化费率,使用新设置费率
  user_setting->draft_setting->future_per_coin_setting.at(ECoin::USDT).optional_default_tkfr = 65000;
  user_setting->draft_setting->future_per_coin_setting.at(ECoin::USDT).optional_tkfr = 65000;
  user_setting->draft_setting->future_per_coin_setting.at(ECoin::USDT).optional_mkfr = 30000;
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomTkfrE8(symbol_config.get(), user_setting.get()), 65000);
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomMkfrE8(symbol_config.get(), user_setting.get()), 30000);

  // 4. 设置symbol维度规则费率,使用新设置费率
  FeeRateConfig =
      R"({"data":[{"rule_type":1,"rule_priority":1,"region_rule":0,"rule_config":{"symbols":["BTCUSDT"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0007","maker":"0.00035"}]}}]})";
  InitFeeRate(FeeRateConfig);
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomTkfrE8(symbol_config.get(), user_setting.get()), 70000);
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomMkfrE8(symbol_config.get(), user_setting.get()), 35000);

  // 5. 设置symbol维度个性化费率,使用新设置费率
  store::FutureSymbolConfig future_symbol_config;
  future_symbol_config.coin = ECoin::USDT;
  future_symbol_config.symbol = ESymbol::BTCUSDT;
  future_symbol_config.taker_fee_rate_e8 = 75000;
  future_symbol_config.maker_fee_rate_e8 = 40000;
  user_setting->draft_setting->future_symbol_setting_dto.future_symbol_config_map.emplace(ESymbol::BTCUSDT,
                                                                                          future_symbol_config);
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomTkfrE8(symbol_config.get(), user_setting.get()), 75000);
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomMkfrE8(symbol_config.get(), user_setting.get()), 40000);

  // 6. 设置coin维度最终费率
  // 没有值则使用symbol维度个性化费率
  user_setting->draft_setting->future_per_coin_setting.at(ECoin::USDT).optional_mkfr.reset();
  user_setting->draft_setting->future_per_coin_setting.at(ECoin::USDT).optional_tkfr.reset();
  user_setting->draft_setting->future_per_coin_setting.at(ECoin::USDT).is_final = true;
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomTkfrE8(symbol_config.get(), user_setting.get()), 75000);
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomMkfrE8(symbol_config.get(), user_setting.get()), 40000);

  // 设置值
  user_setting->draft_setting->future_per_coin_setting.at(ECoin::USDT).optional_tkfr = 80000;
  user_setting->draft_setting->future_per_coin_setting.at(ECoin::USDT).optional_mkfr = 45000;
  user_setting->draft_setting->future_per_coin_setting.at(ECoin::USDT).is_final = true;
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomTkfrE8(symbol_config.get(), user_setting.get()), 80000);
  ASSERT_EQ(biz::feeratebiz::FeeRateHelper::GetCustomMkfrE8(symbol_config.get(), user_setting.get()), 45000);
}

TEST_F(TestFeeRateRule, set_fee_rate_user_rule_for_user) {
  // 分为主站和cis两个站点费率
  // coin费率最低，symbol为coin的2倍，tag为coin的3倍
  const char* FeeRateConfig =
      R"({"data":[{"rule_type":2,"rule_priority":5,"region_rule":0,"rule_config":{"coins":["USDT","BTC"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.00055","maker":"0.0002"},{"user_rule":"vip1","taker":"0.0005","maker":"0.00018"},{"user_rule":"vip2","taker":"0.0004","maker":"0.00016"}]}},{"rule_type":1,"rule_priority":5,"region_rule":0,"rule_config":{"symbols":["BTCUSDT"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.0011","maker":"0.0004"},{"user_rule":"vip1","taker":"0.001","maker":"0.00036"},{"user_rule":"vip2","taker":"0.0008","maker":"0.00032"}]}},{"rule_type":4,"rule_priority":5,"region_rule":0,"rule_config":{"tag_ids":[52],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.00165","maker":"0.0006"},{"user_rule":"vip1","taker":"0.0015","maker":"0.00064"},{"user_rule":"vip2","taker":"0.0012","maker":"0.00048"}]}},{"rule_type":2,"rule_priority":5,"region_rule":1,"rule_config":{"coins":["USDT","BTC"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.001","maker":"0.0005"},{"user_rule":"vip1","taker":"0.0009","maker":"0.00045"},{"user_rule":"vip2","taker":"0.0008","maker":"0.0004"}]}},{"rule_type":1,"rule_priority":5,"region_rule":1,"rule_config":{"symbols":["XXXUSDT"],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.002","maker":"0.001"},{"user_rule":"vip1","taker":"0.0018","maker":"0.0009"},{"user_rule":"vip2","taker":"0.0016","maker":"0.0008"}]}},{"rule_type":4,"rule_priority":5,"region_rule":1,"rule_config":{"tag_ids":[52],"config":[{"user_rule":"eaf2d510f4527c6604d914b7e92f7eb8","taker":"0.003","maker":"0.0015"},{"user_rule":"vip1","taker":"0.0027","maker":"0.00135"},{"user_rule":"vip2","taker":"0.0024","maker":"0.00012"}]}}]})";
  InitFeeRate(FeeRateConfig);

  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  biz::user_id_t const uid_2 = 658002;
  stub user2(te, uid_2);

  UserDeposit(user1, user2);
  {
    std::string user_rule = "vip1";
    FutureSetFeeRateUserRuleBuilder set_fee_rate_user_rule(uid_1, ECoin::USDT, user_rule);
    set_fee_rate_user_rule.msg.mutable_set_fee_rate_user_rule_req()->set_for_user(true);
    auto u1_resp = user1.process(set_fee_rate_user_rule.Build());
    ASSERT_EQ(u1_resp->ret_code(), error::kErrorCodeSuccess);
    auto u1_result = te->PopResult();
    ASSERT_NE(u1_result.m_msg.get(), nullptr);
    auto ret_setting = u1_result.m_msg->asset_margin_result().per_user_setting_data();
    for (auto& [_, setting] : ret_setting.future_per_coin_setting()) {
      ASSERT_EQ(setting.fee_rate_user_rule(), user_rule);
    }
  }
}
std::shared_ptr<tmock::CTradeAppMock> TestFeeRateRule::te;
