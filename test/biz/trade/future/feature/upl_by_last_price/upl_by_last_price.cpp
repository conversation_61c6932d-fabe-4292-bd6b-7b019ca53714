#include "test/biz/trade/future/feature/upl_by_last_price/upl_by_last_price.hpp"

#include <string>

#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/futures/modules/orderbiz/activeorderbiz/active_order_biz.hpp"
#include "src/config/config_proxy.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/future/func/draft_pkg_builder.h"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

int32_t UplByLastPriceTest::Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t UplByLastPriceTest::SwitchMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

/*
测试场景：逐仓模式下，调整市场价格和仓位大小
预期结果：upl和upl_by_lp符合预期
*/
TEST_F(UplByLastPriceTest, calc_upl_by_lp) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  Deposit(uid_1, "2000");
  Deposit(uid_2, "2000");

  SwitchMode(uid_1, EAccountMode::Isolated);
  SwitchMode(uid_2, EAccountMode::Isolated);

  // 下单
  auto order_id = te->GenUUID();
  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  ESide side = ESide::Buy;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 8900 * 1e4;

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9100);

  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);

  order_id = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_2, ********);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());
  EXPECT_EQ(********00, pz_checker.m_msg->unrealised_pnl_e8());
  EXPECT_EQ(4000000000, pz_checker.m_msg->unrealised_pnl_by_lp_e8());

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9050);

  qty = 0.1 * 1e8;
  side = ESide::Buy;
  order_id = te->GenUUID();
  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  order_id = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(30000000, pz_checker.m_msg->size_x());
  EXPECT_EQ(3000000000, pz_checker.m_msg->unrealised_pnl_e8());
  EXPECT_EQ(4500000000, pz_checker.m_msg->unrealised_pnl_by_lp_e8());

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 8990, 9000, 9010);

  qty = 0.15 * 1e8;
  side = ESide::Sell;
  order_id = te->GenUUID();
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  order_id = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(15000000, pz_checker.m_msg->size_x());
  EXPECT_EQ(1350000000, pz_checker.m_msg->unrealised_pnl_e8());
  EXPECT_EQ(1650000000, pz_checker.m_msg->unrealised_pnl_by_lp_e8());

  te->ReCalc(uid_2);

  auto user_store1 = te->GetUserStore(uid_2);
  auto const& pz =
      *(user_store1->working_coins_[5]->working_future_symbols_[5]->all_positions_[EPositionIndex::Single]->Latest());

  EXPECT_EQ(pz.size, 15000000);
  EXPECT_EQ(pz.session_average_price_x, 89000000);
  EXPECT_EQ(pz.mark_price, 89900000);
  EXPECT_EQ(pz.last_price, 90100000);
  EXPECT_EQ(pz.position_balance, 13438110000);
  EXPECT_EQ(pz.unrealised_pnl, -1350000000);
  EXPECT_EQ(pz.position_mm_with_fee_e8, 755610000);

  qty = 0.15 * 1e8;
  side = ESide::Sell;
  order_id = te->GenUUID();
  FutureOneOfCreateOrderBuilder create_build_user1_sell_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  order_id = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user2_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(0, pz_checker.m_msg->size_x());
  EXPECT_EQ(0, pz_checker.m_msg->unrealised_pnl_e8());
  EXPECT_EQ(0, pz_checker.m_msg->unrealised_pnl_by_lp_e8());
}

/*
测试场景：全仓模式下，下活动单
预期结果：下单根据mark price和last price较差的upl进行拦截
*/
TEST_F(UplByLastPriceTest, create_ao_check_margin_ratio_by_worst_upl) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  Deposit(uid_1, "300");
  Deposit(uid_2, "300");

  SwitchMode(uid_1, EAccountMode::Cross);
  SwitchMode(uid_2, EAccountMode::Cross);

  // 下单
  auto order_id = te->GenUUID();
  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  ESide side = ESide::Buy;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 8600);

  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  order_id = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());
  EXPECT_EQ(0, pz_checker.m_msg->unrealised_pnl_e8());
  EXPECT_EQ(-8000000000, pz_checker.m_msg->unrealised_pnl_by_lp_e8());

  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 0.1 * 1e8;
  price = 9000 * 1e4;
  FutureOneOfPreCreateOrderBuilder pre_create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                              ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(pre_create_build_user1_buy.Build());
  EXPECT_EQ(30031, resp->ret_code());

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);
  FutureOneOfPreCreateOrderBuilder pre_create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty,
                                                                price, ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(pre_create_build_user1_buy_2.Build());
  EXPECT_EQ(0, resp->ret_code());

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 8600, 9000, 9000);
  FutureOneOfPreCreateOrderBuilder pre_create_build_user1_buy_3(order_id, symbol, pz_index, side, order_type, qty,
                                                                price, ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(pre_create_build_user1_buy_3.Build());
  EXPECT_EQ(30031, resp->ret_code());

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 8000, 9000, 7000);
  te->ReCalc(uid_1);

  auto user_store1 = te->GetUserStore(uid_1);
  auto const& pz =
      *(user_store1->working_coins_[5]->working_future_symbols_[5]->all_positions_[EPositionIndex::Single]->Latest());

  EXPECT_EQ(pz.entry_price, ************);
  EXPECT_EQ(pz.mark_price, 80000000);
  EXPECT_EQ(pz.last_price, 70000000);

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 10000, 9000, 7000);
  te->ReCalc(uid_1);

  user_store1 = te->GetUserStore(uid_1);
  auto const& pz2 =
      *(user_store1->working_coins_[5]->working_future_symbols_[5]->all_positions_[EPositionIndex::Single]->Latest());

  EXPECT_EQ(pz2.entry_price, ************);
  EXPECT_EQ(pz2.mark_price, 100000000);
  EXPECT_EQ(pz2.last_price, 70000000);
}

std::shared_ptr<tmock::CTradeAppMock> UplByLastPriceTest::te;
