#include "test/biz/trade/future/feature/mm_accumulation/mm_accumulation.hpp"

#include <algorithm>
#include <bbase/common/decimal/decimal.hpp>
#include <string>
#include <unordered_map>
#include <vector>

#include "enums/ebizcode/biz_code.pb.h"
#include "src/biz_worker/engine/pre_worker/pre_engine.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/store/per_symbol_store.hpp"
#include "src/biz_worker/service/trade/store/position/cow_position.hpp"
#include "src/biz_worker/service/trade/store/position/raw_position.hpp"
#include "src/biz_worker/utils/calc_util.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/lib/stub.hpp"

int32_t MmAccumulationTest::Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t MmAccumulationTest::SwitchAccountMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t MmAccumulationTest::SwitchPositionMode(biz::user_id_t uid, EPositionMode mode) {
  stub user(te, uid);
  FutureOneOfSwitchPositionModeBuilder switch_position_mode(ESymbol::BTCUSDT, ECoin::USDT, mode, uid);
  auto resp = user.process(switch_position_mode.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }
  return 0;
}

void MmAccumulationTest::UpdateSymbolConfig(biz::symbol_t symbol, const std::vector<float>& value_vec,
                                            const std::vector<float>& mm_rate_vec, bool use_deduction, bool gray,
                                            int32_t update_mmr_percent_e2, int32_t update_imr_percent_e2,
                                            biz::version_t risk_limit_version, bool risk_limit_is_changed) {
  auto sc_ev = std::make_shared<event::OnUpdateConfigEvent>(0, event::EventType::kEventSymbolConfig,
                                                            event::EventSourceType::kUnknown,
                                                            event::EventDispatchType::kBroadcastToWorker);

  config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr()->InitMockData(gray, risk_limit_version);

  auto risklimit = config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr()->risk_limits_map_[symbol];

  sc_ev->set_symbol_config_mgr(config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr());

  auto risk_info_set = std::make_shared<biz::sc::RiskLimitSet>();
  auto risk_info = std::make_shared<biz::RiskLimitCache>();
  risk_info->symbol_ = symbol;
  risk_info->risk_id_ = 1;
  risk_info->max_leverage_e2_ = static_cast<biz::leverage_e2_t>(100 * 1e2);
  risk_info->initial_margin_rate_e4_ = 100;
  risk_info->maintenance_margin_rate_e4_ = static_cast<biz::value_x_t>(mm_rate_vec[0]);
  risk_info->max_ord_pz_value_x_ = static_cast<biz::value_x_t>(value_vec[0] * 1e8);  // 1
  risk_info->is_lowest_risk_ = true;
  risk_info->maintenance_margin_deduction_e8_ = 0;

  auto risk_info_2 = std::make_shared<biz::RiskLimitCache>();
  risk_info_2->symbol_ = symbol;
  risk_info_2->risk_id_ = 2;
  risk_info_2->max_leverage_e2_ = static_cast<biz::leverage_e2_t>(77 * 1e2);
  risk_info_2->initial_margin_rate_e4_ = 150;
  risk_info_2->maintenance_margin_rate_e4_ = static_cast<biz::value_x_t>(mm_rate_vec[1]);
  risk_info_2->max_ord_pz_value_x_ = static_cast<biz::value_x_t>(value_vec[1] * 1e8);  // 2
  risk_info_2->is_lowest_risk_ = false;

  auto risk_info_3 = std::make_shared<biz::RiskLimitCache>();
  risk_info_3->symbol_ = symbol;
  risk_info_3->risk_id_ = 3;
  risk_info_3->max_leverage_e2_ = static_cast<biz::leverage_e2_t>(40 * 1e2);
  risk_info_3->initial_margin_rate_e4_ = 250;
  risk_info_3->maintenance_margin_rate_e4_ = static_cast<biz::value_x_t>(mm_rate_vec[2]);
  risk_info_3->max_ord_pz_value_x_ = static_cast<biz::value_x_t>(value_vec[2] * 1e8);  // 6
  risk_info_3->is_lowest_risk_ = false;

  if (gray) {
    risk_info->old_maintenance_margin_rate_e4_ = risklimit->risk_limits()[0]->maintenance_margin_rate_e4_;
    risk_info_2->old_maintenance_margin_rate_e4_ = risklimit->risk_limits()[1]->maintenance_margin_rate_e4_;
    risk_info_3->old_maintenance_margin_rate_e4_ = risklimit->risk_limits()[2]->maintenance_margin_rate_e4_;

    if (use_deduction) {
      risk_info->old_maintenance_margin_deduction_e8_ = risklimit->risk_limits()[0]->maintenance_margin_deduction_e8_;
      risk_info_2->old_maintenance_margin_deduction_e8_ = risklimit->risk_limits()[1]->maintenance_margin_deduction_e8_;
      risk_info_3->old_maintenance_margin_deduction_e8_ = risklimit->risk_limits()[2]->maintenance_margin_deduction_e8_;
    }

    risk_info->maintenance_margin_rate_e4_ = risk_info->maintenance_margin_rate_e4_ * update_mmr_percent_e2 / 100;
    risk_info->initial_margin_rate_e4_ = risk_info->initial_margin_rate_e4_ * update_imr_percent_e2 / 100;
    risk_info->max_leverage_e2_ = 1 * 1e4 / risk_info->initial_margin_rate_e4_ * 1e2;

    risk_info_2->maintenance_margin_rate_e4_ = risk_info_2->maintenance_margin_rate_e4_ * update_mmr_percent_e2 / 100;
    risk_info_2->initial_margin_rate_e4_ = risk_info_2->initial_margin_rate_e4_ * update_imr_percent_e2 / 100;
    risk_info_2->max_leverage_e2_ = 1 * 1e4 / risk_info_2->initial_margin_rate_e4_ * 1e2;

    risk_info_3->maintenance_margin_rate_e4_ = risk_info_3->maintenance_margin_rate_e4_ * update_mmr_percent_e2 / 100;
    risk_info_3->initial_margin_rate_e4_ = risk_info_3->initial_margin_rate_e4_ * update_imr_percent_e2 / 100;
    risk_info_3->max_leverage_e2_ = 1 * 1e4 / risk_info_3->initial_margin_rate_e4_ * 1e2;

    sc_ev->symbol_config_mgr()->risk_limits_has_changed_.push_back(symbol);
  }

  if (use_deduction) {
    risk_info->maintenance_margin_deduction_e8_ = 0;
    risk_info_2->maintenance_margin_deduction_e8_ = static_cast<biz::value_x_t>(
        risk_info->max_ord_pz_value_x_ *
        (risk_info_2->maintenance_margin_rate_e4_ - risk_info->maintenance_margin_rate_e4_) / 1e4);
    risk_info_3->maintenance_margin_deduction_e8_ =
        risk_info_2->maintenance_margin_deduction_e8_ +
        static_cast<biz::value_x_t>(
            risk_info_2->max_ord_pz_value_x_ *
            (risk_info_3->maintenance_margin_rate_e4_ - risk_info_2->maintenance_margin_rate_e4_) / 1e4);
  }

  if (risk_limit_is_changed && !gray) {
    sc_ev->symbol_config_mgr()->risk_limits_has_changed_.push_back(symbol);
  }

  risk_info_set->Add(risk_info);
  risk_info_set->Add(risk_info_2);
  risk_info_set->Add(risk_info_3);
  sc_ev->symbol_config_mgr()->UpdateRiskList(symbol, risk_info);
  sc_ev->symbol_config_mgr()->UpdateRiskList(symbol, risk_info_2);
  sc_ev->symbol_config_mgr()->UpdateRiskList(symbol, risk_info_3);
  sc_ev->symbol_config_mgr()->set_version(risk_limit_version + 100);

  te->pipeline()->ring_buffers(worker::WorkerType::kPreWorker)->Write(sc_ev, true);
}

/*
测试场景：1. 用户为全仓双仓模式
        2. 用户Buy方向为高仓位，净持仓仓位mm按照累进算法计算
        3. 用户Buy方向持有挂单 风险限额在3档，订单mm按照第三档的v2mm计算
预期结果：用户的risk id,v2mm,pzmm,ordermm正确刷新
*/
TEST_F(MmAccumulationTest, cross_mode_single_side) {
  biz::user_id_t uid_1 = 10000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 10002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  Deposit(uid_1, "***********");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 15 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(11/10*0.0006 + 0.005) + 0.5*1000000*(11/10*0.0006 + 0.01)
  // 1.5*1000000*(11/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 109*********0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(9/10*0.0006 + 0.005) + 0.5*1000000*(9/10*0.0006 + 0.01)
  // 1.5*1000000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1081*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 6 * 1e8;

  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 600000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 600000000);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1081*********);
  // 6 * 100000 * (0.0006 + 9/10 * 0.0006 + 0.015)
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), ************);
}

/*
测试场景：1. 用户为全仓双仓模式
        2. 用户Buy方向持有仓位 仓位价值在风险限额的2档，仓位mm按照累进算法计算
        3. 用户Buy方向持有挂单 风险限额在3档，订单mm按照第三档的v2mm计算
预期结果：用户两个方向的risk id,v2mm,pzmm,ordermm正确刷新
*/
TEST_F(MmAccumulationTest, cross_mode_both_side) {
  biz::user_id_t uid_1 = 10000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 10002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  SwitchPositionMode(uid_1, EPositionMode::BothSide);
  SwitchPositionMode(uid_2, EPositionMode::BothSide);

  Deposit(uid_1, "***********");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 15 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  EPositionIndex pz_index = EPositionIndex::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  pz_index = EPositionIndex::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 2);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(11/10*0.0006 + 0.005) + 0.5*1000000*(11/10*0.0006 + 0.01)
  // 1.5*1000000*(11/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 109*********0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(1);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 2);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(9/10*0.0006 + 0.005) + 0.5*1000000*(9/10*0.0006 + 0.01)
  // 1.5*1000000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1081*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(1);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  pz_index = EPositionIndex::Sell;
  qty = 10 * 1e8;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂买单
  order_id_2 = te->GenUUID();
  side = ESide::Buy;
  pz_index = EPositionIndex::Buy;
  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 2);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 2 * 1000000 * 9 / 10 * 0.0006 = 1080
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 108*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(1);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 0.5*1000000*(11/10*0.0006 + 0.005) + 2 * 1000000 * 11/10 * 0.0006
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 4**********0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 2);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(11/10*0.0006)*2
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 132*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(1);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 0.5*1000000*(9/10*0.0006 + 0.005) + 2 * 1000000 * 9/10 * 0.0006
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 6 * 1e8;
  pz_index = EPositionIndex::Buy;

  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 600000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 2);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 600000000);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  // 6 * 100000 * (0.0006 + 9/10 * 0.0006 + 0.015)
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), ************);

  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(1);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 132*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 用户Buy方向持有仓位 仓位价值在风险限额的2档，仓位mm按照累进算法计算
        3. 用户Buy方向持有挂单 风险限额在3档，订单mm按照第三档的v2mm计算
预期结果：用户的risk id,v2mm,pzmm,ordermm,liqprice,bustprice正确刷新
*/
TEST_F(MmAccumulationTest, isolated_mode_single_side) {
  biz::user_id_t uid_1 = 10000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 10002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Isolated);
  SwitchAccountMode(uid_2, EAccountMode::Isolated);

  Deposit(uid_1, "***********");  // int64 最多表示 1e18 => 1e10 * E8
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 15 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(11/10*0.0006 + 0.005) + 0.5*1000000*(11/10*0.0006 + 0.01)
  // 1.5*1000000*(11/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 109*********0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 1509*********0);
  // (100000 * 15 + 100000 * 15 * (11/10*0.0006 + 0.1)) / (15 * (1+0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1*********);
  // 1*1000000*0.005+0.5*1000000*0.01 = 10000
  // 1.5*1000000*(0.01) - 5000 = 10000
  // (110000 * 15 - 10000) / 15
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1093333000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(9/10*0.0006 + 0.005) + 0.5*1000000*(9/10*0.0006 + 0.01)
  // 1.5*1000000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1081*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 15081*********);
  // (100000 * 15 - 100000 * 15 * (9/10*0.0006 + 0.1)) / (15 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 1*1000000*0.005+0.5*1000000*0.01 = 10000
  // 1.5*1000000*(0.01) - 5000 = 10000
  // (90000 * 15 + 10000) / 15
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 6 * 1e8;

  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 600000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 600000000);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1081*********);
  // 6 * 100000 * (0.0006 + 9/10 * 0.0006 + 0.015)
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 15081*********);
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);

  // 预下单预估强平价
  order_id = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfPreCreateOrderBuilder create_build_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                    ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_buy.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), 2*********);
  // 1*1000000*0.005+1*1000000*0.01+0.1*1000000*0.015 = 16500
  // 2.1*1000000*(0.015) - 15000 = 16500
  // (90000 * 21 + 16500) / 21
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 907858000);

  order_id = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfPreCreateOrderBuilder create_build_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                     ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_sell.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), *********);
  // 0.9*1000000*0.005 = 4500
  // (90000 * 9 + 4500) / 9
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), *********);
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 用户Buy方向持有仓位 仓位价值在风险限额的1档
        3. 用户Buy方向下市价单，且most price为not liq price，下单后触发升档
预期结果：not liq price更新为升档之后的计算结果
*/
TEST_F(MmAccumulationTest, isolated_mode_update_not_liq_price) {
  biz::user_id_t uid_1 = 10000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 10002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Isolated);
  SwitchAccountMode(uid_2, EAccountMode::Isolated);

  Deposit(uid_1, "***********");  // int64 最多表示 1e18 => 1e10 * E8
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 9 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);

  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 200000;
  sc->price_limit_pnt_y_e6_ = 210000;

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 5 * 1e8;
  order_type = EOrderType::Market;

  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->price_x(), 1098899000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 用户卖方向的成交价格大于订单价格
        3. 用户预下单的预估强平价用pzdto的risk id计算
预期结果：用户的risk id,v2mm,pzmm,ordermm,liqprice,bustprice正确刷新
*/
TEST_F(MmAccumulationTest, isolated_mode_single_side_sell_pre_liq_price) {
  biz::user_id_t uid_1 = 10000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 10002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Isolated);
  SwitchAccountMode(uid_2, EAccountMode::Isolated);

  Deposit(uid_1, "***********");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 10.5 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  price = 95000 * 1e4;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.005
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 10.5*100000*(11/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 10.5*100000*(11/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 10569300000000);
  // (100000 * 10.5 + 100000 * 10.5 * (11/10*0.0006 + 0.1)) / (10.5 * (1+0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1*********);
  // 10.5*100000*(0.005) = 5250
  // (110000 * 10.5 - 5250) / 10.5
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1095000000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 10.5*100000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 10.5*100000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 10556700000000);
  // (100000 * 10.5 - 100000 * 10.5 * (9/10*0.0006 + 0.1)) / (10.5 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 10.5*100000*(0.01) - 5000 = 5500
  // (90000 * 10.5 + 5500) / 10.5
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 905239000);

  // 预下单预估强平价
  order_id = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfPreCreateOrderBuilder pre_create_build_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(pre_create_build_buy.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), 0);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 0);

  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 0.01 * 1e8;
  FutureOneOfPreCreateOrderBuilder pre_create_build_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  pre_create_build_buy_2.msg.mutable_pre_create_order()->set_close_on_trigger(true);
  resp = user2.process(pre_create_build_buy_2.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), -1049000000);
  // 10.49*100000*(0.005) = 5245
  // (110000 * 10.49 - 5245) / 10.49
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 1095000000);
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 用户Buy方向持有仓位 仓位价值在风险限额的1档，仓位mm按照累进算法计算
        3. 用户Buy方向持有挂单 风险限额在1档，订单mm按照第1档的v2mm计算
        4. 更新sc的max_pz_ord_value，订单成交后仓位的mm计算还是按照第一档计算
预期结果：用户的risk id,v2mm,pzmm,ordermm,liqprice,bustprice正确刷新
*/
TEST_F(MmAccumulationTest, isolated_mode_single_side_update_max_pz_ord_value) {
  biz::user_id_t uid_1 = 10000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 10002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Isolated);
  SwitchAccountMode(uid_2, EAccountMode::Isolated);

  Deposit(uid_1, "***********");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 5 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.005
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 0.5*1000000*(11/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 0.5*1000000*(11/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 5033*********);
  // (100000 * 5 + 100000 * 5 * (11/10*0.0006 + 0.1)) / (5 * (1+0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1*********);
  // 0.5*1000000*0.005 = 2500
  // (110000 * 5 - 2500) / 5
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1095000000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 0.5*1000000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 0.5*1000000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 5027*********);
  // (100000 * 5 - 100000 * 5 * (9/10*0.0006 + 0.1)) / (5 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 0.5*1000000*0.005 = 2500
  // (90000 * 5 + 2500) / 5
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 5 * 1e8;

  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  // 5 * 100000 * (0.0006 + 9/10 * 0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 307*********);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 5027*********);
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);

  MmAccumulationTest::UpdateSymbolConfig(symbol, {40000, 50000, 6000000}, {50, 100, 150});

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell_2(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 1*1000000*(9/10*0.0006 + 0.005)
  // 仓位用的symbol的risk id
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 1*1000000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 10054*********);
  // (100000 * 10 - 100000 * 10 * (9/10*0.0006 + 0.1)) / (10 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 1*1000000*0.005 = 5000
  // (90000 * 10 + 5000) / 10
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户加仓，减仓，挂单，撤单
预期结果：用户的risk id,v2mm,pzmm,ordermm正确刷新
*/
TEST_F(MmAccumulationTest, cross_mode_single_side_add_sub_size) {
  biz::user_id_t uid_1 = 10000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 10002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  Deposit(uid_1, "***********");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 5 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.005
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 0.5*1000000*(11/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 0.5*1000000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 10 * 1e8;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell_2(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(11/10*0.0006 + 0.005) + 0.5*1000000*(11/10*0.0006 + 0.01)
  // 1.5*1000000*(11/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 109*********0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(9/10*0.0006 + 0.005) + 0.5*1000000*(9/10*0.0006 + 0.01)
  // 1.5*1000000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1081*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 10 * 1e8;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_3(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell_3(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 1*1000000*(11/10*0.0006 + 0.005) + 1*1000000*(11/10*0.0006 + 0.01) + 0.5*1000000*(11/10*0.0006 + 0.015)
  // 2.5*1000000*(11/10*0.0006 + 0.015) - 15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 24**********0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 1*1000000*(9/10*0.0006 + 0.005) + 1*1000000*(9/10*0.0006 + 0.01) + 0.5*1000000*(9/10*0.0006 + 0.015)
  // 2.5*1000000*(9/10*0.0006 + 0.015) - 15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 2************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  qty = 5 * 1e8;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂买单
  order_id_2 = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(11/10*0.0006 + 0.005) + 1*1000000*(11/10*0.0006 + 0.01)
  // 2*1000000*(11/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1632*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(9/10*0.0006 + 0.005) + 1*1000000*(9/10*0.0006 + 0.01)
  // 2*1000000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), *************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  qty = 10 * 1e8;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂买单
  order_id_2 = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user2_buy_2(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 1*1000000*(11/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 1*1000000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 1 * 1e8;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_4(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_4.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  // 100000 * 1114000
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 111400000000);

  // 撤单
  FutureOneOfCancelOrderBuilder cancel_build_user1(order_id, symbol, uid_1, coin);
  resp = user1.process(cancel_build_user1.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 1*1000000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 用户加仓，减仓，挂单，撤单
预期结果：用户的risk id,v2mm,pzmm,ordermm,bustprice,liqprice正确刷新
*/
TEST_F(MmAccumulationTest, isolated_mode_single_side_add_sub_size) {
  biz::user_id_t uid_1 = 10000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 10002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Isolated);
  SwitchAccountMode(uid_2, EAccountMode::Isolated);

  Deposit(uid_1, "***********");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 5 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.005
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 0.5*1000000*(11/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 0.5*1000000*(11/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 5033*********);
  // (100000 * 5 + 100000 * 5 * (11/10*0.0006 + 0.1)) / (5 * (1+0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1*********);
  // 0.5*1000000*0.005 = 2500
  // (110000 * 5 - 2500) / 5
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1095000000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 0.5*1000000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 0.5*1000000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 5027*********);
  // (100000 * 5 - 100000 * 5 * (9/10*0.0006 + 0.1)) / (5 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 0.5*1000000*0.005 = 2500
  // (90000 * 5 + 2500) / 5
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 10 * 1e8;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell_2(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(11/10*0.0006 + 0.005) + 0.5*1000000*(11/10*0.0006 + 0.01)
  // 1.5*1000000*(11/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 109*********0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 1.5*1000000*(11/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 1509*********0);
  // (100000 * 1.5 + 100000 * 1.5 * (11/10*0.0006 + 0.1)) / (1.5 * (1+0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1*********);
  // 1 * 1000000 * 0.005 + 0.5 * 1000000 * 0.01 = 10000
  // (110000 * 15 - 10000) / 15
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1093333000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(9/10*0.0006 + 0.005) + 0.5*1000000*(9/10*0.0006 + 0.01)
  // 1.5*1000000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1081*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 1.5*1000000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 15081*********);
  // (100000 * 15 - 100000 * 15 * (9/10*0.0006 + 0.1)) / (15 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 1 * 1000000 * 0.005 + 0.5 * 1000000 * 0.01 = 10000
  // (90000 * 15 + 10000) / 15
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 10 * 1e8;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_3(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell_3(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 1*1000000*(11/10*0.0006 + 0.005) + 1*1000000*(11/10*0.0006 + 0.01) + 0.5*1000000*(11/10*0.0006 + 0.015)
  // 2.5*1000000*(11/10*0.0006 + 0.015) - 15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 24**********0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 2.5*1000000*(11/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 2516*********0);
  // (100000 * 25 + 100000 * 25 * (11/10*0.0006 + 0.1)) / (25 * (1+0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1*********);
  // 1*1000000*0.005 + 1*1000000*0.01 + 0.5*1000000*0.015= 22500
  // (110000 * 25 - 22500) / 25
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1091000000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 1*1000000*(9/10*0.0006 + 0.005) + 1*1000000*(9/10*0.0006 + 0.01) + 0.5*1000000*(9/10*0.0006 + 0.015)
  // 2.5*1000000*(9/10*0.0006 + 0.015) - 15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 2************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 2.5*1000000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 2513*********0);
  // (100000 * 25 - 100000 * 25 * (9/10*0.0006 + 0.1)) / (25 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 1*1000000*0.005 + 1*1000000*0.01 + 0.5*1000000*0.015= 22500
  // (90000 * 25 + 22500) / 25
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 909000000);

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  qty = 5 * 1e8;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂买单
  order_id_2 = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(11/10*0.0006 + 0.005) + 1*1000000*(11/10*0.0006 + 0.01)
  // 2*1000000*(11/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1632*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 2*1000000*(11/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 20132*********);
  // (100000 * 20 + 100000 * 20 * (11/10*0.0006 + 0.1)) / (20 * (1+0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1*********);
  // 1*1000000*0.005 + 1*1000000*0.01 = 15000
  // (110000 * 20 - 15000) / 20
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1092500000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(9/10*0.0006 + 0.005) + 1*1000000*(9/10*0.0006 + 0.01)
  // 2*1000000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), *************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 2*1000000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 20108*********);
  // (100000 * 20 - 100000 * 20 * (9/10*0.0006 + 0.1)) / (20 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 1*1000000*0.005 + 1*1000000*0.01 = 15000
  // (90000 * 20 + 15000) / 20
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 907500000);

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  qty = 10 * 1e8;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂买单
  order_id_2 = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user2_buy_2(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 1*1000000*(11/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 1*1000000*(11/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 10066*********);
  // (100000 * 10 + 100000 * 10 * (11/10*0.0006 + 0.1)) / (10 * (1+0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1*********);
  // 1*1000000*0.005 = 5000
  // (110000 * 10 - 5000) / 10
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1095000000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 1*1000000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 1*1000000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 10054*********);
  // (100000 * 10 - 100000 * 10 * (9/10*0.0006 + 0.1)) / (10 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 1*1000000*0.005 = 5000
  // (90000 * 10 + 5000) / 10
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 1 * 1e8;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_4(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_4.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  // 100000 * 1114000
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 111400000000);
  // 1*1000000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 10054*********);
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);

  // 撤单
  FutureOneOfCancelOrderBuilder cancel_build_user1(order_id, symbol, uid_1, coin);
  resp = user1.process(cancel_build_user1.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 1*1000000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 1*1000000*(11/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 10054*********);
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户Buy方向持有仓位 仓位价值在风险限额的1档，仓位mm按照累进算法计算
        3. 用户Buy方向持有挂单 风险限额在1档，订单mm按照第1档的v2mm计算
        4. 更新sc的max_pz_ord_value，订单成交后仓位的mm计算还是按照第一档计算
        5. 更新标记价格，下平仓单如果升档后会MMR溢出，下单失败
        6. 下COT平仓单没有升档，仓位mm还是用第一档计算，下单成功
预期结果：用户的risk id,v2mm,pzmm,ordermm正确刷新
*/
TEST_F(MmAccumulationTest, cross_mode_single_side_update_max_pz_ord_value) {
  biz::user_id_t uid_1 = 10000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 10002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  Deposit(uid_1, "61000");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 5 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.005
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 0.5*1000000*(11/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 0.5*1000000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 1 * 1e8;

  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  // 1 * 100000 * (0.0006 + 9/10 * 0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 61400000000);

  MmAccumulationTest::UpdateSymbolConfig(symbol, {40000, 50000, 6000000}, {50, 100, 150});

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell_2(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 600000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 600000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 0.6*1000000*(9/10*0.0006 + 0.005)
  // 仓位用的symbol的risk id
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  te->AddMarkPrice(symbol, 90600, 100000, 100000);

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  qty = 0.1 * 1e8;

  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeCannotAffordOrderCost);

  // 用户1挂COT卖单
  FutureOneOfCreateOrderBuilder create_build_user1_sell_cot(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_1, coin);
  create_build_user1_sell_cot.msg.mutable_create_order()->set_close_on_trigger(true);

  resp = user1.process(create_build_user1_sell_cot.Build());
  EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 600000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 10000000);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 0.6*1000000*(9/10*0.0006 + 0.005)
  // 仓位用的symbol的risk id
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户Buy方向持有仓位 仓位价值在风险限额的1档，仓位mm按照累进算法计算
        3. 用户Buy方向持有挂单 风险限额在2档，订单mm按照第2档的v2mm计算
        4. mmr上调，且降低max_pz_ord_value，使用户进入只减仓，用户的pzmm和ordermm没有变化
        5. 用户订单成交，pzmm和ordermm用old mm rate和old deduction计算
        6. 用户下普通平仓单，下单失败
        6. 用户下COT平仓单，平仓后降到第一档，pzmm用第一档的old mm rate和old deduction计算
预期结果：用户的risk id,v2mm,pzmm,ordermm正确刷新
*/
TEST_F(MmAccumulationTest, cross_mode_single_side_update_mmr_reduce_only) {
  biz::user_id_t uid_1 = 10000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 10002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  Deposit(uid_1, "115000");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 10 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.005
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 1*1000000*(11/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 1*1000000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 1 * 1e8;

  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  // 1 * 100000 * (0.0006 + 9/10 * 0.0006 + 0.01)
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 111400000000);

  te->AddMarkPrice(symbol, 90250, 100000, 100000);

  MmAccumulationTest::UpdateSymbolConfig(symbol, {40000, 50000, 6000000}, {50, 100, 150}, true, true, 150, 150, 2);
  for (int i = 0; i < 2; i++) {
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    if (pz_checker.m_msg->user_id() == uid_1) {
      EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
      EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
      EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
      EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
      EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
      EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
      EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
      EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 111400000000);
      EXPECT_EQ(pz_checker.m_msg->maintenance_margin_rate_e4(), 100);
    }
  }

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell_2(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 采用old mmrate和old deduction计算
  // 11*100000*(9/10*0.0006 + 0.01) - 5000
  // 仓位用的symbol的risk id
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  qty = 5 * 1e8;

  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  EXPECT_EQ(resp->ret_code(), error::kEcPositionIsReduceOnly);

  // 用户2挂买单
  order_id_2 = te->GenUUID();
  side = ESide::Buy;
  qty = 10.9 * 1e8;

  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  // 用户1挂COT卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user1_sell_cot(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_1, coin);
  create_build_user1_sell_cot.msg.mutable_create_order()->set_close_on_trigger(true);

  resp = user1.process(create_build_user1_sell_cot.Build());
  EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 10.9 * 1e8);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 0.1*100000*(9/10*0.0006 + 0.01)
  // 仓位用的symbol的risk id
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户Buy方向持有仓位 仓位价值在风险限额的1档，仓位mm按照累进算法计算
        3. 用户Buy方向持有挂单 风险限额在2档，订单mm按照第2档的v2mm计算
        4. mmr上调，且降低max_pz_ord_value
        5. 用户没有进入只减仓，刷新用户的pzmm和ordermm没有，pzmm和ordermm用新的2档计算
        6. 用户订单成交，pzmm和ordermm用新的2档计算
        7. 用户下普通开仓单，pzmm和ordermm用新的3档计算
预期结果：用户的risk id,v2mm,pzmm,ordermm正确刷新
*/
TEST_F(MmAccumulationTest, cross_mode_single_side_update_mmr_not_reduce_only) {
  biz::user_id_t uid_1 = 10000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 10002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  Deposit(uid_1, "*********");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 10 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.005
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 1*1000000*(11/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 1*1000000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 1 * 1e8;

  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  // 1 * 100000 * (0.0006 + 9/10 * 0.0006 + 0.01)
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 111400000000);

  MmAccumulationTest::UpdateSymbolConfig(symbol, {40000, 50000, 6000000}, {50, 100, 150}, true, true, 200, 200, 2);

  for (int i = 0; i < 2; i++) {
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    if (pz_checker.m_msg->user_id() == uid_1) {
      EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
      EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
      EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
      EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
      // 0.0006 + 9/10*0.0006 + 0.02
      EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 2114000);
      EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 2126000);
      // 40000*(0.02-0.01)=400
      // 10 * 100000 * (9/10*0.0006 + 0.02)
      EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 2014*********);
      EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), ************);
      EXPECT_EQ(pz_checker.m_msg->maintenance_margin_rate_e4(), 0);
    }
  }

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell_2(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 2114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 2126000);
  // 采用mmrate和deduction计算
  // 40000*(0.02-0.01)=400
  // 11*100000*(9/10*0.0006 + 0.02) - 400
  // 仓位用的symbol的risk id
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 2219400000000);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_3(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);

  resp = user1.process(create_build_user1_buy_3.Build());
  EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.03
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 3114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 3126000);
  // 50000*(0.03-0.02)+400=900
  // 11*100000*(9/10*0.0006 + 0.03)-900
  // 仓位用的symbol的risk id
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3269400000000);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), ************);
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户Sell方向仓位成交价大于订单价
        3. 用户仓位价值按照定价价格算
预期结果：用户的risk id,v2mm,pzmm,ordermm正确刷新
*/
TEST_F(MmAccumulationTest, cross_mode_single_side_sell_exec_price_greater_than_order_price) {
  biz::user_id_t uid_1 = 10000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 10002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  Deposit(uid_1, "*********");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.42 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  MmAccumulationTest::UpdateSymbolConfig(symbol, {40000, 41000, 6000000}, {50, 100, 150});

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 42000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  price = 90000 * 1e4;
  // 100000*0.95*0.42=39900
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 42000000);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 42000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.005
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 42*********00);
  EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ***********000);
  // 42*********00*(11/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 23772000000);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 42000000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 42*********00);
  EXPECT_EQ(pz_checker.m_msg->entry_price_e8(), ***********000);
  // 42*********00*(9/10*0.0006 + 0.015) - 40*********
  // 40000 * (0.01 - 0.005) = 200
  // 41000 * (0.015 - 0.01) + 200 = 405
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 24768000000);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户之前用非累进的计算方式，触发强平后改用累进的计算方式，安全，下发一条无损降档记录
预期结果：用户的risk id,v2mm,pzmm,ordermm正确刷新
*/
TEST_F(MmAccumulationTest, cross_mode_single_side_liq_withoutloss_donot_oprate) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  Deposit(uid_1, "350000");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  MmAccumulationTest::UpdateSymbolConfig(symbol, {1000000, 2000000, 6000000}, {50, 100, 150}, false);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 30 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;

  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(11/10*0.0006 + 0.015)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 4698*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(9/10*0.0006 + 0.015)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 4662*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 改成累进方式
  MmAccumulationTest::UpdateSymbolConfig(symbol, {1000000, 2000000, 6000000}, {50, 100, 150});

  te->AddMarkPrice(symbol, 89500, 100000, 100000);

  // im = 30*100000*0.1=300000
  // 原mm = 46620
  // 新mm = 30*100000*(9/10*0.0006 + 0.015) - 15000 = 46620 - 15000 = 31620
  // mb = 350000
  // 30*(100000-89500)=315000

  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid_1) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 3*********00000);
  // 30*100000*(9/10*0.0006 + 0.015)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3162*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  auto liq_record = result.m_msg->asset_margin_result().liq_record();
  EXPECT_EQ(liq_record.size(), 2);
  EXPECT_EQ(liq_record[0].liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithoutLoss);
  EXPECT_EQ(liq_record[0].is_finished(), true);
  EXPECT_EQ(liq_record[0].liq_position().old_risk_limit(), 6000000);
  EXPECT_EQ(liq_record[0].liq_position().new_risk_limit(), 6000000);
  EXPECT_EQ(liq_record[0].liq_position().old_mm(), 4662*********);
  EXPECT_EQ(liq_record[0].liq_position().new_mm(), 3162*********);
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户有持仓采用累进的计算方式
        3. 用户有挂单，风险限额在1档
        4. 用户设置风险限额档位3档
        5. 触发强平，直接把风险限额档位降到1档，不是2档
预期结果：用户的risk id,v2mm,pzmm,ordermm正确刷新
*/
TEST_F(MmAccumulationTest, cross_mode_single_side_liq_withoutloss_with_order) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  Deposit(uid_1, "105000");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 1 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;

  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.005
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 1*100000*(11/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 56600000000);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.005
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 1*100000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 55400000000);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户1挂买单
  order_id = te->GenUUID();
  qty = 9 * 1e8;
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.005
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 1*100000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 55400000000);
  // 9*100000*(0.0006 + 9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), ************);

  FutureOneOfSetRiskIdBuilder set_risk_id_build_user1(symbol, coin, uid_1, 3);
  resp = user1.process(set_risk_id_build_user1.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.005
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 1*100000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 55400000000);
  // 9*100000*(0.0006 + 9/10*0.0006 + 0.015)
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 1452600000000);

  // im = 10*100000*0.1=100000
  // 原mm = 554+5526=6080
  // 新mm = 554+14526= 15080
  // mb = 105000
  // 10*(100000-91000)=90000

  te->AddMarkPrice(symbol, 91000, 100000, 100000);

  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid_1) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), ***********000);
  // 1*100000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 55400000000);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), ************);
  auto liq_record = result.m_msg->asset_margin_result().liq_record();
  EXPECT_EQ(liq_record.size(), 2);
  EXPECT_EQ(liq_record[0].liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithoutLoss);
  EXPECT_EQ(liq_record[0].is_finished(), true);
  EXPECT_EQ(liq_record[0].liq_position().old_risk_limit(), 6000000);
  EXPECT_EQ(liq_record[0].liq_position().new_risk_limit(), 1000000);
  EXPECT_EQ(liq_record[0].liq_position().old_mm(), 55400000000);
  EXPECT_EQ(liq_record[0].liq_position().new_mm(), 55400000000);
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户有BTC持仓采用非累进的计算方式
        3. 用户有XRP持仓采用累进的计算方式
        3. 用户有XRP挂单，风险限额在2档
        4. 用户设置XRP风险限额档位3档
        5. 触发强平，BTC采用累进计算方式，XRP降档到2档
预期结果：用户的risk id,v2mm,pzmm,ordermm正确刷新
*/
TEST_F(MmAccumulationTest, cross_mode_single_side_liq_withoutloss_multi_symbol) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol_btc = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  Deposit(uid_1, "550000");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol_btc, 100000, 100000, 100000);

  MmAccumulationTest::UpdateSymbolConfig(symbol_btc, {1000000, 2000000, 6000000}, {50, 100, 150}, false);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 30 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;

  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol_btc, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(11/10*0.0006 + 0.015)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 4698*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(9/10*0.0006 + 0.015)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 4662*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  auto symbol_xrp = ESymbol::XRPUSDT;

  te->AddMarkPrice(symbol_xrp, 100000, 100000, 100000);

  MmAccumulationTest::UpdateSymbolConfig(symbol_xrp, {1000000, 2000000, 6000000}, {50, 100, 150});

  qty = 20 * 1e8;
  price = 100000 * 1e4;

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_xrp(order_id, symbol_xrp, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_xrp.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 2*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  qty = 11 * 1e8;
  side = ESide::Sell;

  FutureOneOfCreateOrderBuilder create_build_user2_sell_xrp(order_id_2, symbol_xrp, pz_index, side, order_type, qty,
                                                            price, ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_xrp.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 1*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 11*100000*(11/10*0.0006 + 0.01)-5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 11*100000*(9/10*0.0006 + 0.01)-5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  // 9 * 100000 * 1114000
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 1002600000000);

  FutureOneOfSetRiskIdBuilder set_risk_id_build_user1(symbol_xrp, coin, uid_1, 3);
  resp = user1.process(set_risk_id_build_user1.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  // 9*100000*(0.0006 + 9/10*0.0006 + 0.015)
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 1452600000000);

  // 改成累进方式
  MmAccumulationTest::UpdateSymbolConfig(symbol_btc, {1000000, 2000000, 6000000}, {50, 100, 150});

  te->AddMarkPrice(symbol_btc, 83400, 100000, 100000);

  // im = 50*100000*0.1=500000
  // 原mm = 46620 + 6594 + 14526 = 67740
  // btc新mm = 30*100000*(9/10*0.0006 + 0.015) - 15000 = 46620 - 15000 = 31620
  // xrp降riskid新mm = 6594 + 9*100000*(0.0006 + 9/10*0.0006 + 0.01) = 16620
  // 新mm = 31620 + 16620 = 48240
  // mb = 550000
  // 30*(100000-83400)=498000
  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid_1) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(2, result.m_msg->futures_margin_result().affected_positions_size());
  for (int i = 0; i < 2; i++) {
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(i);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    if (pz_checker.m_msg->symbol() == symbol_btc) {
      EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
      EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
      EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
      EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
      EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
      // 0.0006 + 9/10*0.0006 + 0.015
      EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
      EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
      EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 3*********00000);
      // 30*100000*(9/10*0.0006 + 0.015)
      EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3162*********);
      EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
    }
    if (pz_checker.m_msg->symbol() == symbol_xrp) {
      EXPECT_EQ(pz_checker.m_msg->size_x(), 1*********);
      EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
      EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
      EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
      EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
      // 0.0006 + 9/10*0.0006 + 0.01
      EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
      EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
      EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 1***********000);
      EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
      EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 1002600000000);
    }
  }

  auto liq_record = result.m_msg->asset_margin_result().liq_record();
  EXPECT_EQ(liq_record.size(), 3);
  for (int i = 0; i < 2; i++) {
    if (liq_record[i].liq_position().symbol_id() == symbol_btc) {
      EXPECT_EQ(liq_record[i].liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithoutLoss);
      EXPECT_EQ(liq_record[i].liq_position().old_risk_limit(), 6000000);
      EXPECT_EQ(liq_record[i].liq_position().new_risk_limit(), 6000000);
      EXPECT_EQ(liq_record[i].liq_position().old_mm(), 4662*********);
      EXPECT_EQ(liq_record[i].liq_position().new_mm(), 3162*********);
    }
    if (liq_record[i].liq_position().symbol_id() == symbol_xrp) {
      EXPECT_EQ(liq_record[i].liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithoutLoss);
      EXPECT_EQ(liq_record[i].liq_position().old_risk_limit(), 6000000);
      EXPECT_EQ(liq_record[i].liq_position().new_risk_limit(), 2000000);
      EXPECT_EQ(liq_record[i].liq_position().old_mm(), ************);
      EXPECT_EQ(liq_record[i].liq_position().new_mm(), ************);
    }
  }
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 用户之前用非累进的计算方式
        3. 用户持有订单，风险档位在2档
        4. 用户设置风险档位为3档
        5. 触发强平后改用累进的计算方式，安全，调整风险限额为2档，并下发一条无损降档记录
预期结果：用户的risk id,v2mm,pzmm,ordermm,liqprice,bustprice正确刷新
*/
TEST_F(MmAccumulationTest, isolated_mode_single_side_liq_withoutloss_donot_oprate) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Isolated);
  SwitchAccountMode(uid_2, EAccountMode::Isolated);

  Deposit(uid_1, "***********");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 15 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  MmAccumulationTest::UpdateSymbolConfig(symbol, {1000000, 2000000, 6000000}, {50, 100, 150}, false);

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1.5*1000000*(11/10*0.0006 + 0.01)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 159*********0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 1509*********0);
  // (100000 * 15 + 100000 * 15 * (11/10*0.0006 + 0.1)) / (15 * (1+0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1*********);
  // 1.5*1000000*(0.01) = 15000
  // (110000 * 15 - 15000) / 15
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1090000000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1.5*1000000*(9/10*0.0006 + 0.01)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1581*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 15081*********);
  // (100000 * 15 - 100000 * 15 * (9/10*0.0006 + 0.1)) / (15 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 1.5*1000000*(0.01) = 15000
  // (90000 * 15 + 15000) / 15
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 910000000);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  qty = 5 * 1e8;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), *********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1.5*1000000*(9/10*0.0006 + 0.01)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1581*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 15081*********);
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 910000000);

  FutureOneOfSetRiskIdBuilder set_risk_id_build_user1(symbol, coin, uid_1, 3);
  resp = user1.process(set_risk_id_build_user1.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1581*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 15081*********);
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 910000000);

  // 改成累进方式
  MmAccumulationTest::UpdateSymbolConfig(symbol, {1000000, 2000000, 6000000}, {50, 100, 150});

  te->AddMarkPrice(symbol, 90800, 100000, 100000);

  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid_1) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), **********00000);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  // 15*100000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1081*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 15081*********);
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 1.5*1000000*(0.01) - 5000 = 10000
  // (90000 * 15 + 10000) / 15
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);
  auto liq_record = result.m_msg->asset_margin_result().liq_record();
  EXPECT_EQ(liq_record.size(), 1);
  EXPECT_EQ(liq_record[0].liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithoutLoss);
  EXPECT_EQ(liq_record[0].is_finished(), true);
  EXPECT_EQ(liq_record[0].liq_position().old_risk_limit(), 6000000);
  EXPECT_EQ(liq_record[0].liq_position().new_risk_limit(), 2000000);
  EXPECT_EQ(liq_record[0].liq_position().old_mm(), 1581*********);
  EXPECT_EQ(liq_record[0].liq_position().new_mm(), 1081*********);
  EXPECT_EQ(liq_record[0].liq_position().bust_price(), *********);
  EXPECT_EQ(liq_record[0].liq_position().liq_price(), *********);
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 用户有持仓风险限额在3档，调整标记价格依次触发有损降档
预期结果：用户的risk id,v2mm,pzmm,ordermm,liqprice,bustprice正确刷新
*/
TEST_F(MmAccumulationTest, isolated_mode_single_side_liq_withloss) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Isolated);
  SwitchAccountMode(uid_2, EAccountMode::Isolated);

  Deposit(uid_1, "***********");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 30 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(11/10*0.0006 + 0.015) - 15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3198*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 30*100000*(11/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 30198*********);
  // (100000 * 30 + 100000 * 30 * (11/10*0.0006 + 0.1)) / (30 * (1+0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1*********);
  // 30*100000*(0.015) - 15000 = 30000
  // (110000 * 30 - 30000) / 30
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1090000000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(9/10*0.0006 + 0.015) - 15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3162*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 30*100000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 30162*********);
  // (100000 * 30 - 100000 * 30 * (9/10*0.0006 + 0.1)) / (30 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 30*100000*(0.015) - 15000 = 30000
  // (90000 * 30 + 30000) / 30
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 910000000);

  // 用户2挂买单
  order_id_2 = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(11/10*0.0006 + 0.015) - 15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3198*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 30198*********);
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1*********);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1090000000);

  // 20*100000*(0.01) - 5000 = 15000
  // 2: (90000 * 20 + 15000) / 20 = 90750
  // 10*100000*(0.005) = 5000
  // 1: (90000 * 10 + 5000) / 10 = 90500

  te->AddMarkPrice(symbol, 90800, 100000, 100000);

  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid_1) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 1*********);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 3*********00000);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  // 15*100000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3162*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 30*100000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 30162*********);
  // (100000 * 30 - 100000 * 30 * (9/10*0.0006 + 0.1)) / (30 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 30*100000*(0.015) - 15000 = 30000
  // (90000 * 30 + 30000) / 30
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 910000000);

  auto liq_record = result.m_msg->asset_margin_result().liq_record();
  EXPECT_EQ(liq_record.size(), 1);
  EXPECT_EQ(liq_record[0].liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithLoss);
  EXPECT_EQ(liq_record[0].is_finished(), false);
  EXPECT_EQ(liq_record[0].liq_position().old_risk_limit(), 0);
  EXPECT_EQ(liq_record[0].liq_position().new_risk_limit(), 0);

  // user1减仓单
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 20*100000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), *************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 20*100000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 20108*********);
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 20*100000*(0.01) - 5000 = 15000
  // (90000 * 20 + 15000) / 20
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 907500000);
  liq_record = result.m_msg->asset_margin_result().liq_record();
  EXPECT_EQ(liq_record.size(), 1);
  EXPECT_EQ(liq_record[0].liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithLoss);
  EXPECT_EQ(liq_record[0].is_finished(), true);
  EXPECT_EQ(liq_record[0].liq_position().old_risk_limit(), 6000000);
  EXPECT_EQ(liq_record[0].liq_position().new_risk_limit(), 2000000);
  EXPECT_EQ(liq_record[0].liq_position().old_mm(), 3162*********);
  EXPECT_EQ(liq_record[0].liq_position().new_mm(), *************);
  EXPECT_EQ(liq_record[0].liq_position().bust_price(), *********);
  EXPECT_EQ(liq_record[0].liq_position().liq_price(), 907500000);

  // user2成交回报
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  te->AddMarkPrice(symbol, 90600, 100000, 100000);

  // shard_key为0为旧一轮计算结尾，后面的请求触发新一轮计算
  event = std::make_shared<event::ReCalcEvent>(0);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  event = std::make_shared<event::ReCalcEvent>(0);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 1000100000);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 20*100000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), *************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 20*100000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 20108*********);
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 20*100000*(0.01) - 5000 = 15000
  // (90000 * 20 + 15000) / 20
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 907500000);

  liq_record = result.m_msg->asset_margin_result().liq_record();
  EXPECT_EQ(liq_record.size(), 1);
  EXPECT_EQ(liq_record[0].liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithLoss);
  EXPECT_EQ(liq_record[0].is_finished(), false);

  // user1减仓单
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 999900000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 9.999*100000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 9.999*100000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 10052994600000);
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);

  // user2减仓单
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户之前用非累进的计算方式，触发强平后改用累进的计算方式，并触发有损降档
预期结果：用户的risk id,v2mm,pzmm,ordermm正确刷新
*/
TEST_F(MmAccumulationTest, cross_mode_single_side_liq_withloss_donot_oprate) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  Deposit(uid_1, "350000");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  MmAccumulationTest::UpdateSymbolConfig(symbol, {1000000, 2000000, 6000000}, {50, 100, 150}, false);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 30 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;

  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(11/10*0.0006 + 0.015)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 4698*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(9/10*0.0006 + 0.015)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 4662*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 用户2挂买单
  order_id_2 = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(11/10*0.0006 + 0.015)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 4698*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 改成累进方式
  MmAccumulationTest::UpdateSymbolConfig(symbol, {1000000, 2000000, 6000000}, {50, 100, 150});

  te->AddMarkPrice(symbol, 89000, 100000, 100000);

  // im = 30*100000*0.1=300000
  // 原mm = 46620
  // 新mm = 30*100000*(9/10*0.0006 + 0.015) - 15000 = 46620 - 15000 = 31620
  // mb = 350000
  // 30*(100000-89000)=330000
  // 2: 20*100000*(9/10*0.0006 + 0.01) - 5000 = 16080

  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid_1) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 1*********);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 3*********00000);
  // 30*100000*(9/10*0.0006 + 0.015)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 4662*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  auto liq_record = result.m_msg->asset_margin_result().liq_record();
  EXPECT_EQ(liq_record.size(), 1);
  EXPECT_EQ(liq_record[0].liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithLoss);
  EXPECT_EQ(liq_record[0].is_finished(), false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 2*********00000);
  // 20*100000*(9/10*0.0006 + 0.01)-5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), *************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  liq_record = result.m_msg->asset_margin_result().liq_record();
  EXPECT_EQ(liq_record.size(), 1);
  EXPECT_EQ(liq_record[0].liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithLoss);
  EXPECT_EQ(liq_record[0].is_finished(), true);
  EXPECT_EQ(liq_record[0].liq_position().old_risk_limit(), 6000000);
  EXPECT_EQ(liq_record[0].liq_position().new_risk_limit(), 2000000);
  EXPECT_EQ(liq_record[0].liq_position().old_mm(), 4662*********);
  EXPECT_EQ(liq_record[0].liq_position().new_mm(), *************);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户有XRP持仓采用非累进的计算方式
        3. 用户有BTC持仓采用累进的计算方式
        4. 触发强平，XRP采用累进计算方式，BTC降档到2档
预期结果：用户的risk id,v2mm,pzmm,ordermm正确刷新
*/
TEST_F(MmAccumulationTest, cross_mode_single_side_liq_withloss_multi_symbol) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol_btc = ESymbol::BTCUSDT;
  auto symbol_xrp = ESymbol::XRPUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  Deposit(uid_1, "460000");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol_xrp, 100000, 100000, 100000);

  MmAccumulationTest::UpdateSymbolConfig(symbol_xrp, {1000000, 2000000, 6000000}, {50, 100, 150}, false);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 15 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol_xrp, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;

  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol_xrp, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 15*100000*(11/10*0.0006 + 0.01)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 159*********0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 15*100000*(9/10*0.0006 + 0.01)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1581*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  te->AddMarkPrice(symbol_btc, 100000, 100000, 100000);

  MmAccumulationTest::UpdateSymbolConfig(symbol_btc, {1000000, 2000000, 6000000}, {50, 100, 150});

  qty = 30 * 1e8;
  price = 100000 * 1e4;

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_btc(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_btc.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;

  FutureOneOfCreateOrderBuilder create_build_user2_sell_btc(order_id_2, symbol_btc, pz_index, side, order_type, qty,
                                                            price, ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_btc.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(11/10*0.0006 + 0.015)-15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3198*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(9/10*0.0006 + 0.015)-15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3162*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  order_id_2 = te->GenUUID();
  side = ESide::Buy;

  FutureOneOfCreateOrderBuilder create_build_user2_buy_btc(order_id_2, symbol_btc, pz_index, side, order_type, qty,
                                                           price, ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_btc.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(11/10*0.0006 + 0.015)-15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3198*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 改成累进方式
  MmAccumulationTest::UpdateSymbolConfig(symbol_xrp, {1000000, 2000000, 6000000}, {50, 100, 150});

  te->AddMarkPrice(symbol_btc, 86000, 100000, 100000);

  // im = 45*100000*0.1=450000
  // 原mm = 15810 + 31620 = 47430
  // btc新mm = 15*100000*(9/10*0.0006 + 0.01) - 5000 = 10810
  // xrp mm = 31620
  // 新mm = 31620 + 10810 = 42430
  // mb = 460000
  // btc 2: 20*100000*(9/10*0.0006 + 0.01) - 5000 = 16080
  // 30*(100000-86000)=420000
  // 16080+10810=26890
  // 31620+15810

  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid_1) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(2, result.m_msg->futures_margin_result().affected_positions_size());
  for (int i = 0; i < 2; i++) {
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(i);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    if (pz_checker.m_msg->symbol() == symbol_xrp) {
      EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
      EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
      EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
      EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
      EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
      EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
      EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
      // 15*100000*(9/10*0.0006 + 0.01)-5000
      EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1081*********);
      EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
    }
    if (pz_checker.m_msg->symbol() == symbol_btc) {
      EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
      EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
      EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
      EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
      EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 1*********);
      EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
      EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
      EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3162*********);
      EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
    }
  }

  auto liq_record = result.m_msg->asset_margin_result().liq_record();
  EXPECT_EQ(liq_record.size(), 1);
  EXPECT_EQ(liq_record[0].liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithLoss);
  EXPECT_EQ(liq_record[0].is_finished(), false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 20*100000*(9/10*0.0006 + 0.01)-5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), *************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();  // user1 liqexec 刷新accountinfo
  ASSERT_NE(result.m_msg.get(), nullptr);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // im = 45*100000*0.1=450000
  // mb = 460000
  // btc 2: 20*100000*(9/10*0.0006 + 0.01) - 5000 = 16080
  // 原mm: 16080+10810=26890
  // btc 1: 10*100000*(9/10*0.0006 + 0.005) = 5540
  // 20*(100000-78000)=440000
  // 5540+10810=16350
  te->AddMarkPrice(symbol_btc, 78000, 100000, 100000);
  // shard_key为0为旧一轮计算结尾，后面的请求触发新一轮计算
  event = std::make_shared<event::ReCalcEvent>(0);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  event = std::make_shared<event::ReCalcEvent>(0);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(2, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->symbol(), symbol_btc);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 1000100000);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 20*100000*(9/10*0.0006 + 0.01)-5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), *************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(1);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->symbol(), symbol_xrp);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 15*100000*(9/10*0.0006 + 0.01)-5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1081*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  liq_record = result.m_msg->asset_margin_result().liq_record();
  EXPECT_EQ(liq_record.size(), 1);
  EXPECT_EQ(liq_record[0].liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithLoss);
  EXPECT_EQ(liq_record[0].is_finished(), false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 999900000);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->symbol(), symbol_btc);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 9.999*100000*(9/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户有XRP持仓采用非累进的计算方式，且risk id比实际小，按照累进计算mm大于原来的mm
        3. 用户有BTC持仓采用累进的计算方式
        4. 触发强平，BTC降档到2档，XRP不变
预期结果：用户的risk id,v2mm,pzmm,ordermm正确刷新
*/
TEST_F(MmAccumulationTest, cross_mode_single_side_liq_withloss_multi_symbol_risk_id_less_than_real) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol_btc = ESymbol::BTCUSDT;
  auto symbol_xrp = ESymbol::XRPUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  Deposit(uid_1, "***********");
  Deposit(uid_2, "415000");

  te->AddMarkPrice(symbol_xrp, 100000, 100000, 100000);

  MmAccumulationTest::UpdateSymbolConfig(symbol_xrp, {1000000, 2000000, 6000000}, {50, 100, 150}, false);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 10.5 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol_xrp, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  price = 90000 * 1e4;

  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol_xrp, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.005
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
  // 10.5*100000*(11/10*0.0006 + 0.005)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 10.5*100000*(9/10*0.0006 + 0.01)
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1106700000000);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  te->AddMarkPrice(symbol_btc, 100000, 100000, 100000);

  MmAccumulationTest::UpdateSymbolConfig(symbol_btc, {1000000, 2000000, 6000000}, {50, 100, 150});

  qty = 30 * 1e8;
  price = 100000 * 1e4;

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_btc(order_id, symbol_btc, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_btc.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;

  FutureOneOfCreateOrderBuilder create_build_user2_sell_btc(order_id_2, symbol_btc, pz_index, side, order_type, qty,
                                                            price, ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_btc.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(11/10*0.0006 + 0.015)-15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3198*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(9/10*0.0006 + 0.015)-15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3162*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  order_id = te->GenUUID();
  side = ESide::Sell;

  FutureOneOfCreateOrderBuilder create_build_user1_sell_btc(order_id, symbol_btc, pz_index, side, order_type, qty,
                                                            price, ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_btc.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 3*********);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(9/10*0.0006 + 0.015)-15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3162*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  // 改成累进方式
  MmAccumulationTest::UpdateSymbolConfig(symbol_xrp, {1000000, 2000000, 6000000}, {50, 100, 150});

  te->AddMarkPrice(symbol_btc, 112800, 100000, 100000);

  // im = 40.5*100000*0.1=405000
  // 原mm = 5943 + 31980 = 37923
  // xrp新mm = 10.5*100000*(11/10*0.0006 + 0.01) - 5000 = 6193
  // mb = 415000
  // btc 2: 20*100000*(11/10*0.0006 + 0.01) - 5000 = 16320
  // 30*(112800-100000)=384000

  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid_2) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(2, result.m_msg->futures_margin_result().affected_positions_size());
  for (int i = 0; i < 2; i++) {
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(i);
    ASSERT_NE(pz_checker.m_msg, nullptr);
    if (pz_checker.m_msg->symbol() == symbol_xrp) {
      EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
      EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
      EXPECT_EQ(pz_checker.m_msg->risk_id(), 1);
      EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
      EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
      EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 614000);
      EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 626000);
      // 10.5*100000*(11/10*0.0006 + 0.005)
      EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), ************);
      EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
    }
    if (pz_checker.m_msg->symbol() == symbol_btc) {
      EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
      EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
      EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
      EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 1*********);
      EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
      EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
      EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
      EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3198*********);
      EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
    }
  }

  auto liq_record = result.m_msg->asset_margin_result().liq_record();
  EXPECT_EQ(liq_record.size(), 1);
  EXPECT_EQ(liq_record[0].liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithLoss);
  EXPECT_EQ(liq_record[0].is_finished(), false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 20*100000*(11/10*0.0006 + 0.01)-5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1632*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 用户有持仓风险限额在3档
        3. 触发强平接管
预期结果：用户的risk id,v2mm,pzmm,ordermm,liqprice,bustprice正确刷新
*/
TEST_F(MmAccumulationTest, isolated_mode_single_side_liq_takeover) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Isolated);
  SwitchAccountMode(uid_2, EAccountMode::Isolated);

  Deposit(uid_1, "***********");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 25 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 2*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 2*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 25*100000*(11/10*0.0006 + 0.015) - 15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 24**********0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 25*100000*(11/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 2516*********0);
  // (100000 * 25 + 100000 * 25 * (11/10*0.0006 + 0.1)) / (25 * (1+0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1*********);
  // 25*100000*(0.015) - 15000 = 22500
  // (110000 * 25 - 22500) / 25
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1091000000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 25*100000*(9/10*0.0006 + 0.015) - 15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 2************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  // 25*100000*(9/10*0.0006 + 0.1)
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 2513*********0);
  // (100000 * 25 - 100000 * 25 * (9/10*0.0006 + 0.1)) / (25 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 25*100000*(0.015) - 15000 = 22500
  // (90000 * 25 + 22500) / 25
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 909000000);

  // 10*100000*(0.005) = 5000
  // (90000 * 10 + 5000) / 10 = 90500

  te->AddMarkPrice(symbol, 90000, 100000, 100000);

  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid_1) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 2*********);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 2************);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 2513*********0);
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 909000000);
  auto liq_record = result.m_msg->asset_margin_result().liq_record();
  EXPECT_EQ(liq_record.size(), 1);
  EXPECT_EQ(liq_record[0].liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
  EXPECT_EQ(liq_record[0].is_finished(), false);
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 用户有持仓风险限额在3档
        3. 触发强平接管
预期结果：用户的risk id,v2mm,pzmm,ordermm正确刷新
*/
TEST_F(MmAccumulationTest, cross_mode_single_side_liq_takeover) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  Deposit(uid_1, "350000");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 30 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;

  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), 3*********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(11/10*0.0006 + 0.015) - 15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3198*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  // 30*100000*(9/10*0.0006 + 0.015) - 15000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3162*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  te->AddMarkPrice(symbol, 88500, 100000, 100000);

  // im = 30*100000*0.1=300000
  // 原mm = 31620
  // 新mm = 10*100000*(9/10*0.0006 + 0.005) = 5540
  // mb = 350000
  // 30*(100000-88500)=345000

  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid_1) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 3*********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 3);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 3*********);
  // 0.0006 + 9/10*0.0006 + 0.015
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1614000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1626000);
  EXPECT_EQ(pz_checker.m_msg->session_value_e8(), 3*********00000);
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 3162*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  auto liq_record = result.m_msg->asset_margin_result().liq_record();
  EXPECT_EQ(liq_record.size(), 1);
  EXPECT_EQ(liq_record[0].liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
  EXPECT_EQ(liq_record[0].is_finished(), false);
}

/*
测试场景：1. 用户为逐仓单仓模式
        2. 只更新风险限额的最大可开价值，用户强平价刷新后爆仓
预期结果：用户的risk id,v2mm,pzmm,ordermm,liqprice,bustprice正确刷新
*/
TEST_F(MmAccumulationTest, isolated_mode_reduce_max_pz_ord_value_trigger_liq) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Isolated);
  SwitchAccountMode(uid_2, EAccountMode::Isolated);

  Deposit(uid_1, "***********");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 15 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(11/10*0.0006 + 0.005) + 0.5*1000000*(11/10*0.0006 + 0.01)
  // 1.5*1000000*(11/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 109*********0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 1509*********0);
  // (100000 * 15 + 100000 * 15 * (11/10*0.0006 + 0.1)) / (15 * (1+0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1*********);
  // 1*1000000*0.005+0.5*1000000*0.01 = 10000
  // 1.5*1000000*(0.01) - 5000 = 10000
  // (110000 * 15 - 10000) / 15
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1093333000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(9/10*0.0006 + 0.005) + 0.5*1000000*(9/10*0.0006 + 0.01)
  // 1.5*1000000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1081*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 15081*********);
  // (100000 * 15 - 100000 * 15 * (9/10*0.0006 + 0.1)) / (15 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 1*1000000*0.005+0.5*1000000*0.01 = 10000
  // 1.5*1000000*(0.01) - 5000 = 10000
  // (90000 * 15 + 10000) / 15
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);

  // 0.4*1000000*(0.005) + 1.1*1000000*(0.01)=13000
  // (90000 * 15 + 13000) / 15 = 90866.66666667
  MmAccumulationTest::UpdateSymbolConfig(symbol, {400000, 2000000, 6000000}, {50, 100, 150}, true, false, 100, 100, 2,
                                         true);
  for (int i = 0; i < 2; i++) {
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    if (pz_checker.m_msg->user_id() == uid_1) {
      EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
      EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 908667000);
    }
  }

  te->AddMarkPrice(symbol, 90800, 90800, 90800);

  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid_1) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
}

/*
测试场景：1. 用户为逐仓双仓模式
        2. 只更新风险限额的最大可开价值，用户强平价刷新后爆仓
预期结果：用户的risk id,v2mm,pzmm,ordermm,liqprice,bustprice正确刷新
*/
TEST_F(MmAccumulationTest, isolated_mode_both_side_reduce_max_pz_ord_value_trigger_liq) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Isolated);
  SwitchAccountMode(uid_2, EAccountMode::Isolated);

  SwitchPositionMode(uid_1, EPositionMode::BothSide);
  SwitchPositionMode(uid_2, EPositionMode::BothSide);

  Deposit(uid_1, "***********");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Buy;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 15 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  pz_index = EPositionIndex::Sell;
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 2);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(11/10*0.0006 + 0.005) + 0.5*1000000*(11/10*0.0006 + 0.01)
  // 1.5*1000000*(11/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 109*********0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 1509*********0);
  // (100000 * 15 + 100000 * 15 * (11/10*0.0006 + 0.1)) / (15 * (1+0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), 1*********);
  // 1*1000000*0.005+0.5*1000000*0.01 = 10000
  // 1.5*1000000*(0.01) - 5000 = 10000
  // (110000 * 15 - 10000) / 15
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1093333000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(9/10*0.0006 + 0.005) + 0.5*1000000*(9/10*0.0006 + 0.01)
  // 1.5*1000000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1081*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);
  EXPECT_EQ(pz_checker.m_msg->position_balance_e8(), 15081*********);
  // (100000 * 15 - 100000 * 15 * (9/10*0.0006 + 0.1)) / (15 * (1-0.0006))
  EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
  // 1*1000000*0.005+0.5*1000000*0.01 = 10000
  // 1.5*1000000*(0.01) - 5000 = 10000
  // (90000 * 15 + 10000) / 15
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);

  // 0.4*1000000*(0.005) + 1.1*1000000*(0.01)=13000
  // (90000 * 15 + 13000) / 15 = 90866.66666667
  MmAccumulationTest::UpdateSymbolConfig(symbol, {400000, 2000000, 6000000}, {50, 100, 150}, true, false, 100, 100, 2,
                                         true);
  for (int i = 0; i < 2; i++) {
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    if (pz_checker.m_msg->user_id() == uid_1) {
      EXPECT_EQ(pz_checker.m_msg->bust_price_x(), *********);
      EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 908667000);
    }
  }

  te->AddMarkPrice(symbol, 90800, 90800, 90800);

  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid_1) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
}

/*
测试场景：1. 用户为全仓单仓模式
        2. 只更新风险限额的最大可开价值，用户MMR变大后爆仓
预期结果：用户在更新风险限额的最大可开价值后爆仓
*/
TEST_F(MmAccumulationTest, cross_mode_reduce_max_pz_ord_value_trigger_liq) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSDT;
  auto coin = ECoin::USDT;

  SwitchAccountMode(uid_1, EAccountMode::Cross);
  SwitchAccountMode(uid_2, EAccountMode::Cross);

  Deposit(uid_1, "155000");
  Deposit(uid_2, "***********");

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 15 * 1e8;
  biz::price_x_t price = 100000 * 1e4;

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_1);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(order_checker.m_msg->qty_x(), **********);
  EXPECT_EQ(order_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions_size(), 1);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_2);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  // 0.0006 + 9/10*0.0006 + 0.01
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(11/10*0.0006 + 0.005) + 0.5*1000000*(11/10*0.0006 + 0.01)
  // 1.5*1000000*(11/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 109*********0);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), **********);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
  EXPECT_EQ(pz_checker.m_msg->risk_id(), 2);
  EXPECT_EQ(pz_checker.m_msg->buy_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->sell_leaves_qty_x(), 0);
  EXPECT_EQ(pz_checker.m_msg->buy_value_to_mm_e8(), 1114000);
  EXPECT_EQ(pz_checker.m_msg->sell_value_to_mm_e8(), 1126000);
  // 1*1000000*(9/10*0.0006 + 0.005) + 0.5*1000000*(9/10*0.0006 + 0.01)
  // 1.5*1000000*(9/10*0.0006 + 0.01) - 5000
  EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1081*********);
  EXPECT_EQ(pz_checker.m_msg->order_mm_with_fee_e8(), 0);

  MmAccumulationTest::UpdateSymbolConfig(symbol, {100000, 2000000, 6000000}, {50, 100, 150}, true, false, 100, 100, 2,
                                         true);
  for (int i = 0; i < 2; i++) {
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
    if (pz_checker.m_msg->user_id() == uid_1) {
      // 0.1*1000000*(9/10*0.0006 + 0.005) + 1.4*1000000*(9/10*0.0006 + 0.01)
      EXPECT_EQ(pz_checker.m_msg->position_mm_with_fee_e8(), 1531*********);
    }
  }
  // fee = 15*100000*0.0001=150
  // 155000
  // 15310
  // 10810
  // 15 *(100000 - 90500) = 142500
  te->AddMarkPrice(symbol, 90500, 90500, 90500);

  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid_1) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->user_id(), uid_1);
}

std::shared_ptr<tmock::CTradeAppMock> MmAccumulationTest::te;
