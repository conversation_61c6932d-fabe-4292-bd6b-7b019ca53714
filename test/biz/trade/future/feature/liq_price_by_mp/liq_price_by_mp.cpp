#include "test/biz/trade/future/feature/liq_price_by_mp/liq_price_by_mp.hpp"

#include <string>

#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/futures/modules/orderbiz/activeorderbiz/active_order_biz.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/future/func/draft_pkg_builder.h"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

int32_t LiqPriceByMpTest::Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t LiqPriceByMpTest::SwitchAccountMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

TEST_F(LiqPriceByMpTest, liq_price_by_mp_inverse) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol::BTCUSD;
  auto coin = ECoin::BTC;

  EXPECT_EQ(SwitchAccountMode(uid_1, EAccountMode::Isolated), 0);
  EXPECT_EQ(SwitchAccountMode(uid_2, EAccountMode::Isolated), 0);

  EXPECT_EQ(Deposit(uid_1, "10000", coin), 0);
  EXPECT_EQ(Deposit(uid_2, "10000", coin), 0);

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 9000;
  biz::price_x_t price = 100000 * 1e4;
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 9000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1104970000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 1105555000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 913245000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 913640000);

  FutureOneOfSetLeverageBuilder user1_set_leverage(symbol, coin, uid_1, 2000, 2000);
  resp = user1.process(user1_set_leverage.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.RefFutureMarginResult().RefRelatedPositionSize(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 956940000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 957145000);

  FutureOneOfSetLeverageBuilder user1_set_leverage_1(symbol, coin, uid_1, 100, 100);
  resp = user1.process(user1_set_leverage_1.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.RefFutureMarginResult().RefRelatedPositionSize(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 501255000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 502500000);

  FutureOneOfSetLeverageBuilder user2_set_leverage(symbol, coin, uid_2, 2000, 2000);
  resp = user2.process(user2_set_leverage.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.RefFutureMarginResult().RefRelatedPositionSize(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1047120000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 1047365000);

  FutureOneOfSetLeverageBuilder user2_set_leverage_1(symbol, coin, uid_2, 100, 100);
  resp = user2.process(user2_set_leverage_1.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.RefFutureMarginResult().RefRelatedPositionSize(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 9999990000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 9999990000);

  for (int i = 0; i < 200; i++) {
    qty = 100000;
    order_id = te->GenUUID();
    side = ESide::Buy;
    FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
    resp = user1.process(create_build_user1_buy_2.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    order_id = te->GenUUID();
    side = ESide::Sell;
    FutureOneOfCreateOrderBuilder create_build_user2_sell_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_2, coin);
    resp = user2.process(create_build_user2_sell_2.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  qty = 1000;

  order_id = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_3(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell_3(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 20010000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 9999990000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 9999990000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), *********);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), *********);
}

TEST_F(LiqPriceByMpTest, liq_price_by_mp_linear) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  auto symbol = ESymbol(300);
  auto coin = ECoin::USDT;

  EXPECT_EQ(SwitchAccountMode(uid_1, EAccountMode::Isolated), 0);
  EXPECT_EQ(SwitchAccountMode(uid_2, EAccountMode::Isolated), 0);

  EXPECT_EQ(Deposit(uid_1, "********", coin), 0);
  EXPECT_EQ(Deposit(uid_2, "********", coin), 0);

  te->AddMarkPrice(symbol, 100000, 100000, 100000);

  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = ********;
  biz::price_x_t price = 100000 * 1e4;
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1095000000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 1094527000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 905000000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 904523000);

  FutureOneOfSetLeverageBuilder user1_set_leverage(symbol, coin, uid_1, 2000, 2000);
  resp = user1.process(user1_set_leverage.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.RefFutureMarginResult().RefRelatedPositionSize(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 955000000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 954774000);

  FutureOneOfSetLeverageBuilder user1_set_leverage_1(symbol, coin, uid_1, 100, 100);
  resp = user1.process(user1_set_leverage_1.Build());
  ASSERT_NE(resp, nullptr);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.RefFutureMarginResult().RefRelatedPositionSize(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 5000000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 1000);

  FutureOneOfSetLeverageBuilder user2_set_leverage(symbol, coin, uid_2, 2000, 2000);
  resp = user2.process(user2_set_leverage.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.RefFutureMarginResult().RefRelatedPositionSize(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1045000000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 1044776000);

  FutureOneOfSetLeverageBuilder user2_set_leverage_1(symbol, coin, uid_2, 100, 100);
  resp = user2.process(user2_set_leverage_1.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.RefFutureMarginResult().RefRelatedPositionSize(), 1);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1995000000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 1990049000);

  for (int i = 0; i < 1; i++) {
    qty = ********00;
    order_id = te->GenUUID();
    side = ESide::Buy;
    FutureOneOfCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
    resp = user1.process(create_build_user1_buy_2.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    order_id = te->GenUUID();
    side = ESide::Sell;
    FutureOneOfCreateOrderBuilder create_build_user2_sell_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_2, coin);
    resp = user2.process(create_build_user2_sell_2.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  qty = ********;

  order_id = te->GenUUID();
  side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy_3(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // 用户2挂卖单
  order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell_3(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->size_x(), 1020000000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 1994901000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 1985051000);

  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(pz_checker.m_msg->liq_price_x(), 5099000);
  EXPECT_EQ(pz_checker.m_msg->liq_price_by_mark_price_x(), 1000);
}

std::shared_ptr<tmock::CTradeAppMock> LiqPriceByMpTest::te;
