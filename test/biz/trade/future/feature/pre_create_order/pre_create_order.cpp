#include "test/biz/trade/future/feature/pre_create_order/pre_create_order.hpp"

#include <algorithm>
#include <bbase/common/decimal/decimal.hpp>
#include <memory>
#include <string>
#include <unordered_map>

#include "biz_worker/service/trade/futures/modules/orderbiz/ordervalidationbiz/order_validation_biz.hpp"
#include "biz_worker/service/trade/store/order/raw_order.hpp"
#include "biz_worker/service/trade/store/per_user_store.hpp"
#include "biz_worker/service/trade/store/per_worker_store.hpp"
#include "data/enum.hpp"
#include "enums/ebizcode/biz_code.pb.h"
#include "src/biz_worker/engine/pre_worker/pre_engine.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/store/per_symbol_store.hpp"
#include "src/biz_worker/service/trade/store/position/cow_position.hpp"
#include "src/biz_worker/service/trade/store/position/raw_position.hpp"
#include "src/biz_worker/utils/calc_util.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/lib/stub.hpp"

int32_t PreCreateOrderTest::Deposit(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  // 充值
  std::string req_id = "";
  std::string trans_id = "";
  int wallet_record_type = 1;
  std::string bonus_change = "0";
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
  auto resp = user.process(deposit_build.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t PreCreateOrderTest::SwitchMode(biz::user_id_t uid, EAccountMode mode) {
  stub user(te, uid);

  // 切换仓位模式
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
      static_cast<EAccountMode>(mode));
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }

  return 0;
}

int32_t PreCreateOrderTest::SwitchBothSide(biz::user_id_t uid) {
  stub user(te, uid);
  FutureOneOfSwitchPositionModeBuilder switch_position_mode(ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::BothSide,
                                                            uid);
  auto resp = user.process(switch_position_mode.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }
  return 0;
}

/*
测试场景：预下单纯开仓，订单无浮亏
预期结果：预估强平价符合预期
*/
TEST_F(PreCreateOrderTest, open_pz_without_order_loss) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  EXPECT_EQ(0, Deposit(uid_1, "130"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 6000 * 1e4;

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;

  FutureOneOfPreCreateOrderBuilder create_build_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                    ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_buy.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), ********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), ********);

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  qty = 0.1 * 1e8;
  price = 10000 * 1e4;
  FutureOneOfPreCreateOrderBuilder create_build_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                     ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_sell.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), -********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), *********);
}

/*
测试场景：预下单纯开仓，订单有浮亏
预期结果：预估强平价符合预期
*/
TEST_F(PreCreateOrderTest, open_pz_with_order_loss) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  EXPECT_EQ(0, Deposit(uid_1, "500"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 10000 * 1e4;

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);
  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 100000;
  sc->price_limit_pnt_y_e6_ = 110000;
  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;

  FutureOneOfPreCreateOrderBuilder create_build_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                    ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_buy.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), ********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), 83607860);

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  qty = 0.1 * 1e8;
  price = 8000 * 1e4;
  FutureOneOfPreCreateOrderBuilder create_build_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                     ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_sell.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), -********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), *********);
}

/*
测试场景：预下单反向开仓
预期结果：预估强平价符合预期
*/
TEST_F(PreCreateOrderTest, opposite_add_pz_withupl) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  EXPECT_EQ(0, Deposit(uid_2, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Cross));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 8550 * 1e4;

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);
  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 100000;
  sc->price_limit_pnt_y_e6_ = 110000;
  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_2, ********);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户1挂卖单
  order_id = te->GenUUID();
  qty = 0.3 * 1e8;
  side = ESide::Sell;
  price = 9450 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), -********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), 302891330);

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  price = 8550 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user1_sell_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                             ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_2.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), -********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), 281395010);

  // 用户2挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  price = 9450 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), ********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), 1000);

  // 用户2挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  price = 8600 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user2_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_2.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), ********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), 1000);
}

/*
测试场景：预下单加仓
预期结果：预估强平价符合预期
*/
TEST_F(PreCreateOrderTest, add_pz_withupl) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  EXPECT_EQ(0, Deposit(uid_2, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Cross));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 8500 * 1e4;

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);
  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 100000;
  sc->price_limit_pnt_y_e6_ = 110000;
  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_2, ********);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户1挂买单
  order_id = te->GenUUID();
  qty = 0.1 * 1e8;
  side = ESide::Buy;
  price = 9500 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), ********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), 23847366);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  price = 8300 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user1_sell_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                             ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_2.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), ********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), 18156139);

  // 用户2挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  price = 9700 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), -********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), 155109526);

  // 用户2挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  price = 8600 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user2_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_2.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), -********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), *********);
}

/*
测试场景：预下单部分平仓
预期结果：预估强平价符合预期
*/
TEST_F(PreCreateOrderTest, partial_close_pz_withupl) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  EXPECT_EQ(0, Deposit(uid_2, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Cross));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 8500 * 1e4;

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);
  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 100000;
  sc->price_limit_pnt_y_e6_ = 110000;
  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_2, ********);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户1挂卖单
  order_id = te->GenUUID();
  qty = 0.1 * 1e8;
  side = ESide::Sell;
  price = 9500 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), ********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), 1000);

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  price = 8300 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user1_sell_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                             ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_2.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), ********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), 1000);

  // 用户2挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  price = 9700 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), -********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), *********);

  // 用户2挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  price = 8600 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user2_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_2.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), -********);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), *********);
}

/*
测试场景：预下单完全平仓
预期结果：预估强平价符合预期
*/
TEST_F(PreCreateOrderTest, completely_close_pz_withupl) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  EXPECT_EQ(0, Deposit(uid_2, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Cross));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 8600 * 1e4;

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);
  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 100000;
  sc->price_limit_pnt_y_e6_ = 110000;
  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_2, ********);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  price = 9500 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                           ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), 0);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), 1999998000);

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  price = 8300 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user1_sell_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                             ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_2.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), 0);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), 1999998000);

  // 用户2挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  price = 9700 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), 0);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), 1000);

  // 用户2挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  price = 8600 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user2_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_2.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  ASSERT_EQ(resp->pre_create_order_result().position_size_x(), 0);
  ASSERT_EQ(resp->pre_create_order_result().liq_price_x(), 1000);
}

/*
测试场景：预下单双仓平仓，加仓
预期结果：预估强平价符合预期
*/
TEST_F(PreCreateOrderTest, add_close_pz_both_side) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  EXPECT_EQ(0, Deposit(uid_2, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Cross));

  EXPECT_EQ(0, SwitchBothSide(uid_1));
  EXPECT_EQ(0, SwitchBothSide(uid_2));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 8600 * 1e4;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);
  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 100000;
  sc->price_limit_pnt_y_e6_ = 110000;
  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  EPositionIndex pz_index = EPositionIndex::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  pz_index = EPositionIndex::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_2, ********);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());
  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户1挂卖单
  qty = 0.1 * 1e8;
  order_id = te->GenUUID();
  side = ESide::Sell;
  pz_index = EPositionIndex::Sell;
  FutureOneOfCreateOrderBuilder create_build_user1_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);

  // 用户2挂买单
  order_id_2 = te->GenUUID();
  side = ESide::Buy;
  pz_index = EPositionIndex::Buy;
  FutureOneOfCreateOrderBuilder create_build_5(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_5.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_2, ********);
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(1);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());
  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(1);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户1挂卖单，卖方向为矮仓位
  order_id = te->GenUUID();
  side = ESide::Sell;
  pz_index = EPositionIndex::Sell;
  price = 9500 * 1e4;
  qty = 0.05 * 1e8;

  FutureOneOfPreCreateOrderBuilder create_build_user1_sell_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                             ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_2.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), -********);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 1999998000);

  // 用户1挂卖单，卖方向为完美对锁
  order_id = te->GenUUID();
  side = ESide::Sell;
  pz_index = EPositionIndex::Sell;
  price = 9500 * 1e4;
  qty = 0.1 * 1e8;

  FutureOneOfPreCreateOrderBuilder create_build_user1_sell_3(order_id, symbol, pz_index, side, order_type, qty, price,
                                                             ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_3.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), -********);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 1999998000);

  // 用户1挂卖单，卖方向为高仓位
  order_id = te->GenUUID();
  side = ESide::Sell;
  pz_index = EPositionIndex::Sell;
  price = 9500 * 1e4;
  qty = 0.2 * 1e8;

  FutureOneOfPreCreateOrderBuilder create_build_user1_sell_4(order_id, symbol, pz_index, side, order_type, qty, price,
                                                             ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_4.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), -********);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 294101960);

  // 用户1挂卖单，平仓，买仓位变矮仓位
  order_id = te->GenUUID();
  side = ESide::Sell;
  pz_index = EPositionIndex::Buy;
  price = 9500 * 1e4;
  qty = 0.15 * 1e8;

  FutureOneOfPreCreateOrderBuilder create_build_user1_sell_5(order_id, symbol, pz_index, side, order_type, qty, price,
                                                             ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_5.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), 5000000);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 1000);

  // 用户1挂卖单，平仓，买仓位还是高仓位
  order_id = te->GenUUID();
  side = ESide::Sell;
  pz_index = EPositionIndex::Buy;
  price = 9500 * 1e4;
  qty = 0.05 * 1e8;

  FutureOneOfPreCreateOrderBuilder create_build_user1_sell_6(order_id, symbol, pz_index, side, order_type, qty, price,
                                                             ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_sell_6.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), ********);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 1000);

  // 用户2挂买单，买方向为矮仓位
  order_id = te->GenUUID();
  side = ESide::Buy;
  pz_index = EPositionIndex::Buy;
  price = 9500 * 1e4;
  qty = 0.05 * 1e8;

  FutureOneOfPreCreateOrderBuilder create_build_user2_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_2.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), ********);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 1000);

  // 用户2挂买单，买方向为高仓位
  order_id = te->GenUUID();
  side = ESide::Buy;
  pz_index = EPositionIndex::Buy;
  price = 9500 * 1e4;
  qty = 1 * 1e8;

  FutureOneOfPreCreateOrderBuilder create_build_user2_buy_3(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_3.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), 1********);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 78701924);

  // 用户2挂买单，平仓，卖仓位变矮仓位
  order_id = te->GenUUID();
  side = ESide::Buy;
  pz_index = EPositionIndex::Sell;
  price = 9500 * 1e4;
  qty = 0.15 * 1e8;

  FutureOneOfPreCreateOrderBuilder create_build_user2_buy_4(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_4.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), -5000000);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 1999998000);

  // 用户2挂买单，平仓，卖仓位还是高仓位
  order_id = te->GenUUID();
  side = ESide::Buy;
  pz_index = EPositionIndex::Buy;
  price = 9500 * 1e4;
  qty = 0.05 * 1e8;

  FutureOneOfPreCreateOrderBuilder create_build_user2_buy_5(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_buy_5.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), ********);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 1000);
}

/*
测试场景：预下单市价单开仓
预期结果：预估强平价符合预期
*/
TEST_F(PreCreateOrderTest, open_pz_floating_order) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 1 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->UpdateQuoteData(symbol, 5, "9000", "5000000", false);

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfPreCreateOrderBuilder create_build_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                    ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_buy.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), ********0);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), ********);

  // 用户1挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfPreCreateOrderBuilder create_build_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                     ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_sell.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), -********0);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), *********);
}

/*
测试场景：预下单双仓加仓
预期结果：预估强平价符合预期
*/
TEST_F(PreCreateOrderTest, add_pz_both_side) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  EXPECT_EQ(0, Deposit(uid_2, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Cross));

  EXPECT_EQ(0, SwitchBothSide(uid_1));
  EXPECT_EQ(0, SwitchBothSide(uid_2));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Buy;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 8600 * 1e4;

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);
  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 100000;
  sc->price_limit_pnt_y_e6_ = 110000;
  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  pz_index = EPositionIndex::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_2, ********);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户1挂买单
  order_id = te->GenUUID();
  qty = 0.1 * 1e8;
  side = ESide::Buy;
  price = 9500 * 1e4;
  pz_index = EPositionIndex::Buy;

  FutureOneOfPreCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), ********);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 24517793);

  // 用户1挂买单
  order_id = te->GenUUID();
  side = ESide::Buy;
  price = 8300 * 1e4;

  FutureOneOfPreCreateOrderBuilder create_build_user1_buy_3(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_3.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), ********);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 18826566);

  // 用户2挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  price = 9700 * 1e4;
  pz_index = EPositionIndex::Sell;

  FutureOneOfPreCreateOrderBuilder create_build_user2_sell_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                             ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_2.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), -********);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 155772020);

  // 用户2挂卖单
  order_id = te->GenUUID();
  side = ESide::Sell;
  price = 8600 * 1e4;
  pz_index = EPositionIndex::Sell;

  FutureOneOfPreCreateOrderBuilder create_build_user2_sell_3(order_id, symbol, pz_index, side, order_type, qty, price,
                                                             ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell_3.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), -********);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), *********);
}

/*
测试场景：预下单双仓平仓
预期结果：预估强平价符合预期
*/
TEST_F(PreCreateOrderTest, close_pz_both_side) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, Deposit(uid_1, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_1, EAccountMode::Cross));

  EXPECT_EQ(0, Deposit(uid_2, "2000"));
  EXPECT_EQ(0, SwitchMode(uid_2, EAccountMode::Cross));

  EXPECT_EQ(0, SwitchBothSide(uid_1));
  EXPECT_EQ(0, SwitchBothSide(uid_2));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Buy;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 8600 * 1e4;

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);
  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 100000;
  sc->price_limit_pnt_y_e6_ = 110000;
  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, ********);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  pz_index = EPositionIndex::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_2, ********);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(********, pz_checker.m_msg->size_x());

  // 用户1平买仓
  order_id = te->GenUUID();
  qty = 0.1 * 1e8;
  side = ESide::Sell;
  price = 9500 * 1e4;
  pz_index = EPositionIndex::Buy;

  FutureOneOfPreCreateOrderBuilder create_build_user1_buy_2(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid_1, coin);
  resp = user1.process(create_build_user1_buy_2.Build());
  ASSERT_EQ(resp->has_pre_create_order_result(), true);
  EXPECT_EQ(resp->pre_create_order_result().position_size_x(), ********);
  EXPECT_EQ(resp->pre_create_order_result().liq_price_x(), 1000);
}

/*
测试场景：预下单双仓平仓
预期结果：预估强平价符合预期
*/
TEST_F(PreCreateOrderTest, revise_price) {
  store::PerWorkerStore* per_worker_store = new store::PerWorkerStore();
  auto sync_ev = std::make_shared<event::SyncQuoteDataEvent>();
  sync_ev->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
  (*sync_ev->future_last_price_map)[ESymbol::BTCUSDT] = biz::BigDecimal(1000);
  per_worker_store->SyncLastPrice(sync_ev);
  auto mark_p_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
  mark_p_map->emplace(ESymbol::BTCUSDT, biz::BigDecimal(1000));
  per_worker_store->SyncFutureMarkPrice(mark_p_map);
  auto index_p_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
  index_p_map->emplace(ESymbol::BTCUSDT, biz::BigDecimal(1000));
  per_worker_store->SyncFutureIndexPrice(index_p_map);
  biz::DraftPkg* draft_pkg = new biz::DraftPkg(nullptr, std::make_shared<store::Header>(), per_worker_store, nullptr);
  {
    store::Order order;
    order.order_type = EOrderType::Limit;
    order.symbol = ESymbol::BTCUSDT;
    order.side = ESide::Buy;
    order.price = 8000'0000;
    biz::orderbiz::OrderValidationBiz::RevisePrice(draft_pkg, &order);
    EXPECT_EQ(order.price, 1050'0000);
  }

  {
    store::Order order;
    order.order_type = EOrderType::Limit;
    order.symbol = ESymbol::BTCUSDT;
    order.side = ESide::Sell;
    order.price = 600'0000;
    biz::orderbiz::OrderValidationBiz::RevisePrice(draft_pkg, &order);
    EXPECT_EQ(order.price, 950'0000);
  }
}

std::shared_ptr<tmock::CTradeAppMock> PreCreateOrderTest::te;
