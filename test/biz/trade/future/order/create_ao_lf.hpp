#pragma once

#include <gtest/gtest.h>

#include <bbase/common/nacos_client/nacos_client_impl.hpp>
#include <iostream>
#include <memory>

#include "src/config/config_proxy.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class CreateAoLFTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    auto nacos_client = std::make_shared<bbase::nacos_client::NacosClientImpl>("FUTURE-CONFIG", "uta", "");
    // cache kv dataids
    config::ConfigProxy::Instance().InitConfigCenter();
    nacos_client->SetStringVar("limit.frequency.BTCUSDT", "100000");  // 0.001
    nacos_client->SetStringVar("limit.frequency.BTCUSDT.duration", "200");
    nacos_client->SetStringVar("limit.frequency.BTCUSDT.special_duration", "400");
    nacos_client->SetStringVar("limit.frequency.BTCUSDT.user", "10000");

    te->Init(argument_count, argument_arrary);
    te->Start();

    auto frequency_ev = std::make_shared<event::LoadLimitFrequencyConfigEvent>(0);
    frequency_ev->config_map_ = config::getTlsCfgMgrRaw()->symbol_config_mgr()->send_limit_frequency_map();
    te->pipeline()->ring_buffers(worker::kCrossWorker)->Write(frequency_ev);
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }

  static std::shared_ptr<tmock::CTradeAppMock> te;
};
