#include <gtest/gtest.h>

#include "proto/gen/svc/trading/req/create_order.pb.h"
#include "src/biz_worker/service/trade/store/order/raw_order.hpp"

class OrderEntityTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {}
  static void TearDownTestSuite() {}
};

TEST_F(OrderEntityTest, SetTimeInForce) {
  // 限价单的time_in_force 由req 决定
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    order.order_type = EOrderType::Limit;
    req.set_time_in_force(ETimeInForce::FillOrKill);
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::FillOrKill);
  }
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    order.order_type = EOrderType::Limit;
    req.set_time_in_force(ETimeInForce::PostOnly);
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::PostOnly);
  }
  // 如果市价单，除特定Create_type，其他都是IOC订单
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    order.order_type = EOrderType::Market;
    req.set_time_in_force(ETimeInForce::FillOrKill);
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::ImmediateOrCancel);
  }
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    order.order_type = EOrderType::Market;
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::ImmediateOrCancel);
  }
  // CreateByMartingaleBot/CloseByMartingaleBot/CloseByBot/CreateByBot按输入值来
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CreateByMartingaleBot);
    order.order_type = EOrderType::Market;
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::ImmediateOrCancel);
  }
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CreateByMartingaleBot);
    req.set_time_in_force(ETimeInForce::GoodTillCancel);
    order.order_type = EOrderType::Market;
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::ImmediateOrCancel);
  }
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CreateByMartingaleBot);
    req.set_time_in_force(ETimeInForce::FillOrKill);
    order.order_type = EOrderType::Market;
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::FillOrKill);
  }
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CreateByMartingaleBot);
    order.order_type = EOrderType::Market;
    req.set_time_in_force(ETimeInForce::GoodTillCancel);
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::ImmediateOrCancel);
  }

  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CloseByMartingaleBot);
    order.order_type = EOrderType::Market;
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::ImmediateOrCancel);
  }
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CloseByMartingaleBot);
    req.set_time_in_force(ETimeInForce::GoodTillCancel);
    order.order_type = EOrderType::Market;
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::ImmediateOrCancel);
  }
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CloseByMartingaleBot);
    req.set_time_in_force(ETimeInForce::FillOrKill);
    order.order_type = EOrderType::Market;
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::FillOrKill);
  }
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CreateByMartingaleBot);
    order.order_type = EOrderType::Market;
    req.set_time_in_force(ETimeInForce::GoodTillCancel);
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::ImmediateOrCancel);
  }

  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CloseByBot);
    order.order_type = EOrderType::Market;
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::ImmediateOrCancel);
  }
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CloseByBot);
    req.set_time_in_force(ETimeInForce::GoodTillCancel);
    order.order_type = EOrderType::Market;
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::ImmediateOrCancel);
  }
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CloseByBot);
    req.set_time_in_force(ETimeInForce::FillOrKill);
    order.order_type = EOrderType::Market;
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::FillOrKill);
  }
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CreateByMartingaleBot);
    order.order_type = EOrderType::Market;
    req.set_time_in_force(ETimeInForce::GoodTillCancel);
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::ImmediateOrCancel);
  }

  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CreateByBot);
    order.order_type = EOrderType::Market;
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::ImmediateOrCancel);
  }
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CreateByBot);
    req.set_time_in_force(ETimeInForce::GoodTillCancel);
    order.order_type = EOrderType::Market;
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::ImmediateOrCancel);
  }
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CreateByBot);
    req.set_time_in_force(ETimeInForce::FillOrKill);
    order.order_type = EOrderType::Market;
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::FillOrKill);
  }
  {
    store::Order order;
    svc::trading::req::CreateOrderReq req;
    req.set_create_type(ECreateType::CreateByMartingaleBot);
    order.order_type = EOrderType::Market;
    req.set_time_in_force(ETimeInForce::GoodTillCancel);
    order.SetTimeInForce(req);
    EXPECT_EQ(order.time_in_force, ETimeInForce::ImmediateOrCancel);
  }
}
