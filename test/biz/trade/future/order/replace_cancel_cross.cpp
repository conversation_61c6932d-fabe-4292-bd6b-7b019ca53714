#include "test/biz/trade/future/order/replace_cancel_cross.hpp"

#include <string>

#include "data/error/error.hpp"
#include "enums/eaccountmode/account_mode.pb.h"
#include "lib/msg_builder.hpp"
#include "margin_request/event/event.hpp"
#include "replace_rejected_test.hpp"  // NOLINT
#include "test/biz/trade/lib/stub.hpp"
int32_t ReplaceCancelCrossTest::SwitchMarginMode(stub& user, enums::eaccountmode::AccountMode account_mode) {
  // stub user(PmTest::te, uid);
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(user.m_uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(enums::eaction::Action::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(account_mode);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(user.m_uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }
  return 0;
}
// 停撮合，成交，改撤，起撮合
TEST_F(ReplaceCancelCrossTest, cross_TestReplaceRejected) {
  biz::user_id_t user_id = 11111;
  stub user(te, user_id);
  SwitchMarginMode(user, enums::eaccountmode::Cross);
  {
    // 充值git
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = ECoin::USDT;
    int wallet_record_type = 1;
    std::string amount = "*********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, user_id, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp = user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  biz::user_id_t other_user_id = 10000;
  stub other_user(te, other_user_id);
  SwitchMarginMode(other_user, enums::eaccountmode::Cross);
  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = ECoin::USDT;
    int wallet_record_type = 1;
    std::string amount = "*********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, other_user_id, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp = other_user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  // OpenLong +100@10,000
  // 订单已成交
  {
    te->AddMarkPrice(ESymbol::BTCUSDT, 10000, 10000, 10000);
    FutureOneOfCreateOrderBuilder create_ao_b1(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                               EOrderType::Limit, 100 * 1e4, 10000 * 1e4, ETimeInForce::GoodTillCancel,
                                               user_id, ECoin::USDT);
    auto resp = user.process(create_ao_b1.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    auto order_id = result.m_msg->futures_margin_result().related_orders().at(0).order_id();

    // 停止cross
    te->SuspendCross();

    FutureOneOfCreateOrderBuilder create_ao_b2(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Sell,
                                               EOrderType::Limit, 100 * 1e4, 10000 * 1e4, ETimeInForce::GoodTillCancel,
                                               other_user_id, ECoin::USDT);
    resp = other_user.process(create_ao_b2.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);

    FutureOneOfReplaceOrderBuilder replace_ao(order_id, ESymbol::BTCUSDT, 90 * 1e4, 10001 * 1e4, user_id, ECoin::USDT);
    resp = user.process(replace_ao.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);

    te->ResumeCross();

    // 此时撮合处理的订单为create_ao_b2
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), other_user_id);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).symbol(), ESymbol::BTCUSDT);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ::enums::eside::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 100 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 100 * 10000 * 1e4);

    // 此时撮合处理的订单为create_ao_b1
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), user_id);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).symbol(), ESymbol::BTCUSDT);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ::enums::eside::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 100 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 100 * 10000 * 1e4);

    // 此处处理replace
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), user_id);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(),
              ::enums::eorderstatus::Filled);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cross_status(),
              ::enums::ecrossstatus::ReplaceRejected);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).price_x(), 10000 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).qty_x(), 100 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).leaves_qty_x(), 0);
  }

  // replaceToCanceled
  {
    FutureOneOfCreateOrderBuilder create_ao_b1(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                               EOrderType::Limit, 100 * 1e4, 10000 * 1e4, ETimeInForce::GoodTillCancel,
                                               user_id, ECoin::USDT);
    auto resp = user.process(create_ao_b1.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    auto order_id = result.m_msg->futures_margin_result().related_orders().at(0).order_id();

    te->SuspendCross();

    FutureOneOfCreateOrderBuilder create_ao_b2(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Sell,
                                               EOrderType::Limit, 50 * 1e4, 10000 * 1e4, ETimeInForce::GoodTillCancel,
                                               other_user_id, ECoin::USDT);
    resp = other_user.process(create_ao_b2.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);

    FutureOneOfReplaceOrderBuilder replace_ao(order_id, ESymbol::BTCUSDT, 50 * 1e4, 10001 * 1e4, user_id, ECoin::USDT);
    resp = user.process(replace_ao.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);

    te->ResumeCross();

    // 此时撮合处理的订单为create_ao_b2
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), other_user_id);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).symbol(), ESymbol::BTCUSDT);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ::enums::eside::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 150 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 150 * 10000 * 1e4);

    // 此时撮合处理的订单为create_ao_b1
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), user_id);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).symbol(), ESymbol::BTCUSDT);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ::enums::eside::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 150 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 150 * 10000 * 1e4);

    // 此处处理replace
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), user_id);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(),
              ::enums::eorderstatus::Cancelled);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cross_status(),
              ::enums::ecrossstatus::Canceled);
  }
}

/*
订单不存在
*/
TEST_F(ReplaceCancelCrossTest, order_id_not_exist) {
  biz::user_id_t user_id = 11111;
  stub user(te, user_id);
  SwitchMarginMode(user, enums::eaccountmode::Cross);
  {
    // 充值git
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = ECoin::USDT;
    int wallet_record_type = 1;
    std::string amount = "*********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, user_id, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp = user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  biz::user_id_t other_user_id = 10000;
  stub other_user(te, other_user_id);
  SwitchMarginMode(other_user, enums::eaccountmode::Cross);
  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = ECoin::USDT;
    int wallet_record_type = 1;
    std::string amount = "*********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, other_user_id, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp = other_user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  {
    FutureOneOfCreateOrderBuilder create_ao_b1(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                               EOrderType::Limit, 100 * 1e4, 10000 * 1e4, ETimeInForce::GoodTillCancel,
                                               user_id, ECoin::USDT);
    auto resp = user.process(create_ao_b1.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    // auto order_id = result.m_msg->futures_margin_result().related_orders().at(0).order_id();

    // 停止cross
    te->SuspendCross();

    // 现在改一个不存在的订单，报错
    auto order_id = te->GenUUID();
    FutureOneOfReplaceOrderBuilder replace_ao(order_id, ESymbol::BTCUSDT, 50 * 1e4, 10001 * 1e4, user_id, ECoin::USDT);
    resp = user.process(replace_ao.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeOrderNotExists);
  }
}

/*
订单已成交
*/
TEST_F(ReplaceCancelCrossTest, order_id_filled) {
  biz::user_id_t user_id = 11111;
  stub user(te, user_id);
  SwitchMarginMode(user, enums::eaccountmode::Cross);
  {
    // 充值git
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = ECoin::USDT;
    int wallet_record_type = 1;
    std::string amount = "*********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, user_id, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp = user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  biz::user_id_t other_user_id = 10000;
  stub other_user(te, other_user_id);
  SwitchMarginMode(other_user, enums::eaccountmode::Cross);
  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = ECoin::USDT;
    int wallet_record_type = 1;
    std::string amount = "*********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, other_user_id, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp = other_user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  {
    te->AddMarkPrice(ESymbol::BTCUSDT, 10000, 10000, 10000);
    FutureOneOfCreateOrderBuilder create_ao_b1(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                               EOrderType::Limit, 150 * 1e4, 10000 * 1e4, ETimeInForce::GoodTillCancel,
                                               user_id, ECoin::USDT);
    auto resp = user.process(create_ao_b1.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    auto order_id = result.m_msg->futures_margin_result().related_orders().at(0).order_id();

    // 停止cross
    // te->SuspendCross();

    FutureOneOfCreateOrderBuilder create_ao_b2(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Sell,
                                               EOrderType::Limit, 150 * 1e4, 10000 * 1e4, ETimeInForce::GoodTillCancel,
                                               other_user_id, ECoin::USDT);
    resp = other_user.process(create_ao_b2.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);

    // 此时撮合处理的订单为create_ao_b2
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), other_user_id);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).symbol(), ESymbol::BTCUSDT);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ::enums::eside::Sell);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 150 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 150 * 10000 * 1e4);

    // 此时撮合处理的订单为create_ao_b1
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().user_id(), user_id);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_fills().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).symbol(), ESymbol::BTCUSDT);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).position_idx(), EPositionIndex::Single);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).side(), ::enums::eside::Buy);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).size_x(), 150 * 1e4);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().at(0).value_e8(), 150 * 10000 * 1e4);
    // 现在改一个不存在的订单，报错
    // auto order_id = te->GenUUID();
    FutureOneOfReplaceOrderBuilder replace_ao(order_id, ESymbol::BTCUSDT, 50 * 1e4, 10001 * 1e4, user_id, ECoin::USDT);
    resp = user.process(replace_ao.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeOrderNotExists);
  }
}
std::shared_ptr<tmock::CTradeAppMock> ReplaceCancelCrossTest::te;
