//
// Created by Gdso.li on 19/4/2023.
//

#include "test/biz/trade/future/order/cancel_all_after_trigger_to_active.hpp"

#include <string>

#include "data/type/biz_type.hpp"
#include "src/data/enum.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

std::shared_ptr<tmock::CTradeAppMock> CancelAllAfterTriggerToActive::te;

static biz::user_id_t uid111 = 653106;
static biz::coin_t const coin111 = 5;
static biz::cross_idx_t const cross_idx111 = 5;
static biz::symbol_t const symbol111 = 5;

void CancelAllAfterTriggerToActive::CreateOrder(const std::string& order_id, biz::price_x_t tpx,
                                                EPriceDirection expected_direction, ESide side, bool close_on_trigger,
                                                stub& user1) {
  EPositionIndex const position_idx = EPositionIndex::Single;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t const price = 9000 * 1e4;
  EOrderType const order_type = EOrderType::Limit;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;
  EStopOrderType const stop_order_type = EStopOrderType::Stop;
  ETriggerBy const trigger_by = ETriggerBy::MarkPrice;
  biz::price_x_t const trigger_price = tpx;
  biz::price_x_t base_price = 10000 * 1e4;
  biz::price_x_t const tp_x = 0;
  biz::price_x_t const sl_x = 0;
  ESmpType const smp_type = ESmpType::None;
  int32_t const smp_group = 0;
  FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
      uid111, coin111, symbol111, order_id, position_idx, side, order_type, qty, price, time_in_force, stop_order_type,
      trigger_by, trigger_price, expected_direction, base_price, close_on_trigger, tp_x, sl_x, smp_type, smp_group);

  // 发rpc请求
  auto create_so_resp = user1.process(create_so_build.Build());
  // 校验rpc回报
  ASSERT_EQ(create_so_resp->ret_code(), error::kErrorCodeSuccess);

  // 取trading result
  auto create_so_result = te->PopResult();
  ASSERT_NE(create_so_result.m_msg, nullptr);

  // 获取订单数据
  auto order_check = create_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  // 校验订单状态
  order_check.CheckQty(uid111, qty);
  order_check.CheckTimeInForce(time_in_force);
  order_check.CheckIsCOT(close_on_trigger);
  order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
  order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
}

TEST_F(CancelAllAfterTriggerToActive, ManyStopOrder) {
  // 构建客户端
  stub user1(te, uid111);

  // 充值
  auto req_id = te->GenUUID();
  auto trans_id = te->GenUUID();
  auto bonus_change = "0";  // 赠金
  EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
  FutureDepositBuilder deposit_build(req_id, uid111, trans_id, coin111, static_cast<int>(wallet_record_type),
                                     std::to_string(50000), bonus_change);
  // 发rpc请求
  auto deposit_resp = user1.process(deposit_build.Build());
  // 校验rpc回报
  ASSERT_EQ(deposit_resp->ret_code(), error::kErrorCodeSuccess);
  // 取trading result
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg, nullptr);

  // ============================================================================================================
  // 校准行情
  // ============================================================================================================
  // 校准标记价格 (mark_price) 10000
  auto underlying_price = bbase::decimal::Decimal<>("10000");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol111, underlying_price, exchange_rate);

  // 校准最新价格 (last_price) 10000
  bool sync_trigger = true;
  auto lv0_px = "10000";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol111, cross_idx111, lv0_px, lv0_qty, sync_trigger);

  // ============================================================================================================
  // 报单
  // ============================================================================================================
  // order1:so Sell tps:9980 Failing, price:9000
  std::string order1_id = "a21955de-96a4-4068-b3f7-75253d1a0169";
  CreateOrder(order1_id, 9980 * 1e4, EPriceDirection::Falling, ESide::Sell, false, user1);

  // order2:so Buy tps:10001 Rising, price:9000
  std::string order2_id = "1c56b0cc-4499-4461-a801-0fbe5417ae4a";
  CreateOrder(order2_id, 10001 * 1e4, EPriceDirection::Rising, ESide::Buy, false, user1);

  // order3:cot so Buy tps:10001 Rising, price:9000
  std::string order3_id = "31eb3064-c63c-4d49-be9d-27ab4eed6f33";
  CreateOrder(order3_id, 10001 * 1e4, EPriceDirection::Rising, ESide::Buy, true, user1);

  underlying_price = bbase::decimal::Decimal<>("9980");
  bool to_check_res = false;
  bool checked = false;
  int try_times = 0;  // 最多尝试1次，最多2.5s
  while (!checked && try_times < 4) {
    usleep(500 * 1000);

    // 更新标记价格
    // 模拟下跌触发 (mark_price)9980 <= (trigger_price)9980
    te->UpdateMarketData(symbol111, underlying_price, exchange_rate);
    underlying_price = underlying_price.Sub(1);  // 每次需要变一下价格，不然会被trigger拦截

    // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
    auto time_event = std::make_shared<event::TimerEvent>(0);
    time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
    time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
    auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);

    // 正确的情况order1被触发
    // 取trading result
    auto active_so_result = te->PopResult();
    if (active_so_result.m_msg == nullptr) {
      // 没拿到active_so的result, 继续发标记价格，尝试触发
      try_times++;
      continue;
    }

    // 拿到active_so的result, 说明触发成功，不需要循环发标记价和timer了，走完下面的检查流程即可
    checked = true;

    // 获取订单数据
    auto order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order1_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    // 确认触发
    order_check.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);

    active_so_result = te->PopResult();
    if (active_so_result.m_msg == nullptr) {
      // 没拿到active_so的result, 继续发标记价格，尝试触发
      try_times++;
      continue;
    }
    // 获取订单数据
    order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order1_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    // 确认成为活动单
    order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    to_check_res = true;
  }
  ASSERT_EQ(to_check_res, true);

  // Cancel All
  ECancelType cancel_type = ECancelType::CancelByUser;
  ECancelAllType cancel_all_type = ECancelAllType::NormalStopOrder;
  biz::remark_t remark{"cancel all remain so"};
  FutureCancelAllBuilder cancel_all_builder(symbol111, coin111, uid111, cancel_type, cancel_all_type,
                                            remark.GetValue());

  auto cancel_all_resp = user1.process(cancel_all_builder.Build());
  // 校验rpc回报（现在的逻辑是：应当看到错误，但单子被全部撤掉————可以考虑优化CancelAll的过程）
  ASSERT_EQ(cancel_all_resp->ret_code(), error::kErrorCodeSuccess);

  auto result2 = te->PopResult();
  ASSERT_NE(result2.m_msg.get(), nullptr);

  std::string result_msg{};
  (void)google::protobuf::util::MessageToJsonString(*result2.RefFutureMarginResult().m_msg, &result_msg);
  // std::cout << "[CancelAll] result:" << result_msg << std::endl;

  // 被触发的保持, 所以不在受影响订单范围内
  auto order1_check = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order1_id);
  EXPECT_EQ(nullptr, order1_check.m_msg);

  // 正常撤单
  auto order2_check = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order2_id);
  order2_check.CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);

  // 正常撤单
  auto order3_check = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order3_id);
  order3_check.CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
}
