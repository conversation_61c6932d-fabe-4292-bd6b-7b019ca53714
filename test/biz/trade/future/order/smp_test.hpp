#pragma once

#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <iostream>
#include <memory>
#include <vector>

#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"
#include "test/mocks/match_sdk/futures_match_sdk/FbuCrossSdk.h"
#include "test/mocks/match_sdk/futures_match_sdk/inc/MatchSdkIf.h"

class TestSMP : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }

  event::OnRecvMatchingResultEvent::Ptr GenOnMatchingResult(
      const std::vector<std::shared_ptr<models::tradingdto::TransactDTO>> orders) {
    auto ev = std::make_shared<event::OnRecvMatchingResultEvent>(uid, event::EventType::kEventCrossInput,
                                                                 event::EventSourceType::kPre,
                                                                 event::EventDispatchType::kUnicastToUser);
    ev->derivative_req()->x_resp_items = orders;
    ev->set_product_type(EProductType::Futures);
    store::Header header{
        .coin = ECoin::USDT,
        .uid = 10000,
    };
    ev->set_header(header);
    return ev;
  }

  std::shared_ptr<models::tradingdto::TransactDTO> GenRespItem(const models::tradingdto::TransactDTO ord,
                                                               EOrderStatus cs, ECrossStatus os) {
    auto item = std::make_shared<models::tradingdto::TransactDTO>(ord);
    item->set_cross_status(ECrossStatus(cs));
    item->set_order_status(EOrderStatus(os));
    item->set_cross_seq(++last_seq);
    return item;
  }

  static std::shared_ptr<tmock::CTradeAppMock> te;
  biz::coin_t coin{ECoin::USDT};
  biz::user_id_t uid{10000};
  biz::symbol_t symbol{ESymbol::BTCUSDT};
  biz::seq_t last_seq{0};
};
