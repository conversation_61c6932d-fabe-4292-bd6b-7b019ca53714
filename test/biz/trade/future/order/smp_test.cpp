#include "test/biz/trade/future/order/smp_test.hpp"

#include <bbase/common/utils/time_utils.hpp>

#include "lib/msg_builder.hpp"
#include "src/config/symbol_config/symbol_config_manager.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "src/cross_worker/cross_receiver/xresp/x_future_resp_pkg.hpp"
#include "test/biz/trade/lib/stub.hpp"
std::shared_ptr<tmock::CTradeAppMock> TestSMP::te;

TEST_F(TestSMP, CreateOrder) {
  biz::user_id_t uid123 = 10000;
  stub user(te, uid123);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin123 = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid123, trans_id, coin123, wallet_record_type, amount, bonus_change);

    auto resp1 = user.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  auto order_id = te->GenUUID();
  biz::symbol_t symbol123 = ESymbol::BTCUSDT;
  biz::coin_t coin567 = ECoin::USDT;
  EPositionIndex pz_idx = EPositionIndex::Single;
  ESide side = ESide::Buy;
  // ESide opp_side = ESide::Sell;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 100000;
  biz::price_x_t price = 20000 * 1e4;
  ESmpType smp_type = ESmpType::CancelTaker;
  int32_t smp_group = 100;

  FutureOneOfCreateOrderBuilder create_build(order_id, symbol123, pz_idx, side, order_type, qty, price,
                                             ETimeInForce::GoodTillCancel, uid123, coin567, smp_type, smp_group);
  auto resp = user.process(create_build.Build());
  ASSERT_NE(resp, nullptr);
  auto result = te->PopResult();
  auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  orderCheck.Check(uid123, qty);
  orderCheck.CheckSMP(smp_type, smp_group);
}
TEST_F(TestSMP, CancelTaker) {
  biz::user_id_t uid123 = 10000;
  stub user(te, uid123);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin567 = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid123, trans_id, coin567, wallet_record_type, amount, bonus_change);

    auto resp1 = user.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  auto order_id = te->GenUUID();
  biz::symbol_t symbol567 = ESymbol::BTCUSDT;
  biz::coin_t coin567 = ECoin::USDT;
  EPositionIndex pz_idx = EPositionIndex::Single;
  ESide side = ESide::Buy;
  // ESide opp_side = ESide::Sell;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 100000;
  biz::price_x_t price = 20000 * 1e4;
  ESmpType smp_type = ESmpType::CancelTaker;
  int32_t smp_group = 100;

  FutureOneOfCreateOrderBuilder create_build(order_id, symbol567, pz_idx, side, order_type, qty, price,
                                             ETimeInForce::GoodTillCancel, uid123, coin567, smp_type, smp_group);
  auto resp = user.process(create_build.Build());
  ASSERT_NE(resp, nullptr);
  auto result = te->PopResult();
  auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  orderCheck.Check(uid123, qty);
  orderCheck.CheckSMP(smp_type, smp_group);

  //   auto new_item = GenRespItem(*orderCheck.m_msg, EOrderStatus::New, ECrossStatus::NewAccepted);
  //   auto canceled_item = GenRespItem(*orderCheck.m_msg, EOrderStatus::Cancelled,
  //   ECrossStatus::Expired); auto matching_result = GenOnMatchingResult({new_item, canceled_item});
}

TEST_F(TestSMP, BuidCrossRequest) {
  auto ord = std::make_shared<store::Order>();
  auto& transact = *ord;
  transact.user_id = 10000;
  transact.smp_group = 100;
  transact.smp_type = ESmpType::CancelTaker;
  transact.order_id = boost::uuids::to_string(boost::uuids::random_generator()()).data();
  transact.order_type = EOrderType::Market;
  transact.side = ESide::Buy;
  biz::cross::xReqSender x_req_sender;

  auto err = x_req_sender.BuildMsgNew(ord, EProductType::Futures);
  ASSERT_FALSE(err.HasErr());
  ASSERT_EQ(x_req_sender.req_mgr_.request_obj_->req_.smp_group_, 100);
  ASSERT_EQ(x_req_sender.req_mgr_.request_obj_->req_.fbu_smp_type_, uint8_t(ESmpType::CancelTaker));

  transact.smp_type = ESmpType::CancelMaker;
  err = x_req_sender.BuildMsgNew(ord, EProductType::Futures);
  ASSERT_FALSE(err.HasErr());
  ASSERT_EQ(x_req_sender.req_mgr_.request_obj_->req_.smp_group_, 100);
  ASSERT_EQ(x_req_sender.req_mgr_.request_obj_->req_.fbu_smp_type_, uint8_t(ESmpType::CancelMaker));

  transact.smp_type = ESmpType::CancelBoth;
  err = x_req_sender.BuildMsgNew(ord, EProductType::Futures);
  ASSERT_FALSE(err.HasErr());
  ASSERT_EQ(x_req_sender.req_mgr_.request_obj_->req_.smp_group_, 100);
  ASSERT_EQ(x_req_sender.req_mgr_.request_obj_->req_.fbu_smp_type_, uint8_t(ESmpType::CancelBoth));

  transact.smp_type = ESmpType::None;
  err = x_req_sender.BuildMsgNew(ord, EProductType::Futures);
  ASSERT_FALSE(err.HasErr());
  ASSERT_EQ(x_req_sender.req_mgr_.request_obj_->req_.smp_group_, 100);
  ASSERT_EQ(x_req_sender.req_mgr_.request_obj_->req_.fbu_smp_type_, uint8_t(ESmpType::None));
}

TEST_F(TestSMP, ParseResponse) {
  biz::cross::response::XResponse binary_resp;
  auto& rsp = binary_resp.rsp_;
  rsp.target_comp_id_ = 123456789;
  auto tmp_order_id = boost::uuids::random_generator()();
  rsp.orig_cl_ord_id_ = tmp_order_id;
  auto tmp_exec_id = boost::uuids::random_generator()();
  rsp.exec_id_ = tmp_exec_id;
  rsp.reject_reason_ = biz::cross::kEcBySelfMatch;
  rsp.order_status_ = biz::cross::response::kOSCanceled;
  rsp.smp_group_ = 100;

  // 带了smptype，被cancel的maker单
  rsp.smp_type_ = uint8_t(ESmpType::CancelMaker);
  rsp.symbol_ = ESymbol::BTCUSDT;
  // biz::sc::SymbolConfigManager symbol_cfg_mgr;
  auto symbol_cfg_mgr = config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr();
  biz::cross::DerivativesTransactionPtr item = std::make_shared<models::tradingdto::TransactDTO>();
  biz::cross::XFutureRespPkg x_resp_pkg(symbol_cfg_mgr);
  auto err = x_resp_pkg.ParseCanceledItem(&binary_resp, item);
  ASSERT_FALSE(err.HasErr());
  ASSERT_EQ(item->smp_group(), 100);
  ASSERT_EQ(item->smp_type(), uint8_t(ESmpType::CancelMaker));
  ASSERT_EQ(item->cxl_rej_reason(), uint8_t(biz::cross::kEcBySelfMatch));
  ASSERT_EQ(item->order_status(), EOrderStatus::Cancelled);
  ASSERT_EQ(item->cross_status(), ECrossStatus::Canceled);
  ASSERT_STREQ(item->smp_order_id().c_str(), boost::uuids::to_string(tmp_exec_id).data());

  // 没有带smptype, 但是因为group或者uid, 被cancel的maker单
  rsp.smp_type_ = uint8_t(ESmpType::None);
  rsp.symbol_ = ESymbol::BTCUSDT;
  err = x_resp_pkg.ParseCanceledItem(&binary_resp, item);
  ASSERT_FALSE(err.HasErr());
  ASSERT_EQ(item->smp_group(), 100);
  ASSERT_EQ(item->smp_type(), uint8_t(ESmpType::None));
  ASSERT_EQ(item->cxl_rej_reason(), uint8_t(biz::cross::kEcBySelfMatch));
  ASSERT_EQ(item->order_status(), EOrderStatus::Cancelled);
  ASSERT_EQ(item->cross_status(), ECrossStatus::Canceled);
  ASSERT_STREQ(item->smp_order_id().c_str(), boost::uuids::to_string(tmp_exec_id).data());

  // CancelTaker - new
  rsp.smp_type_ = uint8_t(ESmpType::CancelTaker);
  x_resp_pkg.new_accepted_item = std::make_shared<models::tradingdto::TransactDTO>();
  x_resp_pkg.new_accepted_item->set_order_id(boost::uuids::to_string(tmp_order_id).data(),
                                             boost::uuids::to_string(tmp_order_id).size());
  err = x_resp_pkg.ParseCanceledItem(&binary_resp, item);
  ASSERT_FALSE(err.HasErr());
  ASSERT_EQ(item->smp_group(), 100);
  ASSERT_EQ(item->smp_type(), uint8_t(ESmpType::CancelTaker));
  ASSERT_EQ(item->cxl_rej_reason(), uint8_t(biz::cross::kEcBySelfMatch));
  ASSERT_EQ(item->order_status(), EOrderStatus::Cancelled);
  ASSERT_EQ(item->cross_status(), ECrossStatus::SmpCanceled);
  ASSERT_STREQ(item->smp_order_id().c_str(), boost::uuids::to_string(tmp_exec_id).data());

  // CancelTaker - replaced
  rsp.smp_type_ = uint8_t(ESmpType::CancelTaker);
  x_resp_pkg.new_accepted_item = nullptr;
  x_resp_pkg.replaced_item = std::make_shared<models::tradingdto::TransactDTO>();
  x_resp_pkg.replaced_item->set_order_id(boost::uuids::to_string(tmp_order_id).data(),
                                         boost::uuids::to_string(tmp_order_id).size());
  err = x_resp_pkg.ParseCanceledItem(&binary_resp, item);
  ASSERT_FALSE(err.HasErr());
  ASSERT_EQ(item->smp_group(), 100);
  ASSERT_EQ(item->smp_type(), uint8_t(ESmpType::CancelTaker));
  ASSERT_EQ(item->cxl_rej_reason(), uint8_t(biz::cross::kEcBySelfMatch));
  ASSERT_EQ(item->order_status(), EOrderStatus::Cancelled);
  ASSERT_EQ(item->cross_status(), ECrossStatus::SmpCanceled);
  ASSERT_STREQ(item->smp_order_id().c_str(), boost::uuids::to_string(tmp_exec_id).data());

  // CancelBoth - Taker
  rsp.smp_type_ = uint8_t(ESmpType::CancelBoth);
  err = x_resp_pkg.ParseCanceledItem(&binary_resp, item);
  ASSERT_FALSE(err.HasErr());
  ASSERT_EQ(item->smp_group(), 100);
  ASSERT_EQ(item->smp_type(), uint8_t(ESmpType::CancelBoth));
  ASSERT_EQ(item->cxl_rej_reason(), uint8_t(biz::cross::kEcBySelfMatch));
  ASSERT_EQ(item->order_status(), EOrderStatus::Cancelled);
  ASSERT_EQ(item->cross_status(), ECrossStatus::SmpCanceled);
  ASSERT_STREQ(item->smp_order_id().c_str(), boost::uuids::to_string(tmp_exec_id).data());

  // CancelBoth - Maker
  x_resp_pkg.replaced_item = nullptr;
  err = x_resp_pkg.ParseCanceledItem(&binary_resp, item);
  ASSERT_FALSE(err.HasErr());
  ASSERT_EQ(item->smp_group(), 100);
  ASSERT_EQ(item->smp_type(), uint8_t(ESmpType::CancelBoth));
  ASSERT_EQ(item->cxl_rej_reason(), uint8_t(biz::cross::kEcBySelfMatch));
  ASSERT_EQ(item->order_status(), EOrderStatus::Cancelled);
  ASSERT_EQ(item->cross_status(), ECrossStatus::Canceled);
  ASSERT_STREQ(item->smp_order_id().c_str(), boost::uuids::to_string(tmp_exec_id).data());

  // Reject Invalid smptype
  rsp.reject_reason_ = biz::cross::kEcInvalidSmpType;
  rsp.order_status_ = biz::cross::response::kOSRejected;
  rsp.smp_type_ = 10;  // invalid smp type in response
  err = x_resp_pkg.ParseRejectedFromNewRequest(&binary_resp, item);
  ASSERT_FALSE(err.HasErr());
  ASSERT_EQ(item->smp_type(), uint8_t(ESmpType::None));
  ASSERT_EQ(item->cxl_rej_reason(), uint8_t(biz::cross::kEcInvalidSmpType));
  ASSERT_EQ(item->order_status(), EOrderStatus::Rejected);
}

TEST_F(TestSMP, CancelMaker) {
  biz::user_id_t user_id = 10000;
  stub user(te, user_id);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin567 = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, user_id, trans_id, coin567, wallet_record_type, amount, bonus_change);

    auto deposit_resp = user.process(deposit_build.Build());
    ASSERT_NE(deposit_resp, nullptr);
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.001", "20000");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    RpcContext ct{};
    auto resp = user.create_order(build.Build(), ct);
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->order_link_id(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
    auto maker_order_id = result.m_msg->futures_margin_result().related_orders().at(0).order_id();
    auto orig_maker_op_time = result.m_msg->futures_margin_result().related_orders().at(0).op_time_e9();

    sleep(1);

    OpenApiV5CreateOrderBuilder build2("linear", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    order_link_id = te->GenUUID();
    build2.SetOrderLinkID(order_link_id);
    build2.SetSmpType("CancelMaker");

    auto now_e9 = bbase::utils::Time::GetTimeNs();
    RpcContext ct2{};
    ct2.ct.AddMetadata("inittime", std::to_string(now_e9));
    auto resp2 = user.create_order(build2.Build(), ct2);
    ASSERT_NE(resp2, nullptr);
    EXPECT_EQ(resp2->order_link_id(), order_link_id);
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    for (auto ord : result.m_msg->futures_margin_result().related_orders()) {
      if (ord.order_status() == EOrderStatus::Cancelled) {
        EXPECT_EQ(ord.order_id(), maker_order_id);
        EXPECT_EQ(ord.cross_status(), ECrossStatus::Canceled);
        EXPECT_EQ(ord.cancel_type(), ECancelType::CancelBySmp);
        EXPECT_EQ(ord.cxl_rej_reason(), ECxlRejReason::EC_BySelfMatch);
        EXPECT_EQ(ord.op_time_e9(), orig_maker_op_time);
      }
    }
    // EXPECT_EQ(result2.m_msg->header().req_init_at_e9(), orig_maker_op_time);
    EXPECT_EQ(result2.m_msg->header().req_init_at_e9(), now_e9);
  }
}
