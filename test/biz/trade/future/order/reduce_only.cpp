//
// Created by Gdso.li on 23/3/2023.
//

#include "test/biz/trade/future/order/reduce_only.hpp"

#include <string>

#include "lib/msg_builder.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"

std::shared_ptr<tmock::CTradeAppMock> ReduceOnlyTest::te;

TEST_F(ReduceOnlyTest, BasicReduceOnly) {
  biz::coin_t const coin = 5;      // BTC
  biz::symbol_t const symbol = 5;  // BTCUSDT
  EPositionIndex const pz_index = EPositionIndex::Single;
  EOrderType const order_type = EOrderType::Limit;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  // user1
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin2 = 5;
    int wallet_record_type = 1;
    std::string amount = "*********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid_1, trans_id, coin2, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  // opposite user2
  biz::user_id_t const uid_2 = 658002;
  stub user2(te, uid_2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin2 = 5;
    int wallet_record_type = 1;
    std::string amount = "*********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid_2, trans_id, coin2, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 10000, 10000, 10000);
  // user1: create position 100qty
  biz::size_x_t const pz_qty = 100 * 1e6;
  biz::price_x_t const pz_price = 10000 * 1e4;

  // case准备 -- 构建持仓 100qty
  // ============================================================================================================
  // user1 买入开仓订单 100qty
  auto user1_pz_buy_id = te->GenUUID();
  FutureOneOfCreateOrderBuilder pz_buy_order_build(user1_pz_buy_id, symbol, pz_index, ESide::Buy, order_type, pz_qty,
                                                   pz_price, time_in_force, uid_1, coin);
  auto pz_buy_order_resp = user1.process(pz_buy_order_build.Build());

  // 收user1 buy单result
  auto pz_buy_order_result = te->PopResult();
  ASSERT_NE(pz_buy_order_result.m_msg.get(), nullptr);
  auto pz_buy_order_check = pz_buy_order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(user1_pz_buy_id);
  ASSERT_NE(pz_buy_order_check.m_msg, nullptr);
  pz_buy_order_check.CheckQty(uid_1, pz_qty);
  pz_buy_order_check.CheckPrice(uid_1, pz_price);
  pz_buy_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

  // user2 卖出开仓订单 100qty（主要为了给user1建立持仓）
  auto user2_pz_sell_id = te->GenUUID();
  FutureOneOfCreateOrderBuilder pz_sell_order_build(user2_pz_sell_id, symbol, pz_index, ESide::Sell, order_type, pz_qty,
                                                    pz_price, time_in_force, uid_2, coin);
  auto pz_sell_order_resp = user2.process(pz_sell_order_build.Build());
  ASSERT_EQ(pz_sell_order_resp->ret_code(), error::kErrorCodeSuccess);

  // 收user2 sell单result
  auto pz_sell_order_result = te->PopResult();
  ASSERT_NE(pz_sell_order_result.m_msg.get(), nullptr);
  auto pz_sell_order_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(user2_pz_sell_id);
  ASSERT_NE(pz_sell_order_check.m_msg, nullptr);
  pz_sell_order_check.CheckQty(uid_2, pz_qty);
  pz_sell_order_check.CheckPrice(uid_2, pz_price);
  pz_sell_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

  // 检查user2 持仓
  auto user2_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
  user2_pz_check.CheckSize(pz_qty);
  user2_pz_check.CheckFreeSize(pz_qty);

  // 收user1 buy单成交回报
  pz_buy_order_result = te->PopResult();
  ASSERT_NE(pz_buy_order_result.m_msg.get(), nullptr);
  // 检查user1 持仓
  auto user1_pz_check = pz_buy_order_result.RefFutureMarginResult().RefRelatedPosition(0);
  user1_pz_check.CheckSize(pz_qty);

  // ============================================================================================================
  // 以下全是user1的操作
  // ============================================================================================================

  // ============================================================================================================
  // user1 成本占满closing_box
  // order1, reduce_only, qty:50, price:9999
  // order2, reduce_only, qty:60, price:9999
  //
  // closing_box:
  // extra qty = 0
  // sell  --
  //
  // 报order1
  //
  // closing_box:
  // extra qty = 0
  // sell  1  9999 50 ro <--  edge key
  //
  // 报order2 (in-place 60qty -> 50qty)
  //
  // closing_box:
  // extra qty = 0
  // sell  2  9999 50 ro <--  edge key
  //       1  9999 50 ro
  // ============================================================================================================

  // order1:
  auto order_1_id = te->GenUUID();
  auto const order1_qty = 50 * 1e6;
  auto const order1_price = 9999 * 1e4;
  FutureOneOfCreateOrderBuilder order_1(order_1_id, symbol, pz_index, ESide::Sell, order_type, order1_qty, order1_price,
                                        time_in_force, uid_1, coin);
  order_1.msg.mutable_create_order()->set_reduce_only(true);
  auto order_1_resp = user1.process(order_1.Build());
  ASSERT_EQ(order_1_resp->ret_code(), error::kErrorCodeSuccess);

  // 收order1 sell单result
  auto order_1_result = te->PopResult();
  ASSERT_NE(order_1_result.m_msg.get(), nullptr);
  // 检查order1
  auto order_1_check = order_1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_1_id);
  ASSERT_NE(order_1_check.m_msg, nullptr);
  order_1_check.CheckQty(uid_1, order1_qty);
  order_1_check.CheckPrice(uid_1, order1_price);
  order_1_check.CheckExecInst(uid_1, EExecInst::ReduceOnly);
  order_1_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

  // order2:
  auto order_2_id = te->GenUUID();
  auto const order2_qty = 60 * 1e6;
  auto const order2_price = 9999 * 1e4;
  FutureOneOfCreateOrderBuilder order_2(order_2_id, symbol, pz_index, ESide::Sell, order_type, order2_qty, order2_price,
                                        time_in_force, uid_1, coin);
  order_2.msg.mutable_create_order()->set_reduce_only(true);
  auto order_2_resp = user1.process(order_2.Build());
  ASSERT_EQ(order_2_resp->ret_code(), error::kErrorCodeSuccess);

  // 收order2 sell单result
  auto order_2_result = te->PopResult();
  ASSERT_NE(order_2_result.m_msg.get(), nullptr);
  // 检查order2
  auto order_2_check = order_2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_2_id);
  ASSERT_NE(order_2_check.m_msg, nullptr);
  order_2_check.CheckQty(uid_1, 50 * 1e6);
  order_2_check.CheckPrice(uid_1, order2_price);
  order_2_check.CheckExecInst(uid_1, EExecInst::ReduceOnly);
  order_2_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

  // ============================================================================================================
  // case1: box内下价格更优的ro订单，裁剪其他ro订单
  // order3, reduce_only, qty:15, price:9998
  //
  // closing_box:
  // extra qty = 0
  // sell  2  9999 50 ro <--  edge key
  //       1  9999 50 ro
  //
  // 报order3
  //
  // closing_box:
  // extra qty = 15
  // sell  2  9999 50 ro (os: new, cs:PendingReplace) <--  edge key
  //       1  9999 50 ro
  //       3  9998 15 ro (os: created, cs:WaitToSendNew)
  //
  // 裁order2 15qty: 50qty -> 35qty
  //
  // closing_box:
  // extra qty = 0
  // sell  2  9999 35 ro (os: new, cs:Replaced) <--  edge key
  //       1  9999 50 ro
  //       3  9998 15 ro (os: new, cs:NewAccepted)
  // ============================================================================================================

  // order3:
  auto order_3_id = te->GenUUID();
  auto const order3_qty = 15 * 1e6;
  auto const order3_price = 9998 * 1e4;
  FutureOneOfCreateOrderBuilder order_3(order_3_id, symbol, pz_index, ESide::Sell, order_type, order3_qty, order3_price,
                                        time_in_force, uid_1, coin);
  order_3.msg.mutable_create_order()->set_reduce_only(true);
  auto order_3_resp = user1.process(order_3.Build());
  ASSERT_EQ(order_3_resp->ret_code(), error::kErrorCodeSuccess);

  // order2先发生裁剪，获取order2的result
  order_2_result = te->PopResult();
  ASSERT_NE(order_2_result.m_msg.get(), nullptr);
  // 检查 裁order2 15qty: 50qty -> 35qty
  order_2_check = order_2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_2_id);
  order_2_check.CheckQty(uid_1, 35 * 1e6);
  order_2_check.CheckExecInst(uid_1, EExecInst::ReduceOnly);
  order_2_check.CheckStatus(EOrderStatus::New, ECrossStatus::Replaced);

  // order3发撮合，获取order3的result
  auto order_3_result = te->PopResult();
  ASSERT_NE(order_3_result.m_msg.get(), nullptr);
  // 检查 order3
  auto order_3_check = order_3_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_3_id);
  ASSERT_NE(order_3_check.m_msg, nullptr);
  order_3_check.CheckQty(uid_1, order3_qty);
  order_3_check.CheckPrice(uid_1, order3_price);
  order_3_check.CheckExecInst(uid_1, EExecInst::ReduceOnly);
  order_3_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

  // ============================================================================================================
  // case2: 价格次优的ro订单，撤其他ro订单并后裁剪自己
  // order4, reduce_only, qty:150, price:9998.5
  //
  // closing_box:
  // extra qty = 0
  // sell  2  9999 35 ro <--  edge key
  //       1  9999 50 ro
  //       3  9998 15 ro
  //
  // 报order4
  //
  // closing_box:
  // extra qty = 165
  // sell  2  9999   35  ro (os: new, cs:PendingCancel)
  //       1  9999   50  ro (os: new, cs:PendingCancel)
  //       4  9998.5 85  ro (os: created, cs:WaitToSendNew) <--  edge key
  //       3  9998   15  ro
  //
  // order2 被撤
  // order1 被撤
  // 裁 order4 65qty: 150qty -> 85qty
  //
  // closing_box:
  // extra qty = 0
  // sell  4  9998.5 85  ro (os: new, cs:NewAccepted) <--  edge key
  //       3  9998   15  ro
  // ============================================================================================================

  // order4:
  auto order_4_id = te->GenUUID();
  auto order4_qty = 150 * 1e6;
  auto order4_price = 9998.5 * 1e4;
  FutureOneOfCreateOrderBuilder order_4(order_4_id, symbol, pz_index, ESide::Sell, order_type, order4_qty, order4_price,
                                        time_in_force, uid_1, coin);
  order_4.msg.mutable_create_order()->set_reduce_only(true);
  auto order_4_resp = user1.process(order_4.Build());
  ASSERT_EQ(order_4_resp->ret_code(), error::kErrorCodeSuccess);

  // order2先发生撤单，获取order2的result
  order_2_result = te->PopResult();
  ASSERT_NE(order_2_result.m_msg.get(), nullptr);
  // 检查order2, qty: 35, price: 9999
  order_2_check = order_2_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_2_id);
  order_2_check.CheckQty(uid_1, 35 * 1e6);
  order_2_check.CheckPrice(uid_1, order2_price);
  order_2_check.CheckExecInst(uid_1, EExecInst::ReduceOnly);
  order_2_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);

  // order1再发生撤单，获取order1的result
  order_1_result = te->PopResult();
  ASSERT_NE(order_1_result.m_msg.get(), nullptr);
  // 检查order1, qty: 50, price: 9999
  order_1_check = order_1_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_1_id);
  order_1_check.CheckQty(uid_1, order1_qty);
  order_1_check.CheckPrice(uid_1, order1_price);
  order_1_check.CheckExecInst(uid_1, EExecInst::ReduceOnly);
  order_1_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);

  // order4 本地裁剪后发撮合，获取order4的result
  auto order_4_result = te->PopResult();
  ASSERT_NE(order_4_result.m_msg.get(), nullptr);
  // 检查order4, qty: 85, price: 9998.5
  auto order_4_check = order_4_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_4_id);
  order_4_check = order_4_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_4_id);
  order_4_check.CheckQty(uid_1, 85 * 1e6);
  order_4_check.CheckPrice(uid_1, order4_price);
  order_4_check.CheckExecInst(uid_1, EExecInst::ReduceOnly);
  order_4_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

  // ============================================================================================================
  // case3: box外下ro订单，被拒绝
  // order5, reduce_only, qty:10, price:9999
  //
  // closing_box:
  // extra qty = 0
  // sell  4  9998.5 85  ro <--  edge key
  //       3  9998   15  ro
  //
  // 报order5 qty:10, price:9999，被拒绝（closing_box不变）
  //
  // closing_box:
  // extra qty = 0
  // sell  4  9998.5 85  ro <--  edge key
  //       3  9998   15  ro
  // ============================================================================================================

  // order5:
  auto order_5_id = te->GenUUID();
  auto const order5_qty = 10 * 1e6;
  auto const order5_price = 9998.5 * 1e4;
  FutureOneOfCreateOrderBuilder order_5(order_5_id, symbol, pz_index, ESide::Sell, order_type, order5_qty, order5_price,
                                        time_in_force, uid_1, coin);
  order_5.msg.mutable_create_order()->set_reduce_only(true);
  auto order_5_resp = user1.process(order_5.Build());
  ASSERT_EQ(order_5_resp->ret_code(), error::kErrorCodeReduceOnlyRuleNotSatisfied);

  // ============================================================================================================
  // case4: box外普通单占用成本，修改box内ro订单，先模拟触发部分溢出，再模拟触发完全溢出
  // order6, normal_sell, qty:101, price:9999
  // order4, reduce_only, qty:85 -> 86, price: 9998.5 (触发部分溢出，发撮合前in-place时就被剪回85)
  // order4, reduce_only, qty:85, price: 9998.5 -> 9999.5 (触发完全溢出，被拒绝)
  //
  // closing_box:
  // extra qty = 0
  // sell  4  9998.5 85  ro <--  edge key
  //       3  9998   15  ro
  //
  // 报order6 qty:101, price:9999
  //
  // closing_box:
  // extra qty = 0
  // sell  6  9999   101
  //       4  9998.5 85  ro <--  edge key
  //       3  9998   15  ro
  //
  // 改order4 qty: 85 -> 86, 原地被还原到85（closing_box不变）
  // 改order4 price: 9998.5 -> 9999.5， 被拒绝（closing_box不变）
  //
  // closing_box:
  // extra qty = 0
  // sell  6  9999   101
  //       4  9998.5 85  ro <--  edge key
  //       3  9998   15  ro
  // ============================================================================================================

  // order6:
  auto order_6_id = te->GenUUID();
  auto const order6_qty = 101 * 1e6;
  auto order6_price = 9999 * 1e4;
  FutureOneOfCreateOrderBuilder order_6(order_6_id, symbol, pz_index, ESide::Sell, order_type, order6_qty, order6_price,
                                        time_in_force, uid_1, coin);
  auto order_6_resp = user1.process(order_6.Build());
  ASSERT_EQ(order_6_resp->ret_code(), error::kErrorCodeSuccess);

  // order6 创建成功，获取order4的result
  auto order_6_result = te->PopResult();
  ASSERT_NE(order_6_result.m_msg.get(), nullptr);
  // 检查order6，qty: 101, price: 9999
  auto order_6_check = order_6_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_6_id);
  ASSERT_NE(order_6_check.m_msg, nullptr);
  order_6_check.CheckQty(uid_1, order6_qty);
  order_6_check.CheckPrice(uid_1, order6_price);
  order_6_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

  // 改order4 qty: 85 -> 86, 触发部分裁剪，原地被还原到85
  order4_qty = 86 * 1e6;
  FutureOneOfReplaceOrderBuilder repl_order_qty_4(order_4_id, symbol, order4_qty, order4_price, uid_1, coin);
  order_4_resp = user1.process(repl_order_qty_4.Build());
  ASSERT_EQ(order_4_resp->ret_code(), error::kErrorCodeSuccess);

  // order4改单回报，获取order4的result
  order_4_result = te->PopResult();
  ASSERT_NE(order_4_result.m_msg.get(), nullptr);
  // 检查order4, qty: 85, price: 9998.5
  order_4_check = order_4_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_4_id);
  ASSERT_NE(order_4_check.m_msg, nullptr);
  order_4_check.CheckQty(uid_1, 85 * 1e6);
  order_4_check.CheckPrice(uid_1, order4_price);
  order_4_check.CheckExecInst(uid_1, EExecInst::ReduceOnly);
  order_4_check.CheckStatus(EOrderStatus::New, ECrossStatus::Replaced);

  // 改order4 price: 9998.5 -> 9999.5 触发全部裁剪，被拒绝（closing_box不变）
  order4_price = 9999.5 * 1e4;
  FutureOneOfReplaceOrderBuilder repl_order_price_4(order_4_id, symbol, order4_qty, order4_price, uid_1, coin);
  order_4_resp = user1.process(repl_order_price_4.Build());
  ASSERT_EQ(order_4_resp->ret_code(), error::kErrorCodeReduceOnlyRuleNotSatisfied);

  // ============================================================================================================
  // case5: 改box外普通单价格，提高优先级进入box，box内ro单被挤出
  // order6, normal_sell, qty:101, price:9999 -> 9998
  //
  // closing_box:
  // extra qty = 0
  // sell  6  9999   101    (os: new, cs:NewAccepted)
  //       4  9998.5 85  ro (os: new, cs:Replaced) <--  edge key
  //       3  9998   15  ro
  //
  // 改order6 qty: 101, price: 9999 -> 9998
  //
  // closing_box:
  // extra qty = 0
  // sell  4  9998.5 85  ro (os: new, cs:PendingCancel)
  //       6  9999   101    (os: new, cs:WaitToSendReplace)
  //       3  9998   15  ro  <--  edge key
  //
  // order4 被撤
  //
  // closing_box:
  // extra qty = 0
  // sell  6  9998   101    (os: new, cs:ReAdded)
  //       3  9998   15  ro  <--  edge key
  // ============================================================================================================

  // 改order6 price: 9999 -> 9998
  order6_price = 9998 * 1e4;
  FutureOneOfReplaceOrderBuilder repl_order_6(order_6_id, symbol, order6_qty, order6_price, uid_1, coin);
  order_6_resp = user1.process(repl_order_6.Build());
  ASSERT_EQ(order_6_resp->ret_code(), error::kErrorCodeSuccess);

  // order4先发生撤单，获取order4的result
  order_4_result = te->PopResult();
  ASSERT_NE(order_4_result.m_msg.get(), nullptr);
  // 检查order4 cancel
  order_4_check = order_4_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_4_id);
  order_4_check.CheckQty(uid_1, 85 * 1e6);
  order_4_check.CheckPrice(uid_1, 9998.5 * 1e4);
  order_4_check.CheckExecInst(uid_1, EExecInst::ReduceOnly);
  order_4_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);

  // order6改单发撮合，获取order6的result
  order_6_result = te->PopResult();
  ASSERT_NE(order_6_result.m_msg.get(), nullptr);
  // 检查order6, qty: 101, price: 9998
  order_6_check = order_6_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_6_id);
  ASSERT_NE(order_6_check.m_msg, nullptr);
  order_6_check.CheckQty(uid_1, order6_qty);
  order_6_check.CheckPrice(uid_1, order6_price);
  order_6_check.CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded);
}

TEST_F(ReduceOnlyTest, COT) {
  biz::coin_t const coin = 5;      // BTC
  biz::symbol_t const symbol = 5;  // BTCUSDT
  EPositionIndex const pz_index = EPositionIndex::Single;
  EOrderType const order_type = EOrderType::Limit;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  // user1
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin2 = 5;
    int wallet_record_type = 1;
    std::string amount = "1300";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid_1, trans_id, coin2, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 切到全仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid_1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Cross);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid_1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
  }

  // opposite user2
  biz::user_id_t const uid_2 = 658002;
  stub user2(te, uid_2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin2 = 5;
    int wallet_record_type = 1;
    std::string amount = "*********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid_2, trans_id, coin2, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 10000, 10000, 10000);
  // user1: create position 100qty
  biz::size_x_t const pz_qty = 100 * 1e6;
  biz::price_x_t const pz_price = 10000 * 1e4;

  // case准备 -- 构建持仓 100qty
  // ============================================================================================================
  // user1 买入开仓订单 100qty
  auto user1_pz_buy_id = te->GenUUID();
  FutureOneOfCreateOrderBuilder pz_buy_order_build(user1_pz_buy_id, symbol, pz_index, ESide::Buy, order_type, pz_qty,
                                                   pz_price, time_in_force, uid_1, coin);
  auto pz_buy_order_resp = user1.process(pz_buy_order_build.Build());

  // 收user1 buy单result
  auto pz_buy_order_result = te->PopResult();
  ASSERT_NE(pz_buy_order_result.m_msg.get(), nullptr);
  auto pz_buy_order_check = pz_buy_order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(user1_pz_buy_id);
  ASSERT_NE(pz_buy_order_check.m_msg, nullptr);
  pz_buy_order_check.CheckQty(uid_1, pz_qty);
  pz_buy_order_check.CheckPrice(uid_1, pz_price);
  pz_buy_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

  // user2 卖出开仓订单 100qty（主要为了给user1建立持仓）
  auto user2_pz_sell_id = te->GenUUID();
  FutureOneOfCreateOrderBuilder pz_sell_order_build(user2_pz_sell_id, symbol, pz_index, ESide::Sell, order_type, pz_qty,
                                                    pz_price, time_in_force, uid_2, coin);
  auto pz_sell_order_resp = user2.process(pz_sell_order_build.Build());
  ASSERT_EQ(pz_sell_order_resp->ret_code(), error::kErrorCodeSuccess);

  // 收user2 sell单result
  auto pz_sell_order_result = te->PopResult();
  ASSERT_NE(pz_sell_order_result.m_msg.get(), nullptr);
  auto pz_sell_order_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(user2_pz_sell_id);
  ASSERT_NE(pz_sell_order_check.m_msg, nullptr);
  pz_sell_order_check.CheckQty(uid_2, pz_qty);
  pz_sell_order_check.CheckPrice(uid_2, pz_price);
  pz_sell_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

  // 检查user2 持仓
  auto user2_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
  user2_pz_check.CheckSize(pz_qty);
  user2_pz_check.CheckFreeSize(pz_qty);

  // 收user1 buy单成交回报
  pz_buy_order_result = te->PopResult();
  ASSERT_NE(pz_buy_order_result.m_msg.get(), nullptr);
  // 检查user1 持仓
  auto user1_pz_check = pz_buy_order_result.RefFutureMarginResult().RefRelatedPosition(0);
  user1_pz_check.CheckSize(pz_qty);
  auto buy_wallet_check = pz_buy_order_result.RefAssetMarginResult().RefRelatedWallet(5);
  ASSERT_NE(buy_wallet_check.m_msg, nullptr);
  auto ab = buy_wallet_check.m_msg->wallet().available_balance();

  // user1卖，普通订单，站住ob
  auto user1_pz_sell_id = te->GenUUID();
  FutureOneOfCreateOrderBuilder user1_sell_order_build(user1_pz_sell_id, symbol, pz_index, ESide::Sell, order_type,
                                                       pz_qty, pz_price, time_in_force, uid_1, coin);
  auto user1_pz_sell_resp = user1.process(user1_sell_order_build.Build());
  auto user1_sell_result = te->PopResult();
  ASSERT_NE(user1_sell_result.m_msg.get(), nullptr);
  auto user1_wallet_check = user1_sell_result.RefAssetMarginResult().RefRelatedWallet(5);
  ASSERT_NE(user1_wallet_check.m_msg, nullptr);
  ab = user1_wallet_check.m_msg->wallet().available_balance();

  // user1卖 COT订单，价格优先，可以取消普通订单
  biz::price_x_t pz_price_cot = 9800 * 1e4;
  auto user1_pz_sell_id_cot = te->GenUUID();
  FutureOneOfCreateOrderBuilder user1_sell_cot_order_build(user1_pz_sell_id_cot, symbol, pz_index, ESide::Sell,
                                                           order_type, pz_qty, pz_price_cot, time_in_force, uid_1,
                                                           coin);
  user1_sell_cot_order_build.msg.mutable_create_order()->set_reduce_only(true);
  user1_sell_cot_order_build.msg.mutable_create_order()->set_close_on_trigger(true);

  auto user1_sell_cot_order_resp = user1.process(user1_sell_cot_order_build.Build());
  EXPECT_EQ(user1_sell_cot_order_resp->ret_code(), error::kErrorCodeSuccess);

  auto user1_cancel_result = te->PopResult();
  ASSERT_NE(user1_cancel_result.m_msg.get(), nullptr);
  auto user1_cancel_ord_check = user1_cancel_result.RefFutureMarginResult().RefRelatedOrderByOrderId(user1_pz_sell_id);
  ASSERT_NE(user1_cancel_ord_check.m_msg, nullptr);
  auto os = user1_cancel_ord_check.m_msg->order_status();
  EXPECT_EQ(os, EOrderStatus::Cancelled);
  auto cancel_type = user1_cancel_ord_check.m_msg->cancel_type();
  EXPECT_EQ(static_cast<ECancelType>(cancel_type), ECancelType::CancelByReduceOnly);
}

TEST_F(ReduceOnlyTest, COT_pending_cancel) {
  biz::coin_t const coin = 5;      // BTC
  biz::symbol_t const symbol = 5;  // BTCUSDT
  EPositionIndex const pz_index = EPositionIndex::Single;
  EOrderType const order_type = EOrderType::Limit;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  // user1
  biz::user_id_t const uid_1 = 658001;
  stub user1(te, uid_1);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin2 = 5;
    int wallet_record_type = 1;
    std::string amount = "1300";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid_1, trans_id, coin2, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  // opposite user2
  biz::user_id_t const uid_2 = 658002;
  stub user2(te, uid_2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin2 = 5;
    int wallet_record_type = 1;
    std::string amount = "*********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid_2, trans_id, coin2, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 10000, 10000, 10000);
  // user1: create position 100qty
  biz::size_x_t const pz_qty = 100 * 1e6;
  biz::price_x_t const pz_price = 10000 * 1e4;

  // case准备 -- 构建持仓 100qty
  // ============================================================================================================
  // user1 买入开仓订单 100qty
  auto user1_pz_buy_id = te->GenUUID();
  FutureOneOfCreateOrderBuilder pz_buy_order_build(user1_pz_buy_id, symbol, pz_index, ESide::Buy, order_type, pz_qty,
                                                   pz_price, time_in_force, uid_1, coin);
  auto pz_buy_order_resp = user1.process(pz_buy_order_build.Build());

  // 收user1 buy单result
  auto pz_buy_order_result = te->PopResult();
  ASSERT_NE(pz_buy_order_result.m_msg.get(), nullptr);
  auto pz_buy_order_check = pz_buy_order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(user1_pz_buy_id);
  ASSERT_NE(pz_buy_order_check.m_msg, nullptr);
  pz_buy_order_check.CheckQty(uid_1, pz_qty);
  pz_buy_order_check.CheckPrice(uid_1, pz_price);
  pz_buy_order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

  // user2 卖出开仓订单 100qty（主要为了给user1建立持仓）
  auto user2_pz_sell_id = te->GenUUID();
  FutureOneOfCreateOrderBuilder pz_sell_order_build(user2_pz_sell_id, symbol, pz_index, ESide::Sell, order_type, pz_qty,
                                                    pz_price, time_in_force, uid_2, coin);
  auto pz_sell_order_resp = user2.process(pz_sell_order_build.Build());
  ASSERT_EQ(pz_sell_order_resp->ret_code(), error::kErrorCodeSuccess);

  // 收user2 sell单result
  auto pz_sell_order_result = te->PopResult();
  ASSERT_NE(pz_sell_order_result.m_msg.get(), nullptr);
  auto pz_sell_order_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(user2_pz_sell_id);
  ASSERT_NE(pz_sell_order_check.m_msg, nullptr);
  pz_sell_order_check.CheckQty(uid_2, pz_qty);
  pz_sell_order_check.CheckPrice(uid_2, pz_price);
  pz_sell_order_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

  // 检查user2 持仓
  auto user2_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
  user2_pz_check.CheckSize(pz_qty);
  user2_pz_check.CheckFreeSize(pz_qty);

  // 收user1 buy单成交回报
  pz_buy_order_result = te->PopResult();
  ASSERT_NE(pz_buy_order_result.m_msg.get(), nullptr);
  // 检查user1 持仓
  auto user1_pz_check = pz_buy_order_result.RefFutureMarginResult().RefRelatedPosition(0);
  user1_pz_check.CheckSize(pz_qty);
  auto buy_wallet_check = pz_buy_order_result.RefAssetMarginResult().RefRelatedWallet(5);
  ASSERT_NE(buy_wallet_check.m_msg, nullptr);
  auto ab = buy_wallet_check.m_msg->wallet().available_balance();

  // user1卖，普通订单，站住ob
  auto user1_pz_sell_id = te->GenUUID();
  FutureOneOfCreateOrderBuilder user1_sell_order_build(user1_pz_sell_id, symbol, pz_index, ESide::Sell, order_type,
                                                       pz_qty, pz_price, time_in_force, uid_1, coin);
  auto user1_pz_sell_resp = user1.process(user1_sell_order_build.Build());
  auto user1_sell_result = te->PopResult();
  ASSERT_NE(user1_sell_result.m_msg.get(), nullptr);
  auto user1_wallet_check = user1_sell_result.RefAssetMarginResult().RefRelatedWallet(5);
  ASSERT_NE(user1_wallet_check.m_msg, nullptr);
  ab = user1_wallet_check.m_msg->wallet().available_balance();

  // 关掉撮合
  te->SuspendCross();

  // 取消上一笔订单
  FutureOneOfCancelOrderBuilder cancel_cot(user1_pz_sell_id, symbol, uid_1, coin);
  auto resp_cancel = user1.process(cancel_cot.Build());
  ASSERT_NE(resp_cancel, nullptr);
  EXPECT_EQ(resp_cancel->ret_code(), error::kErrorCodeSuccess);

  // user1卖 COT订单，价格优先，可以取消普通订单
  biz::price_x_t pz_price_cot = 9800 * 1e4;
  auto user1_pz_sell_id_cot = te->GenUUID();
  FutureOneOfCreateOrderBuilder user1_sell_cot_order_build(user1_pz_sell_id_cot, symbol, pz_index, ESide::Sell,
                                                           order_type, pz_qty, pz_price_cot, time_in_force, uid_1,
                                                           coin);
  user1_sell_cot_order_build.msg.mutable_create_order()->set_reduce_only(true);
  user1_sell_cot_order_build.msg.mutable_create_order()->set_close_on_trigger(true);

  auto user1_sell_cot_order_resp = user1.process(user1_sell_cot_order_build.Build());
  EXPECT_EQ(user1_sell_cot_order_resp->ret_code(), error::kErrorCodeSuccess);

  // 打开撮合
  te->ResumeCross();
  auto user1_cancel_result = te->PopResult();
  ASSERT_NE(user1_cancel_result.m_msg.get(), nullptr);
  auto user1_cancel_ord_check = user1_cancel_result.RefFutureMarginResult().RefRelatedOrderByOrderId(user1_pz_sell_id);
  ASSERT_NE(user1_cancel_ord_check.m_msg, nullptr);
  auto os = user1_cancel_ord_check.m_msg->order_status();
  EXPECT_EQ(os, EOrderStatus::Cancelled);
}
