//
// Created by Gdso.li on 31/3/2023.
//
#pragma once

#ifndef TEST_BIZ_TRADE_FUTURE_ORDER_STOP_ORDER_STOP_ORDER_BASIC_HPP_
#define TEST_BIZ_TRADE_FUTURE_ORDER_STOP_ORDER_STOP_ORDER_BASIC_HPP_

#include <gtest/gtest.h>

#include <iostream>
#include <memory>

#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class StopOrderBasicTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void waitRecvHandler() {
    auto& io_service = config::ConfigProxy::Instance().io_service_;
    bool io_service_arrived = false;
    int i = 0;
    io_service.post([&] { io_service_arrived = true; });
    usleep(1000);
    while (++i < 1000) {
      if (io_service_arrived) {
        break;
      }
      usleep(10000);
    }
    if (i >= 1000) {
      LOG_ERROR("io service timeout, recv handler may not take effect");
    }
  }

  static std::shared_ptr<tmock::CTradeAppMock> te;
};

#endif  // TEST_BIZ_TRADE_FUTURE_ORDER_STOP_ORDER_STOP_ORDER_BASIC_HPP_
