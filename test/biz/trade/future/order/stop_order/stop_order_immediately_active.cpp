//
// Created by Gdso.li on 31/3/2023.
//
#include "test/biz/trade/future/order/stop_order/stop_order_immediately_active.hpp"

#include <unistd.h>

#include <string>

#include "proto/gen/enums/ebizcode/biz_code.pb.h"
#include "src/data/enum.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

std::shared_ptr<tmock::CTradeAppMock> StopOrderSpecialActiveTest::te;

TEST_F(StopOrderSpecialActiveTest, trigger_to_active_by_quote_falling) {
  biz::user_id_t uid = 653106;
  biz::coin_t const coin = 5;
  biz::cross_idx_t const cross_idx = 5;
  biz::symbol_t const symbol = 5;

  // 构建客户端
  stub user1(te, uid);

  // 充值
  auto req_id = te->GenUUID();
  auto trans_id = te->GenUUID();
  auto bonus_change = "0";  // 赠金
  EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type),
                                     std::to_string(50000), bonus_change);
  // 发rpc请求
  auto deposit_resp = user1.process(deposit_build.Build());
  // 校验rpc回报
  ASSERT_EQ(deposit_resp->ret_code(), error::kErrorCodeSuccess);
  // 取trading result
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg, nullptr);

  // ============================================================================================================
  // 校准行情
  // ============================================================================================================
  // 校准标记价格 (mark_price)29709.32 < (trigger_price)30982
  auto underlying_price = bbase::decimal::Decimal<>("29709.32");
  auto const exchange_rate = bbase::decimal::Decimal<>("1");
  te->UpdateMarketData(symbol, underlying_price, exchange_rate);

  // 校准最新价格 (last_price)30990 > (trigger_price)30982
  bool sync_trigger = true;
  auto lv0_px = "30990";
  auto lv0_qty = "100";
  te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, sync_trigger);

  // ============================================================================================================
  // 报单
  // ============================================================================================================
  std::string order_id = "6602f5ed-05fe-45e6-9c7e-ee675280d612";
  EPositionIndex const position_idx = EPositionIndex::Single;
  ESide const side = ESide::Sell;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t const price = 0;
  EOrderType const order_type = EOrderType::Market;
  ETimeInForce const time_in_force = ETimeInForce::ImmediateOrCancel;
  EStopOrderType const stop_order_type = EStopOrderType::Stop;
  ETriggerBy const trigger_by = ETriggerBy::LastPrice;
  biz::price_x_t const trigger_price = 30982 * 1e4;
  biz::price_x_t base_price = 30990 * 1e4;
  EPriceDirection const expected_direction = EPriceDirection::Falling;
  bool const close_on_trigger = false;
  biz::price_x_t const tp_x = 0;
  biz::price_x_t const sl_x = 0;
  ESmpType const smp_type = ESmpType::None;
  int32_t const smp_group = 0;
  FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
      uid, coin, symbol, order_id, position_idx, side, order_type, qty, price, time_in_force, stop_order_type,
      trigger_by, trigger_price, expected_direction, base_price, close_on_trigger, tp_x, sl_x, smp_type, smp_group);

  // 发rpc请求
  auto create_so_resp = user1.process(create_so_build.Build());
  // 校验rpc回报
  ASSERT_EQ(create_so_resp->ret_code(), error::kErrorCodeSuccess);

  // 取trading result
  auto create_so_result = te->PopResult();
  ASSERT_NE(create_so_result.m_msg, nullptr);

  // 获取订单数据
  auto order_check = create_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  // 校验订单状态
  order_check.CheckQty(uid, qty);
  order_check.CheckPrice(uid, price);
  order_check.CheckTimeInForce(time_in_force);
  order_check.CheckExecInst(uid, EExecInst::None);
  order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);

  bool to_check_res = false;
  bool checked = false;
  int try_times = 0;  // 最多尝试1次，最多2.5s
  while (!checked && try_times < 2) {
    usleep(500 * 1000);

    // 给Trigger发一个timer事件， 把Triggered订单发给PreWorker
    auto time_event = std::make_shared<event::TimerEvent>(0);
    time_event->interval_ms_ = application::GlobalVarManager::Instance().futures_trigger_timer_interval_ms();
    time_event->trigger_time_ns_ = bbase::utils::Time::GetTimeNs();
    auto ret = te->pipeline()->ring_buffers(worker::WorkerType::kFuturesTriggerWorker)->Write(time_event, true);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);

    // 正确的情况是下面的逻辑都不会走到， 条件单不会被触发
    // 取trading result
    auto active_so_result = te->PopResult();
    if (active_so_result.m_msg == nullptr) {
      // 没拿到active_so的result, 继续发标记价格，尝试触发
      try_times++;
      continue;
    }

    // 拿到active_so的result, 说明触发成功，不需要循环发标记价和timer了，走完下面的检查流程即可
    checked = true;

    // 获取订单数据
    order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckQty(uid, qty);
    order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);

    // 取trading result
    active_so_result = te->PopResult();
    ASSERT_NE(active_so_result.m_msg, nullptr);

    // 获取订单数据
    order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    order_check.CheckQty(uid, qty);
    order_check.CheckStopOrder(stop_order_type, trigger_by, trigger_price, expected_direction);
    order_check.CheckExecInst(uid, EExecInst::None);
    // 这个中间过程目前框架截取不了，直接检查终态（New, NewAccepted）
    //  order_check.CheckStatus(EOrderStatus::Triggered,
    //  ECrossStatus::PendingNew);
    order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);

    to_check_res = true;
  }
  ASSERT_EQ(to_check_res, false);
}

TEST_F(StopOrderSpecialActiveTest, trigger_immediate_fail) {
  biz::user_id_t const uid1 = 100000;
  stub user1(te, uid1);
  {
    // user1充值
    FutureDepositBuilder deposit_build("deposit-req-id", uid1, "deposit-trans-id", ECoin::USDT,
                                       EWalletRecordType::Deposit, "100000000", "123");
    auto resp = user1.process(deposit_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
  }

  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  {
    // siteAPI不受影响
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
        uid1, ECoin::USDT, ESymbol::BTCUSDT, te->GenUUID(), EPositionIndex::Single, ESide::Buy, EOrderType::Market,
        0.1 * 1e8, 0, ETimeInForce::ImmediateOrCancel, EStopOrderType::Stop, ETriggerBy::LastPrice, 18000 * 1e4,
        EPriceDirection::Rising, 18500 * 1e4, false, 0, 0, ESmpType::None, 0);
    auto resp = user1.process(create_so_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::RisingWouldTriggerImmediately);
  }

  {
    // OpenApiV5: 30074->110092，30075->110093
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.1", "18500");
    create_build.SetOrderLinkID(te->GenUUID());
    create_build.SetTrigger("MarkPrice", 1, "18000");
    RpcContext ct{};
    auto resp = user1.create_order(create_build.Build(), ct);
    ASSERT_NE(resp.get(), nullptr);
    EXPECT_EQ(ct.RetCode(), 110092);

    OpenApiV5CreateOrderBuilder create_build2("linear", "BTCUSDT", 0, "Sell", "Limit", "0.1", "18500");
    create_build2.SetOrderLinkID(te->GenUUID());
    create_build2.SetTrigger("MarkPrice", 2, "20050");
    RpcContext ct2{};
    auto resp2 = user1.create_order(create_build2.Build(), ct2);
    ASSERT_NE(resp2.get(), nullptr);
    EXPECT_EQ(ct2.RetCode(), 110093);
  }
}
