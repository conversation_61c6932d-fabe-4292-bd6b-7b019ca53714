#pragma once

#include <gtest/gtest.h>

#include <iostream>
#include <memory>

#include "lib/stub.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class SwitchMarginAutoAdjustTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestSuite() {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  static void TearDownTestSuite() {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }

  static void BuildProduceMsg(biz::cross_idx_t cross_idx, biz::symbol_t symbol, std::int64_t exec_price,
                              std::int64_t exec_time, std::int64_t fee_rate, biz::cross::xReqSender& sender,
                              bbase::hdts::Producer::Message& msg);
  static void SendFundingMessage(biz::cross_idx_t cross_idx, biz::symbol_t symbol, std::int64_t exec_price,
                                 std::int64_t exec_time, std::int64_t fee_rate);
  static int32_t SwitchMarginMode(stub& user, enums::eaccountmode::AccountMode account_mode);
  static std::shared_ptr<tmock::CTradeAppMock> te;
};
