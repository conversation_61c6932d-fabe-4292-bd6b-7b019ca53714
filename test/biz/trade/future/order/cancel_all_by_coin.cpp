//
// Created by SH00678ML on 2023/3/25.
//
#include "test/biz/trade/future/order/cancel_all_by_coin.hpp"

#include <net/if.h>

#include <memory>
#include <string>
#include <vector>

#include "data/enum.hpp"
#include "enums/ecanceltype/cancel_type.pb.h"
#include "enums/ecrossstatus/cross_status.pb.h"
#include "proto/gen/enums/ebizcode/biz_code.pb.h"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

int32_t cancel_all_by_coin_test::DepositAndSwitchToCrossMode(biz::user_id_t uid, std::string amount, biz::coin_t coin) {
  stub user(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string bonus_change = "0";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp = user.process(deposit_build.Build());
    auto result = te->PopResult();
    if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
      return -1;
    }
  }

  {
    // 切到全仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Cross);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      return -1;
    }
  }

  return 0;
}

int32_t cancel_all_by_coin_test::SwitchBothSide(biz::user_id_t uid) {
  stub user(te, uid);
  FutureOneOfSwitchPositionModeBuilder switch_position_mode(ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::BothSide,
                                                            uid);
  auto resp = user.process(switch_position_mode.Build());
  auto result = te->PopResult();
  if (resp->ret_code() != 0 || result.m_msg.get() == nullptr) {
    return -1;
  }
  return 0;
}
TEST_F(cancel_all_by_coin_test, cancel_all_by_coin) {
  //  GTEST_SKIP();
  biz::user_id_t uid = 10001;
  stub user1(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  auto usdt_order_id = te->GenUUID();
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  ESide side = ESide::Sell;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 100000;
  biz::price_x_t price = 200000000;
  biz::coin_t coin = 5;

  FutureOneOfCreateOrderBuilder create_build(usdt_order_id, symbol, pz_index, side, order_type, qty, price,
                                             ETimeInForce::GoodTillCancel, uid, coin);
  auto resp1 = user1.process(create_build.Build());
  auto result1 = te->PopResult();
  ASSERT_NE(result1.m_msg.get(), nullptr);
  auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(usdt_order_id);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  orderCheck.Check(uid, 100000);
  orderCheck.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

  auto usdc_order_id = te->GenUUID();
  symbol = 45;  // BTCPERP;
  te->BatchAddMarkPrice(std::vector<tmock::MockPrice>{{static_cast<ESymbol>(5), 20000, 20000, 20000},
                                                      {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  FutureOneOfCreateOrderBuilder create_build2(usdc_order_id, symbol, pz_index, side, order_type, qty, price,
                                              ETimeInForce::GoodTillCancel, uid, 16);
  auto respusdc = user1.process(create_build2.Build());
  auto resultusdc = te->PopResult();
  ASSERT_NE(resultusdc.m_msg.get(), nullptr);
  auto orderCheckusdc = resultusdc.RefFutureMarginResult().RefRelatedOrderByOrderId(usdc_order_id);
  ASSERT_NE(orderCheckusdc.m_msg, nullptr);
  orderCheckusdc.Check(uid, 100000);
  orderCheckusdc.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

  ECancelType cancelType = ECancelType::CancelByUser;
  ECancelAllType cancelAllType = ECancelAllType::OpenOrder;
  biz::remark_t remark{};
  FutureCancelAllByCoinBuilder cancelAllCoinBuilder(coin, uid, cancelType, cancelAllType, remark.GetValue());
  auto resp2 = user1.process(cancelAllCoinBuilder.Build());
  auto result2 = te->PopResult();

  auto margin_result = result2.RefFutureMarginResult();
  orderCheck = margin_result.RefRelatedOrderByOrderId(usdt_order_id);
  ASSERT_NE(orderCheck.m_msg, nullptr);
  ASSERT_EQ(orderCheck.m_msg->order_status(), EOrderStatus::Cancelled);
}

TEST_F(cancel_all_by_coin_test, cancel_all_by_coin_cancel_only_so_after_partial_active) {
  GTEST_SKIP();
  biz::user_id_t uid = 10002;
  stub user1(te, uid);

  biz::coin_t const coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  {
    // 校准标记价格 (mark_price)10000
    auto underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);
  }

  // ============================================================================================================
  // 报单
  // ============================================================================================================
  EPositionIndex const position_idx = EPositionIndex::Single;
  ESide const side = ESide::Sell;
  EOrderType const order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.1 * 1e6;
  biz::price_x_t const price = 10060 * 1e4;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;
  EStopOrderType const stop_order_type = EStopOrderType::Stop;
  ETriggerBy const trigger_by = ETriggerBy::MarkPrice;
  EPriceDirection const expected_direction = EPriceDirection::Rising;
  biz::price_x_t base_price = 0;
  bool const close_on_trigger = false;
  biz::price_x_t const tp_x = 0;
  biz::price_x_t const sl_x = 0;
  ESmpType const smp_type = ESmpType::None;
  int32_t const smp_group = 0;

  std::string so1_id = "69c5700a-c2d6-498f-b952-6dedbafd1e9f";
  biz::price_x_t const so1_trigger_price = 10050 * 1e4;
  {
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build(uid, coin, symbol, so1_id, position_idx, side, order_type,
                                                             qty, price, time_in_force, stop_order_type, trigger_by,
                                                             so1_trigger_price, expected_direction, base_price,
                                                             close_on_trigger, tp_x, sl_x, smp_type, smp_group);

    // 发rpc请求
    auto create_so_resp = user1.process(create_so_build.Build());
    // 校验rpc回报
    ASSERT_EQ(create_so_resp->ret_code(), error::kErrorCodeSuccess);

    // 取trading result
    auto create_so_result = te->PopResult();
    ASSERT_NE(create_so_result.m_msg, nullptr);

    // 获取订单数据
    auto order_check = create_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(so1_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    // 校验订单状态
    order_check.CheckQty(uid, qty);
    order_check.CheckPrice(uid, price);
    order_check.CheckStopOrder(stop_order_type, trigger_by, so1_trigger_price, expected_direction);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
  }

  std::string so2_id = "3cec5d1a-7d37-4e71-9629-fb938aef2ccb";
  biz::price_x_t const so2_trigger_price = 10100 * 1e4;
  {
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build(uid, coin, symbol, so2_id, position_idx, side, order_type,
                                                             qty, price, time_in_force, stop_order_type, trigger_by,
                                                             so2_trigger_price, expected_direction, base_price,
                                                             close_on_trigger, tp_x, sl_x, smp_type, smp_group);

    // 发rpc请求
    auto create_so_resp = user1.process(create_so_build.Build());
    // 校验rpc回报
    ASSERT_EQ(create_so_resp->ret_code(), error::kErrorCodeSuccess);

    // 取trading result
    auto create_so_result = te->PopResult();
    ASSERT_NE(create_so_result.m_msg, nullptr);

    // 获取订单数据
    auto order_check = create_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(so2_id);
    ASSERT_NE(order_check.m_msg, nullptr);

    // 校验订单状态
    order_check.CheckQty(uid, qty);
    order_check.CheckPrice(uid, price);
    order_check.CheckStopOrder(stop_order_type, trigger_by, so2_trigger_price, expected_direction);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
  }

  // 触发so1
  {
    // 模拟标记价格上涨 (mark_price)10055 > (so1-trigger_price)10050, 不触发so2
    auto underlying_price = bbase::decimal::Decimal<>("10055");
    auto const exchange_rate = bbase::decimal::Decimal<>("1");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    bool to_check_res = false;
    bool checked = false;
    int try_times = 0;  // 最多尝试1次，最多6s
    while (!checked && try_times < 2) {
      // 取trading result
      auto active_so_result = te->PopResult();
      if (active_so_result.m_msg == nullptr) {
        // 没拿到active_so的result, 继续等
        try_times++;
        continue;
      }

      // 拿到active_so的result, 说明触发成功，不需要循环发标记价和timer了，走完下面的检查流程即可
      checked = true;

      // 获取订单数据
      auto order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(so1_id);
      ASSERT_NE(order_check.m_msg, nullptr);

      order_check.CheckQty(uid, qty);
      order_check.CheckPrice(uid, price);
      order_check.CheckStopOrder(stop_order_type, trigger_by, so1_trigger_price, expected_direction);
      order_check.CheckExecInst(uid, EExecInst::None);
      order_check.CheckStatus(EOrderStatus::Triggered, ECrossStatus::Init);

      // 取trading result
      active_so_result = te->PopResult();
      ASSERT_NE(active_so_result.m_msg, nullptr);

      // 获取订单数据
      order_check = active_so_result.RefFutureMarginResult().RefRelatedOrderByOrderId(so1_id);
      ASSERT_NE(order_check.m_msg, nullptr);

      order_check.CheckQty(uid, qty);
      order_check.CheckPrice(uid, price);
      order_check.CheckStopOrder(stop_order_type, trigger_by, so1_trigger_price, expected_direction);
      order_check.CheckExecInst(uid, EExecInst::None);
      order_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

      to_check_res = true;
    }
    ASSERT_EQ(to_check_res, true);
  }

  ECancelType cancel_type = ECancelType::CancelByUser;
  ECancelAllType cancel_all_type = ECancelAllType::OnlyNormalStop;
  biz::remark_t remark{};
  FutureCancelAllByCoinBuilder cancel_all_by_coin_builder(coin, uid, cancel_type, cancel_all_type, remark.GetValue());
  auto cancel_all_resp = user1.process(cancel_all_by_coin_builder.Build());
  // 校验rpc回报
  ASSERT_EQ(cancel_all_resp->ret_code(), error::kErrorCodeSuccess);

  // 取trading result
  auto result = te->PopResult();
  auto f_margin_result = result.RefFutureMarginResult();

  // 分别检查两个订单
  auto cancel_order1_check = f_margin_result.RefRelatedOrderByOrderId(so1_id);
  ASSERT_EQ(cancel_order1_check.m_msg, nullptr);

  auto cancel_order2_check = f_margin_result.RefRelatedOrderByOrderId(so2_id);
  ASSERT_NE(cancel_order2_check.m_msg, nullptr);
  cancel_order2_check.CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);
}

TEST_F(cancel_all_by_coin_test, cancel_all_by_coin_open_v5_only_active) {
  biz::user_id_t uid = 10003;
  stub user(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->BatchAddMarkPrice(std::vector<tmock::MockPrice>{{static_cast<ESymbol>(5), 20000, 20000, 20000},
                                                      {static_cast<ESymbol>(45), 20000, 20000, 20000}});

  auto usdt_order_link_id = te->GenUUID();
  std::string usdt_order_id;
  {
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
    create_build.SetOrderLinkID(usdt_order_link_id);
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp->order_id(), "");
    ASSERT_STREQ(resp->order_link_id().c_str(), usdt_order_link_id.c_str());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(usdt_order_link_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(uid, 100000);
    orderCheck.CheckPrice(uid, 20000 * 1e4);
    usdt_order_id = resp->order_id();
    orderCheck.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
  }

  auto usdc_order_link_id = te->GenUUID();
  std::string usdc_order_id;
  // auto symbol = 45;
  {
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCPERP", 0, "Sell", "Limit", "0.001", "20000");
    create_build.SetOrderLinkID(usdc_order_link_id);
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp->order_id(), "");
    ASSERT_STREQ(resp->order_link_id().c_str(), usdc_order_link_id.c_str());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(usdc_order_link_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(uid, 100000);
    orderCheck.CheckPrice(uid, 20000 * 1e4);
    usdc_order_id = resp->order_id();
    orderCheck.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
  }

  std::string usdt_stop_order_id;
  std::string usdt_stop_order_link_id = "f46608e2-9c2b-47a7-b1c5-bbc2d2adcbfc";
  {
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.1", "10100");
    create_build.SetOrderLinkID(usdt_stop_order_link_id);
    create_build.SetTrigger("MarkPrice", 2, "10050");
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp->order_id(), "");
    ASSERT_STREQ(resp->order_link_id().c_str(), usdt_stop_order_link_id.c_str());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(usdt_stop_order_link_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    order_check.Check(uid, 10000000);
    order_check.CheckPrice(uid, 10100 * 1e4);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
    usdt_stop_order_id = resp->order_id();
  }

  std::string usdc_stop_order_id;
  std::string usdc_stop_order_link_id = "b79600a8-d5e1-47ac-9e34-c7923f2f9057";
  {
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCPERP", 0, "Sell", "Limit", "0.1", "10100");
    create_build.SetOrderLinkID(usdc_stop_order_link_id);
    create_build.SetTrigger("MarkPrice", 2, "10050");
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp->order_id(), "");
    ASSERT_STREQ(resp->order_link_id().c_str(), usdc_stop_order_link_id.c_str());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(usdc_stop_order_link_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    order_check.Check(uid, 10000000);
    order_check.CheckPrice(uid, 10100 * 1e4);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
    usdc_stop_order_id = resp->order_id();
  }

  {
    CancelAllOrderReqV5Builder cancel_all_build("linear", "", "", "USDT", "Order");
    auto resp = user.cancel_all(cancel_all_build.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(1, resp->list_size());
    ASSERT_EQ(usdt_order_id, resp->list(0).order_id());
    ASSERT_EQ(usdt_order_link_id, resp->list(0).order_link_id());
  }

  {
    CancelAllOrderReqV5Builder cancel_all_build("linear", "", "", "USDC", "Order");
    auto resp = user.cancel_all(cancel_all_build.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(1, resp->list_size());
    ASSERT_EQ(usdc_order_id, resp->list(0).order_id());
    ASSERT_EQ(usdc_order_link_id, resp->list(0).order_link_id());
  }

  {
    CancelAllOrderReqV5Builder cancel_all_build("linear", "", "BTC", "", "");
    auto resp = user.cancel_all(cancel_all_build.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(2, resp->list_size());
  }
}

TEST_F(cancel_all_by_coin_test, cancel_all_by_coin_open_v5_only_normal_stop) {
  biz::user_id_t uid = 4678832;
  stub user(te, uid);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  te->BatchAddMarkPrice(std::vector<tmock::MockPrice>{{static_cast<ESymbol>(5), 20000, 20000, 20000},
                                                      {static_cast<ESymbol>(45), 20000, 20000, 20000}});

  auto usdt_order_link_id = te->GenUUID();
  std::string usdt_order_id;
  {
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
    create_build.SetOrderLinkID(usdt_order_link_id);
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp->order_id(), "");
    ASSERT_STREQ(resp->order_link_id().c_str(), usdt_order_link_id.c_str());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(usdt_order_link_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(uid, 100000);
    orderCheck.CheckPrice(uid, 20000 * 1e4);
    usdt_order_id = resp->order_id();
    orderCheck.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
  }

  std::string usdt_stop_order_id;
  std::string usdt_stop_order_link_id = "f46608e2-9c2b-47a7-b1c5-bbc2d2adcbfc";
  {
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.1", "10100");
    create_build.SetOrderLinkID(usdt_stop_order_link_id);
    create_build.SetTrigger("MarkPrice", 2, "10050");
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp->order_id(), "");
    ASSERT_STREQ(resp->order_link_id().c_str(), usdt_stop_order_link_id.c_str());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(usdt_stop_order_link_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    order_check.Check(uid, 10000000);
    order_check.CheckPrice(uid, 10100 * 1e4);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
    usdt_stop_order_id = resp->order_id();
  }

  auto usdc_order_link_id = te->GenUUID();
  std::string usdc_order_id;
  // auto symbol = 45;
  {
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCPERP", 0, "Sell", "Limit", "0.001", "20000");
    create_build.SetOrderLinkID(usdc_order_link_id);
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp->order_id(), "");
    ASSERT_STREQ(resp->order_link_id().c_str(), usdc_order_link_id.c_str());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(usdc_order_link_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(uid, 100000);
    orderCheck.CheckPrice(uid, 20000 * 1e4);
    usdc_order_id = resp->order_id();
    orderCheck.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
  }

  std::string usdc_stop_order_id;
  std::string usdc_stop_order_link_id = "b79600a8-d5e1-47ac-9e34-c7923f2f9057";
  {
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCPERP", 0, "Sell", "Limit", "0.1", "10100");
    create_build.SetOrderLinkID(usdc_stop_order_link_id);
    create_build.SetTrigger("MarkPrice", 2, "10050");
    auto resp = user.create_order(create_build.Build());
    ASSERT_NE(resp->order_id(), "");
    ASSERT_STREQ(resp->order_link_id().c_str(), usdc_stop_order_link_id.c_str());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(usdc_stop_order_link_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    order_check.Check(uid, 10000000);
    order_check.CheckPrice(uid, 10100 * 1e4);
    order_check.CheckExecInst(uid, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
    usdc_stop_order_id = resp->order_id();
  }

  {
    CancelAllOrderReqV5Builder cancel_all_build("linear", "", "", "USDT", "StopOrder", "Stop");
    auto resp = user.cancel_all(cancel_all_build.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(1, resp->list_size());
    ASSERT_EQ(usdt_stop_order_id, resp->list(0).order_id());
    ASSERT_EQ(usdt_stop_order_link_id, resp->list(0).order_link_id());
  }

  {
    CancelAllOrderReqV5Builder cancel_all_build("linear", "", "", "USDC", "StopOrder", "Stop");
    auto resp = user.cancel_all(cancel_all_build.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(1, resp->list_size());
    ASSERT_EQ(usdc_stop_order_id, resp->list(0).order_id());
    ASSERT_EQ(usdc_stop_order_link_id, resp->list(0).order_link_id());
  }

  {
    CancelAllOrderReqV5Builder cancel_all_build("linear", "", "BTC", "", "");
    auto resp = user.cancel_all(cancel_all_build.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(2, resp->list_size());
  }
}

TEST_F(cancel_all_by_coin_test, unexpected_req) {
  biz::user_id_t user_id = 10000;
  stub user(te, user_id);
  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "100000000";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, user_id, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  // case1:cancel_all_types is null
  biz::coin_t coin = ECoin::USDT;
  ECancelType cancel_type = ECancelType::CancelByUser;
  std::vector<ECancelAllType> cancel_all_types{};
  FutureCancelAllByCoinBuilder cancel_all_build1(coin, user_id, cancel_type, cancel_all_types, "remark");
  auto resp = user.process(cancel_all_build1.Build());
  ASSERT_NE(resp, nullptr);
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeParamsError);

  // case2:unknown coin
  FutureCancelAllByCoinBuilder cancel_all_build2(ECoin::UNKNOWN, user_id, cancel_type, cancel_all_types, "remark");
  resp = user.process(cancel_all_build2.Build());
  ASSERT_NE(resp, nullptr);
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeParamsError);

  // 活动单
  FutureOneOfCreateOrderBuilder create_ao_build(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                                EOrderType::Limit, 100 * 1e4, 10000 * 1e4, ETimeInForce::GoodTillCancel,
                                                user_id, coin);
  resp = user.process(create_ao_build.Build());
  ASSERT_NE(resp, nullptr);
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  // 条件单
  FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
      user_id, coin, ESymbol::BTCUSDT, te->GenUUID(), EPositionIndex::Single, ESide::Buy, EOrderType::Limit, 200 * 1e4,
      10000 * 1e4, ETimeInForce::ImmediateOrCancel, EStopOrderType::Stop, ETriggerBy::LastPrice, 9500 * 1e4, {},
      10000 * 1e4);
  resp = user.process(create_so_build.Build());
  ASSERT_NE(resp, nullptr);
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);

  // cancel all stop order
  cancel_all_types.push_back(ECancelAllType::StopOrder);
  cancel_all_types.push_back(ECancelAllType::NormalStopOrder);
  cancel_all_types.push_back(ECancelAllType::UNKNOWN);
  FutureCancelAllByCoinBuilder cancel_all_build3(coin, user_id, cancel_type, cancel_all_types, "remark");
  resp = user.process(cancel_all_build3.Build());
  ASSERT_NE(resp, nullptr);
  EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  std::string msg{};
  // std::cout << "cancel all stop order result:" << utils::PbMessageToJsonString(*result.m_msg, &msg) <<
  // std::endl;
  EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::Deactivated);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cross_status(), ECrossStatus::Deactivated);
}

TEST_F(cancel_all_by_coin_test, cancel_both_side_tpsl) {
  biz::user_id_t uid1 = 100000;
  stub user(te, uid1);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "1000";
    std::string bonus_change = "0";
    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp = user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  {
    FutureOneOfSwitchPositionModeBuilder switch_position_mode(ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::BothSide,
                                                              uid1);
    auto resp = user.process(switch_position_mode.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    result.RefAssetMarginResult().RefRelatedWallet(5).CheckCoin(5);
  }

  {
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
        uid1, ECoin::USDT, ESymbol::BTCUSDT, te->GenUUID(), EPositionIndex::Buy, ESide::Buy, EOrderType::Limit,
        200 * 1e4, 10000 * 1e4, ETimeInForce::ImmediateOrCancel, EStopOrderType::Stop, ETriggerBy::LastPrice,
        9500 * 1e4, {}, 10000 * 1e4);
    auto resp = user.process(create_so_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  }

  {
    FutureOneOfCreateOrderWithTriggerBuilder create_so_build(
        uid1, ECoin::USDT, ESymbol::BTCUSDT, te->GenUUID(), EPositionIndex::Sell, ESide::Sell, EOrderType::Limit,
        200 * 1e4, 10000 * 1e4, ETimeInForce::ImmediateOrCancel, EStopOrderType::Stop, ETriggerBy::LastPrice,
        11000 * 1e4, {}, 9200 * 1e4);
    auto resp = user.process(create_so_build.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 1);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  }

  {
    std::vector<ECancelAllType> cancel_all_types{};
    cancel_all_types.push_back(ECancelAllType::TpSlStopOrder);
    cancel_all_types.push_back(ECancelAllType::StopOrder);
    FutureCancelAllByCoinBuilder cancel_all_build3(ECoin::USDT, uid1, ECancelType::CancelByFGridBotOrderCost,
                                                   cancel_all_types, "remark");
    auto resp = user.process(cancel_all_build3.Build());
    ASSERT_NE(resp, nullptr);
    EXPECT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string msg{};
    // std::cout << "cancel all stop order result:" << utils::PbMessageToJsonString(*result.m_msg, &msg) <<
    // std::endl;
    EXPECT_EQ(result.m_msg->futures_margin_result().affected_positions().size(), 2);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 2);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::Deactivated);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cross_status(), ECrossStatus::Deactivated);
    EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cancel_type(),
              ECancelType::CancelByFGridBotOrderCost);
  }
}

TEST_F(cancel_all_by_coin_test, set_mm_rate_close_req) {
  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, DepositAndSwitchToCrossMode(uid_1, "2000"));
  EXPECT_EQ(0, DepositAndSwitchToCrossMode(uid_2, "2000"));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  // 用户1挂买单
  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                       ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, 20000000);

  // 用户2挂卖单
  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_2, 20000000);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(20000000, pz_checker.m_msg->size_x());

  // 设置Mm Rate Close
  int64_t mm_rate = 5000;
  FutureOneOfSetMmRateCloseBuilder create_mm_rate_close(symbol, pz_index, mm_rate, uid_1, coin);
  resp = user1.process(create_mm_rate_close.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(5000, order_checker.m_msg->trigger_mm_rate_e2());
  EXPECT_EQ(EOrderStatus::Untriggered, order_checker.m_msg->order_status());
  EXPECT_EQ(ECrossStatus::Init, order_checker.m_msg->cross_status());

  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(5000, pz_checker.m_msg->trigger_mm_rate_e2());

  // 修改Mm Rate Close
  mm_rate = 4000;
  FutureOneOfSetMmRateCloseBuilder replace_mm_rate_close(symbol, pz_index, mm_rate, uid_1, coin);
  resp = user1.process(replace_mm_rate_close.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(4000, order_checker.m_msg->trigger_mm_rate_e2());
  EXPECT_EQ(EOrderStatus::Untriggered, order_checker.m_msg->order_status());
  EXPECT_EQ(ECrossStatus::Replaced, order_checker.m_msg->cross_status());

  EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(4000, pz_checker.m_msg->trigger_mm_rate_e2());

  std::vector<ECancelAllType> cancel_all_types{ECancelAllType::MmRateCloseStopOrder};
  FutureCancelAllByCoinBuilder cancel_all_build3(ECoin::USDT, uid_1, ECancelType::CancelByFGridBotOrderCost,
                                                 cancel_all_types, "remark");

  // FutureCancelAllBuilder(biz::symbol_t symbol, biz::coin_t coin, biz::user_id_t uid, ECancelType cancel_type,
  //                        ECancelAllType cancel_all_type, std::string remark)

  resp = user1.process(cancel_all_build3.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(1, result.m_msg->futures_margin_result().related_orders_size());
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
  ASSERT_NE(order_checker.m_msg, nullptr);
  EXPECT_EQ(4000, order_checker.m_msg->trigger_mm_rate_e2());
  EXPECT_EQ(EOrderStatus::Deactivated, order_checker.m_msg->order_status());
  EXPECT_EQ(ECrossStatus::Deactivated, order_checker.m_msg->cross_status());
  EXPECT_EQ(ECancelType::CancelByFGridBotOrderCost, order_checker.m_msg->cancel_type());

  // EXPECT_EQ(1, result.m_msg->futures_margin_result().affected_positions_size());
  // pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  // ASSERT_NE(pz_checker.m_msg, nullptr);
  // EXPECT_EQ(0, pz_checker.m_msg->trigger_mm_rate_e2());
}

TEST_F(cancel_all_by_coin_test, cancel_one_side_tpsl) {
  biz::user_id_t uid1 = 100000;
  stub user(te, uid1);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "1000";
    std::string bonus_change = "0";
    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp = user.process(deposit_build.Build());
    auto result = te->PopResult();
  }

  // {
  //   FutureOneOfSwitchPositionModeBuilder switch_position_mode(ESymbol::BTCUSDT, ECoin::USDT,
  //                                                             EPositionMode::BothSide, uid1);
  //   auto resp = user.process(switch_position_mode.Build());
  //   auto result = te->PopResult();
  //   ASSERT_NE(result.m_msg.get(), nullptr);
  //   result.RefAssetMarginResult().RefRelatedWallet(5).CheckCoin(5);
  // }

  biz::user_id_t uid_1 = 100000;
  stub user1(te, uid_1);

  biz::user_id_t uid_2 = 100002;
  stub user2(te, uid_2);

  EXPECT_EQ(0, DepositAndSwitchToCrossMode(uid_1, "2000"));
  EXPECT_EQ(0, DepositAndSwitchToCrossMode(uid_2, "2000"));

  biz::coin_t coin = 5;
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  biz::size_x_t qty = 0.2 * 1e8;
  biz::price_x_t price = 9000 * 1e4;

  te->AddMarkPrice(static_cast<ESymbol>(symbol), 9000, 9000, 9000);

  {
    FutureOneOfSwitchPositionModeBuilder switch_position_mode(ESymbol::BTCUSDT, ECoin::USDT, EPositionMode::BothSide,
                                                              uid_1);
    auto resp = user1.process(switch_position_mode.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    result.RefAssetMarginResult().RefRelatedWallet(5).CheckCoin(5);
  }

  // 用户1挂买单

  auto order_id = te->GenUUID();
  ESide side = ESide::Buy;
  FutureOneOfCreateOrderBuilder create_build_user1_buy(order_id, symbol, EPositionIndex::Buy, side, order_type, qty,
                                                       price, ETimeInForce::GoodTillCancel, uid_1, coin);
  auto resp = user1.process(create_build_user1_buy.Build());
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_1, 20000000);

  // 用户2挂卖单

  auto order_id_2 = te->GenUUID();
  side = ESide::Sell;
  FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id_2, symbol, pz_index, side, order_type, qty, price,
                                                        ETimeInForce::GoodTillCancel, uid_2, coin);
  resp = user2.process(create_build_user2_sell.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  order_checker = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id_2);
  ASSERT_NE(order_checker.m_msg, nullptr);
  order_checker.Check(uid_2, 20000000);
  auto pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  result = te->PopResult();
  pz_checker = result.RefFutureMarginResult().RefRelatedPosition(0);
  ASSERT_NE(pz_checker.m_msg, nullptr);
  EXPECT_EQ(20000000, pz_checker.m_msg->size_x());

  FutureOneOfSetTpSlTsBuilder set_tpsl{};
  set_tpsl.SetValue(uid1, coin, symbol, EPositionIndex::Buy, {}, 10000 * 1e4, {}, 8000 * 1e4, {}, {}, {}, {}, {}, {},
                    {}, {}, {}, {}, {}, {}, {}, {});
  resp = user1.process(set_tpsl.Build());
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  {
    std::vector<ECancelAllType> cancel_all_types{};
    cancel_all_types.push_back(ECancelAllType::TpSlStopOrder);
    cancel_all_types.push_back(ECancelAllType::StopOrder);

    // 按照合约类型取消所有订单失败
    // FutureCancelAllByCoinBuilder cancel_all_excpetion(ECoin::USDT, uid1, ECancelType::CancelByFGridBotOrderCost,
    //                                                   cancel_all_types, "remark", EContractType::InversePerpetual);
    // auto resp_cancel_all_excpetion = user1.process(cancel_all_excpetion.Build());
    // ASSERT_NE(resp_cancel_all_excpetion, nullptr);
    // EXPECT_EQ(resp_cancel_all_excpetion->ret_code(), ::enums::ebizcode::ParamsError);
    // FutureCancelAllByCoinBuilder cancel_all_excpetion2(ECoin::USDT, uid1, ECancelType::CancelByFGridBotOrderCost,
    //                                                    cancel_all_types, "remark", EContractType::InverseFutures);
    // resp_cancel_all_excpetion = user1.process(cancel_all_excpetion2.Build());
    // ASSERT_NE(resp_cancel_all_excpetion, nullptr);
    // EXPECT_EQ(resp_cancel_all_excpetion->ret_code(), ::enums::ebizcode::ParamsError);

    FutureCancelAllByCoinBuilder cancel_all_build3(ECoin::USDT, uid1, ECancelType::CancelByFGridBotOrderCost,
                                                   cancel_all_types, "remark");
    auto resp2 = user1.process(cancel_all_build3.Build());
    ASSERT_NE(resp2, nullptr);
    EXPECT_EQ(resp2->ret_code(), error::kErrorCodeSuccess);

    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    std::string msg{};
    // std::cout << "cancel all stop order result:" << utils::PbMessageToJsonString(*result.m_msg, &msg) <<
    // std::endl;
    EXPECT_EQ(result2.m_msg->futures_margin_result().affected_positions().size(), 2);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().size(), 2);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::Deactivated);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cross_status(), ECrossStatus::Deactivated);
    EXPECT_EQ(result2.m_msg->futures_margin_result().related_orders().at(0).cancel_type(),
              ECancelType::CancelByFGridBotOrderCost);
  }
}

TEST_F(cancel_all_by_coin_test, cancel_multi_symbol) {
  biz::user_id_t const uid1 = 100000;
  biz::user_id_t const uid2 = 11000;
  stub user1(te, uid1);
  stub user2(te, uid2);

  FutureDepositBuilder deposit_build("ut-deposit-uid1-1", uid1, "ut-deposit-uid1-1-trans-id", ECoin::USDT,
                                     EWalletRecordType::Deposit, "100000000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("ut-deposit-uid1-2", uid1, "ut-deposit-uid1-2-trans-id", ECoin::USDC,
                                      EWalletRecordType::Deposit, "100000000", "0");
  deposit_resp = user1.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->AddMarkPrice(ESymbol::BTCUSDT, 9000, 9000, 10000);

  FutureOneOfCreateOrderBuilder create_order1(te->GenUUID(), ESymbol::BTCUSDT, EPositionIndex::Single, ESide::Buy,
                                              EOrderType::Limit, 0.2 * 1e8, 9000 * 1e4, ETimeInForce::GoodTillCancel,
                                              uid1, ECoin::USDT);
  auto resp = user1.process(create_order1.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).symbol(), ESymbol::BTCUSDT);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::New);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cross_status(), ECrossStatus::NewAccepted);

  te->AddMarkPrice(ESymbol(45), 9000, 9000, 10000);

  FutureOneOfCreateOrderBuilder create_order2(te->GenUUID(), ESymbol(45), EPositionIndex::Single, ESide::Buy,
                                              EOrderType::Limit, 0.2 * 1e8, 9000 * 1e4, ETimeInForce::GoodTillCancel,
                                              uid1, ECoin::USDC);
  resp = user1.process(create_order2.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).symbol(), ESymbol(45));
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::New);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cross_status(), ECrossStatus::NewAccepted);

  te->AddMarkPrice(ESymbol(284), 9000, 9000, 10000);

  FutureOneOfCreateOrderBuilder create_order3(te->GenUUID(), ESymbol(284), EPositionIndex::Single, ESide::Buy,
                                              EOrderType::Limit, 0.2 * 1e8, 9000 * 1e4, ETimeInForce::GoodTillCancel,
                                              uid1, ECoin::USDC);
  resp = user1.process(create_order3.Build());
  ASSERT_NE(resp, nullptr);
  ASSERT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).symbol(), ESymbol(284));
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::New);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cross_status(), ECrossStatus::NewAccepted);

  // user1 有BTCUSDT/BTCPERP/BTC26MAY23订单
  std::vector<ECancelAllType> cancel_all_types{};
  cancel_all_types.push_back(ECancelAllType::OpenOrder);

  // FutureCancelAllByCoinBuilder cancel_all_build1(ECoin::USDT, uid1, ECancelType::CancelByUser, cancel_all_types,
  //                                                "remark", EContractType::InversePerpetual);
  // resp = user1.process(cancel_all_build1.Build());
  // ASSERT_NE(resp, nullptr);
  // EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::ParamsError);

  // FutureCancelAllByCoinBuilder cancel_all_build2(ECoin::USDT, uid1, ECancelType::CancelByUser, cancel_all_types,
  //                                                "remark", EContractType::InverseFutures);
  // resp = user1.process(cancel_all_build2.Build());
  // ASSERT_NE(resp, nullptr);
  // EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::ParamsError);

  FutureCancelAllByCoinBuilder cancel_all_build3(ECoin::USDT, uid1, ECancelType::CancelByUser, cancel_all_types,
                                                 "remark", EContractType::LinearFutures);
  resp = user1.process(cancel_all_build3.Build());
  ASSERT_NE(resp, nullptr);
  EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
  result = te->PopResult();
  ASSERT_EQ(result.m_msg.get(), nullptr);  // 没有USDT的交割订单
  // EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).symbol(), ESymbol(284));
  // EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(),
  // EOrderStatus::Cancelled);
  // EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cross_status(),
  // ECrossStatus::Canceled);

  FutureCancelAllByCoinBuilder cancel_all_build4(ECoin::USDC, uid1, ECancelType::CancelByUser, cancel_all_types,
                                                 "remark", EContractType::LinearFutures);
  resp = user1.process(cancel_all_build4.Build());
  ASSERT_NE(resp, nullptr);
  EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).symbol(), ESymbol(284));
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::Cancelled);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cross_status(), ECrossStatus::Canceled);

  FutureCancelAllByCoinBuilder cancel_all_build5(ECoin::USDT, uid1, ECancelType::CancelByUser, cancel_all_types,
                                                 "remark", EContractType::LinearPerpetual);
  resp = user1.process(cancel_all_build5.Build());
  ASSERT_NE(resp, nullptr);
  EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).symbol(), ESymbol::BTCUSDT);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::Cancelled);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cross_status(), ECrossStatus::Canceled);

  FutureCancelAllByCoinBuilder cancel_all_build6(ECoin::USDC, uid1, ECancelType::CancelByUser, cancel_all_types,
                                                 "remark", EContractType::LinearPerpetual);
  resp = user1.process(cancel_all_build6.Build());
  ASSERT_NE(resp, nullptr);
  EXPECT_EQ(resp->ret_code(), ::enums::ebizcode::NoError);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg->futures_margin_result().related_orders().size(), 1);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).symbol(), ESymbol(45));
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).order_status(), EOrderStatus::Cancelled);
  EXPECT_EQ(result.m_msg->futures_margin_result().related_orders().at(0).cross_status(), ECrossStatus::Canceled);
}

std::shared_ptr<tmock::CTradeAppMock> cancel_all_by_coin_test::te;
