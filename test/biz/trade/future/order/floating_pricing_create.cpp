#include "test/biz/trade/future/order/floating_pricing_create.hpp"

#include <string>
#include <unordered_map>

#include "common/worker/type.hpp"
#include "data/enum.hpp"
#include "data/error/error.hpp"
#include "data/type/biz_type.hpp"
#include "enums/eaccountmode/account_mode.pb.h"
#include "margin_request/event/event.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"
int32_t FloatingPricingCreateTest::SwitchMarginMode(stub& user, enums::eaccountmode::AccountMode account_mode) {
  // stub user(PmTest::te, uid);
  auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
  unified_v_2_request_dto->mutable_req_header()->set_user_id(user.m_uid);
  unified_v_2_request_dto->mutable_req_header()->set_action(enums::eaction::Action::SwitchMarginMode);
  unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(account_mode);
  auto event = std::make_shared<event::MarginHdtsRequestEvent>(user.m_uid, unified_v_2_request_dto, -1);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  auto result = te->PopResult();
  if (result.m_msg.get() == nullptr) {
    return -1;
  }
  return 0;
}
TEST_F(FloatingPricingCreateTest, create_order) {
  biz::user_id_t uid = 4606570;
  stub user1(te, uid);
  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "*********";
    std::string bonus_change = "123";

    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, bonus_change);

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();
  }

  auto order_id = te->GenUUID();
  biz::symbol_t symbol = 5;  // BTCUSDT
  EPositionIndex pz_index = EPositionIndex::Single;
  // ESide side = ESide::Buy;
  // ESide opp_side = ESide::Sell;
  // EOrderType order_type = EOrderType::Limit;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 10000, 10000, 10000);
  biz::size_x_t qty = 100000;
  biz::price_x_t price = 9999 * 1e4;
  biz::coin_t coin = 5;
  order_id = te->GenUUID();

  FutureOneOfCreateOrderBuilder create_build3(order_id, symbol, pz_index, ESide::Buy, EOrderType::Market, qty, price,
                                              ETimeInForce::ImmediateOrCancel, uid, coin);
  auto resp4 = user1.process(create_build3.Build());
  ASSERT_EQ(resp4->ret_code(), error::kErrorCodeSuccess);
  //  auto result4 = te->PopResult();
  //  ASSERT_NE(result4.m_msg.get(), nullptr);
  //  auto orderCheck4 = result4.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  //  ASSERT_NE(orderCheck4.m_msg, nullptr);
  //  orderCheck4.Check(100000, 100000);

  order_id = te->GenUUID();

  FutureOneOfCreateOrderBuilder create_build4(order_id, symbol, pz_index, ESide::Sell, EOrderType::Market, qty,
                                              10001 * 1e4, ETimeInForce::ImmediateOrCancel, uid, coin);
  auto resp5 = user1.process(create_build4.Build());
  ASSERT_EQ(resp5->ret_code(), error::kErrorCodeSuccess);
  //  auto result5 = te->PopResult();
  //  ASSERT_NE(result5.m_msg.get(), nullptr);
  //  auto orderCheck5 = result5.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
  //  ASSERT_NE(orderCheck5.m_msg, nullptr);
  //  orderCheck5.Check(100000, 100000);
}

/*
last price 25000
ab很高
ask1 29500
定价可以成功，但是在波动范围内不能吃到订单
*/
TEST_F(FloatingPricingCreateTest, pm_price_buy_upper_bound) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "1000000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 做一个行情ob sell
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("29500"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("29600"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 现在ask1 是 29500

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    qty = 0.2 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 26250 * 1e4);
  }
}

/*
last price 25000
ab很高
ask1 22500
定价可以成功，在波动上限
*/
TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_ask1_1) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "1000000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 做一个行情ob sell
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("22500"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("25500"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 现在ask1 是 29500

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    qty = 0.2 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 26250 * 1e4);
  }

  // 下slippage市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    qty = 0.2 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    create_build_user2_buy.msg.mutable_create_order()->set_max_slippage_type(ESlippageType::TickSize);
    create_build_user2_buy.msg.mutable_create_order()->set_max_slippage_e2(1000);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 22501 * 1e4);
  }
}

/*
last price 25000
ab很高
ask1 24900
定价可以成功，在ask1和波动上限之间
*/
TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_ask1_2) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "400";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 做一个行情ob sell
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 现在ask1 是 24900

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    qty = 0.1 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 26000 * 1e4);
  }
}

/*
last price 25000
ab 15 较少
ask1 24900
定价失败
*/
TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_ask1_no_enough_ab) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "15";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 做一个行情ob sell
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 现在ask1 是 24900

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    qty = 0.1 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeCannotAffordOrderCost);
  }
}

/*
last price 25000
ab 300 较少
ask1 24900
定价刚好成功
*/
TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_ask1_no_enough_ab2) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "300";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 做一个行情ob sell
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 现在ask1 是 24900

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    qty = 0.1 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 25000 * 1e4);
  }
}

/*
last price 25000
ab 300 较少
ask1 25100
定价失败
*/
TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_ask1_no_enough_ab3) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "300";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 做一个行情ob sell
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("25100"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("25100"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 现在ask1 是 24900

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    qty = 0.1 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeCannotAffordOrderCost);
  }
}

/*
last price 25000
ab 310.01 较少
ask1 25100
定价刚好成功
*/
TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_ask1_no_enough_ab4) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "310.01";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 做一个行情ob sell
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("25100"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("25100"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 现在ask1 是 24900

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    qty = 0.1 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 25100.1 * 1e4);
  }
}

TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_formula) {
  biz::BigDecimal ab(2);
  biz::size_x_t qty = 1000'000;
  biz::price_x_t floating_starting_point = 24000000;
  auto calculated_price_x =
      ab.Div(biz::BigDecimal(qty, -8)).Shift(4).Add(biz::BigDecimal(floating_starting_point)).IntPart();
  EXPECT_EQ(calculated_price_x, 26000000);
}

/*
last price 25000
ab足够
ask1 22400
定价可以成功，在ask1和波动上限之间
*/

TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_lower_bound) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "1000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("22400"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("22400"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 现在ask1 是 23000

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    qty = 0.2 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 26250 * 1e4);
  }
}

/*
last price 25000
ab足够
ask1 25600
市价平仓
定价可以成功，波动上限
*/
// 市价单，平仓
TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_close) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25500, 25500);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "1500";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("25600"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("25600"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25500");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个卖仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.199 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 26250 * 1e4);
  }
}

/*
last price 25000
ab 不足
ask1 24900
市价平仓
定价失败
*/
// 市价单，平仓
TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_close_no_ab) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "606.4";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), qty);
  }

  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25500");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个卖仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.1 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 26250 * 1e4);
  }
}

/*
last price 25000
ab 刚好
ask1 24900
市价平仓
定价成功
*/
// 市价单，平仓
TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_close_ab) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "657.5";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), qty);
  }

  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25500");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个卖仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.1 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 26250 * 1e4);
  }
}

/*
last price 25000
ab足够
ask1 25600
市价单，平仓，ro
定价可以成功，波动上限
*/
TEST_F(FloatingPricingCreateTest, pm_price_buy_ro_close) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25500, 25500);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "1500";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("25100"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("25200"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25500");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个卖仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.199 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    create_build_user2_buy.msg.mutable_create_order()->set_reduce_only(true);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 26250 * 1e4);
  }
}

/*
last price 25000
ab 刚好
ask1 24900
市价反开
定价成功
*/
// 市价单，平仓
TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_close_open_ab) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "657.5";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), qty);
  }

  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25500");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个卖仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.101 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 262500000);
  }
}

/*
last price 24000
ab足够
ask1 22500
市价单 反向开仓
定价可以成功，波动上限
*/
TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_close_then_open_1) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 24000, 25000, 25000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "1750";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("22500"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("24500"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("24500");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个卖仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.350 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 25200 * 1e4);
  }
}

/*
last price 25000
ab足够
ask1 25000
市价单 反向开仓
定价可以成功，波动上限
*/
TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_close_then_open_2) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 24500, 25000, 27000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "5000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("25000"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("25000"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个卖仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.6 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 25725 * 1e4);
  }
}

/*
last price 10000
ab足够
ask1 9100
市价单 反向开仓
定价可以成功，波动上限
*/
TEST_F(FloatingPricingCreateTest, pm_price_buy_normal_close_then_open_3) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 10000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 10000, 10000, 10000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "1200";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("9100"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("10000"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("10000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个卖仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.625 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 10500 * 1e4);
  }
}

/*
特殊业务场景: 市价单买，订单顺序有变化
*/
TEST_F(FloatingPricingCreateTest, pm_price_buy_market_first) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25500, 25500);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "5000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem sell = {bbase::decimal::Decimal("25001"), bbase::decimal::Decimal("0.1")};
    storeOb->sell_side.push_back(sell);
    storeOb->sell_side.push_back({bbase::decimal::Decimal("25000"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 限价单，价格27000
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 27000 * 1e4;
    qty = 0.1 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, EOrderType::Limit, qty,
                                                         price, ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 26250 * 1e4);
  }

  // 市价单，仅平仓，目前uid2有个卖仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.1 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 26250 * 1e4);
  }
}

/*
last price 23000
ab足够
bid1 22500
市价单
定价可以成功，波动下限
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_upper_bound) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "1000000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 23000, 23000, 23000);

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("22500"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("22600"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("23000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 现在ask1 是 29500

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    qty = 0.2 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 21850 * 1e4);
  }
}

/*
last price 25000
ab足够
bid1 27200
市价单
定价可以成功，波动下限
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_normal_bid1) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "1100";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("27200"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("27200"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }
  // 现在bid1 是 27200

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    qty = 0.2 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 23750 * 1e4);
  }
}
/*
last price 25000
ab足够
bid1 24995 浮亏
市价单
定价可以成功，波动下限
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_normal_bid1_2) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "1200";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("24995"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("24995"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    qty = 0.2 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 23750 * 1e4);
  }
}

/*
last price 25000
ab不足
bid1 25100
市价单
定价失败
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_no_ab) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "299.9";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("25100"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("25100"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 现在ask1 是 29500

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    qty = 0.1 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeCannotAffordOrderCost);
  }
}

/*
last price 25000
ab 刚好
bid1 25100
市价单
定价可以成功
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_just_ab_2) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "300";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("25100"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("25100"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 现在ask1 是 29500

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    qty = 0.1 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 25000 * 1e4);
  }
}

/*
last price 25000
ab 刚好不够loss
bid1 24900
市价单
定价失败
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_no_enough_ab_3) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "300";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    qty = 0.1 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeCannotAffordOrderCost);
  }
}

/*
last price 25000
ab 刚好loss
bid1 24900
市价单
定价可以成功，波动下限
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_just_ab_4) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "311";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    qty = 0.1 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 24890 * 1e4);
  }
}

/*
last price 22000
ab足够
bid1 25500
市价单
定价可以成功，波动下限
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_normal_lower_bound) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  {
    // 充值
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "1000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 20000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 22000, 22000, 22000);

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("25500"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("25500"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("22000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 现在bid1 是 25500

  // 下市价单
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    qty = 0.2 * 1e8;
    price = 0;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 20900 * 1e4);
  }
}

/*
last price 25500
mark price 25000
ab足够
bid1 22500
市价单 平仓
定价可以成功，波动下限
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_normal_close) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25500, 25500);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "1600";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 行情ob sell, ask1 = 22500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("22500"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("25500"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25500");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个buy仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 0;
    qty = 0.199 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 23750 * 1e4);
  }
}

/*
last price 25000
mark price 25000
ab不足
bid1 22000
市价单 平仓
定价失败
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_normal_close_no_ab) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "300";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), qty);
  }

  // 行情ob sell, ask1 = 22500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("22000"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("22000"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个buy仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 0;
    qty = 0.1 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeCannotAffordOrderCost);
  }
}

/*
last price 25000
mark price 25000
ab
bid1 24900
市价单 平仓
定价成功
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_normal_close_just_ab) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "300";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), qty);
  }

  // 行情ob sell, ask1 = 22500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("25000"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("25000"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个buy仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 0;
    qty = 0.1 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 24999.9 * 1e4);
  }
}

/*
last price 25500
mark price 25000
ab足够
bid1 24900
市价单 平仓，ro
定价可以成功，波动下限
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_ro_close) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25500, 25500);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "1600";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("24900"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("24800"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25500");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个buy仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 0;
    qty = 0.199 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    create_build_user2_sell.msg.mutable_create_order()->set_reduce_only(true);
    auto resp = user2.process(create_build_user2_sell.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 23750 * 1e4);
  }
}

// 市价单卖，市价单优先
/*
last price 25500
mark price 25000
ab足够
bid1 22500
市价单
定价可以成功，波动下限
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_market_first) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25500, 25500);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "10000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("22500"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("25500"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25500");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 限价单，价格27000
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 23000 * 1e4;
    qty = 0.199 * 1e8;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, EOrderType::Limit, qty,
                                                         price, ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 23750 * 1e4);
  }

  // 市价单，仅平仓，目前uid2有个buy仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 0;
    qty = 0.199 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 23750 * 1e4);
  }
}

/*
last price 25500
mark price 25000
ab足够
bid1 24900
市价单 先平后开
定价可以成功，波动下限
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_close_then_open_1) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25500, 25500);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "5000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("22000"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("22000"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25500");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个buy仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 0;
    qty = 0.25 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 23750 * 1e4);
  }
}

/*
last price 23000
mark price 23000
ab足够
bid1 25000
市价单 先平后开
定价可以成功，波动下限
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_close_then_open_2) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 23000, 23000, 23000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "8000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty,
                                               24150 * 1e4, ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 行情ob sell, ask1 = 25000
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("25000"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("25000"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("23000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个buy仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 0;
    qty = 0.3 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 21850 * 1e4);
  }
}

/*
last price 23000
mark price 23000
ab足够
bid1 28000
市价单 先平后开
定价可以成功，处于bid1和下限之间
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_close_then_open_3) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 23000, 23000, 23000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "6000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty,
                                               24150 * 1e4, ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 行情ob sell, ask1 = 24500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("28000"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("28000"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个buy仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 0;
    qty = 0.3 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 21850 * 1e4);
  }
}

/*
last price 25000
mark price 25000
ab
bid1 24900
市价单 反开
定价成功
*/
TEST_F(FloatingPricingCreateTest, pm_price_sell_normal_close_open_just_ab) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "300";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), qty);
  }

  // 行情ob sell, ask1 = 22500
  {
    std::shared_ptr<event::SyncQuoteDataEvent> event = std::make_shared<event::SyncQuoteDataEvent>(
        0, event::EventType::kEventSyncQuoteData, event::EventSourceType::kQuote,
        event::EventDispatchType::kBroadcastToWorker);
    event->future_order_book_map =
        std::make_shared<std::unordered_map<biz::symbol_t, std::shared_ptr<store::QuoteObSide>>>();
    event->future_last_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();

    auto storeOb = std::make_shared<store::QuoteObSide>();
    // store::QuoteObItem buy = {bbase::decimal::Decimal("10500"), bbase::decimal::Decimal("200")};
    // storeOb->buy_side.push_back(buy);

    store::QuoteObItem buy = {bbase::decimal::Decimal("25000"), bbase::decimal::Decimal("0.1")};
    storeOb->buy_side.push_back(buy);
    storeOb->buy_side.push_back({bbase::decimal::Decimal("25000"), bbase::decimal::Decimal("0.1")});
    (*event->future_order_book_map)[5] = storeOb;
    (*event->future_last_price_map)[5] = bbase::decimal::Decimal<>("25000");

    event->set_product_type(EProductType::Futures);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, true);
  }

  // 市价单，仅平仓，目前uid2有个buy仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 0;
    qty = 0.101 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 24999.9 * 1e4);
  }
}

// 卖平仓有浮亏
TEST_F(FloatingPricingCreateTest, price_sell_close_order_loss_1) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 23000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 23000, 23000, 23000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "6000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 市价单，仅平仓，目前uid2有个buy仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 22500 * 1e4;
    qty = 0.2 * 1e8;
    order_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 22500 * 1e4);
    auto buy_pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(buy_pz_check.m_msg->unrealised_order_loss_e8(),
              buy_pz_check.m_msg->position_mm_with_fee_e8() - *********00);
  }
}

// 卖平仓，一部分有浮亏，一部分没有
TEST_F(FloatingPricingCreateTest, price_sell_close_order_loss_2) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 23000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 23000, 23000, 23000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "6000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 部分平仓有浮亏
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 22500 * 1e4;
    qty = 0.1 * 1e8;
    order_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 22500 * 1e4);
    auto buy_pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(buy_pz_check.m_msg->unrealised_order_loss_e8(),
              buy_pz_check.m_msg->position_mm_with_fee_e8() / 2 - 5000000000);
  }

  // 部分平仓没有浮亏
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 23000 * 1e4;
    qty = 0.1 * 1e8;
    order_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 23000 * 1e4);
    auto buy_pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(buy_pz_check.m_msg->unrealised_order_loss_e8(),
              buy_pz_check.m_msg->position_mm_with_fee_e8() / 2 - 5000000000);
  }
}

// 卖单反向开仓，有浮亏
TEST_F(FloatingPricingCreateTest, price_sell_close_order_loss_3) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 23000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 23000, 23000, 23000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "6000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 部分平仓有浮亏
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 22500 * 1e4;
    qty = 0.3 * 1e8;
    order_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 22500 * 1e4);
    auto buy_pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(buy_pz_check.m_msg->unrealised_order_loss_e8(),
              buy_pz_check.m_msg->position_mm_with_fee_e8() - ***********);
  }
}

// 卖单纯开仓，有浮亏
TEST_F(FloatingPricingCreateTest, price_sell_close_order_loss_4) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Sell;
  auto opp_side = ESide::Buy;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 23000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 23000, 23000, 23000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "6000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  // 下单
  {
    auto order_id = te->GenUUID();
    side = ESide::Sell;
    price = 22500 * 1e4;
    qty = 0.3 * 1e8;
    order_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build_user2_sell(order_id, symbol, pz_index, side, order_type, qty, price,
                                                          ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_sell.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 22500 * 1e4);
    auto buy_pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(buy_pz_check.m_msg->unrealised_order_loss_e8(), -***********);
  }
}

// 买平仓有浮亏
TEST_F(FloatingPricingCreateTest, price_buy_close_order_loss_1) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "5000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 市价单，仅平仓，目前uid2有个卖仓位，下市价单买单出场
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 25500 * 1e4;
    qty = 0.2 * 1e8;
    order_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 25500 * 1e4);
    auto buy_pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(buy_pz_check.m_msg->unrealised_order_loss_e8(),
              buy_pz_check.m_msg->position_mm_with_fee_e8() - *********00);
  }
}

// 买平仓, 一部分有浮亏，一部分没有
TEST_F(FloatingPricingCreateTest, price_buy_close_order_loss_2) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "5000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 部分平仓有浮亏
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 25500 * 1e4;
    qty = 0.1 * 1e8;
    order_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 25500 * 1e4);
    auto buy_pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(buy_pz_check.m_msg->unrealised_order_loss_e8(),
              buy_pz_check.m_msg->position_mm_with_fee_e8() / 2 - 5000000000);
  }

  // 部分平仓无浮亏
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 25000 * 1e4;
    qty = 0.1 * 1e8;
    order_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 25000 * 1e4);
    auto buy_pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(buy_pz_check.m_msg->unrealised_order_loss_e8(),
              buy_pz_check.m_msg->position_mm_with_fee_e8() - 5000000000);
  }
}

// 买单反向开仓，有浮亏
TEST_F(FloatingPricingCreateTest, price_buy_close_order_loss_3) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "5000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }
  // 建仓位
  {
    auto order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, EOrderType::Limit, 2 * qty, price,
                                               ETimeInForce::GoodTillCancel, uid, coin);
    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
    orderCheck.Check(10000, 2 * qty);

    order_id = te->GenUUID();
    FutureOneOfCreateOrderBuilder create_build2(order_id, symbol, pz_index, opp_side, EOrderType::Limit, 2 * qty, price,
                                                ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp2 = user2.process(create_build2.Build());
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto orderCheck2 = result2.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck2.m_msg, nullptr);
    orderCheck2.Check(10002, 2 * qty);
    auto sell_pz_check = result2.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(sell_pz_check.m_msg, nullptr);

    EXPECT_EQ(sell_pz_check.m_msg->size_x(), 2 * qty);

    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto buy_pz_check = result3.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(buy_pz_check.m_msg, nullptr);
    EXPECT_EQ(buy_pz_check.m_msg->size_x(), 2 * qty);
  }

  // 部分平仓有浮亏
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 25500 * 1e4;
    qty = 0.3 * 1e8;
    order_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 25500 * 1e4);
    auto buy_pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(buy_pz_check.m_msg->unrealised_order_loss_e8(),
              buy_pz_check.m_msg->position_mm_with_fee_e8() - ***********);
  }
}

// 买单纯开仓，有浮亏
TEST_F(FloatingPricingCreateTest, price_buy_close_order_loss_4) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 25000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 25000, 25000, 25000);

  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "5000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Portfolio));
  }

  // 部分平仓有浮亏
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 25500 * 1e4;
    qty = 0.3 * 1e8;
    order_type = EOrderType::Limit;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 25500 * 1e4);
    auto buy_pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    EXPECT_EQ(buy_pz_check.m_msg->unrealised_order_loss_e8(), -***********);
  }
}

TEST_F(FloatingPricingCreateTest, slippage_order_tick) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 10000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 10000, 10000, 10000);
  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 100000;
  sc->price_limit_pnt_y_e6_ = 200000;
  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "5000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  // 下买单，tick slippage
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.3 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    create_build_user2_buy.msg.mutable_create_order()->set_max_slippage_type(ESlippageType::TickSize);
    create_build_user2_buy.msg.mutable_create_order()->set_max_slippage_e2(1000);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), 10501 * 1e4);
    EXPECT_EQ(orderCheck.m_msg->max_slippage_e2(), 1000);
    EXPECT_EQ(orderCheck.m_msg->max_slippage_type(), ESlippageType::TickSize);
    // auto buy_pz_check = result.RefFutureMarginResult().RefRelatedPosition(0);
    // EXPECT_EQ(buy_pz_check.m_msg->unrealised_order_loss_e8(), -***********);
  }

  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.3 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfPreCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid2, coin);
    create_build_user2_buy.msg.mutable_pre_create_order()->set_max_slippage_type(ESlippageType::TickSize);
    create_build_user2_buy.msg.mutable_pre_create_order()->set_max_slippage_e2(1000);
    auto resp = user2.process(create_build_user2_buy.Build());
    // ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto pr = resp->pre_create_order_result();
    EXPECT_EQ(pr.max_slippage_e2(), 1000);
    EXPECT_EQ(pr.max_slippage_type(), ESlippageType::TickSize);
  }

  // tick slippage超出范围
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.3 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    create_build_user2_buy.msg.mutable_create_order()->set_max_slippage_type(ESlippageType::TickSize);
    create_build_user2_buy.msg.mutable_create_order()->set_max_slippage_e2(400);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_NE(resp->ret_code(), error::kErrorCodeSuccess);
  }

  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.3 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    create_build_user2_buy.msg.mutable_create_order()->set_max_slippage_type(ESlippageType::TickSize);
    create_build_user2_buy.msg.mutable_create_order()->set_max_slippage_e2(200100);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_NE(resp->ret_code(), error::kErrorCodeSuccess);
  }

  {
    OpenApiV5CreateOrderBuilder api_order_2("linear", "BTCUSDT", 0, "Buy", "Market", "0.3", "0");
    api_order_2.msg.set_slippage_tolerance_type("TickSize");
    api_order_2.msg.set_slippage_tolerance("10");
    RpcContext ct{};
    auto resp = user2.create_order(api_order_2.Build(), ct);
    ASSERT_EQ(ct.RetCode(), error::kErrorCodeSuccess);
  }
}

TEST_F(FloatingPricingCreateTest, slippage_order_percent) {
  biz::user_id_t uid = 10000, uid2 = 10002;
  stub user1(te, uid);
  stub user2(te, uid2);

  // 下单 params
  biz::symbol_t symbol = 5;  // BTCPERP
  EPositionIndex pz_index = EPositionIndex::Single;
  auto side = ESide::Buy;
  auto opp_side = ESide::Sell;
  (void)opp_side;
  EOrderType order_type = EOrderType::Market;
  biz::size_x_t qty = 0.1 * 1e8;
  biz::price_x_t price = 10000 * 1e4;
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 10000, 10000, 10000);
  auto sc = const_cast<biz::SymbolConfig*>(config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(symbol));
  sc->price_limit_pnt_e6_ = 100000;
  sc->price_limit_pnt_y_e6_ = 200000;
  // 充值
  {
    std::string req_id = "";
    std::string trans_id = "";
    int wallet_record_type = 1;
    std::string amount = "5000";
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, wallet_record_type, amount, "0");

    auto resp1 = user1.process(deposit_build.Build());
    auto result1 = te->PopResult();

    // 调整到全仓模式
    EXPECT_EQ(0, SwitchMarginMode(user1, enums::eaccountmode::Cross));

    FutureDepositBuilder deposit_build2(req_id, uid2, trans_id, coin, wallet_record_type, amount, "0");

    auto resp2 = user2.process(deposit_build2.Build());
    auto result2 = te->PopResult();
    EXPECT_EQ(0, SwitchMarginMode(user2, enums::eaccountmode::Cross));
  }

  // 下买单，percent slippage
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.3 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    create_build_user2_buy.msg.mutable_create_order()->set_max_slippage_type(ESlippageType::Percent);
    create_build_user2_buy.msg.mutable_create_order()->set_max_slippage_e2(10);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    EXPECT_EQ(orderCheck.m_msg->price_x(), *********);
    EXPECT_EQ(orderCheck.m_msg->max_slippage_e2(), 10);
    EXPECT_EQ(orderCheck.m_msg->max_slippage_type(), ESlippageType::Percent);
  }

  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.3 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfPreCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                            ETimeInForce::GoodTillCancel, uid2, coin);
    create_build_user2_buy.msg.mutable_pre_create_order()->set_max_slippage_type(ESlippageType::Percent);
    create_build_user2_buy.msg.mutable_pre_create_order()->set_max_slippage_e2(10);
    auto resp = user2.process(create_build_user2_buy.Build());
    // ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);
    auto pr = resp->pre_create_order_result();
    EXPECT_EQ(pr.max_slippage_e2(), 10);
    EXPECT_EQ(pr.max_slippage_type(), ESlippageType::Percent);
  }

  // 范围校验
  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.3 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    create_build_user2_buy.msg.mutable_create_order()->set_max_slippage_type(ESlippageType::Percent);
    create_build_user2_buy.msg.mutable_create_order()->set_max_slippage_e2(101);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_NE(resp->ret_code(), error::kErrorCodeSuccess);
  }

  {
    auto order_id = te->GenUUID();
    side = ESide::Buy;
    price = 0;
    qty = 0.3 * 1e8;
    order_type = EOrderType::Market;

    FutureOneOfCreateOrderBuilder create_build_user2_buy(order_id, symbol, pz_index, side, order_type, qty, price,
                                                         ETimeInForce::GoodTillCancel, uid2, coin);
    create_build_user2_buy.msg.mutable_create_order()->set_max_slippage_type(ESlippageType::Percent);
    create_build_user2_buy.msg.mutable_create_order()->set_max_slippage_e2(4);
    auto resp = user2.process(create_build_user2_buy.Build());
    ASSERT_NE(resp->ret_code(), error::kErrorCodeSuccess);
  }

  {
    OpenApiV5CreateOrderBuilder api_order_2("linear", "BTCUSDT", 0, "Buy", "Market", "0.3", "0");
    api_order_2.msg.set_slippage_tolerance_type("Percent");
    api_order_2.msg.set_slippage_tolerance("0.1");
    RpcContext ct{};
    auto resp = user2.create_order(api_order_2.Build(), ct);
    ASSERT_EQ(ct.RetCode(), error::kErrorCodeSuccess);
  }
}

std::shared_ptr<tmock::CTradeAppMock> FloatingPricingCreateTest::te;
