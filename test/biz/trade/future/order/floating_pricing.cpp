#include "test/biz/trade/future/order/floating_pricing.hpp"

#include <algorithm>
#include <bbase/common/decimal/decimal.hpp>
#include <unordered_map>

#include "biz_worker/service/trade/futures/modules/orderbiz/priceprotectbiz/price_protect_biz.hpp"
#include "enums/ebizcode/biz_code.pb.h"
#include "src/biz_worker/engine/pre_worker/pre_engine.hpp"
#include "src/biz_worker/service/base/draft_pkg.hpp"
#include "src/biz_worker/service/trade/futures/modules/orderbiz/floatingorderpricingbiz/floating_order_pricing_biz.hpp"
#include "src/biz_worker/service/trade/store/per_symbol_store.hpp"
#include "src/biz_worker/service/trade/store/position/cow_position.hpp"
#include "src/biz_worker/service/trade/store/position/raw_position.hpp"
#include "src/biz_worker/utils/calc_util.hpp"
#include "src/data/type/biz_type.hpp"
#include "test/biz/trade/lib/stub.hpp"
/*
TEST_F(FloatingPricingTest, floating_order_pricing) {
  // std::cout << "value: " << bbase::decimal::Decimal<>(111000) << std::endl;
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  biz::symbol_t symbol = 5;  // BTCUSDT
  biz::coin_t coin = 5;
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 20000, 20000, 20000);

  biz::account_id_t account_id(102);

  ESide buy_side = ESide::Buy;

  store::UserDataManager *user_data_manager_ = new store::UserDataManager();
  store::PerUserStore *perUserStore = user_data_manager_->GetUserData(uid);
  // 初始化
  if (perUserStore == nullptr) {
    user_data_manager_->CreateUserData(uid);
    perUserStore = user_data_manager_->GetUserData(uid);
  }

  auto header = std::make_shared<store::Header>();
  header_->op_from = "op_from";
  header_->user_ip = "127.0.0.1";
  header_->uid = uid;
  header_->coin = coin;
  header_->account_id = account_id;
  header_->symbol = symbol;

  event::GrpcContext grpc_context{};
  std::shared_ptr<event::BizEvent> bizEvent = std::make_shared<event::BizEvent>(
      event::BizEvent(uid, event::EventType::kEventGrpcInput, event::EventSourceType::kCross,
                      event::EventDispatchType::kUnicastToUser, event::EventBizType::kCreateOrder));

  auto grpc_biz_event_new = std::dynamic_pointer_cast<event::GrpcBizEvent>(bizEvent);

  auto grpc_biz_event = std::make_shared<event::GrpcBizEvent>(
      uid, event::EventType::kEventGrpcInput, event::EventSourceType::kCross, event::EventDispatchType::kUnicastToUser,
      event::EventBizType::kCreateOrder, event::GrpcSoureType::kGrpcSourceFuture,
      event::GrpcUriType::kGrpcFutureProcess, grpc_context);
  auto new_config_mgr = std::make_shared<config::ConfigManager>(*config::ConfigProxy::Instance().config_mgr());
  auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
  auto symbol_config = std::make_shared<biz::SymbolConfig>();

  symbol_config->value_scale_ = 8;
  symbol_config->price_scale_ = 8;
  symbol_config->symbol_ = ESymbol::BTCUSDT;
  symbol_config->value_scale_ = 8;
  symbol_config->tick_size_x_ = 5;
  symbol_config->price_limit_pnt_e6_ = 10;
  symbol_config_mgr->AddSymbolConfig(symbol_config);
  new_config_mgr->set_symbol_config_mgr(symbol_config_mgr);

  store::PerWorkerStore *per_worker_store = new store::PerWorkerStore();
  config::ConfigProxy::Instance().set_config_mgr(new_config_mgr);

  std::shared_ptr<std::unordered_map<biz::symbol_t, biz::price_d_t>> option_ask_price_map =
      std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
  option_ask_price_map->emplace(5, biz::price_d_t("20000.23"));

  per_worker_store->SyncOptionAskPrice(option_ask_price_map);

  std::shared_ptr<std::unordered_map<biz::symbol_t, biz::price_d_t>> option_bid_price_map =
      std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
  option_bid_price_map->emplace(5, biz::price_d_t("20000.23"));

  per_worker_store->SyncOptionBidPrice(option_bid_price_map);
  std::shared_ptr<std::unordered_map<biz::symbol_t, biz::price_d_t>> future_mark_price_map =
      std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
  future_mark_price_map->emplace(5, biz::price_d_t("20000.23"));

  per_worker_store->SyncFutureMarkPrice(future_mark_price_map);

  std::shared_ptr<std::unordered_map<biz::symbol_t, biz::price_d_t>> future_last_price_map =
      std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
  future_last_price_map->emplace(5, biz::price_d_t("20000.23"));

  per_worker_store->SyncFutureLastPrice(future_last_price_map);

  // 初始化draft_pkg
  biz::DraftPkg *draft_pkg = new biz::DraftPkg(grpc_biz_event, header_, per_worker_store, perUserStore);
  draft_pkg->ref_user_setting_ = perUserStore->user_setting_.get();
  draft_pkg->GetOrAttachUnifyWallet();
  store::PerCoinStore::Ptr per_coin_store = draft_pkg->u_store->working_coins_[coin];
  if (per_coin_store == nullptr) {
    per_coin_store = std::make_shared<store::PerCoinStore>(draft_pkg->uid(), coin);
    draft_pkg->u_store->working_coins_[coin] = per_coin_store;
  }

  store::PerSymbolStore::Ptr symbol_store = per_coin_store->working_future_symbols_[symbol];

  if (symbol_store == nullptr) {
    symbol_store = std::make_shared<store::PerSymbolStore>(symbol, EPositionMode::MergedSingle);
    per_coin_store->working_future_symbols_[symbol] = symbol_store;
  }

  // init position;
  EPositionIndex positionIndex(EPositionIndex::Single);
  auto const& cowPosition = symbol_store->all_positions_[positionIndex];
  if (cowPosition == nullptr) {
    auto idx_pz = std::make_shared<store::Position>();
    auto& position = *idx_pz;
    position.symbol = symbol;
    position.user_id = uid;
    position.coin = coin;
    position.value_scale = 8;
    position.size = 2000000;
    position.data_ver = 1;
    position.side = buy_side;
    position.qty_scale = 8;
    position.price_scale = 8;
    symbol_store->all_positions_[positionIndex] =
        std::make_shared<store::CowPosition>(idx_pz, EProductType::Futures);
  }

  //  store::CowPosition::Ptr ref_pz_p;
  //  ref_pz_p->user_order_mgr.order_book(ESide::Buy)->cached_mark_price_ob_key().price = 19999;
  //  ref_pz_p->puser_order_mgr.order_book(ESide::Sell)->cached_mark_price_ob_key().price = 20001;

     auto order = store::Order();
     auto& transact = order;
     transact.order_id = "123456789012345678901234567890123456";
     transact.user_id = uid;
     transact.order_id.SetValue("123456789012345678901234567890123456");

     auto cow_order = store::CowOrder(order);

     auto o_pr = std::make_shared<store::CowOrder>(cow_order);
     //  cow_order.set
     o_pr->item.is_in_book = true;
     o_pr->item.cost_qty = 10;
     o_pr->item.cost_value = 0;
     o_pr->item.extra_value = 0;
     o_pr->item.extra_qty = 1;
     o_pr->item.key.symbol = 5;
     o_pr->item.key.side = ESide::Buy;

     //  cow_order->set_item();
     auto ask1_price_x = 1999900000000;
     auto bid1_price_x = 2000100000000;
     auto takerFeeRateE8 = 60000;
     auto convertedAvailableBalance = bbase::decimal::Decimal<>(3);
     auto convertedNotLiqAvailableBalance = bbase::decimal::Decimal<>(50000);

     biz::price_x_t calculated_price_x{};
     int32_t biz_error{};

     std::tie(calculated_price_x, biz_error) = biz::FloatingOrderPricingBiz::FloatingOrderPricing(
         draft_pkg, symbol_store->all_positions_[positionIndex], o_pr, ask1_price_x, bid1_price_x, takerFeeRateE8,
         convertedAvailableBalance, convertedNotLiqAvailableBalance);
     ASSERT_EQ(calculated_price_x, 2000023000000L);
     ASSERT_EQ(biz_error, 0);
     // std::cout << "calculated_price_x: " << calculated_price_x << std::endl;
     // std::cout << "biz_error: " << biz_error << std::endl;

     std::tie(calculated_price_x, biz_error) = biz::FloatingOrderPricingBiz::FloatingOrderPricing(
         draft_pkg, symbol_store->all_positions_[positionIndex], o_pr, 2000100000000000L, bid1_price_x, takerFeeRateE8,
         convertedAvailableBalance, convertedNotLiqAvailableBalance);
     ASSERT_EQ(calculated_price_x, 2000043000230L);
     ASSERT_EQ(biz_error, 30022);
     // std::cout << "calculated_price_x: " << calculated_price_x << std::endl;
     // std::cout << "biz_error: " << biz_error << std::endl;

     int32_t biz_code{};
     o_pr->item.key.side = ESide::Sell;
     std::tie(calculated_price_x, biz_error) = biz::FloatingOrderPricingBiz::FloatingOrderPricing(
         draft_pkg, symbol_store->all_positions_[positionIndex], o_pr, ask1_price_x, bid1_price_x, takerFeeRateE8,
         convertedAvailableBalance, convertedNotLiqAvailableBalance);
     ASSERT_EQ(calculated_price_x, 2000023000000L);
     ASSERT_EQ(biz_error, 0);
     // std::cout << "calculated_price_x: " << calculated_price_x << std::endl;
     // std::cout << "biz_error: " << biz_error << std::endl;

     std::tie(calculated_price_x, biz_error) = biz::FloatingOrderPricingBiz::FloatingOrderPricing(
         draft_pkg, symbol_store->all_positions_[positionIndex], o_pr, ask1_price_x, 20001L, takerFeeRateE8,
         convertedAvailableBalance, convertedNotLiqAvailableBalance);
     ASSERT_EQ(calculated_price_x, 2000002999770L);
     ASSERT_EQ(biz_error, 30023);
     // std::cout << "calculated_price_x: " << calculated_price_x << std::endl;
     // std::cout << "biz_error: " << biz_error << std::endl;

     o_pr->item.key.side = ESide::None;
     std::tie(calculated_price_x, biz_error) = biz::FloatingOrderPricingBiz::FloatingOrderPricing(
         draft_pkg, symbol_store->all_positions_[positionIndex], o_pr, ask1_price_x, bid1_price_x, takerFeeRateE8,
         convertedAvailableBalance, convertedNotLiqAvailableBalance);
     ASSERT_EQ(calculated_price_x, 0L);
     ASSERT_EQ(biz_error, 10001);
     std::cout << "calculated_price_x: " << calculated_price_x << std::endl;
     std::cout << "biz_error: " << biz_error << std::endl;

     o_pr->item.is_in_book = false;
     std::tie(calculated_price_x, biz_error) = biz::FloatingOrderPricingBiz::FloatingOrderPricing(
         draft_pkg, symbol_store->all_positions_[positionIndex], o_pr, ask1_price_x, bid1_price_x, takerFeeRateE8,
         convertedAvailableBalance, convertedNotLiqAvailableBalance);
     ASSERT_EQ(calculated_price_x, 0L);
     ASSERT_EQ(biz_error, 10001);
     std::cout << "calculated_price_x: " << calculated_price_x << std::endl;
     std::cout << "biz_error: " << biz_error << std::endl;

     o_pr->item.is_in_book = true;
     o_pr->item.cost_qty = 0;
     std::tie(calculated_price_x, biz_error) = biz::FloatingOrderPricingBiz::FloatingOrderPricing(
         draft_pkg, symbol_store->all_positions_[positionIndex], o_pr, ask1_price_x, bid1_price_x, takerFeeRateE8,
         convertedAvailableBalance, convertedNotLiqAvailableBalance);
     ASSERT_EQ(calculated_price_x, 0L);
     ASSERT_EQ(biz_error, 10001);
     std::cout << "calculated_price_x: " << calculated_price_x << std::endl;
     std::cout << "biz_error: " << biz_error << std::endl;

     o_pr->item.is_in_book = true;
     o_pr->item.cost_qty = 10;
     o_pr->item.cost_value = 10;
     o_pr->item.extra_value = 10;
     std::tie(calculated_price_x, biz_error) = biz::FloatingOrderPricingBiz::FloatingOrderPricing(
         draft_pkg, symbol_store->all_positions_[positionIndex], o_pr, ask1_price_x, bid1_price_x, takerFeeRateE8,
         convertedAvailableBalance, convertedNotLiqAvailableBalance);
     ASSERT_EQ(calculated_price_x, 0L);
     ASSERT_EQ(biz_error, 10001);
     std::cout << "calculated_price_x: " << calculated_price_x << std::endl;
     std::cout << "biz_error: " << biz_error << std::endl;

     std::int64_t extra_qty_x_p = 10000;
     std::int64_t v2c_e8_p = 2;
     std::int64_t mark_price_x = 200000000;
     std::int64_t free_cost_x = 0;
     std::int64_t closing_qty_x_p = 1;
     biz::size_x_t pz_size_x_p = 8;
     biz::value_x_t pz_mm_with_fee_value_e8_p = 8;
     biz::price_x_t least_price_x_p = 199990000;
     biz::price_x_t most_price_x_p = 199990000;
     biz::scale_t price_scale = 4;
     biz::scale_t qty_scale = 8;
     biz::scale_t value_scale = 8;
     std::int64_t tick_size_x_ = 4;
     std::int64_t taker_fee_rate_e8 = 60000;

     int32_t err_code = 0;
     // buy extra_qty_x_p < 0
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearBuyFormula(
         draft_pkg, -1, 10142500L, 200097400000L, bbase::decimal::Decimal<>(12552),
bbase::decimal::Decimal<>(12552), free_cost_x, 0L, 0L, 0L, 200073000000L, 210080000000L, 7, 8, value_scale,
1000000L, 75000L); ASSERT_EQ(calculated_price_x, 0L); ASSERT_EQ(err_code, 10001); std::cout << "calculated_price_x: " <<
calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     // buy leastPrice>mostPrice
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearBuyFormula(
         draft_pkg, 510400000L, 10142500L, 200097400000L, bbase::decimal::Decimal<>(12552),
bbase::decimal::Decimal<>(12552), free_cost_x, 0L, 0L, 0L, 220073000000L, 210080000000L, 7, 8, value_scale,
1000000L, 75000L); ASSERT_EQ(calculated_price_x, 0L); ASSERT_EQ(err_code, 10001); std::cout << "calculated_price_x: " <<
calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     // buy balance_factor_p_x.IsPositive()
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearBuyFormula(
         draft_pkg, 510400000L, 10142500L, 200097400000L, bbase::decimal::Decimal<>(-12552),
bbase::decimal::Decimal<>(12552), free_cost_x, 0L, 0L, 0L, 200073000000L, 210080000000L, 7, 8, value_scale,
1000000L, 75000L); ASSERT_EQ(calculated_price_x, 0L); ASSERT_EQ(err_code, 30031); std::cout << "calculated_price_x: " <<
calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     // buy a_price_x < 0
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearBuyFormula(
         draft_pkg, 510400000L, -10142500L, 200097400000L, bbase::decimal::Decimal<>(12552),
bbase::decimal::Decimal<>(12552), free_cost_x, 0L, 0L, 0L, 200073000000L, 210080000000L, 7, 8, value_scale,
1000000L, 75000L); ASSERT_EQ(calculated_price_x, 210080000000L); ASSERT_EQ(err_code, 0); std::cout <<
"calculated_price_x: " << calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     // buy a_price_x < least_price_x_p  ab缩小
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearBuyFormula(
         draft_pkg, 510400000L, 10142500L, 200097400000L, bbase::decimal::Decimal<>(2552),
bbase::decimal::Decimal<>(12552), free_cost_x, 0L, 0L, 0L, 200073000000L, 210080000000L, 7, 8, value_scale,
1000000L, 75000L); ASSERT_EQ(calculated_price_x, 49297510475L); ASSERT_EQ(err_code, 30031); std::cout <<
"calculated_price_x: " << calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     // buy a_price_x < mark_price_x
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearBuyFormula(
         draft_pkg, 510400000L, 10142500L, 250097400000L, bbase::decimal::Decimal<>(12552),
bbase::decimal::Decimal<>(12552), free_cost_x, 0L, 0L, 0L, 200073000000L, 210080000000L, 7, 8, value_scale,
1000000L, 75000L); ASSERT_EQ(calculated_price_x, 210080000000L); ASSERT_EQ(err_code, 0); std::cout <<
"calculated_price_x: " << calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     // buy 143 qtyFactor.IsPositive()
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearBuyFormula(
         draft_pkg, 0L, 10142500L, 200097400000L, bbase::decimal::Decimal<>(12552),
bbase::decimal::Decimal<>(12552), free_cost_x, 0L, 0L, 0L, 200073000000L, 210080000000L, 7, 8, value_scale,
1000000L, 75000L); ASSERT_EQ(calculated_price_x, 0L); ASSERT_EQ(err_code, 10001); std::cout << "calculated_price_x: " <<
calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     // buy 147 qtyFactor.IsPositive()  缩小closing_qty_x_p
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearBuyFormula(
         draft_pkg, 0L, 10142500L, 200097400000L, bbase::decimal::Decimal<>(12552),
bbase::decimal::Decimal<>(-30000), free_cost_x, 5104L, 510400000L, 0L, 200073000000L, 210080000000L, 7, 8,
value_scale, 1000000L, 75000L); ASSERT_EQ(calculated_price_x, 0L); ASSERT_EQ(err_code, 30031); std::cout <<
"calculated_price_x: " << calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     // buy 147 qtyFactor.IsPositive()  缩小closing_qty_x_p
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearBuyFormula(
         draft_pkg, 0L, 10142500L, 200097400000L, bbase::decimal::Decimal<>(12552),
bbase::decimal::Decimal<>(-30000), free_cost_x, 510400000L, 510400000L, 0L, 200073000000L, 210080000000L, 7, 8,
value_scale, 1000000L, 75000L); ASSERT_EQ(calculated_price_x, 0L); ASSERT_EQ(err_code, 30022); std::cout <<
"calculated_price_x: " << calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearBuyFormula(
         draft_pkg, extra_qty_x_p, v2c_e8_p, mark_price_x, convertedAvailableBalance, convertedNotLiqAvailableBalance,
         free_cost_x, closing_qty_x_p, pz_size_x_p, pz_mm_with_fee_value_e8_p, least_price_x_p, most_price_x_p,
         price_scale, qty_scale, value_scale, tick_size_x_, taker_fee_rate_e8);
     ASSERT_EQ(calculated_price_x, 199990000L);
     ASSERT_EQ(err_code, 0);
     std::cout << "calculated_price_x: " << calculated_price_x << std::endl;
     std::cout << "biz_code: " << err_code << std::endl;
     ASSERT_NE(err_code, 1);

     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearBuyFormula(
         draft_pkg, 510400000L, 10142500L, 200097400000L, bbase::decimal::Decimal<>(12552),
bbase::decimal::Decimal<>(12552), free_cost_x, 0L, 0L, 0L, 200073000000L, 210080000000L, 7, 8, value_scale,
1000000L, 75000L); ASSERT_EQ(calculated_price_x, 203999000000L); ASSERT_EQ(err_code, 0); std::cout <<
"calculated_price_x: " << calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearBuyFormula(
         draft_pkg, 10000000L, 10142500L, 158800000000L, bbase::decimal::Decimal<>("19.88665688"),
         bbase::decimal::Decimal<>("27.13425688"), free_cost_x, 2000000L, 2000000L, 34358550L, 158800000000L,
166635000000L, 8, 8, value_scale, 5000000L, 75000L); ASSERT_EQ(calculated_price_x, 161700000000L); ASSERT_EQ(err_code,
0); std::cout << "calculated_price_x: " << calculated_price_x << std::endl; std::cout << "biz_code: " << err_code <<
std::endl;

     // sell 183 extra_qty_x_p < 0
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearSellFormula(
         draft_pkg, -1L, 7632013L, 278203900L, bbase::decimal::Decimal<>(7932),
bbase::decimal::Decimal<>(9048), free_cost_x, 88100000L, 88100000L, 74870945543L, 278120000L, 264215000L, 4, 8,
value_scale, 5000L, 60000L); ASSERT_EQ(calculated_price_x, 0L); ASSERT_EQ(err_code, 10001); std::cout <<
"calculated_price_x: " << calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     // sell 188 closing_qty_x_p < 0
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearSellFormula(
         draft_pkg, 10L, 7632013L, 278203900L, bbase::decimal::Decimal<>(7932),
bbase::decimal::Decimal<>(9048), free_cost_x, 0L, 88100000L, 74870945543L, 278120000L, 264215000L, 4, 8,
value_scale, 5000L, 60000L); ASSERT_EQ(calculated_price_x, 264215000L); ASSERT_EQ(err_code, 0); std::cout <<
"calculated_price_x: " << calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     // sell least_price_x_p < most_price_x_p
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearSellFormula(
         draft_pkg, 0L, 7632013L, 278203900L, bbase::decimal::Decimal<>(7932),
bbase::decimal::Decimal<>(9048), free_cost_x, 88100000L, 88100000L, 74870945543L, 258120000L, 264215000L, 4, 8,
value_scale, 5000L, 60000L); ASSERT_EQ(calculated_price_x, 0L); ASSERT_EQ(err_code, 10001); std::cout <<
"calculated_price_x: " << calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     // sell least_price_x_p > mark_price_x
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearSellFormula(
         draft_pkg, 200L, -7632013L, 208203900L, bbase::decimal::Decimal<>(7932),
bbase::decimal::Decimal<>(9048), free_cost_x, 88100000L, 88100000L, 74870945543L, 258120000L, 254215000L, 4, 8,
value_scale, 5000L, 60000L); ASSERT_EQ(calculated_price_x, 254215000L); ASSERT_EQ(err_code, 0); std::cout <<
"calculated_price_x: " << calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     // sell 259 b1_price_x > least_price_x_p
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearSellFormula(
         draft_pkg, 1L, 0L, 200002300L, bbase::decimal::Decimal<>(227932),
bbase::decimal::Decimal<>(9048), free_cost_x, 9L, 2000000L, 0, 200002300L, 254215000L, 0, 0, value_scale, 5L,
60000L); ASSERT_EQ(calculated_price_x, 0L); ASSERT_EQ(err_code, 10001); std::cout << "calculated_price_x: " <<
calculated_price_x << std::endl; std::cout << "biz_code: " << err_code
   << std::endl;

     // sell qty_factor.IsZero() balance_factor_p_x.IsPositive()
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearSellFormula(
         draft_pkg, 1000000000L, 300000000L, 278203900L, bbase::decimal::Decimal<>(227932),
bbase::decimal::Decimal<>(9048), free_cost_x, 2000000000L, 88100000L, 74870945543L, 258120000L, 254215000L, 4, 8,
value_scale, 5000L, 60000L); ASSERT_EQ(calculated_price_x, 0L); ASSERT_EQ(err_code, 30031); std::cout <<
"calculated_price_x: " << calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     // sell 325 extra_qty_x_p = 0
     std::tie(calculated_price_x, err_code) = biz::FloatingOrderPricingBiz::LinearSellFormula(
         draft_pkg, 0L, 300000000L, 278203900L, bbase::decimal::Decimal<>(227932),
bbase::decimal::Decimal<>(9048), free_cost_x, 2000000000L, 88100000L, 74870945543L, 258120000L, 254215000L, 4, 8,
value_scale, 5000L, 60000L); ASSERT_EQ(calculated_price_x, 0L); ASSERT_EQ(err_code, 30023); std::cout <<
"calculated_price_x: " << calculated_price_x << std::endl; std::cout << "biz_code: " << err_code << std::endl;

     std::tie(calculated_price_x, biz_code) = biz::FloatingOrderPricingBiz::LinearSellFormula(
         draft_pkg, extra_qty_x_p, v2c_e8_p, mark_price_x, convertedAvailableBalance, convertedNotLiqAvailableBalance,
         free_cost_x, closing_qty_x_p, pz_size_x_p, pz_mm_with_fee_value_e8_p, least_price_x_p, most_price_x_p,
         price_scale, qty_scale, value_scale, tick_size_x_, taker_fee_rate_e8);
     ASSERT_EQ(calculated_price_x, 199990000L);
     ASSERT_EQ(err_code, 30023);
     std::cout << "calculated_price_x: " << calculated_price_x << std::endl;
     std::cout << "biz_code: " << err_code << std::endl;
     //  ASSERT_NE(err_code, 1);

     std::tie(calculated_price_x, biz_code) = biz::FloatingOrderPricingBiz::LinearSellFormula(
         draft_pkg, 10000000L, 10126000L, 306000000L, bbase::decimal::Decimal<>("359.75276290"),
         bbase::decimal::Decimal<>("338.84380720"), 34510990800L, 0, 0, 0, 306000000L, 290700000L, 4, 8, 8, 5000,
60000); ASSERT_EQ(calculated_price_x, 304540000L);

  auto calculated_price_x = 0;
  auto biz_code = 0;
  std::tie(calculated_price_x, biz_code) = biz::FloatingOrderPricingBiz::LinearSellFormula(
      draft_pkg, 1070000000L, 10157500, 2158333, 1000, 240, 110000, bbase::decimal::Decimal<>("10.019"),
      bbase::decimal::Decimal<>("26578200000000000"), 912825000L, 240000000L, 240000000L, 2640000000L,
265782000L, 105000, 104500, 4, 8, 8, 10, 75000); ASSERT_EQ(calculated_price_x, 104510); std::tie(calculated_price_x,
biz_code) = biz::FloatingOrderPricingBiz::LinearSellFormula( draft_pkg, 10000000L, 10126000L, 2158333, 2500, 240,
306000000L, bbase::decimal::Decimal<>("359.75276290"), bbase::decimal::Decimal<>("338.84380720"),
34510990800L, 0, 0, 0, 0, 306000000L, 290700000L, 4, 8, 8, 5000, 60000); ASSERT_EQ(calculated_price_x, 301185000);
  //   std::tie(calculated_price_x, biz_code) = biz::orderbiz::PriceProtectBiz::CalcSellBustPrice(
  //       draft_pkg, 109000, bbase::decimal::Decimal<>("130.262075"), 21000000000L, 21000000000L, 4778186490L,
4, 8, 10,
  //       75000, 5);

}

std::shared_ptr<tmock::CTradeAppMock> FloatingPricingTest::te;
*/
