//
// Created by Gdso.li on 3/4/2023.
//

#include "test/biz/trade/future/order/pct_qty_order_test.hpp"

#include <bbase/common/decimal/decimal.hpp>
#include <span>
#include <string>

#include "data/error/error.hpp"
#include "data/type/biz_type.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"

std::shared_ptr<tmock::CTradeAppMock> PctQtyOrderTest::te;

TEST_F(PctQtyOrderTest, create_pct_qty_buy_invalid_ob_qty) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000.00000000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "10500";
    auto lv0_qty = "0";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  // user1
  biz::user_id_t const uid = 658001;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  // 调 x10 杠杆
  biz::leverage_e2_t buy_lv = 10 * 1e2;
  biz::leverage_e2_t sell_lv = 10 * 1e2;

  FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, uid, buy_lv, sell_lv);
  // 发rpc请求
  auto leverage_resp = user1.process(set_lv_build.Build());
  // 校验rpc回报,默认x10可以不检查rpc返回值
  //  ASSERT_EQ(leverage_resp->ret_code(), 0);
  //  // 取trading result
  //  auto leverage_result = te->PopResult();
  //  ASSERT_NE(leverage_result.m_msg, nullptr);
  //  auto position_check = leverage_result.RefFutureMarginResult().RefRelatedPosition(0);
  //  ASSERT_NE(position_check.m_msg, nullptr);
  //  position_check.CheckLv(10 * 1e2, 6782667, 6790667, 616000, 624000);

  // -------------------------------------------------------------------------------------
  // 充值
  std::string money = "10000";
  auto req_id = te->GenUUID();
  auto trans_id = te->GenUUID();
  auto bonus_change = "0";  // 赠金
  EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                     bonus_change);
  // 发rpc请求
  auto deposit_resp = user1.process(deposit_build.Build());
  // 校验rpc回报
  ASSERT_EQ(deposit_resp->ret_code(), 0);
  // 取trading result
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg, nullptr);
  //  auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
  //  wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  EOrderType const order_type = EOrderType::Market;
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Sell, order_type, qty, price,
                                                time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());
  ASSERT_NE(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);
}

TEST_F(PctQtyOrderTest, create_pct_qty_sell_invalid_ob_qty) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000.00000000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "10500";
    auto lv0_qty = "0";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  // user1
  biz::user_id_t const uid = 658001;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  // 调 x10 杠杆
  biz::leverage_e2_t buy_lv = 10 * 1e2;
  biz::leverage_e2_t sell_lv = 10 * 1e2;

  FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, uid, buy_lv, sell_lv);
  // 发rpc请求
  auto leverage_resp = user1.process(set_lv_build.Build());
  // 校验rpc回报,默认x10可以不检查rpc返回值
  //  ASSERT_EQ(leverage_resp->ret_code(), 0);
  //  // 取trading result
  //  auto leverage_result = te->PopResult();
  //  ASSERT_NE(leverage_result.m_msg, nullptr);
  //  auto position_check = leverage_result.RefFutureMarginResult().RefRelatedPosition(0);
  //  ASSERT_NE(position_check.m_msg, nullptr);
  //  position_check.CheckLv(10 * 1e2, 6782667, 6790667, 616000, 624000);

  // -------------------------------------------------------------------------------------
  // 充值
  std::string money = "10000";
  auto req_id = te->GenUUID();
  auto trans_id = te->GenUUID();
  auto bonus_change = "0";  // 赠金
  EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                     bonus_change);
  // 发rpc请求
  auto deposit_resp = user1.process(deposit_build.Build());
  // 校验rpc回报
  ASSERT_EQ(deposit_resp->ret_code(), 0);
  // 取trading result
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg, nullptr);
  //  auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
  //  wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  EOrderType const order_type = EOrderType::Market;
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Sell, order_type, qty, price,
                                                time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());
  ASSERT_NE(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);
}

// 无浮亏
TEST_F(PctQtyOrderTest, create_pct_qty_buy_without_upnl) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000.00000000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "10500";
    auto lv0_qty = "100";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  // user1
  biz::user_id_t const uid = 658001;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  // 调 x10 杠杆
  biz::leverage_e2_t buy_lv = 10 * 1e2;
  biz::leverage_e2_t sell_lv = 10 * 1e2;

  FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, uid, buy_lv, sell_lv);
  // 发rpc请求
  auto leverage_resp = user1.process(set_lv_build.Build());
  // 校验rpc回报,默认x10可以不检查rpc返回值
  //  ASSERT_EQ(leverage_resp->ret_code(), 0);
  //  // 取trading result
  //  auto leverage_result = te->PopResult();
  //  ASSERT_NE(leverage_result.m_msg, nullptr);
  //  auto position_check = leverage_result.RefFutureMarginResult().RefRelatedPosition(0);
  //  ASSERT_NE(position_check.m_msg, nullptr);
  //  position_check.CheckLv(10 * 1e2, 6782667, 6790667, 616000, 624000);

  // -------------------------------------------------------------------------------------
  // 充值
  std::string money = "10000";
  auto req_id = te->GenUUID();
  auto trans_id = te->GenUUID();
  auto bonus_change = "0";  // 赠金
  EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                     bonus_change);
  // 发rpc请求
  auto deposit_resp = user1.process(deposit_build.Build());
  // 校验rpc回报
  ASSERT_EQ(deposit_resp->ret_code(), 0);
  // 取trading result
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg, nullptr);
  //  auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
  //  wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  EOrderType const order_type = EOrderType::Market;
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Buy, order_type, qty, price,
                                                time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());

  // 收user1 buy单result
  auto order_result = te->PopResult();
  ASSERT_NE(order_result.m_msg.get(), nullptr);
  auto order_check = order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(pct_order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  order_check.CheckQtyTypeAndValue(EQtyType::Percentage, pct_qty);
  order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
}

// 有浮亏
TEST_F(PctQtyOrderTest, create_pct_qty_buy_with_upnl) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000.00000000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "9999";
    auto lv0_qty = "100";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  // user1
  biz::user_id_t const uid = 658001;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  // 调 x10 杠杆
  biz::leverage_e2_t buy_lv = 10 * 1e2;
  biz::leverage_e2_t sell_lv = 10 * 1e2;

  FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, uid, buy_lv, sell_lv);
  // 发rpc请求
  auto leverage_resp = user1.process(set_lv_build.Build());
  // 校验rpc回报,默认x10可以不检查rpc返回值
  //  ASSERT_EQ(leverage_resp->ret_code(), 0);
  //  // 取trading result
  //  auto leverage_result = te->PopResult();
  //  ASSERT_NE(leverage_result.m_msg, nullptr);
  //  auto position_check = leverage_result.RefFutureMarginResult().RefRelatedPosition(0);
  //  ASSERT_NE(position_check.m_msg, nullptr);
  //  position_check.CheckLv(10 * 1e2, 6782667, 6790667, 616000, 624000);

  // -------------------------------------------------------------------------------------
  // 充值
  std::string money = "10000";
  auto req_id = te->GenUUID();
  auto trans_id = te->GenUUID();
  auto bonus_change = "0";  // 赠金
  EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
  FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                     bonus_change);
  // 发rpc请求
  auto deposit_resp = user1.process(deposit_build.Build());
  // 校验rpc回报
  ASSERT_EQ(deposit_resp->ret_code(), 0);
  // 取trading result
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg, nullptr);
  //  auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
  //  wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  EOrderType const order_type = EOrderType::Market;
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Buy, order_type, qty, price,
                                                time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());

  // 收user1 buy单result
  auto order_result = te->PopResult();
  ASSERT_NE(order_result.m_msg.get(), nullptr);
  auto order_check = order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(pct_order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  order_check.CheckQtyTypeAndValue(EQtyType::Percentage, pct_qty);
  order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
}

TEST_F(PctQtyOrderTest, create_pct_qty_buy_with_buy_pz) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "10000";
    auto lv0_qty = "100";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  // user1
  biz::user_id_t const uid = 55338001;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    //  auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
    //  wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));
  }

  // opposite user2
  biz::user_id_t const uid_2 = 55228002;
  stub user2(te, uid_2);

  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid_2, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user2.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
  }

  {
    // user1: create position 1 qty
    biz::size_x_t const pz_qty = 1 * 1e6;
    biz::price_x_t const pz_price = 10000 * 1e4;

    // case准备 -- 构建持仓 1qty
    // ============================================================================================================
    // user2 sell 1 qty
    auto sell_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_ord_build(sell_oid, symbol, pz_index, ESide::Sell, EOrderType::Limit, pz_qty,
                                                 pz_price, time_in_force, uid_2, coin);
    auto sell_resp = user2.process(sell_ord_build.Build());
    ASSERT_EQ(sell_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    auto sell_ord_check = sell_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(sell_oid);
    ASSERT_NE(sell_ord_check.m_msg, nullptr);
    sell_ord_check.CheckQty(uid_2, pz_qty);
    sell_ord_check.CheckPrice(uid_2, pz_price);
    sell_ord_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // user1 buy 1 qty
    auto buy_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_ord_build(buy_oid, symbol, pz_index, ESide::Buy, EOrderType::Limit, pz_qty,
                                                pz_price, time_in_force, uid, coin);
    auto buy_ord_resp = user1.process(buy_ord_build.Build());
    ASSERT_EQ(buy_ord_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto buy_ord_result = te->PopResult();
    ASSERT_NE(buy_ord_result.m_msg.get(), nullptr);
    auto buy_ord_check = buy_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(buy_oid);
    ASSERT_NE(buy_ord_check.m_msg, nullptr);
    buy_ord_check.CheckQty(uid, pz_qty);
    buy_ord_check.CheckPrice(uid, pz_price);
    buy_ord_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 检查user1 持仓
    auto buy_pz_check = buy_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    buy_pz_check.CheckUid(uid);
    buy_pz_check.CheckSize(pz_qty);
    buy_pz_check.CheckFreeSize(-pz_qty);

    // 收user2 sell单成交回报
    sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    // 检查user2 持仓
    auto sell_pz_check = sell_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    sell_pz_check.CheckUid(uid_2);
    sell_pz_check.CheckSize(pz_qty);
    sell_pz_check.CheckFreeSize(pz_qty);
  }

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Buy, EOrderType::Market, qty,
                                                price, time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());
  // 校验rpc回报
  ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);
  // 收user1 buy单result
  auto order_result = te->PopResult();
  ASSERT_NE(order_result.m_msg.get(), nullptr);
  auto order_check = order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(pct_order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  order_check.CheckQtyTypeAndValue(EQtyType::Percentage, pct_qty);
  order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
}

TEST_F(PctQtyOrderTest, create_pct_qty_buy_with_sell_pz) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "10000";
    auto lv0_qty = "100";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  // user1
  biz::user_id_t const uid = 5558001;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    //  auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
    //  wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));
  }

  // opposite user2
  biz::user_id_t const uid_2 = 5558002;
  stub user2(te, uid_2);

  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid_2, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user2.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
  }

  {
    // user1: create position 1 qty
    biz::size_x_t const pz_qty = 1 * 1e6;
    biz::price_x_t const pz_price = 10000 * 1e4;

    // case准备 -- 构建持仓 1qty
    // ============================================================================================================
    // user1 sell 1 qty
    auto sell_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_ord_build(sell_oid, symbol, pz_index, ESide::Sell, EOrderType::Limit, pz_qty,
                                                 pz_price, time_in_force, uid, coin);
    auto sell_resp = user1.process(sell_ord_build.Build());
    ASSERT_EQ(sell_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 sell单result
    auto sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    auto sell_ord_check = sell_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(sell_oid);
    ASSERT_NE(sell_ord_check.m_msg, nullptr);
    sell_ord_check.CheckQty(uid, pz_qty);
    sell_ord_check.CheckPrice(uid, pz_price);
    sell_ord_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // user2 buy 1 qty
    auto buy_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_ord_build(buy_oid, symbol, pz_index, ESide::Buy, EOrderType::Limit, pz_qty,
                                                pz_price, time_in_force, uid_2, coin);
    auto buy_ord_resp = user2.process(buy_ord_build.Build());
    ASSERT_EQ(buy_ord_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 buy单result
    auto buy_ord_result = te->PopResult();
    ASSERT_NE(buy_ord_result.m_msg.get(), nullptr);
    auto buy_ord_check = buy_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(buy_oid);
    ASSERT_NE(buy_ord_check.m_msg, nullptr);
    buy_ord_check.CheckQty(uid_2, pz_qty);
    buy_ord_check.CheckPrice(uid_2, pz_price);
    buy_ord_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 检查user2 持仓
    auto buy_pz_check = buy_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    buy_pz_check.CheckUid(uid_2);
    buy_pz_check.CheckSize(pz_qty);
    buy_pz_check.CheckFreeSize(-pz_qty);

    // 收user1 sell单成交回报
    sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    // 检查user1 持仓
    auto sell_pz_check = sell_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    sell_pz_check.CheckUid(uid);
    sell_pz_check.CheckSize(pz_qty);
    sell_pz_check.CheckFreeSize(pz_qty);
  }

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Buy, EOrderType::Market, qty,
                                                price, time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());
  // 校验rpc回报
  ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);
  // 收user1 buy单result
  auto order_result = te->PopResult();
  ASSERT_NE(order_result.m_msg.get(), nullptr);
  auto order_check = order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(pct_order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  order_check.CheckQtyTypeAndValue(EQtyType::Percentage, pct_qty);
  order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
}

TEST_F(PctQtyOrderTest, create_pct_qty_buy_with_sell_pz_small_lv0) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "10000";
    auto lv0_qty = "0.01";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  // user1
  biz::user_id_t const uid = 5558031;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    //  auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
    //  wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));
  }

  // opposite user2
  biz::user_id_t const uid_2 = 5558072;
  stub user2(te, uid_2);

  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid_2, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user2.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
  }

  {
    // user1: create position 1 qty
    biz::size_x_t const pz_qty = 1 * 1e6;
    biz::price_x_t const pz_price = 10000 * 1e4;

    // case准备 -- 构建持仓 1qty
    // ============================================================================================================
    // user1 sell 1 qty
    auto sell_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_ord_build(sell_oid, symbol, pz_index, ESide::Sell, EOrderType::Limit, pz_qty,
                                                 pz_price, time_in_force, uid, coin);
    auto sell_resp = user1.process(sell_ord_build.Build());
    ASSERT_EQ(sell_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 sell单result
    auto sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    auto sell_ord_check = sell_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(sell_oid);
    ASSERT_NE(sell_ord_check.m_msg, nullptr);
    sell_ord_check.CheckQty(uid, pz_qty);
    sell_ord_check.CheckPrice(uid, pz_price);
    sell_ord_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // user2 buy 1 qty
    auto buy_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_ord_build(buy_oid, symbol, pz_index, ESide::Buy, EOrderType::Limit, pz_qty,
                                                pz_price, time_in_force, uid_2, coin);
    auto buy_ord_resp = user2.process(buy_ord_build.Build());
    ASSERT_EQ(buy_ord_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 buy单result
    auto buy_ord_result = te->PopResult();
    ASSERT_NE(buy_ord_result.m_msg.get(), nullptr);
    auto buy_ord_check = buy_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(buy_oid);
    ASSERT_NE(buy_ord_check.m_msg, nullptr);
    buy_ord_check.CheckQty(uid_2, pz_qty);
    buy_ord_check.CheckPrice(uid_2, pz_price);
    buy_ord_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 检查user2 持仓
    auto buy_pz_check = buy_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    buy_pz_check.CheckUid(uid_2);
    buy_pz_check.CheckSize(pz_qty);
    buy_pz_check.CheckFreeSize(-pz_qty);

    // 收user1 sell单成交回报
    sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    // 检查user1 持仓
    auto sell_pz_check = sell_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    sell_pz_check.CheckUid(uid);
    sell_pz_check.CheckSize(pz_qty);
    sell_pz_check.CheckFreeSize(pz_qty);
  }

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Buy, EOrderType::Market, qty,
                                                price, time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());
  // 校验rpc回报
  ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);
  // 收user1 buy单result
  auto order_result = te->PopResult();
  ASSERT_NE(order_result.m_msg.get(), nullptr);
  auto order_check = order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(pct_order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  order_check.CheckQtyTypeAndValue(EQtyType::Percentage, pct_qty);
  order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
}

// 无浮亏
TEST_F(PctQtyOrderTest, create_pct_qty_sell_without_upnl) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000.00000000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "10500";
    auto lv0_qty = "100";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  // user1
  biz::user_id_t const uid = 858002;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  // 调 x10 杠杆
  {
    biz::leverage_e2_t buy_lv = 10 * 1e2;
    biz::leverage_e2_t sell_lv = 10 * 1e2;

    FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, uid, buy_lv, sell_lv);
    // 发rpc请求
    auto leverage_resp = user1.process(set_lv_build.Build());
    // 校验rpc回报,默认x10可以不检查rpc返回值
    //  ASSERT_EQ(leverage_resp->ret_code(), 0);
    //  // 取trading result
    //  auto leverage_result = te->PopResult();
    //  ASSERT_NE(leverage_result.m_msg, nullptr);
    //  auto position_check = leverage_result.RefFutureMarginResult().RefRelatedPosition(0);
    //  ASSERT_NE(position_check.m_msg, nullptr);
    //  position_check.CheckLv(10 * 1e2, 6782667, 6790667, 616000, 624000);
  }

  // -------------------------------------------------------------------------------------
  // 充值
  {
    std::string money = "10000";  // 1w
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), error::kErrorCodeSuccess);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    //    auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
    //    wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));
  }

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  EOrderType const order_type = EOrderType::Market;
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Sell, order_type, qty, price,
                                                time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());

  // 收user1 buy单result
  auto order_result = te->PopResult();
  ASSERT_NE(order_result.m_msg.get(), nullptr);
  auto order_check = order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(pct_order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  order_check.CheckQtyTypeAndValue(EQtyType::Percentage, pct_qty);
  order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
}

// 有浮亏
TEST_F(PctQtyOrderTest, create_pct_qty_sell_with_upnl) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000.00000000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "9999";
    auto lv0_qty = "100";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  // user1
  biz::user_id_t const uid = 858002;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  // 调 x10 杠杆
  {
    biz::leverage_e2_t buy_lv = 10 * 1e2;
    biz::leverage_e2_t sell_lv = 10 * 1e2;

    FutureOneOfSetLeverageBuilder set_lv_build(symbol, coin, uid, buy_lv, sell_lv);
    // 发rpc请求
    auto leverage_resp = user1.process(set_lv_build.Build());
    // 校验rpc回报,默认x10可以不检查rpc返回值
    //  ASSERT_EQ(leverage_resp->ret_code(), 0);
    //  // 取trading result
    //  auto leverage_result = te->PopResult();
    //  ASSERT_NE(leverage_result.m_msg, nullptr);
    //  auto position_check = leverage_result.RefFutureMarginResult().RefRelatedPosition(0);
    //  ASSERT_NE(position_check.m_msg, nullptr);
    //  position_check.CheckLv(10 * 1e2, 6782667, 6790667, 616000, 624000);
  }

  // -------------------------------------------------------------------------------------
  // 充值
  {
    std::string money = "10000";  // 1w
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    //    auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
    //    wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));
  }

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  EOrderType const order_type = EOrderType::Market;
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Sell, order_type, qty, price,
                                                time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());

  // 收user1 buy单result
  auto order_result = te->PopResult();
  ASSERT_NE(order_result.m_msg.get(), nullptr);
  auto order_check = order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(pct_order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  order_check.CheckQtyTypeAndValue(EQtyType::Percentage, pct_qty);
  order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
}

TEST_F(PctQtyOrderTest, create_pct_qty_sell_with_sell_pz) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "10000";
    auto lv0_qty = "100";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  // user1
  biz::user_id_t const uid = 5958001;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    //  auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
    //  wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));
  }

  // opposite user2
  biz::user_id_t const uid_2 = 5958002;
  stub user2(te, uid_2);

  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid_2, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user2.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
  }

  {
    // user1: create position 1 qty
    biz::size_x_t const pz_qty = 1 * 1e6;
    biz::price_x_t const pz_price = 10000 * 1e4;

    // case准备 -- 构建持仓 1qty
    // ============================================================================================================
    // user1 sell 1 qty
    auto sell_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_ord_build(sell_oid, symbol, pz_index, ESide::Sell, EOrderType::Limit, pz_qty,
                                                 pz_price, time_in_force, uid, coin);
    auto sell_resp = user1.process(sell_ord_build.Build());
    ASSERT_EQ(sell_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 sell单result
    auto sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    auto sell_ord_check = sell_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(sell_oid);
    ASSERT_NE(sell_ord_check.m_msg, nullptr);
    sell_ord_check.CheckQty(uid, pz_qty);
    sell_ord_check.CheckPrice(uid, pz_price);
    sell_ord_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // user2 buy 1 qty
    auto buy_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_ord_build(buy_oid, symbol, pz_index, ESide::Buy, EOrderType::Limit, pz_qty,
                                                pz_price, time_in_force, uid_2, coin);
    auto buy_ord_resp = user2.process(buy_ord_build.Build());
    ASSERT_EQ(buy_ord_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 buy单result
    auto buy_ord_result = te->PopResult();
    ASSERT_NE(buy_ord_result.m_msg.get(), nullptr);
    auto buy_ord_check = buy_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(buy_oid);
    ASSERT_NE(buy_ord_check.m_msg, nullptr);
    buy_ord_check.CheckQty(uid_2, pz_qty);
    buy_ord_check.CheckPrice(uid_2, pz_price);
    buy_ord_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 检查user2 持仓
    auto buy_pz_check = buy_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    buy_pz_check.CheckUid(uid_2);
    buy_pz_check.CheckSize(pz_qty);
    buy_pz_check.CheckFreeSize(-pz_qty);

    // 收user1 sell单成交回报
    sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    // 检查user1 持仓
    auto sell_pz_check = sell_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    sell_pz_check.CheckUid(uid);
    sell_pz_check.CheckSize(pz_qty);
    sell_pz_check.CheckFreeSize(pz_qty);
  }

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Sell, EOrderType::Market, qty,
                                                price, time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());
  // 校验rpc回报
  ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);
  // 收user1 buy单result
  auto order_result = te->PopResult();
  ASSERT_NE(order_result.m_msg.get(), nullptr);
  auto order_check = order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(pct_order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  order_check.CheckQtyTypeAndValue(EQtyType::Percentage, pct_qty);
  order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
}

TEST_F(PctQtyOrderTest, create_pct_qty_sell_with_buy_pz) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "10000";
    auto lv0_qty = "100";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  // user1
  biz::user_id_t const uid = 59998001;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    //  auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
    //  wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));
  }

  // opposite user2
  biz::user_id_t const uid_2 = 59522002;
  stub user2(te, uid_2);

  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid_2, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user2.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
  }

  {
    // user1: create position 0.01 qty
    biz::size_x_t const pz_qty = 1 * 1e6;
    biz::price_x_t const pz_price = 10000 * 1e4;

    // case准备 -- 构建持仓 1qty
    // ============================================================================================================
    // user2 sell 1 qty
    auto sell_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_ord_build(sell_oid, symbol, pz_index, ESide::Sell, EOrderType::Limit, pz_qty,
                                                 pz_price, time_in_force, uid_2, coin);
    auto sell_resp = user2.process(sell_ord_build.Build());
    ASSERT_EQ(sell_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    auto sell_ord_check = sell_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(sell_oid);
    ASSERT_NE(sell_ord_check.m_msg, nullptr);
    sell_ord_check.CheckQty(uid_2, pz_qty);
    sell_ord_check.CheckPrice(uid_2, pz_price);
    sell_ord_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // user1 buy 1 qty
    auto buy_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_ord_build(buy_oid, symbol, pz_index, ESide::Buy, EOrderType::Limit, pz_qty,
                                                pz_price, time_in_force, uid, coin);
    auto buy_ord_resp = user1.process(buy_ord_build.Build());
    ASSERT_EQ(buy_ord_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto buy_ord_result = te->PopResult();
    ASSERT_NE(buy_ord_result.m_msg.get(), nullptr);
    auto buy_ord_check = buy_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(buy_oid);
    ASSERT_NE(buy_ord_check.m_msg, nullptr);
    buy_ord_check.CheckQty(uid, pz_qty);
    buy_ord_check.CheckPrice(uid, pz_price);
    buy_ord_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 检查user1 持仓
    auto buy_pz_check = buy_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    buy_pz_check.CheckUid(uid);
    buy_pz_check.CheckSize(pz_qty);
    buy_pz_check.CheckFreeSize(-pz_qty);

    // 收user2 sell单成交回报
    sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    // 检查user2 持仓
    auto sell_pz_check = sell_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    sell_pz_check.CheckUid(uid_2);
    sell_pz_check.CheckSize(pz_qty);
    sell_pz_check.CheckFreeSize(pz_qty);
  }

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Sell, EOrderType::Market, qty,
                                                price, time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());
  // 校验rpc回报
  ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);
  // 收user1 buy单result
  auto order_result = te->PopResult();
  ASSERT_NE(order_result.m_msg.get(), nullptr);
  auto order_check = order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(pct_order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  order_check.CheckQtyTypeAndValue(EQtyType::Percentage, pct_qty);
  order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
}

TEST_F(PctQtyOrderTest, create_pct_qty_sell_with_buy_pz_small_lv0) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "10000";
    auto lv0_qty = "0.01";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  // user1
  biz::user_id_t const uid = 5958601;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    //  auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
    //  wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));
  }

  // opposite user2
  biz::user_id_t const uid_2 = 5958802;
  stub user2(te, uid_2);

  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid_2, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user2.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
  }

  {
    // user1: create position 1 qty
    biz::size_x_t const pz_qty = 1 * 1e6;
    biz::price_x_t const pz_price = 10000 * 1e4;

    // case准备 -- 构建持仓 1qty
    // ============================================================================================================
    // user2 sell 1 qty
    auto sell_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_ord_build(sell_oid, symbol, pz_index, ESide::Sell, EOrderType::Limit, pz_qty,
                                                 pz_price, time_in_force, uid_2, coin);
    auto sell_resp = user2.process(sell_ord_build.Build());
    ASSERT_EQ(sell_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    auto sell_ord_check = sell_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(sell_oid);
    ASSERT_NE(sell_ord_check.m_msg, nullptr);
    sell_ord_check.CheckQty(uid_2, pz_qty);
    sell_ord_check.CheckPrice(uid_2, pz_price);
    sell_ord_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // user1 buy 1 qty
    auto buy_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_ord_build(buy_oid, symbol, pz_index, ESide::Buy, EOrderType::Limit, pz_qty,
                                                pz_price, time_in_force, uid, coin);
    auto buy_ord_resp = user1.process(buy_ord_build.Build());
    ASSERT_EQ(buy_ord_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto buy_ord_result = te->PopResult();
    ASSERT_NE(buy_ord_result.m_msg.get(), nullptr);
    auto buy_ord_check = buy_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(buy_oid);
    ASSERT_NE(buy_ord_check.m_msg, nullptr);
    buy_ord_check.CheckQty(uid, pz_qty);
    buy_ord_check.CheckPrice(uid, pz_price);
    buy_ord_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 检查user1 持仓
    auto buy_pz_check = buy_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    buy_pz_check.CheckUid(uid);
    buy_pz_check.CheckSize(pz_qty);
    buy_pz_check.CheckFreeSize(-pz_qty);

    // 收user2 sell单成交回报
    sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    // 检查user2 持仓
    auto sell_pz_check = sell_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    sell_pz_check.CheckUid(uid_2);
    sell_pz_check.CheckSize(pz_qty);
    sell_pz_check.CheckFreeSize(pz_qty);
  }

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Sell, EOrderType::Market, qty,
                                                price, time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());
  // 校验rpc回报
  ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);
  // 收user1 buy单result
  auto order_result = te->PopResult();
  ASSERT_NE(order_result.m_msg.get(), nullptr);
  auto order_check = order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(pct_order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  order_check.CheckQtyTypeAndValue(EQtyType::Percentage, pct_qty);
  order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
}

TEST_F(PctQtyOrderTest, create_pct_qty_sell_with_pz_isolate) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Buy;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000.00000000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "10000";
    auto lv0_qty = "100";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  biz::user_id_t uid_1 = ********;
  stub user1(te, uid_1);

  {
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid_1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid_1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    FutureOneOfSwitchPositionModeBuilder switch_position_mode(symbol, coin, EPositionMode::BothSide, uid_1);
    auto resp = user1.process(switch_position_mode.Build());
    // 校验rpc回报
    ASSERT_EQ(resp->ret_code(), error::kErrorCodeSuccess);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto pz_checker_0 = result.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(pz_checker_0.m_msg, nullptr);
    EXPECT_EQ(EPositionMode::MergedSingle, static_cast<EPositionMode>(pz_checker_0.m_msg->mode()));
    EXPECT_EQ(ESide::None, pz_checker_0.m_msg->side());
    EXPECT_EQ(EPositionStatus::Inactive, pz_checker_0.m_msg->position_status());
    auto pz_checker_1 = result.RefFutureMarginResult().RefRelatedPosition(1);
    ASSERT_NE(pz_checker_1.m_msg, nullptr);
    EXPECT_EQ(EPositionMode::BothSide, static_cast<EPositionMode>(pz_checker_1.m_msg->mode()));
    EXPECT_EQ(ESide::None, pz_checker_1.m_msg->side());
    EXPECT_EQ(EPositionStatus::Normal, pz_checker_1.m_msg->position_status());
    auto pz_checker_2 = result.RefFutureMarginResult().RefRelatedPosition(2);
    ASSERT_NE(pz_checker_2.m_msg, nullptr);
    EXPECT_EQ(EPositionMode::BothSide, static_cast<EPositionMode>(pz_checker_2.m_msg->mode()));
    EXPECT_EQ(ESide::None, pz_checker_2.m_msg->side());
    EXPECT_EQ(EPositionStatus::Normal, pz_checker_2.m_msg->position_status());
  }

  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid_1, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    //  auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
    //  wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));
  }

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Buy, EOrderType::Market, qty,
                                                price, time_in_force, uid_1, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());
  // 校验rpc回报
  ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);
  // 收user1 buy单result
  auto order_result = te->PopResult();
  ASSERT_NE(order_result.m_msg.get(), nullptr);
  auto order_check = order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(pct_order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  order_check.CheckQtyTypeAndValue(EQtyType::Percentage, pct_qty);
  order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
}

TEST_F(PctQtyOrderTest, create_pct_qty_invalid_side) {
  biz::coin_t const coin = 5;      // BTC
  biz::symbol_t const symbol = 5;  // BTCUSDT
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  // user1
  biz::user_id_t const uid = 858007;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  // 充值
  {
    std::string money = "10000";  // 1w
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
    wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));
  }

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  EOrderType const order_type = EOrderType::Market;
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, static_cast<ESide>(/*max=*/3),
                                                order_type, qty, price, time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());

  ASSERT_NE(pz_buy_order_resp->ret_code(), error::kErrorCodeDefault);
}

TEST_F(PctQtyOrderTest, create_pct_qty_buy_with_sell_pz_with_sell_ord) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "10000";
    auto lv0_qty = "100";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  // user1
  biz::user_id_t const uid = 55588881;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    //  auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
    //  wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));
  }

  // opposite user2
  biz::user_id_t const uid_2 = 55588882;
  stub user2(te, uid_2);

  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid_2, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user2.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
  }

  {
    // user1: create position 1 qty
    biz::size_x_t const pz_qty = 1 * 1e6;
    biz::price_x_t const pz_price = 10000 * 1e4;

    // case准备 -- 构建持仓 1qty
    // ============================================================================================================
    // user1 sell 1 qty
    auto sell_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_ord_build(sell_oid, symbol, pz_index, ESide::Sell, EOrderType::Limit, pz_qty,
                                                 pz_price, time_in_force, uid, coin);
    auto sell_resp = user1.process(sell_ord_build.Build());
    ASSERT_EQ(sell_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 sell单result
    auto sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    auto sell_ord_check = sell_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(sell_oid);
    ASSERT_NE(sell_ord_check.m_msg, nullptr);
    sell_ord_check.CheckQty(uid, pz_qty);
    sell_ord_check.CheckPrice(uid, pz_price);
    sell_ord_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // user2 buy 1 qty
    auto buy_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_ord_build(buy_oid, symbol, pz_index, ESide::Buy, EOrderType::Limit, pz_qty,
                                                pz_price, time_in_force, uid_2, coin);
    auto buy_ord_resp = user2.process(buy_ord_build.Build());
    ASSERT_EQ(buy_ord_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 buy单result
    auto buy_ord_result = te->PopResult();
    ASSERT_NE(buy_ord_result.m_msg.get(), nullptr);
    auto buy_ord_check = buy_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(buy_oid);
    ASSERT_NE(buy_ord_check.m_msg, nullptr);
    buy_ord_check.CheckQty(uid_2, pz_qty);
    buy_ord_check.CheckPrice(uid_2, pz_price);
    buy_ord_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 检查user2 持仓
    auto buy_pz_check = buy_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    buy_pz_check.CheckUid(uid_2);
    buy_pz_check.CheckSize(pz_qty);
    buy_pz_check.CheckFreeSize(-pz_qty);

    // 收user1 sell单成交回报
    sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    // 检查user1 持仓
    auto sell_pz_check = sell_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    sell_pz_check.CheckUid(uid);
    sell_pz_check.CheckSize(pz_qty);
    sell_pz_check.CheckFreeSize(pz_qty);
  }

  {
    // case准备 -- pz->free_cost > 0
    biz::size_x_t const pz_qty = 1 * 1e6;
    biz::price_x_t const pz_price = 20000 * 1e4;

    // ============================================================================================================
    // user1 sell 0.01 qty
    auto sell_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_ord_build(sell_oid, symbol, pz_index, ESide::Sell, EOrderType::Limit, pz_qty,
                                                 pz_price, time_in_force, uid, coin);
    auto sell_resp = user2.process(sell_ord_build.Build());
    ASSERT_EQ(sell_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    auto sell_ord_check = sell_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(sell_oid);
    ASSERT_NE(sell_ord_check.m_msg, nullptr);
    sell_ord_check.CheckQty(uid, pz_qty);
    sell_ord_check.CheckPrice(uid, pz_price);
    sell_ord_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
  }

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Buy, EOrderType::Market, qty,
                                                price, time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());
  // 校验rpc回报
  ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);
  // 收user1 buy单result
  auto order_result = te->PopResult();
  ASSERT_NE(order_result.m_msg.get(), nullptr);
  auto order_check = order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(pct_order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  order_check.CheckQtyTypeAndValue(EQtyType::Percentage, pct_qty);
  order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
}

TEST_F(PctQtyOrderTest, create_pct_qty_sell_with_buy_pz_with_buy_ord) {
  biz::coin_t const coin = 5;            // BTC
  biz::symbol_t const symbol = 5;        // BTCUSDT
  biz::cross_idx_t const cross_idx = 5;  // BTCUSDT cross
  EPositionIndex const pz_index = EPositionIndex::Single;
  ETimeInForce const time_in_force = ETimeInForce::GoodTillCancel;

  {
    // 校准标记价格
    auto const underlying_price = bbase::decimal::Decimal<>("10000");
    auto const exchange_rate = bbase::decimal::Decimal<>("1.00");
    te->UpdateMarketData(symbol, underlying_price, exchange_rate);

    // 配合OrderBooK
    auto lv0_px = "10000";
    auto lv0_qty = "100";
    te->UpdateQuoteData(symbol, cross_idx, lv0_px, lv0_qty, false);
  }

  // user1
  biz::user_id_t const uid = 599232501;
  stub user1(te, uid);

  // -------------------------------------------------------------------------------------
  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user1.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
    //  auto wallet_check = deposit_result.RefAssetMarginResult().RefRelatedWallet(coin);
    //  wallet_check.CheckCoin(coin).CheckAB(biz::money_t(money));
  }

  // opposite user2
  biz::user_id_t const uid_2 = 595313432;
  stub user2(te, uid_2);

  {
    // 充值
    std::string money = "10000";
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    auto bonus_change = "0";  // 赠金
    EWalletRecordType wallet_record_type = EWalletRecordType::Deposit;
    FutureDepositBuilder deposit_build(req_id, uid_2, trans_id, coin, static_cast<int>(wallet_record_type), money,
                                       bonus_change);
    // 发rpc请求
    auto deposit_resp = user2.process(deposit_build.Build());
    // 校验rpc回报
    ASSERT_EQ(deposit_resp->ret_code(), 0);
    // 取trading result
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg, nullptr);
  }

  {
    // user1: create position 0.01 qty
    biz::size_x_t const pz_qty = 1 * 1e6;
    biz::price_x_t const pz_price = 10000 * 1e4;

    // case准备 -- 构建持仓 1qty
    // ============================================================================================================
    // user2 sell 1 qty
    auto sell_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder sell_ord_build(sell_oid, symbol, pz_index, ESide::Sell, EOrderType::Limit, pz_qty,
                                                 pz_price, time_in_force, uid_2, coin);
    auto sell_resp = user2.process(sell_ord_build.Build());
    ASSERT_EQ(sell_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    auto sell_ord_check = sell_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(sell_oid);
    ASSERT_NE(sell_ord_check.m_msg, nullptr);
    sell_ord_check.CheckQty(uid_2, pz_qty);
    sell_ord_check.CheckPrice(uid_2, pz_price);
    sell_ord_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);

    // user1 buy 1 qty
    auto buy_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_ord_build(buy_oid, symbol, pz_index, ESide::Buy, EOrderType::Limit, pz_qty,
                                                pz_price, time_in_force, uid, coin);
    auto buy_ord_resp = user1.process(buy_ord_build.Build());
    ASSERT_EQ(buy_ord_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user1 buy单result
    auto buy_ord_result = te->PopResult();
    ASSERT_NE(buy_ord_result.m_msg.get(), nullptr);
    auto buy_ord_check = buy_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(buy_oid);
    ASSERT_NE(buy_ord_check.m_msg, nullptr);
    buy_ord_check.CheckQty(uid, pz_qty);
    buy_ord_check.CheckPrice(uid, pz_price);
    buy_ord_check.CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

    // 检查user1 持仓
    auto buy_pz_check = buy_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    buy_pz_check.CheckUid(uid);
    buy_pz_check.CheckSize(pz_qty);
    buy_pz_check.CheckFreeSize(-pz_qty);

    // 收user2 sell单成交回报
    sell_ord_result = te->PopResult();
    ASSERT_NE(sell_ord_result.m_msg.get(), nullptr);
    // 检查user2 持仓
    auto sell_pz_check = sell_ord_result.RefFutureMarginResult().RefRelatedPosition(0);
    sell_pz_check.CheckUid(uid_2);
    sell_pz_check.CheckSize(pz_qty);
    sell_pz_check.CheckFreeSize(pz_qty);
  }

  {
    // case准备 -- pz->free_cost > 0
    biz::size_x_t const pz_qty = 1 * 1e6;
    biz::price_x_t const pz_price = 8000 * 1e4;

    // ============================================================================================================
    // user1 buy 0.01 qty
    auto buy_oid = te->GenUUID();
    FutureOneOfCreateOrderBuilder buy_ord_build(buy_oid, symbol, pz_index, ESide::Buy, EOrderType::Limit, pz_qty,
                                                pz_price, time_in_force, uid, coin);
    auto buy_resp = user2.process(buy_ord_build.Build());
    ASSERT_EQ(buy_resp->ret_code(), error::kErrorCodeSuccess);

    // 收user2 sell单result
    auto buy_ord_result = te->PopResult();
    ASSERT_NE(buy_ord_result.m_msg.get(), nullptr);
    auto buy_ord_check = buy_ord_result.RefFutureMarginResult().RefRelatedOrderByOrderId(buy_oid);
    ASSERT_NE(buy_ord_check.m_msg, nullptr);
    buy_ord_check.CheckQty(uid, pz_qty);
    buy_ord_check.CheckPrice(uid, pz_price);
    buy_ord_check.CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted);
  }

  // -------------------------------------------------------------------------------------
  // 百分比下单
  auto pct_order_id = te->GenUUID();
  biz::size_x_t const qty = 0;
  biz::size_x_t const pct_qty = 100;  // 100% 下单
  biz::price_x_t const price = 0;
  FutureOneOfCreateOrderBuilder pct_order_build(pct_order_id, symbol, pz_index, ESide::Sell, EOrderType::Market, qty,
                                                price, time_in_force, uid, coin);
  pct_order_build.msg.mutable_create_order()->set_qty_type_v(pct_qty);
  pct_order_build.msg.mutable_create_order()->set_qty_type(EQtyType::Percentage);
  auto pz_buy_order_resp = user1.process(pct_order_build.Build());
  // 校验rpc回报
  ASSERT_EQ(pz_buy_order_resp->ret_code(), error::kErrorCodeSuccess);
  // 收user1 buy单result
  auto order_result = te->PopResult();
  ASSERT_NE(order_result.m_msg.get(), nullptr);
  auto order_check = order_result.RefFutureMarginResult().RefRelatedOrderByOrderId(pct_order_id);
  ASSERT_NE(order_check.m_msg, nullptr);

  order_check.CheckQtyTypeAndValue(EQtyType::Percentage, pct_qty);
  order_check.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
}
