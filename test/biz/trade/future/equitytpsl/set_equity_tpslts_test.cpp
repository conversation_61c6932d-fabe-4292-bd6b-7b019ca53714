#include "test/biz/trade/future/equitytpsl/set_equity_tpslts_test.hpp"

#include <bbase/common/decimal/decimal.hpp>
#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>
#include <string>
#include <unordered_map>

#include "application/global_var_manager.hpp"
#include "data/error/error.hpp"
#include "data/type/biz_type.hpp"
#include "enums/eaccountmode/account_mode.pb.h"
#include "src/data/enum.hpp"
std::shared_ptr<tmock::CTradeAppMock> EquityTpsltsTest::te;

/*
1. 设置追踪出场失败
1.1. 全仓或者逐仓净值为0时，报错30193
1.2. pm模式下设置，报错30192
1.3. tag角色不符合条件，报错30194
1.4. 区间不合法，报错30194
1.5. eu不允许设置
*/
TEST_F(EquityTpsltsTest, set_equity_ts_fail) {
  biz::user_id_t user_id1 = 100000;
  // 构建客户端
  stub user1(te, user_id1);

  // 1. 全仓或者逐仓净值为0时，报错30193
  {
    SwitchMarginMode(user1, enums::eaccountmode::AccountMode::Cross);
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, 10, "remark");
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeZeroEquityNotSupportTPSLTS);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);

    // 切换至逐仓
    SwitchMarginMode(user1, enums::eaccountmode::AccountMode::Isolated);
    resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeZeroEquityNotSupportTPSLTS);
    result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  //  2. pm模式下设置，报错30192
  {
    SwitchMarginMode(user1, enums::eaccountmode::AccountMode::Portfolio);
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, 10, "remark");
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeTPSLTSNotSupportPm);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  SwitchMarginMode(user1, enums::eaccountmode::AccountMode::Cross);
  Deposit(user1, 1000);

  // 3. tag角色不符合条件，报错30194
  {
    // 尝试设置equity tpslts
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, 10, "remark");
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeInValidTPSLTSParameter);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // 4. 区间不合法，报错30191
  {
    int64_t res_user_tag = static_cast<int64_t>(EUserTag::ComboBot);
    auto business_line = ESourceBusinessLine::FGridBot;
    TestOneUserTag(user_id1, user1, EUserTag::ComboBot, business_line, res_user_tag);

    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, 100, "remark");
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeInValidTPSLTSParameter);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  //  1.5. eu不允许设置
  application::GlobalVarManager::Instance().set_site_id("EU");
  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("SitePermittedAction-SetEquityTpslts", "False");
  {
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, 10, "remark");
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeInValidTPSLTSParameter);
    EXPECT_EQ(resp->ret_msg(), "site:EU not support the action:SetEquityTpslts");
  }
}

/*
2. 设置追踪出场成功
2.1. grpc回复数据正常，dump数据成功；
2.2. 修改非法数据失败，grpc回复失败，tpsl数据是之前的，没有dump；
2.3. 不做任何修改，grpc回复数据，无dump
2.4. 只修改remark字段，grpc回复数据，有dump
2.5. 正常修改，grpc回复数据，有dump
2.6. 切换至逐仓成功，切换到pm模式失败，报错37034
2.7. 逐仓下正常修改，grpc回复数据，有dump
2.8. 进行充值，相关字段更新
2.9. 提现，相关字段更新
2.10. 净值提现至0，追踪出场数据清0
 */
TEST_F(EquityTpsltsTest, set_equity_ts_success) {
  biz::user_id_t user_id1 = 100000;
  // 构建客户端
  stub user1(te, user_id1);
  // 设置逐仓10x杠杆+充钱
  Deposit(user1, 1000);
  SwitchMarginMode(user1, enums::eaccountmode::AccountMode::Cross);
  int64_t res_user_tag = static_cast<int64_t>(EUserTag::ComboBot);
  auto business_line = ESourceBusinessLine::FGridBot;
  TestOneUserTag(user_id1, user1, EUserTag::ComboBot, business_line, res_user_tag);
  {
    // 1. grpc回复数据正常，dump数据成功；
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, 10, "remark");
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data.last_top_equity_time_e9(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data2.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data2.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data2.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data2.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data2.last_top_equity_time_e9(), 0);
  }

  {
    // 2. 修改非法数据失败，grpc回复失败，tpsl数据是之前的，没有dump；
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, 100, "remark");
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeInValidTPSLTSParameter);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data.last_top_equity_time_e9(), 0);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    // 3. 不做任何修改，grpc回复数据，无dump
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, 10, "remark");
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data.last_top_equity_time_e9(), 0);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    // 4. 只修改remark字段，grpc回复数据，有dump
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, 10, "remark2");
    set_equity_tpsl_ts.msg.clear_set_equity_tpsl_ts_req();
    set_equity_tpsl_ts.msg.mutable_set_equity_tpsl_ts_req()->set_remark("remark2");
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark2");
    EXPECT_EQ(std::atoll(equity_tpsl_data.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data.last_top_equity_time_e9(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data2.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data2.remark(), "remark2");
    EXPECT_EQ(std::atoll(equity_tpsl_data2.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data2.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data2.last_top_equity_time_e9(), 0);
  }

  {
    // 5. 正常修改，grpc回复数据，有dump
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, 20, "remark2");
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 20);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark2");
    EXPECT_EQ(std::atoll(equity_tpsl_data.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data.exit_equity().data()), 800);
    EXPECT_NE(equity_tpsl_data.last_top_equity_time_e9(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data2.trailing_stop_percent_e2(), 20);
    EXPECT_EQ(equity_tpsl_data2.remark(), "remark2");
    EXPECT_EQ(std::atoll(equity_tpsl_data2.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data2.exit_equity().data()), 800);
    EXPECT_NE(equity_tpsl_data2.last_top_equity_time_e9(), 0);
  }

  {
    // 6. 切换至逐仓成功，切换到pm模式失败，报错37034
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user1.m_uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(enums::eaction::Action::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        enums::eaccountmode::AccountMode::Isolated);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user1.m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);

    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        enums::eaccountmode::AccountMode::Portfolio);
    event = std::make_shared<event::MarginHdtsRequestEvent>(user1.m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->ret_code(), error::ErrorCode::kErrorCodeHasEquityTpsl);
  }

  {
    // 7. 逐仓情况下正常修改，grpc回复数据，有dump
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, 10, "remark");
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data.last_top_equity_time_e9(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data2.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data2.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data2.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data2.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data2.last_top_equity_time_e9(), 0);
  }

  {
    // 8. 进行充值，相关字段更新
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    FutureDepositBuilder deposit_build(req_id, user1.m_uid, trans_id, 5, EWalletRecordType::Deposit, "100", "0");
    const auto resp = user1.process(deposit_build.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto result = user1.m_te->PopResult();
    EXPECT_NE(result.m_msg, nullptr);
    auto equity_tpsl_data = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data.top_equity().data()), 1100);
    EXPECT_EQ(std::atoll(equity_tpsl_data.exit_equity().data()), 990);
    EXPECT_NE(equity_tpsl_data.last_top_equity_time_e9(), 0);
  }

  {
    // 9. 提现，相关字段更新
    const std::string req_id = user1.m_te->GenUUID();
    const std::string trans_id = user1.m_te->GenUUID();
    FutureWithdrawBuilder withdraw_build(req_id, user1.m_uid, trans_id, 5, 2, "100", "0");
    const auto resp = user1.process(withdraw_build.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto result = user1.m_te->PopResult();
    EXPECT_NE(result.m_msg, nullptr);
    auto equity_tpsl_data = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data.last_top_equity_time_e9(), 0);
  }

  {
    // 10. 净值提现至0，追踪出场数据清0
    const std::string req_id = user1.m_te->GenUUID();
    const std::string trans_id = user1.m_te->GenUUID();
    FutureWithdrawBuilder withdraw_build(req_id, user1.m_uid, trans_id, 5, 2, "1000", "0");
    const auto resp = user1.process(withdraw_build.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto result = user1.m_te->PopResult();
    EXPECT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::Withdraw);
    auto equity_tpsl_data = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data.last_top_equity_time_e9(), 0);
  }
}

/*
3. 触发追踪出场信号
3.1. 持有仓位，行情变化，净值更新并持久化
3.2. 行情变化，达到出场标准，dump流水
*/
TEST_F(EquityTpsltsTest, trigger_ts) {
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  // 构建客户端
  biz::user_id_t user_id1 = 100001;
  stub user1(te, user_id1);
  biz::user_id_t user_id2 = 100002;
  stub user2(te, user_id2);
  // 设置逐仓10x杠杆+充钱
  Deposit(user1, 1000);
  Deposit(user2);
  SwitchMarginMode(user1, enums::eaccountmode::AccountMode::Cross);
  int64_t res_user_tag = static_cast<int64_t>(EUserTag::ComboBot);
  auto business_line = ESourceBusinessLine::FGridBot;
  TestOneUserTag(user_id1, user1, EUserTag::ComboBot, business_line, res_user_tag);
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 10000, 10000, 10000);
  // 3.1. 持有仓位，行情变化，净值更新并持久化
  {
    // 1. grpc回复数据正常，dump数据成功；
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, 10, "remark");
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data.last_top_equity_time_e9(), 0);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data2.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data2.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data2.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data2.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data2.last_top_equity_time_e9(), 0);
  }
  // uid 1
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 1000000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }
  // uid2
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;
    biz::size_x_t qty = 1000000;
    biz::price_x_t price = 1000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);
    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }
  // 成交回报
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    EXPECT_EQ(result1.m_msg->header().user_id(), user_id1);
    auto orderCheck2 = result1.RefFutureMarginResult();
    auto positon = orderCheck2.RefRelatedPosition(0);
    positon.CheckSize(1000000);
    EXPECT_EQ(positon.m_msg->side(), ESide::Buy);
  }

  // 标记价格上升后，发生recalc事件
  {
    auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
        bbase::object_manager::ObjectType::kObjectConfigCenter);
    config_center->SetStringVar("ShortestInterval", "1");
    sleep(1);
    // sleep(10);
    auto price = std::make_shared<biz::FuturePriceData>();
    price->symbol_mark_price_map_->emplace(ESymbol::BTCUSDT, bbase::decimal::Decimal<>(13000));
    price->symbol_index_price_map_->emplace(ESymbol::BTCUSDT, bbase::decimal::Decimal<>(13000));
    price->symbol_last_price_map_->emplace(ESymbol::BTCUSDT, bbase::decimal::Decimal<>(13000));

    auto price_ev = std::make_shared<event::OnMarkPriceEvent>(0, event::EventType::kEventMarkPrice,
                                                              event::EventSourceType::kUnknown,
                                                              event::EventDispatchType::kBroadcastToWorker);
    price_ev->set_future_price(price);
    te->mark_price_synchronizer()->OnMarkPrice(price_ev);
    // 触发大计算,无dump
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(user_id1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result1 = te->PopResult();
    ASSERT_EQ(result1.m_msg.get(), nullptr);
    // 完成这一轮最后一个触发
    event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    // 前面得slepp 10s，这里才会有dump
    // auto equity_tpsl_data = result1.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    // EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 10);
    // EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    // EXPECT_EQ(std::atoll(equity_tpsl_data.top_equity().data()), 1029.99 * 1e8);
    // EXPECT_EQ(std::atoll(equity_tpsl_data.exit_equity().data()), 926.991 * 1e8);
    // EXPECT_NE(equity_tpsl_data.last_top_equity_time_e9(), 0);
  }
  // 2. 行情变化，达到出场标准，dump流水
  {
    sleep(1);
    auto price = std::make_shared<biz::FuturePriceData>();
    price->symbol_mark_price_map_->emplace(ESymbol::BTCUSDT, bbase::decimal::Decimal<>(1000));
    price->symbol_index_price_map_->emplace(ESymbol::BTCUSDT, bbase::decimal::Decimal<>(1000));
    price->symbol_last_price_map_->emplace(ESymbol::BTCUSDT, bbase::decimal::Decimal<>(1000));

    auto price_ev = std::make_shared<event::OnMarkPriceEvent>(1, event::EventType::kEventMarkPrice,
                                                              event::EventSourceType::kUnknown,
                                                              event::EventDispatchType::kBroadcastToWorker);
    price_ev->set_future_price(price);
    te->mark_price_synchronizer()->OnMarkPrice(price_ev);

    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(user_id1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event2 = std::make_shared<event::ReCalcEvent>(sharding_key);
    // 有两个用户一个时间片轮询不到，所以要有两次大计算触发
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event2, false);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event2, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data2.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data2.remark(), "remark");
    EXPECT_EQ(std::atof(equity_tpsl_data2.top_equity().data()), 1029.99);
    EXPECT_EQ(std::atof(equity_tpsl_data2.exit_equity().data()), 926.991);
    EXPECT_NE(equity_tpsl_data2.last_top_equity_time_e9(), 0);
  }
}
/*
4. bot切换逐仓和pm失败，报错37034
4.1. ComboBot，用户切仓位报错
4.2. ComboBot，二级标签ComboBot为1的，用户切仓位报错
*/

TEST_F(EquityTpsltsTest, has_ts_switch_margin_mode_fail) {
  {
    biz::user_id_t user_id1 = 100001;
    stub user1(te, user_id1);
    int64_t res_user_tag = static_cast<int64_t>(EUserTag::ComboBot);
    auto business_line = ESourceBusinessLine::FGridBot;
    TestOneUserTag(user_id1, user1, EUserTag::ComboBot, business_line, res_user_tag);
    SwitchMarginMode(user1, enums::eaccountmode::AccountMode::Cross);

    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user1.m_uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(enums::eaction::Action::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        enums::eaccountmode::AccountMode::Portfolio);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user1.m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->ret_code(), error::ErrorCode::kErrorCodeHasEquityTpsl);

    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        enums::eaccountmode::AccountMode::Isolated);
    event = std::make_shared<event::MarginHdtsRequestEvent>(user1.m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->ret_code(), error::ErrorCode::kErrorCodeHasEquityTpsl);

    Deposit(user1, 1000);
    // combot设置成功 grpc回复数据正常，dump数据成功；
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, 10, "remark");
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data.last_top_equity_time_e9(), 0);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data2.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data2.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data2.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data2.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data2.last_top_equity_time_e9(), 0);
  }

  {
    biz::user_id_t user_id2 = 100002;
    stub user2(te, user_id2);
    int64_t common_bot_tag = 103;
    int64_t expect_common_bot_tag = common_bot_tag;
    int64_t expect_user_tag = static_cast<int64_t>(EUserTag::CommonBot);
    // 构建客户端
    TestOneCommonBotTag(user_id2, user2, common_bot_tag, expect_user_tag, expect_common_bot_tag, false);
    SwitchMarginMode(user2, enums::eaccountmode::AccountMode::Cross);

    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user2.m_uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(enums::eaction::Action::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        enums::eaccountmode::AccountMode::Portfolio);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user2.m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->ret_code(), error::ErrorCode::kErrorCodeHasEquityTpsl);

    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        enums::eaccountmode::AccountMode::Isolated);
    event = std::make_shared<event::MarginHdtsRequestEvent>(user2.m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->ret_code(), error::ErrorCode::kErrorCodeHasEquityTpsl);

    Deposit(user2, 1000);
    // common bot设置成功 grpc回复数据正常，dump数据成功；
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id2, 10, "remark");
    auto resp = user2.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data.last_top_equity_time_e9(), 0);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data2.trailing_stop_percent_e2(), 10);
    EXPECT_EQ(equity_tpsl_data2.remark(), "remark");
    EXPECT_EQ(std::atoll(equity_tpsl_data2.top_equity().data()), 1000);
    EXPECT_EQ(std::atoll(equity_tpsl_data2.exit_equity().data()), 900);
    EXPECT_NE(equity_tpsl_data2.last_top_equity_time_e9(), 0);
  }
}

/*
1. 设置止盈止损失败
  1.1. 全仓净值为0时，报错30193
  1.2. pm和逐仓模式下设置，报错37035
  1.3. tag角色不符合条件，报错30194
  1.4. 区间不合法，报错30194
  1.5. 带着净值设置，止损值高于当前净值拒绝37013
  1.6. 带着净值设置，止盈值低于当前净值拒绝30027
  1.5. eu站不允许设置
*/

// 设置净值止盈止损失败
TEST_F(EquityTpsltsTest, set_equity_tpsl_fail) {
  biz::user_id_t user_id1 = 100000;
  // 构建客户端
  stub user1(te, user_id1);

  //   1. 全仓净值为0时，报错30193
  {
    SwitchMarginMode(user1, enums::eaccountmode::AccountMode::Cross);
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, "remark", "", 0, std::optional<int64_t>{}, 101, 99);
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeZeroEquityNotSupportTPSLTS);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  Deposit(user1, 1000);

  //    2. pm和逐仓模式下设置，报错37035
  {
    SwitchMarginMode(user1, enums::eaccountmode::AccountMode::Portfolio);
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, "remark", "", 0, std::optional<int64_t>{}, 101, 99);
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNotCrossCannotSetEquityTpsl);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);

    SwitchMarginMode(user1, enums::eaccountmode::AccountMode::Isolated);
    resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeNotCrossCannotSetEquityTpsl);
    result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  SwitchMarginMode(user1, enums::eaccountmode::AccountMode::Cross);

  // 3. tag角色不符合条件，报错30194
  {
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, "remark", "", 0, std::optional<int64_t>{}, 101, 99);
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeInValidTPSLTSParameter);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // 4. 区间不合法，报错30191
  {
    int64_t res_user_tag = static_cast<int64_t>(EUserTag::ComboBot);
    auto business_line = ESourceBusinessLine::FGridBot;
    TestOneUserTag(user_id1, user1, EUserTag::ComboBot, business_line, res_user_tag);

    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, "remark", "", 0, std::optional<int64_t>{}, 99, 99);
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeInValidTPSLTSParameter);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // 5. 带着净值设置，止损值高于当前净值拒绝37013
  {
    int64_t res_user_tag = static_cast<int64_t>(EUserTag::ComboBot);
    auto business_line = ESourceBusinessLine::FGridBot;
    TestOneUserTag(user_id1, user1, EUserTag::ComboBot, business_line, res_user_tag);

    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, "remark", "", 10000 * 1e8, std::optional<int64_t>{},
                                                         101, 99);
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSlShouldLtBase);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // 6. 带着净值设置，止盈值低于当前净值拒绝30027
  {
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, "remark", "", 100 * 1e8, std::optional<int64_t>{},
                                                         101, 99);
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeTpShouldGtBase);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  //  7. eu不允许设置
  application::GlobalVarManager::Instance().set_site_id("EU");
  auto config_center = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::object_manager::ConfigCenter>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  config_center->SetStringVar("SitePermittedAction-SetEquityTpslts", "False");
  {
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, "remark", "", 0, std::optional<int64_t>{}, 101, 99);
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeInValidTPSLTSParameter);
    EXPECT_EQ(resp->ret_msg(), "site:EU not support the action:SetEquityTpslts");
  }
}

/*
2. 设置止盈止损成功
  2.1. grpc回复数据正常，dump数据成功；
  2.2. 修改非法数据失败，grpc回复失败，tpsl数据是之前的，没有dump；
  2.3. 不做任何修改，grpc回复数据，无dump
  2.4. 只修改remark字段，grpc回复数据，有dump
  2.5. 正常修改，grpc回复数据，有dump
  2.6. 切换至逐仓和pm模式失败，报错37034
  2.7. 进行充值，相关字段更新
  2.8. 提现，相关字段更新
  2.9. 净值提现至0，tpsl数据清0
  2.10. 带着old_equity，设置成功
*/
TEST_F(EquityTpsltsTest, set_equity_tpsl_success) {
  biz::user_id_t user_id1 = 100000;
  // 构建客户端
  stub user1(te, user_id1);
  // 设置逐仓10x杠杆+充钱
  Deposit(user1, 1000);
  SwitchMarginMode(user1, enums::eaccountmode::AccountMode::Cross);
  int64_t res_user_tag = static_cast<int64_t>(EUserTag::ComboBot);
  auto business_line = ESourceBusinessLine::FGridBot;
  TestOneUserTag(user_id1, user1, EUserTag::ComboBot, business_line, res_user_tag);
  {
    // 1. grpc回复数据正常，dump数据成功；
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, "remark", "", 0, std::optional<int64_t>{}, 101, 99);
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    EXPECT_EQ(equity_tpsl_data.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data.tp_equity().data()), 1010);
    EXPECT_EQ(std::atoll(equity_tpsl_data.sl_equity().data()), 990);
    EXPECT_EQ(equity_tpsl_data.tp_equity_percent_e2(), 101);
    EXPECT_EQ(equity_tpsl_data.sl_equity_percent_e2(), 99);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data2.remark(), "remark");
    EXPECT_EQ(equity_tpsl_data2.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data2.tp_equity().data()), 1010);
    EXPECT_EQ(std::atoll(equity_tpsl_data2.sl_equity().data()), 990);
    EXPECT_EQ(equity_tpsl_data2.tp_equity_percent_e2(), 101);
    EXPECT_EQ(equity_tpsl_data2.sl_equity_percent_e2(), 99);
  }

  {
    // 2. 修改非法数据失败，grpc回复失败，tpsl数据是之前的，没有dump；
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, "remark", "", 0, std::optional<int64_t>{}, 99,
                                                         std::optional<int64_t>{});
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeInValidTPSLTSParameter);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    EXPECT_EQ(equity_tpsl_data.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data.tp_equity().data()), 1010);
    EXPECT_EQ(std::atoll(equity_tpsl_data.sl_equity().data()), 990);
    EXPECT_EQ(equity_tpsl_data.tp_equity_percent_e2(), 101);
    EXPECT_EQ(equity_tpsl_data.sl_equity_percent_e2(), 99);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }
  {
    // 3. 不做任何修改，grpc回复数据，无dump
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, "remark", "", 0, std::optional<int64_t>{}, 101, 99);
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    EXPECT_EQ(equity_tpsl_data.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data.tp_equity().data()), 1010);
    EXPECT_EQ(std::atoll(equity_tpsl_data.sl_equity().data()), 990);
    EXPECT_EQ(equity_tpsl_data.tp_equity_percent_e2(), 101);
    EXPECT_EQ(equity_tpsl_data.sl_equity_percent_e2(), 99);
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    // 4. 只修改remark字段，grpc回复数据，有dump
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, "remark2", "", 0, std::optional<int64_t>{},
                                                         std::optional<int64_t>{}, std::optional<int64_t>{});
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark2");
    EXPECT_EQ(equity_tpsl_data.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data.tp_equity().data()), 1010);
    EXPECT_EQ(std::atoll(equity_tpsl_data.sl_equity().data()), 990);
    EXPECT_EQ(equity_tpsl_data.tp_equity_percent_e2(), 101);
    EXPECT_EQ(equity_tpsl_data.sl_equity_percent_e2(), 99);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data2.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data2.remark(), "remark2");
    EXPECT_EQ(equity_tpsl_data2.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data2.tp_equity().data()), 1010);
    EXPECT_EQ(std::atoll(equity_tpsl_data2.sl_equity().data()), 990);
    EXPECT_EQ(equity_tpsl_data2.tp_equity_percent_e2(), 101);
    EXPECT_EQ(equity_tpsl_data2.sl_equity_percent_e2(), 99);
  }

  {
    // 5. 正常修改，grpc回复数据，有dump
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, "remark2", "", 0, std::optional<int64_t>{}, 110, 99);
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark2");
    EXPECT_EQ(equity_tpsl_data.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data.tp_equity().data()), 1100);
    EXPECT_EQ(std::atoll(equity_tpsl_data.sl_equity().data()), 990);
    EXPECT_EQ(equity_tpsl_data.tp_equity_percent_e2(), 110);
    EXPECT_EQ(equity_tpsl_data.sl_equity_percent_e2(), 99);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data2.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data2.remark(), "remark2");
    EXPECT_EQ(equity_tpsl_data2.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data2.tp_equity().data()), 1100);
    EXPECT_EQ(std::atoll(equity_tpsl_data2.sl_equity().data()), 990);
    EXPECT_EQ(equity_tpsl_data2.tp_equity_percent_e2(), 110);
    EXPECT_EQ(equity_tpsl_data2.sl_equity_percent_e2(), 99);
  }

  {
    // 6. 切换至逐仓和pm模式失败，报错37034
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(user1.m_uid);
    unified_v_2_request_dto->mutable_req_header()->set_action(enums::eaction::Action::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        enums::eaccountmode::AccountMode::Isolated);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(user1.m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->ret_code(), error::ErrorCode::kErrorCodeHasEquityTpsl);

    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        enums::eaccountmode::AccountMode::Portfolio);
    event = std::make_shared<event::MarginHdtsRequestEvent>(user1.m_uid, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    result = te->PopResult();
    ASSERT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->ret_code(), error::ErrorCode::kErrorCodeHasEquityTpsl);
  }

  {
    // 7. 进行充值，相关字段更新
    auto req_id = te->GenUUID();
    auto trans_id = te->GenUUID();
    FutureDepositBuilder deposit_build(req_id, user1.m_uid, trans_id, 5, EWalletRecordType::Deposit, "100", "0");
    const auto resp = user1.process(deposit_build.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto result = user1.m_te->PopResult();
    EXPECT_NE(result.m_msg, nullptr);
    auto equity_tpsl_data = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark2");
    EXPECT_EQ(equity_tpsl_data.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data.tp_equity().data()), 1210);
    EXPECT_EQ(std::atoll(equity_tpsl_data.sl_equity().data()), 1089);
    EXPECT_EQ(equity_tpsl_data.tp_equity_percent_e2(), 110);
    EXPECT_EQ(equity_tpsl_data.sl_equity_percent_e2(), 99);
  }

  {
    // 8. 提现，相关字段更新
    const std::string req_id = user1.m_te->GenUUID();
    const std::string trans_id = user1.m_te->GenUUID();
    FutureWithdrawBuilder withdraw_build(req_id, user1.m_uid, trans_id, 5, 2, "100", "0");
    const auto resp = user1.process(withdraw_build.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto result = user1.m_te->PopResult();
    EXPECT_NE(result.m_msg, nullptr);
    auto equity_tpsl_data = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark2");
    EXPECT_EQ(equity_tpsl_data.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data.tp_equity().data()), 1100);
    EXPECT_EQ(std::atoll(equity_tpsl_data.sl_equity().data()), 990);
    EXPECT_EQ(equity_tpsl_data.tp_equity_percent_e2(), 110);
    EXPECT_EQ(equity_tpsl_data.sl_equity_percent_e2(), 99);
  }

  {
    // 9. 净值提现至0，止盈止损数据清0
    const std::string req_id = user1.m_te->GenUUID();
    const std::string trans_id = user1.m_te->GenUUID();
    FutureWithdrawBuilder withdraw_build(req_id, user1.m_uid, trans_id, 5, 2, "1000", "0");
    const auto resp = user1.process(withdraw_build.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto result = user1.m_te->PopResult();
    EXPECT_NE(result.m_msg, nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::Withdraw);
    auto equity_tpsl_data = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark2");
    EXPECT_EQ(equity_tpsl_data.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data.tp_equity().data()), 0);
    EXPECT_EQ(std::atoll(equity_tpsl_data.sl_equity().data()), 0);
    EXPECT_EQ(equity_tpsl_data.tp_equity_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data.sl_equity_percent_e2(), 0);
  }

  {
    // 10. 带着old_equity，设置成功
    Deposit(user1, 1000);

    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, "remark2", "", 1100 * 1e8, std::optional<int64_t>{},
                                                         110, 60);
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark2");
    EXPECT_EQ(equity_tpsl_data.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data.tp_equity().data()), 1210);
    EXPECT_EQ(std::atoll(equity_tpsl_data.sl_equity().data()), 660);
    EXPECT_EQ(equity_tpsl_data.tp_equity_percent_e2(), 110);
    EXPECT_EQ(equity_tpsl_data.sl_equity_percent_e2(), 60);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data2.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data2.remark(), "remark2");
    EXPECT_EQ(equity_tpsl_data2.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data2.tp_equity().data()), 1210);
    EXPECT_EQ(std::atoll(equity_tpsl_data2.sl_equity().data()), 660);
    EXPECT_EQ(equity_tpsl_data2.tp_equity_percent_e2(), 110);
    EXPECT_EQ(equity_tpsl_data2.sl_equity_percent_e2(), 60);
  }
}

/*
触发止盈止损

*/
TEST_F(EquityTpsltsTest, trigger_tpsl) {
  EPositionIndex pz_index = EPositionIndex::Single;
  EOrderType order_type = EOrderType::Limit;
  // 构建客户端
  biz::user_id_t user_id1 = 100001;
  stub user1(te, user_id1);
  biz::user_id_t user_id2 = 100002;
  stub user2(te, user_id2);
  // 设置逐仓10x杠杆+充钱
  Deposit(user1, 1000);
  Deposit(user2);
  SwitchMarginMode(user1, enums::eaccountmode::AccountMode::Cross);
  int64_t res_user_tag = static_cast<int64_t>(EUserTag::ComboBot);
  auto business_line = ESourceBusinessLine::FGridBot;
  TestOneUserTag(user_id1, user1, EUserTag::ComboBot, business_line, res_user_tag);
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 10000, 10000, 10000);
  // 3.1. 持有仓位，行情变化，净值更新并持久化
  {
    // 1. grpc回复数据正常，dump数据成功；
    FutureOneOfSetEquityTpsltsBuilder set_equity_tpsl_ts(user_id1, "remark", "", 0, std::optional<int64_t>{}, 110, 90);
    auto resp = user1.process(set_equity_tpsl_ts.Build());
    EXPECT_EQ(resp->ret_code(), error::ErrorCode::kErrorCodeSuccess);
    const auto& equity_tpsl_data = resp->per_user_equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data.remark(), "remark");
    EXPECT_EQ(equity_tpsl_data.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data.tp_equity().data()), 1100);
    EXPECT_EQ(std::atoll(equity_tpsl_data.sl_equity().data()), 900);
    EXPECT_EQ(equity_tpsl_data.tp_equity_percent_e2(), 110);
    EXPECT_EQ(equity_tpsl_data.sl_equity_percent_e2(), 90);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data2.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data2.remark(), "remark");
    EXPECT_EQ(equity_tpsl_data2.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data2.tp_equity().data()), 1100);
    EXPECT_EQ(std::atoll(equity_tpsl_data2.sl_equity().data()), 900);
    EXPECT_EQ(equity_tpsl_data2.tp_equity_percent_e2(), 110);
    EXPECT_EQ(equity_tpsl_data2.sl_equity_percent_e2(), 90);
  }
  // uid 1
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Buy;

    biz::size_x_t qty = 10000000;
    biz::price_x_t price = 10000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id1, coin);

    auto resp1 = user1.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto orderCheck = result1.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }
  // uid2
  {
    auto order_id = te->GenUUID();
    ESide side = ESide::Sell;
    biz::size_x_t qty = 10000000;
    biz::price_x_t price = 1000'0000;

    FutureOneOfCreateOrderBuilder create_build(order_id, symbol, pz_index, side, order_type, qty, price,
                                               ETimeInForce::GoodTillCancel, user_id2, coin);
    auto resp1 = user2.process(create_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }
  // 成交回报
  {
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    EXPECT_EQ(result1.m_msg->header().user_id(), user_id1);
    auto orderCheck2 = result1.RefFutureMarginResult();
    auto positon = orderCheck2.RefRelatedPosition(0);
    positon.CheckSize(10000000);
    EXPECT_EQ(positon.m_msg->side(), ESide::Buy);
  }

  // 2. 行情变化，达到止损标准，dump流水
  {
    auto price = std::make_shared<biz::FuturePriceData>();
    price->symbol_mark_price_map_->emplace(ESymbol::BTCUSDT, bbase::decimal::Decimal<>(100));
    price->symbol_index_price_map_->emplace(ESymbol::BTCUSDT, bbase::decimal::Decimal<>(100));
    price->symbol_last_price_map_->emplace(ESymbol::BTCUSDT, bbase::decimal::Decimal<>(100));

    auto price_ev = std::make_shared<event::OnMarkPriceEvent>(1, event::EventType::kEventMarkPrice,
                                                              event::EventSourceType::kUnknown,
                                                              event::EventDispatchType::kBroadcastToWorker);
    price_ev->set_future_price(price);
    te->mark_price_synchronizer()->OnMarkPrice(price_ev);

    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(user_id1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event2 = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event2, false);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event2, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    EXPECT_EQ(result.m_msg->header().action(), EAction::TriggerEquitySL);
    auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    EXPECT_EQ(equity_tpsl_data2.trailing_stop_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data2.remark(), "remark");
    EXPECT_EQ(equity_tpsl_data2.quote_coin(), "");
    EXPECT_EQ(std::atoll(equity_tpsl_data2.tp_equity().data()), 0);
    EXPECT_EQ(std::atoll(equity_tpsl_data2.sl_equity().data()), 0);
    EXPECT_EQ(equity_tpsl_data2.tp_equity_percent_e2(), 0);
    EXPECT_EQ(equity_tpsl_data2.sl_equity_percent_e2(), 0);

    // 完成这一轮最后一个触发
    event2 = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event2, false);
  }

  // 3. 行情变化，达到止盈标准，因为已经清理，没有流程dump
  {
    auto price = std::make_shared<biz::FuturePriceData>();
    price->symbol_mark_price_map_->emplace(ESymbol::BTCUSDT, bbase::decimal::Decimal<>(26000));
    price->symbol_index_price_map_->emplace(ESymbol::BTCUSDT, bbase::decimal::Decimal<>(26000));
    price->symbol_last_price_map_->emplace(ESymbol::BTCUSDT, bbase::decimal::Decimal<>(26000));

    auto price_ev = std::make_shared<event::OnMarkPriceEvent>(1, event::EventType::kEventMarkPrice,
                                                              event::EventSourceType::kUnknown,
                                                              event::EventDispatchType::kBroadcastToWorker);
    price_ev->set_future_price(price);
    te->mark_price_synchronizer()->OnMarkPrice(price_ev);

    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(user_id1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event2 = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event2, false);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event2, false);
    auto result = te->PopResult();
    // ASSERT_EQ(result.m_msg.get(), nullptr);
    // EXPECT_EQ(result.m_msg->header().action(), EAction::TriggerEquityTP);
    // auto equity_tpsl_data2 = result.m_msg->asset_margin_result().per_user_trading_dataset().equity_tpsl_data();
    // EXPECT_EQ(equity_tpsl_data2.trailing_stop_percent_e2(), 0);
    // EXPECT_EQ(equity_tpsl_data2.remark(), "remark");
    // EXPECT_EQ(equity_tpsl_data2.quote_coin(), "");
    // EXPECT_EQ(std::atoll(equity_tpsl_data2.tp_equity().data()), 0);
    // EXPECT_EQ(std::atoll(equity_tpsl_data2.sl_equity().data()), 0);
    // EXPECT_EQ(equity_tpsl_data2.tp_equity_percent_e2(), 0);
    // EXPECT_EQ(equity_tpsl_data2.sl_equity_percent_e2(), 0);

    // 完成这一轮最后一个触发
    event2 = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event2, false);
  }
}
