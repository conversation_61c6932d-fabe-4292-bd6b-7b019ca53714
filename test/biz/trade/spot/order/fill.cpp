#include "fill.hpp"  // NOLINT

#include <string>

#include "enums/ecrossstatus/cross_status.pb.h"
#include "enums/eorderstatus/order_status.pb.h"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(SFillTest, self_fill) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("self_fill", uid, "self_fill", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("self_fill1", uid, "self_fill1", 5, 1, "100000", "0");
  deposit_resp = user1.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("21700"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "21706.1");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  //  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Buy", "Limit", "0.001", "21706.1");
  order_link_id = te->GenUUID();
  build2.SetOrderLinkID(order_link_id);

  auto resp2 = user1.create_order(build2.Build());
  ASSERT_EQ(resp2->order_link_id(), order_link_id);
  auto result2 = te->PopResult();

  ASSERT_NE(result2.m_msg.get(), nullptr);
}

TEST_F(SFillTest, partial_fill) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  biz::user_id_t uid2 = 100001;
  stub user2(te, uid2);

  FutureDepositBuilder deposit_build("partial_fill", uid1, "partial_fill", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("partial_fill", uid2, "partial_fill", 5, 1, "100000", "0");
  deposit_resp = user2.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Buy", "Limit", "0.002", "20000");
  auto order_link_id2 = te->GenUUID();
  build2.SetOrderLinkID(order_link_id);

  auto resp2 = user2.create_order(build2.Build());
  ASSERT_EQ(resp2->order_link_id(), order_link_id);
  auto result2 = te->PopResult();
  ASSERT_NE(result2.m_msg.get(), nullptr);
  te->PopResult();

  OpenApiV5CreateOrderBuilder build3("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id3 = te->GenUUID();
  build3.SetOrderLinkID(order_link_id3);

  auto resp3 = user1.create_order(build3.Build());
  ASSERT_EQ(resp3->order_link_id(), order_link_id3);
  auto result3 = te->PopResult();
  ASSERT_NE(result3.m_msg.get(), nullptr);

  auto result4 = te->PopResult();
  ASSERT_NE(result4.m_msg.get(), nullptr);
}

TEST_F(SFillTest, cancel_after_partial_fill) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  biz::user_id_t uid2 = 100001;
  stub user2(te, uid2);

  FutureDepositBuilder deposit_build("cancel_after_partial_fill", uid1, "cancel_after_partial_fill", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("cancel_after_partial_fill", uid2, "cancel_after_partial_fill", 5, 1, "100000",
                                      "0");
  deposit_resp = user2.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Buy", "Limit", "0.002", "20000");
  auto order_link_id2 = te->GenUUID();
  build2.SetOrderLinkID(order_link_id2);

  auto resp2 = user2.create_order(build2.Build());
  ASSERT_EQ(resp2->order_link_id(), order_link_id2);
  auto result2 = te->PopResult();
  ASSERT_NE(result2.m_msg.get(), nullptr);
  te->PopResult();

  OpenApiV5CancelOrderBuilder cancel_build("spot", "BTCUSDT", "", order_link_id2, "order");
  auto resp3 = user2.cancel_order(cancel_build.Build());
  auto result3 = te->PopResult(2000);
  ASSERT_NE(result3.m_msg.get(), nullptr);
}

TEST_F(SFillTest, x_makerfill_recovery) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  biz::user_id_t uid2 = 100001;
  stub user2(te, uid2);

  FutureDepositBuilder deposit_build("x_makerfill_recovery", uid1, "x_makerfill_recovery", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("x_makerfill_recovery", uid2, "x_makerfill_recovery", 5, 1, "100000", "0");
  deposit_resp = user2.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  // new recovery
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    te->DelOrderInMem(uid1, "spot", "BTCUSDT", "", order_link_id);

    OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Buy", "Limit", "0.001", "20000");
    auto order_link_id2 = te->GenUUID();
    build2.SetOrderLinkID(order_link_id2);

    auto resp2 = user2.create_order(build2.Build());
    ASSERT_EQ(resp2->order_link_id(), order_link_id2);
    auto result2 = te->PopResult(1000);
    ASSERT_NE(result2.m_msg.get(), nullptr);
    result2 = te->PopResult(1000);
    ASSERT_NE(result2.m_msg.get(), nullptr);
  }
}

TEST_F(SFillTest, market_partial_fill) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);

  FutureDepositBuilder deposit_build("market_partial_fill", uid2, "market_partial_fill", 1, 1, "100", "0");
  auto deposit_resp = user2.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("market_partial_fill", uid, "market_partial_fill", 5, 1, "100000", "0");
  deposit_resp = user1.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));

  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult(1000);
  ASSERT_NE(result.m_msg.get(), nullptr);
  result.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
      .Check(uid, "0.001", "20");

  OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Sell", "Market", "20", "");
  auto order_link_id1 = te->GenUUID();
  build1.SetOrderLinkID(order_link_id1);

  auto resp2 = user2.create_order(build1.Build());
  ASSERT_EQ(resp2->order_link_id(), order_link_id1);
  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id1)
      .CheckStatus(EOrderStatus::PartiallyFilledCanceled, ECrossStatus::Canceled)
      .Check(uid2, "20", "0");

  auto result3 = te->PopResult(5000);
  ASSERT_NE(result3.m_msg.get(), nullptr);
  result3.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill)
      .Check(uid, "0.001", "20");
}

TEST_F(SFillTest, cancel_rejected) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);

  FutureDepositBuilder deposit_build("cancel_rejected", uid2, "cancel_rejected", 1, 1, "100", "0");
  auto deposit_resp = user2.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("cancel_rejected", uid, "cancel_rejected", 5, 1, "100000", "0");
  deposit_resp = user1.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.01", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult(1000);
  ASSERT_NE(result.m_msg.get(), nullptr);
  result.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
      .Check(uid, "0.01", "200");
  te->SuspendCross();

  OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Sell", "Market", "20", "");
  auto order_link_id1 = te->GenUUID();
  build1.SetOrderLinkID(order_link_id1);

  auto resp2 = user2.create_order(build1.Build());
  ASSERT_EQ(resp2->order_link_id(), order_link_id1);

  OpenApiV5CancelOrderBuilder cancel_build("spot", "BTCUSDT", "", order_link_id, "order");
  auto resp4 = user1.cancel_order(cancel_build.Build());

  te->ResumeCross();
  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id1)
      .CheckStatus(EOrderStatus::PartiallyFilledCanceled, ECrossStatus::Canceled)
      .Check(uid2, "20", "0");

  auto result3 = te->PopResult(5000);
  ASSERT_NE(result3.m_msg.get(), nullptr);
  result3.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill)
      .Check(uid, "0.01", "200");

  auto result4 = te->PopResult(2000);
  ASSERT_NE(result4.m_msg.get(), nullptr);
  result4.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::Filled, ECrossStatus::CancelRejected)
      .Check(uid, "0.01", "200");
}

TEST_F(SFillTest, partial_fill_cancel) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);

  FutureDepositBuilder deposit_build("cancel_rejected", uid2, "cancel_rejected", 1, 1, "100", "0");
  auto deposit_resp = user2.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("cancel_rejected", uid, "cancel_rejected", 5, 1, "100000", "0");
  deposit_resp = user1.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.02", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult(1000);
  ASSERT_NE(result.m_msg.get(), nullptr);
  result.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
      .Check(uid, "0.02", "400");
  te->SuspendCross();

  OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Sell", "Market", "0.01", "");
  auto order_link_id1 = te->GenUUID();
  build1.SetOrderLinkID(order_link_id1);

  auto resp2 = user2.create_order(build1.Build());
  ASSERT_EQ(resp2->order_link_id(), order_link_id1);

  OpenApiV5CancelOrderBuilder cancel_build("spot", "BTCUSDT", "", order_link_id, "order");
  auto resp4 = user1.cancel_order(cancel_build.Build());

  te->ResumeCross();
  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id1)
      .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);

  auto result3 = te->PopResult(5000);
  ASSERT_NE(result3.m_msg.get(), nullptr);
  result3.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);

  auto result4 = te->PopResult(2000);
  ASSERT_NE(result4.m_msg.get(), nullptr);
  result4.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::PartiallyFilledCanceled, ECrossStatus::Canceled)
      .CheckLeaves("0.01", "200");
}

static constexpr const char* kSpotSymbolContent1 =
    R"({"data":[{"allowBargain":0,"allowPlan":1,"allowTrade":1,"banBuyStatus":0,"banSellStatus":0,"baseCoin":"BTC","baseCoinId":1,
    "baseCoinName":"BTC","baseCoinType":1,"basePrecision":10,"brokerId":9001,"crossId":10000,"direction":"","etpNavDeviation":"0","etpRiskSwitchStatus":0,
    "exchangeSymbolId":1,"forbidOpenapiTrade":0,"inleverage":0E-18,"isTest":0,"limitOrderPriceLimitPercentage":"10000000000000000","limitY":"15000000000000000","makerBuyFee":"0.00100000",
    "makerSellFee":"0.00100000","marginLoanOpen":1,"marketOrderPriceLimitPercentage":"10000000000000000","minPricePrecision":"0.000000000001","needPreviewCheck":0,
    "openPrice":0.0000000001,"openTime":1679973300000,"settleCoin":"USDT","settleCoinId":5,"settleCoinName":"USDT","settleCoinType":1,
    "settlePrecision":8,"showStatus":1,"showStatusOpenTime":1679973300000,"status":3,"symbolFullName":"BTCUSDT","symbolId":10,"symbolName":"BTCUSDT",
    "symbolType":1,"takerBuyFee":"0.00100000","takerSellFee":"0.00100000", "maxTradeQuantity":"100", "minTradeQuantity":"0.0000000001", "maxTradeAmount":"1000000",
    "minTradeAmount":"0.0000000001"}],"version":"f56ff996-a578-4f2f-9050-95f1b59465cf"})";

TEST_F(SFillTest, cancel_by_order_value_zero) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);

  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent1));
  FutureDepositBuilder deposit_build("cancel_by_order_value_zero", uid, "cancel_by_order_value_zero", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("cancel_by_order_value_zero", uid2, "cancel_by_order_value_zero", 5, 1, "100000",
                                      "0");
  deposit_resp = user2.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("100"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "99.999999999", "100");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult(1000);
  ASSERT_NE(result.m_msg.get(), nullptr);
  result.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
      .Check(uid, "99.999999999", "9999.9999999");
  te->SuspendCross();

  OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Buy", "Limit", "0.0000000001", "0.0000000001");
  auto order_link_id1 = te->GenUUID();
  build1.SetOrderLinkID(order_link_id1);

  // 限价单也要校验最小成交额
  RpcContext ct2;
  auto resp2 = user2.create_order(build1.Build(), ct2);
  // 现在调用CheckAmount来检查amount 之前是直接比较minAmount， 现在是先判断是否为0, Amount rounddown to zero,
  // 报170003错误
  ASSERT_EQ(ct2.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170003");
  // ASSERT_EQ(resp2->order_link_id(), order_link_id1);

  /*
  te->ResumeCross();
  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id1)
      .CheckCxlRejectReason(ECxlRejReason::EC_CancelByOrderValueZero)
      .CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled)
      .CheckLeaves("0.0000000001", "0");
  */
}

TEST_F(SFillTest, cancel_by_match_value_zero) {
  GTEST_SKIP() << "TO Complete";
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);

  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent1));
  FutureDepositBuilder deposit_build("cancel_by_match_value_zero", uid, "cancel_by_match_value_zero", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("cancel_by_match_value_zero", uid2, "cancel_by_match_value_zero", 5, 1, "100000",
                                      "0");
  deposit_resp = user2.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("0.0000000001"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "100", "0.0000000001");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult(1000);
  ASSERT_NE(result.m_msg.get(), nullptr);
  result.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
      .Check(uid, "100", "0.00000001");
  te->SuspendCross();

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("1"));
  OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Buy", "Limit", "0.000000001", "1");
  auto order_link_id1 = te->GenUUID();
  build1.SetOrderLinkID(order_link_id1);

  auto resp2 = user2.create_order(build1.Build());
  // 限价单不再强校验amount精度。约束条件：baseprecision + minpriceprecision = settleprecision不满足只报警
  ASSERT_EQ(resp2->order_link_id(), order_link_id1);

  /*
  te->ResumeCross();
  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id1)
      .CheckCxlRejectReason(ECxlRejReason::EC_CancelByMatchValueZero)
      .CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled)
      .CheckLeaves("0.000000001", "0.000000001");
  */
}
// maker: limit order,对手方为限价单,leaveQty >= minQty && 订单价值为0，终态：fill+leaveqty
TEST_F(SFillTest, limit_order_maker_fill) {
  GTEST_SKIP() << "TO Complete";
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);

  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent1));
  FutureDepositBuilder deposit_build("limit_order_maker_fill", uid, "limit_order_maker_fill", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("limit_order_maker_fill", uid2, "limit_order_maker_fill", 5, 1, "100000", "0");
  deposit_resp = user2.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("0.0000000001"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "100", "0.0000000001");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult(1000);
  ASSERT_NE(result.m_msg.get(), nullptr);
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("1"));
  te->SuspendCross();

  OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Buy", "Limit", "99.999999999", "1");
  auto order_link_id1 = te->GenUUID();
  build1.SetOrderLinkID(order_link_id1);

  auto resp2 = user2.create_order(build1.Build());
  // 限价单金额不满足settleprecision 只报警不拒单
  ASSERT_EQ(resp2->order_link_id(), order_link_id1);

  /*
  te->ResumeCross();
  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  auto result3 = te->PopResult(5000);
  result3.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckCxlRejectReason(ECxlRejReason::EC_NoError)
      .CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill)
      .CheckLeaves("0.000000001", "0.000000000000000001");
  auto result4 = te->PopResult(5000);
  ASSERT_EQ(result4.m_msg.get(), nullptr);
  */
}
// maker: limit order,对手方为市价单,leaveQty >= minQty && 订单价值为0，终态：fill+leaveqty
TEST_F(SFillTest, zero_value_maker_fill) {
  GTEST_SKIP() << "TO Complete";
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);

  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent1));
  FutureDepositBuilder deposit_build("zero_value_maker_fill", uid2, "zero_value_maker_fill", 1, 1, "100", "0");
  auto deposit_resp = user2.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("zero_value_maker_fill", uid, "zero_value_maker_fill", 5, 1, "100000", "0");
  deposit_resp = user1.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("2"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "100", "0.0000000001");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult(1000);
  ASSERT_NE(result.m_msg.get(), nullptr);
  te->SuspendCross();

  OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Sell", "Market", "99.999999999", "");
  auto order_link_id1 = te->GenUUID();
  build1.SetOrderLinkID(order_link_id1);

  auto resp2 = user2.create_order(build1.Build());
  ASSERT_EQ(resp2->order_link_id(), order_link_id1);

  te->ResumeCross();
  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);

  auto result3 = te->PopResult(5000);
  ASSERT_NE(result3.m_msg.get(), nullptr);
  result3.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckCxlRejectReason(ECxlRejReason::EC_NoError)
      .CheckStatus(EOrderStatus::Filled, ECrossStatus::MakerFill)
      .CheckLeaves("0.000000001", "0.000000000000000001");
}
// taker: limit order,leaveQty >= minQty && 订单价值为0 && 成交价值为0，终态：fill+leaveqty
TEST_F(SFillTest, limit_order_taker_second_order_value_zero) {
  GTEST_SKIP() << "TO Complete";
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);

  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent1));
  FutureDepositBuilder deposit_build("limit_order_taker_second_order_value_zero", uid,
                                     "limit_order_taker_second_order_value_zero", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("limit_order_taker_second_order_value_zero", uid2,
                                      "limit_order_taker_second_order_value_zero", 5, 1, "100000", "0");
  deposit_resp = user2.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("200"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "99.999999999", "100");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user2.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult(1000);
  ASSERT_NE(result.m_msg.get(), nullptr);
  te->SuspendCross();

  OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Sell", "Limit", "100", "0.0000000001");
  auto order_link_id1 = te->GenUUID();
  build1.SetOrderLinkID(order_link_id1);

  auto resp2 = user1.create_order(build1.Build());
  ASSERT_EQ(resp2->order_link_id(), order_link_id1);

  te->ResumeCross();
  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id1)
      .CheckCxlRejectReason(ECxlRejReason::EC_NoError)
      .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill)
      .CheckLeaves("0.000000001", "0");
}
// taker: limit order,leaveQty >= minQty && 订单价值不等于0 && 成交价值为0，终态：fill+leaveqty
TEST_F(SFillTest, limit_order_taker_second_matchvalue_zero) {
  GTEST_SKIP() << "TO Complete";
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);

  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent1));
  FutureDepositBuilder deposit_build("limit_order_taker_second_matchvalue_zero", uid,
                                     "limit_order_taker_second_matchvalue_zero", 1, 1, "1000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("limit_order_taker_second_matchvalue_zero", uid2,
                                      "limit_order_taker_second_matchvalue_zero", 5, 1, "1000000", "0");
  deposit_resp = user2.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("0.0000000001"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "99.999999999", "0.0000000001");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  // 限价单校验金额 精度不满足settleprecision 只报警不拒单
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  // auto result = te->PopResult(1000);
  // ASSERT_NE(result.m_msg.get(), nullptr);

  /*
  OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Sell", "Limit", "99.999999999", "0.0000000001");
  auto order_link_id1 = te->GenUUID();
  build1.SetOrderLinkID(order_link_id1);

  resp1 = user1.create_order(build1.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id1);
  auto result = te->PopResult(1000);
  ASSERT_NE(result.m_msg.get(), nullptr);
  te->SuspendCross();

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("100"));
  OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Buy", "Limit", "100", "100");
  auto order_link_id2 = te->GenUUID();
  build2.SetOrderLinkID(order_link_id2);

  auto resp2 = user2.create_order(build2.Build());
  ASSERT_EQ(resp2->order_link_id(), order_link_id2);

  te->ResumeCross();
  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id2)
      .CheckCxlRejectReason(ECxlRejReason::EC_NoError)
      .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill)
      .CheckLeaves("0.000000001", "9999.999999990000000001");
  */
}
// taker: maketbase order,leaveQty < minQty，终态：fill+leaveqty
TEST_F(SFillTest, zero_value_taker_fill) {
  GTEST_SKIP() << "TO Complete";
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);

  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent1));
  FutureDepositBuilder deposit_build("zero_value_maker_fill", uid2, "zero_value_maker_fill", 1, 1, "100", "0");
  auto deposit_resp = user2.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("zero_value_maker_fill", uid, "zero_value_maker_fill", 5, 1, "100000", "0");
  deposit_resp = user1.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("100"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "99.999999999", "100");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult(1000);
  ASSERT_NE(result.m_msg.get(), nullptr);
  te->SuspendCross();

  // 通过修改basePrecision的值来修改min_qty参数
  constexpr const char* kSpotSymbolContentTmp =
      R"({"data":[{"allowBargain":0,"allowPlan":1,"allowTrade":1,"banBuyStatus":0,"banSellStatus":0,"baseCoin":"BTC","baseCoinId":1,
    "baseCoinName":"BTC","baseCoinType":1,"basePrecision":8,"brokerId":9001,"crossId":10000,"direction":"","etpNavDeviation":"0","etpRiskSwitchStatus":0,
    "exchangeSymbolId":1,"forbidOpenapiTrade":0,"inleverage":0E-18,"isTest":0,"limitOrderPriceLimitPercentage":"10000000000000","limitY":15000000000000000,"makerBuyFee":"0.00100000",
    "makerSellFee":"0.00100000","marginLoanOpen":1,"marketOrderPriceLimitPercentage":"10000000000000","minPricePrecision":"0.000000000001","needPreviewCheck":0,
    "openPrice":100.000000000000000000,"openTime":1679973300000,"settleCoin":"USDT","settleCoinId":5,"settleCoinName":"USDT","settleCoinType":1,
    "settlePrecision":8,"showStatus":1,"showStatusOpenTime":1679973300000,"status":3,"symbolFullName":"BTCUSDT","symbolId":10,"symbolName":"BTCUSDT",
    "symbolType":1,"takerBuyFee":"0.00100000","takerSellFee":"0.00100000", "maxTradeQuantity":"100", "minTradeQuantity":"0.00000001", "maxTradeAmount":"1000000",
    "minTradeAmount":"0.0000000001"}],"version":"f56ff996-a578-4f2f-9050-95f1b59465cf"})";

  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContentTmp));
  OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Sell", "Market", "100", "");
  auto order_link_id1 = te->GenUUID();
  build1.SetOrderLinkID(order_link_id1);

  auto resp2 = user2.create_order(build1.Build());
  ASSERT_EQ(resp2->order_link_id(), order_link_id1);

  te->ResumeCross();
  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id1)
      .CheckCxlRejectReason(ECxlRejReason::EC_NoError)
      .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill)
      .CheckLeaves("0.000000001", "0");
}
// taker: marketbase order,leaveQty >= minQty && 成交价值为0，终态：fill+leaveqty
TEST_F(SFillTest, zero_value_taker_second_match_market_base) {
  GTEST_SKIP() << "TO Complete";
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);

  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent1));
  FutureDepositBuilder deposit_build("zero_value_taker_second_match", uid2, "zero_value_taker_second_match", 1, 1,
                                     "100", "0");
  auto deposit_resp = user2.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("zero_value_taker_second_match", uid, "zero_value_taker_second_match", 5, 1,
                                      "100000", "0");
  deposit_resp = user1.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("100"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "99.999999999", "0.0000000001");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  // 限价单amount精度不合法 只报警不拒单
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  /*
  auto result = te->PopResult(1000);
  ASSERT_NE(result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Buy", "Limit", "100", "0.0000000001");
  auto order_link_id1 = te->GenUUID();
  build1.SetOrderLinkID(order_link_id1);

  auto resp2 = user1.create_order(build1.Build());
  ASSERT_EQ(resp2->order_link_id(), order_link_id1);
  auto result1 = te->PopResult(1000);
  ASSERT_NE(result1.m_msg.get(), nullptr);
  te->SuspendCross();

  OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Sell", "Market", "100", "");
  auto order_link_id2 = te->GenUUID();
  build2.SetOrderLinkID(order_link_id2);

  auto resp3 = user2.create_order(build2.Build());
  ASSERT_EQ(resp3->order_link_id(), order_link_id2);

  te->ResumeCross();
  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id2)
      .CheckCxlRejectReason(ECxlRejReason::EC_NoError)
      .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill)
      .CheckLeaves("0.000000001", "0");
  */
}
// taker: marketquote order,leaveAmount >= minAmount && 成交价值为0，终态：fill+leaveqty
TEST_F(SFillTest, zero_value_taker_second_match_market_quote) {
  GTEST_SKIP() << "TO Complete";
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);

  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent1));
  FutureDepositBuilder deposit_build("zero_value_taker_second_match", uid, "zero_value_taker_second_match", 1, 1,
                                     "1000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("zero_value_taker_second_match", uid2, "zero_value_taker_second_match", 5, 1,
                                      "10000000", "0");
  deposit_resp = user2.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("1"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "99.9999", "1");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult(1000);
  ASSERT_NE(result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Sell", "Limit", "100", "1");
  auto order_link_id1 = te->GenUUID();
  build1.SetOrderLinkID(order_link_id1);

  auto resp2 = user1.create_order(build1.Build());
  ASSERT_EQ(resp2->order_link_id(), order_link_id1);
  auto result1 = te->PopResult(1000);
  ASSERT_NE(result1.m_msg.get(), nullptr);
  te->SuspendCross();

  // 通过修改basePrecision的值来修改min_qty参数
  constexpr const char* kSpotSymbolContentTmp =
      R"({"data":[{"allowBargain":0,"allowPlan":1,"allowTrade":1,"banBuyStatus":0,"banSellStatus":0,"baseCoin":"BTC","baseCoinId":1,
    "baseCoinName":"BTC","baseCoinType":1,"basePrecision":3,"brokerId":9001,"crossId":10000,"direction":"","etpNavDeviation":"0","etpRiskSwitchStatus":0,
    "exchangeSymbolId":1,"forbidOpenapiTrade":0,"inleverage":0E-18,"isTest":0,"limitOrderPriceLimitPercentage":"10000000000000","limitY":15000000000000000,"makerBuyFee":"0.00100000",
    "makerSellFee":"0.00100000","marginLoanOpen":1,"marketOrderPriceLimitPercentage":"10000000000000","minPricePrecision":"0.000000000001","needPreviewCheck":0,
    "openPrice":100.000000000000000000,"openTime":1679973300000,"settleCoin":"USDT","settleCoinId":5,"settleCoinName":"USDT","settleCoinType":1,
    "settlePrecision":7,"showStatus":1,"showStatusOpenTime":1679973300000,"status":3,"symbolFullName":"BTCUSDT","symbolId":10,"symbolName":"BTCUSDT",
    "symbolType":1,"takerBuyFee":"0.00100000","takerSellFee":"0.00100000", "maxTradeQuantity":"100", "minTradeQuantity":"0.00000001", "maxTradeAmount":"1000000",
    "minTradeAmount":"0.0000000001"}],"version":"f56ff996-a578-4f2f-9050-95f1b59465cf"})";
  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContentTmp));
  OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Buy", "Market", "100", "");
  auto order_link_id2 = te->GenUUID();
  build2.SetOrderLinkID(order_link_id2);

  auto resp3 = user2.create_order(build2.Build());
  ASSERT_EQ(resp3->order_link_id(), order_link_id2);

  te->ResumeCross();
  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id2)
      .CheckCxlRejectReason(ECxlRejReason::EC_NoError)
      .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill)
      .CheckLeaves("0", "0.0001");
}
// taker: limitbase order,leaveQty >= minQty && leaveAmount >= minAmount && 成交价值为0，终态：fill+leaveqty
TEST_F(SFillTest, zero_value_taker_secondmatch_limitbase) {
  GTEST_SKIP() << "TO Complete";
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);

  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent1));
  FutureDepositBuilder deposit_build("zero_value_taker_secondmatch_limitbase", uid,
                                     "zero_value_taker_secondmatch_limitbase", 1, 1, "1000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("zero_value_taker_secondmatch_limitbase", uid2,
                                      "zero_value_taker_secondmatch_limitbase", 5, 1, "1000000", "0");
  deposit_resp = user2.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("0.0000000001"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "99.999999999", "0.0000000001");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  // 限价单amount 精度不合法 只报警 不拒单
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  /*
  auto result = te->PopResult(1000);
  ASSERT_NE(result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Sell", "Limit", "99.999999999", "0.0000000001");
  auto order_link_id1 = te->GenUUID();
  build1.SetOrderLinkID(order_link_id1);

  resp1 = user1.create_order(build1.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id1);
  result = te->PopResult(1000);
  ASSERT_NE(result.m_msg.get(), nullptr);
  te->SuspendCross();

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("100"));
  OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Buy", "Limit", "100", "100", "", "", "", "", "", "", "IOC");
  auto order_link_id2 = te->GenUUID();
  build2.SetOrderLinkID(order_link_id2);

  auto resp2 = user2.create_order(build2.Build());
  ASSERT_EQ(resp2->order_link_id(), order_link_id2);

  te->ResumeCross();
  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id2)
      .CheckCxlRejectReason(ECxlRejReason::EC_NoError)
      .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill)
      .CheckLeaves("0.000000001", "9999.999999990000000001");
  */
}

TEST_F(SFillTest, taker_to_maker_two_fee_items) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);

  {
    // 设置特殊费率
    SpotSetFeeRateBuilder builder("", uid, "", -10000, "maker", false, true, false);
    auto resp = user1.process(builder.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    SpotSetFeeRateBuilder builder2("", uid, "", 20000, "taker", true, true, false);
    resp = user2.process(builder2.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }
  {
    FutureDepositBuilder deposit_build("market_partial_fill", uid2, "market_partial_fill", 1, 1, "100", "0");
    auto deposit_resp = user2.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);

    FutureDepositBuilder deposit_build1("market_partial_fill", uid, "market_partial_fill", 5, 1, "100000", "0");
    deposit_resp = user1.process(deposit_build1.Build());
    deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);

    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));

    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.1", "20000");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user2.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid2, "0.1", "2000");

    OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Buy", "Limit", "0.2", "20000");
    auto order_link_id1 = te->GenUUID();
    build1.SetOrderLinkID(order_link_id1);

    // user1 taker
    auto resp2 = user1.create_order(build1.Build());
    ASSERT_EQ(resp2->order_link_id(), order_link_id1);
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
    auto uid_ord_check = result2.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(order_link_id1);
    ASSERT_NE(uid_ord_check.m_msg, nullptr);
    EXPECT_EQ(uid_ord_check.m_msg->cum_exec_fee_map_size(), 1);
    auto fit = uid_ord_check.m_msg->cum_exec_fee_map().find(1);
    ASSERT_NE(fit, uid_ord_check.m_msg->cum_exec_fee_map().end());
    EXPECT_EQ(fit->second, "0.00002");
    EXPECT_EQ(fit->first, 1);

    // user2 maker
    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
    auto uid2_ord_check = result3.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
    ASSERT_NE(uid2_ord_check.m_msg, nullptr);
    EXPECT_EQ(uid2_ord_check.m_msg->cum_exec_fee_map_size(), 1);
    auto fit1 = uid2_ord_check.m_msg->cum_exec_fee_map().find(5);
    ASSERT_NE(fit1, uid2_ord_check.m_msg->cum_exec_fee_map().end());
    EXPECT_EQ(fit1->second, "2");
    EXPECT_EQ(fit1->first, 5);

    // user2 taker
    OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Sell", "Limit", "0.2", "20000");
    auto order_link_id2 = te->GenUUID();
    build2.SetOrderLinkID(order_link_id2);

    auto resp3 = user2.create_order(build2.Build());
    ASSERT_EQ(resp3->order_link_id(), order_link_id2);
    auto result4 = te->PopResult();
    ASSERT_NE(result4.m_msg.get(), nullptr);

    uid2_ord_check = result4.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(order_link_id2);
    ASSERT_NE(uid2_ord_check.m_msg, nullptr);
    EXPECT_EQ(uid2_ord_check.m_msg->cum_exec_fee_map_size(), 1);
    fit1 = uid2_ord_check.m_msg->cum_exec_fee_map().find(5);
    ASSERT_NE(fit1, uid2_ord_check.m_msg->cum_exec_fee_map().end());
    EXPECT_EQ(fit1->second, "2");
    EXPECT_EQ(fit1->first, 5);

    // user1 maker
    auto result5 = te->PopResult();
    ASSERT_NE(result5.m_msg.get(), nullptr);
    uid_ord_check = result5.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(order_link_id1);
    ASSERT_NE(uid_ord_check.m_msg, nullptr);
    EXPECT_EQ(uid_ord_check.m_msg->cum_exec_fee_map_size(), 2);
    fit = uid_ord_check.m_msg->cum_exec_fee_map().find(1);
    ASSERT_NE(fit, uid_ord_check.m_msg->cum_exec_fee_map().end());
    EXPECT_EQ(fit->second, "0.00002");
    EXPECT_EQ(fit->first, 1);
    fit = uid_ord_check.m_msg->cum_exec_fee_map().find(5);
    ASSERT_NE(fit, uid_ord_check.m_msg->cum_exec_fee_map().end());
    EXPECT_EQ(fit->second, "-0.2");
    EXPECT_EQ(fit->first, 5);

    OpenApiV5CreateOrderBuilder build3("spot", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    auto order_link_id3 = te->GenUUID();
    build3.SetOrderLinkID(order_link_id3);

    // user1 taker
    auto resp4 = user1.create_order(build3.Build());
    ASSERT_EQ(resp4->order_link_id(), order_link_id3);
    auto result6 = te->PopResult();
    ASSERT_NE(result6.m_msg.get(), nullptr);
    uid_ord_check = result6.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(order_link_id3);
    ASSERT_NE(uid_ord_check.m_msg, nullptr);
    EXPECT_EQ(uid_ord_check.m_msg->cum_exec_fee_map_size(), 1);
    fit = uid_ord_check.m_msg->cum_exec_fee_map().find(1);
    ASSERT_NE(fit, uid_ord_check.m_msg->cum_exec_fee_map().end());
    EXPECT_EQ(fit->second, "0.00002");
    EXPECT_EQ(fit->first, 1);

    // user2 maker
    auto result7 = te->PopResult();
    ASSERT_NE(result7.m_msg.get(), nullptr);
    uid2_ord_check = result7.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(order_link_id2);
    ASSERT_NE(uid2_ord_check.m_msg, nullptr);
    EXPECT_EQ(uid2_ord_check.m_msg->cum_exec_fee_map_size(), 1);
    fit = uid2_ord_check.m_msg->cum_exec_fee_map().find(5);
    ASSERT_NE(fit, uid2_ord_check.m_msg->cum_exec_fee_map().end());
    EXPECT_EQ(fit->second, "4");
    EXPECT_EQ(fit->first, 5);
  }
  {
    FutureDepositBuilder deposit_build("taker_to_maker_two_fee_items", uid, "taker_to_maker_two_fee_items", 1, 1, "100",
                                       "0");
    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);

    FutureDepositBuilder deposit_build1("taker_to_maker_two_fee_items", uid2, "taker_to_maker_two_fee_items", 5, 1,
                                        "100000", "0");
    deposit_resp = user2.process(deposit_build1.Build());
    deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);

    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));

    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp = user2.create_order(build.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid2, "0.1", "2000");

    OpenApiV5CreateOrderBuilder build1("spot", "BTCUSDT", 0, "Sell", "Limit", "0.2", "20000");
    auto order_link_id1 = te->GenUUID();
    build1.SetOrderLinkID(order_link_id1);

    // user1 taker
    resp = user1.create_order(build1.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id1);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto uid_ord_check = result.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(order_link_id1);
    ASSERT_NE(uid_ord_check.m_msg, nullptr);
    EXPECT_EQ(uid_ord_check.m_msg->cum_exec_fee_map_size(), 1);
    auto fit = uid_ord_check.m_msg->cum_exec_fee_map().find(5);
    ASSERT_NE(fit, uid_ord_check.m_msg->cum_exec_fee_map().end());
    EXPECT_EQ(fit->second, "0.4");
    EXPECT_EQ(fit->first, 5);

    // user2 maker
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto uid2_ord_check = result.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(order_link_id);
    ASSERT_NE(uid2_ord_check.m_msg, nullptr);
    EXPECT_EQ(uid2_ord_check.m_msg->cum_exec_fee_map_size(), 1);
    auto fit1 = uid2_ord_check.m_msg->cum_exec_fee_map().find(1);
    ASSERT_NE(fit1, uid2_ord_check.m_msg->cum_exec_fee_map().end());
    EXPECT_EQ(fit1->second, "0.0001");
    EXPECT_EQ(fit1->first, 1);

    // user2 taker
    OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Buy", "Limit", "0.2", "20000");
    auto order_link_id2 = te->GenUUID();
    build2.SetOrderLinkID(order_link_id2);

    resp = user2.create_order(build2.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id2);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    uid2_ord_check = result.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(order_link_id2);
    ASSERT_NE(uid2_ord_check.m_msg, nullptr);
    EXPECT_EQ(uid2_ord_check.m_msg->cum_exec_fee_map_size(), 1);
    fit1 = uid2_ord_check.m_msg->cum_exec_fee_map().find(1);
    ASSERT_NE(fit1, uid2_ord_check.m_msg->cum_exec_fee_map().end());
    EXPECT_EQ(fit1->second, "0.0001");
    EXPECT_EQ(fit1->first, 1);

    // user1 maker
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    uid_ord_check = result.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(order_link_id1);
    ASSERT_NE(uid_ord_check.m_msg, nullptr);
    EXPECT_EQ(uid_ord_check.m_msg->cum_exec_fee_map_size(), 2);
    fit = uid_ord_check.m_msg->cum_exec_fee_map().find(1);
    ASSERT_NE(fit, uid_ord_check.m_msg->cum_exec_fee_map().end());
    EXPECT_EQ(fit->second, "-0.00001");
    EXPECT_EQ(fit->first, 1);
    fit = uid_ord_check.m_msg->cum_exec_fee_map().find(5);
    ASSERT_NE(fit, uid_ord_check.m_msg->cum_exec_fee_map().end());
    EXPECT_EQ(fit->second, "0.4");
    EXPECT_EQ(fit->first, 5);

    OpenApiV5CreateOrderBuilder build3("spot", "BTCUSDT", 0, "Sell", "Limit", "0.1", "20000");
    auto order_link_id3 = te->GenUUID();
    build3.SetOrderLinkID(order_link_id3);

    // user1 taker
    resp = user1.create_order(build3.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id3);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    uid_ord_check = result.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(order_link_id3);
    ASSERT_NE(uid_ord_check.m_msg, nullptr);
    EXPECT_EQ(uid_ord_check.m_msg->cum_exec_fee_map_size(), 1);
    fit = uid_ord_check.m_msg->cum_exec_fee_map().find(5);
    ASSERT_NE(fit, uid_ord_check.m_msg->cum_exec_fee_map().end());
    EXPECT_EQ(fit->second, "0.4");
    EXPECT_EQ(fit->first, 5);

    // user2 maker
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    uid2_ord_check = result.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(order_link_id2);
    ASSERT_NE(uid2_ord_check.m_msg, nullptr);
    EXPECT_EQ(uid2_ord_check.m_msg->cum_exec_fee_map_size(), 1);
    fit = uid2_ord_check.m_msg->cum_exec_fee_map().find(1);
    ASSERT_NE(fit, uid2_ord_check.m_msg->cum_exec_fee_map().end());
    EXPECT_EQ(fit->second, "0.0002");
    EXPECT_EQ(fit->first, 1);
  }
}

std::shared_ptr<tmock::CTradeAppMock> SFillTest::te;
