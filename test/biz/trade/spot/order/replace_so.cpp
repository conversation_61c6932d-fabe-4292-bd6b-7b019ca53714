#include "replace_so.hpp"  // NOLINT

#include <string>

#include "enums/eorderstatus/order_status.pb.h"
#include "lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

// TEST_F(SReplaceSoTest, replace_normal_so) {
//   biz::user_id_t uid = 100000;
//   stub user1(te, uid);

//   te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20000));
//   te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));

//   FutureDepositBuilder deposit_build("open_api_cancel_tpsl_order", uid,
//     "open_api_cancel_tpsl_order", 1, 1, "100", "0");
//   auto deposit_resp = user1.process(deposit_build.Build());
//   auto deposit_result = te->PopResult();
//   ASSERT_NE(deposit_result.m_msg.get(), nullptr);

//   auto order_link_id = te->GenUUID();

//   // 币对不存在
//   {
//     SpotSiteApiReplacePlanSpotOrderBuilder build("XXXUSDT", "1", "20000", "21000", "1", "0");
//     RpcContext ct{};
//     auto resp = user1.replace_plan_order(build.Build(), ct);
//     ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "32101");
//   }

//   // planType不合法
//   {
//     SpotSiteApiReplacePlanSpotOrderBuilder build("BTCUSDT", "1", "20000", "21000", "3", "0");
//     RpcContext ct{};
//     auto resp = user1.replace_plan_order(build.Build(), ct);
//     ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "30003");
//   }

//   {
//     OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "21000");
//     build.SetOrderFilter("StopOrder");
//     build.SetOrderLinkID(order_link_id);
//     build.SetTriggerPrice("21000");

//     auto resp1 = user1.create_order(build.Build());
//     ASSERT_EQ(resp1->order_link_id(), order_link_id);

//     auto result = te->PopResult();
//     ASSERT_NE(result.m_msg.get(), nullptr);

//     result = te->PopResult();
//     ASSERT_EQ(result.m_msg.get(), nullptr);
//   }

//   {
//     OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "20001", "21001");
//     auto resp1 = user1.replace_order(build.Build());
//     ASSERT_EQ(resp1->order_link_id(), order_link_id);

//     auto result = te->PopResult();
//     ASSERT_NE(result.m_msg.get(), nullptr);
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(),
//               ECrossStatus::Replaced);
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "20001");
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.002");
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_qty(), "0.002");
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_amount(), "40.002");
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].trigger_price(), "21001");
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].last_price(), "20000");
//   }

//   {
//     OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT1", "", order_link_id, "", "", "21001");
//     RpcContext ct{};
//     auto resp1 = user1.replace_order(build.Build(), ct);
//     ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170121");
//   }

//   {
//     OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "xxx", "", "21001");
//     RpcContext ct{};
//     auto resp1 = user1.replace_order(build.Build(), ct);
//     ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170130");
//   }

//   {
//     OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "", "xxx", "21001");
//     RpcContext ct{};
//     auto resp1 = user1.replace_order(build.Build(), ct);
//     ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170130");
//   }

//   {
//     OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "", "", "xxx");
//     RpcContext ct{};
//     auto resp1 = user1.replace_order(build.Build(), ct);
//     ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170130");
//   }

//   {
//     OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "", "", "");
//     RpcContext ct{};
//     auto resp1 = user1.replace_order(build.Build(), ct);
//     ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "10001");
//   }

//   {
//     OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "", "", "200000");
//     RpcContext ct{};
//     auto resp1 = user1.replace_order(build.Build(), ct);
//     ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170204");
//   }

//   {
//     OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "", "", "20000");
//     RpcContext ct{};
//     auto resp1 = user1.replace_order(build.Build(), ct);
//     ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "0");
//     auto result = te->PopResult();
//     ASSERT_NE(result.m_msg.get(), nullptr);

//     result = te->PopResult();
//     ASSERT_NE(result.m_msg.get(), nullptr);
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
//     auto trigger_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
//     ASSERT_EQ(trigger_order.order_status(), EOrderStatus::Triggered);

//     result = te->PopResult();
//     ASSERT_NE(result.m_msg.get(), nullptr);
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);

//     auto send_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
//     ASSERT_EQ(send_order.order_status(), EOrderStatus::New);
//     ASSERT_EQ(send_order.cross_status(), ECrossStatus::NewAccepted);
//   }
// }

// TEST_F(SReplaceSoTest, replace_normal_so_2) {
//   biz::user_id_t uid = 100000;
//   stub user1(te, uid);

//   te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20000));
//   te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));

//   auto order_link_id = te->GenUUID();

//   {
//     OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "21000");
//     build.SetOrderFilter("StopOrder");
//     build.SetOrderLinkID(order_link_id);
//     build.SetTriggerPrice("21000");

//     auto resp1 = user1.create_order(build.Build());
//     ASSERT_EQ(resp1->order_link_id(), order_link_id);

//     auto result = te->PopResult();
//     ASSERT_NE(result.m_msg.get(), nullptr);

//     result = te->PopResult();
//     ASSERT_EQ(result.m_msg.get(), nullptr);
//   }

//   te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20900));

//   {
//     OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "20001", "20900");
//     auto resp1 = user1.replace_order(build.Build());
//     ASSERT_EQ(resp1->order_link_id(), order_link_id);

//     auto result = te->PopResult();
//     ASSERT_NE(result.m_msg.get(), nullptr);
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(),
//               ECrossStatus::Replaced);
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "20001");
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.002");
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_qty(), "0.002");
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_amount(), "40.002");
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].trigger_price(), "20900");
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].last_price(), "20900");

//     result = te->PopResult();
//     ASSERT_NE(result.m_msg.get(), nullptr);
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
//     auto trigger_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
//     ASSERT_EQ(trigger_order.order_status(), EOrderStatus::Rejected);
//   }
// }

// TEST_F(SReplaceSoTest, replace_tpsl_so) {
//   biz::user_id_t uid = 100000;
//   stub user1(te, uid);

//   FutureDepositBuilder deposit_build("open_api_cancel_tpsl_order", uid,
//     "open_api_cancel_tpsl_order", 1, 1, "100", "0");
//   auto deposit_resp = user1.process(deposit_build.Build());
//   auto deposit_result = te->PopResult();
//   ASSERT_NE(deposit_result.m_msg.get(), nullptr);

//   auto order_link_id = te->GenUUID();

//   {
//     OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
//     build.SetOrderFilter("tpslOrder");
//     build.SetOrderLinkID(order_link_id);
//     build.SetTriggerPrice("21000");

//     auto resp1 = user1.create_order(build.Build());
//     ASSERT_EQ(resp1->order_link_id(), order_link_id);

//     auto result = te->PopResult();
//     ASSERT_NE(result.m_msg.get(), nullptr);
//   }

//   {
//     OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "20001", "21001");
//     auto resp1 = user1.replace_order(build.Build());
//     ASSERT_EQ(resp1->order_link_id(), order_link_id);

//     auto result = te->PopResult();
//     ASSERT_NE(result.m_msg.get(), nullptr);
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(),
//               ECrossStatus::Replaced);
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "20001");
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.002");
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_qty(), "0.002");
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_amount(), "40.002");
//     ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].trigger_price(), "21001");
//   }
// }

// TEST_F(SReplaceSoTest, replace_oco_so) {
//   biz::user_id_t uid = 100000;
//   stub user1(te, uid);

//   te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));

//   FutureDepositBuilder deposit_build("open_api_cancel_tpsl_order", uid, "open_api_cancel_tpsl_order", 5, 1,
//                                      "100000000000", "0");
//   auto deposit_resp = user1.process(deposit_build.Build());
//   auto deposit_result = te->PopResult();
//   ASSERT_NE(deposit_result.m_msg.get(), nullptr);

//   auto order_link_id = te->GenUUID();

//   {
//     SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "", "100", "", "", 10, "GTC", "", "0", "");
//     build.SetOrderLinkID(order_link_id);

//     build.SetTpSlOCO("18000", "22000", "", "", "Market", "Market");

//     auto resp1 = user1.create_order(build.Build());
//     ASSERT_EQ(resp1->clientorderid(), order_link_id);
//     auto result = te->PopResult();
//     ASSERT_NE(result.m_msg.get(), nullptr);
//   }

//   {
//     OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "", "", "21001");
//     RpcContext ct{};
//     auto resp1 = user1.replace_order(build.Build(), ct);
//     ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170312");
//   }
// }

// TEST_F(SReplaceSoTest, siteapi_replace_plan_order) {
//   biz::user_id_t uid = 100000;
//   stub user1(te, uid);

//   FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid,
//     "open_api_v5_create_order2", 1, 1, "10000", "0");
//   auto deposit_resp = user1.process(deposit_build.Build());
//   auto deposit_result = te->PopResult();
//   ASSERT_NE(deposit_result.m_msg.get(), nullptr);

//   FutureDepositBuilder deposit_build2("open_api_v5_create_order3", uid, "open_api_v5_create_order3", 5, 1, "10000",
//                                       "0");
//   auto deposit_resp2 = user1.process(deposit_build2.Build());
//   auto deposit_result2 = te->PopResult();
//   ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

//   // createOrder
//   auto order_link_id = te->GenUUID();
//   auto order_id = std::string("");
//   {
//     OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
//     build.SetOrderLinkID(order_link_id);
//     build.SetTriggerPrice("19800");
//     build.SetOrderFilter("tpslOrder");

//     auto resp = user1.create_order(build.Build());
//     order_id = resp->order_id();
//     ASSERT_NE(resp->order_link_id(), "");
//     auto result = te->PopResult();
//     ASSERT_NE(result.m_msg.get(), nullptr);

//     result.RefSpotMarginResult()
//         .RefRelatedOrderByOrderLinkId(order_link_id)
//         .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init)
//         .CheckLeaves("0.002", "40");
//   }

//   {
//     SpotSiteApiReplacePlanSpotOrderBuilder build("BTCUSDT", "0.003", "20010", "19900", "1", "0");
//     build.SetOrderID(order_id);
//     auto resp = user1.replace_plan_order(build.Build());
//     auto result = te->PopResult();
//     ASSERT_NE(result.m_msg.get(), nullptr);

//     result.RefSpotMarginResult()
//         .RefRelatedOrderByOrderId(order_id)
//         .CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Replaced)
//         .CheckLeaves("0.003", "60.03");
//   }

//   {
//     SpotSiteApiReplacePlanSpotOrderBuilder build("ETHUSDT", "0.003", "20010", "19900", "1", "0");
//     build.SetOrderID(order_id);
//     RpcContext ct{};
//     auto resp = user1.replace_plan_order(build.Build(), ct);
//     ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "30003");
//   }
// }

std::shared_ptr<tmock::CTradeAppMock> SReplaceSoTest::te;
