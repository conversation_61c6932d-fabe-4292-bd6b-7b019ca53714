#include "cancel_all_so.hpp"  // NOLINT

#include <string>
#include <vector>

#include "lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(SCancelAllSoTest, cancel_trigger_order_by_ids) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));

  FutureDepositBuilder deposit_build("cancel_trigger_order_by_ids", uid, "cancel_trigger_order_by_ids", 1, 1, "100",
                                     "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);
  build.SetOrderFilter("tpslOrder");
  build.SetTriggerPrice("21000");

  auto resp1 = user1.create_order(build.Build());
  auto order_id = resp1->order_id();
  ASSERT_NE("", order_id);
  ASSERT_EQ(resp1->order_link_id(), order_link_id);

  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  auto not_exist_id = te->GenUUID();

  SpotSiteApiPlanSpotCreateOrderBuilder tp_b("BTCUSDT", "Sell", "", "0.1", "", "", 10, "GTC", "", "0", "");
  auto oco_order_link_id = te->GenUUID();
  tp_b.SetOrderLinkID(oco_order_link_id);

  tp_b.SetTpSlOCO("22000", "18000", "", "", "Market", "Market");

  auto resp2 = user1.create_order(tp_b.Build());
  ASSERT_EQ(resp2->clientorderid(), oco_order_link_id);
  auto tp_oco_id = resp2->orderid();
  auto tp_result = te->PopResult();
  ASSERT_NE(tp_result.m_msg.get(), nullptr);

  {
    BatchCancelTriggerOrderByIdsRequestBuilder b2(uid, 1, std::vector<std::string>{order_id, tp_oco_id},
                                                  std::vector<std::string>{not_exist_id, order_link_id});
    auto resp = user1.batch_cancel_order(b2.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(2, resp->results_size());
    ASSERT_EQ(tp_oco_id, resp->results(0).order_id());
    ASSERT_EQ(not_exist_id, resp->results(1).client_order_id());
    ASSERT_NE(200, resp->results(0).code());
  }
}

TEST_F(SCancelAllSoTest, batch_cancel_plan_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("batch_cancel_plan_order", uid, "batch_cancel_plan_order", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);
  build.SetOrderFilter("tpslOrder");
  build.SetTriggerPrice("21000");

  auto resp1 = user1.create_order(build.Build());
  auto order_id = resp1->order_id();
  ASSERT_NE("", order_id);
  ASSERT_EQ(resp1->order_link_id(), order_link_id);

  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  auto not_exist_id = te->GenUUID();

  {
    BatchCancelPlanOrderRequestBuilder b2("", "", "BTCUSDT", "Sell", "", "", "", 1);
    auto resp = user1.batch_cancel_order(b2.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(1, resp->order_count());
    ASSERT_TRUE(resp->success());
  }
}

TEST_F(SCancelAllSoTest, batch_cancel_tpsl_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("batch_cancel_tpsl_order", uid, "batch_cancel_tpsl_order", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);
  build.SetOrderFilter("tpslOrder");
  build.SetTriggerPrice("21000");

  auto resp1 = user1.create_order(build.Build());
  auto order_id = resp1->order_id();
  ASSERT_NE("", order_id);
  ASSERT_EQ(resp1->order_link_id(), order_link_id);

  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    CancelAllOrderReqV5Builder b2("spot", "BTCUSDT", "", "", "tpslOrder");
    auto resp = user1.cancel_all(b2.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(1, resp->list_size());
    ASSERT_NE("", resp->list(0).order_id());
    ASSERT_EQ(order_link_id, resp->list(0).order_link_id());
  }
}

TEST_F(SCancelAllSoTest, batch_cancel_stop_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("batch_cancel_stop_order", uid, "batch_cancel_stop_order", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);
  build.SetOrderFilter("StopOrder");
  build.SetTriggerPrice("21000");

  auto resp1 = user1.create_order(build.Build());
  auto order_id = resp1->order_id();
  ASSERT_NE("", order_id);
  ASSERT_EQ(resp1->order_link_id(), order_link_id);

  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    CancelAllOrderReqV5Builder b2("spot", "BTCUSDT", "", "", "StopOrder");
    auto resp = user1.cancel_all(b2.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(1, resp->list_size());
    ASSERT_NE("", resp->list(0).order_id());
    ASSERT_EQ(order_link_id, resp->list(0).order_link_id());
  }
}

TEST_F(SCancelAllSoTest, cancel_order_fail) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  {
    BatchCancelPlanOrderRequestBuilder b2("", "", "BTCUSDT1", "Sell", "", "", "", 1);
    auto resp = user1.batch_cancel_order(b2.Build());
    auto result = te->PopResult();
    ASSERT_FALSE(resp->success());
  }

  {
    BatchCancelPlanOrderRequestBuilder b2("", "", "BTCUSDT", "Sell1", "", "", "", 1);
    auto resp = user1.batch_cancel_order(b2.Build());
    auto result = te->PopResult();
    ASSERT_FALSE(resp->success());
  }

  {
    BatchCancelPlanOrderRequestBuilder b2("", "", "BTCUSDT", "Sell", "LIMIT1", "", "", 1);
    auto resp = user1.batch_cancel_order(b2.Build());
    auto result = te->PopResult();
    ASSERT_FALSE(resp->success());
  }

  {
    BatchCancelPlanOrderRequestBuilder b2("", "", "BTCUSDT", "Sell", "", "AAA", "", 1);
    auto resp = user1.batch_cancel_order(b2.Build());
    auto result = te->PopResult();
    ASSERT_FALSE(resp->success());
  }

  {
    BatchCancelPlanOrderRequestBuilder b2("", "", "BTCUSDT", "Sell", "", "", "BBB", 1);
    auto resp = user1.batch_cancel_order(b2.Build());
    auto result = te->PopResult();
    ASSERT_FALSE(resp->success());
  }
}

TEST_F(SCancelAllSoTest, batch_cancel_oco_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("batch_cancel_oco_order", uid, "batch_cancel_oco_order", 5, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "", "100", "", "", 10, "GTC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    build.SetTpSlOCO("18000", "22000", "", "", "Market", "Market");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto oco_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
    ASSERT_EQ(oco_order.stop_order_type(), EStopOrderType::TakeProfitStopLossOCO);
    ASSERT_EQ(oco_order.order_status(), EOrderStatus::Untriggered);
  }

  {
    CancelAllOrderReqV5Builder b2("spot", "BTCUSDT", "", "", "OcoOrder");
    auto resp = user1.cancel_all(b2.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(1, resp->list_size());
    ASSERT_NE("", resp->list(0).order_id());
  }
}

TEST_F(SCancelAllSoTest, batch_cancel_bidirectional_tpsl_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  // 充值
  FutureDepositBuilder deposit_build("create_limit_sell_order_carry_tpsl", uid, "create_limit_sell_order_carry_tpsl", 5,
                                     1, "100000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("create_limit_sell_order_carry_tpsl2", uid, "create_limit_sell_order_carry_tpsl2",
                                      1, 1, "100000", "0");
  deposit_resp = user1.process(deposit_build2.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  auto sc = config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolName(
      "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id());

  te->UpdateQuoteData(sc->exchange_symbol_id, sc->cross_id, "20000", "0.1", false, EProductType::Spot);

  // 完全成交
  {
    // 挂卖单
    SpotSiteApiCreateAOBuilder build("BTCUSDT", "Sell", "Limit", "1", "20000", "GTC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    build.SetTpSl("18000", "22000", "17000", "", "Limit", "Market");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->client_order_id(), order_link_id);
    auto carry_tpsl_order_id = resp1->order_id();
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 挂买单完全成交
    SpotSiteApiCreateAOBuilder build2("BTCUSDT", "Buy", "Limit", "1", "20000", "GTC", "", "0", "");
    auto order_link_id2 = te->GenUUID();
    build2.SetOrderLinkID(order_link_id2);

    RpcContext ct2;
    auto resp2 = user1.create_order(build2.Build(), ct2);
    ASSERT_EQ(resp2->client_order_id(), order_link_id2);

    auto result2 = te->PopResult(2000);
    ASSERT_NE(result2.m_msg.get(), nullptr);
    ASSERT_EQ(result2.m_msg.get()->spot_margin_result().related_spot_orders().size(), 3);
    // 限价卖单完全成交，生成一笔买方向的双向止盈订单
    std::string bidirection_tpsl_order_id;
    for (auto i : result2.m_msg.get()->spot_margin_result().related_spot_orders()) {
      if (i.stop_order_type() == EStopOrderType::BidirectionalTpSl) {
        bidirection_tpsl_order_id = i.order_id();
        ASSERT_EQ(i.order_status(), EOrderStatus::Untriggered);
        ASSERT_EQ(i.tp_trigger_price(), "18000");
        ASSERT_EQ(i.tp_order_type(), EOrderType::Limit);
        ASSERT_EQ(i.sl_trigger_price(), "22000");
        ASSERT_EQ(i.sl_order_type(), EOrderType::Market);
        // ASSERT_EQ(i.parent_order_id(), carry_tpsl_order_id);
        ASSERT_EQ(i.side(), ESide::Buy);
      }
    }
  }

  {
    CancelAllOrderReqV5Builder b2("spot", "BTCUSDT", "", "", "BidirectionalTpslOrder");
    auto resp = user1.cancel_all(b2.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(1, resp->list_size());
    ASSERT_NE("", resp->list(0).order_id());
  }
}

TEST_F(SCancelAllSoTest, site_api_batch_cancel_tpsl) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  // 充值
  FutureDepositBuilder deposit_build("create_limit_sell_order_carry_tpsl", uid, "create_limit_sell_order_carry_tpsl", 5,
                                     1, "100000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("create_limit_sell_order_carry_tpsl2", uid, "create_limit_sell_order_carry_tpsl2",
                                      1, 1, "100000", "0");
  deposit_resp = user1.process(deposit_build2.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  auto sc = config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolName(
      "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id());

  te->UpdateQuoteData(sc->exchange_symbol_id, sc->cross_id, "20000", "0.1", false, EProductType::Spot);

  // 完全成交
  {
    // 挂卖单
    SpotSiteApiCreateAOBuilder build("BTCUSDT", "Sell", "Limit", "1", "20000", "GTC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    build.SetTpSl("18000", "22000", "17000", "", "Limit", "Market");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->client_order_id(), order_link_id);
    auto carry_tpsl_order_id = resp1->order_id();
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 挂买单完全成交
    SpotSiteApiCreateAOBuilder build2("BTCUSDT", "Buy", "Limit", "1", "20000", "GTC", "", "0", "");
    auto order_link_id2 = te->GenUUID();
    build2.SetOrderLinkID(order_link_id2);

    RpcContext ct2;
    auto resp2 = user1.create_order(build2.Build(), ct2);
    ASSERT_EQ(resp2->client_order_id(), order_link_id2);

    auto result2 = te->PopResult(2000);
    ASSERT_NE(result2.m_msg.get(), nullptr);
    ASSERT_EQ(result2.m_msg.get()->spot_margin_result().related_spot_orders().size(), 3);
    // 限价卖单完全成交，生成一笔买方向的双向止盈订单
    std::string bidirection_tpsl_order_id;
    for (auto i : result2.m_msg.get()->spot_margin_result().related_spot_orders()) {
      if (i.stop_order_type() == EStopOrderType::BidirectionalTpSl) {
        bidirection_tpsl_order_id = i.order_id();
        ASSERT_EQ(i.order_status(), EOrderStatus::Untriggered);
        ASSERT_EQ(i.tp_trigger_price(), "18000");
        ASSERT_EQ(i.tp_order_type(), EOrderType::Limit);
        ASSERT_EQ(i.sl_trigger_price(), "22000");
        ASSERT_EQ(i.sl_order_type(), EOrderType::Market);
        // ASSERT_EQ(i.parent_order_id(), carry_tpsl_order_id);
        ASSERT_EQ(i.side(), ESide::Buy);
      }
    }
  }

  {
    // 挂一笔止盈止损
    SpotSiteApiPlanSpotCreateOrderBuilder tp_b("BTCUSDT", "Sell", "Market", "0.1", "", "22000", 1, "GTC", "", "0", "");
    auto order_link_id = te->GenUUID();
    tp_b.SetOrderLinkID(order_link_id);
    auto resp2 = user1.create_order(tp_b.Build());
    ASSERT_EQ(resp2->clientorderid(), order_link_id);
    auto tp_result = te->PopResult();
    ASSERT_NE(tp_result.m_msg.get(), nullptr);
  }

  {
    // 模拟下单区cancel_all，两笔全部撤销
    BatchCancelPlanOrderRequestBuilder b2("", "", "BTCUSDT", "", "", "", "", 1);
    auto resp = user1.batch_cancel_order(b2.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(2, resp->order_count());
    ASSERT_TRUE(resp->success());
  }
}

TEST_F(SCancelAllSoTest, site_api_batch_cancel_tpsl_market) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  // 充值
  FutureDepositBuilder deposit_build("create_limit_sell_order_carry_tpsl", uid, "create_limit_sell_order_carry_tpsl", 5,
                                     1, "100000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("create_limit_sell_order_carry_tpsl2", uid, "create_limit_sell_order_carry_tpsl2",
                                      1, 1, "100000", "0");
  deposit_resp = user1.process(deposit_build2.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  auto sc = config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolName(
      "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id());

  te->UpdateQuoteData(sc->exchange_symbol_id, sc->cross_id, "20000", "0.1", false, EProductType::Spot);

  // 完全成交
  {
    // 挂卖单
    SpotSiteApiCreateAOBuilder build("BTCUSDT", "Sell", "Limit", "1", "20000", "GTC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    build.SetTpSl("18000", "22000", "17000", "", "Limit", "Market");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->client_order_id(), order_link_id);
    auto carry_tpsl_order_id = resp1->order_id();
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 挂买单完全成交
    SpotSiteApiCreateAOBuilder build2("BTCUSDT", "Buy", "Limit", "1", "20000", "GTC", "", "0", "");
    auto order_link_id2 = te->GenUUID();
    build2.SetOrderLinkID(order_link_id2);

    RpcContext ct2;
    auto resp2 = user1.create_order(build2.Build(), ct2);
    ASSERT_EQ(resp2->client_order_id(), order_link_id2);

    auto result2 = te->PopResult(2000);
    ASSERT_NE(result2.m_msg.get(), nullptr);
    ASSERT_EQ(result2.m_msg.get()->spot_margin_result().related_spot_orders().size(), 3);
    // 限价卖单完全成交，生成一笔买方向的双向止盈订单
    std::string bidirection_tpsl_order_id;
    for (auto i : result2.m_msg.get()->spot_margin_result().related_spot_orders()) {
      if (i.stop_order_type() == EStopOrderType::BidirectionalTpSl) {
        bidirection_tpsl_order_id = i.order_id();
        ASSERT_EQ(i.order_status(), EOrderStatus::Untriggered);
        ASSERT_EQ(i.tp_trigger_price(), "18000");
        ASSERT_EQ(i.tp_order_type(), EOrderType::Limit);
        ASSERT_EQ(i.sl_trigger_price(), "22000");
        ASSERT_EQ(i.sl_order_type(), EOrderType::Market);
        // ASSERT_EQ(i.parent_order_id(), carry_tpsl_order_id);
        ASSERT_EQ(i.side(), ESide::Buy);
      }
    }
  }

  {
    // 挂一笔止盈止损
    SpotSiteApiPlanSpotCreateOrderBuilder tp_b("BTCUSDT", "Sell", "Market", "0.1", "", "22000", 1, "GTC", "", "0", "");
    auto order_link_id = te->GenUUID();
    tp_b.SetOrderLinkID(order_link_id);
    auto resp2 = user1.create_order(tp_b.Build());
    ASSERT_EQ(resp2->clientorderid(), order_link_id);
    auto tp_result = te->PopResult();
    ASSERT_NE(tp_result.m_msg.get(), nullptr);
  }

  {
    // 模拟订单区cancel_all，只撤销tpsl
    BatchCancelPlanOrderRequestBuilder b2("", "", "BTCUSDT", "", "market", "", "", 1);
    auto resp = user1.batch_cancel_order(b2.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(1, resp->order_count());
    ASSERT_TRUE(resp->success());
  }
}

std::shared_ptr<tmock::CTradeAppMock> SCancelAllSoTest::te;
