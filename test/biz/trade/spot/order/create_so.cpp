#include "create_so.hpp"  // NOLINT

#include <string>
#include <vector>

#include "lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(SCreateSoTest, open_api_create_tpsl_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_create_tpsl_order", uid, "open_api_create_tpsl_order", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);
  build.SetOrderFilter("tpslOrder");
  build.SetTriggerPrice("21000");

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto stop_order_type = result.m_msg.get()->spot_margin_result().related_spot_orders().Get(0).stop_order_type();
  ASSERT_EQ(stop_order_type, EStopOrderType::TakeProfitStopLoss);
}
TEST_F(SCreateSoTest, open_api_create_stop_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_create_stop_order", uid, "open_api_create_stop_order", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);
  build.SetOrderFilter("StopOrder");
  build.SetTriggerPrice("21000");

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  auto stop_order_type = result.m_msg.get()->spot_margin_result().related_spot_orders().Get(0).stop_order_type();
  ASSERT_EQ(stop_order_type, EStopOrderType::Stop);
}

TEST_F(SCreateSoTest, over_order_quantity) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("site_api_create_plan_order", uid, "site_api_create_plan_order", 1, 1, "10000",
                                     "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  {
    for (auto i = 0; i < 30; i++) {
      SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.001", "20000", "21000", 0, "GTC", "",
                                                  "0", "");
      auto order_link_id = te->GenUUID();
      build.SetOrderLinkID(order_link_id);

      auto resp1 = user1.create_order(build.Build());
      ASSERT_EQ(resp1->clientorderid(), order_link_id);
    }

    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.001", "20000", "21000", 0, "GTC", "",
                                                "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), "");
  }

  {
    for (auto i = 0; i < 30; i++) {
      SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.001", "20000", "21000", 1, "GTC", "",
                                                  "0", "");
      auto order_link_id = te->GenUUID();
      build.SetOrderLinkID(order_link_id);

      auto resp1 = user1.create_order(build.Build());
      ASSERT_EQ(resp1->clientorderid(), order_link_id);
    }

    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.001", "20000", "21000", 1, "GTC", "",
                                                "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), "");
  }
}

TEST_F(SCreateSoTest, site_api_create_stop_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.001", "20000", "21000", 0, "GTC", "",
                                                "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.001", "20000", "25000", 0, "GTC", "",
                                                "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), "");
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.001", "20000", "XXXXX", 0, "GTC", "",
                                                "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), "");
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }
}

TEST_F(SCreateSoTest, site_api_create_stop_order2) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  auto sc = const_cast<config::SpotSymbolDTO*>(config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolName(
      "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id()));

  auto spot_platform_config_data = std::make_shared<config::SpotPlatformConfigDTO>();
  spot_platform_config_data->risk_time_min = bbase::decimal::Decimal{5};
  config::getTlsCfgMgrRaw()->spot_client_sptr()->set_spot_platform_config_data(spot_platform_config_data);

  {
    sc->online_time_ms = bbase::utils::Time::GetTimeMs() + (10 * 60 * 1000);
    spot_platform_config_data->risk_time_min = bbase::decimal::Decimal{5};

    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.001", "20000", "21000", 0, "GTC", "",
                                                "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), "");
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);

    sc->online_time_ms = 0;
    spot_platform_config_data->risk_time_min = bbase::decimal::Decimal{0};
  }

  {
    sc->online_time_ms = bbase::utils::Time::GetTimeMs() + (10 * 60 * 1000);
    spot_platform_config_data->risk_time_min = bbase::decimal::Decimal{5};

    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.001", "20000", "21000", 1, "GTC", "",
                                                "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), "");
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);

    sc->online_time_ms = 0;
    spot_platform_config_data->risk_time_min = bbase::decimal::Decimal{0};
  }

  {
    sc->allow_plan = 0;

    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.001", "20000", "21000", 0, "GTC", "",
                                                "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), "");
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);

    sc->allow_plan = 1;
  }
}

TEST_F(SCreateSoTest, site_api_create_tpsl_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("site_api_create_tpsl_order", uid, "site_api_create_tpsl_order", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.001", "20000", "21000", 1, "GTC", "", "0",
                                              "");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->clientorderid(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
}

TEST_F(SCreateSoTest, open_api_create_so_miss_trigger_price) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_create_so_miss_trigger_price", uid,
                                     "open_api_create_so_miss_trigger_price", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);
  build.SetOrderFilter("tpslOrder");
  //  build.SetTriggerPrice("21000");

  RpcContext ct{};
  auto resp1 = user1.create_order(build.Build(), ct);
  ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170202");
}

TEST_F(SCreateSoTest, site_api_create_oco_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));

  FutureDepositBuilder deposit_build("site_api_create_oco_order", uid, "site_api_create_oco_order", 5, 1,
                                     "************", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "", "100", "", "", 10, "GTC", "", "0", "");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  build.SetTpSlOCO("18000", "22000", "", "", "Market", "Market");

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->clientorderid(), order_link_id);
  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  auto oco_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
  ASSERT_EQ(oco_order.stop_order_type(), EStopOrderType::TakeProfitStopLossOCO);
  ASSERT_EQ(oco_order.order_status(), EOrderStatus::Untriggered);

  {
    MergeBatchCancelOrderRequestBuilder b2("BTCUSDT", "", "", "", "");
    auto resp3 = user1.batch_cancel_order(b2.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(1, resp3->order_count());
    ASSERT_TRUE(resp3->success());
  }
}

TEST_F(SCreateSoTest, create_oco_order_validate_quote_price) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));

  FutureDepositBuilder deposit_build("create_oco_order_validate_quote_price", uid,
                                     "create_oco_order_validate_quote_price", 5, 1, "************", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "", "100", "", "", 10, "GTC", "", "0", "");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  build.SetTpSlOCO("20000", "22000", "", "", "Market", "Market");

  RpcContext ct{};
  auto resp1 = user1.create_order(build.Build(), ct);
  ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "32181");
}

TEST_F(SCreateSoTest, create_oco_limit_buy_invalid_qty) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));

  FutureDepositBuilder deposit_build("create_oco_limit_buy_invalid_qty", uid, "create_oco_limit_buy_invalid_qty", 5, 1,
                                     "************", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "", "1.2", "", "", 10, "GTC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    build.SetTpSlOCO("18000", "22000", "18500", "21500", "Limit", "Limit");

    RpcContext ct{};
    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "30003");
  }

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "", "1.2", "", "", 10, "GTC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    build.SetTpSlOCO("18000", "22000", "", "", "Market", "Market");

    RpcContext ct{};
    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "0");
  }
}

// NaN 在大整数中不支持.
// TEST_F(SCreateSoTest, buy_bankruptcy_price_v2) {
//   auto last_price = biz::BigDecimal("2000");
//   auto amount = biz::BigDecimal("0");
//   auto total_haircut = biz::BigDecimal("0");
//   auto account_ab = biz::BigDecimal("0");
//   auto extra_haircut = biz::BigDecimal("0");
//   auto extra_spot_mm = biz::BigDecimal("0");
//   auto base_coin_index = biz::BigDecimal("1599.********");
//
//   auto slippage_z = account_ab.Sub(total_haircut.Add(extra_haircut).Max(decimal::kZero)).Sub(extra_spot_mm);
//   auto amt_index_qty = amount.Div(last_price).RoundDown(18);
//   auto slippage_index_qty = slippage_z.Div(base_coin_index).RoundDown(18);
//
//   auto bankruptcyPrice = amount.Div(amt_index_qty.Sub(slippage_index_qty)).RoundDown(18);
//   if (bankruptcyPrice <= decimal::kZero) {
//     bankruptcyPrice = decimal::kMaxAmount;
//   }
//   /** min(bp, MAX BUY Price), which means user won't get bankrupt even lose all amount */
//   bankruptcyPrice = bankruptcyPrice.Min(decimal::kMaxAmount);
//   std::cout << "=======" << std::endl;
//   std::cout << bankruptcyPrice.StringFixed(18, true) << std::endl;
// }

TEST_F(SCreateSoTest, create_order_req_expired) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));

  FutureDepositBuilder deposit_build("create_oco_order_validata_quote_price", uid,
                                     "create_oco_order_validata_quote_price", 5, 1, "************", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "", "100", "", "", 10, "GTC", "", "0", "");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  build.SetTpSlOCO("20000", "22000", "", "", "Market", "Market");

  RpcContext ct{};
  nlohmann::json j;
  auto nowNs = bbase::utils::Time::GetTimeNs() - static_cast<int64_t>(60 * 1e9);
  j["expe9"] = nowNs;
  ct.ct.AddMetadata("extension", j.dump());
  auto resp1 = user1.create_order(build.Build(), ct);
  ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "-1");
}

TEST_F(SCreateSoTest, creat_oco_order_over_max_num) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));

  FutureDepositBuilder deposit_build("create_oco_order_validata_quote_price", uid,
                                     "create_oco_order_validata_quote_price", 5, 1, "************", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  {
    for (auto i = 0; i < 30; i++) {
      SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "", "100", "", "", 10, "GTC", "", "0", "");
      auto order_link_id = te->GenUUID();
      build.SetOrderLinkID(order_link_id);

      build.SetTpSlOCO("18000", "22000", "", "", "Market", "Market");
      auto resp1 = user1.create_order(build.Build());
      ASSERT_EQ(resp1->clientorderid(), order_link_id);
    }
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "", "100", "", "", 10, "GTC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    build.SetTpSlOCO("18000", "22000", "", "", "Market", "Market");
    RpcContext ct{};
    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "32401");
  }
}

TEST_F(SCreateSoTest, create_oco_order_limit_price_invalid) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));

  FutureDepositBuilder deposit_build("create_oco_order_validate_quote_price", uid,
                                     "create_oco_order_validate_quote_price", 5, 1, "************", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "", "100", "", "", 10, "GTC", "", "0", "");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  build.SetTpSlOCO("18000", "22000", "30000", "", "Limit", "Market");

  RpcContext ct{};
  auto resp1 = user1.create_order(build.Build(), ct);
  ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "32230");
}

TEST_F(SCreateSoTest, create_buy_trailing_stop) {
  biz::user_id_t uid = 10000;
  stub user1(te, uid);

  {
    FutureDepositBuilder deposit_build("open_api_create_tpsl_order", uid, "open_api_create_tpsl_order", 5, 1, "100",
                                       "0");
    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20000));

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "Market", "10", "", "", 4, "IOC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    build.SetTrailingInfo("", "10", "");
    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1.get()->clientorderid(), order_link_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).stop_order_type(),
              EStopOrderType::TrailingStop);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "20000");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "20010");
  }

  // 拉低价格，更新base和trigger_price
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(19000));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(19000));

  {
    usleep(10000);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "19000");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "19010");
  }

  // 抬高价格，不触发
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(19009));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(19009));
  {
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // 抬高价格，触发
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(19011));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(19011));

  {
    usleep(10000);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).order_status(),
              EOrderStatus::Triggered);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).order_status(),
              EOrderStatus::Cancelled);
  }

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "Market", "10", "", "", 4, "IOC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    build.SetTrailingInfo("20000", "10", "");
    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1.get()->clientorderid(), order_link_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).stop_order_type(),
              EStopOrderType::TrailingProfit);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "20000");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "20000");
  }

  // 抬高价格，不激活
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(19900));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(19900));
  {
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // 抬高价格，激活
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20100));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20100));
  {
    usleep(10000);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).stop_order_type(),
              EStopOrderType::TrailingStop);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).order_status(),
              EOrderStatus::Untriggered);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "20100");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "20110");
  }

  // 拉低价格，更新base和trigger_price
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(19000));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(19000));

  {
    usleep(10000);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "19000");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "19010");
  }

  // 抬高价格，不触发
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(19009));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(19009));
  {
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // 抬高价格，触发
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(19110));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(19110));

  {
    usleep(10000);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).order_status(),
              EOrderStatus::Triggered);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).order_status(),
              EOrderStatus::Cancelled);
  }
}

TEST_F(SCreateSoTest, create_sell_trailing_stop) {
  biz::user_id_t uid = 10000;
  stub user1(te, uid);

  {
    FutureDepositBuilder deposit_build("open_api_create_tpsl_order", uid, "open_api_create_tpsl_order", 1, 1, "100",
                                       "0");
    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20000));

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Market", "1", "", "", 4, "IOC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    build.SetTrailingInfo("", "10", "");
    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1.get()->clientorderid(), order_link_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).stop_order_type(),
              EStopOrderType::TrailingStop);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "20000");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "19990");
  }

  // 拉低价格，更新base和trigger_price
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(21000));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(21000));

  {
    usleep(10000);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "21000");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "20990");
  }

  // 拉低价格，不触发
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20991));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20991));
  {
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // 拉低价格，触发
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20990));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20990));

  {
    usleep(10000);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).order_status(),
              EOrderStatus::Triggered);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).order_status(),
              EOrderStatus::Cancelled);
  }

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Market", "1", "", "", 4, "IOC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    build.SetTrailingInfo("20000", "10", "");
    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1.get()->clientorderid(), order_link_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).stop_order_type(),
              EStopOrderType::TrailingProfit);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "20000");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "20000");
  }

  // 拉低价格，不激活
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20900));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20900));
  {
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // 拉低价格，激活
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20000));
  {
    usleep(10000);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).stop_order_type(),
              EStopOrderType::TrailingStop);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).order_status(),
              EOrderStatus::Untriggered);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "20000");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "19990");
  }

  // 抬高价格，更新base和trigger_price
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(21000));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(21000));

  {
    usleep(10000);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "21000");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "20990");
  }

  // 拉低价格，不触发
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20991));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20991));
  {
    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // 抬高价格，触发
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20990));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20990));

  {
    usleep(10000);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).order_status(),
              EOrderStatus::Triggered);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).order_status(),
              EOrderStatus::Cancelled);
  }
}

TEST_F(SCreateSoTest, replace_trailing_stop) {
  biz::user_id_t uid = 10000;
  stub user1(te, uid);

  {
    FutureDepositBuilder deposit_build("open_api_create_tpsl_order", uid, "open_api_create_tpsl_order", 1, 1, "100",
                                       "0");
    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20000));

  auto order_link_id = te->GenUUID();
  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Market", "1", "", "", 4, "IOC", "", "0", "");
    build.SetOrderLinkID(order_link_id);

    build.SetTrailingInfo("", "10", "");
    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1.get()->clientorderid(), order_link_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).stop_order_type(),
              EStopOrderType::TrailingStop);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "20000");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "19990");
  }

  {
    SpotSiteApiReplacePlanSpotOrderBuilder build("BTCUSDT", "", "", "", "4", "0");
    build.SetOrderLinkID(order_link_id);
    build.SetTrailingInfo("", "20", "");
    auto resp = user1.replace_plan_order(build.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).stop_order_type(),
              EStopOrderType::TrailingStop);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "20000");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "19980");
  }

  {
    SpotSiteApiReplacePlanSpotOrderBuilder build("BTCUSDT", "", "", "1234", "4", "0");
    build.SetOrderLinkID(order_link_id);
    RpcContext ct{};
    auto resp = user1.replace_plan_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "30003");
  }

  {
    SpotSiteApiReplacePlanSpotOrderBuilder build("BTCUSDT", "", "", "", "4", "0");
    build.SetOrderLinkID(order_link_id);
    build.SetTrailingInfo("19000", "20", "");
    RpcContext ct{};
    auto resp = user1.replace_plan_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "30003");
  }

  {
    SpotSiteApiReplacePlanSpotOrderBuilder build("BTCUSDT", "", "", "", "4", "0");
    build.SetOrderLinkID(order_link_id);
    build.SetTrailingInfo("", "20", "20");
    RpcContext ct{};
    auto resp = user1.replace_plan_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "30003");
  }

  {
    SpotSiteApiReplacePlanSpotOrderBuilder build("BTCUSDT", "", "", "", "4", "0");
    build.SetOrderLinkID(order_link_id);
    build.SetTrailingInfo("", "", "20");
    RpcContext ct{};
    auto resp = user1.replace_plan_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "30003");
  }

  {
    SpotSiteApiReplacePlanSpotOrderBuilder build("BTCUSDT", "", "", "", "4", "0");
    build.SetOrderLinkID(order_link_id);
    build.SetTrailingInfo("asf", "", "");
    RpcContext ct{};
    auto resp = user1.replace_plan_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "30003");
  }

  {
    SpotSiteApiReplacePlanSpotOrderBuilder build("BTCUSDT", "", "", "", "4", "0");
    build.SetOrderLinkID(order_link_id);
    build.SetTrailingInfo("", "asdf", "");
    RpcContext ct{};
    auto resp = user1.replace_plan_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "30003");
  }

  {
    SpotSiteApiReplacePlanSpotOrderBuilder build("BTCUSDT", "", "", "", "4", "0");
    build.SetOrderLinkID(order_link_id);
    build.SetTrailingInfo("", "", "asdf");
    RpcContext ct{};
    auto resp = user1.replace_plan_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "30003");
  }

  order_link_id = te->GenUUID();
  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Market", "1", "", "", 4, "IOC", "", "0", "");
    build.SetOrderLinkID(order_link_id);

    build.SetTrailingInfo("19000", "", "10");
    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1.get()->clientorderid(), order_link_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).stop_order_type(),
              EStopOrderType::TrailingProfit);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "19000");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "19000");
  }

  {
    SpotSiteApiReplacePlanSpotOrderBuilder build("BTCUSDT", "", "", "", "4", "0");
    build.SetOrderLinkID(order_link_id);
    build.SetTrailingInfo("", "", "20");
    auto resp = user1.replace_plan_order(build.Build());

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).stop_order_type(),
              EStopOrderType::TrailingProfit);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "19000");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "19000");
  }

  {
    SpotSiteApiReplacePlanSpotOrderBuilder build("BTCUSDT", "", "", "", "4", "0");
    build.SetOrderLinkID(order_link_id);
    build.SetTrailingInfo("0", "", "");
    auto resp = user1.replace_plan_order(build.Build());

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).stop_order_type(),
              EStopOrderType::TrailingStop);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "20000");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "16000");
  }
}

TEST_F(SCreateSoTest, site_api_create_stop_order_market_unit) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
  {
    // 市价卖金额
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Market", "100000", "20000", "21000", 0, "GTC", "",
                                                "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    // 卖价值100000USTD的BTC
    build.SetMarketUnit("QuoteCoin");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto stop_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
    ASSERT_EQ(stop_order.order_type(), EOrderType::Market);
    ASSERT_EQ(stop_order.order_type(), EStopOrderType::Stop);

    FutureDepositBuilder deposit_build("site_api_create_stop_order_market_unit_sell_amount", uid,
                                       "site_api_create_stop_order_market_unit_sell_amount", ECoin::BTC, 1, "100", "0");

    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);

    /*
    // 拉高价格，更新base和trigger_price
    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(21000));
    te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(21000));

    {
      usleep(10000);
      result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
      // ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).base_price(), "21000");
      // ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "20990");
    }
    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
    te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20000));
    */
  }

  {
    // 市价数量卖
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Market", "1", "20000", "21000", 0, "GTC", "", "0",
                                                "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetMarketUnit("BaseCoin");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto stop_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
    ASSERT_EQ(stop_order.order_type(), EOrderType::Market);
    ASSERT_EQ(stop_order.order_type(), EStopOrderType::Stop);

    FutureDepositBuilder deposit_build("site_api_create_stop_order_market_unit_sell_amount_basecoin", uid,
                                       "site_api_create_stop_order_market_unit_sell_amount_basecoin", ECoin::BTC, 1,
                                       "1", "0");

    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  {
    // 市价买数量
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "Market", "0.001", "20000", "21000", 0, "GTC", "",
                                                "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    // 买0.01个BTC
    build.SetMarketUnit("BaseCoin");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto stop_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
    ASSERT_EQ(stop_order.order_type(), EOrderType::Market);
    ASSERT_EQ(stop_order.order_type(), EStopOrderType::Stop);

    FutureDepositBuilder deposit_build("site_api_create_stop_order_market_unit_buy_qty", uid,
                                       "site_api_create_stop_order_market_unit_buy_qty", ECoin::USDT, 1, "10000000",
                                       "0");

    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  {
    // 市价金额买
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "Market", "100000", "20000", "21000", 0, "GTC", "",
                                                "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    // 买0.01个BTC
    build.SetMarketUnit("QuoteCoin");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto stop_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
    ASSERT_EQ(stop_order.order_type(), EOrderType::Market);
    ASSERT_EQ(stop_order.order_type(), EStopOrderType::Stop);

    FutureDepositBuilder deposit_build("site_api_create_stop_order_market_unit_buy_qty_quote", uid,
                                       "site_api_create_stop_order_market_unit_buy_qty_quote", ECoin::USDT, 1,
                                       "10000000", "0");

    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  {
    // 市价卖数量
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Market", "10", "20000", "21000", 0, "GTC", "", "0",
                                                "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto stop_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
    ASSERT_EQ(stop_order.order_type(), EOrderType::Market);
    ASSERT_EQ(stop_order.order_type(), EStopOrderType::Stop);

    FutureDepositBuilder deposit_build("site_api_create_stop_order_market_unit_sell_amount", uid,
                                       "site_api_create_stop_order_market_unit_sell_amount", ECoin::BTC, 1, "100", "0");

    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }
  {
    // 市价买金额
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "Market", "100000", "20000", "21000", 0, "GTC", "",
                                                "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto stop_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
    ASSERT_EQ(stop_order.order_type(), EOrderType::Market);
    ASSERT_EQ(stop_order.order_type(), EStopOrderType::Stop);

    FutureDepositBuilder deposit_build("site_api_create_stop_order_market_unit_buy_qty", uid,
                                       "site_api_create_stop_order_market_unit_buy_qty", ECoin::USDT, 1, "10000000",
                                       "0");

    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }
}

TEST_F(SCreateSoTest, site_api_create_tpsl_order_market_unit_buy_baseCoin) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
  // 测试止盈止损市价数量买能通过

  FutureDepositBuilder deposit_build("site_api_create_tpsl_order_market_unit_buy", uid,
                                     "site_api_create_tpsl_order_market_unit_buy", 5, 1, "************", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "Market", "10", "", "21000", 1, "GTC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetMarketUnit("BaseCoin");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), order_link_id);
    auto result = te->PopResult();
    // usleep(3000);

    ASSERT_NE(result.m_msg.get(), nullptr);
    auto oco_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
    ASSERT_EQ(oco_order.stop_order_type(), EStopOrderType::TakeProfitStopLoss);
    ASSERT_EQ(oco_order.order_status(), EOrderStatus::Untriggered);
    // 发送margin result时ToPB::ConvertToSpotTransactDTO会将market_unit不为unknown的单子变为市价单
    ASSERT_EQ(oco_order.order_type(), EOrderType::Market);
  }

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "Market", "1000", "", "21000", 1, "GTC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetMarketUnit("QuoteCoin");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), order_link_id);
    auto result = te->PopResult();

    ASSERT_NE(result.m_msg.get(), nullptr);
    auto oco_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
    ASSERT_EQ(oco_order.stop_order_type(), EStopOrderType::TakeProfitStopLoss);
    ASSERT_EQ(oco_order.order_status(), EOrderStatus::Untriggered);
    // 发送margin result时ToPB::ConvertToSpotTransactDTO会将market_unit不为unknown的单子变为市价单
    ASSERT_EQ(oco_order.order_type(), EOrderType::Market);
  }
}

TEST_F(SCreateSoTest, site_api_create_tpsl_order_market_unit_sell_quoteCoin) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(21000));
  // 测试止盈止损市价卖计价货币数量
  FutureDepositBuilder deposit_build("site_api_create_tpsl_order_market_unit_sell", uid,
                                     "site_api_create_tpsl_order_market_unit_sell", ECoin::BTC, 1, "1000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Market", "1000000", "", "21000", 1, "GTC", "", "0",
                                                "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetMarketUnit("QuoteCoin");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), order_link_id);
    ASSERT_NE(resp1->orderid(), "0");
    auto result = te->PopResult();

    ASSERT_NE(result.m_msg.get(), nullptr);
    auto oco_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
    ASSERT_EQ(oco_order.stop_order_type(), EStopOrderType::TakeProfitStopLoss);
    // ASSERT_EQ(oco_order.order_status(), EOrderStatus::Untriggered);
    ASSERT_EQ(oco_order.order_type(), EOrderType::Market);
  }

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Market", "1", "", "21000", 1, "GTC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetMarketUnit("BaseCoin");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), order_link_id);
    ASSERT_NE(resp1->orderid(), "0");
    auto result = te->PopResult();

    ASSERT_NE(result.m_msg.get(), nullptr);
    auto oco_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
    ASSERT_EQ(oco_order.stop_order_type(), EStopOrderType::TakeProfitStopLoss);
    // ASSERT_EQ(oco_order.order_status(), EOrderStatus::Untriggered);
    ASSERT_EQ(oco_order.order_type(), EOrderType::Market);
  }
}

TEST_F(SCreateSoTest, site_api_create_tpsl_order_market_unit_buy_quoteCoin) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));

  FutureDepositBuilder deposit_build("site_api_create_tpsl_order_market_unit_buy", uid,
                                     "site_api_create_tpsl_order_market_unit_buy", 5, 1, "************", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "Market", "100000", "", "21000", 1, "GTC", "", "0", "");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->clientorderid(), order_link_id);
  auto result = te->PopResult();

  ASSERT_NE(result.m_msg.get(), nullptr);
  auto oco_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
  ASSERT_EQ(oco_order.stop_order_type(), EStopOrderType::TakeProfitStopLoss);
  // ASSERT_EQ(oco_order.order_status(), EOrderStatus::Untriggered);
  //  发送margin result时ToPB::ConvertToSpotTransactDTO会将market_unit不为unknown的单子变为市价单
  ASSERT_EQ(oco_order.order_type(), EOrderType::Market);
}

TEST_F(SCreateSoTest, site_api_create_tpsl_order_market_unit_sell_baseCoin) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(21000));
  FutureDepositBuilder deposit_build("site_api_create_tpsl_order_market_unit_sell", uid,
                                     "site_api_create_tpsl_order_market_unit_sell", ECoin::BTC, 1, "1000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Market", "10", "", "21000", 1, "GTC", "", "0", "");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->clientorderid(), order_link_id);
  ASSERT_NE(resp1->orderid(), "0");
  auto result = te->PopResult();

  ASSERT_NE(result.m_msg.get(), nullptr);
  auto oco_order = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
  ASSERT_EQ(oco_order.stop_order_type(), EStopOrderType::TakeProfitStopLoss);
  // ASSERT_EQ(oco_order.order_status(), EOrderStatus::Untriggered);
  ASSERT_EQ(oco_order.order_type(), EOrderType::Market);
}

TEST_F(SCreateSoTest, pre_create_stop_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("deposit_btc", uid, "deposit_btc", ECoin::BTC, 1, "1", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("deposit_usdt", uid, "deposit_usdt", ECoin::USDT, 1, "10000", "0");
  deposit_resp = user1.process(deposit_build2.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Market", "0.6", "", "20000", 1, "IOC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.pre_create_order(build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto resp2 = user1.create_order(build.Build());
    ASSERT_EQ(resp2->clientorderid(), order_link_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp3 = user1.pre_create_order(build.Build());
    ASSERT_EQ(resp3->ret_code(), 33001);
  }
}

TEST_F(SCreateSoTest, site_api_create_stop_order_with_try_regulated_config) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  auto sc =
      const_cast<config::SpotSymbolDTO*>(config::ConfigProxy::Instance().config_mgr()->spot_client()->QueryBySymbolName(
          "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id()));
  // 初始化TRYUSDT币对
  sc->base_coin_name = "TRY";
  sc->settle_coin_name = "USDT";

  // 初始化TRY币对拦截配置
  auto spot_regulated_token_config_data = std::make_shared<config::SpotRegulatedTokenConfigData>();
  config::ConfigProxy::Instance().config_mgr()->spot_client_sptr()->set_spot_regulated_token_config_data(
      spot_regulated_token_config_data);
  spot_regulated_token_config_data->data.emplace("TRY", std::vector<std::string>{"TUR"});

  // 土耳其站点下条件单TRY币对成功
  {
    RpcContext ct{};
    ct.ct.AddMetadata("x-refer-site-id", "TUR");

    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.001", "20000", "21000", 0, "GTC", "",
                                                "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "0");
    ASSERT_EQ(resp1->clientorderid(), order_link_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  // 主战站点下条件单TRY币对失败
  {
    RpcContext ct{};
    ct.ct.AddMetadata("x-refer-site-id", "");

    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.001", "20000", "21000", 0, "GTC", "",
                                                "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "32241");
    ASSERT_EQ(resp1->clientorderid(), "");

    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // 充值
  FutureDepositBuilder deposit_build("create_limit_sell_order_carry_tpsl", uid, "create_limit_sell_order_carry_tpsl", 5,
                                     1, "100000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("create_limit_sell_order_carry_tpsl2", uid, "create_limit_sell_order_carry_tpsl2",
                                      1, 1, "100000", "0");
  deposit_resp = user1.process(deposit_build2.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->UpdateQuoteData(sc->exchange_symbol_id, sc->cross_id, "20000", "0.1", false, EProductType::Spot);

  // TRY站点下TPSL单TRY币对成功
  {
    RpcContext ct{};
    ct.ct.AddMetadata("x-refer-site-id", "TUR");

    // 挂卖单
    SpotSiteApiCreateAOBuilder build("BTCUSDT", "Sell", "Limit", "1", "20000", "GTC", "", "0", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);

    build.SetTpSl("18000", "22000", "17000", "", "Limit", "Market");

    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(resp1->client_order_id(), order_link_id);
    auto carry_tpsl_order_id = resp1->order_id();
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    // 挂买单完全成交
    SpotSiteApiCreateAOBuilder build2("BTCUSDT", "Buy", "Limit", "1", "20000", "GTC", "", "0", "");
    auto order_link_id2 = te->GenUUID();
    build2.SetOrderLinkID(order_link_id2);

    RpcContext ct2;
    ct2.ct.AddMetadata("x-refer-site-id", "TUR");
    auto resp2 = user1.create_order(build2.Build(), ct2);
    ASSERT_EQ(resp2->client_order_id(), order_link_id2);

    auto result2 = te->PopResult(2000);
    ASSERT_NE(result2.m_msg.get(), nullptr);
    ASSERT_EQ(result2.m_msg.get()->spot_margin_result().related_spot_orders().size(), 3);
    // 限价卖单完全成交，生成一笔买方向的双向止盈订单
    std::string bidirection_tpsl_order_id;
    for (auto i : result2.m_msg.get()->spot_margin_result().related_spot_orders()) {
      if (i.stop_order_type() == EStopOrderType::BidirectionalTpSl) {
        bidirection_tpsl_order_id = i.order_id();
        ASSERT_EQ(i.order_status(), EOrderStatus::Untriggered);
        ASSERT_EQ(i.tp_trigger_price(), "18000");
        ASSERT_EQ(i.tp_order_type(), EOrderType::Limit);
        ASSERT_EQ(i.sl_trigger_price(), "22000");
        ASSERT_EQ(i.sl_order_type(), EOrderType::Market);
        // ASSERT_EQ(i.parent_order_id(), carry_tpsl_order_id);
        ASSERT_EQ(i.side(), ESide::Buy);
        ASSERT_NE(i.order_link_id(), "");
      }
    }
  }
}

static constexpr const char* kSpotSymbolContentForStopOrderApiUser =
    R"({"data":[{"allowBargain":0,"allowPlan":1,"allowTrade":1,"banBuyStatus":0,"banSellStatus":0,"baseCoin":"BTC","baseCoinId":1,
    "baseCoinName":"BTC","baseCoinType":1,"basePrecision":8,"brokerId":9001,"crossId":10000,"direction":"","etpNavDeviation":"0","etpRiskSwitchStatus":0,
    "exchangeSymbolId":1,"forbidOpenapiTrade":0,"inleverage":0E-18,"isTest":0,"limitOrderPriceLimitPercentage":"************00000","makerBuyFee":"0.00100000",
    "makerSellFee":"0.00100000","marginLoanOpen":1,"marketOrderPriceLimitPercentage":"************00000","minPricePrecision":"0.000000000001","needPreviewCheck":0,
    "openPrice":20000,"openTime":1679973300000,"settleCoin":"USDT","settleCoinId":5,"settleCoinName":"USDT","settleCoinType":1,
    "settlePrecision":2,"showStatus":1,"showStatusOpenTime":1679973300000,"status":3,"symbolFullName":"BTCUSDT","symbolId":10,"symbolName":"BTCUSDT",
    "symbolType":1,"takerBuyFee":"0.00100000","takerSellFee":"0.00100000", "maxTradeQuantity":"100", "minTradeQuantity":"0.01", "maxTradeAmount":"1000000",
    "minTradeAmount":"5.0","minTradeAmountApi":"10.0"}],"version":"f56ff996-a578-4f2f-9050-95f1b59465cf"})";
static constexpr const char* kSpotSymbolContentForStopOrderApiUser1 =
    R"({"data":[{"allowBargain":0,"allowPlan":1,"allowTrade":1,"banBuyStatus":0,"banSellStatus":0,"baseCoin":"BTC","baseCoinId":1,
    "baseCoinName":"BTC","baseCoinType":1,"basePrecision":8,"brokerId":9001,"crossId":10000,"direction":"","etpNavDeviation":"0","etpRiskSwitchStatus":0,
    "exchangeSymbolId":1,"forbidOpenapiTrade":0,"inleverage":0E-18,"isTest":0,"limitOrderPriceLimitPercentage":"************00000","makerBuyFee":"0.00100000",
    "makerSellFee":"0.00100000","marginLoanOpen":1,"marketOrderPriceLimitPercentage":"************00000","minPricePrecision":"0.000000000001","needPreviewCheck":0,
    "openPrice":20000,"openTime":1679973300000,"settleCoin":"USDT","settleCoinId":5,"settleCoinName":"USDT","settleCoinType":1,
    "settlePrecision":2,"showStatus":1,"showStatusOpenTime":1679973300000,"status":3,"symbolFullName":"BTCUSDT","symbolId":10,"symbolName":"BTCUSDT",
    "symbolType":1,"takerBuyFee":"0.00100000","takerSellFee":"0.00100000", "maxTradeQuantity":"100", "minTradeQuantity":"0.000001", "maxTradeAmount":"1000000",
    "minTradeAmount":"15.0","minTradeAmountApi":"20.0"}],"version":"f56ff996-a578-4f2f-9050-95f1b59465cf"})";
// 测试针对API用户的最小成交额参数校验
TEST_F(SCreateSoTest, stop_order_check_min_trade_amount) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order", uid, "open_api_v5_create_order", 5, 1, "1000000000",
                                     "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000",
                                      "0");
  deposit_resp = user1.process(deposit_build2.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  auto order_link_id = te->GenUUID();
  std::string order_id;

  // 测试OCO条件单 买 止盈限价单 下单金额 低于 minTradeQuantity
  {
    ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContentForStopOrderApiUser));
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "Limit", "15.0", "", "", 10, "GTC", "0", "0", "");
    order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetTpSlOCO("15000", "25000", "15000", "25000", "Limit", "Limit");
    RpcContext ct{};
    ct.ct.AddMetadata("user-site-id", "BYBIT");
    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "32106");
  }
  // 测试OCO条件单 卖 止损限价单 下单金额 低于 minTradeAmount
  {
    ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContentForStopOrderApiUser1));
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.001", "", "", 10, "GTC", "0", "0", "");
    order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetTpSlOCO("25000", "14000", "25000", "14000", "Limit", "Limit");
    RpcContext ct{};
    ct.ct.AddMetadata("user-site-id", "BYBIT");
    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "32109");
  }
  // 测试OCO条件单(API只能下条件单和止盈止损单) 买止盈价大于minTradeAmount 正常发单
  {
    ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContentForStopOrderApiUser));
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "Limit", "250.0", "", "", 10, "GTC", "0", "0", "");
    order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetTpSlOCO("15000", "25000", "15000", "25000", "Limit", "Limit");
    RpcContext ct{};
    ct.ct.AddMetadata("user-site-id", "BYBIT");
    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "0");
  }
  // 测试OCO条件单(API只能下条件单和止盈止损单) 卖止损价大于minTradeAmount 正常发单
  {
    ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContentForStopOrderApiUser1));
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Sell", "Limit", "0.01", "", "", 10, "GTC", "0", "0", "");
    order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetTpSlOCO("25000", "15000", "25000", "15000", "Limit", "Limit");
    RpcContext ct{};
    ct.ct.AddMetadata("user-site-id", "BYBIT");
    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "0");
  }
  // 测试API下stoporder单 不满足最小校验金额
  {
    ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContentForStopOrderApiUser1));
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.0004", "20000");
    order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetOrderFilter("StopOrder");
    build.SetTriggerPrice("21000");
    RpcContext ct{};
    ct.ct.AddMetadata("user-site-id", "BYBIT");
    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), "");
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170140");
  }
  // 测试API下止盈止损单 不满足最小校验金额
  {
    ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContentForStopOrderApiUser1));
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.0004", "20000");
    order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetOrderFilter("tpslOrder");
    build.SetTriggerPrice("21000");
    RpcContext ct{};
    ct.ct.AddMetadata("user-site-id", "BYBIT");
    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), "");
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170140");
  }
}
TEST_F(SCreateSoTest, rpi_stop_order_trigger) {
  auto kSpotCopperConfig =
      R"({"data":[{"uids":[12345678,1002],"symbolIds":[1,279] }, {"uids":[1003,1004],"symbolIds": [279,278] }]})";
  config::ConfigProxy::Instance().SpotRLPReceiveHandler(kSpotCopperConfig);
  te->waitRecvHandler();
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));

  //  12345678 RPI 用户
  biz::user_id_t uid = 12345678;
  stub user1(te, uid);
  FutureDepositBuilder deposit_build("create_order_by_rpi", uid, "create_order_by_rpi", ECoin::BTC, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  // 22345678 非RPI 用户
  biz::user_id_t uid2 = 22345678;
  stub user2(te, uid2);
  FutureDepositBuilder deposit_build3("create_order_by_rpi_3", uid2, "create_order_by_rpi_3", ECoin::USDT, 1, "10000",
                                      "0");
  auto deposit_resp3 = user2.process(deposit_build3.Build());
  auto deposit_result3 = te->PopResult();
  ASSERT_NE(deposit_result3.m_msg.get(), nullptr);

  // 构造OB order1.下RPI订单成功
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.02", "20000");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetTimeInForce("RPI");
    RpcContext ct;
    ct.ct.AddMetadata("parent_uid", "12345678");

    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result2 = te->PopResult();
    ASSERT_EQ(result2.m_msg.get()->spot_margin_result().related_spot_orders(0).forbid_match_rpi(), 1);
    ASSERT_EQ(result2.m_msg.get()->spot_margin_result().related_spot_orders(0).order_link_id(), order_link_id);
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20000));

  // 通过API下条件单不能成交
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.001", "20000");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetTriggerPrice("19990");
    build.SetOrderFilter("StopOrder");

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "19990");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_fills_size(), 0);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).forbid_match_rpi(), 1);
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(19980));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(19980));

  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).trigger_price(), "19990");
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_fills_size(), 0);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().at(0).forbid_match_rpi(), 1);
  }

  // 通过site-api下条件单，可以成交
  std::string site_api_order_id;
  {
    SpotSiteApiPlanSpotCreateOrderBuilder build("BTCUSDT", "Buy", "Limit", "0.001", "20000", "20000", 0, "GTC", "", "0",
                                                "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    auto resp1 = user2.create_order(build.Build());
    ASSERT_EQ(resp1->clientorderid(), order_link_id);
    site_api_order_id = order_link_id;
    std::cout << "site-api: order_link_id:" << order_link_id << " order_id:" << resp1->orderid() << std::endl;

    auto result = te->PopResult();
    auto margin_result = result.m_msg.get()->spot_margin_result();
    ASSERT_EQ(margin_result.related_spot_orders().at(0).order_status(), EOrderStatus::Untriggered);
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20010));
  te->MockSpotLastPriceToTrigger("BTCUSDT", bbase::decimal::Decimal<>(20010));

  {
    auto result = te->PopResult();
    auto margin_result = result.m_msg.get()->spot_margin_result();
    ASSERT_EQ(margin_result.related_spot_orders().at(0).order_status(), EOrderStatus::Triggered);
  }
  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto margin_result = result.m_msg.get()->spot_margin_result();
    ASSERT_EQ(margin_result.related_spot_orders().at(0).order_link_id(), site_api_order_id);
    ASSERT_EQ(margin_result.related_spot_orders().at(0).trigger_price(), "20000");
    ASSERT_EQ(margin_result.related_spot_orders().at(0).forbid_match_rpi(), 0);
    ASSERT_EQ(margin_result.related_spot_orders().at(0).order_status(), EOrderStatus::Filled);

    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_fills_size(), 1);
  }

  // 校验对手方是RPI
  {
    auto result = te->PopResult();

    ASSERT_NE(result.m_msg.get(), nullptr);
    auto margin_result = result.m_msg.get()->spot_margin_result();
    ASSERT_EQ(margin_result.related_spot_orders().at(0).forbid_match_rpi(), 1);
    ASSERT_EQ(margin_result.related_spot_orders().at(0).time_in_force(), ETimeInForce::RPI);

    ASSERT_EQ(margin_result.related_spot_fills_size(), 1);
  }
}

TEST_F(SCreateSoTest, idn_tpsl_order_tax_check) {
  // 税率为10%
  static constexpr const char* test_7 =
      R"({"tax_rule_list":[
    {"tax_site": "IDN","fiat_coin_id": 25, "tax_rule_id": 1,"tax_ppn_e8": 10000000,"tax_pph_e8": 10000000,"cfx_fee_e8": 10000000}],
    "tax_rule_map": {"IDN": 1}
    })";

  ASSERT_EQ(config::ConfigProxy::Instance().TaxRuleConfigReceiveHandler(test_7), true);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));

  biz::user_id_t uid1 = 22345678;
  stub user1(te, uid1);
  FutureDepositBuilder deposit_build3("create_order_by_idn_2", uid1, "create_order_by_idn_2", ECoin::USDT, 1, "2000",
                                      "0");
  auto deposit_resp = user1.process(deposit_build3.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  biz::user_id_t uid2 = 12345678;
  stub user2(te, uid2);
  FutureDepositBuilder deposit_build("create_order_by_idn", uid2, "create_order_by_idn", ECoin::BTC, 1, "10000", "0");
  auto deposit_resp2 = user2.process(deposit_build.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  // 卖方向触发
  {
    // 摆盘
    {
      SpotSiteApiCreateAOBuilder build("BTCUSDT", "Buy", "Limit", "0.01", "20000", "GTC", "", "0", "");
      auto order_link_id = te->GenUUID();
      build.SetOrderLinkID(order_link_id);
      auto resp1 = user1.create_order(build.Build());
      ASSERT_EQ(resp1->client_order_id(), order_link_id);
      auto result = te->PopResult();
      EXPECT_EQ(22345678, result.m_msg->header().user_id());

      ASSERT_NE(result.m_msg.get(), nullptr);
    }

    // 下tpsl订单直接成交
    {
      RpcContext ct{};
      ct.ct.AddMetadata("user-site-id", "IDN");
      SpotSiteApiCreateAOBuilder build("BTCUSDT", "Sell", "Limit", "0.01", "20000", "GTC", "", "0", "");

      build.SetTpSl("19990", "20010", "19990", "20010", "Limit", "Limit");

      RpcContext ct2;
      ct2.ct.AddMetadata("user-site-id", "IDN");
      auto resp1 = user2.create_order(build.Build(), ct2);

      auto result = te->PopResult();
      EXPECT_EQ(12345678, result.m_msg->header().user_id());
      const auto& spot_orders_1 = result.m_msg->spot_margin_result().related_spot_orders();
      ASSERT_EQ(spot_orders_1.size(), 2);

      ASSERT_EQ(spot_orders_1.at(0).order_status(), EOrderStatus::Untriggered);
      // ASSERT_EQ(spot_orders_1.at(0).qty(), "0.005");
      ASSERT_EQ(spot_orders_1.at(0).side(), ESide::Buy);

      ASSERT_EQ(spot_orders_1.at(1).order_status(), EOrderStatus::Filled);
      ASSERT_EQ(spot_orders_1.at(1).side(), ESide::Sell);

      const auto& spot_fills_1 = result.m_msg->spot_margin_result().related_spot_fills();
      ASSERT_EQ(spot_fills_1.size(), 1);
      ASSERT_EQ(spot_fills_1[0].cum_order_extra_fee_item()[0].extra_fee(), "40");
      ASSERT_EQ(spot_fills_1[0].cum_order_extra_fee_item()[1].extra_fee(), "40");
    }

    {
      auto result = te->PopResult();
      EXPECT_EQ(22345678, result.m_msg->header().user_id());
    }
  }

  // 买方向触发
  {
    // 摆盘
    {
      SpotSiteApiCreateAOBuilder build("BTCUSDT", "Sell", "Limit", "0.01", "20000", "GTC", "", "0", "");
      auto order_link_id = te->GenUUID();
      build.SetOrderLinkID(order_link_id);
      auto resp1 = user2.create_order(build.Build());
      ASSERT_EQ(resp1->client_order_id(), order_link_id);
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
    }

    // 下tpsl订单直接成交
    {
      RpcContext ct{};
      ct.ct.AddMetadata("user-site-id", "IDN");
      SpotSiteApiCreateAOBuilder build("BTCUSDT", "Buy", "Limit", "0.01", "20000", "GTC", "", "0", "");

      build.SetTpSl("20010", "19990", "20010", "19990", "Limit", "Limit");

      RpcContext ct2;
      ct2.ct.AddMetadata("user-site-id", "IDN");
      auto resp1 = user1.create_order(build.Build(), ct2);

      auto result = te->PopResult();
      EXPECT_EQ(22345678, result.m_msg->header().user_id());
      const auto& spot_orders_1 = result.m_msg->spot_margin_result().related_spot_orders();
      ASSERT_EQ(spot_orders_1.size(), 2);

      ASSERT_EQ(spot_orders_1.at(0).order_status(), EOrderStatus::Untriggered);
      ASSERT_EQ(spot_orders_1.at(0).qty(), "0.005");
      ASSERT_EQ(spot_orders_1.at(0).side(), ESide::Sell);

      ASSERT_EQ(spot_orders_1.at(1).order_status(), EOrderStatus::Filled);
      ASSERT_EQ(spot_orders_1.at(1).side(), ESide::Buy);

      const auto& spot_fills_1 = result.m_msg->spot_margin_result().related_spot_fills();
      ASSERT_EQ(spot_fills_1.size(), 1);
      ASSERT_EQ(spot_fills_1[0].cum_order_extra_fee_item()[0].extra_fee(), "0.002");
      ASSERT_EQ(spot_fills_1[0].cum_order_extra_fee_item()[1].extra_fee(), "0.002");
    }

    {
      auto result = te->PopResult();
      EXPECT_EQ(12345678, result.m_msg->header().user_id());
    }
  }
}

TEST_F(SCreateSoTest, site_api_create_trailling_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build1("site_api_trailling_coin_1", uid, "site_api_create_tpsl_order", 1, 1, "100", "0");
  auto deposit_resp1 = user1.process(deposit_build1.Build());
  auto deposit_result1 = te->PopResult();
  ASSERT_NE(deposit_result1.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("site_api_trailling_coin_1", uid, "site_api_create_tpsl_order", 5, 1, "100000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(2000));
  {
    SpotSiteApiPlanSpotCreateOrderBuilder build3("BTCUSDT", "Buy", "Market", "0.1", "", "", 4, "IOC", "", "0", "");
    auto order_link_id1 = te->GenUUID();
    build3.SetOrderLinkID(order_link_id1);

    build3.SetTrailingInfo("1900", "", "10");
    build3.SetMarketUnit("BaseCoin");

    auto resp1 = user1.create_order(build3.Build());
    ASSERT_EQ(resp1->clientorderid(), order_link_id1);
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
  }

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build4("BTCUSDT", "Sell", "Market", "10000", "", "", 4, "IOC", "", "0", "");
    auto order_link_id2 = te->GenUUID();
    build4.SetOrderLinkID(order_link_id2);

    build4.SetTrailingInfo("1900", "", "10");
    build4.SetMarketUnit("quoteCoin");

    auto resp2 = user1.create_order(build4.Build());
    ASSERT_EQ(resp2->clientorderid(), order_link_id2);
    auto result2 = te->PopResult();
    ASSERT_NE(result2.m_msg.get(), nullptr);
  }

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build5("BTCUSDT", "Buy", "Market", "0.1", "", "", 4, "IOC", "", "0", "");
    auto order_link_id3 = te->GenUUID();
    build5.SetOrderLinkID(order_link_id3);

    build5.SetTrailingInfo("", "", "10");
    build5.SetMarketUnit("BaseCoin");

    auto resp3 = user1.create_order(build5.Build());
    ASSERT_EQ(resp3->clientorderid(), order_link_id3);
    auto result3 = te->PopResult();
    ASSERT_NE(result3.m_msg.get(), nullptr);
  }

  {
    SpotSiteApiPlanSpotCreateOrderBuilder build6("BTCUSDT", "Sell", "Market", "10000", "", "", 4, "IOC", "", "0", "");
    auto order_link_id4 = te->GenUUID();
    build6.SetOrderLinkID(order_link_id4);

    build6.SetTrailingInfo("", "", "10");
    build6.SetMarketUnit("quoteCoin");

    auto resp4 = user1.create_order(build6.Build());
    ASSERT_EQ(resp4->clientorderid(), order_link_id4);
    auto result4 = te->PopResult();
    ASSERT_NE(result4.m_msg.get(), nullptr);
  }
}

std::shared_ptr<tmock::CTradeAppMock> SCreateSoTest::te;
