#include "calc_market_order_px.hpp"  // NOLINT

#include <iostream>
#include <string>

#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(SCalcMarketOrderPxTest, reduce_liability_buy) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);
  auto sc = const_cast<config::SpotSymbolDTO*>(config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolName(
      "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id()));
  {
    FutureDepositBuilder deposit_build("self_fill", uid, "self_fill", 1, 1, "1", "0");
    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  {
    FutureDepositBuilder deposit_build("self_fill", uid, "self_fill22", 5, 1, "100000", "0");
    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  {
    FutureDepositBuilder deposit_build("self_fill", uid2, "self_fill", 5, 1, "10000000", "0");
    auto deposit_resp = user2.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  {
    SwitchSpotMarginBuilder switchSpotMarginBuilder(user1.m_uid, true);
    auto resp = user1.process(switchSpotMarginBuilder.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    sc->open_price = bbase::decimal::Decimal<>("100");

    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Market", "9", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetIsLeverage(true);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);

    ASSERT_TRUE(biz::BigDecimal(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].price())
                    .Equal(sc->open_price.Mul(decimal::kOne + bbase::decimal::Decimal("5").Shift(-2))));
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "4.5", "20000");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetIsLeverage(true);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::cout << result.m_msg.get()->asset_margin_result().usd_wallet().im_rate_e2() << std::endl;
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("21700"));
  {
    OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Buy", "Limit", "20", "21706.1");
    auto order_link_id = te->GenUUID();
    build2.SetOrderLinkID(order_link_id);

    auto resp2 = user2.create_order(build2.Build());
    ASSERT_EQ(resp2->order_link_id(), order_link_id);
    auto result2 = te->PopResult();

    ASSERT_NE(result2.m_msg.get(), nullptr);

    result2 = te->PopResult();

    ASSERT_NE(result2.m_msg.get(), nullptr);
  }
}

TEST_F(SCalcMarketOrderPxTest, reduce_liability_sell) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  biz::user_id_t uid2 = 10001;
  stub user2(te, uid2);
  auto sc = const_cast<config::SpotSymbolDTO*>(config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolName(
      "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id()));
  {
    FutureDepositBuilder deposit_build("self_fill", uid, "self_fill", 5, 1, "100000", "0");
    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  {
    FutureDepositBuilder deposit_build("self_fill", uid, "self_fillaaa", 1, 1, "100", "0");
    auto deposit_resp = user1.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  {
    FutureDepositBuilder deposit_build("self_fill", uid2, "self_fill", 1, 1, "100", "0");
    auto deposit_resp = user2.process(deposit_build.Build());
    auto deposit_result = te->PopResult();
    ASSERT_NE(deposit_result.m_msg.get(), nullptr);
  }

  {
    SwitchSpotMarginBuilder switchSpotMarginBuilder(user1.m_uid, true);
    auto resp = user1.process(switchSpotMarginBuilder.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
        bbase::object_manager::ObjectType::kObjectConfigCenter);
    nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                               "market_order_mmr_divide", "0.1");

    sc->open_price = bbase::decimal::Decimal<>("100");

    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Market", "2", "");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetIsLeverage(true);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders_size(), 1);
    ASSERT_TRUE(biz::BigDecimal(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].price())
                    .Equal(sc->open_price.Mul(decimal::kOne - bbase::decimal::Decimal<>(5).Shift(-2))));
  }

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "2", "20000");
    auto order_link_id = te->GenUUID();
    build.SetOrderLinkID(order_link_id);
    build.SetIsLeverage(true);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::cout << result.m_msg.get()->asset_margin_result().usd_wallet().im_rate_e2() << std::endl;
  }

  {
    OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Sell", "Limit", "2", "20000");
    auto order_link_id = te->GenUUID();
    build2.SetOrderLinkID(order_link_id);

    auto resp2 = user2.create_order(build2.Build());
    ASSERT_EQ(resp2->order_link_id(), order_link_id);
    auto result2 = te->PopResult();

    ASSERT_NE(result2.m_msg.get(), nullptr);

    result2 = te->PopResult();

    ASSERT_NE(result2.m_msg.get(), nullptr);
  }
}

std::shared_ptr<tmock::CTradeAppMock> SCalcMarketOrderPxTest::te;
