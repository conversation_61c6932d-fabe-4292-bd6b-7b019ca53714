#include "replace_ao.hpp"  // NOLINT

#include <string>
#include <vector>

#include "enums/ecreatetype/create_type.pb.h"
#include "lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(SReplaceAoTest, replace_pre_check) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  // 充值BTC
  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  // 充值USDT
  FutureDepositBuilder deposit_build2("open_api_v5_create_order3", uid, "open_api_v5_create_order3", 5, 1, "1000000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));

  // replace 币对不存在
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "XXXUSDT", "", "123456", "0.002", "20001", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170121");

    SpotSiteApiReplaceAOBuilder build2("XXXUSDT", "1", "20000", "0");
    RpcContext ct2{};
    auto resp2 = user1.replace_order(build2.Build(), ct2);
    ASSERT_EQ(ct2.ct.GetServerInitialMetadata().find("next-response-codes")->second, "32101");

    SpotInnerReplaceOrderBuilder build3(uid, "XXXUSDT", "", "1", "20000", "0", "");
    auto resp3 = user1.replace_order(build3.Build());
    ASSERT_EQ(resp3.get()->ret_code(), 170121);
  }

  // 不传order_id与order_link_id
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", "", "0.002", "20001", "", "", "");
    RpcContext ct{};
    svc::uta_engine::req::ReplaceOrderReqV5 replace_order_req = build.Build();
    replace_order_req.set_order_link_id("");
    auto resp1 = user1.replace_order(replace_order_req, ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170003");
  }

  // replace order_link_id不存在
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", "123456", "0.002", "20001", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170213");
  }

  // createOrder maker
  auto order_link_id = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.002", "40");
  }

  // replace order_id不存在
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "123456", "", "0.002", "20001", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170213");
  }

  /*// createOrder taker
  auto order_link_id2 = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id2);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id2)
        .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill)
        .Check(uid, "0.002", "40");
  }

  // replace taker订单已完结 无法测试
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id2, "0.002", "20001", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "110008");
  }*/

  // createOrder IOC
  auto order_link_id3 = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id3);
    build.SetTimeInForce("IOC");

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  /*// replace 修改IOC单 无法测试
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id3, "0.002", "20001", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170300");
  }*/

  // createOrder maker
  auto order_link_id4 = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.2", "20000");
    build.SetOrderLinkID(order_link_id4);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id4)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.2", "4000");
  }

  // replace 修改价格 超过价格精度
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id4, "0.2", "20000.0002", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170134");
  }

  // replace 价格数量没有变化
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id4, "0.2", "20000", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "10001");

    SpotSiteApiReplaceAOBuilder build2("BTCUSDT", "0.2", "20000", "");
    build2.SetOrderLinkID(order_link_id4);
    RpcContext ct2{};
    auto resp2 = user1.replace_order(build2.Build(), ct2);
    ASSERT_EQ(ct2.ct.GetServerInitialMetadata().find("next-response-codes")->second, "32316");
  }

  // replace 价格数量没有变化
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id4, "0.2", "0", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170130");
  }

  // replace 价格数量没有变化
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id4, "0", "20000", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170130");
  }

  // replace 价格数量没有变化
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id4, "0", "0", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170130");
  }

  // replace 价格数量没有变化
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id4, "", "", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "10001");
  }

  // replace 超过数量精度
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id4, "2.0005", "20100", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170137");
  }

  // replace 修改价格 价格改小 违反盘面风控
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id4, "0.2", "1995", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170194");
  }

  // createOrder taker 完全成交
  auto order_link_id5 = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(order_link_id5);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id5)
        .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill)
        .Check(uid, "0.1", "2000");
  }

  // replace 修改数量，小于已成交数量
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id4, "0.05", "20000", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170313");
  }
}

TEST_F(SReplaceAoTest, index_mov_avg_price_limit_buy) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 5, 1, "100000",
                                     "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  auto sc = const_cast<config::SpotSymbolDTO*>(config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolName(
      "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id()));
  sc->limit_status = true;
  sc->limit_order_price_limit_percentage = bbase::decimal::Decimal("10");
  sc->market_order_price_limit_percentage = bbase::decimal::Decimal("10");
  sc->limit_y_percentage = bbase::decimal::Decimal("15");
  te->MockSpotLastPriceToPreEngine(sc->symbol_name, bbase::decimal::Decimal("2"));
  usleep(10);

  // createOrder
  auto order_link_id = te->GenUUID();
  std::basic_string<char> order_id = "";
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.002", "1");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.002", "0.002");

    order_id = resp->order_id();
  }

  // 开关未打开, 买价在最大范围以外
  // lastPrice: 2, 限价 2 * 100 = 200, 拦截
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "201", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170193");

    auto result1 = te->PopResult(2000);
    ASSERT_EQ(result1.m_msg.get(), nullptr);
  }

  // 开关未打开, 卖价减小到限价范围以上
  // lastPrice: 1, 限价 1 * 100 = 100, 不拦截
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "199", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    //    result1.m_msg.get()->spot_margin_result().
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "199");
  }

  te->MockSpotLastPriceToPreEngine(sc->symbol_name, bbase::decimal::Decimal("1000"));
  usleep(10);
  // 恢复为 1000
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "1000", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    //    result1.m_msg.get()->spot_margin_result().
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "1000");
  }

  sc->limit_status = false;

  // 开关打开, 买价大于最大买价
  // lastPrice: 1000, x: 10, 限价 1000 * (1 + 10%) = 1100, 拦截
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "1101", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170193");

    auto result1 = te->PopResult(2000);
    ASSERT_EQ(result1.m_msg.get(), nullptr);
  }

  // 开关打开, 买价大于最大买价
  // lastPrice: 1000, x: 10, 限价 1000 * (1 + 10%) = 1100, 拦截
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "1099", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    //    result1.m_msg.get()->spot_margin_result().
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "1099");
  }

  // 恢复为 1000
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "1000", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    //    result1.m_msg.get()->spot_margin_result().
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "1000");
  }

  // 指数价格 900, 20的滑动溢价, 当前时间, 指数价格可信
  te->MockSpotIndexMovAvgToPreEngine(sc->symbol_id, "1000", "20", bbase::utils::Time::GetTimeMs(), true);
  usleep(10);

  // 最高买价
  // min(max(1000, 1000*1.1 + 20), 1000*1.15) = 1120, 高于 1120 拦截
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "1121", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170193");

    auto result1 = te->PopResult(2000);
    ASSERT_EQ(result1.m_msg.get(), nullptr);
  }

  // 最高买价
  // min(max(1000, 1000*1.1 + 20), 1000*1.15) = 1120, 低于 1120 不拦截
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "1119", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    //    result1.m_msg.get()->spot_margin_result().
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "1119");
  }
}

TEST_F(SReplaceAoTest, index_mov_avg_price_limit_sell) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  auto sc = const_cast<config::SpotSymbolDTO*>(config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolName(
      "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id()));
  sc->limit_status = true;
  sc->limit_order_price_limit_percentage = bbase::decimal::Decimal("10");
  sc->market_order_price_limit_percentage = bbase::decimal::Decimal("10");
  sc->limit_y_percentage = bbase::decimal::Decimal("15");
  te->MockSpotLastPriceToPreEngine(sc->symbol_name, bbase::decimal::Decimal("1000"));
  usleep(10);

  // createOrder
  auto order_link_id = te->GenUUID();
  std::basic_string<char> order_id = "";
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "1100");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.002", "2.2");

    order_id = resp->order_id();
  }

  // 开关未打开, 卖价减小到限价范围以下
  // lastPrice: 1000, x: 10, 限价 1000 * (1 - 10%) = 900, 拦截
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "899", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170194");

    auto result1 = te->PopResult(2000);
    ASSERT_EQ(result1.m_msg.get(), nullptr);
  }

  // 开关未打开, 卖价减小到限价范围以上
  // lastPrice: 1000, x: 10, 限价 1000 * (1 - 10%) = 900, 不拦截
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "901", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    //    result1.m_msg.get()->spot_margin_result().
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "901");
  }

  // 恢复为 1000
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "1000", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    //    result1.m_msg.get()->spot_margin_result().
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "1000");
  }

  sc->limit_status = false;

  // 开关打开, 卖价减小到限价范围以下
  // lastPrice: 1000, x: 10, 限价 1000 * (1 - 10%) = 900, 拦截
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "899", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170194");

    auto result1 = te->PopResult(2000);
    ASSERT_EQ(result1.m_msg.get(), nullptr);
  }

  // 开关打开, 卖价减小到限价范围以上
  // lastPrice: 1000, x: 10, 限价 1000 * (1 - 10%) = 900, 不拦截
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "901", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    //    result1.m_msg.get()->spot_margin_result().
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "901");
  }

  // 恢复为 1000
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "1000", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    //    result1.m_msg.get()->spot_margin_result().
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "1000");
  }

  // 指数价格 900, 20的滑动溢价, 当前时间, 指数价格可信
  te->MockSpotIndexMovAvgToPreEngine(sc->symbol_id, "900", "20", bbase::utils::Time::GetTimeMs(), true);
  usleep(10);

  // 最低卖价
  // max(min(900, 900 * 0.9 + 20), 900*0.85) = 830, 低于 830 拦截
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "829", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170194");

    auto result1 = te->PopResult(2000);
    ASSERT_EQ(result1.m_msg.get(), nullptr);
  }

  // 最低卖价
  // max(min(900, 900 * 0.9 + 20), 900*0.85) = 830, 高于 830 不拦截
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "831", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    //    result1.m_msg.get()->spot_margin_result().
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "831");
  }
}

TEST_F(SReplaceAoTest, replace_normal_ao) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  // createOrder
  auto order_link_id = te->GenUUID();
  std::basic_string<char> order_id = "";
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.002", "40");

    order_id = resp->order_id();
  }

  // replace 修改价格 价格改小
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "19995", "", "", "");
    RpcContext ct{};
    auto resp1 = user1.replace_order(build.Build(), ct);
    // ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    //    result1.m_msg.get()->spot_margin_result().
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(), ECrossStatus::ReAdded);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "19995");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.002");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_qty(), "0.002");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_amount(), "39.99");
  }

  // replace 修改价格 价格改大
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "21000", "", "", "");
    auto resp1 = user1.replace_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(), ECrossStatus::ReAdded);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "21000");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.002");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_qty(), "0.002");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_amount(), "42");
  }

  // replace 修改数量 数量改大
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.004", "21000", "", "", "");
    auto resp1 = user1.replace_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(), ECrossStatus::ReAdded);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "21000");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.004");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_qty(), "0.004");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_amount(), "84");
  }

  // replace 修改数量 数量改小
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.001", "21000", "", "", "");
    auto resp1 = user1.replace_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(),
              ECrossStatus::Replaced);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "21000");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.001");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_qty(), "0.001");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_amount(), "21");
  }

  {
    SpotInnerReplaceOrderBuilder build(uid, "BTCUSDT", "", order_link_id, "21000", "0.004", "");
    auto resp1 = user1.replace_order(build.Build());
    ASSERT_EQ(resp1.get()->ret_code(), 0);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(), ECrossStatus::ReAdded);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "21000");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.004");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_qty(), "0.004");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_amount(), "84");
  }

  // inner replace order with need reply after cross
  {
    SpotInnerReplaceOrderBuilder build(uid, "BTCUSDT", "", order_link_id, "22000", "0.001", "");
    build.SetNeedReplyAfterCross(true);
    auto resp1 = user1.replace_order(build.Build());
    ASSERT_EQ(resp1.get()->ret_code(), 0);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(), ECrossStatus::ReAdded);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "22000");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.001");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_qty(), "0.001");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_amount(), "22");
  }
}

TEST_F(SReplaceAoTest, replace_partion_fill_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("open_api_v5_create_order3", uid, "open_api_v5_create_order3", 5, 1, "10000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  // createOrder
  auto order_link_id = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.002", "40");
  }

  // 部分成交
  auto order_link_id2 = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.001", "20000");
    build.SetOrderLinkID(order_link_id2);

    auto resp = user1.create_order(build.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id2);
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id2)
        .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill)
        .Check(uid, "0.001", "20");
  }

  // replace 修改价格 和数量
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.004", "19995", "", "", "");
    auto resp1 = user1.replace_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].order_status(),
              EOrderStatus::PartiallyFilled);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(), ECrossStatus::ReAdded);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "19995");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.004");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_qty(), "0.003");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_amount(), "59.985");
  }
}

// replace 数量 = 已执行量
TEST_F(SReplaceAoTest, replace_qty_eq_exec_qty_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("open_api_v5_create_order3", uid, "open_api_v5_create_order3", 5, 1, "10000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  // createOrder
  auto order_link_id = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.002", "40");
  }

  // 部分成交
  auto order_link_id2 = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.001", "20000");
    build.SetOrderLinkID(order_link_id2);

    auto resp = user1.create_order(build.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id2);
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id2)
        .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill)
        .Check(uid, "0.001", "20");
  }

  // replace 数量 = 已执行量
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.001", "19995", "", "", "");
    auto resp1 = user1.replace_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].order_status(),
              EOrderStatus::PartiallyFilledCanceled);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(),
              ECrossStatus::Canceled);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "20000");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.002");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_qty(), "0.001");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_amount(), "20");
  }
}

// replace 先挂盘口， 再挂单1， 改单， 和 挂单1 成交
TEST_F(SReplaceAoTest, replace_to_taker) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("open_api_v5_create_order3", uid, "open_api_v5_create_order3", 5, 1, "10000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  // 1.挂盘口
  auto order_link_id = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.002", "40");
  }

  // 2.挂单
  auto order_link_id2 = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.003", "19990");
    build.SetOrderLinkID(order_link_id2);

    auto resp = user1.create_order(build.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id2);
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id2)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.003", "59.97");
  }

  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id2, "0.002", "20000", "", "", "");
    auto resp1 = user1.replace_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id2);

    auto result3 = te->PopResult(5000);
    ASSERT_NE(result3.m_msg.get(), nullptr);
    result3.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id2)
        .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill)
        .Check(uid, "0.002", "40")
        .CheckLeaves("0", "0");
  }
}

// 场景：构造pending-replace 和 pending-cancel 同时出现场景
// 操作顺序：挂住撮合，先replace，后cancel
TEST_F(SReplaceAoTest, replace_then_cancel) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("open_api_v5_create_order3", uid, "open_api_v5_create_order3", 5, 1, "10000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  // 1.挂盘口
  auto order_link_id = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.002", "40");
  }

  te->SuspendCross();

  // 1.replace
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.003", "20000", "", "", "");
    auto resp1 = user1.replace_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
  }

  // 2.cancel
  {
    OpenApiV5CancelOrderBuilder cancel_build("spot", "BTCUSDT", "", order_link_id, "order");
    auto resp2 = user1.cancel_order(cancel_build.Build());
    // auto result2 = te->PopResult(50000);
  }

  te->ResumeCross();
  auto result1 = te->PopResult(3000);
  ASSERT_NE(result1.m_msg.get(), nullptr);
  result1.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded)
      .Check(uid, "0.003", "60")
      .CheckLeaves("0.003", "60");

  auto result2 = te->PopResult(3000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled)
      .Check(uid, "0.003", "60")
      .CheckLeaves("0.003", "60");
}

// 场景：构造pending-new 和 pending-replace 同时出现场景
// 操作顺序：挂住撮合，先下单，然后replace
TEST_F(SReplaceAoTest, new_then_replace) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("open_api_v5_create_order3", uid, "open_api_v5_create_order3", 5, 1, "10000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  te->SuspendCross();

  // 1.构造pending-new
  auto order_link_id = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
  }

  // 2.构造pending-replace
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.003", "19990", "", "", "");
    auto resp1 = user1.replace_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
  }

  te->ResumeCross();

  auto result1 = te->PopResult(3000);
  ASSERT_NE(result1.m_msg.get(), nullptr);
  result1.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
      .Check(uid, "0.002", "40")
      .CheckLeaves("0.002", "40");

  auto result2 = te->PopResult(3000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded)
      .Check(uid, "0.003", "59.97")
      .CheckLeaves("0.003", "59.97");
}

// 场景：maker fill的时候，构造replace
TEST_F(SReplaceAoTest, place_then_makerfill) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("open_api_v5_create_order3", uid, "open_api_v5_create_order3", 5, 1, "10000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  // 1.构造maker订单
  auto order_link_id = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.003", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result1 = te->PopResult(3000);
    result1.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.003", "60");
  }

  te->SuspendCross();

  // 2.构造taker订单, maker 完全成交， taker: 0.001
  auto order_link_id2 = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.001", "20000");
    build.SetOrderLinkID(order_link_id2);

    auto resp = user1.create_order(build.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id2);
  }

  // 3.构造pending-replace
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "20000", "", "", "");
    auto resp1 = user1.replace_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
  }

  te->ResumeCross();

  // taker 部分成交回报
  auto result1 = te->PopResult(3000);
  ASSERT_NE(result1.m_msg.get(), nullptr);
  result1.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill)
      .Check(uid, "0.003", "60")
      .CheckLeaves("0.002", "40");

  // 改单回报
  auto result2 = te->PopResult(3000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::Replaced)
      .Check(uid, "0.002", "40")
      .CheckLeaves("0.001", "20");
}

TEST_F(SReplaceAoTest, replace_double_pending_check) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  // createOrder
  auto order_link_id = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  te->SuspendCross();

  // 第一次 replace 修改价格
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "19995", "", "", "");
    auto resp1 = user1.replace_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);
  }

  // 第二次 replace 修改数量
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.003", "19995", "", "", "");
    RpcContext ct{};
    auto resp = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "110079");
  }

  te->ResumeCross();
  te->PopResult(3000);
}

TEST_F(SReplaceAoTest, replace_pending_new_check) {
  biz::user_id_t uid = 10004;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  te->SuspendCross();
  // createOrder
  auto order_link_id = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
  }

  // replace
  {
    OpenApiV5ReplaceOrderBuilder build2("spot", "BTCUSDT", "", order_link_id, "0.003", "19995", "", "", "");
    RpcContext ct{};
    auto resp2 = user1.replace_order(build2.Build(), ct);
  }

  te->ResumeCross();

  auto result1 = te->PopResult();
  ASSERT_NE(result1.m_msg.get(), nullptr);

  result1.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
      .Check(uid, "0.002", "40");
  ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "20000");
  ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.002");

  auto result2 = te->PopResult();
  ASSERT_NE(result1.m_msg.get(), nullptr);

  result2.RefSpotMarginResult()
      .RefRelatedOrderByOrderLinkId(order_link_id)
      .CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded)
      .Check(uid, "0.003", "59.985");

  ASSERT_EQ(result2.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "19995");
  ASSERT_EQ(result2.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.003");
}

TEST_F(SReplaceAoTest, replace_immediate_check) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  te->SuspendCross();
  {
    // replace ioc
    auto order_link_id = te->GenUUID();

    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);
    build.SetTimeInForce("ioc");

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");

    // replace  ioc
    {
      OpenApiV5ReplaceOrderBuilder build2("spot", "BTCUSDT", "", order_link_id, "0.003", "19995", "", "", "");
      RpcContext ct{};
      auto resp2 = user1.replace_order(build2.Build(), ct);
      ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170312");
    }
  }

  {
    // replace fok
    auto order_link_id = te->GenUUID();

    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);
    build.SetTimeInForce("fok");

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");

    // replace  ioc
    {
      OpenApiV5ReplaceOrderBuilder build2("spot", "BTCUSDT", "", order_link_id, "0.003", "19995", "", "", "");
      RpcContext ct{};
      auto resp2 = user1.replace_order(build2.Build(), ct);
      ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170312");
    }
  }

  te->ResumeCross();
}

TEST_F(SReplaceAoTest, replace_inactive_order_check) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("open_api_v5_create_order3", uid, "open_api_v5_create_order3", 5, 1, "10000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  // createOrder
  auto order_link_id = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.002", "40");
  }

  // 完全成交
  auto order_link_id2 = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id2);

    auto resp = user1.create_order(build.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id2);
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id2)
        .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill)
        .Check(uid, "0.002", "40");
  }

  // order_link_id 完全成交，replace 修改价格 和数量
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.004", "19995", "", "", "");
    RpcContext ct{};
    auto resp2 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170213");
  }
}

TEST_F(SReplaceAoTest, siteapi_replace_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("open_api_v5_create_order3", uid, "open_api_v5_create_order3", 5, 1, "10000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  // createOrder
  auto order_link_id = te->GenUUID();
  auto order_id = std::string("");
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    order_id = resp->order_id();
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.002", "40");
  }

  {
    SpotSiteApiReplaceAOBuilder build("BTCUSDT", "0.003", "20001", "0");
    build.SetOrderID(order_id);
    auto resp = user1.replace_order(build.Build());
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderId(order_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::ReAdded)
        .Check(uid, "0.003", "60.003");
  }

  {
    SpotSiteApiReplaceAOBuilder build("ETHUSDT", "0.004", "20002", "0");
    build.SetOrderID(order_id);
    RpcContext ct{};
    auto resp = user1.replace_order(build.Build(), ct);
    auto result = te->PopResult(2000);
    ASSERT_EQ(result.m_msg.get(), nullptr);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "30003");
  }
}

TEST_F(SReplaceAoTest, replace_reject) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("open_api_v5_create_order3", uid, "open_api_v5_create_order3", 5, 1, "10000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  {
    // 下卖单
    auto order_link_id = te->GenUUID();
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build());
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.002", "40");

    // 卡住撮合
    te->SuspendCross();

    // 下买单
    OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Buy", "Limit", "0.002", "20000");
    build2.SetOrderLinkID(te->GenUUID());
    auto resp2 = user1.create_order(build2.Build());

    // 改单
    OpenApiV5ReplaceOrderBuilder build3("spot", "BTCUSDT", "", order_link_id, "0.005", "20000", "");
    auto resp3 = user1.replace_order(build3.Build());

    // 恢复撮合
    te->ResumeCross();

    auto result2 = te->PopResult(5000);
    auto result3 = te->PopResult(5000);

    result3.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::Filled, ECrossStatus::ReplaceRejected);
  }

  {
    auto order_link_id = te->GenUUID();
    SpotInnerCreateAOBuilder builder(uid, "BTCUSDT", ESide::Buy, EOrderType::Limit, "0.002", "20000",
                                     ETimeInForce::GoodTillCancel, ELeveragePlaceType::SPOT_TYPE);
    builder.SetOrderLinkID(order_link_id);
    builder.SetCreateType(ECreateType::CreateByArbitrage);
    auto resp = user1.create_order(builder.Build());
    auto result = te->PopResult();

    OpenApiV5ReplaceOrderBuilder builder2("spot", "BTCUSDT", "", order_link_id, "0.005", "20000", "");
    RpcContext ct{};
    auto resp2 = user1.replace_order(builder2.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170312");
  }
}

TEST_F(SReplaceAoTest, replace_iceberg_fail) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("open_api_v5_create_order3", uid, "open_api_v5_create_order3", 5, 1, "10000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("2000"));
  {
    SpotInnerCreateAOBuilder builder(uid, "BTCUSDT", ESide::Sell, EOrderType::Limit, "1", "2000",
                                     ETimeInForce::GoodTillCancel, ELeveragePlaceType::SPOT_TYPE);
    auto order_link_id = te->GenUUID();
    builder.SetOrderLinkID(order_link_id);
    builder.SetCreateType(ECreateType::CreateByIceBerg);

    auto resp1 = user1.create_order(builder.Build());
    ASSERT_NE(resp1->order_id(), "");

    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.05", "20000", "", "", "");
    RpcContext ct{};
    auto resp2 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170312");
  }
}

TEST_F(SReplaceAoTest, replace_twap_fail) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("open_api_v5_create_order3", uid, "open_api_v5_create_order3", 5, 1, "10000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("2000"));
  {
    SpotInnerCreateAOBuilder builder(uid, "BTCUSDT", ESide::Sell, EOrderType::Limit, "1", "2000",
                                     ETimeInForce::GoodTillCancel, ELeveragePlaceType::SPOT_TYPE);
    auto order_link_id = te->GenUUID();
    builder.SetOrderLinkID(order_link_id);
    builder.SetCreateType(ECreateType::CreateByTWAP);

    auto resp1 = user1.create_order(builder.Build());
    ASSERT_NE(resp1->order_id(), "");

    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.05", "20000", "", "", "");
    RpcContext ct{};
    auto resp2 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170363");
  }
}

TEST_F(SReplaceAoTest, replace_bidi_tpsl) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  // 充值BTC
  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  // 充值USDT
  FutureDepositBuilder deposit_build2("open_api_v5_create_order3", uid, "open_api_v5_create_order3", 5, 1, "1000000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));

  // createOrder maker
  auto order_link_id4 = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.2", "20000", "21000", "19000", "22500",
                                      "20000", "Limit", "Limit");
    build.SetOrderLinkID(order_link_id4);

    RpcContext ct{};
    auto resp = user1.create_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "0");
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id4)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.2", "4000");
  }

  // replace 价格数量没有变化
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id4, "0.2", "20000", "", "", "");
    RpcContext ct{};
    ct.ct.AddMetadata("extension", "{\"eopf\":3}");
    auto resp1 = user1.replace_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "10001");

    OpenApiV5ReplaceOrderBuilder build2("spot", "BTCUSDT", "", order_link_id4, "0.2", "20000", "", "21000", "19000",
                                        "22500", "20000");
    RpcContext ct2{};
    ct2.ct.AddMetadata("extension", "{\"eopf\":3}");
    auto resp2 = user1.replace_order(build2.Build(), ct2);
    ASSERT_EQ(ct2.ct.GetServerInitialMetadata().find("next-response-codes")->second, "10001");

    SpotSiteApiReplaceAOBuilder build3("BTCUSDT", "0.2", "20000", "");
    build3.SetOrderLinkID(order_link_id4);
    RpcContext ct3{};
    auto resp3 = user1.replace_order(build3.Build(), ct3);
    ASSERT_EQ(ct3.ct.GetServerInitialMetadata().find("next-response-codes")->second, "32316");

    SpotSiteApiReplaceAOBuilder build4("BTCUSDT", "0.2", "20000", "", "21000", "19000", "22500", "20000", "Limit",
                                       "Limit");
    build4.SetOrderLinkID(order_link_id4);
    RpcContext ct4{};
    auto resp4 = user1.replace_order(build4.Build(), ct4);
    ASSERT_EQ(ct4.ct.GetServerInitialMetadata().find("next-response-codes")->second, "32316");
  }

  // 改单成功
  {
    OpenApiV5ReplaceOrderBuilder build2("spot", "BTCUSDT", "", order_link_id4, "0.2", "20000", "", "21000", "19000",
                                        "22600", "20010");
    RpcContext ct2{};
    ct2.ct.AddMetadata("extension", "{\"eopf\":3}");
    auto resp2 = user1.replace_order(build2.Build(), ct2);
    auto result2 = te->PopResult(2000);
    ASSERT_EQ(ct2.ct.GetServerInitialMetadata().find("next-response-codes")->second, "0");

    OpenApiV5ReplaceOrderBuilder build3("spot", "BTCUSDT", "", order_link_id4, "0.2", "20000", "", "21000", "0",
                                        "22600", "");
    RpcContext ct3{};
    ct3.ct.AddMetadata("extension", "{\"eopf\":3}");
    auto resp3 = user1.replace_order(build3.Build(), ct3);
    auto result3 = te->PopResult(2000);
    ASSERT_EQ(ct3.ct.GetServerInitialMetadata().find("next-response-codes")->second, "0");
    result3.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id4)
        .CheckSlTriggerPrice("0")
        .CheckSlLimitPrice("0")
        .CheckSlOrderType(EOrderType::UNKNOWN);

    OpenApiV5ReplaceOrderBuilder build4("spot", "BTCUSDT", "", order_link_id4, "0.2", "20000", "", "21000", "19000",
                                        "22600", "");
    RpcContext ct4{};
    ct4.ct.AddMetadata("extension", "{\"eopf\":3}");
    auto resp4 = user1.replace_order(build4.Build(), ct4);
    auto result4 = te->PopResult(2000);
    ASSERT_EQ(ct4.ct.GetServerInitialMetadata().find("next-response-codes")->second, "0");
    result4.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id4)
        .CheckSlTriggerPrice("19000")
        .CheckSlLimitPrice("0")
        .CheckSlOrderType(EOrderType::Market);

    OpenApiV5ReplaceOrderBuilder build5("spot", "BTCUSDT", "", order_link_id4, "0.2", "20000", "", "21100", "19000",
                                        "22600", "");
    RpcContext ct5{};
    ct5.ct.AddMetadata("extension", "{\"eopf\":3}");
    auto resp5 = user1.replace_order(build5.Build(), ct5);
    auto result5 = te->PopResult(2000);
    ASSERT_EQ(ct5.ct.GetServerInitialMetadata().find("next-response-codes")->second, "0");
    result5.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id4)
        .CheckTpTriggerPrice("21100")
        .CheckTpLimitPrice("22600")
        .CheckTpOrderType(EOrderType::Limit);

    SpotSiteApiReplaceAOBuilder build6("BTCUSDT", "0.2", "20000", "", "21000", "19000", "21500", "20010", "Market",
                                       "Limit");
    build6.SetOrderLinkID(order_link_id4);
    RpcContext ct6{};
    auto resp6 = user1.replace_order(build6.Build(), ct6);
    auto result6 = te->PopResult(2000);
    ASSERT_EQ(ct6.ct.GetServerInitialMetadata().find("next-response-codes")->second, "32198");

    SpotSiteApiReplaceAOBuilder build7("BTCUSDT", "0.2", "20000", "", "21000", "19000", "0", "20010", "Market",
                                       "Limit");
    build7.SetOrderLinkID(order_link_id4);
    RpcContext ct7{};
    auto resp7 = user1.replace_order(build7.Build(), ct7);
    auto result7 = te->PopResult(2000);
    ASSERT_EQ(ct7.ct.GetServerInitialMetadata().find("next-response-codes")->second, "0");
    result7.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id4)
        .CheckTpTriggerPrice("21000")
        .CheckTpLimitPrice("0")
        .CheckTpOrderType(EOrderType::Market);

    SpotInnerReplaceOrderBuilder build8(uid, "BTCUSDT", "", order_link_id4, "20000", "0.3", "");
    auto resp8 = user1.replace_order(build8.Build());
    auto result8 = te->PopResult(2000);
    ASSERT_EQ(resp8.get()->ret_code(), 0);
    result8.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id4)
        .CheckQty(uid, "0.3")
        .CheckTpTriggerPrice("21000")
        .CheckTpLimitPrice("0")
        .CheckTpOrderType(EOrderType::Market);

    // SL清空
    OpenApiV5ReplaceOrderBuilder build9("spot", "BTCUSDT", "", order_link_id4, "0.2", "20000", "", "", "0", "", "");
    RpcContext ct9{};
    ct9.ct.AddMetadata("extension", "{\"eopf\":3}");
    auto resp9 = user1.replace_order(build9.Build(), ct9);
    auto result9 = te->PopResult(2000);
    ASSERT_EQ(ct9.ct.GetServerInitialMetadata().find("next-response-codes")->second, "0");

    // SL改市价单带限价价格
    OpenApiV5ReplaceOrderBuilder build10("spot", "BTCUSDT", "", order_link_id4, "0.2", "20000", "", "", "19000", "",
                                         "20010");
    RpcContext ct10{};
    ct10.ct.AddMetadata("extension", "{\"eopf\":3}");
    auto resp10 = user1.replace_order(build10.Build(), ct10);
    auto result10 = te->PopResult(2000);
    ASSERT_EQ(ct10.ct.GetServerInitialMetadata().find("next-response-codes")->second, "0");

    // TP 改限价价格
    OpenApiV5ReplaceOrderBuilder build11("spot", "BTCUSDT", "", order_link_id4, "0.2", "20000", "", "21000", "",
                                         "21500", "");
    RpcContext ct11{};
    ct11.ct.AddMetadata("extension", "{\"eopf\":3}");
    auto resp11 = user1.replace_order(build11.Build(), ct11);
    auto result11 = te->PopResult(2000);
    ASSERT_EQ(ct11.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170334");
  }
}

TEST_F(SReplaceAoTest, batch_replace_order) {
  biz::user_id_t uid = 10000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  auto order_link_id = te->GenUUID();
  auto order_link_id2 = te->GenUUID();
  auto order_link_id3 = te->GenUUID();
  auto order_link_id4 = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
    build.SetOrderLinkID(order_link_id2);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id2);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    BatchReplaceOrderV5Builder builder("spot");
    builder.AddRequest("BTCUSDT", "", order_link_id, "0.002", "", "");
    builder.AddRequest("BTCUSDT", "", order_link_id2, "0.002", "", "");
    builder.AddRequest("BTCUSDT2", "", order_link_id2, "0.002", "", "");
    RpcContext ct{};
    ct.ct.AddMetadata("limit_rule", "limit_rule");
    ct.ct.AddMetadata("limit_value", "100");
    ct.ct.AddMetadata("items_count", "3");

    auto resp1 = user1.batch_replace_order(builder.Build(), ct);
    ASSERT_EQ(resp1->list().size(), 3);
    std::cout << resp1->ShortDebugString() << std::endl;

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    BatchReplaceOrderV5Builder builder("spot");
    builder.AddRequest("BTCUSDT", "", order_link_id, "0.003", "", "");
    builder.AddRequest("BTCUSDT", "", order_link_id2, "0.003", "", "");
    RpcContext ct{};
    ct.ct.AddMetadata("limit_rule", "limit_rule");
    ct.ct.AddMetadata("limit_value", "1");
    ct.ct.AddMetadata("items_count", "2");

    auto resp1 = user1.batch_replace_order(builder.Build(), ct);
    ASSERT_EQ(resp1->list().size(), 2);
    std::cout << resp1->ShortDebugString() << std::endl;

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    BatchReplaceOrderV5Builder builder("spot");
    builder.AddRequest("BTCUSDT", "", order_link_id, "0.003", "", "");
    builder.AddRequest("BTCUSDT", "", order_link_id2, "0.003", "", "");
    RpcContext ct{};
    ct.ct.AddMetadata("limit_rule", "limit_rule");
    ct.ct.AddMetadata("limit_value", "1");
    ct.ct.AddMetadata("items_count", "2");

    auto resp1 = user1.batch_replace_order(builder.Build(), ct);
    ASSERT_EQ(resp1->list().size(), 2);
    std::cout << resp1->ShortDebugString() << std::endl;

    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);

    result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    BatchReplaceOrderV5Builder builder("spot");
    RpcContext ct{};
    ct.ct.AddMetadata("limit_rule", "limit_rule");
    ct.ct.AddMetadata("limit_value", "10");
    ct.ct.AddMetadata("items_count", "0");

    auto resp1 = user1.batch_replace_order(builder.Build(), ct);
    ASSERT_EQ(resp1->list().size(), 0);

    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }
}

static constexpr const char* kSpotSymbolContent2 =
    R"({"data":[{"allowBargain":0,"allowPlan":1,"allowTrade":1,"banBuyStatus":0,"banSellStatus":0,"baseCoin":"BTC","baseCoinId":1,
    "baseCoinName":"BTC","baseCoinType":1,"basePrecision":10,"brokerId":9001,"crossId":10000,"direction":"","etpNavDeviation":"0","etpRiskSwitchStatus":0,
    "exchangeSymbolId":1,"forbidOpenapiTrade":0,"inleverage":0E-18,"isTest":0,"limitOrderPriceLimitPercentage":"99","limitY":"99","makerBuyFee":"0.00100000",
    "makerSellFee":"0.00100000","marginLoanOpen":1,"marketOrderPriceLimitPercentage":"99","minPricePrecision":"0.0000000001","needPreviewCheck":0,
    "openPrice":0.000000001,"openTime":1679973300000,"settleCoin":"USDT","settleCoinId":5,"settleCoinName":"USDT","settleCoinType":1,
    "settlePrecision":8,"showStatus":1,"showStatusOpenTime":1679973300000,"status":3,"symbolFullName":"BTCUSDT","symbolId":10,"symbolName":"BTCUSDT",
    "symbolType":1,"takerBuyFee":"0.00100000","takerSellFee":"0.00100000", "maxTradeQuantity":"100", "minTradeQuantity":"0.0000000001", "maxTradeAmount":"1000000",
    "minTradeAmount":"0.0000000001"}],"version":"f56ff996-a578-4f2f-9050-95f1b59465cf"})";

TEST_F(SReplaceAoTest, replace_less_qty_value_zero) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent2));
  FutureDepositBuilder deposit_build("replace_less_qty_value_zero", uid, "replace_less_qty_value_zero", 1, 1, "10000",
                                     "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("replace_less_qty_value_zero", uid, "replace_less_qty_value_zero", 5, 1, "10000",
                                      "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  // 1.挂盘口
  auto order_link_id = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.000000001", "0.000000001");
    build.SetOrderLinkID(order_link_id);
    // 限价下单也要校验最小成交额
    RpcContext ct2;
    auto resp = user1.create_order(build.Build(), ct2);
    // auto resp2 = user2.create_order(build1.Build(), ct2);
    ASSERT_EQ(ct2.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170140");
    /*
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.000000001", "0.000000000000000001");
    */
  }
}

TEST_F(SReplaceAoTest, replace_change_price_value_zero_nomatch) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContent2));
  FutureDepositBuilder deposit_build("replace_change_price_value_zero_nomatch", uid,
                                     "replace_change_price_value_zero_nomatch", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build2("replace_change_price_value_zero_nomatch", uid,
                                      "replace_change_price_value_zero_nomatch", 5, 1, "10000", "0");
  auto deposit_resp2 = user1.process(deposit_build2.Build());
  auto deposit_result2 = te->PopResult();
  ASSERT_NE(deposit_result2.m_msg.get(), nullptr);

  // 1.挂盘口
  auto order_link_id = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.000000001", "0.000000001");
    build.SetOrderLinkID(order_link_id);

    // 限价单改单也要判断最小成交金额
    RpcContext ct{};
    auto resp = user1.create_order(build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170140");
    /*
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.000000001", "0.000000000000000001");
   */
  }
}

TEST_F(SReplaceAoTest, replace_normal_ao_with_token_regulated_config) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  auto sc =
      const_cast<config::SpotSymbolDTO*>(config::ConfigProxy::Instance().config_mgr()->spot_client()->QueryBySymbolName(
          "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id()));
  // 初始化TRYUSDT币对
  sc->base_coin_name = "TRY";
  sc->settle_coin_name = "USDT";

  // 初始化TRY币对拦截配置
  auto spot_regulated_token_config_data = std::make_shared<config::SpotRegulatedTokenConfigData>();
  config::ConfigProxy::Instance().config_mgr()->spot_client_sptr()->set_spot_regulated_token_config_data(
      spot_regulated_token_config_data);
  spot_regulated_token_config_data->data.emplace("TRY", std::vector<std::string>{"TUR"});

  // createOrder
  auto order_link_id = te->GenUUID();
  std::basic_string<char> order_id = "";
  {
    RpcContext ct{};
    ct.ct.AddMetadata("x-refer-site-id", "TUR");

    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build(), ct);
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.002", "40");

    order_id = resp->order_id();
  }

  // replace 修改价格 价格改小 从土耳其站点改单成功
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "19995", "", "", "");
    RpcContext ct{};
    ct.ct.AddMetadata("x-refer-site-id", "TUR");
    auto resp1 = user1.replace_order(build.Build(), ct);
    // ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(), ECrossStatus::ReAdded);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "19995");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.002");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_qty(), "0.002");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_amount(), "39.99");
  }

  // replace 从主站点改单失败
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "19995", "", "", "");
    RpcContext ct{};
    ct.ct.AddMetadata("x-refer-site-id", "");
    auto resp1 = user1.replace_order(build.Build(), ct);
    // ASSERT_EQ(resp1->order_link_id(), order_link_id);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170348");
  }
}

TEST_F(SReplaceAoTest, replace_normal_ao_with_show_platform_regulated_config) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  auto sc = const_cast<config::SpotSymbolDTO*>(config::getTlsCfgMgrRaw()->spot_client()->QueryBySymbolName(
      "BTCUSDT", application::GlobalVarManager::Instance().spot_broker_id()));
  // 初始化展示平台
  sc->show_platforms.emplace("TUR");

  // createOrder
  auto order_link_id = te->GenUUID();
  std::basic_string<char> order_id = "";
  {
    RpcContext ct{};
    ct.ct.AddMetadata("user-site-id", "TUR");

    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.002", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build(), ct);
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.002", "40");

    order_id = resp->order_id();
  }

  // replace 修改价格 价格改小 从土耳其站点改单成功
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "19995", "", "", "");
    RpcContext ct{};
    ct.ct.AddMetadata("user-site-id", "TUR");
    auto resp1 = user1.replace_order(build.Build(), ct);
    // ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(), ECrossStatus::ReAdded);
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "19995");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.002");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_qty(), "0.002");
    ASSERT_EQ(result1.m_msg.get()->spot_margin_result().related_spot_orders()[0].leaves_amount(), "39.99");
  }

  // replace 从主站点改单失败
  {
    OpenApiV5ReplaceOrderBuilder build("spot", "BTCUSDT", "", order_link_id, "0.002", "19995", "", "", "");
    RpcContext ct{};
    ct.ct.AddMetadata("user-site-id", "Bybit");
    auto resp1 = user1.replace_order(build.Build(), ct);
    // ASSERT_EQ(resp1->order_link_id(), order_link_id);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170219");
  }
}

static constexpr const char* kSpotSymbolContentForApiReplaceOrder =
    R"({"data":[{"allowBargain":0,"allowPlan":1,"allowTrade":1,"banBuyStatus":0,"banSellStatus":0,"baseCoin":"BTC","baseCoinId":1,
    "baseCoinName":"BTC","baseCoinType":1,"basePrecision":8,"brokerId":9001,"crossId":10000,"direction":"","etpNavDeviation":"0","etpRiskSwitchStatus":0,
    "exchangeSymbolId":1,"forbidOpenapiTrade":0,"inleverage":0E-18,"isTest":0,"limitOrderPriceLimitPercentage":"10000000000000000","makerBuyFee":"0.00100000",
    "makerSellFee":"0.00100000","marginLoanOpen":1,"marketOrderPriceLimitPercentage":"10000000000000000","minPricePrecision":"0.000000000001","needPreviewCheck":0,
    "openPrice":19000,"openTime":1679973300000,"settleCoin":"USDT","settleCoinId":5,"settleCoinName":"USDT","settleCoinType":1,
    "settlePrecision":2,"showStatus":1,"showStatusOpenTime":1679973300000,"status":3,"symbolFullName":"BTCUSDT","symbolId":10,"symbolName":"BTCUSDT",
    "symbolType":1,"takerBuyFee":"0.00100000","takerSellFee":"0.00100000", "maxTradeQuantity":"100", "minTradeQuantity":"0.0000000001", "maxTradeAmount":"1000000",
    "minTradeAmount":"5.0","minTradeAmountApi":"10.0"}],"version":"f56ff996-a578-4f2f-9050-95f1b59465cf"})";
TEST_F(SReplaceAoTest, api_order_with_min_trade_amount_api) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);
  ASSERT_TRUE(config::ConfigProxy::Instance().SpotSymbolReceiveHandler(kSpotSymbolContentForApiReplaceOrder));

  FutureDepositBuilder deposit_build("open_api_v5_create_order2", uid, "open_api_v5_create_order2", 1, 1, "10000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  // createOrder
  auto order_link_id = te->GenUUID();
  {
    RpcContext ct{};
    ct.ct.AddMetadata("user-site-id", "BYBIT");

    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.0006", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build(), ct);
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.0006", "12");

    // 主站API用户 改单 小于minTradeAmountApi 改单被拒
    OpenApiV5ReplaceOrderBuilder build1("spot", "BTCUSDT", "", order_link_id, "0.0005", "19500", "", "", "");
    RpcContext ct1{};
    ct1.ct.AddMetadata("user-site-id", "BYBIT");
    auto resp1 = user1.replace_order(build1.Build(), ct1);
    ASSERT_EQ(ct1.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170140");
  }

  {
    order_link_id = te->GenUUID();
    RpcContext ct{};
    ct.ct.AddMetadata("user-site-id", "BYBIT");
    ct.ct.AddMetadata("extension", R"({"opfrom":"api.xxx"})");

    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.0006", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp = user1.create_order(build.Build(), ct);
    ASSERT_NE(resp->order_link_id(), "");
    auto result = te->PopResult(2000);
    ASSERT_NE(result.m_msg.get(), nullptr);

    result.RefSpotMarginResult()
        .RefRelatedOrderByOrderLinkId(order_link_id)
        .CheckStatus(EOrderStatus::New, ECrossStatus::NewAccepted)
        .Check(uid, "0.0006", "12");

    // broker用户改单时，金额 大于minTradeAmount 小于minTradeAmountApi 改单通过
    OpenApiV5ReplaceOrderBuilder build1("spot", "BTCUSDT", "", order_link_id, "0.0005", "19500", "", "", "");
    RpcContext ct1{};
    auto resp1 = user1.replace_order(build1.Build(), ct1);
    ASSERT_EQ(ct1.ct.GetServerInitialMetadata().find("next-response-codes")->second, "0");
  }
}

std::shared_ptr<tmock::CTradeAppMock> SReplaceAoTest::te;
