#pragma once

#include <gtest/gtest.h>

#include <iostream>
#include <memory>
#include <string>

#include "test/biz/trade/main.hpp"  // NOLINT
#include "test/biz/trade/mocks/trading_app_mock.hpp"

class SCreateAoTest : public testing::Test {
 public:
  void SetUp() override {
    te = std::make_shared<tmock::CTradeAppMock>();
    bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, te);

    te->Init(argument_count, argument_arrary);
    te->Start();
  }
  void TearDown() override {
    auto duration_us = bbase::utils::Time::GetTimeUs() - te->start_time_us;
    std::cout << "duration_us:" << duration_us << std::endl;
#ifdef __APPLE__
    if (duration_us > 0 && duration_us < 2e6) {
      usleep(2e6 - duration_us);
    }
#endif
    te->Stop(true);
    bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);
    te.reset();
  }
  static void SetUpTestSuite() {}
  static void TearDownTestSuite() {}

  static std::shared_ptr<tmock::CTradeAppMock> te;

  static void CheckTaxAttr(const ::models::tradingdto::UnifySpotTransactDTO& order, int32_t tax_rule_id,
                           size_t cum_order_size, size_t extra_trade_size, ESide side, int32_t file_line);
  static void CheckExtraTradeItem(const ::models::tradingdto::ExtraFeeItem& item, int32_t fee_coin,
                                  EExtraFeeType fee_type, EExtraFeeSubType sub_fee_type, int64_t extra_fee_rate_e8,
                                  std::string extra_fee, int32_t file_line);
};
