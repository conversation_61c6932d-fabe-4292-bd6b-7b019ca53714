#include "cancel_ao.hpp"  // NOLINT

#include "lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(SCancelAoTest, open_v5_cancel_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_v5_cancel_order", uid, "open_v5_cancel_order", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);

  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // std::string orderId  = result.m_msg->mutable_spot_margin_result()->related_spot_orders(0).order_id().c_str();
  OpenApiV5CancelOrderBuilder cancel_build("spot", "BTCUSDT", "", order_link_id, "order");
  auto resp2 = user1.cancel_order(cancel_build.Build());
  auto result2 = te->PopResult(50000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
}

TEST_F(SCancelAoTest, open_v5_cancel_reject_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_v5_cancel_reject_order", uid, "open_v5_cancel_reject_order", 1, 1, "100",
                                     "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  FutureDepositBuilder deposit_build1("open_v5_cancel_reject_order1", uid, "open_v5_cancel_reject_order1", 5, 1,
                                      "1000000", "0");

  deposit_resp = user1.process(deposit_build1.Build());
  deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  // 停止cross
  te->SuspendCross();
  te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);
  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);

  OpenApiV5CreateOrderBuilder build2("spot", "BTCUSDT", 0, "Buy", "Limit", "0.001", "20000");
  auto order_link_id2 = te->GenUUID();
  build2.SetOrderLinkID(order_link_id2);
  auto resp2 = user1.create_order(build2.Build());
  ASSERT_EQ(resp2->order_link_id(), order_link_id2);

  {
    OpenApiV5ReplaceOrderBuilder build3("spot", "BTCUSDT", "", order_link_id, "0.003", "19990", "", "", "");
    auto resp3 = user1.replace_order(build3.Build());
    ASSERT_EQ(resp3->order_link_id(), order_link_id);
  }

  // std::string orderId  = result.m_msg->mutable_spot_margin_result()->related_spot_orders(0).order_id().c_str();
  OpenApiV5CancelOrderBuilder cancel_build("spot", "BTCUSDT", "", order_link_id, "order");
  auto resp3 = user1.cancel_order(cancel_build.Build());

  // 恢复cross
  te->ResumeCross();

  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].order_status(), EOrderStatus::New);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(),
            ECrossStatus::NewAccepted);
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().size(), 2);
  for (auto i = 0; i < result.m_msg.get()->spot_margin_result().related_spot_orders().size(); i++) {
    auto item = result.m_msg.get()->spot_margin_result().related_spot_orders()[0];
    if (item.order_link_id() == order_link_id2) {
      ASSERT_EQ(item.order_status(), EOrderStatus::Filled);
      ASSERT_EQ(item.cross_status(), ECrossStatus::TakerFill);
      ASSERT_EQ(item.qty(), "0.001");
      ASSERT_EQ(item.price(), "20000");
    } else {
      ASSERT_EQ(item.order_status(), EOrderStatus::Filled);
      ASSERT_EQ(item.cross_status(), ECrossStatus::MakerFill);
    }
  }
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].order_status(), EOrderStatus::Filled);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(),
            ECrossStatus::ReplaceRejected);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.001");
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "20000");
  result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders().size(), 1);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].order_status(), EOrderStatus::Filled);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].cross_status(),
            ECrossStatus::CancelRejected);
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].qty(), "0.001");
  ASSERT_EQ(result.m_msg.get()->spot_margin_result().related_spot_orders()[0].price(), "20000");
}

TEST_F(SCancelAoTest, site_api_cancel_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("site_api_cancel_order", uid, "site_api_cancel_order", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);

  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // std::string orderId  = result.m_msg->mutable_spot_margin_result()->related_spot_orders(0).order_id().c_str();
  SpotSiteApiCancelOrderBuilder cancel_build("", order_link_id);
  auto resp2 = user1.cancel_order(cancel_build.Build());
  auto result2 = te->PopResult(50000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
}

TEST_F(SCancelAoTest, site_api_merge_cancel_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("site_api_merge_cancel_order", uid, "site_api_merge_cancel_order", 1, 1, "100",
                                     "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);

  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // std::string orderId  = result.m_msg->mutable_spot_margin_result()->related_spot_orders(0).order_id().c_str();
  SpotSiteApiMergeCancelOrderBuilder cancel_build("", order_link_id, "order");
  auto resp2 = user1.cancel_order(cancel_build.Build());
  auto result2 = te->PopResult(50000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
}

TEST_F(SCancelAoTest, inner_api_cancel_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("inner_api_cancel_order", uid, "inner_api_cancel_order", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);

  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // std::string orderId  = result.m_msg->mutable_spot_margin_result()->related_spot_orders(0).order_id().c_str();
  SpotInnerApiCancelOrderBuilder cancel_build("", order_link_id);
  cancel_build.SetUserId(uid);

  auto resp2 = user1.cancel_order(cancel_build.Build());
  auto result2 = te->PopResult(50000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
}

TEST_F(SCancelAoTest, inner_api_cancel_order_with_reply_after_cross) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("inner_api_cancel_order", uid, "inner_api_cancel_order", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);

  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // std::string orderId  = result.m_msg->mutable_spot_margin_result()->related_spot_orders(0).order_id().c_str();
  SpotInnerApiCancelOrderBuilder cancel_build("", order_link_id);
  cancel_build.SetUserId(uid);
  cancel_build.SetNeedReplyAfterCross(true);

  auto resp2 = user1.cancel_order(cancel_build.Build());
  auto result2 = te->PopResult(50000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
}

TEST_F(SCancelAoTest, cancel_pending_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("cancel_pending_order", uid, "cancel_pending_order", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->SuspendCross();
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);

  OpenApiV5CancelOrderBuilder cancel_build("spot", "BTCUSDT", "", order_link_id, "order");
  auto resp2 = user1.cancel_order(cancel_build.Build());

  te->ResumeCross();

  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
}

TEST_F(SCancelAoTest, double_cancel_order) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("cancel_pending_order", uid, "cancel_pending_order", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  te->SuspendCross();
  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);

  {
    OpenApiV5CancelOrderBuilder cancel_build("spot", "BTCUSDT", "", order_link_id, "order");
    auto resp2 = user1.cancel_order(cancel_build.Build());
  }

  {
    OpenApiV5CancelOrderBuilder cancel_build("spot", "BTCUSDT", "", order_link_id, "order");
    RpcContext ct{};
    auto resp2 = user1.cancel_order(cancel_build.Build(), ct);
    ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "170212");
  }

  te->ResumeCross();

  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  auto result2 = te->PopResult(5000);
  ASSERT_NE(result2.m_msg.get(), nullptr);
}

TEST_F(SCancelAoTest, cancel_order_fail) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  {
    OpenApiV5CancelOrderBuilder cancel_build("spot", "BTCUSDT", "", "", "order11");
    auto resp1 = user1.cancel_order(cancel_build.Build());
    ASSERT_EQ(resp1->order_link_id(), "");
  }

  {
    OpenApiV5CancelOrderBuilder cancel_build("spot", "BTCUSDT", "", "", "order");
    auto resp1 = user1.cancel_order(cancel_build.Build());
    ASSERT_EQ(resp1->order_link_id(), "");
  }

  {
    OpenApiV5CancelOrderBuilder cancel_build("spot", "BTCUSDT", "", "order_link_id", "order");
    auto resp1 = user1.cancel_order(cancel_build.Build());
    ASSERT_EQ(resp1->order_link_id(), "");
  }

  {
    OpenApiV5CancelOrderBuilder cancel_build("spot", "BTCUSDT", "order_id", "", "order");
    auto resp1 = user1.cancel_order(cancel_build.Build());
    ASSERT_EQ(resp1->order_link_id(), "");
  }
}

TEST_F(SCancelAoTest, open_v5_cancel_order_invalid_order_type) {
  biz::user_id_t uid = 100000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_v5_cancel_order", uid, "open_v5_cancel_order", 1, 1, "100", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
  auto order_link_id = te->GenUUID();
  build.SetOrderLinkID(order_link_id);

  auto resp1 = user1.create_order(build.Build());
  ASSERT_EQ(resp1->order_link_id(), order_link_id);

  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);

  // std::string orderId  = result.m_msg->mutable_spot_margin_result()->related_spot_orders(0).order_id().c_str();
  SpotSiteApiMergeCancelOrderBuilder cancel_build("", order_link_id, "planOrder");
  RpcContext ct{};
  auto resp2 = user1.cancel_order(cancel_build.Build(), ct);
  ASSERT_EQ(ct.ct.GetServerInitialMetadata().find("next-response-codes")->second, "32004");
}

TEST_F(SCancelAoTest, batch_cancel_ordeer) {
  biz::user_id_t uid = 10000;
  stub user1(te, uid);

  FutureDepositBuilder deposit_build("open_v5_cancel_order", uid, "open_v5_cancel_order", 1, 1, "1000", "0");
  auto deposit_resp = user1.process(deposit_build.Build());
  auto deposit_result = te->PopResult();
  ASSERT_NE(deposit_result.m_msg.get(), nullptr);

  auto order_link_id = te->GenUUID();
  auto order_link_id2 = te->GenUUID();
  auto order_link_id3 = te->GenUUID();
  auto order_link_id4 = te->GenUUID();
  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
    build.SetOrderLinkID(order_link_id);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
    build.SetOrderLinkID(order_link_id2);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id2);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    BatchCancelOrderV5Builder builder("spot");
    builder.AddRequest("BTCUSDT", "", order_link_id);
    builder.AddRequest("BTCUSDT", "", order_link_id2);
    builder.AddRequest("BTCUSDT", "", order_link_id3);
    builder.AddRequest("BTCUSDT1", "", order_link_id3);
    RpcContext ct{};
    ct.ct.AddMetadata("limit_rule", "limit_rule");
    ct.ct.AddMetadata("limit_value", "100");
    ct.ct.AddMetadata("items_count", "4");

    auto resp1 = user1.batch_cancel_order(builder.Build(), ct);
    ASSERT_EQ(resp1->list().size(), 4);
    std::cout << resp1->ShortDebugString() << std::endl;

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
    build.SetOrderLinkID(order_link_id3);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id3);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.001", "20000");
    build.SetOrderLinkID(order_link_id4);

    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id4);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    BatchCancelOrderV5Builder builder("spot");
    builder.AddRequest("BTCUSDT", "", order_link_id3);
    builder.AddRequest("BTCUSDT", "", order_link_id3);
    RpcContext ct{};
    ct.ct.AddMetadata("limit_rule", "limit_rule");
    ct.ct.AddMetadata("limit_value", "1");
    ct.ct.AddMetadata("items_count", "2");

    auto resp1 = user1.batch_cancel_order(builder.Build(), ct);
    ASSERT_EQ(resp1->list().size(), 2);
    std::cout << resp1->ShortDebugString() << std::endl;

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    BatchCancelOrderV5Builder builder("spot");
    RpcContext ct{};
    ct.ct.AddMetadata("limit_rule", "limit_rule");
    ct.ct.AddMetadata("limit_value", "10");
    ct.ct.AddMetadata("items_count", "0");

    auto resp1 = user1.batch_cancel_order(builder.Build(), ct);
    ASSERT_EQ(resp1->list().size(), 0);

    auto result = te->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }
}

std::shared_ptr<tmock::CTradeAppMock> SCancelAoTest::te;
