#include "data/type/biz_type.hpp"
#include "lib/msg_builder.hpp"
#include "liquidation/liquidation_test.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(LiquidationTest, liq_celan_isolated) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "2050", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 切换逐仓模式
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_margin_mode(true);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切换逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    te->PopResult();
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18080, 18080, 18080);
  }

  {
    te->SuspendCross();

    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    sharding_key = utils::hash(static_cast<std::uint64_t>(uid2) /
                               static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
                   application::GlobalConfig::re_calc_total_user_sharding();
    event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  }

  {
    biz::symbol_t symbol = 5;  // BTCUSDT
    biz::coin_t coin = 5;
    EPositionIndex pz_idx = EPositionIndex::Single;
    int64_t origin_cross_seq = 3;

    FutureOneOfLiqCleanBuilder liq_clean(symbol, coin, uid1, pz_idx, origin_cross_seq);
    auto resp1 = user1.process(liq_clean.Build());
  }

  {
    biz::symbol_t symbol = 5;  // BTCUSDT
    biz::coin_t coin = 5;
    EPositionIndex pz_idx = EPositionIndex::Single;
    int64_t origin_cross_seq = 1;

    FutureOneOfLiqCleanBuilder liq_clean(symbol, coin, uid1, pz_idx, origin_cross_seq);
    auto resp1 = user1.process(liq_clean.Build());
    te->ResumeCross();
  }

  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "liq clean:" << jsonStr2 << std::endl;
  }

  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "接管结果:" << jsonStr2 << std::endl;
  }
}

TEST_F(LiquidationTest, liq_celan) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  openAccount(uid1);
  openAccount(uid2);

  // 账户充值
  depositAccount(uid1, ECoin::USDT, "50000");
  depositAccount(uid2, ECoin::USDT, "50000");

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  // 调整风险限额
  increaseRiskLimit(uid1, ESymbol::BTCUSDT, ECoin::USDT, 2);
  increaseRiskLimit(uid2, ESymbol::BTCUSDT, ECoin::USDT, 2);

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Taker:" << jsonStr2 << std::endl;
  }

  {
    // 等待成交回报
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "成交回报Maker:" << jsonStr2 << std::endl;
  }

  {
    auto order_link_id_1 = te->GenUUID();
    CreateOrderBuilder create_ao_builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "1", "2000");
    create_ao_builder.SetOrderLinkID(order_link_id_1);
    auto resp = user2.create_order(create_ao_builder.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期权订单:" << jsonStr2 << std::endl;
  }

  {
    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("20000"));
    // 创建现货订单
    auto order_link_id_1 = te->GenUUID();
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(order_link_id_1);
    RpcContext ct{};
    auto resp1 = user2.create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), 0);
    ASSERT_EQ(ct.RetMsg(), "OK");
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);
    auto result = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货订单:" << jsonStr << std::endl;
  }

  te->SuspendCross();

  // 触发强平取消开仓单
  {
    std::cout << "定时盈亏1:" << std::endl;
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    sharding_key = utils::hash(static_cast<std::uint64_t>(uid2) /
                               static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
                   application::GlobalConfig::re_calc_total_user_sharding();
    event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    usleep(1000);
  }

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 5000, 5000, 5000);

  // 触发强平取消开仓单
  {
    std::cout << "定时盈亏2:" << std::endl;

    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    usleep(1000);
  }

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 5000, 5000, 5000);

  te->ResumeCross();

  {
    UTALiqCleanUpReqBuilder liq_clean_build("", uid1);
    auto msg = liq_clean_build.Build();
    auto resp = user1.process(msg);
    ASSERT_EQ(resp->ret_code(), 0);
  }

  {
    auto result = te->PopResult(1000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "强平执行结果Result:" << jsonStr2 << std::endl;
  }

  {
    auto result = te->PopResult(1000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "重制账户状态Result:" << jsonStr2 << std::endl;
  }

  // 触发强平取消开仓单
  {
    std::cout << "定时盈亏2:" << std::endl;

    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "强平结果2:" << jsonStr2 << std::endl;

    while (true) {
      result = te->PopResult(1000);
      if (result.m_msg == nullptr) {
        break;
      }

      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "强平cancel结果:" << jsonStr2 << std::endl;
    }
  }
}
