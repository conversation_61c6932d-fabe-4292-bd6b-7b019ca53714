#include <algorithm>
#include <memory>
#include <string>
#include <vector>

#include "src/data/enum.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/liquidation/liq_test.hpp"

/**
 * 测试含有 futureSpread 组合单并处于成交中间状态时的强平行为
 *
 * 测试场景：
 * 验证当用户持有 futureSpread 组合单并处于成交中间状态时，组合账户模式下的强平行为
 *
 * 预期结果：
 * 1. 组合模式下不能进入强平
 */
TEST_F(TestLiq, PM_FutureSpreadLiq) {
  auto u1 =
      TestLiqHelper::InitTestUser(te_, 70001, EAccountMode::Portfolio, {{ECoin::USDT, "100000"}, {ECoin::BTC, "3"}});
  auto u2 =
      TestLiqHelper::InitTestUser(te_, 70002, EAccountMode::Portfolio, {{ECoin::USDT, "100000"}, {ECoin::BTC, "3"}});

  // 设置价格
  {
    // price
    te_->AddMarkPrice(ESymbol::BTCUSDT, 70000, 70000, 70000);
  }

  // u1 下期货单构造仓位 3 * 70000
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "3", "70000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp = u1->create_order(build.Build());
    ASSERT_NE(resp->order_id(), "");
    auto result = te_->PopResult();
    ASSERT_TRUE(result.m_msg);
  }

  // u2 下期货单构造仓位 -3 * 70000
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "3", "70000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp = u2->create_order(build.Build());
    ASSERT_NE(resp->order_id(), "");

    // u2 taker成交
    auto result = te_->PopResult();
    ASSERT_TRUE(result.m_msg);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1 maker 形成仓位
  {
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // 设置价格 BTCUSDT 70000 BTCUSD 70000
  {
    te_->BatchAddMarkPrice({{ESymbol::BTCUSDT, 70000, 70000, 70000}, {ESymbol::BTCUSD, 70000, 70000, 70000}});
    te_->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("70000"));
  }

  // u1 挂 futureSpread 组合单
  {
    biz::order_link_id_t m_order_link_id{te_->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Buy", "Limit", "GTC", "100", "3");
    RpcContext ct;
    auto m_resp = u1->comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  {
    // 设置单步模式
    te_->SetCrossSingleStepMode();
  }

  {
    biz::order_link_id_t m_order_link_id{te_->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Sell", "Limit", "GTC", "100", "1");
    RpcContext ct;
    auto m_resp = u2->comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);
  }

  // 处理一个撮合消息，来自futureSpread组合单对应的撮合
  {
    te_->ProcessOneXReq(te_->GetCombCrossIdx());

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_GT(result.m_msg->combination_margin_result().related_combination_orders_size(), 0);
    const auto& comb_order = result.m_msg->combination_margin_result().related_combination_orders().begin();
    ASSERT_EQ(comb_order->order_status(), ::enums::ecomborderstatus::WaitToRecvPassthrough);
    ASSERT_EQ(comb_order->cross_status(), ::enums::ecombcrossstatus::WaitToSetFill);
  }

  // 设置价格 BTCUSDT 17783 BTCUSD 17783
  {
    te_->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 17783, 17783, 17783}, {static_cast<ESymbol>(45), 17783, 17783, 17783}});
    te_->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(17783));

    // ASSERT_TRUE(TestLiqHelper::Withdraw(&u1, ECoin::USDT, "18000"));
    // ASSERT_TRUE(TestLiqHelper::Withdraw(&u1, ECoin::BTC, "2"));
  }

  // ASSERT_TRUE(TestLiqHelper::AdjustUserMMROverflow(u1->get()));

  // 大计算 触发强平流程。 预期被拦住
  {
    // 发送大计算请求事件
    ASSERT_TRUE(u1->ReCalcEvent(false));
    ASSERT_TRUE(u1->ReCalcEvent(true));
  }

  // 获取用户数据 并校验imr/mmr 预期MMR溢出但是没有触发强平
  {
    auto resp = u1->DebugUserData();
    TestLiqHelper::PrintPb(resp);
    ASSERT_TRUE(resp->has_user_store_data());
    ASSERT_TRUE(resp->user_store_data().has_user_data());
    ASSERT_EQ(resp->user_store_data().user_data().account_info().account_status(),
              ::enums::eaccountstatus::AccountStatus::Normal);
    auto unify_wallet_unit_str = resp->user_store_data().user_data().unify_wallet_unit_();
    fmt::print(stderr, "unify_wallet_unit_str:{}\n", unify_wallet_unit_str);
    ASSERT_TRUE(unify_wallet_unit_str.find("imr:100,mmr:100,") != std::string::npos);
  }

  // 使用再次触发强平流程的方式来校验没有进行强平流程
  {
    // 发送大计算请求事件
    ASSERT_TRUE(u1->ReCalcEvent(true));
  }

  // 获取用户数据 并校验imr/mmr 预期MMR溢出但是没有触发强平
  {
    auto resp = u1->DebugUserData();
    TestLiqHelper::PrintPb(resp);
    ASSERT_TRUE(resp->has_user_store_data());
    ASSERT_TRUE(resp->user_store_data().has_user_data());
    ASSERT_EQ(resp->user_store_data().user_data().account_info().account_status(),
              ::enums::eaccountstatus::AccountStatus::Normal);
    auto unify_wallet_unit_str = resp->user_store_data().user_data().unify_wallet_unit_();
    fmt::print(stderr, "unify_wallet_unit_str:{}\n", unify_wallet_unit_str);
    ASSERT_TRUE(unify_wallet_unit_str.find("imr:100,mmr:100,") != std::string::npos);
  }

  {
    // 单步处理
    te_->ProcessOneXReq(te_->GetSpotCrossIdx());

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  {
    // 取消单步模式
    te_->UnSetCrossSingleStepMode();

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // 发送大计算请求事件
  {
    ASSERT_TRUE(u1->ReCalcEvent(false));
    ASSERT_TRUE(u1->ReCalcEvent(true));
  }

  // 执行强平
  {
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_EQ(result.m_msg->asset_margin_result().account_info().account_status(),
              ::enums::eaccountstatus::AccountStatus::PendingLiq);
    // CancelAllOrders
    ASSERT_EQ(result.m_msg->asset_margin_result().account_info().liq_step(), 4);
  }
}

/**
 * 测试含有 futureSpread 组合单并处于成交中间状态时的强平行为(500档)
 *
 * 测试场景：
 * 验证当用户持有 futureSpread 组合单并处于成交中间状态时，组合账户模式下的强平行为
 *
 * 预期结果：
 * 1. 成交中间时组合模式下不能进入强平
 */
TEST_F(TestLiq, PM_500_FutureSpreadLiq) {
  auto u1 =
      TestLiqHelper::InitTestUser(te_, 70001, EAccountMode::Portfolio, {{ECoin::USDT, "100000"}, {ECoin::BTC, "3"}});
  auto u2 =
      TestLiqHelper::InitTestUser(te_, 70002, EAccountMode::Portfolio, {{ECoin::USDT, "100000"}, {ECoin::BTC, "3"}});

  // 设置u1为500档用户
  {
    UserSettingLiqLevelBuilder builder(u1->m_uid, 500);
    auto resp = u1->process(builder.Build());
    TestLiqHelper::PrintPb(resp);
    auto mq_resp = te_->PopResult();
    mq_resp.CheckUid(u1->m_uid);
    EXPECT_EQ(500, mq_resp.RefAssetMarginResult().RefRelatedUserSetting().m_msg->trigger_liq_level());
  }

  te_->AddMarkPrice(ESymbol::BTCUSDT, 70000, 70000, 70000);

  // u1 下期货单构造仓位 3 * 70000
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "3", "70000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp = u1->create_order(build.Build());
    ASSERT_NE(resp->order_id(), "");
    auto result = te_->PopResult();
    ASSERT_TRUE(result.m_msg);
  }

  // u2 下期货单构造仓位 -3 * 70000
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "3", "70000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp = u2->create_order(build.Build());
    ASSERT_NE(resp->order_id(), "");

    // u2 taker成交
    auto result = te_->PopResult();
    ASSERT_TRUE(result.m_msg);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1 maker 形成仓位
  {
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  {
    te_->BatchAddMarkPrice({{ESymbol::BTCUSDT, 70000, 70000, 70000}, {ESymbol::BTCUSD, 70000, 70000, 70000}});
    te_->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("70000"));
  }

  // u1 挂 futureSpread 组合单
  {
    biz::order_link_id_t m_order_link_id{te_->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Buy", "Limit", "GTC", "100", "3");
    RpcContext ct;
    auto m_resp = u1->comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  {
    // 设置单步模式
    te_->SetCrossSingleStepMode();
  }

  {
    biz::order_link_id_t m_order_link_id{te_->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Sell", "Limit", "GTC", "100", "1");
    RpcContext ct;
    auto m_resp = u2->comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);
  }

  // 处理一个撮合消息，来自futureSpread组合单对应的撮合
  {
    // 单步处理
    te_->ProcessOneXReq(te_->GetCombCrossIdx());
  }

  {
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_GT(result.m_msg->combination_margin_result().related_combination_orders_size(), 0);
    const auto& comb_order = result.m_msg->combination_margin_result().related_combination_orders().begin();
    ASSERT_EQ(comb_order->order_status(), ::enums::ecomborderstatus::WaitToRecvPassthrough);
    ASSERT_EQ(comb_order->cross_status(), ::enums::ecombcrossstatus::WaitToSetFill);
  }

  {
    te_->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 17783, 17783, 17783}, {static_cast<ESymbol>(45), 17783, 17783, 17783}});
    te_->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(17783));

    // ASSERT_TRUE(TestLiqHelper::Withdraw(&u1, ECoin::USDT, "18000"));
    // ASSERT_TRUE(TestLiqHelper::Withdraw(&u1, ECoin::BTC, "2"));
    ASSERT_TRUE(u1->ReCalcEvent(true));
  }

  // ASSERT_TRUE(TestLiqHelper::AdjustUserMMROverflow(u1->get()));

  // 大计算 触发强平流程。 预期被拦住
  {
    // 发送大计算请求事件
    ASSERT_TRUE(u1->ReCalcEvent(true));
  }

  // 获取用户数据 并校验imr/mmr 预期MMR溢出但是没有触发强平
  {
    auto resp = u1->DebugUserData();
    TestLiqHelper::PrintPb(resp);
    ASSERT_TRUE(resp->has_user_store_data());
    ASSERT_TRUE(resp->user_store_data().has_user_data());
    ASSERT_EQ(resp->user_store_data().user_data().account_info().account_status(),
              ::enums::eaccountstatus::AccountStatus::Normal);
    auto unify_wallet_unit_str = resp->user_store_data().user_data().unify_wallet_unit_();
    fmt::print(stderr, "unify_wallet_unit_str:{}\n", unify_wallet_unit_str);
    ASSERT_TRUE(unify_wallet_unit_str.find("imr:100,mmr:100,") != std::string::npos);
  }

  // 使用再次触发强平流程的方式来校验没有进行强平流程
  {
    // 发送大计算请求事件
    ASSERT_TRUE(u1->ReCalcEvent(true));
  }

  // 获取用户数据 并校验imr/mmr 预期MMR溢出但是没有触发强平
  {
    auto resp = u1->DebugUserData();
    // Print(*resp);
    ASSERT_TRUE(resp->has_user_store_data());
    ASSERT_TRUE(resp->user_store_data().has_user_data());
    ASSERT_EQ(resp->user_store_data().user_data().account_info().account_status(),
              ::enums::eaccountstatus::AccountStatus::Normal);
    auto unify_wallet_unit_str = resp->user_store_data().user_data().unify_wallet_unit_();
    fmt::print(stderr, "unify_wallet_unit_str:{}\n", unify_wallet_unit_str);
    ASSERT_TRUE(unify_wallet_unit_str.find("imr:100,mmr:100,") != std::string::npos);
  }

  {
    // 单步处理
    te_->ProcessOneXReq(te_->GetSpotCrossIdx());
  }

  {
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  {
    // 取消单步模式
    te_->UnSetCrossSingleStepMode();
  }

  {
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // 让其进入延迟强平等待逻辑 需要长时间等待
  /*{
    ASSERT_TRUE(u1->ReCalcEvent(false));
    ASSERT_TRUE(u1->ReCalcEvent(true));
  }

  // 执行强平 500档延迟强平 等待
  {
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_EQ(result.m_msg->asset_margin_result().account_info().account_status(),
              ::enums::eaccountstatus::AccountStatus::Normal);
    // DelayLiqWait
    ASSERT_EQ(result.m_msg->asset_margin_result().account_info().liq_step(), 10);
  }*/

  // 执行延迟强平 (跳过等待时间进入强平逻辑)
  {
    auto event = std::make_shared<event::OnLiqEvent>(u1->m_uid);
    event->execute_action = ELiqStep::DelayLiqWait;
    te_->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // 延迟强平执行(撤销所有订单)
  {
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_EQ(result.m_msg->header().action(), EAction::LiqCoinExecute);
  }
}

/**
 * 测试手动强平接管功能
 *
 * 测试场景：
 * 1. 验证含有 futureSpread 组合单时的手动强平撤单逻辑
 * 2. 验证在撤全部(开仓)单/全部单中会卡住状态的情况（组合/组合延迟强平）
 *
 * 预期结果：
 * - 正确处理 futureSpread 组合单的撤单逻辑
 * - 在特定条件下，强平流程会卡在撤单状态
 */
TEST_F(TestLiq, PM_RiskClosePosition) {
  auto u1 =
      TestLiqHelper::InitTestUser(te_, 70001, EAccountMode::Portfolio, {{ECoin::USDT, "70000"}, {ECoin::BTC, "10"}});
  auto u2 =
      TestLiqHelper::InitTestUser(te_, 70002, EAccountMode::Portfolio, {{ECoin::USDT, "70000"}, {ECoin::BTC, "10"}});

  // 设置价格
  {
    te_->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 30000, 30000, 30000}, {static_cast<ESymbol>(45), 30000, 30000, 30000}});
    // te_->MockSpotLastPriceToPreEngine("BTC/USDT", bbase::decimal::Decimal<>(30000));
  }

  // u1创建futureSpread组合单
  {
    CombSiteCreateOrderBuilder builder("PERP-SPOT", biz::order_link_id_t(te_->GenUUID()), "Buy", "Limit", "GTC", "10",
                                       "3");
    RpcContext rpc_ctx{};
    auto resp = u1->comb_siteapi_create_order(builder.Build(), rpc_ctx);
    ASSERT_NE(nullptr, resp);
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), builder.msg.order_link_id());

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_GT(result.m_msg->combination_margin_result().related_combination_orders_size(), 0);
  }

  // u1创建期货订单用于构建仓位
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "30000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp1 = u1->create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u2创建期货订单用于构建仓位
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "30000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp1 = u2->create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    // taker
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    // u1 maker 成交
    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1 挂一个期货单
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "30000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp1 = u1->create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  te_->BatchAddMarkPrice(
      {{static_cast<ESymbol>(5), 10000, 10000, 10000}, {static_cast<ESymbol>(45), 10000, 10000, 10000}});
  te_->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(10000));

  // u1创建期权订单用于构造仓位
  {
    auto order_link_id_1 = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id_1);
    auto resp = u1->create_order(builder.Build());
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u2创建期权订单并作为taker成交
  {
    auto order_link_id = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Sell", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id);
    auto resp = u2->create_order(builder.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id);
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1挂一个期权订单
  {
    auto order_link_id_1 = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id_1);
    auto resp = u1->create_order(builder.Build());
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1挂一个现货订单
  {
    auto order_link_id_1 = te_->GenUUID();
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.1", "10000");
    build.SetOrderLinkID(order_link_id_1);
    RpcContext ct{};
    auto resp1 = u1->create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), 0);
    ASSERT_EQ(ct.RetMsg(), "OK");
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // 获取用户数据
  {
    auto resp = u1->DebugUserData();
    TestLiqHelper::PrintPb(resp);
  }

  // 大计算
  {
    // 发起大计算请求事件，会执行全部用户
    ASSERT_TRUE(u1->ReCalcEvent(true));
  }

  // 发RiskClosePz请求，预期失败，原因是不满足手动接管条件
  {
    auto m_rsp = u1->RiskClosePz();
    ASSERT_TRUE(m_rsp);
    TestLiqHelper::PrintPb(m_rsp);
    ASSERT_TRUE(m_rsp->ret_code() == -1 && m_rsp->has_header() && m_rsp->header().user_id() == u1->m_uid &&
                m_rsp->header().action() == EAction::RiskClosePz);
    ASSERT_EQ(m_rsp->ret_msg(), "mm less than 160");
  }

  // 手动接管条件是mmr >= 1.6 或者"/liq/manualLiqWhiteList"(","分隔)有此uid
  {
    auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
        bbase::object_manager::ObjectType::kObjectConfigCenter);
    nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                               "/liq/manualLiqWhiteList", std::to_string(u1->m_uid));
  }

  // 发RiskClosePz，设置account_action为ELiqStep::RiskClosePositions
  {
    auto m_rsp = u1->RiskClosePz();
    ASSERT_TRUE(m_rsp);
    TestLiqHelper::PrintPb(m_rsp);
    ASSERT_TRUE(m_rsp->ret_code() == 0 && m_rsp->has_header() && m_rsp->header().action() == EAction::RiskClosePz);
  }

  // 暂停撮合
  te_->SuspendCross();

  // 从大计算进入RiskClosePz触发事件继续流程
  ASSERT_TRUE(u1->ReCalcEvent(true));

  // 撮合暂停，此时不会有撮合回报
  ASSERT_EQ(te_->PopResult().m_msg, nullptr);

  // 获取用户数据
  {
    auto resp = u1->DebugUserData();
    TestLiqHelper::PrintPb(resp);
  }

  // 恢复撮合
  te_->ResumeCross();

  // 从大计算进入RiskClosePz触发事件继续流程
  {
    auto m_rsp = te_->PopResult();
    ASSERT_NE(m_rsp.m_msg, nullptr);
    auto m = m_rsp.m_msg;
    ASSERT_TRUE(m->ret_code() == 0 && m->has_header());
    ASSERT_TRUE(m->header().action() == EAction::LiqCoinExecute);
    ASSERT_TRUE(m->asset_margin_result().account_info().account_status() ==
                ::enums::eaccountstatus::AccountStatus::Liq);
    ASSERT_TRUE(m->asset_margin_result().account_info().liq_step() == ELiqStep::RiskClosePositions);
    TestLiqHelper::PrintPb(m);

    std::vector<std::shared_ptr<svc::unified_v2::UnifiedV2ResultDTO>> rsp_vec;
    for (auto rsp = te_->PopResult(); rsp.m_msg != nullptr; rsp = te_->PopResult()) {
      TestLiqHelper::PrintPb(rsp.m_msg);
      rsp_vec.push_back(rsp.m_msg);
    }
    /*
    1. 撮合回报 - 组合单撤单
    2. 撮合回报 - 期权撤单
    3. 撮合回报 - 现货撤单
    4. 撮合回报 - 期货撤单
    5. 撤单结束 - 继续强平流程(LiqCoinExecute) 接管
    6. 撮合回报 - 现货接管
    7. TR撮合回报 - 现货接管
    8. 撮合回报 - 期权接管
    9. TR撮合回报 - 期权接管
    10. 撮合回报 - 期货接管
    11. TR撮合回报 - 期货接管
    12. 撮合回报 - 现货接管
    13. 撮合回报 - 继续强平流程(LiqCoinExecute) 接管结束
    14. TR撮合回报 - 现货接管
    <******** 增加中间接管户>
    */
    ASSERT_EQ(rsp_vec.size(), 18);

    // 校验接管的撮合包在接管户之前，且最后账户恢复正常
    auto tr_user_id = application::GlobalVarManager::Instance().tr_user().first;

    auto find_first_match = [&rsp_vec](const auto& predicate) -> int {
      for (size_t i = 0; i < rsp_vec.size(); i++) {
        if (predicate(rsp_vec[i])) {
          return static_cast<int>(i);
        }
      }
      return -1;
    };

    auto user_idx_comb_order = find_first_match([&u1](const auto& msg) {
      return msg->header().user_id() == u1->m_uid && msg->has_combination_margin_result() &&
             msg->combination_margin_result().related_combination_orders_size() > 0;
    });
    auto user_idx_future_order = find_first_match([&u1](const auto& msg) {
      return msg->header().user_id() == u1->m_uid && msg->has_futures_margin_result() &&
             msg->futures_margin_result().related_orders_size() > 0;
    });
    auto user_idx_option_order = find_first_match([&u1](const auto& msg) {
      return msg->header().user_id() == u1->m_uid && msg->has_options_margin_result() &&
             msg->options_margin_result().related_orders_size() > 0;
    });
    auto user_idx_spot_order = find_first_match([&u1](const auto& msg) {
      return msg->header().user_id() == u1->m_uid && msg->has_spot_margin_result() &&
             msg->spot_margin_result().related_spot_orders_size() > 0;
    });
    int user_idx_future = find_first_match([&u1](const auto& msg) {
      return msg->has_futures_margin_result() && msg->header().user_id() == u1->m_uid &&
             msg->futures_margin_result().related_fills_size() > 0;
    });
    int tr_idx_future = find_first_match([tr_user_id](const auto& msg) {
      return msg->has_futures_margin_result() && msg->header().user_id() == tr_user_id;
    });
    auto user_idx_option = find_first_match([&u1](const auto& msg) {
      return msg->has_options_margin_result() && msg->header().user_id() == u1->m_uid &&
             msg->options_margin_result().related_fills_size() > 0;
    });
    auto tr_idx_option = find_first_match([tr_user_id](const auto& msg) {
      return msg->has_options_margin_result() && msg->header().user_id() == tr_user_id;
    });
    int user_idx_spot = find_first_match([&u1](const auto& msg) {
      return msg->has_spot_margin_result() && msg->header().user_id() == u1->m_uid &&
             msg->spot_margin_result().related_spot_fills_size() > 0;
    });
    int tr_idx_spot = find_first_match([tr_user_id](const auto& msg) {
      return msg->has_spot_margin_result() && msg->header().user_id() == tr_user_id;
    });
    int user_idx_normal = find_first_match([&](const auto& msg) {
      return msg->asset_margin_result().account_info().account_status() ==
                 ::enums::eaccountstatus::AccountStatus::Normal &&
             msg->header().user_id() == u1->m_uid &&
             msg->asset_margin_result().account_info().liq_step() == ELiqStep::WaitTrigger;
    });
    // 撤单在接管之前 用户接管在TR接管之前
    ASSERT_GT(user_idx_comb_order, -1);
    ASSERT_GT(user_idx_future_order, -1);
    ASSERT_GT(user_idx_option_order, -1);
    ASSERT_GT(user_idx_spot_order, -1);
    ASSERT_GT(tr_idx_spot, -1);
    ASSERT_GT(user_idx_option, -1);
    ASSERT_GT(tr_idx_option, -1);
    ASSERT_GT(user_idx_spot, -1);
    ASSERT_GT(tr_idx_spot, -1);
    ASSERT_LT(std::max({user_idx_comb_order, user_idx_future_order, user_idx_option_order, user_idx_spot_order}),
              std::min({user_idx_future, user_idx_option, user_idx_spot}));
    ASSERT_LT(user_idx_option, tr_idx_option);
    ASSERT_LT(user_idx_future, tr_idx_future);
    ASSERT_LT(user_idx_spot, tr_idx_spot);
    ASSERT_GT(user_idx_normal, std::max({user_idx_option, user_idx_future, user_idx_spot}));

    // 校验组合单在全部撤单场景下的liq_record
    svc::unified_v2::UnifiedV2ResultDTO* comb_msg{};
    for (auto& result : rsp_vec) {
      // comb order
      if (result && result->has_header() && result->header().user_id() == u1->m_uid &&
          result->has_asset_margin_result() && result->has_combination_margin_result() &&
          result->combination_margin_result().related_combination_orders_size() > 0) {
        comb_msg = result.get();
        break;
      }
    }
    ASSERT_NE(comb_msg, nullptr);
    TestLiqHelper::PrintPb(comb_msg);
    ASSERT_EQ(comb_msg->asset_margin_result().liq_record_size(), 1);
    ASSERT_EQ(comb_msg->asset_margin_result().liq_record().begin()->contract_type(), 8);
    ASSERT_EQ(comb_msg->asset_margin_result().liq_record().begin()->liq_action(), 2);
    ASSERT_TRUE(comb_msg->asset_margin_result().liq_record().begin()->has_cur_usd_wallet_snapshot());
  }
}

/**
 * 测试手动强平接管功能
 *
 * 测试场景：
 * 1. 验证含有 futureSpread 组合单并处于成交中间状态时的强平撤单逻辑
 * 2. 验证在撤全部(开仓)单/全部单中会卡住状态的情况（组合/组合延迟强平）
 *
 * 预期结果：
 * - 正确处理 futureSpread 组合单的撤单逻辑
 * - 在特定条件下，强平流程会卡在撤单状态
 */
TEST_F(TestLiq, PM_RiskClosePosition_FILL_IN_PROCESS) {
  auto u1 =
      TestLiqHelper::InitTestUser(te_, 70001, EAccountMode::Portfolio, {{ECoin::USDT, "70000"}, {ECoin::BTC, "10"}});
  auto u2 =
      TestLiqHelper::InitTestUser(te_, 70002, EAccountMode::Portfolio, {{ECoin::USDT, "70000"}, {ECoin::BTC, "10"}});

  // 设置价格
  {
    te_->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 30000, 30000, 30000}, {static_cast<ESymbol>(45), 30000, 30000, 30000}});
    // te_->MockSpotLastPriceToPreEngine("BTC/USDT", bbase::decimal::Decimal<>(30000));
  }

  // u1创建futureSpread组合单
  {
    CombSiteCreateOrderBuilder builder("PERP-SPOT", biz::order_link_id_t(te_->GenUUID()), "Buy", "Limit", "GTC", "100",
                                       "3");
    RpcContext rpc_ctx{};
    auto resp = u1->comb_siteapi_create_order(builder.Build(), rpc_ctx);
    ASSERT_NE(nullptr, resp);
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), builder.msg.order_link_id());

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_GT(result.m_msg->combination_margin_result().related_combination_orders_size(), 0);
  }

  // u1创建期货订单用于构建仓位
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "30000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp1 = u1->create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u2创建期货订单用于构建仓位
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "30000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp1 = u2->create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    // taker
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    // u1 maker 成交
    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1 挂一个期货单
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "30000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp1 = u1->create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  te_->BatchAddMarkPrice(
      {{static_cast<ESymbol>(5), 10000, 10000, 10000}, {static_cast<ESymbol>(45), 10000, 10000, 10000}});
  te_->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(10000));

  // u1创建期权订单用于构造仓位
  {
    auto order_link_id_1 = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id_1);
    auto resp = u1->create_order(builder.Build());
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u2创建期权订单并作为taker成交
  {
    auto order_link_id = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Sell", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id);
    auto resp = u2->create_order(builder.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id);
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1挂一个期权订单
  {
    auto order_link_id_1 = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id_1);
    auto resp = u1->create_order(builder.Build());
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1挂一个现货订单
  {
    auto order_link_id_1 = te_->GenUUID();
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.1", "10000");
    build.SetOrderLinkID(order_link_id_1);
    RpcContext ct{};
    auto resp1 = u1->create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), 0);
    ASSERT_EQ(ct.RetMsg(), "OK");
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // 获取用户数据
  {
    auto resp = u1->DebugUserData();
    TestLiqHelper::PrintPb(resp);
  }

  // 大计算
  {
    // 发起大计算请求事件，会执行全部用户
    ASSERT_TRUE(u1->ReCalcEvent(true));
  }

  // 发RiskClosePz请求，预期失败，原因是不满足手动接管条件
  {
    auto m_rsp = u1->RiskClosePz();
    ASSERT_TRUE(m_rsp);
    TestLiqHelper::PrintPb(m_rsp);
    ASSERT_TRUE(m_rsp->ret_code() == -1 && m_rsp->has_header() && m_rsp->header().user_id() == u1->m_uid &&
                m_rsp->header().action() == EAction::RiskClosePz);
    ASSERT_EQ(m_rsp->ret_msg(), "mm less than 160");
  }

  // 手动接管条件是mmr >= 1.6 或者"/liq/manualLiqWhiteList"(","分隔)有此uid
  {
    auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
        bbase::object_manager::ObjectType::kObjectConfigCenter);
    nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                               "/liq/manualLiqWhiteList", std::to_string(u1->m_uid));
  }

  // 发RiskClosePz，设置account_action为ELiqStep::RiskClosePositions
  {
    auto m_rsp = u1->RiskClosePz();
    ASSERT_TRUE(m_rsp);
    TestLiqHelper::PrintPb(m_rsp);
    ASSERT_TRUE(m_rsp->ret_code() == 0 && m_rsp->has_header() && m_rsp->header().action() == EAction::RiskClosePz);
  }

  // u1 挂 futureSpread 组合单
  {
    biz::order_link_id_t m_order_link_id{te_->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Buy", "Limit", "GTC", "100", "2");
    RpcContext ct;
    auto m_resp = u1->comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  {
    // 设置单步模式
    te_->SetCrossSingleStepMode();
  }

  {
    biz::order_link_id_t m_order_link_id{te_->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Sell", "Limit", "GTC", "100", "1");
    RpcContext ct;
    auto m_resp = u2->comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);
  }

  // 获取用户数据
  {
    auto resp = u1->DebugUserData();
    TestLiqHelper::PrintPb(resp);
  }

  {
    te_->ProcessOneXReq(te_->GetCombCrossIdx());

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te_->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // 从大计算进入RiskClosePz触发事件继续流程
  {
    ASSERT_TRUE(u1->ReCalcEvent(false));
    ASSERT_TRUE(u1->ReCalcEvent(true));
  }

  // 获取用户数据
  {
    auto resp = u1->DebugUserData();
    TestLiqHelper::PrintPb(resp);
  }

  // 从大计算进入RiskClosePz触发事件继续流程时 中间状态直接return 而不继续流程
  {
    auto m_rsp = te_->PopResult();
    ASSERT_EQ(m_rsp.m_msg, nullptr);
    TestLiqHelper::PrintPb(m_rsp.m_msg);
  }

  {
    // 取消单步模式
    te_->UnSetCrossSingleStepMode();
  }

  {
    auto m_rsp = te_->PopResult();
    ASSERT_NE(m_rsp.m_msg, nullptr);
    TestLiqHelper::PrintPb(m_rsp.m_msg);

    m_rsp = te_->PopResult();
    ASSERT_NE(m_rsp.m_msg, nullptr);
    TestLiqHelper::PrintPb(m_rsp.m_msg);

    m_rsp = te_->PopResult();
    ASSERT_NE(m_rsp.m_msg, nullptr);
    TestLiqHelper::PrintPb(m_rsp.m_msg);

    m_rsp = te_->PopResult();
    ASSERT_NE(m_rsp.m_msg, nullptr);
    TestLiqHelper::PrintPb(m_rsp.m_msg);
  }

  // 大计算重新触发手动接管
  {
    ASSERT_TRUE(u1->ReCalcEvent(false));
    ASSERT_TRUE(u1->ReCalcEvent(true));
  }

  {
    auto m_rsp = te_->PopResult();
    ASSERT_NE(m_rsp.m_msg, nullptr);
    TestLiqHelper::PrintPb(m_rsp.m_msg);
    auto m = m_rsp.m_msg;
    ASSERT_TRUE(m->ret_code() == 0 && m->has_header());
    ASSERT_TRUE(m->header().action() == EAction::LiqCoinExecute);
    ASSERT_TRUE(m->asset_margin_result().account_info().account_status() ==
                ::enums::eaccountstatus::AccountStatus::Liq);
    ASSERT_TRUE(m->asset_margin_result().account_info().liq_step() == ELiqStep::RiskClosePositions);
    TestLiqHelper::PrintPb(m);

    std::vector<std::shared_ptr<svc::unified_v2::UnifiedV2ResultDTO>> rsp_vec;
    for (auto rsp = te_->PopResult(); rsp.m_msg != nullptr; rsp = te_->PopResult()) {
      TestLiqHelper::PrintPb(rsp.m_msg);
      rsp_vec.push_back(rsp.m_msg);
    }
    /*
    1. 撮合回报 - 组合单撤单(2个)
    2. 撮合回报 - 期权撤单
    3. 撮合回报 - 现货撤单
    4. 撮合回报 - 期货撤单
    5. 撤单结束 - 继续强平流程(LiqCoinExecute) 接管
    6. 撮合回报 - 现货接管
    7. TR撮合回报 - 现货接管
    8. 撮合回报 - 期权接管
    9. TR撮合回报 - 期权接管
    10. 撮合回报 - 期货接管
    11. TR撮合回报 - 期货接管
    12. 撮合回报 - 现货接管
    13. 撮合回报 - 继续强平流程(LiqCoinExecute) 接管结束
    14. TR撮合回报 - 现货接管
    <******** 增加TR中间账户>
    */
    ASSERT_EQ(rsp_vec.size(), 19);

    // 校验接管的撮合包在接管户之前，且最后账户恢复正常
    auto tr_user_id = application::GlobalVarManager::Instance().tr_user().first;

    auto find_first_match = [&rsp_vec](const auto& predicate) -> int {
      for (size_t i = 0; i < rsp_vec.size(); i++) {
        if (predicate(rsp_vec[i])) {
          return static_cast<int>(i);
        }
      }
      return -1;
    };

    auto user_idx_comb_order = find_first_match([&u1](const auto& msg) {
      return msg->header().user_id() == u1->m_uid && msg->has_combination_margin_result() &&
             msg->combination_margin_result().related_combination_orders_size() > 0;
    });
    auto user_idx_future_order = find_first_match([&u1](const auto& msg) {
      return msg->header().user_id() == u1->m_uid && msg->has_futures_margin_result() &&
             msg->futures_margin_result().related_orders_size() > 0;
    });
    auto user_idx_option_order = find_first_match([&u1](const auto& msg) {
      return msg->header().user_id() == u1->m_uid && msg->has_options_margin_result() &&
             msg->options_margin_result().related_orders_size() > 0;
    });
    auto user_idx_spot_order = find_first_match([&u1](const auto& msg) {
      return msg->header().user_id() == u1->m_uid && msg->has_spot_margin_result() &&
             msg->spot_margin_result().related_spot_orders_size() > 0;
    });
    int user_idx_future = find_first_match([&u1](const auto& msg) {
      return msg->has_futures_margin_result() && msg->header().user_id() == u1->m_uid &&
             msg->futures_margin_result().related_fills_size() > 0;
    });
    int tr_idx_future = find_first_match([tr_user_id](const auto& msg) {
      return msg->has_futures_margin_result() && msg->header().user_id() == tr_user_id;
    });
    auto user_idx_option = find_first_match([&u1](const auto& msg) {
      return msg->has_options_margin_result() && msg->header().user_id() == u1->m_uid &&
             msg->options_margin_result().related_fills_size() > 0;
    });
    auto tr_idx_option = find_first_match([tr_user_id](const auto& msg) {
      return msg->has_options_margin_result() && msg->header().user_id() == tr_user_id;
    });
    int user_idx_spot = find_first_match([&u1](const auto& msg) {
      return msg->has_spot_margin_result() && msg->header().user_id() == u1->m_uid &&
             msg->spot_margin_result().related_spot_fills_size() > 0;
    });
    int tr_idx_spot = find_first_match([tr_user_id](const auto& msg) {
      return msg->has_spot_margin_result() && msg->header().user_id() == tr_user_id;
    });
    int user_idx_normal = find_first_match([&](const auto& msg) {
      return msg->asset_margin_result().account_info().account_status() ==
                 ::enums::eaccountstatus::AccountStatus::Normal &&
             msg->header().user_id() == u1->m_uid &&
             msg->asset_margin_result().account_info().liq_step() == ELiqStep::WaitTrigger;
    });
    // 撤单在接管之前 用户接管在TR接管之前
    ASSERT_GT(user_idx_comb_order, -1);
    ASSERT_GT(user_idx_future_order, -1);
    ASSERT_GT(user_idx_option_order, -1);
    ASSERT_GT(user_idx_spot_order, -1);
    ASSERT_GT(tr_idx_spot, -1);
    ASSERT_GT(user_idx_option, -1);
    ASSERT_GT(tr_idx_option, -1);
    ASSERT_GT(user_idx_spot, -1);
    ASSERT_GT(tr_idx_spot, -1);
    ASSERT_LT(std::max({user_idx_comb_order, user_idx_future_order, user_idx_option_order, user_idx_spot_order}),
              std::min({user_idx_future, user_idx_option, user_idx_spot}));
    ASSERT_LT(user_idx_option, tr_idx_option);
    ASSERT_LT(user_idx_future, tr_idx_future);
    ASSERT_LT(user_idx_spot, tr_idx_spot);
    ASSERT_GT(user_idx_normal, std::max({user_idx_option, user_idx_future, user_idx_spot}));
  }
}
/**
 * 测试包含futureSpread组合定单的兑币场景
 *
 * 测试场景：
 * 执行兑币时对 futureSpread 组合订单的处理
 *
 * 预期结果：
 * - 正常执行
 */
TEST_F(TestLiq, PM_ReleaseAssetFrozen_FutureSpread) {
  auto u1 =
      TestLiqHelper::InitTestUser(te_, 70001, EAccountMode::Portfolio, {{ECoin::USDT, "100000"}, {ECoin::BTC, "3"}});
  auto u2 =
      TestLiqHelper::InitTestUser(te_, 70002, EAccountMode::Portfolio, {{ECoin::USDT, "100000"}, {ECoin::BTC, "3"}});

  {
    te_->BatchAddMarkPrice({{ESymbol::BTCUSDT, 70000, 70000, 70000}, {ESymbol::BTCUSD, 70000, 70000, 70000}});
    te_->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("70000"));
  }

  // 设置BTC为保证金币种
  ASSERT_TRUE(TestLiqHelper::SetSpotCollateralCoin(u1.get(), ECoin::BTC));

  // u1 下期货单构造仓位 3 * 70000
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "3", "70000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp = u1->create_order(build.Build());
    ASSERT_NE(resp->order_id(), "");
    auto result = te_->PopResult();
    ASSERT_TRUE(result.m_msg);
  }

  // u2 下期货单构造仓位 -3 * 70000
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "3", "70000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp = u2->create_order(build.Build());
    ASSERT_NE(resp->order_id(), "");

    // u2 taker成交
    auto result = te_->PopResult();
    ASSERT_TRUE(result.m_msg);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1 maker 形成仓位
  {
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1创建期权订单用于构造仓位
  {
    auto order_link_id_1 = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id_1);
    auto resp = u1->create_order(builder.Build());
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u2创建期权订单并作为taker成交
  {
    auto order_link_id = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Sell", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id);
    auto resp = u2->create_order(builder.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id);
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1挂一个期权订单
  {
    auto order_link_id_1 = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id_1);
    auto resp = u1->create_order(builder.Build());
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1挂一个现货订单
  {
    auto order_link_id_1 = te_->GenUUID();
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "1.42", "70000");
    build.SetOrderLinkID(order_link_id_1);
    RpcContext ct{};
    auto resp1 = u1->create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), 0);
    ASSERT_EQ(ct.RetMsg(), "OK");
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1 挂 futureSpread 组合单
  {
    biz::order_link_id_t m_order_link_id{te_->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Buy", "Limit", "GTC", "100", "3");
    RpcContext ct;
    auto m_resp = u1->comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    result = te_->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // InterestSettlement
  {
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    auto* req_header = request->mutable_req_header();
    req_header->set_req_id(te_->GenUUID());
    req_header->set_user_id(u1->m_uid);
    req_header->set_coin(ECoin::BTC);
    req_header->set_action(EAction::InterestSettlement);
    auto i_req = request->mutable_lendingreqgroupbody()->mutable_lendingrequest()->mutable_interestuserrequest()->Add();
    i_req->set_requestid(te_->GenUUID());
    i_req->set_interesttype("ACTUAL_INTEREST");
    i_req->set_amount("0.7");
    i_req->set_settlecoin(1);
    i_req->set_equitysnapshot("120");
    i_req->set_liabilitysnapshot("123");
    i_req->set_handletime(bbase::utils::Time::GetTimeMs());
    i_req->set_interestbase("500");
    i_req->set_interestratebyhour("0.000001142");
    i_req->set_interestviewtime(1685516401590);
    auto margin_req_event = std::make_shared<event::MarginHdtsRequestEvent>(u1->m_uid, request, -1);
    u1->m_te->pipeline()->ring_buffers(worker::kPreWorker)->Write(margin_req_event, false);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result.CheckUid(u1->m_uid);

    result = te_->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // SpotBorrowLiquidation
  {
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    auto req_header = request->mutable_req_header();
    req_header->set_req_id(te_->GenUUID());
    req_header->set_user_id(u1->m_uid);
    req_header->set_coin(ECoin::BTC);
    req_header->set_action(EAction::SpotBorrowLiquidation);
    auto lending_request = request->mutable_lendingreqgroupbody()->mutable_lendingrequest();
    lending_request->set_riskrule("LIABILITY_RULE");
    auto repayment_exchange = lending_request->mutable_repaymentuserexchangerequest()->Add();
    repayment_exchange->set_amount("10");
    repayment_exchange->set_coin(ECoin::BTC);
    auto margin_req_event = std::make_shared<event::MarginHdtsRequestEvent>(u1->m_uid, request, -1);
    u1->m_te->pipeline()->ring_buffers(worker::kPreWorker)->Write(margin_req_event, false);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_TRUE(result.m_msg->has_lending_result());
    ASSERT_TRUE(result.m_msg->lending_result().has_userexchangecoinresult());
    // 校验执行了现货撤单
    ASSERT_EQ(result.m_msg->lending_result().userexchangecoinresult().handleresult(), "3200820");

    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_TRUE(result.m_msg->has_combination_margin_result() && result.m_msg->has_spot_margin_result() &&
                result.m_msg->has_futures_margin_result());
    ASSERT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    ASSERT_EQ(result.m_msg->combination_margin_result().related_combination_orders().begin()->cancel_type(),
              ECancelType::CancelAllBeforeLiq);

    result = te_->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }
}
/**
 * 测试包含futureSpread组合定单的兑币场景(在组合单成交中间状态时)
 *
 * 测试场景：
 * 执行兑币时对 futureSpread 组合订单的处理
 *
 * 预期结果：
 * - 成交中间状态时卡在撤单这一步，恢复后正常执行
 */
TEST_F(TestLiq, PM_ReleaseAssetFrozen_FutureSpread_FILL_IN_PROCESS) {
  auto u1 =
      TestLiqHelper::InitTestUser(te_, 70001, EAccountMode::Portfolio, {{ECoin::USDT, "100000"}, {ECoin::BTC, "3"}});
  auto u2 =
      TestLiqHelper::InitTestUser(te_, 70002, EAccountMode::Portfolio, {{ECoin::USDT, "100000"}, {ECoin::BTC, "3"}});

  {
    te_->BatchAddMarkPrice({{ESymbol::BTCUSDT, 70000, 70000, 70000}, {ESymbol::BTCUSD, 70000, 70000, 70000}});
    te_->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal("70000"));
  }

  // 设置BTC为保证金币种
  ASSERT_TRUE(TestLiqHelper::SetSpotCollateralCoin(u1.get(), ECoin::BTC));

  // u1 下期货单构造仓位 3 * 70000
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "3", "70000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp = u1->create_order(build.Build());
    ASSERT_NE(resp->order_id(), "");
    auto result = te_->PopResult();
    ASSERT_TRUE(result.m_msg);
  }

  // u2 下期货单构造仓位 -3 * 70000
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "3", "70000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp = u2->create_order(build.Build());
    ASSERT_NE(resp->order_id(), "");

    // u2 taker成交
    auto result = te_->PopResult();
    ASSERT_TRUE(result.m_msg);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1 maker 形成仓位
  {
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1创建期权订单用于构造仓位
  {
    auto order_link_id_1 = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id_1);
    auto resp = u1->create_order(builder.Build());
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u2创建期权订单并作为taker成交
  {
    auto order_link_id = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Sell", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id);
    auto resp = u2->create_order(builder.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id);
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1挂一个期权订单
  {
    auto order_link_id_1 = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id_1);
    auto resp = u1->create_order(builder.Build());
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1挂一个现货订单
  {
    auto order_link_id_1 = te_->GenUUID();
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "1.42", "70000");
    build.SetOrderLinkID(order_link_id_1);
    RpcContext ct{};
    auto resp1 = u1->create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), 0);
    ASSERT_EQ(ct.RetMsg(), "OK");
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1 挂 futureSpread 组合单
  {
    biz::order_link_id_t m_order_link_id{te_->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Buy", "Limit", "GTC", "100", "2");
    RpcContext ct;
    auto m_resp = u1->comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    result = te_->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // u1 挂 futureSpread 组合单
  {
    biz::order_link_id_t m_order_link_id{te_->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Buy", "Limit", "GTC", "100", "0.03");
    RpcContext ct;
    auto m_resp = u1->comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  {
    // 设置单步模式
    te_->SetCrossSingleStepMode();
  }

  {
    biz::order_link_id_t m_order_link_id{te_->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Sell", "Limit", "GTC", "100", "0.01");
    RpcContext ct;
    auto m_resp = u2->comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);
  }

  // 处理一个撮合消息，来自futureSpread组合单对应的撮合
  {
    // 单步处理
    te_->ProcessOneXReq(te_->GetCombCrossIdx());

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_GT(result.m_msg->combination_margin_result().related_combination_orders_size(), 0);
    const auto& comb_order = result.m_msg->combination_margin_result().related_combination_orders().begin();
    ASSERT_EQ(comb_order->order_status(), ::enums::ecomborderstatus::WaitToRecvPassthrough);
    ASSERT_EQ(comb_order->cross_status(), ::enums::ecombcrossstatus::WaitToSetFill);

    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    result = te_->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // InterestSettlement
  {
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    auto* req_header = request->mutable_req_header();
    req_header->set_req_id(te_->GenUUID());
    req_header->set_user_id(u1->m_uid);
    req_header->set_coin(ECoin::BTC);
    req_header->set_action(EAction::InterestSettlement);
    auto i_req = request->mutable_lendingreqgroupbody()->mutable_lendingrequest()->mutable_interestuserrequest()->Add();
    i_req->set_requestid(te_->GenUUID());
    i_req->set_interesttype("ACTUAL_INTEREST");
    i_req->set_amount("5");
    i_req->set_settlecoin(1);
    i_req->set_equitysnapshot("120");
    i_req->set_liabilitysnapshot("123");
    i_req->set_handletime(bbase::utils::Time::GetTimeMs());
    i_req->set_interestbase("500");
    i_req->set_interestratebyhour("0.000001142");
    i_req->set_interestviewtime(1685516401590);
    auto margin_req_event = std::make_shared<event::MarginHdtsRequestEvent>(u1->m_uid, request, -1);
    u1->m_te->pipeline()->ring_buffers(worker::kPreWorker)->Write(margin_req_event, false);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result.CheckUid(u1->m_uid);

    result = te_->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // SpotBorrowLiquidation
  {
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    auto req_header = request->mutable_req_header();
    req_header->set_req_id(te_->GenUUID());
    req_header->set_user_id(u1->m_uid);
    req_header->set_coin(ECoin::BTC);
    req_header->set_action(EAction::SpotBorrowLiquidation);
    auto lending_request = request->mutable_lendingreqgroupbody()->mutable_lendingrequest();
    lending_request->set_riskrule("LIABILITY_RULE");
    auto repayment_exchange = lending_request->mutable_repaymentuserexchangerequest()->Add();
    repayment_exchange->set_amount("10");
    repayment_exchange->set_coin(ECoin::BTC);
    auto margin_req_event = std::make_shared<event::MarginHdtsRequestEvent>(u1->m_uid, request, -1);
    u1->m_te->pipeline()->ring_buffers(worker::kPreWorker)->Write(margin_req_event, false);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_TRUE(result.m_msg->has_lending_result());
    ASSERT_TRUE(result.m_msg->lending_result().has_userexchangecoinresult());
    // 校验执行了现货撤单
    ASSERT_EQ(result.m_msg->lending_result().userexchangecoinresult().handleresult(), "3200820");

    result = te_->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  {
    te_->UnSetCrossSingleStepMode();

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    /*ASSERT_TRUE(result.m_msg->has_combination_margin_result() && result.m_msg->has_spot_margin_result() &&
    result.m_msg->has_futures_margin_result());
    ASSERT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    ASSERT_EQ(result.m_msg->combination_margin_result().related_combination_orders().begin()->cancel_type(),
    ECancelType::CancelAllBeforeLiq);*/

    result = te_->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }

  // SpotBorrowLiquidation
  {
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    auto req_header = request->mutable_req_header();
    req_header->set_req_id(te_->GenUUID());
    req_header->set_user_id(u1->m_uid);
    req_header->set_coin(ECoin::BTC);
    req_header->set_action(EAction::SpotBorrowLiquidation);
    auto lending_request = request->mutable_lendingreqgroupbody()->mutable_lendingrequest();
    lending_request->set_riskrule("LIABILITY_RULE");
    auto repayment_exchange = lending_request->mutable_repaymentuserexchangerequest()->Add();
    repayment_exchange->set_amount("10");
    repayment_exchange->set_coin(ECoin::BTC);
    auto margin_req_event = std::make_shared<event::MarginHdtsRequestEvent>(u1->m_uid, request, -1);
    u1->m_te->pipeline()->ring_buffers(worker::kPreWorker)->Write(margin_req_event, false);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_TRUE(result.m_msg->has_lending_result());
    ASSERT_TRUE(result.m_msg->lending_result().has_userexchangecoinresult());
    // 校验执行了现货撤单
    ASSERT_EQ(result.m_msg->lending_result().userexchangecoinresult().handleresult(), "3200820");

    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_TRUE(result.m_msg->has_combination_margin_result() && result.m_msg->has_spot_margin_result() &&
                result.m_msg->has_futures_margin_result());
    ASSERT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    ASSERT_EQ(result.m_msg->combination_margin_result().related_combination_orders().begin()->cancel_type(),
              ECancelType::CancelAllBeforeLiq);

    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_TRUE(result.m_msg->has_combination_margin_result() && result.m_msg->has_spot_margin_result() &&
                result.m_msg->has_futures_margin_result());
    ASSERT_EQ(result.m_msg->combination_margin_result().related_combination_orders_size(), 1);
    ASSERT_EQ(result.m_msg->combination_margin_result().related_combination_orders().begin()->cancel_type(),
              ECancelType::CancelAllBeforeLiq);

    result = te_->PopResult();
    ASSERT_EQ(result.m_msg.get(), nullptr);
  }
}

/**
 * 测试组合模式(PM)下执行adl
 *
 * 测试场景：
 * 在组合模式下，不能执行adl
 *
 * 预期结果：
 * - 报错退出
 *
 */
TEST_F(TestLiq, PM_ADL) {
  auto u1 =
      TestLiqHelper::InitTestUser(te_, 70001, EAccountMode::Portfolio, {{ECoin::USDT, "50000"}, {ECoin::BTC, "3"}});
  auto u2 =
      TestLiqHelper::InitTestUser(te_, 70002, EAccountMode::Portfolio, {{ECoin::USDT, "70000"}, {ECoin::BTC, "5"}});

  // 更新行情
  {
    te_->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 30000, 30000, 30000}, {static_cast<ESymbol>(45), 30000, 30000, 30000}});
    // te_->MockSpotLastPriceToPreEngine("BTC/USDT", bbase::decimal::Decimal<>(30000));
  }

  // u1创建futureSpread组合单
  {
    CombSiteCreateOrderBuilder builder("PERP-SPOT", biz::order_link_id_t(te_->GenUUID()), "Buy", "Limit", "GTC", "10",
                                       "3");
    RpcContext rpc_ctx{};
    auto resp = u1->comb_siteapi_create_order(builder.Build(), rpc_ctx);
    ASSERT_NE(nullptr, resp);
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), builder.msg.order_link_id());

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    ASSERT_GT(result.m_msg->combination_margin_result().related_combination_orders_size(), 0);
  }

  // u1创建期货订单用于构建仓位
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "30000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp1 = u1->create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u2创建期货订单用于构建仓位
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "30000");
    build.SetOrderLinkID(te_->GenUUID());
    auto resp1 = u2->create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    // taker
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    // u1 maker 成交
    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1 挂一个期货单
  {
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "30000");
    build.SetOrderLinkID("3ec7d5af-0de9-4011-ad10-7e7c52e09246");
    auto resp1 = u1->create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  te_->BatchAddMarkPrice(
      {{static_cast<ESymbol>(5), 10000, 10000, 10000}, {static_cast<ESymbol>(45), 10000, 10000, 10000}});
  te_->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(10000));

  // u1创建期权订单用于构造仓位
  {
    auto order_link_id_1 = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id_1);
    auto resp = u1->create_order(builder.Build());
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u2创建期权订单并作为taker成交
  {
    auto order_link_id = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Sell", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id);
    auto resp = u2->create_order(builder.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id);
    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);

    result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1挂一个期权订单
  {
    auto order_link_id_1 = te_->GenUUID();
    CreateOrderBuilder builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "2", "20000");
    builder.SetOrderLinkID(order_link_id_1);
    auto resp = u1->create_order(builder.Build());
    TestLiqHelper::PrintPb(resp);
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  // u1挂一个现货订单
  {
    auto order_link_id_1 = te_->GenUUID();
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.1", "10000");
    build.SetOrderLinkID(order_link_id_1);
    RpcContext ct{};
    auto resp1 = u1->create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), 0);
    ASSERT_EQ(ct.RetMsg(), "OK");
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);

    auto result = te_->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  {
    te_->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 35000, 35000, 35000}, {static_cast<ESymbol>(45), 10000, 10000, 10000}});
    te_->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(35000));
  }

  // 大计算， u1此时imr:xxxx, mmr:xxxx
  {
    ASSERT_TRUE(u1->ReCalcEvent(false));
    ASSERT_TRUE(u1->ReCalcEvent(true));
  }

  // 获取用户数据
  {
    auto resp = u1->DebugUserData();
    TestLiqHelper::PrintPb(resp);
  }

  // 选中u1执行adl 预期报错返回
  {
    auto order_id = te_->GenUUID();
    biz::money_e8_t risk_pool_balance_e8 = 10 * 1e8;
    AdlReqBuilder build(u1->m_uid, ECoin::USDT, order_id, ESymbol::BTCUSDT, ESide::Sell, ********, *********, 4,
                        ESide::Buy, EPositionIndex::Single, *********, ********0, risk_pool_balance_e8, 5002);
    auto result = u1->process(build.Build());
    TestLiqHelper::PrintPb(result);
    ASSERT_EQ(result->ret_code(), error::ErrorCode::kErrorCodeDefault);
    ASSERT_EQ(result->ret_msg(), "account mode not support");
  }
}
