//
// Created by SH33023ML on 2023/3/21.
//

#include <algorithm>
#include <iostream>
#include <memory>
#include <string>
#include <thread>
#include <utility>
#include <vector>

#include "biz_worker/service/trade/liquidation/calculator/clean_spot_step_calculation.hpp"
#include "src/biz_worker/utils/calc_util.hpp"
#include "src/cross_worker/cross_common/util/x_comm.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/liquidation/liquidation_test.hpp"

namespace clean_step_spot_test {

TEST_F(LiquidationTest, clean_step_spot_test_1_HasDerivativesAsset) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  int32_t wait_time = 300;

  // 账户开户
  openAccount(uid1);
  openAccount(uid2);

  // 账户充值
  depositAccount(uid1, ECoin::USDT, "0.001");
  depositAccount(uid1, ECoin::USDC, "50000");

  depositAccount(uid2, ECoin::USDC, "50000");
  depositAccount(uid2, ECoin::BTC, "0.001");

  // 调整风险限额
  increaseRiskLimit(uid1, ESymbol::BTCUSDT, ECoin::USDT, 2);
  increaseRiskLimit(uid2, ESymbol::BTCUSDT, ECoin::USDT, 2);

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单New:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Taker:" << jsonStr2 << std::endl;
  }

  {
    // 等待成交回报
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "成交回报Maker:" << jsonStr2 << std::endl;
  }

  // 修改标记价格
  te->AddMarkPrice(ESymbol::BTCUSDT, 29900, 20000, 20000);

  // 进行接管
  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid2) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    while (true) {
      auto result = te->PopResult(wait_time);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "强平结果1:" << jsonStr2 << std::endl;
      if (result.m_msg->header().user_id() == uid2) {
        auto lendings = result.m_msg->lending_result();
        if (lendings.has_userexchangecoinresult()) {
          auto exchange_order_0 = result.m_msg.get()->lending_result().userexchangecoinresult().exchangeorder(0);
          ASSERT_EQ(exchange_order_0.buycoin(), "USDT");
          ASSERT_EQ(exchange_order_0.sellcoin(), "BTC");
          ASSERT_EQ(exchange_order_0.sellmoney(), "0.001000000000000000");
          ASSERT_EQ(exchange_order_0.buymoney(), "19.607843137254901960");

          auto exchange_order = result.m_msg.get()->lending_result().userexchangecoinresult().exchangeorder(1);
          ASSERT_EQ(exchange_order.buycoin(), "USDT");
          ASSERT_EQ(exchange_order.sellcoin(), "USDC");

          ASSERT_EQ(exchange_order.sellmoney(), "50000.000000000000000000");
          ASSERT_EQ(exchange_order.buymoney(), "48529.411764705882350000");
        }
      }
    }
  }
}

TEST_F(LiquidationTest, clean_step_spot_test_1_pm_HasDerivativesAsset) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  int32_t wait_time = 300;

  // 账户开户
  openAccount(uid1);
  openAccount(uid2);

  // 账户充值
  depositAccount(uid1, ECoin::USDT, "0.001");
  depositAccount(uid1, ECoin::USDC, "50000");

  depositAccount(uid2, ECoin::USDC, "50000");
  depositAccount(uid2, ECoin::BTC, "0.001");
  switchPm(uid2);

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单New:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Taker:" << jsonStr2 << std::endl;
  }

  {
    // 等待成交回报
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "成交回报Maker:" << jsonStr2 << std::endl;
  }

  // 修改标记价格
  te->AddMarkPrice(ESymbol::BTCUSDT, 27500, 20000, 20000);

  // 进行接管
  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid2) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    while (true) {
      auto result = te->PopResult(wait_time);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "强平结果1:" << jsonStr2 << std::endl;
      if (result.m_msg->header().user_id() == uid2) {
        auto lendings = result.m_msg->lending_result();
        if (lendings.has_userexchangecoinresult()) {
          EXPECT_EQ(result.m_msg.get()->lending_result().userexchangecoinresult().exchangeorder_size(), 2);
          auto exchange_order_0 = result.m_msg.get()->lending_result().userexchangecoinresult().exchangeorder(0);
          ASSERT_EQ(exchange_order_0.buycoin(), "USDT");
          ASSERT_EQ(exchange_order_0.sellcoin(), "BTC");
          ASSERT_EQ(exchange_order_0.sellmoney(), "0.001000000000000000");
          ASSERT_EQ(exchange_order_0.buymoney(), "19.607843137254901960");

          auto exchange_order = result.m_msg.get()->lending_result().userexchangecoinresult().exchangeorder(1);
          ASSERT_EQ(exchange_order.buycoin(), "USDT");
          ASSERT_EQ(exchange_order.sellcoin(), "USDC");

          ASSERT_EQ(exchange_order.sellmoney(), "38677.979797979797979411");
          ASSERT_EQ(exchange_order.buymoney(), "37540.392156862745098040");
        }
      }
    }
  }
}

TEST_F(LiquidationTest, clean_step_spot_test_1_HasDerivativesAsset_mult) {
  // GTEST_SKIP();
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID1
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  int32_t wait_time = 300;

  // 账户开户
  openAccount(uid1);
  openAccount(uid2);

  // 账户充值
  depositAccount(uid1, ECoin::USDT, "0.001");
  depositAccount(uid1, ECoin::USDC, "50000");

  depositAccount(uid2, ECoin::USDC, "50000");
  depositAccount(uid2, ECoin::BTC, "0.001");

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单New:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Taker:" << jsonStr2 << std::endl;
  }

  {
    // 等待成交回报
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "成交回报Maker:" << jsonStr2 << std::endl;
  }

  // 修改标记价格
  te->AddMarkPrice(ESymbol::BTCUSDT, 29310, 20000, 20000);

  // 进行接管
  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid2) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    while (true) {
      auto result = te->PopResult(wait_time);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "强平结果1:" << jsonStr2 << std::endl;
      if (result.m_msg->header().user_id() == uid2) {
        auto lendings = result.m_msg->lending_result();
        if (lendings.has_userexchangecoinresult()) {
          auto exchange_order_0 = result.m_msg.get()->lending_result().userexchangecoinresult().exchangeorder(0);
          ASSERT_EQ(exchange_order_0.buycoin(), "USDT");
          ASSERT_EQ(exchange_order_0.sellcoin(), "BTC");
          ASSERT_EQ(exchange_order_0.sellmoney(), "0.001000000000000000");
          ASSERT_EQ(exchange_order_0.buymoney(), "19.607843137254901960");

          auto exchange_order = result.m_msg.get()->lending_result().userexchangecoinresult().exchangeorder(1);
          ASSERT_EQ(exchange_order.buycoin(), "USDT");
          ASSERT_EQ(exchange_order.sellcoin(), "USDC");

          ASSERT_EQ(exchange_order.sellmoney(), "12000.555555555555555435");
          ASSERT_EQ(exchange_order.buymoney(), "11647.598039215686274510");
        }
      }
    }
  }
}

TEST_F(LiquidationTest, clean_step_spot_sort) {
  std::vector<biz::CleanSpotStepCoin> positive_asset_vector;
  biz::CleanSpotStepCoin clean_spot_step_coin;
  clean_spot_step_coin.clearing_order_ = 2;
  clean_spot_step_coin.has_derivatives_asset = false;
  positive_asset_vector.push_back(clean_spot_step_coin);

  biz::CleanSpotStepCoin clean_spot_step_coin_2;
  clean_spot_step_coin_2.clearing_order_ = 3;
  clean_spot_step_coin_2.has_derivatives_asset = true;
  positive_asset_vector.push_back(clean_spot_step_coin_2);

  biz::CleanSpotStepCoin clean_spot_step_coin_3;
  clean_spot_step_coin_3.clearing_order_ = 1;
  clean_spot_step_coin_3.has_derivatives_asset = true;
  positive_asset_vector.push_back(clean_spot_step_coin_3);

  biz::CleanSpotStepCoin clean_spot_step_coin_4;
  clean_spot_step_coin_4.clearing_order_ = 4;
  clean_spot_step_coin_4.has_derivatives_asset = false;
  positive_asset_vector.push_back(clean_spot_step_coin_4);

  sort(positive_asset_vector.begin(), positive_asset_vector.end(), biz::CleanSpotStepCalculation::compare);
  auto positive_asset_0 = positive_asset_vector.begin();
  ASSERT_EQ(positive_asset_0->clearing_order_, 2);
  positive_asset_0++;
  ASSERT_EQ(positive_asset_0->clearing_order_, 4);
  positive_asset_0++;
  ASSERT_EQ(positive_asset_0->clearing_order_, 1);
  positive_asset_0++;
  ASSERT_EQ(positive_asset_0->clearing_order_, 3);
}
}  // namespace clean_step_spot_test
