#include "liquidation/liquidation_test.hpp"
#include "src/biz_worker/service/trade/liquidation/event/liq_event.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"

#define P(msg) std::cerr << "Pb" << __LINE__ << " " << utils::PbMessageToJsonString(msg) << "\n";

TEST_F(LiquidationTest, isolated_liq) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "5000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // set risk id
    biz::symbol_t symbol = 5;  // BTCUSDT
    biz::coin_t coin = 5;
    biz::risk_id_t risk_id = 2;
    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid1, risk_id);
    auto resp1 = user1.process(set_risk_id_build.Build());
    auto result1 = te->PopResult();
    P(*result1.m_msg)
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  }

  {
    std::string usdt_order_id;
    std::string usdt_order_link_id = "6b0f2f11-c87a-4594-ac61-c2ae0aba51b3";
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.1", "10100");
    create_build.SetOrderLinkID(usdt_order_link_id);
    create_build.SetTrigger("MarkPrice", 2, "10050");  // 1-rises 2-falls
    auto resp = user1.create_order(create_build.Build());
    ASSERT_NE(resp->order_id(), "");
    ASSERT_STREQ(resp->order_link_id().c_str(), usdt_order_link_id.c_str());
    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(usdt_order_link_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    order_check.CheckQty(uid1, 10000000);
    order_check.CheckPrice(uid1, 10100 * 1e4);
    order_check.CheckExecInst(uid1, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
    usdt_order_id = resp->order_id();
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    auto r = te->PopResult();
    if (r.m_msg != nullptr) {
      P(*r.m_msg)
    }
  }

  {
    // 挂单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 挂单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18180, 18180, 18180);
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    sharding_key = utils::hash(static_cast<std::uint64_t>(uid2) /
                               static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
                   application::GlobalConfig::re_calc_total_user_sharding();
    event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "无损降档:" << jsonStr2 << std::endl;
    }
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18080, 18080, 18080);
  }

  {
    te->SuspendCross();
    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  }

  {
    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    te->ResumeCross();

    int32_t cancel_order_cnt = 0;
    int32_t take_over_op_cnt = 0;
    int32_t matching_result_msg_cnt = 0;
    int32_t before_liq_msg_cnt = 0;
    int32_t cancel_before_liq_record_cnt = 0;
    int32_t takeover_before_liq_record_cnt = 0;
    while (true) {
      auto result = te->PopResult(200);
      if (result.m_msg.get() == nullptr) {
        break;
      }

      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "强平执行结果:" << jsonStr2 << std::endl;

      if (result.m_msg->header().user_id() == 5002) {
        continue;
      }

      // 2 - cancle all
      if (result.m_msg->header().action() == EAction::OnRecvMatchingResult) {
        matching_result_msg_cnt++;

        if (matching_result_msg_cnt == 1) {
          cancel_order_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          if (cancel_order_cnt == 1) {
            ASSERT_EQ(liq_record.is_finished(), false);
          } else {
            ASSERT_EQ(liq_record.is_finished(), true);
          }
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqCancelAll);  // 2-撤单
          ASSERT_EQ(liq_record.before_liq_execute(), false);
          ASSERT_EQ(liq_record.has_liq_position(), true);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        } else if (matching_result_msg_cnt == 2) {
          cancel_order_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          if (cancel_order_cnt == 1) {
            ASSERT_EQ(liq_record.is_finished(), false);
          } else {
            ASSERT_EQ(liq_record.is_finished(), true);
          }
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqCancelAll);  // 2-撤单
          ASSERT_EQ(liq_record.before_liq_execute(), false);
          ASSERT_EQ(liq_record.has_liq_position(), true);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        } else if (matching_result_msg_cnt == 3) {
          take_over_op_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);
          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.is_finished(), true);                                          // 接管回来
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);  // 5-接管
          ASSERT_EQ(liq_record.before_liq_execute(), false);
          ASSERT_EQ(liq_record.has_liq_position(), true);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 1);

          const auto& translog = translogs[0];
          ASSERT_EQ(translog.platformfee(), "0.0");
          ASSERT_EQ(translog.fee_rate(), "0.0006**********0000");
          ASSERT_EQ(translog.exec_fee(), "-10.8**********0000000");
          ASSERT_EQ(translog.exec_qty(), "1.**********00000000");
          ASSERT_EQ(translog.exec_price(), "18000.**********00000000");
          ASSERT_EQ(translog.trans_type(), store::TransLog::TransTypeEnum::kTransTypeLiquidation);
          ASSERT_EQ(translog.cash_flow(), "-2000.**********00000000");
          ASSERT_EQ(translog.change(), "-2010.8**********0000000");
        } else {
          ASSERT_EQ(true, false);
        }
      } else if (result.m_msg->header().action() == EAction::LiqCoinExecute) {
        before_liq_msg_cnt++;

        if (before_liq_msg_cnt == 1) {
          cancel_before_liq_record_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqCancelAll);  // 5-接管
          ASSERT_EQ(liq_record.is_finished(), false);                                      // 接管回来
          ASSERT_EQ(liq_record.before_liq_execute(), true);
          ASSERT_EQ(liq_record.has_liq_position(), false);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        } else if (before_liq_msg_cnt == 2) {
          takeover_before_liq_record_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);  // 5-接管
          ASSERT_EQ(liq_record.is_finished(), false);                                         // 接管回来
          ASSERT_EQ(liq_record.before_liq_execute(), true);
          ASSERT_EQ(liq_record.has_liq_position(), false);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        } else {
          ASSERT_EQ(true, false);
        }
      } else if (result.m_msg->header().action() == EAction::UpdateUserLoadStatusToOut) {
      } else {
        ASSERT_EQ(true, false);
      }
    }

    ASSERT_EQ(cancel_order_cnt, 2);
    ASSERT_EQ(take_over_op_cnt, 1);

    ASSERT_EQ(cancel_before_liq_record_cnt, 1);
    ASSERT_EQ(takeover_before_liq_record_cnt, 1);
  }

  {
    int32_t leave_msg_cnt = 0;
    while (true) {
      auto result = te->PopResult(300);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "接管结果:" << jsonStr2 << std::endl;
    }

    ASSERT_EQ(leave_msg_cnt, 0);
  }
}

TEST_F(LiquidationTest, isolated_liq_cancel_with_value_limit) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "3000000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置mqrkPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // set risk id
    biz::symbol_t symbol = 5;  // BTCUSDT
    biz::coin_t coin = 5;
    biz::risk_id_t risk_id = 2;
    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid1, risk_id);
    auto resp1 = user1.process(set_risk_id_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // uid1挂1笔单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "49", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // uid1挂第二笔单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "10", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  // mm = 1*20000*(9/10*0.00055+0.005)=109.9
  // (20000-20000*0.10114000)/((1-0.00055))=17987.0929011
  // (17987.09 + 109.9) / 1=18096.99

  {
    // 设置markPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18050, 18050, 18050);
  }

  {
    int32_t before_liq_msg_cnt = 0;
    int32_t after_liq_msg_cnt = 0;

    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      std::string jsonStr;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
      std::cout << "消息:" << jsonStr << std::endl;

      if (result.m_msg->header().user_id() == 5002) {
        continue;
      }

      if (result.m_msg->header().action() != EAction::LiqCoinExecute) {
        after_liq_msg_cnt++;

        if (after_liq_msg_cnt == 1 || after_liq_msg_cnt == 2) {
          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqCancelAll);  // 2-撤单

          if (after_liq_msg_cnt == 1) {
            // 第一笔撤单撮合回包
            ASSERT_EQ(liq_record.is_finished(), false);
          } else {
            // 最后一笔订单撤单完成
            ASSERT_EQ(liq_record.is_finished(), true);
          }
          ASSERT_EQ(liq_record.before_liq_execute(), false);
          ASSERT_EQ(liq_record.has_liq_position(), true);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        } else {
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record1 = liq_records[0];
          ASSERT_EQ(liq_record1.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
          EXPECT_EQ(liq_record1.is_finished(), true);
          EXPECT_EQ(liq_record1.before_liq_execute(), false);
          EXPECT_EQ(liq_record1.has_liq_position(), true);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 1);
        }

      } else {
        before_liq_msg_cnt++;

        if (before_liq_msg_cnt == 1) {  // 撤单开始
          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record1 = liq_records[0];
          ASSERT_EQ(liq_record1.liq_action(), enums::eliqaction::LiqAction::LiqCancelAll);  // 2-撤单
          ASSERT_EQ(liq_record1.is_finished(), false);
          ASSERT_EQ(liq_record1.before_liq_execute(), true);
          ASSERT_EQ(liq_record1.has_liq_position(), false);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        } else {
          // 强平中撮合回报 不降低用户风险档位 撮合回包触发强平流程续上时无损降档使仓位安全
          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record1 = liq_records[0];
          ASSERT_EQ(liq_record1.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
          ASSERT_EQ(liq_record1.is_finished(), false);
          ASSERT_EQ(liq_record1.before_liq_execute(), true);
          ASSERT_EQ(liq_record1.has_liq_position(), false);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        }
      }
    }

    ASSERT_EQ(after_liq_msg_cnt, 3);
    ASSERT_EQ(before_liq_msg_cnt, 2);
  }
}

TEST_F(LiquidationTest, isolated_liq_cancel_with_position_idx) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "30000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换为双向持仓
    FutureSwitchPositionModeReqBuilder build(uid1, 5, 5, static_cast<EPositionMode>(3));
    auto resp1 = user1.process(build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切双仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 1, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // 挂单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 1, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货BUY订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 挂单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 2, "Sell", "Limit", "1", "20500");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货SELL订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置markPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18079, 18079, 18079);
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    int32_t cancel_matching_result_cnt = 0;
    int32_t takeover_matching_result_cnt = 0;
    int32_t before_liq_msg_cnt = 0;
    int32_t matching_result_msg_cnt = 0;
    int32_t before_cancel_msg_cnt = 0;
    int32_t before_takeover_msg_cnt = 0;

    while (true) {
      auto result = te->PopResult(300);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      std::string jsonStr;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
      std::cout << "撮合回包:" << jsonStr << std::endl;

      if (result.m_msg->header().user_id() == 5002) {
        continue;
      }

      if (result.m_msg->header().action() == EAction::LiqCoinExecute) {
        before_liq_msg_cnt++;
        if (before_liq_msg_cnt == 1) {  // 强平撤单流程开始时的强平记录 撤销多仓上的订单
          before_cancel_msg_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record1 = liq_records[0];
          ASSERT_EQ(liq_record1.liq_action(), enums::eliqaction::LiqAction::LiqCancelAll);  // 2-撤单
          ASSERT_EQ(liq_record1.is_finished(), false);
          ASSERT_EQ(liq_record1.before_liq_execute(), true);
          ASSERT_EQ(liq_record1.has_liq_position(), false);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        } else {
          before_takeover_msg_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record1 = liq_records[0];
          ASSERT_EQ(liq_record1.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);  // 5-接管
          ASSERT_EQ(liq_record1.is_finished(), false);
          ASSERT_EQ(liq_record1.before_liq_execute(), true);
          ASSERT_EQ(liq_record1.has_liq_position(), false);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        }
      } else if (result.m_msg->header().action() == EAction::OnRecvMatchingResult) {
        matching_result_msg_cnt++;
        if (matching_result_msg_cnt == 1) {  // 第一笔订单撤单
          cancel_matching_result_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record1 = liq_records[0];
          ASSERT_EQ(liq_record1.liq_action(), enums::eliqaction::LiqAction::LiqCancelAll);  // 2-撤单
          ASSERT_EQ(liq_record1.is_finished(), true);
          ASSERT_EQ(liq_record1.before_liq_execute(), false);
          ASSERT_EQ(liq_record1.has_liq_position(), true);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        } else if (matching_result_msg_cnt == 2) {  // 接管仓位
          takeover_matching_result_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record1 = liq_records[0];
          ASSERT_EQ(liq_record1.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);  // 2-撤单
          ASSERT_EQ(liq_record1.is_finished(), true);
          ASSERT_EQ(liq_record1.before_liq_execute(), false);
          ASSERT_EQ(liq_record1.has_liq_position(), true);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 1);

          const auto& translog = translogs[0];
          ASSERT_EQ(translog.platformfee(), "0.0");
          ASSERT_EQ(translog.fee_rate(), "0.0006**********0000");
          ASSERT_EQ(translog.exec_fee(), "-10.8**********0000000");
          ASSERT_EQ(translog.exec_qty(), "1.**********00000000");
          ASSERT_EQ(translog.exec_price(), "18000.**********00000000");
          ASSERT_EQ(translog.trans_type(), store::TransLog::TransTypeEnum::kTransTypeLiquidation);
          ASSERT_EQ(translog.cash_flow(), "-2000.**********00000000");
          ASSERT_EQ(translog.change(), "-2010.8**********0000000");
        } else {
          ASSERT_EQ(true, false);
        }
      } else {
        ASSERT_EQ(true, false);
      }
    }

    ASSERT_EQ(cancel_matching_result_cnt, 1);
    ASSERT_EQ(takeover_matching_result_cnt, 1);
    ASSERT_EQ(matching_result_msg_cnt, 2);
    ASSERT_EQ(before_cancel_msg_cnt, 1);
    ASSERT_EQ(before_takeover_msg_cnt, 1);
  }
}

TEST_F(LiquidationTest, isolated_liq_subposition_success) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "3000000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "3000000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // set risk id
    biz::symbol_t symbol = 5;  // BTCUSDT
    biz::coin_t coin = 5;
    biz::risk_id_t risk_id = 2;
    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid1, risk_id);
    auto resp1 = user1.process(set_risk_id_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  }

  {
    // set risk id
    biz::symbol_t symbol = 5;  // BTCUSDT
    biz::coin_t coin = 5;
    biz::risk_id_t risk_id = 2;
    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid2, risk_id);
    auto resp1 = user2.process(set_risk_id_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // 挂单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  // mm = 60*20000*(9/10*0.00055+0.01)-5000=7594
  // (20000-20000*0.10114000)/((1-0.00055))=17987.0929011
  // // 20*100000*(0.01) - 5000 = 15000
  // (17987.09 * 60 + 7594) / 60=18113.65666667

  // mm = 50*20000*(9/10*0.00055+0.005)=5495
  // (17987.09 * 50 + 5495) / 50=18096.99

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18097, 18110, 18110);
  }

  {
    // uid2挂对手盘订单用于吃掉减仓单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "10", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "挂对手盘 Result:" << jsonStr2 << std::endl;
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    int32_t before_liq_with_loss = 0;
    int32_t after_liq_with_loss = 0;
    while (true) {
      auto result = te->PopResult(300);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "有损降档:" << jsonStr2 << std::endl;

      {
        if (result.m_msg->header().user_id() == uid1) {
          if (result.m_msg->header().action() != EAction::LiqCoinExecute) {
            after_liq_with_loss++;

            // 账户状态
            const auto& account_i = result.m_msg->asset_margin_result().account_info();
            ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

            const auto& fills = result.m_msg->futures_margin_result().related_fills();
            ASSERT_EQ(fills.size(), 1);

            const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
            ASSERT_EQ(liq_records.size(), 1);

            const auto& liq_record = liq_records[0];
            ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithLoss);
            ASSERT_EQ(liq_record.is_finished(), true);
            ASSERT_EQ(liq_record.before_liq_execute(), false);
            ASSERT_EQ(liq_record.has_liq_position(), true);

            // 资金流
            const auto& translogs = result.m_msg->futures_margin_result().translog();
            ASSERT_EQ(translogs.size(), 1);
            const auto& translog = translogs[0];
            ASSERT_EQ(translog.platformfee(), "0.0");
            ASSERT_EQ(translog.fee_rate(), "0.0006**********0000");
            // 19921*10*0.0006
            EXPECT_EQ(translog.exec_fee(), "-54.07532244**********");
            EXPECT_EQ(translog.exec_qty(), "4.743**********00000");
            EXPECT_EQ(translog.exec_price(), "19001.8**********0000000");
            EXPECT_EQ(translog.trans_type(), store::TransLog::TransTypeEnum::kTransTypeTrade);
            // (20000-19921)*10
            EXPECT_EQ(translog.cash_flow(), "-4734.4626**********0000");
            // -119.526-790
            EXPECT_EQ(translog.change(), "-4788.53792244**********");
          } else {
            before_liq_with_loss++;

            // 账户状态
            const auto& account_i = result.m_msg->asset_margin_result().account_info();
            ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

            const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
            ASSERT_EQ(liq_records.size(), 1);

            const auto& liq_record = liq_records[0];
            ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithLoss);
            ASSERT_EQ(liq_record.is_finished(), false);
            ASSERT_EQ(liq_record.before_liq_execute(), true);
            ASSERT_EQ(liq_record.has_liq_position(), false);

            // 资金流
            const auto& translogs = result.m_msg->futures_margin_result().translog();
            ASSERT_EQ(translogs.size(), 0);
          }
        }
      }
    }  // while

    ASSERT_EQ(after_liq_with_loss, 1);
    ASSERT_EQ(before_liq_with_loss, 1);
  }
}

TEST_F(LiquidationTest, isolated_liq_subposition_3_time_fail_to_token_over) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "3000000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "3000000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // set risk id
    biz::symbol_t symbol = 5;  // BTCUSDT
    biz::coin_t coin = 5;
    biz::risk_id_t risk_id = 2;
    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid1, risk_id);
    auto resp1 = user1.process(set_risk_id_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  }

  {
    // set risk id
    biz::symbol_t symbol = 5;  // BTCUSDT
    biz::coin_t coin = 5;
    biz::risk_id_t risk_id = 2;
    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid2, risk_id);
    auto resp1 = user2.process(set_risk_id_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "期货订单Result:" << jsonStr << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "期货订单Result:" << jsonStr << std::endl;

    te->PopResult();
  }

  {
    // 挂单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "期货订单Result:" << jsonStr << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "期货订单Result:" << jsonStr << std::endl;

    te->PopResult();
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18110, 18110, 18110);
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    sharding_key = utils::hash(static_cast<std::uint64_t>(uid2) /
                               static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
                   application::GlobalConfig::re_calc_total_user_sharding();
    event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    int32_t before_liq_with_loss = 0;
    int32_t after_liq_with_loss = 0;
    while (true) {
      auto result = te->PopResult(300);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
      std::cout << "有损降档无盘口失败第一次:" << jsonStr << std::endl;

      {
        if (result.m_msg->header().user_id() == uid1) {
          if (result.m_msg->header().action() == EAction::OnRecvMatchingResult) {
            after_liq_with_loss++;

            // 账户状态
            const auto& account_i = result.m_msg->asset_margin_result().account_info();
            ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

            const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
            ASSERT_EQ(liq_records.size(), 0);
            auto o_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
            o_checker.CheckCumQtyX(uid1, 0);
            o_checker.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
            // 资金流
            const auto& translogs = result.m_msg->futures_margin_result().translog();
            ASSERT_EQ(translogs.size(), 0);

          } else if (result.m_msg->header().action() == EAction::LiqCoinExecute) {
            before_liq_with_loss++;

            // 账户状态
            const auto& account_i = result.m_msg->asset_margin_result().account_info();
            ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

            const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
            ASSERT_EQ(liq_records.size(), 1);

            const auto& liq_record = liq_records[0];
            ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithLoss);
            ASSERT_EQ(liq_record.is_finished(), false);
            ASSERT_EQ(liq_record.before_liq_execute(), true);
            ASSERT_EQ(liq_record.has_liq_position(), false);

            // 资金流
            const auto& translogs = result.m_msg->futures_margin_result().translog();
            ASSERT_EQ(translogs.size(), 0);
          }
        } else {
          ASSERT_EQ(true, false);
        }
      }
    }  // end while

    ASSERT_EQ(after_liq_with_loss, 1);
    ASSERT_EQ(before_liq_with_loss, 1);
  }

  {
    // shard_key为0为旧一轮计算结尾，后面的请求触发新一轮计算
    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    int32_t before_liq_with_loss = 0;
    int32_t after_liq_with_loss = 0;
    while (true) {
      auto result = te->PopResult(300);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
      std::cout << "有损降档无盘口失败第二次:" << jsonStr << std::endl;

      {
        if (result.m_msg->header().user_id() == uid1) {
          if (result.m_msg->header().action() != EAction::LiqCoinExecute) {
            after_liq_with_loss++;

            // 账户状态
            const auto& account_i = result.m_msg->asset_margin_result().account_info();
            ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

            const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
            ASSERT_EQ(liq_records.size(), 0);
            auto o_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
            o_checker.CheckCumQtyX(uid1, 0);
            o_checker.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);
            // 资金流
            const auto& translogs = result.m_msg->futures_margin_result().translog();
            ASSERT_EQ(translogs.size(), 0);
          } else {
            before_liq_with_loss++;

            // 账户状态
            const auto& account_i = result.m_msg->asset_margin_result().account_info();
            ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

            const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
            ASSERT_EQ(liq_records.size(), 1);

            const auto& liq_record = liq_records[0];
            ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithLoss);
            ASSERT_EQ(liq_record.is_finished(), false);
            ASSERT_EQ(liq_record.before_liq_execute(), true);
            ASSERT_EQ(liq_record.has_liq_position(), false);

            // 资金流
            const auto& translogs = result.m_msg->futures_margin_result().translog();
            ASSERT_EQ(translogs.size(), 0);
          }
        }
      }
    }  // end while

    ASSERT_EQ(after_liq_with_loss, 1);
    ASSERT_EQ(before_liq_with_loss, 1);
  }

  {
    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    int32_t before_liq_with_loss = 0;
    int32_t after_liq_with_loss = 0;
    while (true) {
      auto result = te->PopResult(300);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
      std::cout << "有损降档无盘口失败第三次:" << jsonStr << std::endl;

      {
        if (result.m_msg->header().user_id() == uid1) {
          if (result.m_msg->header().action() != EAction::LiqCoinExecute) {
            after_liq_with_loss++;

            // 账户状态
            const auto& account_i = result.m_msg->asset_margin_result().account_info();
            ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

            const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
            ASSERT_EQ(liq_records.size(), 0);

            auto o_checker = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
            o_checker.CheckCumQtyX(uid1, 0);
            o_checker.CheckStatus(EOrderStatus::Cancelled, ECrossStatus::Canceled);

            // 资金流
            const auto& translogs = result.m_msg->futures_margin_result().translog();
            ASSERT_EQ(translogs.size(), 0);
          } else {
            before_liq_with_loss++;

            // 账户状态
            const auto& account_i = result.m_msg->asset_margin_result().account_info();
            ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

            const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
            ASSERT_EQ(liq_records.size(), 1);

            const auto& liq_record = liq_records[0];
            ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetRiskWithLoss);
            ASSERT_EQ(liq_record.is_finished(), false);
            ASSERT_EQ(liq_record.before_liq_execute(), true);
            ASSERT_EQ(liq_record.has_liq_position(), false);

            // 资金流
            const auto& translogs = result.m_msg->futures_margin_result().translog();
            ASSERT_EQ(translogs.size(), 0);
          }
        }
      }
    }  // end while

    ASSERT_EQ(after_liq_with_loss, 1);
    ASSERT_EQ(before_liq_with_loss, 1);
  }

  {
    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    int32_t before_liq_takeover_msg_cnt = 0;
    int32_t after_liq_takeover_msg_cnt = 0;
    while (true) {
      auto result = te->PopResult(300);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      ASSERT_NE(result.m_msg.get(), nullptr);

      std::string jsonStr;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
      std::cout << "接管:" << jsonStr << std::endl;

      {
        if (result.m_msg->header().user_id() == uid1) {
          if (result.m_msg->header().action() != EAction::LiqCoinExecute) {
            after_liq_takeover_msg_cnt++;

            // 账户状态
            const auto& account_i = result.m_msg->asset_margin_result().account_info();
            ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

            const auto& fills = result.m_msg->futures_margin_result().related_fills();
            ASSERT_EQ(fills.size(), 1);

            const auto& fill = fills[0];
            ASSERT_EQ(fill.exec_fee_e8(), 648 * 1e8);
            ASSERT_EQ(fill.fee_rate_e8(), 6 * 1e4);

            const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
            ASSERT_EQ(liq_records.size(), 1);

            const auto& liq_record = liq_records[0];
            ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
            ASSERT_EQ(liq_record.is_finished(), true);
            ASSERT_EQ(liq_record.before_liq_execute(), false);
            ASSERT_EQ(liq_record.has_liq_position(), true);

            // 资金流
            const auto& translogs = result.m_msg->futures_margin_result().translog();
            ASSERT_EQ(translogs.size(), 1);

            const auto& translog = translogs[0];
            ASSERT_EQ(translog.platformfee(), "0.0");
            ASSERT_EQ(translog.fee_rate(), "0.0006**********0000");
            ASSERT_EQ(translog.exec_fee(), "-648.**********00000000");
            ASSERT_EQ(translog.exec_qty(), "60.**********00000000");
            ASSERT_EQ(translog.exec_price(), "18000.**********00000000");
            ASSERT_EQ(translog.trans_type(), store::TransLog::TransTypeEnum::kTransTypeLiquidation);
            ASSERT_EQ(translog.cash_flow(), "-120000.**********00000000");
            ASSERT_EQ(translog.change(), "-120648.**********00000000");
          } else {
            before_liq_takeover_msg_cnt++;

            // 账户状态
            const auto& account_i = result.m_msg->asset_margin_result().account_info();
            ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

            const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
            ASSERT_EQ(liq_records.size(), 1);

            const auto& liq_record = liq_records[0];
            ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
            ASSERT_EQ(liq_record.is_finished(), false);
            ASSERT_EQ(liq_record.before_liq_execute(), true);
            ASSERT_EQ(liq_record.has_liq_position(), false);

            // 资金流
            const auto& translogs = result.m_msg->futures_margin_result().translog();
            ASSERT_EQ(translogs.size(), 0);
          }
        }
      }
    }  // end while

    ASSERT_EQ(after_liq_takeover_msg_cnt, 1);
    ASSERT_EQ(before_liq_takeover_msg_cnt, 1);
  }
}

TEST_F(LiquidationTest, isolated_liq_subposition_to_normal_to_subposition) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "3000000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "3000000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // set risk id
    biz::symbol_t symbol = 5;  // BTCUSDT
    biz::coin_t coin = 5;
    biz::risk_id_t risk_id = 2;
    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid1, risk_id);
    auto resp1 = user1.process(set_risk_id_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  }

  {
    // set risk id
    biz::symbol_t symbol = 5;  // BTCUSDT
    biz::coin_t coin = 5;
    biz::risk_id_t risk_id = 2;
    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid2, risk_id);
    auto resp1 = user2.process(set_risk_id_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // 挂单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18110, 18110, 18110);
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    while (true) {
      auto result = te->PopResult(200);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "有损降档:" << jsonStr2 << std::endl;
    }
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "有损降档:" << jsonStr2 << std::endl;
    }
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    while (true) {
      auto result = te->PopResult(200);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "有损降档:" << jsonStr2 << std::endl;
    }
  }

  {
    // 模拟有损未成交，价格恢复
    te->AddMarkPrice(static_cast<ESymbol>(5), 18300, 18300, 18300);
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  }

  {
    auto result = te->PopResult(200);
    if (result.m_msg.get() != nullptr) {
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "有损降档价格恢复dump:" << jsonStr2 << std::endl;
    }
  }

  {
    // 再次进入强平,走有损
    te->AddMarkPrice(static_cast<ESymbol>(5), 18110, 18110, 18110);
  }

  {
    // 挂单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "10", "18000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    {
      auto result = te->PopResult(200);
      if (result.m_msg.get() != nullptr) {
        // ASSERT_NE(result.m_msg.get(), nullptr);
        std::string jsonStr2;
        (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
        std::cout << "发起有损降档:" << jsonStr2 << std::endl;
      }
    }

    {
      auto result = te->PopResult();
      if (result.m_msg.get() != nullptr) {
        // ASSERT_NE(result.m_msg.get(), nullptr);
        std::string jsonStr2;
        (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
        std::cout << "有损降档撮合回报:" << jsonStr2 << std::endl;
        auto order_check = result.RefFutureMarginResult().RefRelatedOrderByIndex(0);
        order_check.CheckCreateType(uid1, ECreateType::CreateByLiq);
      }
    }
    {
      auto result = te->PopResult();
      if (result.m_msg.get() != nullptr) {
        std::string jsonStr2;
        (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
        std::cout << "对手方撮合回报:" << jsonStr2 << std::endl;
      }
    }
  }
}

TEST_F(LiquidationTest, isolated_liq_add_margin) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "3000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "3000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
    te->PopResult();
  }

  {
    FutureSetAutoAddMarginBuilder build("", uid1, 5, 5, true, static_cast<EPositionIndex>(0));
    user1.process(build.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18080, 18080, 18080);
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr1;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr1);
    std::cout << "追加保证金:" << jsonStr1 << std::endl;

    {
      int32_t after_liq_auto_add_margin = 0;
      if (result.m_msg->header().user_id() == uid1) {
        if (result.m_msg->header().action() == EAction::LiqCoinExecute) {
          after_liq_auto_add_margin++;

          // 账户状态
          result.RefAssetMarginResult().RefRelatedAccountInfo().CheckAccountStatus(enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqAddMargin);
          ASSERT_EQ(liq_record.is_finished(), true);
          ASSERT_EQ(liq_record.before_liq_execute(), false);
          ASSERT_EQ(liq_record.has_liq_position(), true);

          // 倉位狀態
          auto positionCheck = result.RefFutureMarginResult().RefRelatedPosition(0);
          ASSERT_NE(positionCheck.m_msg, nullptr);
          positionCheck.CheckBustPrice(*********);
          positionCheck.CheckLiqPrice(*********);
          positionCheck.CheckPb(************);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        } else {
          ASSERT_EQ(true, false);
        }
      }

      ASSERT_EQ(after_liq_auto_add_margin, 1);
    }
  }

  {
    te->AddMarkPrice(static_cast<ESymbol>(5), 17000, 17000, 17000);
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    int32_t before_liq_takeover_msg_cnt = 0;
    int32_t after_liq_takeover_msg_cnt = 0;
    while (true) {
      auto result1 = te->PopResult(300);
      if (result1.m_msg == nullptr) {
        return;
      }

      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result1.m_msg.get(), &jsonStr2);
      // 打印4次，接管戶和被接管戶，發撮合，收撮合各1次
      std::cout << "接管:" << jsonStr2 << std::endl;

      if (result1.m_msg->header().user_id() == uid1) {
        if (result1.m_msg->header().action() != EAction::LiqCoinExecute) {
          after_liq_takeover_msg_cnt++;

          // 账户状态
          const auto& account_i = result1.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          // 成交
          const auto& fills = result1.m_msg->futures_margin_result().related_fills();
          ASSERT_EQ(fills.size(), 1);

          const auto& fill = fills[0];
          ASSERT_EQ(fill.exec_fee_e8(), **********);
          ASSERT_EQ(fill.fee_rate_e8(), 6 * 1e4);

          const auto& liq_records = result1.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
          ASSERT_EQ(liq_record.is_finished(), true);
          ASSERT_EQ(liq_record.before_liq_execute(), false);
          ASSERT_EQ(liq_record.has_liq_position(), true);

          // 资金流
          const auto& translogs = result1.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 1);

          const auto& translog = translogs[0];
          ASSERT_EQ(translog.platformfee(), "0.0");
          ASSERT_EQ(translog.fee_rate(), "0.0006**********0000");
          ASSERT_EQ(translog.exec_fee(), "-10.20738**********000");
          ASSERT_EQ(translog.exec_qty(), "1.**********00000000");
          ASSERT_EQ(translog.exec_price(), "17012.3**********0000000");
          ASSERT_EQ(translog.trans_type(), store::TransLog::TransTypeEnum::kTransTypeLiquidation);
          ASSERT_EQ(translog.cash_flow(), "-2987.7**********0000000");
          ASSERT_EQ(translog.change(), "-2997.90738**********000");
        } else {
          before_liq_takeover_msg_cnt++;

          // 账户状态 逐仓不允许修改账户状态
          const auto& account_i = result1.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result1.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
          ASSERT_EQ(liq_record.is_finished(), false);
          ASSERT_EQ(liq_record.before_liq_execute(), true);
          ASSERT_EQ(liq_record.has_liq_position(), false);

          // 资金流
          const auto& translogs = result1.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        }
      }
    }  // end while

    ASSERT_EQ(after_liq_takeover_msg_cnt, 1);
    ASSERT_EQ(before_liq_takeover_msg_cnt, 1);
  }
}

TEST_F(LiquidationTest, isolated_liq_sell_add_margin) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "3000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "3000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid2);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid2, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
    te->PopResult();
  }

  {
    FutureSetAutoAddMarginBuilder build("", uid2, 5, 5, true, static_cast<EPositionIndex>(0));
    user1.process(build.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 21950, 21950, 21950);
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    sharding_key = utils::hash(static_cast<std::uint64_t>(uid2) /
                               static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
                   application::GlobalConfig::re_calc_total_user_sharding();
    event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr1;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr1);
    std::cout << "追加保证金:" << jsonStr1 << std::endl;

    {
      int32_t after_liq_auto_add_margin = 0;
      if (result.m_msg->header().user_id() == uid2) {
        if (result.m_msg->header().action() == EAction::LiqCoinExecute) {
          after_liq_auto_add_margin++;

          // 账户状态
          result.RefAssetMarginResult().RefRelatedAccountInfo().CheckAccountStatus(enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqAddMargin);
          ASSERT_EQ(liq_record.is_finished(), true);
          ASSERT_EQ(liq_record.before_liq_execute(), false);
          ASSERT_EQ(liq_record.has_liq_position(), true);

          // 倉位狀態
          auto positionCheck = result.RefFutureMarginResult().RefRelatedPosition(0);
          ASSERT_NE(positionCheck.m_msg, nullptr);
          positionCheck.CheckPb(************);
          positionCheck.CheckBustPrice(*********);
          positionCheck.CheckLiqPrice(*********);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        } else {
          ASSERT_EQ(true, false);
        }
      }

      ASSERT_EQ(after_liq_auto_add_margin, 1);
    }
  }

  {
    te->AddMarkPrice(static_cast<ESymbol>(5), 22900, 22900, 22900);

    // shard_key为0为旧一轮计算结尾，后面的请求触发新一轮计算
    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    int32_t before_liq_takeover_msg_cnt = 0;
    int32_t after_liq_takeover_msg_cnt = 0;
    while (true) {
      auto result1 = te->PopResult(300);
      if (result1.m_msg == nullptr) {
        return;
      }

      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result1.m_msg.get(), &jsonStr2);
      // 打印4次，接管戶和被接管戶，發撮合，收撮合各1次
      std::cout << "接管:" << jsonStr2 << std::endl;

      if (result1.m_msg->header().user_id() == uid2) {
        if (result1.m_msg->header().action() != EAction::LiqCoinExecute) {
          after_liq_takeover_msg_cnt++;

          // 账户状态
          const auto& account_i = result1.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          // 成交
          const auto& fills = result1.m_msg->futures_margin_result().related_fills();
          ASSERT_EQ(fills.size(), 1);

          const auto& fill = fills[0];
          ASSERT_EQ(fill.exec_fee_e8(), **********);
          ASSERT_EQ(fill.fee_rate_e8(), 6 * 1e4);

          const auto& liq_records = result1.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
          ASSERT_EQ(liq_record.is_finished(), true);
          ASSERT_EQ(liq_record.before_liq_execute(), false);
          ASSERT_EQ(liq_record.has_liq_position(), true);

          // 资金流
          const auto& translogs = result1.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 1);

          const auto& translog = translogs[0];
          EXPECT_EQ(translog.platformfee(), "0.0");
          EXPECT_EQ(translog.fee_rate(), "0.0006**********0000");
          EXPECT_EQ(translog.exec_fee(), "-13.78452**********000");
          EXPECT_EQ(translog.exec_qty(), "1.**********00000000");
          EXPECT_EQ(translog.exec_price(), "22974.2**********0000000");
          EXPECT_EQ(translog.trans_type(), store::TransLog::TransTypeEnum::kTransTypeLiquidation);
          EXPECT_EQ(translog.cash_flow(), "-2974.2**********0000000");
          EXPECT_EQ(translog.change(), "-2987.98452**********000");
        } else {
          before_liq_takeover_msg_cnt++;

          // 账户状态 逐仓不允许修改账户状态
          const auto& account_i = result1.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result1.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
          ASSERT_EQ(liq_record.is_finished(), false);
          ASSERT_EQ(liq_record.before_liq_execute(), true);
          ASSERT_EQ(liq_record.has_liq_position(), false);

          // 资金流
          const auto& translogs = result1.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        }
      }
    }  // end while

    ASSERT_EQ(after_liq_takeover_msg_cnt, 1);
    ASSERT_EQ(before_liq_takeover_msg_cnt, 1);
  }
}

TEST_F(LiquidationTest, isolated_liqing_check) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "2050", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "3000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
    te->PopResult();
  }

  {
    FutureSetAutoAddMarginBuilder build("", uid1, 5, 5, true, static_cast<EPositionIndex>(0));
    user1.process(build.Build());
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 17100, 17100, 17100);
  }

  te->SuspendCross();

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    {
      // 设置lastPrice
      te->AddMarkPrice(static_cast<ESymbol>(5), 19000, 19000, 19000);
    }

    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    te->ResumeCross();

    {
      auto result = te->PopResult();
      ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr1;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr1);
      std::cout << "追加保证金:" << jsonStr1 << std::endl;

      int32_t after_liq_auto_add_margin = 0;
      if (result.m_msg->header().user_id() == uid1) {
        if (result.m_msg->header().action() == EAction::LiqCoinExecute) {
          after_liq_auto_add_margin++;

          // 账户状态
          result.RefAssetMarginResult().RefRelatedAccountInfo().CheckAccountStatus(enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          // 追加保證金後不足，走到接管
          ASSERT_EQ(liq_records.size(), 2);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqAddMargin);
          ASSERT_EQ(liq_record.is_finished(), false);
          ASSERT_EQ(liq_record.before_liq_execute(), false);
          ASSERT_EQ(liq_record.has_liq_position(), true);

          // 倉位狀態
          auto positionCheck = result.RefFutureMarginResult().RefRelatedPosition(0);
          ASSERT_NE(positionCheck.m_msg, nullptr);
          positionCheck.CheckBustPrice(*********);
          positionCheck.CheckLiqPrice(*********);
          positionCheck.CheckPb(************);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        } else {
          ASSERT_EQ(true, false);
        }
      }

      ASSERT_EQ(after_liq_auto_add_margin, 1);
    }

    {
      auto result1 = te->PopResult();
      ASSERT_NE(result1.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result1.m_msg.get(), &jsonStr2);
      std::cout << "接管:" << jsonStr2 << std::endl;

      int32_t after_liq_takeover = 0;
      if (result1.m_msg->header().user_id() == uid1) {
        if (result1.m_msg->header().action() != EAction::LiqCoinExecute) {
          after_liq_takeover++;

          // 账户状态
          const auto& account_i = result1.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          // 成交
          const auto& fills = result1.m_msg->futures_margin_result().related_fills();
          ASSERT_EQ(fills.size(), 1);

          const auto& fill = fills[0];
          ASSERT_EQ(fill.exec_fee_e8(), **********);
          ASSERT_EQ(fill.fee_rate_e8(), 6 * 1e4);

          const auto& liq_records = result1.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
          ASSERT_EQ(liq_record.is_finished(), true);
          ASSERT_EQ(liq_record.before_liq_execute(), false);
          ASSERT_EQ(liq_record.has_liq_position(), true);

          // 资金流
          const auto& translogs = result1.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 1);

          const auto& translog = translogs[0];
          ASSERT_EQ(translog.platformfee(), "0.0");
          ASSERT_EQ(translog.fee_rate(), "0.0006**********0000");
          ASSERT_EQ(translog.exec_fee(), "-10.77768**********000");
          ASSERT_EQ(translog.exec_qty(), "1.**********00000000");
          ASSERT_EQ(translog.exec_price(), "17962.8**********0000000");
          ASSERT_EQ(translog.trans_type(), store::TransLog::TransTypeEnum::kTransTypeLiquidation);
          ASSERT_EQ(translog.cash_flow(), "-2037.2**********0000000");
          ASSERT_EQ(translog.change(), "-2047.97768**********000");
        }
      }
      ASSERT_EQ(after_liq_takeover, 1);
    }
  }
}

TEST_F(LiquidationTest, isolated_liq_with_funding) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "2100", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "3000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
    te->PopResult();
  }
  te->SendFundingReqToCross(5, 5, 5, 18**********0, 1000000);

  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "settle funding uid2:" << jsonStr << std::endl;
  }

  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "settle funding uid1:" << jsonStr << std::endl;
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 17100, 17100, 17100);
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result1.m_msg.get(), &jsonStr2);
    std::cout << "接管:" << jsonStr2 << std::endl;
  }
}

TEST_F(LiquidationTest, isolated_liqing_sell_pre_market_take_over) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "3000000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "3000000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(ESymbol(180)), 20000, 20000, 20000);
  }
  {
    // set risk id
    biz::symbol_t symbol = ESymbol(180);  // BTCUSDT
    biz::coin_t coin = 5;
    biz::risk_id_t risk_id = 2;
    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid1, risk_id);
    auto resp1 = user1.process(set_risk_id_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  }

  {
    // set risk id
    biz::symbol_t symbol = ESymbol(180);  // BTCUSDT
    biz::coin_t coin = 5;
    biz::risk_id_t risk_id = 2;
    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid2, risk_id);
    auto resp1 = user2.process(set_risk_id_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "ALPHAUSDT", 0, "Buy", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "ALPHAUSDT", 0, "Sell", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // 挂单
    OpenApiV5CreateOrderBuilder build("linear", "ALPHAUSDT", 0, "Buy", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "ALPHAUSDT", 0, "Sell", "Limit", "30", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(ESymbol(180)), 18180, 18180, 18180);
  }
  auto now_ns = bbase::utils::Time::GetTimeNs();
  // 修改s2时间为当前，s3为100ms后，s4为200ms后
  UpdatePreMarketTime(now_ns - 2 * 1e9, now_ns - 1 * 1e9, now_ns, now_ns + 1 * 1e8, now_ns + 8 * 1e9);

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    {
      auto result = te->PopResult();
      if (result.m_msg.get() != nullptr) {
        // ASSERT_NE(result.m_msg.get(), nullptr);
        std::string jsonStr2;
        (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
        std::cout << "直接接管:" << jsonStr2 << std::endl;
      }
    }
  }
  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    {
      auto result = te->PopResult();
      if (result.m_msg.get() != nullptr) {
        // ASSERT_NE(result.m_msg.get(), nullptr);
        std::string jsonStr2;
        (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
        std::cout << "直接接管:" << jsonStr2 << std::endl;
        result.RefFutureMarginResult()
            .RefRelatedOrderByIndex(0)
            .CheckCreateType(uid1, ECreateType::CreateByTakeOver_PassThrough)
            .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);
      }
    }
  }
}

TEST_F(LiquidationTest, isolated_liqing_sell_take_over) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "2050", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "2050", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid2);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_20000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid2, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "形成持仓:" << jsonStr2 << std::endl;
    te->PopResult();
  }

  {
    // 设置MarkPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 22000, 22000, 22000);
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    int32_t before_liq_takeover_msg_cnt = 0;
    int32_t after_liq_takeover_msg_cnt = 0;
    while (true) {
      auto result = te->PopResult(300);
      if (result.m_msg == nullptr) {
        return;
      }
      std::string jsonStr;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
      std::cout << "接管:" << jsonStr << std::endl;

      if (result.m_msg->header().user_id() == uid2) {
        if (result.m_msg->header().action() != EAction::LiqCoinExecute) {
          after_liq_takeover_msg_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          // 成交
          const auto& fills = result.m_msg->futures_margin_result().related_fills();
          ASSERT_EQ(fills.size(), 1);

          const auto& fill = fills[0];
          ASSERT_EQ(fill.exec_fee_e8(), **********);
          ASSERT_EQ(fill.fee_rate_e8(), 6 * 1e4);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
          ASSERT_EQ(liq_record.is_finished(), true);
          ASSERT_EQ(liq_record.before_liq_execute(), false);
          ASSERT_EQ(liq_record.has_liq_position(), true);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 1);

          const auto& translog = translogs[0];
          ASSERT_EQ(translog.platformfee(), "0.0");
          ASSERT_EQ(translog.fee_rate(), "0.0006**********0000");
          ASSERT_EQ(translog.exec_fee(), "-13.2**********0000000");
          ASSERT_EQ(translog.exec_qty(), "1.**********00000000");
          ASSERT_EQ(translog.exec_price(), "22000.**********00000000");
          ASSERT_EQ(translog.trans_type(), store::TransLog::TransTypeEnum::kTransTypeLiquidation);
          ASSERT_EQ(translog.cash_flow(), "-2000.**********00000000");
          ASSERT_EQ(translog.change(), "-2013.2**********0000000");
        } else {
          before_liq_takeover_msg_cnt++;

          // 账户状态 逐仓不允许修改账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
          ASSERT_EQ(liq_record.is_finished(), false);
          ASSERT_EQ(liq_record.before_liq_execute(), true);
          ASSERT_EQ(liq_record.has_liq_position(), false);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        }
      }
    }  // end while

    ASSERT_EQ(after_liq_takeover_msg_cnt, 1);
    ASSERT_EQ(before_liq_takeover_msg_cnt, 1);
  }
}

/**
 * 持仓升级中遇到逐仓强平，直接跳过不发生强平
 */
TEST_F(LiquidationTest, isolated_liqing_hit_upgrading) {
  // UID1
  biz::user_id_t uid1 = 40000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 50000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "2050", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "2050", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid2);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_20000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid2, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "形成持仓:" << jsonStr2 << std::endl;
    te->PopResult();
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 22000, 22000, 22000);
  }

  userUpgradingLabel(uid1);
  userUpgradingLabel(uid2);

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result1 = te->PopResult();
    ASSERT_EQ(result1.m_msg.get(), nullptr);
  }
}

TEST_F(LiquidationTest, isolated_liq_check_liq_price_backstop) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "5000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    std::string usdt_order_id;
    std::string usdt_order_link_id = "6b0f2f11-c87a-4594-ac61-c2ae0aba51b3";
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.1", "10100");
    create_build.SetOrderLinkID(usdt_order_link_id);
    create_build.SetTrigger("MarkPrice", 2, "10050");  // 1-rises 2-falls
    auto resp = user1.create_order(create_build.Build());
    ASSERT_NE(resp->order_id(), "");
    ASSERT_STREQ(resp->order_link_id().c_str(), usdt_order_link_id.c_str());
    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(usdt_order_link_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    order_check.CheckQty(uid1, 10000000);
    order_check.CheckPrice(uid1, 10100 * 1e4);
    order_check.CheckExecInst(uid1, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
    usdt_order_id = resp->order_id();
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    auto r = te->PopResult();
    if (r.m_msg != nullptr) {
      P(*r.m_msg)
    }
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18100, 18100, 18100);
  }

  // 调低合约默认费率
  auto sc = const_cast<biz::SymbolConfig*>(
      config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(enums::esymbol::BTCUSDT));
  sc->default_taker_fee_rate_e8_ = 40000;

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    sharding_key = utils::hash(static_cast<std::uint64_t>(uid2) /
                               static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
                   application::GlobalConfig::re_calc_total_user_sharding();
    event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);

      std::cout << "rebalance后自动恢复:" << jsonStr2 << std::endl;

      auto affect_positions = result.m_msg->futures_margin_result().affected_positions();
      ASSERT_EQ(affect_positions.size(), 1);
      ASSERT_EQ(affect_positions[0].liq_price_x(), 180964000);
      ASSERT_EQ(affect_positions[0].bust_price_x(), 179964000);
    }
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18080, 18080, 18080);
  }

  {
    te->SuspendCross();
    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  }

  {
    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    te->ResumeCross();

    int32_t take_over_op_cnt = 0;
    int32_t matching_result_msg_cnt = 0;
    int32_t before_liq_msg_cnt = 0;
    int32_t takeover_before_liq_record_cnt = 0;
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }

      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "强平执行结果:" << jsonStr2 << std::endl;

      if (result.m_msg->header().user_id() == 5002) {
        continue;
      }

      // 2 - cancle all
      if (result.m_msg->header().action() == EAction::OnRecvMatchingResult) {
        matching_result_msg_cnt++;

        if (matching_result_msg_cnt == 1) {
          take_over_op_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);
          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.is_finished(), true);                                          // 接管回来
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);  // 5-接管
          ASSERT_EQ(liq_record.before_liq_execute(), false);
          ASSERT_EQ(liq_record.has_liq_position(), true);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 1);

          const auto& translog = translogs[0];
          ASSERT_EQ(translog.platformfee(), "0.0");
          ASSERT_EQ(translog.fee_rate(), "0.0004**********0000");
          ASSERT_EQ(translog.exec_fee(), "-7.19856**********000");
          ASSERT_EQ(translog.exec_qty(), "1.**********00000000");
          ASSERT_EQ(translog.exec_price(), "17996.4**********0000000");
          ASSERT_EQ(translog.trans_type(), store::TransLog::TransTypeEnum::kTransTypeLiquidation);
          ASSERT_EQ(translog.cash_flow(), "-2003.6**********0000000");
          ASSERT_EQ(translog.change(), "-2010.79856**********000");
        } else {
          ASSERT_EQ(true, false);
        }
      } else if (result.m_msg->header().action() == EAction::LiqCoinExecute) {
        before_liq_msg_cnt++;

        if (before_liq_msg_cnt == 1) {
          takeover_before_liq_record_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);  // 5-接管
          ASSERT_EQ(liq_record.is_finished(), false);                                         // 接管回来
          ASSERT_EQ(liq_record.before_liq_execute(), true);
          ASSERT_EQ(liq_record.has_liq_position(), false);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        } else {
          ASSERT_EQ(true, false);
        }
      } else if (result.m_msg->header().action() == EAction::UpdateUserLoadStatusToOut) {
      } else {
        ASSERT_EQ(true, false);
      }
    }

    ASSERT_EQ(take_over_op_cnt, 1);

    ASSERT_EQ(takeover_before_liq_record_cnt, 1);
  }
}

TEST_F(LiquidationTest, isolated_liq_max_price_backstop) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "5000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid2);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_20000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_20000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid2, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    std::string usdt_order_id;
    std::string usdt_order_link_id = "6b0f2f11-c87a-4594-ac61-c2ae0aba51b3";
    OpenApiV5CreateOrderBuilder create_build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.1", "10100");
    create_build.SetOrderLinkID(usdt_order_link_id);
    create_build.SetTrigger("MarkPrice", 2, "10050");  // 1-rises 2-falls
    auto resp = user1.create_order(create_build.Build());
    ASSERT_NE(resp->order_id(), "");
    ASSERT_STREQ(resp->order_link_id().c_str(), usdt_order_link_id.c_str());
    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto order_check = result.RefFutureMarginResult().RefRelatedOrderByOrderLinkId(usdt_order_link_id);
    ASSERT_NE(order_check.m_msg, nullptr);
    order_check.CheckQty(uid1, 10000000);
    order_check.CheckPrice(uid1, 10100 * 1e4);
    order_check.CheckExecInst(uid1, EExecInst::None);
    order_check.CheckStatus(EOrderStatus::Untriggered, ECrossStatus::Init);
    usdt_order_id = resp->order_id();
  }

  {
    // 修改合约最大价格
    auto sc = const_cast<biz::SymbolConfig*>(
        config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(enums::esymbol::BTCUSDT));
    sc->max_price_x_ = 215000000;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    P(*result.m_msg)
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    auto r = te->PopResult();
    if (r.m_msg != nullptr) {
      P(*r.m_msg)
    }
  }

  {
    // 修改合约最大价格
    auto sc = const_cast<biz::SymbolConfig*>(
        config::getTlsCfgMgrRaw()->symbol_config_mgr()->GetSymbolConfig(enums::esymbol::BTCUSDT));
    sc->max_price_x_ = 300000000;
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 21501, 21501, 21501);
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    sharding_key = utils::hash(static_cast<std::uint64_t>(uid2) /
                               static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
                   application::GlobalConfig::re_calc_total_user_sharding();
    event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);

      std::cout << "rebalance后自动恢复:" << jsonStr2 << std::endl;

      auto affect_positions = result.m_msg->futures_margin_result().affected_positions();
      ASSERT_EQ(affect_positions.size(), 1);
      ASSERT_EQ(affect_positions[0].liq_price_x(), 219000000);
      ASSERT_EQ(affect_positions[0].bust_price_x(), 220000000);
    }
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 22000, 22000, 22000);
  }

  {
    te->SuspendCross();
    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  }

  {
    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    te->ResumeCross();

    int32_t take_over_op_cnt = 0;
    int32_t matching_result_msg_cnt = 0;
    int32_t before_liq_msg_cnt = 0;
    int32_t takeover_before_liq_record_cnt = 0;
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }

      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "强平执行结果:" << jsonStr2 << std::endl;

      if (result.m_msg->header().user_id() == 5002) {
        continue;
      }

      // 2 - cancle all
      if (result.m_msg->header().action() == EAction::OnRecvMatchingResult) {
        matching_result_msg_cnt++;

        if (matching_result_msg_cnt == 1) {
          take_over_op_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);
          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.is_finished(), true);                                          // 接管回来
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);  // 5-接管
          ASSERT_EQ(liq_record.before_liq_execute(), false);
          ASSERT_EQ(liq_record.has_liq_position(), true);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 1);

          const auto& translog = translogs[0];
          ASSERT_EQ(translog.platformfee(), "0.0");
          ASSERT_EQ(translog.fee_rate(), "0.0006**********0000");
          ASSERT_EQ(translog.exec_fee(), "-13.2**********0000000");
          ASSERT_EQ(translog.exec_qty(), "1.**********00000000");
          ASSERT_EQ(translog.exec_price(), "22000.**********00000000");
          ASSERT_EQ(translog.trans_type(), store::TransLog::TransTypeEnum::kTransTypeLiquidation);
          ASSERT_EQ(translog.cash_flow(), "-2000.**********00000000");
          ASSERT_EQ(translog.change(), "-2013.2**********0000000");
        } else {
          ASSERT_EQ(true, false);
        }
      } else if (result.m_msg->header().action() == EAction::LiqCoinExecute) {
        before_liq_msg_cnt++;

        if (before_liq_msg_cnt == 1) {
          takeover_before_liq_record_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);  // 5-接管
          ASSERT_EQ(liq_record.is_finished(), false);                                         // 接管回来
          ASSERT_EQ(liq_record.before_liq_execute(), true);
          ASSERT_EQ(liq_record.has_liq_position(), false);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        } else {
          ASSERT_EQ(true, false);
        }
      } else if (result.m_msg->header().action() == EAction::UpdateUserLoadStatusToOut) {
      } else {
        ASSERT_EQ(true, false);
      }
    }

    ASSERT_EQ(take_over_op_cnt, 1);

    ASSERT_EQ(takeover_before_liq_record_cnt, 1);
  }
}
