#include "liquidation/liquidation_test.hpp"
#include "src/biz_worker/service/trade/liquidation/event/liq_event.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(LiquidationTest, isolated_liq_usdc) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "2100", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 16;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDC);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCPERP", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCPERP", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    te->PopResult();
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "形成持仓:" << jsonStr2 << std::endl;
  }

  te->SendFundingReqToCross(34, 45, 16, 1800000000000, 1000000);

  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "settle funding uid2:" << jsonStr << std::endl;
  }

  {
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "settle funding uid1:" << jsonStr << std::endl;
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(45), 18080, 18080, 18080);
  }

  {
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    int32_t before_liq_takeover_msg_cnt = 0;
    int32_t after_liq_takeover_msg_cnt = 0;
    while (true) {
      auto result = te->PopResult(1000);
      if (result.m_msg.get() == nullptr) {
        break;
      }
      // ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "接管结果:" << jsonStr2 << std::endl;

      if (result.m_msg->header().user_id() == uid1) {
        if (result.m_msg->header().action() != EAction::LiqCoinExecute) {
          after_liq_takeover_msg_cnt++;

          // 账户状态
          const auto& account_i = result.m_msg->asset_margin_result().account_info();
          ASSERT_EQ(account_i.account_status(), enums::eaccountstatus::Normal);

          // 成交
          const auto& fills = result.m_msg->futures_margin_result().related_fills();
          ASSERT_EQ(fills.size(), 1);

          const auto& fill = fills[0];
          ASSERT_EQ(fill.exec_fee_e8(), **********);
          ASSERT_EQ(fill.fee_rate_e8(), 6 * 1e4);

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
          ASSERT_EQ(liq_record.is_finished(), true);
          ASSERT_EQ(liq_record.before_liq_execute(), false);
          ASSERT_EQ(liq_record.has_liq_position(), true);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 1);

          const auto& translog = translogs[0];
          ASSERT_EQ(translog.platformfee(), "0.0");
          ASSERT_EQ(translog.fee_rate(), "0.000600000000000000");
          ASSERT_EQ(translog.exec_fee(), "-10.855740000000000000");
          ASSERT_EQ(translog.exec_qty(), "1.000000000000000000");
          ASSERT_EQ(translog.exec_price(), "18092.900000000000000000");
          ASSERT_EQ(translog.trans_type(), store::TransLog::TransTypeEnum::kTransTypeLiquidation);
          ASSERT_EQ(translog.cash_flow(), "92.900000000000000000");
          ASSERT_EQ(translog.change(), "82.044260000000000000");
        } else {
          before_liq_takeover_msg_cnt++;

          const auto& liq_records = result.m_msg->asset_margin_result().liq_record();
          ASSERT_EQ(liq_records.size(), 1);

          const auto& liq_record = liq_records[0];
          ASSERT_EQ(liq_record.liq_action(), enums::eliqaction::LiqAction::LiqSetTakenOver);
          ASSERT_EQ(liq_record.is_finished(), false);
          ASSERT_EQ(liq_record.before_liq_execute(), true);
          ASSERT_EQ(liq_record.has_liq_position(), false);

          // 资金流
          const auto& translogs = result.m_msg->futures_margin_result().translog();
          ASSERT_EQ(translogs.size(), 0);
        }
      }
    }  // end while

    ASSERT_EQ(after_liq_takeover_msg_cnt, 1);
    ASSERT_EQ(before_liq_takeover_msg_cnt, 1);
  }
}
