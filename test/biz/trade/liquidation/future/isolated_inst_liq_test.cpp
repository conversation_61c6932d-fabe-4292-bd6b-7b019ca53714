#include "liquidation/liquidation_test.hpp"
#include "src/biz_worker/service/trade/liquidation/event/liq_event.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(LiquidationTest, isolated_inst_liq_OTC) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "5000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置MarkPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  //  {
  //    // set risk id
  //    biz::symbol_t symbol = 5;  // BTCUSDT
  //    biz::coin_t coin = 5;
  //    biz::risk_id_t risk_id = 2;
  //    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid1, risk_id);
  //    auto resp1 = user1.process(set_risk_id_build.Build());
  //    auto result1 = te->PopResult();
  //    ASSERT_NE(result1.m_msg.get(), nullptr);
  //    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
  //    ASSERT_NE(positionCheck.m_msg, nullptr);
  //    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  //  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2期货订单Result:" << jsonStr2 << std::endl;

    auto pz_sell_order_result = te->PopResult();

    // auto user2_pz_check = pz_sell_order_result.RefFutureMarginResult().RefRelatedPosition(0);
    // user2_pz_check.CheckSize(pz_qty);
    // user2_pz_check.CheckFreeSize(pz_qty);
  }

  {
    // 机构借贷交易封禁
    InstBanOrUnban(uid1, true, true, true, true, true, true, true, true, true);

    // 机构借贷取消订单
    InstCancelAll(uid1);

    // 机构借贷平仓
    InstClosePz(uid1, false);
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18180, 18180, 18180);
    // te->SuspendCross();
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    // 恢复cross
    te->ResumeCross();
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "驱动结果:" << jsonStr2 << std::endl;
    }
  }
}

TEST_F(LiquidationTest, isolated_inst_liq_subposition) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "3000000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "3000000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // set risk id
    biz::symbol_t symbol = 5;  // BTCUSDT
    biz::coin_t coin = 5;
    biz::risk_id_t risk_id = 2;
    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid1, risk_id);
    auto resp1 = user1.process(set_risk_id_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  }

  {
    // set risk id
    biz::symbol_t symbol = 5;  // BTCUSDT
    biz::coin_t coin = 5;
    biz::risk_id_t risk_id = 2;
    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid2, risk_id);
    auto resp1 = user2.process(set_risk_id_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // 挂单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "10", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "挂对手盘 Result:" << jsonStr2 << std::endl;
  }

  {
    // 机构借贷交易封禁
    InstBanOrUnban(uid1, true, true, true, true, true, true, true, true, true);

    // 机构借贷取消订单
    InstCancelAll(uid1);

    // 机构借贷平仓
    InstClosePz(uid1, false);
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "回报:" << jsonStr2 << std::endl;
    }
  }
}

TEST_F(LiquidationTest, isolated_inst_pre_market_liq_takeover) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "3000000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "10000";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "3000000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 切换到逐仓
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::SwitchMarginMode);
    unified_v_2_request_dto->mutable_req_header()->set_req_id("switch_margin_mode_10000_1");
    unified_v_2_request_dto->mutable_req_header()->set_coin(ECoin::USDT);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_accountmode(
        EAccountMode::Isolated);
    unified_v_2_request_dto->mutable_walletreqgroupbody()->mutable_marginmodereq()->set_trans_id(
        "switch_margin_mode_10000_1");
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "切逐仓Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(ESymbol(180)), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // set risk id
    biz::symbol_t symbol = ESymbol(180);  // BTCUSDT
    biz::coin_t coin = 5;
    biz::risk_id_t risk_id = 2;
    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid1, risk_id);
    auto resp1 = user1.process(set_risk_id_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  }

  {
    // set risk id
    biz::symbol_t symbol = ESymbol(180);  // BTCUSDT
    biz::coin_t coin = 5;
    biz::risk_id_t risk_id = 2;
    FutureOneOfSetRiskIdBuilder set_risk_id_build(symbol, coin, uid2, risk_id);
    auto resp1 = user2.process(set_risk_id_build.Build());
    auto result1 = te->PopResult();
    ASSERT_NE(result1.m_msg.get(), nullptr);
    auto positionCheck = result1.RefFutureMarginResult().RefRelatedPosition(0);
    ASSERT_NE(positionCheck.m_msg, nullptr);
    positionCheck.CheckLv(1000, 10114000, 10126000, 1114000, 1126000).CheckRiskId(2);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "ALPHAUSDT", 0, "Buy", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "ALPHAUSDT", 0, "Sell", "Limit", "1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;

    te->PopResult();
  }

  {
    // 挂单
    OpenApiV5CreateOrderBuilder build("linear", "ALPHAUSDT", 0, "Buy", "Limit", "10", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "挂对手盘 Result:" << jsonStr2 << std::endl;
  }

  {
    // 机构借贷交易封禁
    InstBanOrUnban(uid1, true, true, true, true, true, true, true, true, true);

    // 机构借贷取消订单
    InstCancelAll(uid1);
    auto now_ns = bbase::utils::Time::GetTimeNs();
    // 修改s2时间为当前，s3为100ms后，s4为200ms后
    UpdatePreMarketTime(now_ns - 2 * 1e9, now_ns - 1 * 1e9, now_ns, now_ns + 1 * 1e8, now_ns + 2 * 1e8);
    // 机构借贷平仓
    InstClosePz(uid1, false);
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "回包:" << jsonStr2 << std::endl;
    result.RefFutureMarginResult()
        .RefRelatedOrderByIndex(0)
        .CheckCreateType(uid1, ECreateType::CreateByTakeOver_PassThrough)
        .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);
  }
}
