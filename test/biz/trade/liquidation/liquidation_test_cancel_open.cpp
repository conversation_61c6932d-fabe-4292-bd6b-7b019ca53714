//
// Created by GZ00070ML on 2023/3/20.
//
#include <string>

#include "enums/eaccountstatus/account_status.pb.h"
#include "enums/eorderstatus/order_status.pb.h"
#include "future/feature/pre_market_symbol/pre_market_symbol.hpp"
#include "liquidation/liquidation_test.hpp"
#include "src/biz_worker/service/trade/liquidation/event/liq_event.hpp"
#include "test/biz/trade/lib/stub.hpp"
TEST_F(LiquidationTest, cancel_rm_pre_market_open) {
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  // 账户开户
  openAccount(uid2);
  // 账户充值
  depositAccount(uid2, ECoin::USDT, "13000");
  biz::symbol_t symbol = ESymbol(180);

  // 设置lastPrice
  te->AddMarkPrice(ESymbol(180), 20000, 20000, 20000);
  biz::user_id_t uid1 = 10001;
  stub user1(te, uid1);
  int32_t wait_time = 300;
  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "ALPHAUSDT", 0, "Buy", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单:" << jsonStr2 << std::endl;
  }
  auto now_ns = bbase::utils::Time::GetTimeNs();
  // 修改s2时间为当前，s3为100ms后，s4为200ms后
  UpdatePreMarketTime(now_ns - 2 * 1e9, now_ns - 1 * 1e9, now_ns, now_ns + 1 * 1e8, now_ns + 2 * 1e8);
  // 设置lastPrice
  te->AddMarkPrice(static_cast<ESymbol>(symbol), 17000, 20000, 20000);

  // 触发强平取消开仓单
  {
    std::cout << "定时盈亏2:" << std::endl;
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid2) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  }

  while (true) {
    auto result = te->PopResult(wait_time);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "强平结果:" << jsonStr2 << std::endl;
  }
}

TEST_F(LiquidationTest, cancel_open) {
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  int32_t wait_time = 300;

  // 账户开户
  openAccount(uid2);

  // 账户充值
  depositAccount(uid2, ECoin::USDT, "13000");

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单:" << jsonStr2 << std::endl;
  }

  {
    auto order_link_id_1 = te->GenUUID();
    CreateOrderBuilder create_ao_builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "1", "2000");
    create_ao_builder.SetOrderLinkID(order_link_id_1);
    auto resp = user2.create_order(create_ao_builder.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);
    auto result = te->PopResult(wait_time);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期权订单:" << jsonStr2 << std::endl;
  }

  {
    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
    // 创建现货订单
    auto order_link_id_1 = te->GenUUID();
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(order_link_id_1);
    auto resp1 = user2.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);
    auto result = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货订单:" << jsonStr << std::endl;
  }

  // 触发强平取消开仓单
  {
    std::cout << "定时盈亏1:" << std::endl;
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid2) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    usleep(300);
  }

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 19000, 20000, 20000);

  // 触发强平取消开仓单
  {
    std::cout << "定时盈亏2:" << std::endl;
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid2) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  }

  while (true) {
    auto result = te->PopResult(wait_time);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    // ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "强平结果:" << jsonStr2 << std::endl;
  }
}

TEST_F(LiquidationTest, spot_cancel_stop_order) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  int32_t wait_time = 300;

  // 账户开户
  openAccount(uid1);
  openAccount(uid2);

  // 账户充值
  depositAccount(uid1, ECoin::USDT, "13000");
  depositAccount(uid2, ECoin::USDT, "13000");

  auto order_link_id = te->GenUUID();
  {
    // 现货止盈止损订单， 存在haircut：600
    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.6", "20000");
    build.SetOrderLinkID(order_link_id);
    build.SetOrderFilter("tpslOrder");
    build.SetTriggerPrice("21000");

    auto resp1 = user1.create_order(build.Build());
    //    ASSERT_EQ(resp1->order_link_id(), order_link_id);
    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货订单:" << jsonStr << std::endl;
    auto stop_order_type = result.m_msg.get()->spot_margin_result().related_spot_orders().Get(0).stop_order_type();
    ASSERT_EQ(stop_order_type, EStopOrderType::TakeProfitStopLoss);
  }

  {
    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 20000, 20000, 20000);
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "user2期货订单成交Result:" << jsonStr << std::endl;

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "user1期货订单成交Result:" << jsonStr << std::endl;
  }

  // 触发强平取消开仓单
  {
    te->AddMarkPrice(static_cast<ESymbol>(5), 17624, 18000, 18000);
    std::cout << "定时盈亏1:" << std::endl;
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid2) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    usleep(300);
  }

  while (true) {
    auto result = te->PopResult(wait_time);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    if (result.m_msg.get()->header().action() == EAction::LiqCoinExecute) {
      // 订单被取消，但是并未执行兑币，所以没有lending result
      result.RefSpotMarginResult()
          .RefRelatedOrderByOrderLinkId(order_link_id)
          .CheckStatus(EOrderStatus::Deactivated, ECrossStatus::Deactivated);

      ASSERT_EQ(result.m_msg->has_lending_result(), false);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "强平结果:" << jsonStr2 << std::endl;
    }
  }
}

// 场景： 现货GTC限价单,settle coin存在haircut, 部分成交，触发cancelOpen后正常取消
// 结果： 正常取消并且流程正常执行, 并且走接管
TEST_F(LiquidationTest, spot_cancel_open1) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  // 账户开户
  openAccount(uid1);
  openAccount(uid2);

  // 账户充值
  depositAccount(uid1, ECoin::USDT, "20000");
  depositAccount(uid2, ECoin::BTC, "0.1");
  depositAccount(uid2, ECoin::USDT, "20000");

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  {
    // 创建期货订单
    auto order_link_id_1 = te->GenUUID();
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "5", "20000");
    build.SetOrderLinkID(order_link_id_1);
    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);
    auto result = te->PopResult(1000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string json_str;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &json_str);
    std::cout << "margin_result:" << json_str << std::endl;
  }

  {
    // 创建期货订单
    auto order_link_id_1 = te->GenUUID();
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "5", "20000");
    build.SetOrderLinkID(order_link_id_1);
    auto resp1 = user2.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);
    auto result = te->PopResult(1000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string json_str;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &json_str);
    std::cout << "margin_result:" << json_str << std::endl;
  }

  {
    auto result = te->PopResult(1000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string json_str;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &json_str);
    std::cout << "margin_result:" << json_str << std::endl;
  }

  auto target_order_link_id = te->GenUUID();
  {
    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
    // 创建现货订单, 消耗2000U买0.1个BTC
    auto order_link_id_1 = target_order_link_id;
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(order_link_id_1);
    auto resp1 = user1.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);
    auto result = te->PopResult(1000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string json_str;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &json_str);
    std::cout << "margin_result:" << json_str << std::endl;
  }

  {
    // 创建现货订单, 消1000U买0.05个BTC
    auto order_link_id_1 = te->GenUUID();
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.05", "20000");
    build.SetOrderLinkID(order_link_id_1);
    auto resp1 = user2.create_order(build.Build());
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);

    while (true) {
      auto result = te->PopResult(1000);
      ASSERT_NE(result.m_msg.get(), nullptr);
      std::string json_str;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &json_str);
      std::cout << "margin_result:" << json_str << std::endl;

      if (result.m_msg.get()->header().user_id() == uid1) {
        auto target_order_checker = result.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(target_order_link_id);
        target_order_checker.CheckStatus(EOrderStatus::PartiallyFilled, ECrossStatus::MakerFill);
        break;
      }
    }
  }

  {
    // 修改标记价格
    te->AddMarkPrice(ESymbol::BTCUSDT, 16112, 20000, 20000);
  }
  te->SuspendCross();
  {
    // 定时盈亏
    std::cout << "定时盈亏:" << std::endl;
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    sharding_key = utils::hash(static_cast<std::uint64_t>(uid2) /
                               static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
                   application::GlobalConfig::re_calc_total_user_sharding();
    event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    // std::string json_str;
    // (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &json_str);
    // std::cout << "margin_result:" << json_str << std::endl;
    ASSERT_EQ(result.m_msg.get() == nullptr, true);
  }
  {
    // sleep 几秒钟
    usleep(3000000);
    te->ResumeCross();
    // 定时盈亏
    std::cout << "定时盈亏:" << std::endl;

    // shard_key为0为旧一轮计算结尾，后面的请求触发新一轮计算
    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult(1000);
    std::string json_str;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &json_str);
    std::cout << "margin_result:" << json_str << std::endl;
    auto result_msg = result.m_msg.get();

    ASSERT_EQ(result_msg->header().action(), enums::eaction::Action::LiqCoinExecute);
    ASSERT_EQ(result_msg->asset_margin_result().account_info().account_status(), enums::eaccountstatus::PendingLiq);
    ASSERT_EQ(result_msg->asset_margin_result().account_info().liq_step(),
              static_cast<std::int32_t>(ELiqStep ::CancelAllOpenOrders));
    //    auto target_order_checker = result.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(target_order_link_id);
    //    target_order_checker.CheckStatus(enums::eorderstatus::PartiallyFilledCanceled, enums::ecrossstatus::Canceled);
  }

  {
    // 取消订单结果
    std::cout << "取消订单结果:" << std::endl;
    auto result = te->PopResult(1000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string json_str;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &json_str);
    std::cout << "margin_result:" << json_str << std::endl;
    auto target_order_checker = result.RefSpotMarginResult().RefRelatedOrderByOrderLinkId(target_order_link_id);
    target_order_checker.CheckStatus(enums::eorderstatus::PartiallyFilledCanceled, enums::ecrossstatus::Canceled);
  }

  {
    // 执行接管
    std::cout << "执行接管:" << std::endl;
    auto result = te->PopResult(1000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string json_str;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &json_str);
    std::cout << "margin_result:" << json_str << std::endl;

    auto result_msg = result.m_msg.get();
    ASSERT_EQ(result_msg->header().action(), EAction::LiqCoinExecute);
    ASSERT_EQ(result_msg->asset_margin_result().account_info().account_status(), enums::eaccountstatus::Liq);
    ASSERT_EQ(result_msg->asset_margin_result().account_info().liq_step(),
              static_cast<std::int32_t>(ELiqStep::FutureTakeOver));
  }

  {
    // 接管撮合结果
    auto result = te->PopResult(1000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string json_str;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &json_str);
    std::cout << "margin_result:" << json_str << std::endl;
    auto result_msg = result.m_msg.get();
    ASSERT_EQ(result_msg->header().action(), EAction::OnRecvMatchingResult);

    result.RefAssetMarginResult().RefRelatedAccountInfo().CheckAccountStatus(enums::eaccountstatus::Liq);
    result.RefFutureMarginResult().RefRelatedPosition(0).CheckSize(0);
    result.RefFutureMarginResult()
        .RefRelatedOrderByIndex(0)
        .CheckCreateType(uid1, ECreateType::CreateByTakeOver_PassThrough)
        .CheckStatus(EOrderStatus::Filled, ECrossStatus::TakerFill);
  }

  {
    // 账户状态恢复
    auto result = te->PopResult(1000);
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string json_str;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &json_str);
    std::cout << "margin_result:" << json_str << std::endl;
    auto result_msg = result.m_msg.get();
    ASSERT_EQ(result_msg->header().action(), EAction::LiqCoinExecute);

    result.RefAssetMarginResult().RefRelatedAccountInfo().CheckAccountStatus(enums::eaccountstatus::Normal);
  }
}
