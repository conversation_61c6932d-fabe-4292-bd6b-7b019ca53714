#include <string>

#include "biz_worker/utils/pb_convertor/to_pb.hpp"
#include "src/cross_worker/cross_common/util/x_comm.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_req_sender.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/liquidation/liquidation_test.hpp"

namespace twap {

TEST_F(LiquidationTest, twap_settle_no_any_trade) {
  biz::user_id_t uid1 = 5002;  // twap user
  stub user1(te, uid1);
  openAccount(uid1);

  switchIm(uid1);

  SimulateSettle(static_cast<ECrossIdx>(5), static_cast<ESymbol>(5), static_cast<ECoin>(5));

  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  std::string jsonStr;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
  std::cout << "无交易无仓位下架Result:" << jsonStr << std::endl;

  {
    auto& positions = result.m_msg->futures_margin_result().affected_positions();
    ASSERT_EQ(positions.size(), 1);
    auto& position = positions.at(0);
    ASSERT_EQ(position.size_x(), 0);
    ASSERT_GT(position.settle_price_x(), 0);
    ASSERT_GT(position.settle_fee_rate_e8(), 0);
    ASSERT_EQ(position.term(), 0);
    ASSERT_EQ(position.user_id(), uid1);
    ASSERT_EQ(position.leverage_e2(), 1000);
    ASSERT_EQ(position.risk_id(), 1);
    ASSERT_EQ(position.is_isolated(), true);
    ASSERT_GT(position.mark_price_x(), 0);
    ASSERT_GT(position.last_price_x(), 0);
    ASSERT_EQ(position.symbol_name(), "BTCUSDT");
    ASSERT_GT(position.price_scale(), 0);
    ASSERT_GT(position.value_scale(), 0);
    ASSERT_GT(position.qty_scale(), 0);
    ASSERT_EQ(position.value_e8(), 0);
  }
}

TEST_F(LiquidationTest, twap_settle_trade_no_pz) {
  biz::user_id_t uid1 = 5002;  // twap user
  stub user1(te, uid1);
  openAccount(uid1);

  switchIm(uid1);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "3900";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "充值Result:" << jsonStr << std::endl;
  }

  auto order_price = 30000;
  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "1", std::to_string(order_price));
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto order_id = resp1->order_id();

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "期货订单Result:" << jsonStr << std::endl;

    OpenApiV5CancelOrderBuilder cancel_build("linear", "BTCUSDT", order_id, "", "Order");
    auto cancel_resp = user1.cancel_order(cancel_build.Build());
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    auto orderCheck = result.RefFutureMarginResult().RefRelatedOrderByOrderId(order_id);
    ASSERT_NE(orderCheck.m_msg, nullptr);
  }

  SimulateSettle(static_cast<ECrossIdx>(5), static_cast<ESymbol>(5), static_cast<ECoin>(5));

  auto result = te->PopResult();
  ASSERT_NE(result.m_msg.get(), nullptr);
  std::string jsonStr;
  (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
  std::cout << "无仓位下架Result:" << jsonStr << std::endl;

  {
    auto& positions = result.m_msg->futures_margin_result().affected_positions();
    ASSERT_EQ(positions.size(), 1);
    auto& position = positions.at(0);
    ASSERT_EQ(position.size_x(), 0);
    ASSERT_GT(position.settle_price_x(), 0);
    ASSERT_GT(position.settle_fee_rate_e8(), 0);
    ASSERT_EQ(position.term(), 0);
    ASSERT_EQ(position.user_id(), uid1);
    ASSERT_EQ(position.leverage_e2(), 1000);
    ASSERT_EQ(position.risk_id(), 1);
    ASSERT_EQ(position.is_isolated(), true);
    ASSERT_GT(position.mark_price_x(), 0);
    ASSERT_GT(position.last_price_x(), 0);
    ASSERT_EQ(position.symbol_name(), "BTCUSDT");
    ASSERT_GT(position.price_scale(), 0);
    ASSERT_GT(position.value_scale(), 0);
    ASSERT_GT(position.qty_scale(), 0);
    ASSERT_EQ(position.side(), 0);
    ASSERT_EQ(position.value_e8(), 0);
  }

  {
    auto& asset = result.m_msg->asset_margin_result().affected_wallets();
    auto usdt_wallet = asset.find(ECoin::USDT);
    ASSERT_NE(usdt_wallet, asset.end());

    ASSERT_EQ(std::atof(usdt_wallet->second.wallet().cash_balance().c_str()), 3900);
  }
}

TEST_F(LiquidationTest, twap_settle_with_pz) {
  biz::user_id_t uid1 = 5002;  // twap user
  stub user1(te, uid1);
  openAccount(uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid2);

  switchIm(uid1);

  {
    // 设置lastPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 30000, 30000, 30000}, {static_cast<ESymbol>(45), 30000, 30000, 30000}});
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "3900";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "uid1 充值Result:" << jsonStr << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string amount = "3900";
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, amount, bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "uid2 充值Result:" << jsonStr << std::endl;
  }

  auto order_price = 30000;
  auto qty = 1;
  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", std::to_string(qty),
                                      std::to_string(order_price));
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "期货订单Result:" << jsonStr << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", std::to_string(qty),
                                      std::to_string(order_price));
    build.SetOrderLinkID(te->GenUUID());
    auto resp = user2.create_order(build.Build());
    ASSERT_NE(resp->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "uid2 期货订单Result:" << jsonStr << std::endl;

    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "uid1 成交Result:" << jsonStr << std::endl;
  }

  SimulateSettle(static_cast<ECrossIdx>(5), static_cast<ESymbol>(5), static_cast<ECoin>(5));

  int cnt = 0;
  while (true) {
    auto result = te->PopResult(100);
    if (result.m_msg.get() == nullptr) {
      break;
    }
    cnt++;
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "下架Result:" << jsonStr << std::endl;

    auto& positions = result.m_msg->futures_margin_result().affected_positions();
    ASSERT_EQ(positions.size(), 1);
    auto& position = positions.at(0);
    auto user_id = position.user_id();
    auto cum_trade_fee_e8 = 0;
    if (user_id == uid1) {
      ASSERT_EQ(position.size_x(), 0);
      ASSERT_GT(position.settle_price_x(), 0);
      ASSERT_GT(position.settle_fee_rate_e8(), 0);
      ASSERT_EQ(position.term(), 1);
      ASSERT_EQ(position.leverage_e2(), 1000);
      ASSERT_EQ(position.risk_id(), 1);
      ASSERT_EQ(position.is_isolated(), true);
      ASSERT_GT(position.mark_price_x(), 0);
      ASSERT_GT(position.last_price_x(), 0);
      ASSERT_EQ(position.symbol_name(), "BTCUSDT");
      ASSERT_GT(position.price_scale(), 0);
      ASSERT_GT(position.value_scale(), 0);
      ASSERT_GT(position.qty_scale(), 0);
      ASSERT_EQ(position.side(), 0);
      ASSERT_EQ(position.value_e8(), 0);
      biz::money_e8_t close_pnl_e8 = (position.settle_price_x() - order_price * 1e4) * qty * 1e4;
      ASSERT_EQ(position.cum_closed_pnl_e8(), close_pnl_e8);
      biz::money_e8_t total_release_pnl_e8 = close_pnl_e8 + position.cum_trade_fee_e8();
      ASSERT_EQ(position.cum_realised_pnl_e8(), total_release_pnl_e8);
      ASSERT_EQ(position.today_realised_pnl_e8(), total_release_pnl_e8);
      ASSERT_EQ(position.cur_term_realised_pnl_e8(), 0);

      cum_trade_fee_e8 = position.cum_trade_fee_e8();
      biz::money_e8_t settle_fee_e8 = position.settle_price_x() / 1e4 * qty * position.settle_fee_rate_e8();
      ASSERT_EQ(position.cum_trade_fee_e8(), -(300000000 + settle_fee_e8));
    } else if (position.user_id() == uid2) {
      ASSERT_EQ(position.size_x(), 0);
      ASSERT_GT(position.settle_price_x(), 0);
      ASSERT_GT(position.settle_fee_rate_e8(), 0);
      ASSERT_EQ(position.term(), 1);
      ASSERT_EQ(position.leverage_e2(), 1000);
      ASSERT_EQ(position.risk_id(), 1);
      ASSERT_EQ(position.is_isolated(), false);
      ASSERT_GT(position.mark_price_x(), 0);
      ASSERT_GT(position.last_price_x(), 0);
      ASSERT_EQ(position.symbol_name(), "BTCUSDT");
      ASSERT_GT(position.price_scale(), 0);
      ASSERT_GT(position.value_scale(), 0);
      ASSERT_GT(position.qty_scale(), 0);
      ASSERT_EQ(position.side(), 0);
      ASSERT_EQ(position.value_e8(), 0);
      biz::money_e8_t close_pnl_e8 = (order_price * 1e4 - position.settle_price_x()) * qty * 1e4;
      ASSERT_EQ(position.cum_closed_pnl_e8(), close_pnl_e8);
      biz::money_e8_t total_release_pnl_e8 = close_pnl_e8 + position.cum_trade_fee_e8();
      ASSERT_EQ(position.cum_realised_pnl_e8(), total_release_pnl_e8);
      ASSERT_EQ(position.today_realised_pnl_e8(), total_release_pnl_e8);
      ASSERT_EQ(position.cur_term_realised_pnl_e8(), 0);
      cum_trade_fee_e8 = position.cum_trade_fee_e8();
    } else {
      cnt--;
    }

    biz::money_e8_t close_pnl_e8 = 0;
    biz::money_e8_t fee_e8 = 0;
    auto& fills = result.m_msg->futures_margin_result().related_fills();
    ASSERT_EQ(fills.size(), 1);

    auto& fill = fills[0];
    if (fill.user_id() == uid1) {
      ASSERT_EQ(fill.exec_type(), EExecType::Settle);
      ASSERT_EQ(fill.exec_qty_x(), qty * 1e8);
      close_pnl_e8 = (position.settle_price_x() - order_price * 1e4) * qty * 1e4;
      ASSERT_EQ(fill.closed_pnl_e8(), close_pnl_e8);
      ASSERT_EQ(fill.exec_price_x(), position.settle_price_x());
      fee_e8 = position.settle_price_x() / 1e4 * qty * position.settle_fee_rate_e8();
      ASSERT_EQ(fill.cum_exec_fee_e8(), fee_e8);
      ASSERT_EQ(fill.exec_fee_e8(), fee_e8);
    } else if (fill.user_id() == uid2) {
      ASSERT_EQ(fill.exec_type(), EExecType::Settle);
      ASSERT_EQ(fill.exec_qty_x(), qty * 1e8);
      close_pnl_e8 = (order_price * 1e4 - position.settle_price_x()) * qty * 1e4;
      ASSERT_EQ(fill.closed_pnl_e8(), close_pnl_e8);
      ASSERT_EQ(fill.exec_price_x(), position.settle_price_x());
      fee_e8 = position.settle_price_x() / 1e4 * qty * position.settle_fee_rate_e8();
      ASSERT_EQ(fill.cum_exec_fee_e8(), fee_e8);
      ASSERT_EQ(fill.exec_fee_e8(), fee_e8);
    } else {
      cnt--;
    }

    auto& wallets = result.m_msg->asset_margin_result().affected_wallets();
    auto usdt_wallet = wallets.find(ECoin::USDT);
    ASSERT_NE(usdt_wallet, wallets.end());

    auto& wallet = usdt_wallet->second.wallet();
    if (user_id == uid1) {
      ASSERT_EQ(std::atof(wallet.asset_im().c_str()), 0);
      biz::money_e8_t leave_cb_e8 =
          3900 + (static_cast<double>(close_pnl_e8) / 1e8 + static_cast<double>(cum_trade_fee_e8) / 1e8);
      ASSERT_EQ(std::atof(wallet.cash_balance().c_str()), leave_cb_e8);
      ASSERT_EQ(std::atof(wallet.available_balance().c_str()), leave_cb_e8);
      ASSERT_EQ(std::atof(wallet.total_order_im().c_str()), 0);
    } else if (user_id == uid2) {
      ASSERT_EQ(std::atof(wallet.asset_im().c_str()), 0);
      biz::money_e8_t leave_cb_e8 =
          3900 + (static_cast<double>(close_pnl_e8) / 1e8 + static_cast<double>(cum_trade_fee_e8) / 1e8);
      ASSERT_EQ(std::atof(wallet.cash_balance().c_str()), leave_cb_e8);
      ASSERT_EQ(std::atof(wallet.available_balance().c_str()), leave_cb_e8);
      ASSERT_EQ(std::atof(wallet.total_order_im().c_str()), 0);
    } else {
      cnt--;
    }
  }
  ASSERT_EQ(cnt, 2);
}
}  // namespace twap
