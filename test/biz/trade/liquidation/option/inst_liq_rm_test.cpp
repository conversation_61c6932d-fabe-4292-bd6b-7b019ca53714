//
// Created by HK00009ML on 4/4/2023.
//
#include <chrono>
#include <iostream>
#include <thread>

#include "enums/einstactionstatus/inst_action_status.pb.h"
#include "liquidation/liquidation_test.hpp"
#include "src/margin_request/event/event.hpp"
#include "test/biz/trade/lib/stub.hpp"
#include "test/biz/trade/liquidation/liq_test.hpp"

// 全仓非对锁，期权OB成功
TEST_F(LiquidationTest, inst_rm_opt_partial_liq) {
  biz::user_id_t const uid = 10000;
  std::string symbol = "BTC-31DEC24-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string qty = "1";
  std::string price = "2000";

  depositAccount(uid, 16, "50000");
  createOptPosition(uid, symbol, position_idx, side, qty, price);
  createOptOrder(uid + 1, symbol, position_idx, side, qty, price);

  // 机构借贷交易封禁
  InstBanOrUnban(uid, true, true, true, true, true, true, true, true, true);

  // 机构借贷取消订单
  InstCancelAll(uid);

  // 机构借贷平仓
  InstClosePz(uid, false);

  // te->AddOptionMarkPrice(static_cast<ESymbol>(102313), 30000, 30000, 30000);
  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  while (true) {
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      break;
    }
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "驱动结果:" << jsonStr2 << std::endl;
  }
}

// 全仓非对锁，期权OB不成功走OTC
TEST_F(LiquidationTest, inst_opt_liq_otc) {
  biz::user_id_t const uid = 10000;
  std::string symbol = "BTC-31DEC24-40000-C";
  int position_idx = 0;
  std::string side = "Sell";
  std::string qty = "1";
  std::string price = "2000";

  depositAccount(uid, 16, "50000");
  createOptPosition(uid, symbol, position_idx, side, qty, price);
  // createOptOrder(uid + 1, symbol, position_idx, side, qty, price);

  // 机构借贷交易封禁
  InstBanOrUnban(uid, true, true, true, true, true, true, true, true, true);

  // 机构借贷取消订单
  InstCancelAll(uid);

  // 机构借贷平仓
  InstClosePz(uid, false);

  // te->AddOptionMarkPrice(static_cast<ESymbol>(102313), 30000, 30000, 30000);
  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(uid) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
  while (true) {
    auto result = te->PopResult();
    if (result.m_msg.get() == nullptr) {
      break;
    }
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "驱动结果:" << jsonStr2 << std::endl;
  }
}

//  mmr>=1 全仓永续走takeover（有期权、没期权）kyo

//  三种衍生品  撤单（现货普通条件单、止盈止损单、普通活动单； 期货条件单、普通活动单；期权活动单）kyo

//  订单+持仓 ，只打标平仓 kyo

// 全仓对锁 正常size、size超过OB限制（有期权、无期权） steven

//  全仓永续OB成交，正常size，size超过OB限制，多个仓位（无期权、有期权）steven

/*
 * 全仓永续OB成交，正常size，size超过OB限制，多个仓位（无期权、有期权）
 */
TEST_F(LiquidationTest, inst_liq_CM_OB) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "5000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置MarkPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2期货订单Result:" << jsonStr2 << std::endl;

    auto pz_sell_order_result = te->PopResult();
  }

  {
    // 机构借贷交易封禁
    InstBanOrUnban(uid1, true, true, true, true, true, true, true, true, true);

    // 机构借贷取消订单
    InstCancelAll(uid1);

    // 机构借贷平仓
    InstClosePz(uid1, false);

    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18180, 18180, 18180);
    // te->SuspendCross();
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    // 恢复cross
    te->ResumeCross();
    while (true) {
      auto result = te->PopResult();
      if (result.m_msg.get() == nullptr) {
        break;
      }
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "驱动结果:" << jsonStr2 << std::endl;
    }
  }
}

TEST_F(LiquidationTest, inst_liq_CM_pre_market_cancel) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "5000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置MarkPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(ESymbol(180)), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "ALPHAUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 机构借贷交易封禁
    InstBanOrUnban(uid1, true, true, true, true, true, true, true, true, true);
    te->SuspendCross();
    // 机构借贷取消订单
    auto now_ns = bbase::utils::Time::GetTimeNs();
    // 修改s2时间为当前，s3为100ms后，s4为200ms后
    UpdatePreMarketTime(now_ns - 2 * 1e9, now_ns - 1 * 1e9, now_ns, now_ns + 1 * 1e8, now_ns + 8 * 1e9);
    // 设置取消标记
    auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
    unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
    unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstCancelAll);
    auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    // cancellAll取消后下发的marginResult
    auto result = te->PopResult();
    // 撮合回报回来的
    auto result1 = te->PopResult();
    ASSERT_EQ(result.m_msg.get() == nullptr, true);
    // 恢复cross
    te->ResumeCross();
    // 机构借贷取消订单
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result3 = te->PopResult();
    ASSERT_EQ(result3.RefAssetMarginResult().m_msg->inst_exec_result().inst_action_status(),
              enums::einstactionstatus::Processing);

    usleep(10000000);
    // 机构借贷取消订单
    InstCancelAll(uid1);
  }
}

//  全仓永续OB转OTC（有期权、无期权）steven

/**
 * 测试全仓模式(CM)下执行inst(有futureSpread订单成交中间状态)
 *
 * 测试场景：
 * 在全仓模式下，执行inst时的行为，特别是对撤销futureSpread组合订单的处理
 *
 * 预期结果：
 * - inst时如果遇到futureSpread组合订单成交中间状态就报错退出
 */
TEST_F(LiquidationTest, CM_inst_liq_FILL_IN_PROCESS) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "5000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 1;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "1", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 1;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "5", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置MarkPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2期货订单Result:" << jsonStr2 << std::endl;

    auto pz_sell_order_result = te->PopResult();
  }

  // u1 挂 futureSpread 组合单
  {
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Buy", "Limit", "GTC", "100", "0.003");
    RpcContext ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  {
    // 单步模式
    te->SetCrossSingleStepMode();
  }

  {
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Sell", "Limit", "GTC", "100", "0.001");
    RpcContext ct;
    auto m_resp = user2.comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);
  }

  // 获取用户数据
  {
    auto resp = user1.DebugUserData();
    TestLiqHelper::PrintPb(resp);
  }

  {
    te->ProcessOneXReq(te->GetCombCrossIdx());

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
    result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  {
    // 机构借贷交易封禁
    InstBanOrUnban(uid1, true, true, true, true, true, true, true, true, true);

    // 机构借贷取消订单 InstCancelAll(uid1);
    {
      auto unified_v_2_request_dto = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>();
      unified_v_2_request_dto->mutable_req_header()->set_user_id(uid1);
      unified_v_2_request_dto->mutable_req_header()->set_action(EAction::InstCancelAll);
      auto event = std::make_shared<event::MarginHdtsRequestEvent>(uid1, unified_v_2_request_dto, -1);
      te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
      // cancellAll取消后下发的marginResult
      auto result = te->PopResult();
      // 撮合回报回来的

      auto result1 = te->PopResult();

      ASSERT_NE(result.m_msg.get(), nullptr);
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "设置InstCancelAll:" << jsonStr2 << std::endl;
      ASSERT_EQ(result.m_msg->ret_code(), error::kErrorCodeDefault);
      ASSERT_EQ(result.m_msg->ret_msg(), "cancel order fail.");
    }

    {
      te->UnSetCrossSingleStepMode();
      auto resp = te->PopResult();
      ASSERT_NE(resp.m_msg.get(), nullptr);
      TestLiqHelper::PrintPb(resp.m_msg);

      resp = te->PopResult();
      ASSERT_NE(resp.m_msg.get(), nullptr);
      TestLiqHelper::PrintPb(resp.m_msg);

      resp = te->PopResult();
      ASSERT_NE(resp.m_msg.get(), nullptr);
      TestLiqHelper::PrintPb(resp.m_msg);

      resp = te->PopResult();
      ASSERT_NE(resp.m_msg.get(), nullptr);
      TestLiqHelper::PrintPb(resp.m_msg);

      resp = te->PopResult();
      ASSERT_EQ(resp.m_msg.get(), nullptr);
    }

    // 重新执行 机构借贷取消订单
    InstCancelAll(uid1);

    // 机构借贷平仓
    InstClosePz(uid1, false);

    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18180, 18180, 18180);
    // te->SuspendCross();
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    // 恢复cross
    // te->ResumeCross();
    while (true) {
      auto resp = te->PopResult();
      if (resp.m_msg.get() == nullptr) {
        break;
      }
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*resp.m_msg.get(), &jsonStr2);
      std::cout << "驱动结果:" << jsonStr2 << std::endl;
    }
  }
}

/**
 * 测试全仓模式(CM)下执行inst(有futureSpread订单)
 *
 * 测试场景：
 * 在全仓模式下，执行inst时的行为，特别是对撤销futureSpread组合订单的处理
 *
 * 预期结果：
 * - 正常执行
 */
TEST_F(LiquidationTest, CM_inst_liq) {
  // UID1
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  // UID2
  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);
  openAccount(uid1);
  openAccount(uid2);

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "5000", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 1;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid1, trans_id, coin, wallet_record_type, "1", bonus_change);
    auto resp1 = user1.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 5;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "10000", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    std::string req_id = "";
    std::string trans_id = "";
    biz::coin_t coin = 1;
    int wallet_record_type = 1;
    std::string bonus_change = "0";

    FutureDepositBuilder deposit_build(req_id, uid2, trans_id, coin, wallet_record_type, "5", bonus_change);
    auto resp1 = user2.process(deposit_build.Build());
    ASSERT_EQ(resp1->ret_code(), 0);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2充值Result:" << jsonStr2 << std::endl;
  }

  {
    // 设置MarkPrice
    te->BatchAddMarkPrice(
        {{static_cast<ESymbol>(5), 20000, 20000, 20000}, {static_cast<ESymbol>(45), 20000, 20000, 20000}});
    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid1期货订单Result:" << jsonStr2 << std::endl;
  }

  {
    // 形成持仓
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "0.1", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "uid2期货订单Result:" << jsonStr2 << std::endl;

    auto pz_sell_order_result = te->PopResult();
  }

  // u1 挂 futureSpread 组合单
  {
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Buy", "Limit", "GTC", "100", "0.003");
    RpcContext ct;
    auto m_resp = user1.comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(result.m_msg);
  }

  {
    biz::order_link_id_t m_order_link_id{te->GenUUID()};
    CombSiteCreateOrderBuilder m_create_build("PERP-SPOT", m_order_link_id, "Sell", "Limit", "GTC", "100", "0.001");
    RpcContext ct;
    auto m_resp = user2.comb_siteapi_create_order(m_create_build.Build(), ct);
    TestLiqHelper::PrintPb(m_resp);
    ASSERT_EQ(m_resp->ret_code(), error::kErrorCodeSuccess);
  }

  {
    auto resp = te->PopResult();
    ASSERT_NE(resp.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(resp.m_msg);

    resp = te->PopResult();
    ASSERT_NE(resp.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(resp.m_msg);

    resp = te->PopResult();
    ASSERT_NE(resp.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(resp.m_msg);

    resp = te->PopResult();
    ASSERT_NE(resp.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(resp.m_msg);

    resp = te->PopResult();
    ASSERT_NE(resp.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(resp.m_msg);

    resp = te->PopResult();
    ASSERT_NE(resp.m_msg.get(), nullptr);
    TestLiqHelper::PrintPb(resp.m_msg);

    resp = te->PopResult();
    ASSERT_EQ(resp.m_msg.get(), nullptr);
  }

  // 获取用户数据
  {
    auto resp = user1.DebugUserData();
    TestLiqHelper::PrintPb(resp);
  }

  {
    // 机构借贷交易封禁
    InstBanOrUnban(uid1, true, true, true, true, true, true, true, true, true);

    // 机构借贷取消订单
    InstCancelAll(uid1);

    // 机构借贷平仓
    InstClosePz(uid1, false);

    // 设置lastPrice
    te->AddMarkPrice(static_cast<ESymbol>(5), 18180, 18180, 18180);
    // te->SuspendCross();
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);

    // 恢复cross
    // te->ResumeCross();
    while (true) {
      auto resp = te->PopResult();
      if (resp.m_msg.get() == nullptr) {
        break;
      }
      std::string jsonStr2;
      (void)google::protobuf::util::MessageToJsonString(*resp.m_msg.get(), &jsonStr2);
      std::cout << "驱动结果:" << jsonStr2 << std::endl;
    }
  }
}
