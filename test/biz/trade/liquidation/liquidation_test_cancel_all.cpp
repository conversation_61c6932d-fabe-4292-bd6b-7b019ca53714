//
// Created by GZ00070ML on 2023/3/20.
//

#include <string>

#include "liquidation/liquidation_test.hpp"
#include "src/biz_worker/service/trade/liquidation/event/liq_event.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(LiquidationTest, cancel_all) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  // 账户开户
  openAccount(uid1);
  openAccount(uid2);

  // 账户充值
  depositAccount(uid1, ECoin::USDT, "50000");
  depositAccount(uid2, ECoin::USDT, "50000");

  // 调整风险限额
  increaseRiskLimit(uid1, ESymbol::BTCUSDT, ECoin::USDT, 2);
  increaseRiskLimit(uid2, ESymbol::BTCUSDT, ECoin::USDT, 2);

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Taker:" << jsonStr2 << std::endl;
  }

  {
    // 等待成交回报
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "成交回报Maker:" << jsonStr2 << std::endl;
  }

  {
    auto order_link_id_1 = te->GenUUID();
    CreateOrderBuilder create_ao_builder("option", "BTC-31DEC24-40000-C", 0, "Buy", "Limit", "1", "2000");
    create_ao_builder.SetOrderLinkID(order_link_id_1);
    auto resp = user2.create_order(create_ao_builder.Build());
    ASSERT_EQ(resp->order_link_id(), order_link_id_1);
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期权订单:" << jsonStr2 << std::endl;
  }

  {
    te->MockSpotLastPriceToPreEngine("BTCUSDT", bbase::decimal::Decimal<>(20000));
    // 创建现货订单
    auto order_link_id_1 = te->GenUUID();
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Buy", "Limit", "0.1", "20000");
    build.SetOrderLinkID(order_link_id_1);
    RpcContext ct{};
    auto resp1 = user2.create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), 0);
    ASSERT_EQ(ct.RetMsg(), "OK");
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);
    auto result = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货订单:" << jsonStr << std::endl;
  }

  // 触发强平取消开仓单
  {
    std::cout << "定时盈亏1:" << std::endl;
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    sharding_key = utils::hash(static_cast<std::uint64_t>(uid2) /
                               static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
                   application::GlobalConfig::re_calc_total_user_sharding();
    event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    usleep(1000);
  }

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 5000, 5000, 5000);

  // 触发强平取消开仓单
  {
    std::cout << "定时盈亏2:" << std::endl;
    // shard_key为0为旧一轮计算结尾，后面的请求触发新一轮计算
    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "强平结果2:" << jsonStr2 << std::endl;

    while (true) {
      result = te->PopResult(1000);
      if (result.m_msg == nullptr) {
        break;
      }

      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "强平cancel结果:" << jsonStr2 << std::endl;
    }
  }
}

TEST_F(LiquidationTest, cancel_all_pm_ValidSpotMarginOrder) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  {
    std::shared_ptr<config::MarginCoinConfigClient> margin_coin_config_client_ =
        std::make_shared<config::MarginCoinConfigClient>();

    auto btc_config = std::shared_ptr<config::MarginCoinConfigDTO>(
        new config::MarginCoinConfigDTO{.coin_id = 1,
                                        .coin = "BTC",
                                        .is_margin = "N",
                                        .is_loan = "Y",
                                        .short_spot_im_rate = bbase::decimal::Decimal<>("0.002"),
                                        .short_spot_mm_rate = bbase::decimal::Decimal<>("0.003"),
                                        .margin_value_rate = bbase::decimal::Decimal<>("0.60"),
                                        .margin_value_rate_e4 = 6000,
                                        .status = 1});

    auto eth_config = std::shared_ptr<config::MarginCoinConfigDTO>(
        new config::MarginCoinConfigDTO{.coin_id = 2,
                                        .coin = "ETH",
                                        .is_margin = "Y",
                                        .short_spot_im_rate = bbase::decimal::Decimal<>("0.002"),
                                        .short_spot_mm_rate = bbase::decimal::Decimal<>("0.003"),
                                        .margin_value_rate = bbase::decimal::Decimal<>("0.96"),
                                        .margin_value_rate_e4 = 9600,
                                        .status = 1});
    auto usdt_config = std::shared_ptr<config::MarginCoinConfigDTO>(
        new config::MarginCoinConfigDTO{.coin_id = 5,
                                        .coin = "USDT",
                                        .is_margin = "Y",
                                        .is_loan = "Y",
                                        .short_spot_im_rate = bbase::decimal::Decimal<>("0.002"),
                                        .short_spot_mm_rate = bbase::decimal::Decimal<>("0.003"),
                                        .margin_value_rate = bbase::decimal::Decimal<>("1"),
                                        .margin_value_rate_e4 = 10000,
                                        .status = 1});

    auto usdc_config = std::shared_ptr<config::MarginCoinConfigDTO>(
        new config::MarginCoinConfigDTO{.coin_id = 16,
                                        .coin = "USDC",
                                        .is_margin = "Y",
                                        .is_loan = "Y",
                                        .short_spot_im_rate = bbase::decimal::Decimal<>("0.002"),
                                        .short_spot_mm_rate = bbase::decimal::Decimal<>("0.003"),
                                        .margin_value_rate = bbase::decimal::Decimal<>("0.99"),
                                        .margin_value_rate_e4 = 9900,
                                        .status = 1});
    auto data = std::make_shared<config::MarginCoinConfigData>();
    data->coin_id_map.emplace(btc_config->coin_id, btc_config);
    data->coin_id_map.emplace(eth_config->coin_id, eth_config);
    data->coin_id_map.emplace(usdt_config->coin_id, usdt_config);
    data->coin_id_map.emplace(usdc_config->coin_id, usdc_config);

    data->coin_map["BTC"] = btc_config;
    data->coin_map["USDT"] = usdt_config;
    data->coin_map["ETH"] = eth_config;
    data->coin_map["USDC"] = usdc_config;

    data->coin_map.emplace(eth_config->coin, eth_config);
    data->coin_map.emplace(usdc_config->coin, usdc_config);
    margin_coin_config_client_->set_margin_coin_config_data(data);
    config::ConfigProxy::Instance().config_mgr()->set_margin_coin_config_client(margin_coin_config_client_);
    auto margin_coin_value_ratio_data = std::make_shared<config::MarginCoinStepValueRatioConfigData>();
    for (auto& [coin_name, coin_config] : data->coin_map) {
      auto step_cfg = std::make_shared<config::MarginCoinStepValueRatioConfigDTO>();
      step_cfg->coin_name = coin_name;
      step_cfg->coin_id = coin_config->coin_id;

      auto level_cfg = std::make_shared<config::StepLevelConfig>();
      level_cfg->last_level = true;
      level_cfg->margin_value_rate = coin_config->margin_value_rate;
      step_cfg->ordered_value_ratio_map[decimal::kZero] = level_cfg;

      step_cfg->ReBuildAndCheckConfig();

      margin_coin_value_ratio_data->coin_map[coin_name] = step_cfg;
      margin_coin_value_ratio_data->coin_id_map[step_cfg->coin_id] = step_cfg;
    }

    margin_coin_config_client_->set_margin_coin_step_value_ratio_config_data(margin_coin_value_ratio_data);
  }
  // 账户开户
  openAccount(uid1);
  openAccount(uid2);

  // 账户充值
  depositAccount(uid1, ECoin::BTC, "125");
  depositAccount(uid1, ECoin::USDT, "500000");
  depositAccount(uid2, ECoin::USDT, "50000");
  switchPm(uid1);
  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Taker:" << jsonStr2 << std::endl;
  }

  {
    // 等待成交回报
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "成交回报Maker:" << jsonStr2 << std::endl;
  }
  auto order_link_id_1 = te->GenUUID();

  {
    // 创建现货订单
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.1", "20000");
    build.SetOrderLinkID(order_link_id_1);
    RpcContext ct{};
    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), 0);
    ASSERT_EQ(ct.RetMsg(), "OK");
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);
    auto result = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货订单:" << jsonStr << std::endl;
  }

  // 触发强平取消开仓单
  {
    std::cout << "定时盈亏1:" << std::endl;
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    sharding_key = utils::hash(static_cast<std::uint64_t>(uid2) /
                               static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
                   application::GlobalConfig::re_calc_total_user_sharding();
    event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    usleep(1000);
  }

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, **********, 5000, 5000);

  // 触发强平取消开仓单
  {
    std::cout << "定时盈亏2:" << std::endl;
    // shard_key为0为旧一轮计算结尾，后面的请求触发新一轮计算
    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "强平结果2:" << jsonStr2 << std::endl;
    ASSERT_EQ(true, result.RefSpotMarginResult().RefRelatedOrderByOrderId(order_link_id_1).m_msg == nullptr);
  }
}

TEST_F(LiquidationTest, cancel_all_pm_ValidSpotMarginOrder_spotHedge) {
  biz::user_id_t uid1 = 100000;
  stub user1(te, uid1);

  biz::user_id_t uid2 = 200000;
  stub user2(te, uid2);

  {
    std::shared_ptr<config::MarginCoinConfigClient> margin_coin_config_client_ =
        std::make_shared<config::MarginCoinConfigClient>();

    auto btc_config = std::shared_ptr<config::MarginCoinConfigDTO>(
        new config::MarginCoinConfigDTO{.coin_id = 1,
                                        .coin = "BTC",
                                        .is_margin = "N",
                                        .is_loan = "Y",
                                        .short_spot_im_rate = bbase::decimal::Decimal<>("0.002"),
                                        .short_spot_mm_rate = bbase::decimal::Decimal<>("0.003"),
                                        .margin_value_rate = bbase::decimal::Decimal<>("0.60"),
                                        .margin_value_rate_e4 = 6000,
                                        .status = 1});

    auto eth_config = std::shared_ptr<config::MarginCoinConfigDTO>(
        new config::MarginCoinConfigDTO{.coin_id = 2,
                                        .coin = "ETH",
                                        .is_margin = "Y",
                                        .short_spot_im_rate = bbase::decimal::Decimal<>("0.002"),
                                        .short_spot_mm_rate = bbase::decimal::Decimal<>("0.003"),
                                        .margin_value_rate = bbase::decimal::Decimal<>("0.96"),
                                        .margin_value_rate_e4 = 9600,
                                        .status = 1});
    auto usdt_config = std::shared_ptr<config::MarginCoinConfigDTO>(
        new config::MarginCoinConfigDTO{.coin_id = 5,
                                        .coin = "USDT",
                                        .is_margin = "Y",
                                        .is_loan = "Y",
                                        .short_spot_im_rate = bbase::decimal::Decimal<>("0.002"),
                                        .short_spot_mm_rate = bbase::decimal::Decimal<>("0.003"),
                                        .margin_value_rate = bbase::decimal::Decimal<>("1"),
                                        .margin_value_rate_e4 = 10000,
                                        .status = 1});

    auto usdc_config = std::shared_ptr<config::MarginCoinConfigDTO>(
        new config::MarginCoinConfigDTO{.coin_id = 16,
                                        .coin = "USDC",
                                        .is_margin = "Y",
                                        .is_loan = "Y",
                                        .short_spot_im_rate = bbase::decimal::Decimal<>("0.002"),
                                        .short_spot_mm_rate = bbase::decimal::Decimal<>("0.003"),
                                        .margin_value_rate = bbase::decimal::Decimal<>("0.99"),
                                        .margin_value_rate_e4 = 9900,
                                        .status = 1});
    auto data = std::make_shared<config::MarginCoinConfigData>();
    data->coin_id_map.insert_or_assign(btc_config->coin_id, btc_config);
    data->coin_id_map.insert_or_assign(eth_config->coin_id, eth_config);
    data->coin_id_map.insert_or_assign(usdt_config->coin_id, usdt_config);
    data->coin_id_map.insert_or_assign(usdc_config->coin_id, usdc_config);

    data->coin_map.insert_or_assign(btc_config->coin, btc_config);
    data->coin_map.insert_or_assign(eth_config->coin, eth_config);
    data->coin_map.insert_or_assign(usdc_config->coin, usdc_config);
    data->coin_map.insert_or_assign(usdt_config->coin, usdt_config);
    margin_coin_config_client_->set_margin_coin_config_data(data);
    config::ConfigProxy::Instance().config_mgr()->set_margin_coin_config_client(margin_coin_config_client_);
    auto margin_coin_value_ratio_data = std::make_shared<config::MarginCoinStepValueRatioConfigData>();
    for (auto& [coin_name, coin_config] : data->coin_map) {
      auto step_cfg = std::make_shared<config::MarginCoinStepValueRatioConfigDTO>();
      step_cfg->coin_name = coin_name;
      step_cfg->coin_id = coin_config->coin_id;

      auto level_cfg = std::make_shared<config::StepLevelConfig>();
      level_cfg->last_level = true;
      level_cfg->margin_value_rate = coin_config->margin_value_rate;
      step_cfg->ordered_value_ratio_map[decimal::kZero] = level_cfg;

      step_cfg->ReBuildAndCheckConfig();

      margin_coin_value_ratio_data->coin_map[coin_name] = step_cfg;
      margin_coin_value_ratio_data->coin_id_map[step_cfg->coin_id] = step_cfg;
    }

    margin_coin_config_client_->set_margin_coin_step_value_ratio_config_data(margin_coin_value_ratio_data);
  }
  // 账户开户
  openAccount(uid1);
  openAccount(uid2);

  // 账户充值
  depositAccount(uid1, ECoin::BTC, "125");
  depositAccount(uid1, ECoin::USDT, "500000");
  depositAccount(uid2, ECoin::USDT, "50000");
  switchPm(uid1);
  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, 20000, 20000, 20000);

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Buy", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user2.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单:" << jsonStr2 << std::endl;
  }

  {
    // 创建期货订单
    OpenApiV5CreateOrderBuilder build("linear", "BTCUSDT", 0, "Sell", "Limit", "5", "20000");
    build.SetOrderLinkID(te->GenUUID());
    auto resp1 = user1.create_order(build.Build());
    ASSERT_NE(resp1->order_id(), "");
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "期货订单Taker:" << jsonStr2 << std::endl;
  }

  {
    // 等待成交回报
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "成交回报Maker:" << jsonStr2 << std::endl;
  }
  auto order_link_id_1 = te->GenUUID();

  {
    // 创建现货订单
    OpenApiV5CreateOrderBuilder build("spot", "BTCUSDT", 0, "Sell", "Limit", "0.1", "20000");
    build.SetOrderLinkID(order_link_id_1);
    RpcContext ct{};
    auto resp1 = user1.create_order(build.Build(), ct);
    ASSERT_EQ(ct.RetCode(), 0);
    ASSERT_EQ(ct.RetMsg(), "OK");
    ASSERT_EQ(resp1->order_link_id(), order_link_id_1);
    auto result = te->PopResult();
    std::string jsonStr;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr);
    std::cout << "现货订单:" << jsonStr << std::endl;
  }

  // 触发强平取消开仓单
  {
    std::cout << "定时盈亏1:" << std::endl;
    std::int64_t sharding_key =
        utils::hash(static_cast<std::uint64_t>(uid1) /
                    static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
        application::GlobalConfig::re_calc_total_user_sharding();
    auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    sharding_key = utils::hash(static_cast<std::uint64_t>(uid2) /
                               static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
                   application::GlobalConfig::re_calc_total_user_sharding();
    event = std::make_shared<event::ReCalcEvent>(sharding_key);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    usleep(1000);
  }

  {
    AccountCustomeSpotHedgeBuilder spot_hedge_build("", uid1, 5, true, false);
    auto resp1 = user1.process(spot_hedge_build.Build());
    ASSERT_EQ(resp1->ret_code(), error::kErrorCodeSuccess);

    auto result = te->PopResult();
    ASSERT_NE(result.m_msg.get(), nullptr);

    auto disable_coin_list = result.m_msg->asset_margin_result().per_user_setting_data().open_pm_spot_hedge();
    ASSERT_EQ(disable_coin_list, true);
  }

  // 设置lastPrice
  te->AddMarkPrice(ESymbol::BTCUSDT, **********, 5000, 5000);

  // 触发强平取消开仓单
  {
    std::cout << "定时盈亏2:" << std::endl;
    // shard_key为0为旧一轮计算结尾，后面的请求触发新一轮计算
    auto event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    event = std::make_shared<event::ReCalcEvent>(0);
    te->pipeline()->ring_buffers(worker::kPreWorker)->Write(event, false);
    auto result = te->PopResult();
    std::string jsonStr2;
    (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
    std::cout << "强平结果2:" << jsonStr2 << std::endl;

    while (true) {
      result = te->PopResult(1000);
      if (result.m_msg == nullptr) {
        break;
      }

      (void)google::protobuf::util::MessageToJsonString(*result.m_msg.get(), &jsonStr2);
      std::cout << "强平cancel结果:" << jsonStr2 << std::endl;
    }
  }
}
