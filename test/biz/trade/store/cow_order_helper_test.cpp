#include "cow_order_helper_test.h"  // NOLINT

#include "src/biz_worker/service/trade/store/order/cow_order.hpp"
#include "test/biz/trade/store/x_store_builder.h"

TEST_F(CowOrderHelperTest, test_recover_cow_order_from_dump_normal_01) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::Untriggered;
  order->cross_status = ECrossStatus::Init;

  auto [cow_order, version] = store::CowOrder::RecoverCowOrderFromDump(order, 10000);
  ASSERT_TRUE(cow_order != nullptr && version == 10000);
}

TEST_F(CowOrderHelperTest, test_recover_cow_order_from_dump_normal_02) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::Triggered;
  order->cross_status = ECrossStatus::Init;

  auto [cow_order, version] = store::CowOrder::RecoverCowOrderFromDump(order, 10000);
  ASSERT_TRUE(cow_order != nullptr && version == 10000);
}

TEST_F(CowOrderHelperTest, test_recover_cow_order_from_dump_normal_03) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::New;
  order->cross_status = ECrossStatus::NewAccepted;

  auto [cow_order, version] = store::CowOrder::RecoverCowOrderFromDump(order, 10000);
  ASSERT_TRUE(cow_order != nullptr && version == 10000);
}

TEST_F(CowOrderHelperTest, test_recover_cow_order_from_dump_normal_04) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::PartiallyFilled;
  order->cross_status = ECrossStatus::TakerFill;

  auto [cow_order, version] = store::CowOrder::RecoverCowOrderFromDump(order, 10000);
  ASSERT_TRUE(cow_order != nullptr && version == 10000);
}

TEST_F(CowOrderHelperTest, test_recover_cow_order_from_dump_panic_01) {
  GTEST_SKIP() << "这个case 触发了panic, 目前panic会产生abort, 需要跳过";
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::Untriggered;
  order->cross_status = ECrossStatus::TakerFill;

  std::tuple<store::CowOrder::Ptr, biz::seq_t> order_tuple;

  // #4: create cow order
  try {
    order_tuple = store::CowOrder::RecoverCowOrderFromDump(order, 10000);
  } catch (BizException&) {
    ASSERT_TRUE(true);
  }

  ASSERT_TRUE(std::get<0>(order_tuple) == nullptr);
}

TEST_F(CowOrderHelperTest, test_recover_cow_order_from_dump_panic_02) {
  GTEST_SKIP() << "这个case 触发了panic, 目前panic会产生abort, 需要跳过";
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::Triggered;
  order->cross_status = ECrossStatus::TakerFill;

  std::tuple<store::CowOrder::Ptr, biz::seq_t> order_tuple;

  // #4: create cow order
  try {
    order_tuple = store::CowOrder::RecoverCowOrderFromDump(order, 10000);
  } catch (BizException&) {
    ASSERT_TRUE(true);
  }

  ASSERT_TRUE(std::get<0>(order_tuple) == nullptr);
}

TEST_F(CowOrderHelperTest, test_recover_cow_order_from_dump_panic_03) {
  GTEST_SKIP() << "这个case 触发了panic, 目前panic会产生abort, 需要跳过";
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::New;
  order->cross_status = ECrossStatus::Init;

  std::tuple<store::CowOrder::Ptr, biz::seq_t> order_tuple;

  // #4: create cow order
  try {
    order_tuple = store::CowOrder::RecoverCowOrderFromDump(order, 10000);
  } catch (BizException&) {
    ASSERT_TRUE(true);
  }

  ASSERT_TRUE(std::get<0>(order_tuple) == nullptr);
}

TEST_F(CowOrderHelperTest, test_recover_cow_order_from_dump_panic_04) {
  GTEST_SKIP() << "这个case 触发了panic, 目前panic会产生abort, 需要跳过";
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::PartiallyFilled;
  order->cross_status = ECrossStatus::Init;

  std::tuple<store::CowOrder::Ptr, biz::seq_t> order_tuple;

  // #4: create cow order
  try {
    order_tuple = store::CowOrder::RecoverCowOrderFromDump(order, 10000);
  } catch (BizException&) {
    ASSERT_TRUE(true);
  }

  ASSERT_TRUE(std::get<0>(order_tuple) == nullptr);
}

TEST_F(CowOrderHelperTest, test_recover_cow_order_from_dump_warn) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);
  order->op_seq = 15000;

  order->order_status = EOrderStatus::Rejected;
  order->cross_status = ECrossStatus::Init;

  auto [cow_order, version] = store::CowOrder::RecoverCowOrderFromDump(order, 10000);
  ASSERT_TRUE(cow_order != nullptr && version == 15000);
}

TEST_F(CowOrderHelperTest, test_recover_cow_order_from_xreq_panic_01) {
  GTEST_SKIP() << "这个case 触发了panic, 目前panic会产生abort, 需要跳过";
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);
  order->op_seq = 15000;

  order->order_status = EOrderStatus::Created;
  order->cross_status = ECrossStatus::Init;

  std::tuple<store::CowOrder::Ptr, biz::seq_t> order_tuple;

  // #4: create cow order
  try {
    order_tuple = store::CowOrder::RecoverCowOrderFromXReq(order, 10000);
  } catch (BizException&) {
    ASSERT_TRUE(true);
  }

  ASSERT_TRUE(std::get<0>(order_tuple) == nullptr);
}

TEST_F(CowOrderHelperTest, test_recover_cow_order_from_xreq_panic_02) {
  GTEST_SKIP() << "这个case 触发了panic, 目前panic会产生abort, 需要跳过";
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);
  order->op_seq = 15000;

  order->order_status = EOrderStatus::Filled;
  order->cross_status = ECrossStatus::Init;

  std::tuple<store::CowOrder::Ptr, biz::seq_t> order_tuple;

  // #4: create cow order
  try {
    order_tuple = store::CowOrder::RecoverCowOrderFromXReq(order, 10000);
  } catch (BizException&) {
    ASSERT_TRUE(true);
  }

  ASSERT_TRUE(std::get<0>(order_tuple) == nullptr);
}

TEST_F(CowOrderHelperTest, test_recover_cow_order_from_xreq_normal_01) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);
  order->op_seq = 15000;

  order->order_status = EOrderStatus::Created;
  order->cross_status = ECrossStatus::PendingNew;

  std::tuple<store::CowOrder::Ptr, biz::seq_t> order_tuple;

  // #4: create cow order
  try {
    order_tuple = store::CowOrder::RecoverCowOrderFromXReq(order, 10000);
  } catch (BizException&) {
    ASSERT_TRUE(true);
  }

  ASSERT_TRUE(std::get<0>(order_tuple) != nullptr && std::get<1>(order_tuple) == 15000);
}
