#include "per_user_store_test.hpp"  // NOLINT

#include <boost/uuid/uuid.hpp>
#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>

#include "biz_worker/service/trade/store/order/cow_order.hpp"
#include "biz_worker/service/trade/store/spot_order/cow_spot_order.hpp"
#include "test/biz/trade/store/per_user_store_test.hpp"
#include "test/biz/trade/store/x_store_builder.h"

#define DEFAULT_TEST_UID 10086
#define DEFAULT_TEST_AID 80086

#define DEFAULT_TEST_SYMBOL 128
#define DEFAULT_TEST_COIN 5
#define DEFAULT_TEST_SETTLE_COIN 6

static_assert(ECoin::USDT == DEFAULT_TEST_COIN);

////////////////////////////////////////////////////////////////////////////////////////

TEST_F(PerUserStoreTest, test_swap_future_order_to_store) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create per-user store
  store::PerUserStore user_store(user_info.uid);
  builder.CreateCoinStore(&user_store, EProductType::Futures, user_info.uid, order_info.coin, order_info.symbol);

  // #3: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  // #4: create cow order
  auto cow_order = store::CowOrder::CreateCowOrder(order);
  user_store.SwapFuturesOrderToStore(cow_order.get(), nullptr);

  // #5: first stage checking
  biz::coin_t coin = order_info.coin;
  biz::symbol_t symbol = order_info.symbol;

  auto& order_id_map = user_store.all_future_working_order_map_by_order_id_;
  const auto& itr1 = order_id_map.find(order_info.order_id);
  ASSERT_TRUE(itr1 != order_id_map.end());

  auto& order_link_id_map = user_store.all_future_working_order_map_by_order_link_id_;
  const auto& itr2 = order_link_id_map.find(order_info.order_link_id);
  ASSERT_TRUE(itr2 != order_link_id_map.end());

  auto const& per_coin_store = user_store.GetPerCoinStore(coin);
  ASSERT_TRUE(per_coin_store != nullptr);

  auto const& per_symbol_store = user_store.GetPerSymbolStore(per_coin_store, EProductType::Futures, symbol);
  ASSERT_TRUE(per_symbol_store != nullptr);

  auto& all_orders = per_symbol_store->all_future_orders_;
  const auto& itr3 = all_orders.find(order_info.order_id);
  ASSERT_TRUE(itr3 != all_orders.end());

  auto& all_futures_order = per_symbol_store->all_future_active_orders_;
  const auto& itr4 = all_futures_order.find(order_info.order_id);
  ASSERT_TRUE(itr4 != all_futures_order.end());

  auto& all_futures_stop_orders = per_symbol_store->all_future_normal_stop_orders_;
  const auto& itr5 = all_futures_stop_orders.find(order_info.order_id);
  ASSERT_TRUE(itr5 == all_futures_stop_orders.end());

  // #6: update order status
  auto& transact = *order;
  transact.order_status = EOrderStatus::Rejected;
  transact.cross_status = ECrossStatus::NewRejected;

  user_store.SwapFuturesOrderToStore(cow_order.get(), nullptr);

  // #7: second stage checking
  const auto& itr6 = order_id_map.find(order_info.order_id);
  ASSERT_TRUE(itr6 == order_id_map.end());

  const auto& itr7 = order_link_id_map.find(order_info.order_link_id);
  ASSERT_TRUE(itr7 == order_link_id_map.end());

  const auto& itr8 = all_orders.find(order_info.order_id);
  ASSERT_TRUE(itr8 == all_orders.end());

  const auto& itr9 = all_futures_order.find(order_info.order_id);
  ASSERT_TRUE(itr9 == all_orders.end());

  const auto& itr10 = all_futures_stop_orders.find(order_info.order_id);
  ASSERT_TRUE(itr10 == all_futures_stop_orders.end());
}

TEST_F(PerUserStoreTest, test_swap_option_order_to_store) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create per-user store
  store::PerUserStore user_store(user_info.uid);
  builder.CreateCoinStore(&user_store, EProductType::Options, user_info.uid, order_info.coin, order_info.symbol);

  // #3: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  // #4: create cow order
  auto cow_order = store::CowOrder::CreateCowOrder(order);
  user_store.SwapOptionsOrderToStore(cow_order.get());

  // #5: first stage checking
  biz::coin_t coin = order_info.coin;
  biz::symbol_t symbol = order_info.symbol;

  auto& order_id_map = user_store.all_option_working_order_map_by_order_id_;
  const auto& itr1 = order_id_map.find(order_info.order_id);
  ASSERT_TRUE(itr1 != order_id_map.end());

  auto& order_link_id_map = user_store.all_option_working_order_map_by_order_link_id_;
  const auto& itr2 = order_link_id_map.find(order_info.order_link_id);
  ASSERT_TRUE(itr2 != order_link_id_map.end());

  auto const& per_coin_store = user_store.GetPerCoinStore(coin);
  ASSERT_TRUE(per_coin_store != nullptr);

  auto const& per_symbol_store = user_store.GetPerSymbolStore(per_coin_store, EProductType::Options, symbol);
  ASSERT_TRUE(per_symbol_store != nullptr);

  auto& all_orders = per_symbol_store->all_option_orders_;
  const auto& itr3 = all_orders.find(order_info.order_id);
  ASSERT_TRUE(itr3 != all_orders.end());

  auto& all_option_orders = per_symbol_store->all_option_active_orders_;
  const auto& itr4 = all_option_orders.find(order_info.order_id);
  ASSERT_TRUE(itr4 != all_option_orders.end());

  // #6: update order status
  auto& transact = *order;
  transact.order_status = EOrderStatus::Rejected;
  transact.cross_status = ECrossStatus::NewRejected;

  user_store.SwapOptionsOrderToStore(cow_order.get());

  // #7: second stage checking
  const auto& itr6 = order_id_map.find(order_info.order_id);
  ASSERT_TRUE(itr6 == order_id_map.end());

  const auto& itr7 = order_link_id_map.find(order_info.order_link_id);
  ASSERT_TRUE(itr7 == order_link_id_map.end());

  const auto& itr8 = all_orders.find(order_info.order_id);
  ASSERT_TRUE(itr8 == all_orders.end());

  const auto& itr9 = all_option_orders.find(order_info.order_id);
  ASSERT_TRUE(itr9 == all_orders.end());
}

TEST_F(PerUserStoreTest, test_swap_spot_order_to_store) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create per-user store
  store::PerUserStore user_store(user_info.uid);
  builder.CreateCoinStore(&user_store, EProductType::Spot, user_info.uid, order_info.coin, order_info.symbol);

  // #3: create active order & init
  auto order = std::make_shared<store::SpotOrder>();
  builder.InitOrder(order, user_info, order_info);

  // #4: create cow order
  auto cow_order = store::CowSpotOrder::CreateCowSpotOrder(order);
  user_store.FirstTimeInsertSpotOrderToStore(cow_order);

  // #5: first stage checking
  biz::coin_t coin = order_info.coin;
  biz::symbol_t symbol = order_info.symbol;

  auto& order_id_map = user_store.all_spot_working_order_map_by_order_id_;
  const auto& itr1 = order_id_map.find(order_info.order_id);
  ASSERT_TRUE(itr1 != order_id_map.end());

  auto& order_link_id_map = user_store.all_spot_working_order_map_by_order_link_id_;
  const auto& itr2 = order_link_id_map.find(order_info.order_link_id);
  ASSERT_TRUE(itr2 != order_link_id_map.end());

  auto& working_order_num = user_store.spot_per_symbol_working_order_;
  auto& stop_order_num = user_store.spot_per_symbol_stop_order_;
  auto& tpsl_order_num = user_store.spot_per_symbol_tpsl_order_;

  const auto& itr3 = working_order_num.find(symbol);
  ASSERT_TRUE(itr3 != working_order_num.end() && itr3->second.size() == 1);

  const auto& itr4 = stop_order_num.find(symbol);
  ASSERT_TRUE(itr4 == stop_order_num.end());

  const auto& itr5 = tpsl_order_num.find(symbol);
  ASSERT_TRUE(itr5 == tpsl_order_num.end());

  auto const& per_coin_store = user_store.GetPerCoinStore(coin);
  ASSERT_TRUE(per_coin_store != nullptr);

  auto const& per_symbol_store = user_store.GetPerSymbolStore(per_coin_store, EProductType::Spot, symbol);
  ASSERT_TRUE(per_symbol_store != nullptr);

  auto& all_orders = per_symbol_store->all_spot_orders_;
  const auto& itr6 = all_orders.find(order_info.order_id);
  ASSERT_TRUE(itr6 != all_orders.end());

  // #6: update order status
  auto& transact = *order;
  transact.order_status = EOrderStatus::Rejected;
  transact.cross_status = ECrossStatus::NewRejected;

  user_store.SwapSpotOrderToStore(cow_order.get());

  // #7: second stage checking
  const auto& itr7 = order_id_map.find(order_info.order_id);
  ASSERT_TRUE(itr7 == order_id_map.end());

  const auto& itr8 = order_link_id_map.find(order_info.order_link_id);
  ASSERT_TRUE(itr8 == order_link_id_map.end());

  const auto& itr9 = all_orders.find(order_info.order_id);
  ASSERT_TRUE(itr9 == all_orders.end());

  const auto& itr10 = working_order_num.find(symbol);
  ASSERT_TRUE(itr10 == working_order_num.end());

  const auto& itr11 = stop_order_num.find(symbol);
  ASSERT_TRUE(itr11 == stop_order_num.end());

  const auto& itr12 = tpsl_order_num.find(symbol);
  ASSERT_TRUE(itr12 == tpsl_order_num.end());
}

TEST_F(PerUserStoreTest, test_is_futures_symbol_has_working_orders) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create per-user store
  store::PerUserStore user_store(user_info.uid);
  builder.CreateCoinStore(&user_store, EProductType::Futures, user_info.uid, order_info.coin, order_info.symbol);

  // #3: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  // #4: create cow order
  auto cow_order = store::CowOrder::CreateCowOrder(order);
  user_store.SwapFuturesOrderToStore(cow_order.get(), nullptr);

  // #5: do testing
  bool ret = user_store.IsFuturesSymbolHasWorkingOrders(order_info.coin, order_info.symbol);
  ASSERT_TRUE(ret == true);
}

TEST_F(PerUserStoreTest, test_future_get_working_tp_sl_ts_order) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create per-user store
  store::PerUserStore user_store(user_info.uid);
  builder.CreateCoinStore(&user_store, EProductType::Futures, user_info.uid, order_info.coin, order_info.symbol);

  // #3: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);
  order->stop_order_type = EStopOrderType::TakeProfit;  // tp

  // #4: create cow order
  auto cow_order = store::CowOrder::CreateCowOrder(order);
  user_store.SwapFuturesOrderToStore(cow_order.get(), nullptr);

  // #5: do testing
  auto target_cow_order =
      user_store.FutureGetWorkingTpSlTsOrder(EStopOrderType::TakeProfit, order_info.symbol, EPositionIndex::Single);
  ASSERT_TRUE(cow_order.get() == target_cow_order);
}

TEST_F(PerUserStoreTest, test_check_pz_more_than_zero) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create per-user store
  store::PerUserStore user_store(user_info.uid);
  builder.CreateCoinStore(&user_store, EProductType::Futures, user_info.uid, order_info.coin, order_info.symbol);

  // #3: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);
  order->stop_order_type = EStopOrderType::TakeProfit;  // tp

  // #4: create cow order
  auto cow_order = store::CowOrder::CreateCowOrder(order);
  user_store.SwapFuturesOrderToStore(cow_order.get(), nullptr);

  // #5: testing
  biz::coin_t coin = order_info.coin;
  auto coin_store = user_store.working_coins_[coin];
  ASSERT_TRUE(coin_store != nullptr);

  biz::symbol_t symbol = order_info.symbol;
  auto symbol_store = coin_store->working_future_symbols_[symbol];
  ASSERT_TRUE(symbol_store != nullptr);

  bool ret = symbol_store->check_pz_morethan_zero();
  ASSERT_TRUE(ret == false);
}

TEST_F(PerUserStoreTest, MonitorUserActiveOrder) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create per-user store
  store::PerUserStore user_store(user_info.uid);
  builder.CreateCoinStore(&user_store, EProductType::Futures, user_info.uid, order_info.coin, order_info.symbol);

  // #3: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);
  order->stop_order_type = EStopOrderType::UNKNOWN;
  order->order_status = EOrderStatus::Created;
  order->cross_status = ECrossStatus::WaitToSendNew;

  // #4: create cow order
  auto cow_order = store::CowOrder::CreateCowOrder(order);
  user_store.SwapFuturesOrderToStore(cow_order.get(), nullptr);

  // #5: testing 期货
  user_store.MonitorUserActiveOrder(0, EProductType::Futures);

  // #6: testing 期权
  user_store.MonitorUserActiveOrder(0, EProductType::Options);

  // #7: testing 现货
  user_store.MonitorUserActiveOrder(0, EProductType::Spot);
}

// TEST_F(PerUserStoreTest, test_check_create_makeup_msg) {
//   // #0: create x-store builder instance
//   XStoreBuilder builder;
//
//   // #1: prepare order info
//   UserInfo user_info;
//   builder.PrepareUserInfo(user_info);
//
//   // #2: create per-user store
//   store::PerUserStore user_store(user_info.uid);
//
//   store::UserDataManager user_manager;
//   user_manager.CreateMakeUpMsg(ESymbol::BTCUSDT, &user_store);
// }
