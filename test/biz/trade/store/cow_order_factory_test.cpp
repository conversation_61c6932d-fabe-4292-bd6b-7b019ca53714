#include "cow_order_factory_test.h"  // NOLINT

#include "biz_worker/service/trade/store/order/cow_order.hpp"
#include "test/biz/trade/store/x_store_builder.h"

TEST_F(CowOrderFactoryTest, test_create_cow_order_normal_01) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::Untriggered;
  order->cross_status = ECrossStatus::Init;

  // #4: create cow order
  auto cow_order = store::CowOrder::CreateCowOrder(order);
  ASSERT_TRUE(cow_order != nullptr);
}

TEST_F(CowOrderFactoryTest, test_create_cow_order_normal_02) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::Triggered;
  order->cross_status = ECrossStatus::Init;

  // #4: create cow order
  auto cow_order = store::CowOrder::CreateCowOrder(order);
  ASSERT_TRUE(cow_order != nullptr);
}

TEST_F(CowOrderFactoryTest, test_create_cow_order_normal_03) {
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::Created;
  order->cross_status = ECrossStatus::WaitToSendNew;

  // #4: create cow order
  auto cow_order = store::CowOrder::CreateCowOrder(order);
  ASSERT_TRUE(cow_order != nullptr);
}

TEST_F(CowOrderFactoryTest, test_create_cow_order_panic_01) {
  GTEST_SKIP() << "这个case 触发了panic, 目前panic会产生abort, 需要跳过";
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::Untriggered;
  order->cross_status = ECrossStatus::WaitToSendNew;

  store::CowOrder::Ptr cow_order = nullptr;

  // #4: create cow order
  try {
    cow_order = store::CowOrder::CreateCowOrder(order);
  } catch (BizException&) {
    ASSERT_TRUE(true);
  }

  ASSERT_TRUE(cow_order == nullptr);
}

TEST_F(CowOrderFactoryTest, test_create_cow_order_panic_02) {
  GTEST_SKIP() << "这个case 触发了panic, 目前panic会产生abort, 需要跳过";
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::Triggered;
  order->cross_status = ECrossStatus::WaitToSendNew;

  store::CowOrder::Ptr cow_order = nullptr;

  // #4: create cow order
  try {
    cow_order = store::CowOrder::CreateCowOrder(order);
  } catch (BizException&) {
    ASSERT_TRUE(true);
  }

  ASSERT_TRUE(cow_order == nullptr);
}

TEST_F(CowOrderFactoryTest, test_create_cow_order_panic_03) {
  GTEST_SKIP() << "这个case 触发了panic, 目前panic会产生abort, 需要跳过";
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::Created;
  order->cross_status = ECrossStatus::Init;

  store::CowOrder::Ptr cow_order = nullptr;

  // #4: create cow order
  try {
    cow_order = store::CowOrder::CreateCowOrder(order);
  } catch (BizException&) {
    ASSERT_TRUE(true);
  }

  ASSERT_TRUE(cow_order == nullptr);
}

TEST_F(CowOrderFactoryTest, test_create_cow_order_panic_04) {
  GTEST_SKIP() << "这个case 触发了panic, 目前panic会产生abort, 需要跳过";
  // #0: create x-store builder instance
  XStoreBuilder builder;

  // #1: prepare order info
  UserInfo user_info;
  builder.PrepareUserInfo(user_info);

  OrderInfo order_info;
  builder.PrepareOrderInfo(order_info);

  // #2: create active order & init
  auto order = std::make_shared<store::Order>();
  builder.InitOrder(order, user_info, order_info);

  order->order_status = EOrderStatus::Rejected;
  order->cross_status = ECrossStatus::Init;

  store::CowOrder::Ptr cow_order = nullptr;

  // #4: create cow order
  try {
    cow_order = store::CowOrder::CreateCowOrder(order);
  } catch (BizException&) {
    ASSERT_TRUE(true);
  }

  ASSERT_TRUE(cow_order == nullptr);
}
