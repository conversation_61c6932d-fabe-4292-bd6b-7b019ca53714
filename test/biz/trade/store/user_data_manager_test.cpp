#include "test/biz/trade/store/user_data_manager_test.h"

#include "data/event/type.hpp"
#include "models/dump/userdata.pb.h"
#include "src/biz_worker/service/trade/store/per_worker_store.hpp"
#include "test/biz/trade/store/per_user_store_test.hpp"
#include "test/biz/trade/store/store_test_common.h"
#include "test/mocks/pipeline/result_handler_mock.hpp"

////////////////////////////////////////////////////////////////////////////////////////////

void UserDataManagerTest::SetUpTestSuite() {}

void UserDataManagerTest::TearDownTestSuite() {}

////////////////////////////////////////////////////////////////////////////////////////////

MockedUserDataManager::MockedUserDataManager() {}

MockedUserDataManager::~MockedUserDataManager() {}

int32_t MockedUserDataManager::TestIsNeedLoadUser(biz::user_id_t uid, store::Header* header, ConstBizEventSPtrRef ev,
                                                  std::string* msg) {
  (void)uid;
  (void)header;
  (void)ev;
  (void)msg;

  // #1: make uid blocked
  // block_user_set_.insert(uid);

  // #2: test
  // UserDataManager::IsNeedLoadUser(uid, header, ev, msg);
  return 0;
}

////////////////////////////////////////////////////////////////////////////////////////////

TEST_F(PerUserStoreTest, test_is_need_load_user) {
  // #1: prepare input parameters
  biz::user_id_t uid = 10086;
  auto ev =
      std::make_shared<event::BizEvent>(uid, event::EventType::kEventGrpcInput, event::EventSourceType::kGrpc,
                                        event::EventDispatchType::kUnicastToUser, event::EventBizType::kCreateOrder);

  store::Header header;
  header.coin = 5;

  // #2: call functions
  MockedUserDataManager manager;
  int32_t ret = manager.TestIsNeedLoadUser(uid, &header, ev, nullptr);
  ASSERT_EQ(ret, 0);
}

TEST_F(PerUserStoreTest, test_is_force_unload_user) {
  biz::user_id_t uid = 1008;
  MockedUserDataManager manager;
  manager.CreateUserData(uid, uid);
  int32_t ret = manager.UnloadUserData(uid, true);
  ASSERT_EQ(ret, 0);
  ASSERT_EQ(manager.IsExistUserData(uid), false);
}

TEST_F(UserDataManagerTest, check_timeout_and_load_user) {
  MockedUserDataManager manager;
  MockResultHandler handler;
  manager.CheckTimeoutAndLoadUserData(&handler);

  auto& user_store = manager.CreateUserData(1);
  auto loading_time = user_store->last_loading_time_ns_;
  manager.CheckTimeoutAndLoadUserData(&handler);
  ASSERT_EQ(user_store->last_loading_time_ns_, loading_time);

  user_store->load_status_ = ELoadDumpStatus::Loading;
  user_store->last_loading_time_ns_ = 0;
  manager.CheckTimeoutAndLoadUserData(&handler);
  ASSERT_GT(user_store->last_loading_time_ns_, 0);

  user_store->last_loading_time_ns_ -= 25 * 1e9;
  loading_time = user_store->last_loading_time_ns_;
  manager.CheckTimeoutAndLoadUserData(&handler);
  ASSERT_EQ(user_store->last_loading_time_ns_, loading_time);

  auto header = std::make_shared<store::Header>();
  user_store->pending_req_queue_.emplace_back(header, nullptr);
  manager.CheckTimeoutAndLoadUserData(&handler);
  ASSERT_GT(user_store->last_loading_time_ns_, loading_time);
}

TEST_F(UserDataManagerTest, sync_preload_uids) {
  MockedUserDataManager manager;
  std::unordered_set<biz::user_id_t> uid_set;
  uid_set.insert(2);
  uid_set.insert(3);
  ASSERT_EQ(manager.all_user_data_.size(), 0);
  manager.SyncPreloadUids(uid_set);
  ASSERT_EQ(manager.all_user_data_.size(), 2);
  auto iter = manager.all_user_data_.find(2);
  ASSERT_NE(iter, manager.all_user_data_.end());
  ASSERT_EQ(iter->second->load_status_, ELoadDumpStatus::Loading);
}

TEST_F(UserDataManagerTest, process_unload_status) {
  MockedUserDataManager manager;
  biz::user_id_t uid = 2;
  auto event =
      std::make_shared<event::BizEvent>(uid, event::EventType::kEventUnknown, event::EventSourceType::kCross,
                                        event::EventDispatchType::kUnicastToUser, event::EventBizType::kAddMargin);
  auto header = std::make_shared<store::Header>();
  std::string err_msg;
  MockResultHandler handler;
  store::PerUserStore user_store{uid};
  auto ret = manager.ProcessUnloadStatus(uid, event, header, &user_store, &handler, &err_msg);
  ASSERT_EQ(ret, 0);
}

TEST_F(UserDataManagerTest, sync_injected_preload_uids) {
  MockedUserDataManager manager;
  auto uid_set = std::make_shared<std::unordered_set<biz::user_id_t>>();
  uid_set->insert(111);
  ASSERT_EQ(manager.injected_preload_user_set_, nullptr);
  manager.SyncInjectedPreloadUids(std::move(uid_set));
  ASSERT_EQ(manager.injected_preload_user_set_->size(), 1);
}

TEST_F(UserDataManagerTest, option_history_symbol_load) {
  MockedUserDataManager manager;
  biz::user_id_t uid = 2000;
  manager.CreateUserData(uid, uid);
  ::models::dump::UserData pb;
  pb.set_user_id(uid);
  ::models::dump::UserData_PerSymbolData per_symbol_data;
  ::models::tradingdto::TransactDTO transact_dto;
  auto& option_symbol_data = *pb.mutable_option_symbol_data();
  auto& pending_orders = *per_symbol_data.mutable_pending_orders();

  // case1: pending order size > 0
  pending_orders[1] = transact_dto;
  option_symbol_data[1234] = per_symbol_data;

  // case2: workign order size > 0
  ::models::dump::UserData_PerSymbolData per_symbol_data2;
  auto& working_orders = *per_symbol_data2.mutable_working_orders();
  working_orders["order_id"] = transact_dto;
  option_symbol_data[1235] = per_symbol_data2;

  // case3: position size > 0
  ::models::dump::UserData_PerSymbolData per_symbol_data3;
  auto& working_positions = *per_symbol_data3.mutable_working_positions();
  ::models::tradingdto::PositionDTO position_dto;
  position_dto.set_size_x(100);
  working_positions[0] = position_dto;
  option_symbol_data[1236] = per_symbol_data3;

  auto config_mgr = config::getTlsCfgMgrRaw();
  store::PerWorkerStore worker_store;
  MockResultHandler result_handler;
  manager.LoadUserDataFromPb(&pb, config_mgr, &worker_store, &result_handler);
}

TEST_F(UserDataManagerTest, load_spot_recent_ordert) {
  store::UserDataManager user_data_manager;

  const biz::user_id_t uid17 = 200017;
  const biz::account_id_t aid17 = 200017;

  application::GlobalVarManager::Instance().sharding_config_storage().AddUserWhiteList(uid17);

  MockResultHandler result_handler{};
  store::PerWorkerStore per_worker_store;

  auto symbol_config_mgr = std::make_shared<biz::sc::SymbolConfigManager>();
  auto symbol_config = std::make_shared<biz::SymbolConfig>();
  auto config_manager = std::make_shared<config::ConfigManager>();
  config_manager->set_symbol_config_mgr(symbol_config_mgr);
  (*per_worker_store.future_symbol_mark_price_map_)[5] = biz::price_d_t("1200.678");

  store::PerUserStore* per_user_store = user_data_manager.GetUserData(uid17, aid17, true);

  auto user_data = std::make_shared<models::dump::UserData>();
  user_data->set_user_id(uid17);
  user_data->set_account_id(aid17);

  auto* spot_recent_order_link_ids = user_data->mutable_spot_recent_order_link_ids();
  spot_recent_order_link_ids->Add("spot_1234567");
  user_data_manager.LoadUserDataFromPb(user_data.get(), config_manager.get(), &per_worker_store, &result_handler);

  per_user_store = user_data_manager.GetUserData(uid17, aid17);
  ASSERT_NE(per_user_store, nullptr);
  ASSERT_EQ(per_user_store->spot_recent_order_link_ids_.contains(biz::order_link_id_t("spot_1234567")), true);
}
