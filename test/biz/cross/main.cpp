#include <bbase/common/bkafka/bkafka.hpp>
#include <bbase/common/blogger/blogger.hpp>
#include <bbase/common/hdts/hdts.hpp>
#include <bbase/common/nacos_client/nacos_client_impl.hpp>
#include <bbase/common/object_manager/object_manager.hpp>
#include <bbase/common/utils/time_utils.hpp>
#include <iostream>
#include <thread>

#include "src/biz_worker/service/trade/store/passthrough/raw_recover_pass.hpp"
#include "src/biz_worker/service/trade/store/position/raw_position.hpp"
#include "src/config/config_proxy.hpp"
#include "src/cross_worker/cross_producer/xreq_sender/x_spot_req_sender.hpp"
#include "src/cross_worker/cross_receiver/cross_define.hpp"
#include "src/cross_worker/cross_receiver/cross_receiver.hpp"
#include "src/data/error/error.hpp"
#include "src/data/type/biz_type.hpp"

// 消费者消息处理回调
class ConsumerHandle : public dtssdk::ConsumeCbInterface {
 public:
  ConsumerHandle() {}
  ~ConsumerHandle() {}
  // 业务消息处理回调
  void OnConsumeCallback(dtssdk::Message& msg) override {
    std::cout << "consume msg callback topic:" << msg.TopicName() << ",offset:" << msg.Offset() << std::endl;
  }

  void OnConsumeError(dtssdk::SysEventCode err) override { std::cout << "consume err: " << err << std::endl; }
};

void TestConsume() {
  std::string topic = "dts_demo_topic_1";

  bbase::hdts::Offset start_offset = 1;
  bbase::hdts::Offset end_offset = 10;

  std::cout << "start consumer" << std::endl;

  dtssdk::ConsumeCbInterface* cb_handle = new ConsumerHandle();

  auto result = bbase::hdts::Hdts::SpawnConsumer(topic, start_offset, end_offset, cb_handle, false);

  if (result.second == bbase::hdts::Error::kSuccess) {
    std::cout << "sleep" << std::endl;

    std::this_thread::sleep_for(std::chrono::seconds(5));  // sleep 5秒等消费一部分数据
    bbase::hdts::Hdts::DestroyConsumer(result.first);

    std::cout << "close done" << std::endl;
  } else {
    std::cout << "create consumer failed error code:" << static_cast<std::int32_t>(result.second) << std::endl;
  }
}

void TestProducer() {
  auto index = 0;
  std::string body = "test_data_";
  std::string topic = "dts_demo_topic_1";

  // 生产ack处理,这个message消息不能抛到其他线程处理

  bbase::hdts::ProducerCallback::Data d = {
      .data = nullptr,
      .send_success = [](dtssdk::Message& message_p,
                         void*) { std::cout << "producer ack index:" << message_p.Offset() << std::endl; },
      .send_error = nullptr,
      .start_timestamp_ns = bbase::utils::Time::GetTimeNs()};

  std::cout << " start produce msg" << std::endl;

  while (index < 100) {
    auto data = body + std::to_string(index);

    bbase::hdts::Producer::Message message{
        .topic = topic,
        .value = data,
        .need_callback = true,
        .callback_data = d,
    };

    auto result = bbase::hdts::Hdts::ProduceMessage(message);

    if (!result) {
      std::cout << "produce failed" << std::endl;
      break;
    } else {
      std::cout << result << " index:" << index << std::endl;
      index++;
    }
  }

  std::this_thread::sleep_for(std::chrono::seconds(15));  // sleep 10秒等生产ack
}

void TestHightWaterOffset() {
  std::string topic = "dts_demo_topic_1";
  bbase::hdts::Offset hight_water_offset = 0;
  auto result = bbase::hdts::Hdts::GetHighWaterOffset(topic, &hight_water_offset);

  if (result == bbase::hdts::Error::kSuccess) {
    std::cout << "hight_water_offset: " << hight_water_offset << std::endl;
  } else {
    std::cout << "err code: " << static_cast<std::int32_t>(result) << std::endl;
  }
}

void TestLowWaterOffset() {
  std::string topic = "dts_demo_topic_1";
  bbase::hdts::Offset low_water_offset = 0;
  auto result = bbase::hdts::Hdts::GetLowWaterOffset(topic, &low_water_offset);

  if (result == bbase::hdts::Error::kSuccess) {
    std::cout << "low_water_offset: " << low_water_offset << std::endl;
  } else {
    std::cout << "err code: " << static_cast<std::int32_t>(result) << std::endl;
  }
}

void TestHdts() {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");

  TestHightWaterOffset();
  TestLowWaterOffset();

  TestProducer();
  TestConsume();

  bbase::hdts::Hdts::Fini();
}

void ProcessMatchingResult(biz::cross_idx_t cross_idx, const std::shared_ptr<biz::cross::XRespBase>&) {
  std::cout << "ProcessMatchingResult cross_idx: " << cross_idx << std::endl;
}

void TestCross() {
  cross::CrossConsumeTaskParam task;
  task.business_type = cross::BusinessType::kFbuType;
  task.topic_type = cross::TopicType::kRespType;
  task.cross_idx = 5;
  task.next_cross_seq = 1;
  task.process_matching_result = std::bind(ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  cross::CrossReceiver::AddConsumeTask(task);
}
void TestCross2() {
  cross::CrossConsumeTaskParam task;
  task.business_type = cross::BusinessType::kObuType;
  task.topic_type = cross::TopicType::kRespType;
  task.cross_idx = 10007;
  task.next_cross_seq = 1;
  task.process_matching_result = std::bind(ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  cross::CrossReceiver::AddConsumeTask(task);
}

void TestCrossSpot() {
  cross::CrossConsumeTaskParam task;
  task.business_type = cross::BusinessType::kSbuType;
  task.topic_type = cross::TopicType::kRespType;
  task.cross_idx = 20007;
  task.next_cross_seq = 1;
  task.process_matching_result = std::bind(ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  cross::CrossReceiver::AddConsumeTask(task);
}

void TestCrossHelper() {
  cross::CrossConsumeTaskParam task;
  task.business_type = cross::BusinessType::kFbuType;
  task.topic_type = cross::TopicType::kRespHelpType;
  task.cross_idx = 5;
  task.next_cross_seq = 91900;
  task.process_matching_result = std::bind(ProcessMatchingResult, std::placeholders::_1, std::placeholders::_2);
  cross::CrossReceiver::AddConsumeTask(task);
}

int main() {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::ObjectType::kObjectKafka,
                                                       std::make_shared<bbase::bkafka::Bkafka>());
  config::ConfigProxy::Instance().InitConfigManager();

  std::this_thread::sleep_for(std::chrono::seconds(1));
  cross::CrossReceiver::Init();
  //  TestCrossHelper();
  TestCrossSpot();

  std::this_thread::sleep_for(std::chrono::seconds(100000));
  cross::CrossReceiver::Fini();
  config::ConfigProxy::Instance().Fini();
  return 0;
}
