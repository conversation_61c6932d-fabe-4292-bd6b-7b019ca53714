find_package(Catch2 3 REQUIRED)

add_compile_options(-fno-access-control)

# These tests can use the Catch2-provided main
add_executable(bigint_ut.bin
        ${CMAKE_CURRENT_SOURCE_DIR}/main.cpp)

target_link_libraries(bigint_ut.bin PRIVATE
        Catch2::Catch2WithMain
        bbase::bcommon_ut
        bbase::bshare)

install(TARGETS bigint_ut.bin
        COMPONENT bigint_ut.bin
        PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
        RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/test_v2/benchmark
)
