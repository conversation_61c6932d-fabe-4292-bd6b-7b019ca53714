//
// Created by SH00588<PERSON> on 2023/3/29.
//
#include <gmock/gmock.h>

#include <iostream>
#include <memory>

#include "src/biz_worker/service/wallet/common/wallet_allinone_service.hpp"
#include "test/biz/wallet_service_ut/wallet_test.hpp"

#ifndef TEST_BIZ_WALLET_SERVICE_UT_SET_SPOT_LEVERAGE_SET_SPOT_LEVERAGE_SERVICE_TEST_HPP_
#define TEST_BIZ_WALLET_SERVICE_UT_SET_SPOT_LEVERAGE_SET_SPOT_LEVERAGE_SERVICE_TEST_HPP_

using testing::Return;
class MockedAioSrv : public biz::WalletAllInOneService {
 public:
  MOCK_METHOD(int32_t, CheckMarginRatio,
              (biz::DraftPkg * draft_pkg, bool, bool, bool, bool, bool, biz::MarginRatioCheckScene, bool,
               biz::symbol_t cur_symbol_id, biz::coin_t coin));
};

class SetSpotLeverageServiceTest : public WalletTest {};

#endif  // TEST_BIZ_WALLET_SERVICE_UT_SET_SPOT_LEVERAGE_SET_SPOT_LEVERAGE_SERVICE_TEST_HPP_
