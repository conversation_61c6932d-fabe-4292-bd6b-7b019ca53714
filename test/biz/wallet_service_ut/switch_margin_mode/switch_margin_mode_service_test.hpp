//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/3/24.
//

#include <gmock/gmock.h>

#include <iostream>
#include <memory>

#include "src/biz_worker/service/wallet/common/wallet_allinone_service.hpp"
#include "test/biz/wallet_service_ut/wallet_test.hpp"

#ifndef TEST_BIZ_WALLET_SERVICE_UT_SWITCH_MARGIN_MODE_SWITCH_MARGIN_MODE_SERVICE_TEST_HPP_
#define TEST_BIZ_WALLET_SERVICE_UT_SWITCH_MARGIN_MODE_SWITCH_MARGIN_MODE_SERVICE_TEST_HPP_

class MockedAioSrv4MarginMode : public biz::WalletAllInOneService {
 public:
};

class SwitchMarginModeServiceTest : public WalletTest {};

#endif  // TEST_BIZ_WALLET_SERVICE_UT_SWITCH_MARGIN_MODE_SWITCH_MARGIN_MODE_SERVICE_TEST_HPP_
