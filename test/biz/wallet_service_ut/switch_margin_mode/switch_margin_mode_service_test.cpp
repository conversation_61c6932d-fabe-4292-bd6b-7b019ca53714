//
// Created by SH00728ML on 2023/3/24.
//
#include "test/biz/wallet_service_ut/switch_margin_mode/switch_margin_mode_service_test.hpp"

#include <gtest/gtest.h>

#include "biz_worker/service/wallet/common/wallet_ret_code.hpp"
#include "biz_worker/service/wallet/wallet_service.hpp"
#include "margin_request/event/event.hpp"
#include "src/biz_worker/service/trade/futures/modules/commonbiz/lazy_init_biz.hpp"
#include "test/biz/trade/lib/stub.hpp"

TEST_F(WalletTest, SwitchMarginModeBizService) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("switch_margin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SwitchMarginMode);
  auto* switch_margin_mode_body = request->mutable_walletreqgroupbody()->mutable_marginmodereq();

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& se = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();

  // 不再支持旧的PM设置请求
  switch_margin_mode_body->set_margin_mode(true);
  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());
  ASSERT_EQ(ret, 10001);
  ASSERT_FALSE(se.pm_mode());

  se = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  se.account_mode = EAccountMode::Portfolio;
  switch_margin_mode_body->set_accountmode(EAccountMode::Portfolio);
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());
  ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);

  auto& other_se = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  other_se.account_mode = EAccountMode::UnKnown;
  switch_margin_mode_body->set_margin_mode(false);
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());
  ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);

  auto mockedAioSrv = std::make_shared<MockedAioSrv4MarginMode>();
  biz::WalletServiceFactory::wallet_biz_service_ = mockedAioSrv;

  switch_margin_mode_body->set_margin_mode(true);
  switch_margin_mode_body->set_accountmode(EAccountMode::Cross);

  ret = wallet_service->RunBySingleUser(biz_event, resource.get());
  ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);

  biz::WalletServiceFactory::InitService();  // free mockedAioSrv
}

TEST_F(WalletTest, SwitchMarginModeBizService_Isolated) {
  const std::shared_ptr<biz::SwitchMarginModeBizService> service = std::make_shared<biz::SwitchMarginModeBizService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto cur_setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = cur_setting;
  cur_setting->recalculate_initiated_ = true;

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("marginmodereq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SwitchMarginMode);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::MarginModeReq&>(request->walletreqgroupbody().marginmodereq());

  req.set_accountmode(EAccountMode::Isolated);

  int32_t ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, 0);
  EXPECT_EQ(per_user_store->user_setting_->Latest()->account_mode, EAccountMode::Isolated);
  draft_pkg->CommitAll();

  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, 0);

  req.set_accountmode(EAccountMode::UnKnown);
  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::ErrorCode::kRequestCheckException);
  draft_pkg->CommitAll();

  req.set_accountmode(EAccountMode::Isolated);
  per_user_store->user_setting_->LazyDraftS()->account_mode = EAccountMode::UnKnown;

  // 初始化per_user_store_的数据

  draft_pkg->CreateOrAttachDerivativesSymbolMarginCost(ECoin::USDC, 10099, EContractType::Option);

  draft_pkg->u_store->InsertOptionActiveOrderCoinSymbolMap(ECoin::USDC, 10099);
  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::ErrorCode::kOptionOrderNotEmpty);

  draft_pkg->u_store->RemoveOptionActiveOrderCoinSymbolMap(ECoin::USDC, 10099);
  draft_pkg->u_store->InsertOptionActivePositionCoinSymbolMap(ECoin::USDC, 10099);
  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::ErrorCode::kOptionPositionNotEmpty);

  draft_pkg->u_store->RemoveOptionActivePositionCoinSymbolMap(ECoin::USDC, 10099);
  auto cow_order = std::make_shared<store::CowOrder>();
  auto new_order = std::make_shared<store::Order>();
  cow_order->cur_order = (new_order);
  store::MmRateCloseOrderKey key{new_order->trigger_mm_rate_e2, new_order->symbol, new_order->position_idx};
  draft_pkg->u_store->all_future_mm_rate_close_order_[key] = cow_order.get();
  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::ErrorCode::kEcMmRateCloseOrderIsNotEmpty);
  draft_pkg->u_store->all_future_mm_rate_close_order_.clear();

  draft_pkg->CreateOrAttachSpotSymbolMarginCost(1, 1);

  store::CowSpotOrder::Ptr spotOrder = std::make_shared<store::CowSpotOrder>();
  store::SpotOrder::Ptr ao = std::make_shared<::store::SpotOrder>();
  spotOrder->cur_order = (ao);
  ao->leverage_place_type = ELeveragePlaceType::AUTO_LOAN_AND_SPOT;
  per_user_store->working_coins_[1]->working_spot_symbols_[1]->all_spot_orders_[biz::order_id_t("123")] =
      spotOrder.get();
  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::ErrorCode::kSpotLeverageOrderNotEmpty);
  ao->leverage_place_type = ELeveragePlaceType::SPOT_TYPE;

  auto dc_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->draft_wallet;
  dc_wallet->asset_frozen = bbase::decimal::Decimal<>("100");
  draft_pkg->CommitAll();
  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::ErrorCode::kErrorCodeModeSwitchValueExceededForSetRiskLimit);

  LOG_INFO("SwitchMarginModeBizService_Isolated Done");
}

TEST_F(WalletTest, SwitchMarginModeBizService_AccountModeSwitchIsolated) {
  const std::shared_ptr<biz::SwitchMarginModeBizService> service = std::make_shared<biz::SwitchMarginModeBizService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto cur_setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = cur_setting;
  cur_setting->recalculate_initiated_ = true;

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("marginmodereq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SwitchMarginMode);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::MarginModeReq&>(request->walletreqgroupbody().marginmodereq());

  req.set_accountmode(EAccountMode::Isolated);

  int32_t ret = service->AccountModeSwitchIsolated(draft_pkg.get());
  EXPECT_EQ(ret, 0);
  EXPECT_EQ(per_user_store->user_setting_->LazyDraftS()->account_mode, EAccountMode::Isolated);

  auto& usdcWallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->LazyDraftW();
  usdcWallet.wallet_balance = bbase::decimal::Decimal<>("-1");
  ret = service->AccountModeSwitchIsolated(draft_pkg.get());
  EXPECT_EQ(ret, error::kAssetBalanceCheckFail);
  usdcWallet.wallet_balance = bbase::decimal::Decimal<>("0");
  draft_pkg->CommitAll();

  draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  ret = service->AccountModeSwitchIsolated(draft_pkg.get());
  EXPECT_EQ(ret, 0);
  draft_pkg->CommitAll();

  draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& usdcWallet2 = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->LazyDraftW();
  usdcWallet2.liability = bbase::decimal::Decimal<>("1");
  ret = service->AccountModeSwitchIsolated(draft_pkg.get());
  EXPECT_EQ(ret, error::kAssetBalanceCheckFail);
  usdcWallet2.liability = bbase::decimal::Decimal<>("0");
  draft_pkg->CommitAll();

  draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& usdcWallet3 = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->LazyDraftW();
  usdcWallet3.liability_without_bonus = bbase::decimal::Decimal<>("1");
  ret = service->AccountModeSwitchIsolated(draft_pkg.get());
  EXPECT_EQ(ret, error::kLiabilityWithoutBonusCheckFail);
  usdcWallet3.liability_without_bonus = bbase::decimal::Decimal<>("0");
  draft_pkg->CommitAll();

  LOG_INFO("SwitchMarginModeBizService_AccountModeSwitchIsolated Done");
}

TEST_F(WalletTest, SwitchMarginModeBizService_Cross) {
  const std::shared_ptr<biz::SwitchMarginModeBizService> service = std::make_shared<biz::SwitchMarginModeBizService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto cur_setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = cur_setting;
  cur_setting->recalculate_initiated_ = true;

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("marginmodereq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SwitchMarginMode);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::MarginModeReq&>(request->walletreqgroupbody().marginmodereq());

  req.set_accountmode(EAccountMode::Cross);

  int32_t ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, 0);
  EXPECT_EQ(per_user_store->user_setting_->LazyDraftS()->account_mode, EAccountMode::Cross);

  per_user_store->user_setting_->LazyDraftS()->account_mode = EAccountMode::Isolated;

  LOG_INFO("SwitchMarginModeBizService_Cross Done");
}

TEST_F(WalletTest, SwitchMarginModeBizService_AccountModeSwitchCross) {
  const std::shared_ptr<biz::SwitchMarginModeBizService> service = std::make_shared<biz::SwitchMarginModeBizService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto cur_setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = cur_setting;
  cur_setting->recalculate_initiated_ = true;

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("marginmodereq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SwitchMarginMode);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  biz::commonbiz::LazyInitBiz::EnsureInitSymbolStore(draft_pkg.get(), ESymbol::BTCUSDT);
  auto& coin_store = draft_pkg->u_store->working_coins_[ECoin::USDT];
  auto& per_symbol_store = coin_store->working_future_symbols_[ESymbol::BTCUSDT];
  per_symbol_store->mode_ = EPositionMode::BothSide;
  per_symbol_store->all_positions_[EPositionIndex::Buy]->LazyDraftP()->risk_id = 1;
  per_symbol_store->all_positions_[EPositionIndex::Sell]->LazyDraftP()->risk_id = 2;

  int32_t ret = service->AccountModeSwitchCross(draft_pkg.get());
  EXPECT_EQ(ret, error::kErrorCodeSuccess);

  per_symbol_store->all_positions_[EPositionIndex::Buy]->LazyDraftP()->risk_id = 1;
  per_symbol_store->all_positions_[EPositionIndex::Sell]->LazyDraftP()->risk_id = 1;

  per_symbol_store->all_positions_[EPositionIndex::Buy]->LazyDraftP()->leverage = 1;
  per_symbol_store->all_positions_[EPositionIndex::Sell]->LazyDraftP()->leverage = 2;

  ret = service->AccountModeSwitchCross(draft_pkg.get());
  EXPECT_EQ(ret, error::kErrorCodeSuccess);

  per_symbol_store->all_positions_[EPositionIndex::Buy]->LazyDraftP()->leverage = 1;
  per_symbol_store->all_positions_[EPositionIndex::Sell]->LazyDraftP()->leverage = 1;
  draft_pkg->CommitAll();

  LOG_INFO("SwitchMarginModeBizService_AccountModeSwitchCross Done");
}

TEST_F(WalletTest, SwitchMarginModeBizService_Portfolio) {
  const std::shared_ptr<biz::SwitchMarginModeBizService> service = std::make_shared<biz::SwitchMarginModeBizService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto cur_setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = cur_setting;
  cur_setting->recalculate_initiated_ = true;

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("marginmodereq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SwitchMarginMode);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::MarginModeReq&>(request->walletreqgroupbody().marginmodereq());

  req.set_accountmode(EAccountMode::Portfolio);

  int32_t ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, 0);
  EXPECT_EQ(per_user_store->user_setting_->LazyDraftS()->account_mode, EAccountMode::Portfolio);

  per_user_store->user_setting_->LazyDraftS()->account_mode = EAccountMode::UnKnown;

  auto cow_order = std::make_shared<store::CowOrder>();
  auto new_order = std::make_shared<store::Order>();
  cow_order->cur_order = (new_order);
  store::MmRateCloseOrderKey key{cow_order->Latest()->trigger_mm_rate_e2, cow_order->Latest()->symbol,
                                 cow_order->Latest()->position_idx};
  draft_pkg->u_store->all_future_mm_rate_close_order_[key] = cow_order.get();
  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::ErrorCode::kEcMmRateCloseOrderIsNotEmpty);
  draft_pkg->u_store->all_future_mm_rate_close_order_.clear();

  auto dc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->LazyDraftW();
  dc_wallet.asset_frozen = bbase::decimal::Decimal<>("100");
  draft_pkg->CommitAll();
  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::ErrorCode::kErrorCodeModeSwitchValueExceededForSetRiskLimit);
  dc_wallet.asset_frozen = bbase::decimal::Decimal<>("0");

  biz::commonbiz::LazyInitBiz::EnsureInitSymbolStore(draft_pkg.get(), ESymbol::BTCUSDT);
  auto& coin_store = draft_pkg->u_store->working_coins_[ECoin::USDT];
  auto& per_symbol_store = coin_store->working_future_symbols_[ESymbol::BTCUSDT];
  per_symbol_store->mode_ = EPositionMode::BothSide;
  per_symbol_store->all_positions_[EPositionIndex::Buy]->LazyDraftP()->size = 1;

  per_user_store->user_setting_->LazyDraftS()->account_mode = EAccountMode::Cross;

  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::ErrorCode::kBothSidePositionNotEmpty);

  auto pz_p = std::make_shared<store::Position>();
  pz_p->symbol = ESymbol::BTCUSDT;
  pz_p->size = 100;
  pz_p->side = ESide::Buy;
  pz_p->user_id = 1001;
  auto cow_position = std::make_shared<store::CowPosition>(pz_p, EProductType::Futures);

  LOG_INFO("SwitchMarginModeBizService_Portfolio Done");
}

TEST_F(WalletTest, SwitchMarginModeBizService_AccountModeSwitchPm) {
  const std::shared_ptr<biz::SwitchMarginModeBizService> service = std::make_shared<biz::SwitchMarginModeBizService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto cur_setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = cur_setting;
  cur_setting->recalculate_initiated_ = true;

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("marginmodereq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SwitchMarginMode);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::MarginModeReq&>(request->walletreqgroupbody().marginmodereq());

  req.set_accountmode(EAccountMode::Isolated);

  biz::commonbiz::LazyInitBiz::EnsureInitSymbolStore(draft_pkg.get(), ESymbol::BTCUSDT);
  auto& coin_store = draft_pkg->u_store->working_coins_[ECoin::USDT];
  auto& per_symbol_store = coin_store->working_future_symbols_[ESymbol::BTCUSDT];
  per_symbol_store->mode_ = EPositionMode::BothSide;

  store::CowOrder::Ptr spotOrder = std::make_shared<store::CowOrder>();
  store::Order::Ptr ao = std::make_shared<::store::Order>();
  spotOrder->cur_order = (ao);
  per_symbol_store->all_future_orders_[biz::order_id_t("123")] = spotOrder.get();

  // draft_pkg->GetOrAttachUnifyWallet()->DraftRef()->wallet().im_ratio = 1;

  int32_t ret = service->AccountModeSwitchPm(draft_pkg.get());
  EXPECT_EQ(ret, error::kBothSideOrderNotEmpty);

  LOG_INFO("SwitchMarginModeBizService_AccountModeSwitchPm Done");
}

TEST_F(WalletTest, SwitchMarginModeBizService_checkIsLiqIng) {
  const std::shared_ptr<biz::SwitchMarginModeBizService> service = std::make_shared<biz::SwitchMarginModeBizService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto cur_setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = cur_setting;
  cur_setting->recalculate_initiated_ = true;

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("marginmodereq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SwitchMarginMode);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  biz::commonbiz::LazyInitBiz::EnsureInitSymbolStore(draft_pkg.get(), ESymbol::BTCUSDT);
  auto& coin_store = draft_pkg->u_store->working_coins_[ECoin::USDT];
  auto& per_symbol_store = coin_store->working_future_symbols_[ESymbol::BTCUSDT];
  per_symbol_store->mode_ = EPositionMode::BothSide;
  per_symbol_store->all_positions_[EPositionIndex::Buy]->LazyDraftP()->position_status = EPositionStatus::Liq;

  auto mutable_setting = const_cast<store::Setting*>(per_user_store->user_setting_->cur_setting.get());
  mutable_setting->account_mode = EAccountMode::Isolated;
  int32_t ret = service->checkIsLiqIng(draft_pkg.get());
  EXPECT_EQ(ret, error::kAccountIsInLiq);

  auto mutable_setting2 = const_cast<store::Setting*>(per_user_store->user_setting_->cur_setting.get());
  mutable_setting2->account_mode = EAccountMode::Cross;
  draft_pkg->GetOrAttachUnifyWallet()->LazyDraftW()->mm_ratio = 2;
  ret = service->checkIsLiqIng(draft_pkg.get());
  EXPECT_EQ(ret, error::kAccountIsInLiq);

  LOG_INFO("SwitchMarginModeBizService_checkIsLiqIng Done");
}

TEST_F(WalletTest, SwitchMarginModeBizService_UnKnown) {
  const std::shared_ptr<biz::SwitchMarginModeBizService> service = std::make_shared<biz::SwitchMarginModeBizService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto cur_setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = cur_setting;
  cur_setting->recalculate_initiated_ = true;
  cur_setting->user_tag = EUserTag::CopyPro;

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("marginmodereq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SwitchMarginMode);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  int32_t ret = service->AccountModeSwitch(draft_pkg.get(), EAccountMode::UnKnown);
  EXPECT_EQ(ret, error::kRequestCheckException);

  per_user_store->user_setting_->LazyDraftS()->account_mode = EAccountMode::Isolated;
  ret = service->AccountModeSwitch(draft_pkg.get(), EAccountMode::Isolated);
  EXPECT_EQ(ret, error::kErrorCodeSuccess);

  ret = service->AccountModeSwitch(draft_pkg.get(), EAccountMode::Portfolio);
  EXPECT_EQ(ret, error::kAccountMarginModeNotSupport);

  LOG_INFO("SwitchMarginModeBizService_UnKnown Done");
}

TEST_F(WalletTest, SwitchMarginModeBizService_equity_tpsl_data) {
  const std::shared_ptr<biz::SwitchMarginModeBizService> service = std::make_shared<biz::SwitchMarginModeBizService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto cur_setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = cur_setting;
  cur_setting->recalculate_initiated_ = true;
  cur_setting->account_mode = EAccountMode::Cross;

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("marginmodereq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SwitchMarginMode);
  req_header->set_cross_seq(999);

  LOG_INFO("SwitchMarginModeBizService_equity_tpsl_data Done");
}

TEST_F(WalletTest, SwitchMarginModeBizService_equity_tpsl_data_2) {
  const std::shared_ptr<biz::SwitchMarginModeBizService> service = std::make_shared<biz::SwitchMarginModeBizService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto cur_setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = cur_setting;
  cur_setting->recalculate_initiated_ = true;
  cur_setting->account_mode = EAccountMode::Cross;

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("marginmodereq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SwitchMarginMode);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  LOG_INFO("SwitchMarginModeBizService_equity_tpsl_data_2 Done");
}

TEST_F(WalletTest, SwitchMarginModeBizService_kInstAllTradeDisabled) {
  const std::shared_ptr<biz::SwitchMarginModeBizService> service = std::make_shared<biz::SwitchMarginModeBizService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto cur_setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = cur_setting;
  cur_setting->recalculate_initiated_ = true;
  cur_setting->account_mode = EAccountMode::Cross;
  cur_setting->all_trade_disabled = true;

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("marginmodereq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SwitchMarginMode);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto ret = service->AccountModeSwitch(draft_pkg.get(), EAccountMode::Portfolio);
  EXPECT_EQ(ret, error::kInstAllTradeDisabled);
  LOG_INFO("SwitchMarginModeBizService_kInstAllTradeDisabled Done");
}

TEST_F(WalletTest, SwitchMarginModeBizService_kUserUpgrading) {
  const std::shared_ptr<biz::SwitchMarginModeBizService> service = std::make_shared<biz::SwitchMarginModeBizService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto cur_setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = cur_setting;
  cur_setting->recalculate_initiated_ = true;
  cur_setting->account_mode = EAccountMode::Cross;
  cur_setting->upgrading_with_position = true;

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("marginmodereq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SwitchMarginMode);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto ret = service->AccountModeSwitch(draft_pkg.get(), EAccountMode::Portfolio);
  EXPECT_EQ(ret, error::kUserUpgrading);
  LOG_INFO("SwitchMarginModeBizService_kUserUpgrading Done");
}
