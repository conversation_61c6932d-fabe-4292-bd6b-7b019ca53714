//
// Author: <PERSON>.liu
// Created on 2023/3/24.
// Copyright (c) 2018-2023 inc. All rights reserved.

#include "test/biz/wallet_service_ut/lending_ut/spot_clear_process_test.hpp"

#include <memory>

#include "biz_worker/service/wallet/biz/lending/liability_settlement_service.hpp"

// 无负债，退出
TEST_F(WalletTest, SpotClearNoLiabilityService) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto deposit_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto deposit_req = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(deposit_request_dto);
  auto* deposit_req_header = deposit_req->mutable_req_header();
  deposit_req_header->set_req_id("deposit_1001_1");
  deposit_req_header->set_user_id(1001);
  deposit_req_header->set_coin(ECoin::USDT);
  deposit_req_header->set_action(EAction::Deposit);
  auto* deposit_body = deposit_req->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("1000");
  auto deposit_biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, deposit_req);
  auto deposit_draft_pkg =
      std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = deposit_draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = 1;

  int32_t ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());

  deposit_req_header->set_req_id("deposit_1001_2");
  deposit_body->set_coin(ECoin::USDC);
  deposit_body->set_amount("500");
  ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());

  ASSERT_EQ(ret, 0);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());

  int32_t const ret4 = biz::LiabilitySettlementService::Process(draft_pkg.get());
  ASSERT_EQ(ret4, 0);
  std::cout << ret << "spot clear test done" << std::endl;
}

// mmr触发，拒绝
TEST_F(WalletTest, SpotClearRejectMMR) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto deposit_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto deposit_req = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(deposit_request_dto);
  auto* deposit_req_header = deposit_req->mutable_req_header();
  deposit_req_header->set_req_id("deposit_1001_1");
  deposit_req_header->set_user_id(1001);
  deposit_req_header->set_coin(ECoin::USDT);
  deposit_req_header->set_action(EAction::Deposit);
  auto* deposit_body = deposit_req->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("1000");
  auto deposit_biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, deposit_req);
  auto deposit_draft_pkg =
      std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = deposit_draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = 1;

  int32_t ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());

  deposit_req_header->set_req_id("deposit_1001_2");
  deposit_body->set_coin(ECoin::USDC);
  deposit_body->set_amount("500");
  ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());

  ASSERT_EQ(ret, 0);

  // 封装请求
  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("SpotBorrowLiquidation_1001_2");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SpotBorrowLiquidation);

  // 还款数据
  auto* repayment_exchange_list =
      request->mutable_lendingreqgroupbody()->mutable_lendingrequest()->mutable_repaymentuserexchangerequest();

  auto usdt_coin = svc::unified_v2::req::lending::UserExchangeCoinRequest::default_instance();
  usdt_coin.set_amount("500");
  usdt_coin.set_coin(ECoin::USDT);
  usdt_coin.set_riskruleinfo("MM_UPPER_LIMIT");
  repayment_exchange_list->Add(std::move(usdt_coin));

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  int32_t const ret2 = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret2, 3200822);
  std::cout << ret << "spot clear test done" << std::endl;
}

// 模拟账户
TEST_F(WalletTest, LiabilitySettlementServiceUserLoanClear) {
  // auto liquidation_fee_rate = bbase::decimal::Decimal("0.01");
  // auto price_btc = bbase::decimal::Decimal("28000");
  // auto price_usdt = bbase::decimal::Decimal("1.001");
  // auto price_usdc = bbase::decimal::Decimal("0.997");
  //
  // auto liability = bbase::decimal::Decimal("0.01");
  // auto usdt = bbase::decimal::Decimal("10");
  // auto usdc = bbase::decimal::Decimal("1000");
  //
  // // 第一步
  // auto bankrupt_price = price_btc.Div(price_usdt).RoundDown(18);
  // auto asset_sold = liability.Mul(bankrupt_price).RoundUp(18);
  // auto liq_fee = asset_sold.Mul(liquidation_fee_rate).RoundUp(18);
  //
  // // 无法完全cover，正资产变卖所有，此处asset_sold不包含fee
  // asset_sold = usdt.Div(bbase::decimal::Decimal("1").Add(liquidation_fee_rate)).RoundUp(18);
  // liq_fee = usdt - asset_sold;
  // auto liability_bought = asset_sold.Div(bankrupt_price).RoundDown(18);
  //
  // liability -= liability_bought;
  // std::cout << "first liability_left: " << liability.StringFixed(18, true)
  //           << " total_sold: " << usdt.StringFixed(18, true) << " asset_sold: " << asset_sold.StringFixed(18, true)
  //           << " liq_fee: " << liq_fee.StringFixed(18, true)
  //           << " bankrupt_price: " << bankrupt_price.StringFixed(18, true) << std::endl;
  //
  // bankrupt_price = price_btc.Div(price_usdc).RoundDown(18);
  // asset_sold = liability.Mul(bankrupt_price).RoundUp(18);
  // liq_fee = asset_sold.Mul(liquidation_fee_rate).RoundUp(18);
  //
  // auto total_sold = asset_sold.Add(liq_fee);
  // auto total_sold2 = liability.Mul(bankrupt_price).Mul(bbase::decimal::Decimal("1").Add(liquidation_fee_rate));
  // auto left_asset = usdc.Sub(total_sold);
  // std::cout << "second total_sold: " << total_sold.StringFixed(18, true)
  //           << " total_sold2: " << total_sold2.StringFixed(18, true)
  //           << " asset_sold: " << asset_sold.StringFixed(18, true) << " liq_fee: " << liq_fee.StringFixed(18, true)
  //           << " left_asset: " << left_asset.StringFixed(18, true) << std::endl;

  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto deposit_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto deposit_req = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(deposit_request_dto);
  auto* deposit_req_header = deposit_req->mutable_req_header();
  deposit_req_header->set_req_id("deposit_1001_1");
  deposit_req_header->set_user_id(1001);
  deposit_req_header->set_coin(ECoin::USDT);
  deposit_req_header->set_action(EAction::Deposit);
  auto* deposit_body = deposit_req->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("10");
  auto deposit_biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, deposit_req);
  auto deposit_draft_pkg =
      std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = deposit_draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = 1;
  int32_t ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());

  deposit_req_header->set_req_id("deposit_1001_2");
  deposit_body->set_coin(ECoin::USDC);
  deposit_body->set_amount("1000");
  ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());
  ASSERT_EQ(ret, 0);

  // 封装请求
  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("SpotBorrowLiquidation_1001_6");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::BTC);
  req_header->set_action(EAction::SpotBorrowLiquidation);

  // 还款数据
  auto* lending_request = request->mutable_lendingreqgroupbody()->mutable_lendingrequest();
  lending_request->set_riskrule("USER_LOAN_CLEAR");
  auto* repayment_exchange_list = lending_request->mutable_repaymentuserexchangerequest();

  // 清退类型USER_LOAN_CLEAR，不会使用上游传递的负债数据，按自身实际负债执行
  auto btc_coin = svc::unified_v2::req::lending::UserExchangeCoinRequest::default_instance();
  btc_coin.set_amount("0.009");
  btc_coin.set_coin(ECoin::BTC);
  btc_coin.set_riskruleinfo("USER_LOAN_CLEAR");
  repayment_exchange_list->Add(std::move(btc_coin));

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  // 负债构造
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  auto cowWalletBTC = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC);
  auto arcWalletBTC = cowWalletBTC->LazyDraftW();
  arcWalletBTC->wallet_balance = biz::BigDecimal("-0.01");

  int32_t const ret4 = biz::LiabilitySettlementService::ProcessByLendingRequest(draft_pkg.get(), request);
  ASSERT_EQ(ret4, 0);
  auto& wallet_c = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->Latest();
  ASSERT_EQ(wallet_c.wallet_balance.ToDouble(), 726.389167502507495325);
  ASSERT_EQ(arcWalletBTC->wallet_balance.ToDouble(), 0);
  std::cout << ret << "spot clear test done" << std::endl;
}
