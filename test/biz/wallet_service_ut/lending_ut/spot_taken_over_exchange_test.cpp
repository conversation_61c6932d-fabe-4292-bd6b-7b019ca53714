//
// Author: <PERSON>.liu
// Created on 2023/3/24.
// Copyright (c) 2018-2023 inc. All rights reserved.

#include <memory>

#include "biz_worker/service/wallet/biz/lending/liability_settlement_service.hpp"
#include "test/biz/wallet_service_ut/lending_ut/spot_clear_process_test.hpp"

// 1. 不执行场景 有symbol场景空跑不执行
TEST_F(WalletTest, SpotTakenOverExchangeServiceWithSymbol) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto deposit_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto deposit_req = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(deposit_request_dto);
  auto* deposit_req_header = deposit_req->mutable_req_header();
  deposit_req_header->set_req_id("deposit_1001_1");
  deposit_req_header->set_user_id(1001);
  deposit_req_header->set_coin(ECoin::BTC);
  deposit_req_header->set_action(EAction::Deposit);
  auto* deposit_body = deposit_req->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::BTC);
  deposit_body->set_amount("1");
  auto deposit_biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, deposit_req);
  auto deposit_draft_pkg =
      std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = deposit_draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = 1;

  int32_t ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());
  ASSERT_EQ(ret, 0);
  ASSERT_DOUBLE_EQ(deposit_draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest()->wallet_balance.ToDouble(), 1);

  deposit_req_header->set_req_id("deposit_1001_2");
  deposit_body->set_coin(ECoin::USDC);
  deposit_body->set_amount("500");
  ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());
  ASSERT_EQ(ret, 0);

  auto draft_pkg = std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());

  int32_t const ret4 = biz::LiabilitySettlementService::SpotTakenOverExchangeProcess(draft_pkg.get());
  ASSERT_EQ(ret4, 0);
  // BTCUSDT存在不应被执行
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest()->wallet_balance.ToDouble(), 1);
  std::cout << ret << "spot taken over test done" << std::endl;
}

// 2. 无symbol币种接管兑币 正资产和负资产接管场景
TEST_F(WalletTest, SpotTakenOverExchangeServiceNoSymbol) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto deposit_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto deposit_req = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(deposit_request_dto);
  auto* deposit_req_header = deposit_req->mutable_req_header();
  deposit_req_header->set_req_id("deposit_1001_1");
  deposit_req_header->set_user_id(1001);
  deposit_req_header->set_coin(ECoin::USDT);
  deposit_req_header->set_action(EAction::Deposit);
  auto* deposit_body = deposit_req->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("1000");
  auto deposit_biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, deposit_req);
  auto deposit_draft_pkg =
      std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = deposit_draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = 1;

  int32_t ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());
  ASSERT_EQ(ret, 0);

  deposit_req_header->set_req_id("deposit_1001_2");
  deposit_body->set_coin(ECoin::USDC);
  deposit_body->set_amount("500");
  ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());
  ASSERT_EQ(ret, 0);

  // SOL负资产 -SOL mock行情及非保证金币种
  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("InterestSettlement_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::InterestSettlement);

  auto* interest_body = request->mutable_lendingreqgroupbody()->mutable_lendingrequest()->mutable_interestuserrequest();
  auto interest_request = interest_body->Add();
  interest_request->set_settlecoin(ECoin::BTC);
  interest_request->set_amount("1");

  auto interest_request2 = interest_body->Add();
  interest_request2->set_settlecoin(ECoin::SOL);
  interest_request2->set_amount("50");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  ret = wallet_service->RunBySingleUser(biz_event, resource.get());
  ASSERT_EQ(ret, 0);

  // 接管清算
  auto draft_pkg = std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());

  int32_t const ret4 = biz::LiabilitySettlementService::SpotTakenOverExchangeProcess(draft_pkg.get());
  ASSERT_EQ(ret4, 0);
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest()->wallet_balance.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest()->wallet_balance.ToDouble(), -1);
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::SOL)->Latest()->wallet_balance.ToDouble(), 0);
  std::cout << ret << "spot taken over test done" << std::endl;
}
