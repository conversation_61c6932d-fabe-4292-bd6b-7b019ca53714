//
// Author: <PERSON>.liu
// Created on 2023/3/29.
// Copyright (c) 2018-2023 inc. All rights reserved.

#include "test/biz/wallet_service_ut/lending_ut/interest_settlement_service_test.hpp"

#include "data/const.hpp"

TEST_F(WalletTest, InterestSettlementService_success) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();
  auto account = std::make_shared<store::Account>();
  account->account_status = enums::eaccountstatus::Normal;
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = account;
  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("InterestSettlement_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::InterestSettlement);

  auto* interest_body = request->mutable_lendingreqgroupbody()->mutable_lendingrequest()->mutable_interestuserrequest();
  auto interest_request = interest_body->Add();
  interest_request->set_settlecoin(ECoin::USDT);
  interest_request->set_amount("18");

  auto interest_request2 = interest_body->Add();
  interest_request2->set_settlecoin(ECoin::USDC);
  interest_request2->set_amount("36");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  // mock账户钱包
  auto cowWalletUSDT = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto arcWalletUSDT = cowWalletUSDT->LazyDraftW();
  arcWalletUSDT->wallet_balance = biz::BigDecimal("-1000");
  arcWalletUSDT->bonus = biz::BigDecimal("10");
  arcWalletUSDT->liability = biz::BigDecimal("1000");
  auto cowWalletUSDC = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC);
  auto arcWalletUSDC = cowWalletUSDC->LazyDraftW();
  arcWalletUSDC->wallet_balance = biz::BigDecimal("500");

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 0);
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest()->wallet_balance.ToDouble(), -1018);
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest()->liability.ToDouble(), 1018);
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest()->bonus.ToDouble(), 0);

  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->Latest()->wallet_balance.ToDouble(), 464);

  // 幂等场景
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 3200102);
}

TEST_F(WalletTest, InterestSettlementService_fail) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->per_worker_store = workerStore.get();
  auto account = std::make_shared<store::Account>();
  account->account_status = enums::eaccountstatus::Normal;
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = account;
  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("InterestSettlement_1001_2");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::InterestSettlement);

  auto* interest_body = request->mutable_lendingreqgroupbody()->mutable_lendingrequest()->mutable_interestuserrequest();

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  // mock账户钱包
  auto cowWalletUSDT = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto arcWalletUSDT = cowWalletUSDT->LazyDraftW();
  arcWalletUSDT->wallet_balance = biz::BigDecimal("-1000");
  arcWalletUSDT->bonus = biz::BigDecimal("10");
  auto cowWalletUSDC = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC);
  auto arcWalletUSDC = cowWalletUSDC->LazyDraftW();
  arcWalletUSDC->wallet_balance = biz::BigDecimal("500");
  draft_pkg->CommitAll();

  // 币种不存在
  auto interest_request = interest_body->Add();
  interest_request->set_settlecoin(9999);
  interest_request->set_amount("18");
  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());
  ASSERT_EQ(ret, 0);
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest()->wallet_balance.ToDouble(), -1000);
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->Latest()->wallet_balance.ToDouble(), 500);

  req_header->set_req_id("InterestSettlement_1001_3");
  interest_request->set_settlecoin(ECoin::USDT);
  // 结息为负，跳过执行
  auto interest_request2 = interest_body->Add();
  interest_request2->set_settlecoin(ECoin::USDC);
  interest_request2->set_amount("-33");
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 0);
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest()->wallet_balance.ToDouble(), -1018);
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->Latest()->wallet_balance.ToDouble(), 500);

  // 溢出场景
  req_header->set_req_id("InterestSettlement_1001_4");
  interest_request2->set_amount("52341633505405059324.173119706310967321");
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 0);
  ASSERT_FALSE(draft_pkg->HasErr());
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest()->wallet_balance.ToDouble(), -1036);
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->Latest()->wallet_balance.ToDouble(), 500);

  // 非数字
  req_header->set_req_id("InterestSettlement_1001_5");
  interest_request->set_amount("123abc");
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 0);
  ASSERT_FALSE(draft_pkg->HasErr());
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest()->wallet_balance.ToDouble(), -1036);
  ASSERT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->Latest()->wallet_balance.ToDouble(), 500);

  // 逐仓
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Isolated;

  // 逐仓不支持
  req_header->set_req_id("InterestSettlement_1001_6");
  interest_request->set_amount("123");
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());
  ASSERT_EQ(ret, 10001);
}
