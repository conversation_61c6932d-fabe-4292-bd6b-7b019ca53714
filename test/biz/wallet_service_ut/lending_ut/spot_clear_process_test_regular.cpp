
//
// Author: <PERSON>.liu
// Created on 2023/3/24.
// Copyright (c) 2018-2023 inc. All rights reserved.

#include <memory>

#include "biz_worker/service/wallet/biz/lending/liability_settlement_service.hpp"
#include "test/biz/wallet_service_ut/lending_ut/spot_clear_process_test.hpp"
#include "test/biz/wallet_service_ut/process_trading_result/trans_log_spot.hpp"

// 有负债无frozen，一个负债正常还债(非刚兑)
TEST_F(WalletTest, RegularRepayOneLiability) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto deposit_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto deposit_req = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(deposit_request_dto);
  auto* deposit_req_header = deposit_req->mutable_req_header();
  deposit_req_header->set_req_id("deposit_1001_1");
  deposit_req_header->set_user_id(1001);
  deposit_req_header->set_coin(ECoin::USDT);
  deposit_req_header->set_action(EAction::Deposit);
  auto* deposit_body = deposit_req->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("1000");
  auto deposit_biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, deposit_req);
  auto deposit_draft_pkg =
      std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = deposit_draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = 1;

  int32_t ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());

  deposit_req_header->set_req_id("deposit_1001_2");
  deposit_body->set_coin(ECoin::USDC);
  deposit_body->set_amount("500");
  ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());

  ASSERT_EQ(ret, 0);

  auto draft_pkg = std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());

  // spot buy
  {
    auto contract_type = EContractType::Spot;
    draft_pkg->spot_related_fills_.clear();
    draft_pkg->ref_spot_trans_logs_.clear();
    // buy
    TransLogSpotTest::MockSpotMatchingResultForBuy(draft_pkg.get(), 1400, 0);

    int32_t const ret3 =
        biz::WalletServiceFactory::BuildService()->ProcessTradingResult(draft_pkg.get(), contract_type);
    ASSERT_EQ(ret3, 0);
    ASSERT_EQ(draft_pkg->ref_spot_trans_logs_.size(), 2);
  }

  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0.5);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->liability.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->wallet_balance.ToDouble(), -400);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->liability.ToDouble(), 400);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->wallet_balance.ToDouble(), 500);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->liability.ToDouble(), 0);

  int32_t const ret4 = biz::LiabilitySettlementService::Process(draft_pkg.get());

  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->wallet_balance.ToDouble(),
                   0.485414);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->liability.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->liability.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->wallet_balance.ToDouble(), 500);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->liability.ToDouble(), 0);
  ASSERT_EQ(ret4, 0);
  std::cout << ret << "spot clear test done" << std::endl;
}

// 有负债无frozen，2个负债1个正资产(非刚兑)
TEST_F(WalletTest, RegularRepayTwoLiabilities) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto deposit_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto deposit_req = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(deposit_request_dto);
  auto* deposit_req_header = deposit_req->mutable_req_header();
  deposit_req_header->set_req_id("deposit_1001_1");
  deposit_req_header->set_user_id(1001);
  deposit_req_header->set_coin(ECoin::USDT);
  deposit_req_header->set_action(EAction::Deposit);
  auto* deposit_body = deposit_req->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("1000");
  auto deposit_biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, deposit_req);
  auto deposit_draft_pkg =
      std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = deposit_draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = 1;

  int32_t ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());
  ASSERT_EQ(ret, 0);

  deposit_req_header->set_req_id("deposit_1001_2");
  deposit_body->set_coin(ECoin::USDC);
  deposit_body->set_amount("16000");
  ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());

  ASSERT_EQ(ret, 0);

  auto draft_pkg = std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());

  // spot buy
  {
    auto contract_type = EContractType::Spot;
    draft_pkg->spot_related_fills_.clear();
    draft_pkg->ref_spot_trans_logs_.clear();
    // buy
    TransLogSpotTest::MockSpotMatchingResultForBuy(draft_pkg.get(), 1400, 1);

    int32_t const ret3 =
        biz::WalletServiceFactory::BuildService()->ProcessTradingResult(draft_pkg.get(), contract_type);
    ASSERT_EQ(ret3, 0);
    ASSERT_EQ(draft_pkg->ref_spot_trans_logs_.size(), 2);
  }

  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->wallet_balance.ToDouble(), -0.5);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->liability.ToDouble(), 0.5);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->wallet_balance.ToDouble(), -400);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->liability.ToDouble(), 400);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->wallet_balance.ToDouble(),
                   16000);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->liability.ToDouble(), 0);

  int32_t const ret4 = biz::LiabilitySettlementService::Process(draft_pkg.get());

  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->liability.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->liability.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->wallet_balance.ToDouble(),
                   1267.3941825476429);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->liability.ToDouble(), 0);
  ASSERT_EQ(ret4, 0);
  std::cout << ret << "spot clear test done" << std::endl;
}

TEST_F(WalletTest, spot_clear_fee1) {
  MockCoin(ECoin::LTC, "LTC", biz::BigDecimal(1));
  MockCoin(ECoin::AVA, "AVA", biz::BigDecimal(1));
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();
  auto liq_event = std::make_shared<event::OnLiqEvent>(1001, EAction::TriggerReBalance);
  auto liq_header = std::make_shared<store::Header>(liq_event->header());
  auto draft_pkg = std::make_shared<biz::DraftPkg>(liq_event, liq_header, workerStore.get(), per_user_store.get());

  auto account_info = draft_pkg->GetOrAttachAccountInfo()->LazyDraftA();
  account_info->account_status = enums::eaccountstatus::AccountStatus::Normal;

  auto& ava_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::AVA)->LazyDraftW();
  ava_wallet.wallet_balance = bbase::decimal::Decimal("100");
  ava_wallet.equity = bbase::decimal::Decimal("100");

  auto& ltc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::LTC)->LazyDraftW();
  ltc_wallet.wallet_balance = bbase::decimal::Decimal("-100");
  ltc_wallet.liability = bbase::decimal::Decimal("100");

  auto& usdt_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->LazyDraftW();
  usdt_wallet.asset_im = decimal::kOne;  // 模拟有衍生品，不进行刚兑

  int32_t ret = biz::LiabilitySettlementService::Process(draft_pkg.get());
  ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);

  auto& spot_clear_context = draft_pkg->CreateOrAttachSpotClearContext();
  ASSERT_EQ(spot_clear_context->result_code, 0);
  ASSERT_EQ(spot_clear_context->exchange_records_.size(), 1);

  // 兑币单
  auto& exchange_order = spot_clear_context->exchange_records_[0];
  ASSERT_EQ(exchange_order->sell_coin_id_, ECoin::AVA);
  ASSERT_EQ(exchange_order->buy_coin_id_, ECoin::LTC);
  ASSERT_EQ(exchange_order->asset_sold_amount_.StringFixed(18), "98.039215686274509803");
  ASSERT_EQ(exchange_order->sell_money_.StringFixed(18), "100.000000000000000000");
  ASSERT_EQ(exchange_order->buy_money_.StringFixed(18), "98.039215686274509803");
  ASSERT_EQ(exchange_order->fee_money_.StringFixed(18), "1.960784313725490197");
  ASSERT_EQ(exchange_order->fee_rate_.StringFixed(18), "0.020000000000000000");
  ASSERT_EQ(exchange_order->is_rigid, false);
  ASSERT_EQ(exchange_order->order_type_, svc::unified_v2::res::ExchangeOrderType::AUTO_EXCHANGE);
  ASSERT_EQ(exchange_order->exchange_price_.StringFixed(18), "1.000000000000000000");

  // translog
  ASSERT_EQ(draft_pkg->ref_asset_trans_logs_.size(), 2);
  int32_t msg_count = 0;
  for (const auto& trans_log : draft_pkg->ref_asset_trans_logs_) {
    if (trans_log->trans_type_ == store::TransLog::TransTypeEnum::kTransTypeSpotAutoExchangeSell) {
      msg_count++;
      ASSERT_EQ(trans_log->change_.StringFixed(18), "-100.000000000000000000");
      ASSERT_EQ(trans_log->cash_balance_.StringFixed(18), "0.0");
      ASSERT_EQ(trans_log->exec_fee_.StringFixed(18), "-1.960784313725490197");
      ASSERT_EQ(trans_log->fee_rate_.StringFixed(18), "0.020000000000000000");
    }

    if (trans_log->trans_type_ == store::TransLog::TransTypeEnum::kTransTypeSpotAutoExchangeBuy) {
      msg_count++;
      ASSERT_EQ(trans_log->change_.StringFixed(18), "98.039215686274509803");
      ASSERT_EQ(trans_log->cash_balance_.StringFixed(18), "-1.960784313725490197");
      ASSERT_EQ(trans_log->exec_fee_.StringFixed(18), "0.0");
      ASSERT_EQ(trans_log->fee_rate_.StringFixed(18), "0.0");
    }
  }
  ASSERT_EQ(msg_count, 2);
  // 钱包
  ltc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::LTC)->Latest();
  ASSERT_EQ(ltc_wallet.wallet_balance.StringFixed(18), "-1.960784313725490197");
  ASSERT_EQ(ltc_wallet.liability.StringFixed(18), "1.960784313725490197");

  ava_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::AVA)->Latest();
  ASSERT_EQ(ava_wallet.wallet_balance.StringFixed(18), "0.0");
  ASSERT_EQ(ava_wallet.equity.StringFixed(18), "0.0");
}

TEST_F(WalletTest, spot_clear_fee2) {
  MockCoin(ECoin::LTC, "LTC", biz::BigDecimal(1));
  MockCoin(ECoin::AVA, "AVA", biz::BigDecimal(1));
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();
  auto liq_event = std::make_shared<event::OnLiqEvent>(1001, EAction::TriggerReBalance);
  auto liq_header = std::make_shared<store::Header>(liq_event->header());
  auto draft_pkg = std::make_shared<biz::DraftPkg>(liq_event, liq_header, workerStore.get(), per_user_store.get());

  auto account_info = draft_pkg->GetOrAttachAccountInfo()->LazyDraftA();
  account_info->account_status = enums::eaccountstatus::AccountStatus::Normal;

  auto& ava_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::AVA)->LazyDraftW();
  ava_wallet.wallet_balance = bbase::decimal::Decimal("120");
  ava_wallet.equity = bbase::decimal::Decimal("120");
  ava_wallet.asset_frozen = bbase::decimal::Decimal("20");

  auto& ltc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::LTC)->LazyDraftW();
  ltc_wallet.wallet_balance = bbase::decimal::Decimal("-98.02");
  ltc_wallet.liability = bbase::decimal::Decimal("98.02");

  auto& usdt_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->LazyDraftW();
  usdt_wallet.asset_im = decimal::kOne;  // 模拟有衍生品，不进行刚兑
  int32_t ret = biz::LiabilitySettlementService::Process(draft_pkg.get());
  ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);

  auto& spot_clear_context = draft_pkg->CreateOrAttachSpotClearContext();
  ASSERT_EQ(spot_clear_context->result_code, 0);

  ASSERT_EQ(spot_clear_context->exchange_records_.size(), 1);

  // 兑币单
  auto& exchange_order = spot_clear_context->exchange_records_[0];
  ASSERT_EQ(exchange_order->sell_coin_id_, ECoin::AVA);
  ASSERT_EQ(exchange_order->buy_coin_id_, ECoin::LTC);
  ASSERT_EQ(exchange_order->asset_sold_amount_.StringFixed(18), "98.020000000000000000");
  ASSERT_EQ(exchange_order->sell_money_.StringFixed(18), "99.980400000000000000");
  ASSERT_EQ(exchange_order->buy_money_.StringFixed(18), "98.020000000000000000");
  ASSERT_EQ(exchange_order->fee_money_.StringFixed(18), "1.960400000000000000");
  ASSERT_EQ(exchange_order->fee_rate_.StringFixed(18), "0.020000000000000000");
  ASSERT_EQ(exchange_order->is_rigid, false);
  ASSERT_EQ(exchange_order->order_type_, svc::unified_v2::res::ExchangeOrderType::AUTO_EXCHANGE);
  ASSERT_EQ(exchange_order->exchange_price_.StringFixed(18), "1.000000000000000000");

  // translog
  ASSERT_EQ(draft_pkg->ref_asset_trans_logs_.size(), 2);
  int32_t msg_count = 0;
  for (const auto& trans_log : draft_pkg->ref_asset_trans_logs_) {
    if (trans_log->trans_type_ == store::TransLog::TransTypeEnum::kTransTypeSpotAutoExchangeSell) {
      msg_count++;
      ASSERT_EQ(trans_log->change_.StringFixed(18), "-99.980400000000000000");
      ASSERT_EQ(trans_log->cash_balance_.StringFixed(18), "20.019600000000000000");
      ASSERT_EQ(trans_log->exec_fee_.StringFixed(18), "-1.960400000000000000");
      ASSERT_EQ(trans_log->fee_rate_.StringFixed(18), "0.020000000000000000");
    }

    if (trans_log->trans_type_ == store::TransLog::TransTypeEnum::kTransTypeSpotAutoExchangeBuy) {
      msg_count++;
      ASSERT_EQ(trans_log->change_.StringFixed(18), "98.020000000000000000");
      ASSERT_EQ(trans_log->cash_balance_.StringFixed(18), "0.0");
      ASSERT_EQ(trans_log->exec_fee_.StringFixed(18), "0.0");
      ASSERT_EQ(trans_log->fee_rate_.StringFixed(18), "0.0");
    }
  }
  ASSERT_EQ(msg_count, 2);
  // 钱包
  ltc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::LTC)->Latest();
  ASSERT_EQ(ltc_wallet.wallet_balance.StringFixed(18), "0.0");
  ASSERT_EQ(ltc_wallet.liability.StringFixed(18), "0.0");

  ava_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::AVA)->Latest();
  ASSERT_EQ(ava_wallet.wallet_balance.StringFixed(18), "20.019600000000000000");
  ASSERT_EQ(ava_wallet.equity.StringFixed(18), "20.019600000000000000");
  ASSERT_EQ(ava_wallet.asset_frozen.StringFixed(18), "20.000000000000000000");
}

// 兩幣兌換值不是1, 正資產不足
TEST_F(WalletTest, spot_clear_fee3) {
  MockCoin(ECoin::DOGE, "DOGE", biz::BigDecimal(1));
  MockCoin(ECoin::LINK, "LINK", biz::BigDecimal(1));
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();
  auto liq_event = std::make_shared<event::OnLiqEvent>(1001, EAction::TriggerReBalance);
  auto liq_header = std::make_shared<store::Header>(liq_event->header());
  auto draft_pkg = std::make_shared<biz::DraftPkg>(liq_event, liq_header, workerStore.get(), per_user_store.get());

  auto account_info = draft_pkg->GetOrAttachAccountInfo()->LazyDraftA();
  account_info->account_status = enums::eaccountstatus::AccountStatus::Normal;

  auto& ava_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::LINK)->LazyDraftW();
  ava_wallet.wallet_balance = bbase::decimal::Decimal("100");
  ava_wallet.equity = bbase::decimal::Decimal("100");

  auto& ltc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::DOGE)->LazyDraftW();
  ltc_wallet.wallet_balance = bbase::decimal::Decimal("-100");
  ltc_wallet.liability = bbase::decimal::Decimal("100");

  auto& usdt_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->LazyDraftW();
  usdt_wallet.asset_im = decimal::kOne;  // 模拟有衍生品，不进行刚兑

  int32_t ret = biz::LiabilitySettlementService::Process(draft_pkg.get());
  ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);

  auto& spot_clear_context = draft_pkg->CreateOrAttachSpotClearContext();
  ASSERT_EQ(spot_clear_context->result_code, 0);
  ASSERT_EQ(spot_clear_context->exchange_records_.size(), 1);

  // 兑币单
  auto& exchange_order = spot_clear_context->exchange_records_[0];
  ASSERT_EQ(exchange_order->sell_coin_id_, ECoin::LINK);
  ASSERT_EQ(exchange_order->buy_coin_id_, ECoin::DOGE);
  ASSERT_EQ(exchange_order->asset_sold_amount_.StringFixed(18), "98.039215686274509803");
  ASSERT_EQ(exchange_order->sell_money_.StringFixed(18), "100.000000000000000000");
  ASSERT_EQ(exchange_order->buy_money_.StringFixed(18), "58.823529411764705905");
  ASSERT_EQ(exchange_order->fee_money_.StringFixed(18), "1.960784313725490197");
  ASSERT_EQ(exchange_order->fee_rate_.StringFixed(18), "0.020000000000000000");
  ASSERT_EQ(exchange_order->is_rigid, false);
  ASSERT_EQ(exchange_order->order_type_, svc::unified_v2::res::ExchangeOrderType::AUTO_EXCHANGE);
  ASSERT_EQ(exchange_order->exchange_price_.StringFixed(18), "1.666666666666666666");

  // translog
  ASSERT_EQ(draft_pkg->ref_asset_trans_logs_.size(), 2);
  int32_t msg_count = 0;
  for (const auto& trans_log : draft_pkg->ref_asset_trans_logs_) {
    if (trans_log->trans_type_ == store::TransLog::TransTypeEnum::kTransTypeSpotAutoExchangeSell) {
      msg_count++;
      ASSERT_EQ(trans_log->change_.StringFixed(18), "-100.000000000000000000");
      ASSERT_EQ(trans_log->cash_balance_.StringFixed(18), "0.0");
      ASSERT_EQ(trans_log->exec_fee_.StringFixed(18), "-1.960784313725490197");
      ASSERT_EQ(trans_log->fee_rate_.StringFixed(18), "0.020000000000000000");
    }

    if (trans_log->trans_type_ == store::TransLog::TransTypeEnum::kTransTypeSpotAutoExchangeBuy) {
      msg_count++;
      ASSERT_EQ(trans_log->change_.StringFixed(18), "58.823529411764705905");
      ASSERT_EQ(trans_log->cash_balance_.StringFixed(18), "-41.176470588235294095");
      ASSERT_EQ(trans_log->exec_fee_.StringFixed(18), "0.0");
      ASSERT_EQ(trans_log->fee_rate_.StringFixed(18), "0.0");
    }
  }
  ASSERT_EQ(msg_count, 2);
  // 钱包
  ltc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::DOGE)->Latest();
  ASSERT_EQ(ltc_wallet.wallet_balance.StringFixed(18), "-41.176470588235294095");
  ASSERT_EQ(ltc_wallet.liability.StringFixed(18), "41.176470588235294095");

  ava_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::LINK)->Latest();
  ASSERT_EQ(ava_wallet.wallet_balance.StringFixed(18), "0.0");
  ASSERT_EQ(ava_wallet.equity.StringFixed(18), "0.0");
}
