//
// Author: <PERSON>.liu
// Created on 2023/3/24.
// Copyright (c) 2018-2023 inc. All rights reserved.

#include <iostream>
#include <memory>

#include "biz_worker/service/trade/liquidation/calculator/clean_spot_step_calculation.hpp"
#include "biz_worker/service/wallet/biz/lending/liability_settlement_service.hpp"
#include "test/biz/wallet_service_ut/lending_ut/spot_clear_process_test.hpp"
#include "test/biz/wallet_service_ut/process_trading_result/trans_log_spot.hpp"

// 有负债无frozen，还债(刚兑场景1：2个负债，第一个正常刚兑，第二个无正资产刚兑)
TEST_F(WalletTest, RigidRepayLiability) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto deposit_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto deposit_req = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(deposit_request_dto);
  auto* deposit_req_header = deposit_req->mutable_req_header();
  deposit_req_header->set_req_id("deposit_1001_1");
  deposit_req_header->set_user_id(1001);
  deposit_req_header->set_coin(ECoin::USDT);
  deposit_req_header->set_action(EAction::Deposit);
  auto* deposit_body = deposit_req->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("1000");
  auto deposit_biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, deposit_req);
  auto deposit_draft_pkg =
      std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = deposit_draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = 1;

  int32_t ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());

  deposit_req_header->set_req_id("deposit_1001_2");
  deposit_body->set_coin(ECoin::USDC);
  deposit_body->set_amount("500");
  ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());

  ASSERT_EQ(ret, 0);

  auto draft_pkg = std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());

  // spot buy
  {
    auto contract_type = EContractType::Spot;
    draft_pkg->spot_related_fills_.clear();
    draft_pkg->ref_spot_trans_logs_.clear();
    // buy
    TransLogSpotTest::MockSpotMatchingResultForBuy(draft_pkg.get());

    int32_t const ret3 =
        biz::WalletServiceFactory::BuildService()->ProcessTradingResult(draft_pkg.get(), contract_type);
    ASSERT_EQ(ret3, 0);
    ASSERT_EQ(draft_pkg->ref_spot_trans_logs_.size(), 2);
  }

  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->wallet_balance.ToDouble(),
                   -1999.5);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->liability.ToDouble(), 1999.5);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->wallet_balance.ToDouble(), -9000);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->liability.ToDouble(), 9000);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->wallet_balance.ToDouble(), 500);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->liability.ToDouble(), 0);
  int32_t const ret4 = biz::LiabilitySettlementService::Process(draft_pkg.get());
  ASSERT_EQ(ret4, 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->liability.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->liability.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->liability.ToDouble(), 0);
  std::cout << ret << "spot clear test done" << std::endl;
}

// 有负债无frozen，还债(刚兑场景2：2个负债，无正资产刚兑)
TEST_F(WalletTest, RigidRepayWithoutPositiveAsset) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto deposit_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto deposit_req = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(deposit_request_dto);
  auto* deposit_req_header = deposit_req->mutable_req_header();
  deposit_req_header->set_req_id("deposit_1001_1");
  deposit_req_header->set_user_id(1001);
  deposit_req_header->set_coin(ECoin::USDT);
  deposit_req_header->set_action(EAction::Deposit);
  auto* deposit_body = deposit_req->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("1000");
  auto deposit_biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, deposit_req);
  auto deposit_draft_pkg =
      std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = deposit_draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = 1;

  int32_t ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());

  ASSERT_EQ(ret, 0);

  auto draft_pkg = std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());

  // spot buy
  {
    auto contract_type = EContractType::Spot;
    draft_pkg->spot_related_fills_.clear();
    draft_pkg->ref_spot_trans_logs_.clear();
    // buy
    TransLogSpotTest::MockSpotMatchingResultForBuy(draft_pkg.get());

    int32_t const ret3 =
        biz::WalletServiceFactory::BuildService()->ProcessTradingResult(draft_pkg.get(), contract_type);
    ASSERT_EQ(ret3, 0);
    ASSERT_EQ(draft_pkg->ref_spot_trans_logs_.size(), 2);
  }

  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->wallet_balance.ToDouble(),
                   -1999.5);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->liability.ToDouble(), 1999.5);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->wallet_balance.ToDouble(), -9000);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->liability.ToDouble(), 9000);
  int32_t const ret4 = biz::LiabilitySettlementService::Process(draft_pkg.get());
  ASSERT_EQ(ret4, 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->liability.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->liability.ToDouble(), 0);
  std::cout << ret << "spot clear test done" << std::endl;
}

// 有负债无frozen，还债(刚兑场景2：2个负债，第一个正常兑币，第二个刚兑)
TEST_F(WalletTest, RepayTwoAssetsWithOneRigid) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto deposit_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto deposit_req = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(deposit_request_dto);
  auto* deposit_req_header = deposit_req->mutable_req_header();
  deposit_req_header->set_req_id("deposit_1001_1");
  deposit_req_header->set_user_id(1001);
  deposit_req_header->set_coin(ECoin::USDT);
  deposit_req_header->set_action(EAction::Deposit);
  auto* deposit_body = deposit_req->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("1000");
  auto deposit_biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, deposit_req);
  auto deposit_draft_pkg =
      std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = deposit_draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = 1;

  int32_t ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());

  deposit_req_header->set_req_id("deposit_1001_2");
  deposit_body->set_coin(ECoin::USDC);
  deposit_body->set_amount("16000");
  ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());

  ASSERT_EQ(ret, 0);

  auto draft_pkg = std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());

  // spot buy
  {
    auto contract_type = EContractType::Spot;
    draft_pkg->spot_related_fills_.clear();
    draft_pkg->ref_spot_trans_logs_.clear();
    // buy
    TransLogSpotTest::MockSpotMatchingResultForBuy(draft_pkg.get(), 24000, 1);

    int32_t const ret3 =
        biz::WalletServiceFactory::BuildService()->ProcessTradingResult(draft_pkg.get(), contract_type);
    ASSERT_EQ(ret3, 0);
    ASSERT_EQ(draft_pkg->ref_spot_trans_logs_.size(), 2);
  }

  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->wallet_balance.ToDouble(), -0.5);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->liability.ToDouble(), 0.5);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->wallet_balance.ToDouble(),
                   -23000);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->liability.ToDouble(), 23000);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->wallet_balance.ToDouble(),
                   16000);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->liability.ToDouble(), 0);
  int32_t const ret4 = biz::LiabilitySettlementService::Process(draft_pkg.get());
  ASSERT_EQ(ret4, 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->liability.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->liability.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(
      per_user_store->working_coins_.find(16)->second->wallet_->Latest()->wallet_balance.Floor().ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(16)->second->wallet_->Latest()->liability.Floor().ToDouble(), 0);
  std::cout << ret << "spot clear test done" << std::endl;
}

/**
 * 19位负债处理场景
 * 应该多还一些， 防止兑币完成后负债未还清，MMR仍溢出
 * 负债：0.0054965710800000027 (存在第19位)
 */
TEST_F(WalletTest, RegularRepayOneLiabilityTwoAssets) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto deposit_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto deposit_req = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(deposit_request_dto);
  auto* deposit_req_header = deposit_req->mutable_req_header();
  deposit_req_header->set_req_id("deposit_1001_1");
  deposit_req_header->set_user_id(1001);
  deposit_req_header->set_coin(ECoin::MKR);
  deposit_req_header->set_action(EAction::Deposit);
  auto* deposit_body = deposit_req->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::MKR);
  deposit_body->set_amount("0.00549108000000000200");
  auto deposit_biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, deposit_req);
  auto deposit_draft_pkg =
      std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = deposit_draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = 1;

  int32_t ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());
  ASSERT_EQ(ret, 0);

  auto draft_pkg = std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());
  // spot buy
  {
    auto contract_type = EContractType::Spot;
    draft_pkg->spot_related_fills_.clear();
    draft_pkg->ref_spot_trans_logs_.clear();
    // buy
    auto const trade = std::make_shared<store::SpotOrder>();
    auto& raw_transact = *trade;
    raw_transact.op_from = "app";
    raw_transact.transact_time = bbase::utils::Time::GetTimeNs();
    raw_transact.cross_seq = 1003;

    raw_transact.base_coin = 1;
    raw_transact.settle_coin = 5;
    raw_transact.symbol = 1;
    raw_transact.symbol_name = "BTCUSDT";
    raw_transact.order_id = "order_id_1";
    raw_transact.side = ESide::Buy;
    raw_transact.exec_amount = bbase::decimal::Decimal<>("0.00549108000000000250");
    raw_transact.exec_qty = bbase::decimal::Decimal<>("0");
    raw_transact.exec_fee = bbase::decimal::Decimal<>("0");
    raw_transact.exec_price = bbase::decimal::Decimal<>(20000);
    raw_transact.price = bbase::decimal::Decimal<>(19000);
    raw_transact.exec_type = EExecType::Trade;
    raw_transact.exec_id = "exec_id_01";
    raw_transact.last_liquidity_ind = ELastLiquidityInd::AddedLiquidity;

    raw_transact.block_order_id = "block_id_001";
    raw_transact.order_link_id = "order_link_id_001";
    raw_transact.default_maker_fee_rate_e8 = 78;
    raw_transact.default_taker_fee_rate_e8 = 79;

    draft_pkg->spot_related_fills_.push_back(trade);

    draft_pkg->GetOrAttachSettleCoinWallet(raw_transact.settle_coin);
    draft_pkg->GetOrAttachSettleCoinWallet(raw_transact.base_coin);

    int32_t const ret3 =
        biz::WalletServiceFactory::BuildService()->ProcessTradingResult(draft_pkg.get(), contract_type);
    ASSERT_EQ(ret3, 0);
    ASSERT_EQ(draft_pkg->ref_spot_trans_logs_.size(), 2);
    auto const& usd_wallet = *draft_pkg->GetOrAttachUnifyWallet()->LazyDraftW();
    ASSERT_EQ(usd_wallet.im_ratio, 100);
    ASSERT_EQ(usd_wallet.mm_ratio, 100);
    ASSERT_EQ(usd_wallet.liability.StringFixed(18), "0.005496571080000003");  // fixme: decimal版本尾数是 0002
  }

  {
    int32_t const ret4 = biz::LiabilitySettlementService::Process(draft_pkg.get());

    ASSERT_EQ(ret4, 0);
    ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0);
    ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->liability.ToDouble(), 0);
    auto const& usd_wallet = *draft_pkg->GetOrAttachUnifyWallet()->LazyDraftW();
    ASSERT_EQ(usd_wallet.mm_ratio, 0);
    ASSERT_EQ(usd_wallet.im_ratio, 0);
    ASSERT_DOUBLE_EQ(usd_wallet.liability.ToDouble(), 0);
    ASSERT_DOUBLE_EQ(usd_wallet.wallet_balance.ToDouble(), 0);  // 19位负债被偿还
    auto spot_clear_context = draft_pkg->CreateOrAttachSpotClearContext();
    std::cout << ret << "spot clear test done" << std::endl;
  }
}

/**
 * 19位负债处理场景
 * 应该多还一些， 防止兑币完成后负债未还清，MMR仍溢出
 * 负债：0.0054965710800000027 (存在第19位)
 */
TEST_F(WalletTest, RegularRepayOneLiabilityTwoAssets_clean_step) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto deposit_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto deposit_req = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(deposit_request_dto);
  auto* deposit_req_header = deposit_req->mutable_req_header();
  deposit_req_header->set_req_id("deposit_1001_1");
  deposit_req_header->set_user_id(1001);
  deposit_req_header->set_coin(ECoin::MKR);
  deposit_req_header->set_action(EAction::Deposit);
  auto* deposit_body = deposit_req->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::MKR);
  deposit_body->set_amount("0.00549108000000000200");
  auto deposit_biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, deposit_req);
  auto deposit_draft_pkg =
      std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = deposit_draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = 1;

  int32_t ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());
  ASSERT_EQ(ret, 0);

  auto draft_pkg = std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());

  draft_pkg->u_store->cow_liquidation_ =
      std::make_shared<store::CowLiquidation>(std::make_shared<store::Liquidation>());
  // spot buy
  {
    auto contract_type = EContractType::Spot;
    draft_pkg->spot_related_fills_.clear();
    draft_pkg->ref_spot_trans_logs_.clear();
    // buy
    auto const trade = std::make_shared<store::SpotOrder>();
    auto& raw_transact = *trade;
    raw_transact.op_from = "app";
    raw_transact.transact_time = bbase::utils::Time::GetTimeNs();
    raw_transact.cross_seq = 1003;

    raw_transact.base_coin = 1;
    raw_transact.settle_coin = 5;
    raw_transact.symbol = 1;
    raw_transact.symbol_name = "BTCUSDT";
    raw_transact.order_id = "order_id_1";
    raw_transact.side = ESide::Buy;
    raw_transact.exec_amount = bbase::decimal::Decimal<>("0.00549108000000000250");
    raw_transact.exec_qty = bbase::decimal::Decimal<>("0");
    raw_transact.exec_fee = bbase::decimal::Decimal<>("0");
    raw_transact.exec_price = bbase::decimal::Decimal<>(20000);
    raw_transact.price = bbase::decimal::Decimal<>(19000);
    raw_transact.exec_type = EExecType::Trade;
    raw_transact.exec_id = "exec_id_01";
    raw_transact.last_liquidity_ind = ELastLiquidityInd::AddedLiquidity;

    raw_transact.block_order_id = "block_id_001";
    raw_transact.order_link_id = "order_link_id_001";
    raw_transact.default_maker_fee_rate_e8 = 78;
    raw_transact.default_taker_fee_rate_e8 = 79;

    draft_pkg->spot_related_fills_.push_back(trade);

    draft_pkg->GetOrAttachSettleCoinWallet(raw_transact.settle_coin);
    draft_pkg->GetOrAttachSettleCoinWallet(raw_transact.base_coin);

    int32_t const ret3 =
        biz::WalletServiceFactory::BuildService()->ProcessTradingResult(draft_pkg.get(), contract_type);
    ASSERT_EQ(ret3, 0);
    ASSERT_EQ(draft_pkg->ref_spot_trans_logs_.size(), 2);
    auto const& usd_wallet = *draft_pkg->GetOrAttachUnifyWallet()->LazyDraftW();
    ASSERT_EQ(usd_wallet.im_ratio, 100);
    ASSERT_EQ(usd_wallet.mm_ratio, 100);
    ASSERT_EQ(usd_wallet.liability.StringFixed(18), "0.005496571080000003");  // fixme: decimal版本尾数是 0002
  }

  {
    biz::CleanSpotStepCalculation::CleanSpotStep(draft_pkg.get());

    // ASSERT_EQ(ret4, 0);
    ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0);
    ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->liability.ToDouble(), 0);
    auto const& usd_wallet = *draft_pkg->GetOrAttachUnifyWallet()->LazyDraftW();
    ASSERT_EQ(usd_wallet.mm_ratio, 0);
    ASSERT_EQ(usd_wallet.im_ratio, 0);
    ASSERT_DOUBLE_EQ(usd_wallet.liability.ToDouble(), 0);
    ASSERT_DOUBLE_EQ(usd_wallet.wallet_balance.ToDouble(), 0);  // 19位负债被偿还
    auto spot_clear_context = draft_pkg->CreateOrAttachSpotClearContext();
    std::cout << ret << "spot clear test done" << std::endl;
  }
}

// 有负债无frozen，还债(刚兑场景2：2个负债，无正资产刚兑)
TEST_F(WalletTest, RigidRepayWithoutPositiveAsset_spot_step) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto deposit_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto deposit_req = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(deposit_request_dto);
  auto* deposit_req_header = deposit_req->mutable_req_header();
  deposit_req_header->set_req_id("deposit_1001_1");
  deposit_req_header->set_user_id(1001);
  deposit_req_header->set_coin(ECoin::USDT);
  deposit_req_header->set_action(EAction::Deposit);
  auto* deposit_body = deposit_req->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("1000");
  auto deposit_biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, deposit_req);
  auto deposit_draft_pkg =
      std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = deposit_draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = 1;

  int32_t ret = wallet_service->RunBySingleUser(deposit_biz_event, resource.get());

  ASSERT_EQ(ret, 0);

  auto draft_pkg = std::make_shared<biz::DraftPkg>(deposit_biz_event, header, workerStore.get(), per_user_store.get());

  draft_pkg->u_store->cow_liquidation_ =
      std::make_shared<store::CowLiquidation>(std::make_shared<store::Liquidation>());
  // spot buy
  {
    auto contract_type = EContractType::Spot;
    draft_pkg->spot_related_fills_.clear();
    draft_pkg->ref_spot_trans_logs_.clear();
    // buy
    TransLogSpotTest::MockSpotMatchingResultForBuy(draft_pkg.get());

    int32_t const ret3 =
        biz::WalletServiceFactory::BuildService()->ProcessTradingResult(draft_pkg.get(), contract_type);
    ASSERT_EQ(ret3, 0);
    ASSERT_EQ(draft_pkg->ref_spot_trans_logs_.size(), 2);
  }

  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->wallet_balance.ToDouble(),
                   -1999.5);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->liability.ToDouble(), 1999.5);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->wallet_balance.ToDouble(), -9000);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->liability.ToDouble(), 9000);
  biz::CleanSpotStepCalculation::CleanSpotStep(draft_pkg.get());
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(1)->second->wallet_->Latest()->liability.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->wallet_balance.ToDouble(), 0);
  ASSERT_DOUBLE_EQ(per_user_store->working_coins_.find(5)->second->wallet_->Latest()->liability.ToDouble(), 0);
  std::cout << ret << "spot clear test done" << std::endl;
}
