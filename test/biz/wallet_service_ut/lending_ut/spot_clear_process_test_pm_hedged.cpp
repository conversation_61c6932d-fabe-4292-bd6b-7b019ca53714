//
// Created by HK00119ML on 3/10/2023.
//

#include <gtest/gtest.h>

#include <memory>
#include <valarray>

#include "biz_worker/service/wallet/biz/lending/liability_settlement_service.hpp"
#include "data/type/biz_type.hpp"
#include "test/biz/wallet_service_ut/lending_ut/spot_clear_process_test.hpp"

// PM模式下开启现货对冲，但没有正资产或负债拿来做对冲，走普通还债模式
// 正资产:
// - BTC:  wb: 1,     hedged balance: 0, clearing order: 2
// - USDC: wb: 1000,  hedged balance: 0, clearing order: 4
// 负债:
// - USDT: wb: -28400,hedged balance: 0, clearing order: 1
TEST_F(WalletTest, SpotClearPmWithNoHedgedSpot) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();
  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(header->uid) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(event, header, workerStore.get(), per_user_store.get());
  auto account = const_cast<store::Account*>(draft_pkg->GetOrAttachAccountInfo()->Latest());
  account->account_status = 1;
  auto user_setting = draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  user_setting->open_pm_spot_hedge = true;
  user_setting->account_mode = EAccountMode::Portfolio;

  auto& usdc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->Latest());
  usdc_wallet.wallet_balance = bbase::decimal::Decimal("1000");
  usdc_wallet.equity = bbase::decimal::Decimal("1000");

  auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
  usdt_wallet.wallet_balance = bbase::decimal::Decimal("-28200");
  usdt_wallet.liability = bbase::decimal::Decimal("28200");

  auto& btc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest());
  btc_wallet.wallet_balance = bbase::decimal::Decimal("1");

  auto ret = biz::LiabilitySettlementService::Process(draft_pkg.get());
  ASSERT_EQ(ret, 0);

  auto spot_clear_context = draft_pkg->CreateOrAttachSpotClearContext();
  ASSERT_NE(spot_clear_context, nullptr);
  auto btc_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::BTC);
  auto usdc_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDC);
  auto usdt_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDT);
  auto expected_usable_usd_value = btc_exchange_index.Mul(1) + usdc_exchange_index.Mul(1000);
  ASSERT_EQ(spot_clear_context.get()->usable_positive_usd_value.ToDouble(), expected_usable_usd_value.ToDouble());
  auto expected_liability_usd_value = usdt_exchange_index.Mul(28200);
  ASSERT_EQ(spot_clear_context->target_liability_usd_value.ToDouble(), expected_liability_usd_value.ToDouble());
  ASSERT_EQ(spot_clear_context->exchange_records_.size(), 2);
  ASSERT_EQ(spot_clear_context->liability_asset_.size(), 1);
  ASSERT_EQ(spot_clear_context->positive_asset_.size(), 2);
  auto btc_positive_asset = spot_clear_context->positive_asset_[0];
  ASSERT_EQ(btc_positive_asset->coin_, ECoin::BTC);
  ASSERT_DOUBLE_EQ(btc_positive_asset->asset_balance_.ToDouble(), 0);
  auto usdc_positive_asset = spot_clear_context->positive_asset_[1];
  ASSERT_EQ(usdc_positive_asset->coin_, ECoin::USDC);
  ASSERT_DOUBLE_EQ(usdc_positive_asset->asset_balance_.ToDouble(), 204.85055165496487);

  auto exchange_order_1 = spot_clear_context->exchange_records_[0];
  ASSERT_EQ(exchange_order_1->sell_coin_id_, ECoin::BTC);
  ASSERT_EQ(exchange_order_1->buy_coin_id_, ECoin::USDT);
  ASSERT_DOUBLE_EQ(exchange_order_1->sell_money_.ToDouble(), 1);
  ASSERT_DOUBLE_EQ(exchange_order_1->buy_money_.ToDouble(), 27423.556835321542);
  ASSERT_DOUBLE_EQ(exchange_order_1->exchange_price_.ToDouble(), 0.000035750000000000002);
  ASSERT_DOUBLE_EQ(exchange_order_1->fee_rate_.ToDouble(), 0.02);
  ASSERT_DOUBLE_EQ(exchange_order_1->fee_money_.ToDouble(), 0.019607843137254905);

  auto exchange_order_2 = spot_clear_context->exchange_records_[1];
  ASSERT_EQ(exchange_order_2->sell_coin_id_, ECoin::USDC);
  ASSERT_EQ(exchange_order_2->buy_coin_id_, ECoin::USDT);
  ASSERT_DOUBLE_EQ(exchange_order_2->sell_money_.ToDouble(), 795.14944834503513);
  ASSERT_DOUBLE_EQ(exchange_order_2->buy_money_.ToDouble(), 776.44316467845886);
  ASSERT_DOUBLE_EQ(exchange_order_2->exchange_price_.ToDouble(), 1.0040120361083249);
  ASSERT_DOUBLE_EQ(exchange_order_2->fee_rate_.ToDouble(), 0.02);
  ASSERT_DOUBLE_EQ(exchange_order_2->fee_money_.ToDouble(), 15.591165653824216);
}

// PM模式下开启现货对冲，正资产被拿来做现货对冲, 正资产剔除对冲部分, 可用为0，无法正常偿还返回失败
// 正资产:
// - BTC:  wb: 1,  hedged balance: 1, clearing order: 2
// - USDC: wb: 1000, hedged balance: 0, clearing order: 4
// 负债:
// - USDT: wb: -2000,hedged balance: 0, clearing order: 1
// 执行结果：无法正常偿还
TEST_F(WalletTest, SpotClearPositiveHedgedSpot4) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();
  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(header->uid) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(event, header, workerStore.get(), per_user_store.get());
  auto account = const_cast<store::Account*>(draft_pkg->GetOrAttachAccountInfo()->Latest());
  account->account_status = 1;
  auto user_setting = draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  user_setting->open_pm_spot_hedge = true;
  user_setting->account_mode = EAccountMode::Portfolio;

  auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
  usdt_wallet.wallet_balance = bbase::decimal::Decimal("-2000");
  usdt_wallet.liability = bbase::decimal::Decimal("2000");

  auto& btc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest());
  btc_wallet.wallet_balance = bbase::decimal::Decimal("1");

  auto cow_pm_risk_unit = draft_pkg->CreateOrAttachPmRiskUnit(biz::coin_name_t("BTC"));
  auto draft_pm_risk_unit = cow_pm_risk_unit->LazyDraftPRU();
  draft_pkg->SetUserRecalculateInited();
  for (auto& pm_calc_unit : draft_pm_risk_unit->pm_calc_unit_array) {
    for (std::size_t price_idx = 0; price_idx < 11; ++price_idx) {
      pm_calc_unit.total_future_loss_matrix_in_usd[price_idx] = -560;
    }
  }

  auto ret = biz::LiabilitySettlementService::Process(draft_pkg.get());
  ASSERT_EQ(ret, 0);

  auto spot_clear_context = draft_pkg->CreateOrAttachSpotClearContext();
  ASSERT_NE(spot_clear_context, nullptr);
  auto btc_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::BTC);
  /* auto usdc_exchange_index = */ draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDC);
  auto usdt_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDT);
  auto expected_usable_usd_value = btc_exchange_index.Mul(0);
  ASSERT_EQ(spot_clear_context.get()->usable_positive_usd_value.ToDouble(), expected_usable_usd_value.ToDouble());
  auto expected_liability_usd_value = usdt_exchange_index.Mul(2000);
  ASSERT_EQ(spot_clear_context->target_liability_usd_value.ToDouble(), expected_liability_usd_value.ToDouble());
  ASSERT_EQ(spot_clear_context->exchange_records_.size(), 0);
  ASSERT_EQ(spot_clear_context->liability_asset_.size(), 1);
  ASSERT_EQ(spot_clear_context->positive_asset_.size(), 0);
  ASSERT_EQ(draft_pkg->ref_asset_trans_logs_.size(), 0);
}

// PM模式下开启现货对冲，正资产被拿来做现货对冲, 正资产剔除对冲部分，足以偿还负债，并且正常偿还
// 正资产:
// - BTC:  wb: 1.5,  hedged balance: 1, clearing order: 2
// - USDC: wb: 1000, hedged balance: 0, clearing order: 4
// 负债:
// - USDT: wb: -2000,hedged balance: 0, clearing order: 1
//
// 执行结果：卖出 0.072929999999999995 个BTC， 买入2000个USDT
TEST_F(WalletTest, SpotClearPositiveHedgedSpot) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();
  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(header->uid) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(event, header, workerStore.get(), per_user_store.get());
  auto account = const_cast<store::Account*>(draft_pkg->GetOrAttachAccountInfo()->Latest());
  account->account_status = 1;
  auto user_setting = draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  user_setting->open_pm_spot_hedge = true;
  user_setting->account_mode = EAccountMode::Portfolio;

  auto& usdc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->Latest());
  usdc_wallet.wallet_balance = bbase::decimal::Decimal("1000");
  usdc_wallet.equity = bbase::decimal::Decimal("1000");

  auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
  usdt_wallet.wallet_balance = bbase::decimal::Decimal("-2000");
  usdt_wallet.liability = bbase::decimal::Decimal("2000");

  auto& btc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest());
  btc_wallet.wallet_balance = bbase::decimal::Decimal("1.5");

  auto cow_pm_risk_unit = draft_pkg->CreateOrAttachPmRiskUnit(biz::coin_name_t("BTC"));
  auto draft_pm_risk_unit = cow_pm_risk_unit->LazyDraftPRU();
  draft_pkg->SetUserRecalculateInited();
  for (auto& pm_calc_unit : draft_pm_risk_unit->pm_calc_unit_array) {
    for (std::size_t price_idx = 0; price_idx < 11; ++price_idx) {
      pm_calc_unit.total_future_loss_matrix_in_usd[price_idx] = -560;
    }
  }

  auto ret = biz::LiabilitySettlementService::Process(draft_pkg.get());
  ASSERT_EQ(ret, 0);

  auto spot_clear_context = draft_pkg->CreateOrAttachSpotClearContext();
  ASSERT_NE(spot_clear_context, nullptr);
  auto btc_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::BTC);
  auto usdc_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDC);
  auto usdt_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDT);
  auto expected_usable_usd_value = btc_exchange_index.Mul(0.5) + usdc_exchange_index.Mul(1000);
  ASSERT_LE(
      std::abs(spot_clear_context.get()->usable_positive_usd_value.ToDouble() - expected_usable_usd_value.ToDouble()),
      0.001);
  auto expected_liability_usd_value = usdt_exchange_index.Mul(2000);
  ASSERT_EQ(spot_clear_context->target_liability_usd_value.ToDouble(), expected_liability_usd_value.ToDouble());
  ASSERT_EQ(spot_clear_context->exchange_records_.size(), 1);
  ASSERT_EQ(spot_clear_context->liability_asset_.size(), 1);
  ASSERT_EQ(spot_clear_context->positive_asset_.size(), 2);
  auto btc_positive_asset = spot_clear_context->positive_asset_[0];
  ASSERT_EQ(btc_positive_asset->coin_, ECoin::BTC);
  ASSERT_DOUBLE_EQ(btc_positive_asset->asset_balance_.ToDouble(), 0.42706999000000001);
  auto usdt_positive_asset = spot_clear_context->positive_asset_[1];
  ASSERT_EQ(usdt_positive_asset->coin_, ECoin::USDC);
  ASSERT_DOUBLE_EQ(usdt_positive_asset->asset_balance_.ToDouble(), 1000);
  auto exchange_order = spot_clear_context->exchange_records_[0];
  ASSERT_EQ(exchange_order->sell_coin_id_, ECoin::BTC);
  ASSERT_EQ(exchange_order->buy_coin_id_, ECoin::USDT);
  ASSERT_DOUBLE_EQ(exchange_order->sell_money_.ToDouble(), 0.072929999999999995);
  ASSERT_DOUBLE_EQ(exchange_order->buy_money_.ToDouble(), 2000);
  ASSERT_DOUBLE_EQ(exchange_order->exchange_price_.ToDouble(), 0.000035750000000000002);
  ASSERT_DOUBLE_EQ(exchange_order->fee_rate_.ToDouble(), 0.02);
  ASSERT_DOUBLE_EQ(exchange_order->fee_money_.ToDouble(), 0.0014300000000000001);
}

// PM模式下开启现货对冲，正资产被拿来做现货对冲,
// 正资产剔除对冲部分，足以偿还负债，并且正常偿还(因为正资产存在对冲部分，所以需要两个正资产一起偿还) 正资产:
// - BTC:  wb: 1.5,  hedged balance: 1, clearing order: 2
// - USDC: wb: 1000, hedged balance: 0, clearing order: 4
// 负债:
// - USDT: wb: -14500,hedged balance: 0, clearing order: 1
//
// 执行结果：卖出 0.5 个BTC, 807.21191个USDC，买入14500个USDT
TEST_F(WalletTest, SpotClearPositiveHedgedSpot2) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();
  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(header->uid) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(event, header, workerStore.get(), per_user_store.get());
  auto account = const_cast<store::Account*>(draft_pkg->GetOrAttachAccountInfo()->Latest());
  account->account_status = 1;
  auto user_setting = draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  user_setting->open_pm_spot_hedge = true;
  user_setting->account_mode = EAccountMode::Portfolio;

  auto& usdc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->Latest());
  usdc_wallet.wallet_balance = bbase::decimal::Decimal("1000");
  usdc_wallet.equity = bbase::decimal::Decimal("1000");

  auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
  usdt_wallet.wallet_balance = bbase::decimal::Decimal("-14500");
  usdt_wallet.liability = bbase::decimal::Decimal("14500");

  auto& btc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest());
  btc_wallet.wallet_balance = bbase::decimal::Decimal("1.5");

  auto cow_pm_risk_unit = draft_pkg->CreateOrAttachPmRiskUnit(biz::coin_name_t("BTC"));
  auto draft_pm_risk_unit = cow_pm_risk_unit->LazyDraftPRU();
  draft_pkg->SetUserRecalculateInited();
  for (auto& pm_calc_unit : draft_pm_risk_unit->pm_calc_unit_array) {
    for (std::size_t price_idx = 0; price_idx < 11; ++price_idx) {
      pm_calc_unit.total_future_loss_matrix_in_usd[price_idx] = -560;
    }
  }

  auto ret = biz::LiabilitySettlementService::Process(draft_pkg.get());
  ASSERT_EQ(ret, 0);

  auto spot_clear_context = draft_pkg->CreateOrAttachSpotClearContext();
  ASSERT_NE(spot_clear_context, nullptr);
  auto btc_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::BTC);
  auto usdc_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDC);
  auto usdt_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDT);
  auto expected_usable_usd_value = btc_exchange_index.Mul(0.5) + usdc_exchange_index.Mul(1000);
  ASSERT_LE(
      spot_clear_context.get()->usable_positive_usd_value.Sub(expected_usable_usd_value.ToDouble()).Abs().ToDouble(),
      0.001);
  auto expected_liability_usd_value = usdt_exchange_index.Mul(14500);
  ASSERT_EQ(spot_clear_context->target_liability_usd_value.ToDouble(), expected_liability_usd_value.ToDouble());
  ASSERT_EQ(spot_clear_context->exchange_records_.size(), 2);
  ASSERT_EQ(spot_clear_context->liability_asset_.size(), 1);
  ASSERT_EQ(spot_clear_context->positive_asset_.size(), 2);
  auto btc_positive_asset = spot_clear_context->positive_asset_[0];
  ASSERT_EQ(btc_positive_asset->coin_, ECoin::BTC);
  ASSERT_DOUBLE_EQ(btc_positive_asset->asset_balance_.ToDouble(), 0);
  auto usdc_positive_asset = spot_clear_context->positive_asset_[1];
  ASSERT_EQ(usdc_positive_asset->coin_, ECoin::USDC);
  ASSERT_DOUBLE_EQ(usdc_positive_asset->asset_balance_.ToDouble(), 192.78808425275827);
  auto exchange_order_1 = spot_clear_context->exchange_records_[0];

  ASSERT_EQ(exchange_order_1->sell_coin_id_, ECoin::BTC);
  ASSERT_EQ(exchange_order_1->buy_coin_id_, ECoin::USDT);
  ASSERT_DOUBLE_EQ(exchange_order_1->sell_money_.ToDouble(), 0.49999999000000001);
  ASSERT_DOUBLE_EQ(exchange_order_1->buy_money_.ToDouble(), 13711.778143425203);
  ASSERT_DOUBLE_EQ(exchange_order_1->exchange_price_.ToDouble(), 0.000035750000000000002);
  ASSERT_DOUBLE_EQ(exchange_order_1->fee_rate_.ToDouble(), 0.02);
  ASSERT_DOUBLE_EQ(exchange_order_1->fee_money_.ToDouble(), 0.0098039213725490196);
  auto exchange_order_2 = spot_clear_context->exchange_records_[1];
  ASSERT_EQ(exchange_order_2->sell_coin_id_, ECoin::USDC);
  ASSERT_EQ(exchange_order_2->buy_coin_id_, ECoin::USDT);
  ASSERT_DOUBLE_EQ(exchange_order_2->sell_money_.ToDouble(), 807.21191574724173);
  ASSERT_DOUBLE_EQ(exchange_order_2->buy_money_.ToDouble(), 788.22185657479781);
  ASSERT_DOUBLE_EQ(exchange_order_2->exchange_price_.ToDouble(), 1.0040120361083249);
  ASSERT_DOUBLE_EQ(exchange_order_2->fee_rate_.ToDouble(), 0.02);
  ASSERT_DOUBLE_EQ(exchange_order_2->fee_money_.ToDouble(), 15.827684622494935);
}

// PM模式下开启现货对冲，部分负债被拿来做现货对冲, 对冲的负债在普通还币中被保留
// 正资产:
// - USDT: wb: 2000, hedged balance: 0,    clearing order: 1
// - USDC: wb: 1000, hedged balance: 0,    clearing order: 4
// 负债:
// - BTC:  wb: -0.5, hedged balance: -0.4, clearing order: 2
//
// 执行结果: 对冲负债被保留，卖出2000个USDT，857.37291个USDC， 买入0.1个BTC
TEST_F(WalletTest, SpotClearNegativeHedgedSpot) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();
  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(header->uid) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(event, header, workerStore.get(), per_user_store.get());
  auto account = const_cast<store::Account*>(draft_pkg->GetOrAttachAccountInfo()->Latest());
  account->account_status = 1;
  auto user_setting = draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  user_setting->open_pm_spot_hedge = true;
  user_setting->account_mode = EAccountMode::Portfolio;

  auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
  usdt_wallet.wallet_balance = bbase::decimal::Decimal("2000");
  usdt_wallet.equity = bbase::decimal::Decimal("2000");

  auto& usdc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->Latest());
  usdc_wallet.wallet_balance = bbase::decimal::Decimal("1000");
  usdc_wallet.equity = bbase::decimal::Decimal("1000");

  auto& btc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest());
  btc_wallet.wallet_balance = bbase::decimal::Decimal("-0.5");
  btc_wallet.liability = bbase::decimal::Decimal("0.5");

  auto cow_pm_risk_unit = draft_pkg->CreateOrAttachPmRiskUnit(biz::coin_name_t("BTC"));
  auto draft_pm_risk_unit = cow_pm_risk_unit->LazyDraftPRU();
  draft_pkg->SetUserRecalculateInited();
  for (auto& pm_calc_unit : draft_pm_risk_unit->pm_calc_unit_array) {
    for (std::size_t price_idx = 0; price_idx < 11; ++price_idx) {
      pm_calc_unit.total_future_loss_matrix_in_usd[price_idx] = -224;
    }
  }

  auto ret = biz::LiabilitySettlementService::Process(draft_pkg.get());
  ASSERT_EQ(ret, 0);
  auto spot_clear_context = draft_pkg->CreateOrAttachSpotClearContext();
  ASSERT_NE(spot_clear_context, nullptr);
  auto btc_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::BTC);
  auto usdc_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDC);
  auto usdt_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDT);
  auto expected_usable_usd_value = usdt_exchange_index.Mul(2000) + usdc_exchange_index.Mul(1000);
  ASSERT_EQ(spot_clear_context.get()->usable_positive_usd_value.ToDouble(), expected_usable_usd_value.ToDouble());

  auto expected_liability_usd_value = btc_exchange_index.Mul(0.1);
  EXPECT_LE(spot_clear_context->target_liability_usd_value.Sub(expected_liability_usd_value).Abs().ToDouble(), 0.001);
  ASSERT_EQ(spot_clear_context->exchange_records_.size(), 2);
  ASSERT_EQ(spot_clear_context->liability_asset_.size(), 1);
  ASSERT_EQ(spot_clear_context->positive_asset_.size(), 2);

  auto usdt_positive_asset = spot_clear_context->positive_asset_[0];
  ASSERT_EQ(usdt_positive_asset->coin_, ECoin::USDT);
  ASSERT_DOUBLE_EQ(usdt_positive_asset->asset_balance_.ToDouble(), 0);
  auto usdc_positive_asset = spot_clear_context->positive_asset_[1];
  ASSERT_EQ(usdc_positive_asset->coin_, ECoin::USDC);
  ASSERT_DOUBLE_EQ(usdc_positive_asset->asset_balance_.ToDouble(), 143.43057733199598);
  auto exchange_order_1 = spot_clear_context->exchange_records_[0];
  ASSERT_EQ(exchange_order_1->sell_coin_id_, ECoin::USDT);
  ASSERT_EQ(exchange_order_1->buy_coin_id_, ECoin::BTC);
  ASSERT_DOUBLE_EQ(exchange_order_1->sell_money_.ToDouble(), 2000);
  ASSERT_DOUBLE_EQ(exchange_order_1->buy_money_.ToDouble(), 0.070098039215686275);
  ASSERT_DOUBLE_EQ(exchange_order_1->exchange_price_.ToDouble(), 27972.027972027972);
  ASSERT_DOUBLE_EQ(exchange_order_1->fee_rate_.ToDouble(), 0.02);
  ASSERT_DOUBLE_EQ(exchange_order_1->fee_money_.ToDouble(), 39.215686274509807);
  auto exchange_order_2 = spot_clear_context->exchange_records_[1];
  ASSERT_EQ(exchange_order_2->sell_coin_id_, ECoin::USDC);
  ASSERT_EQ(exchange_order_2->buy_coin_id_, ECoin::BTC);
  ASSERT_DOUBLE_EQ(exchange_order_2->sell_money_.ToDouble(), 856.56942266800411);
  ASSERT_DOUBLE_EQ(exchange_order_2->buy_money_.ToDouble(), 0.029901950784313729);
  ASSERT_DOUBLE_EQ(exchange_order_2->exchange_price_.ToDouble(), 28084.252758274826);
  ASSERT_DOUBLE_EQ(exchange_order_2->fee_rate_.ToDouble(), 0.02);
  ASSERT_DOUBLE_EQ(exchange_order_2->fee_money_.ToDouble(), 16.795478875843216);
  btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
  ASSERT_DOUBLE_EQ(btc_wallet.wallet_balance.ToDouble(), -0.40000001000000002);
  ASSERT_DOUBLE_EQ(btc_wallet.liability.ToDouble(), 0.40000001000000002);
}

// PM模式下开启现货对冲，部分负债被拿来做现货对冲, 对冲的负债在普通还币中被保留
// 正资产:
// - USDT: wb: 5000, hedged balance: 0,    clearing order: 1
// 负债:
// - BTC:  wb: -0.5, hedged balance: -0.4, clearing order: 2
// - ETH:  wb: -5,   hedged balance: -4,   clearing order: 3
// 执行结果: 对冲负债被保留，卖出2853.1468531468531个USDT，买入0.1个BTC, 卖出 1630.3696303696304个USDC，买入1个ETH
TEST_F(WalletTest, SpotClearNegativeHedgedSpot2) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();
  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(header->uid) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(event, header, workerStore.get(), per_user_store.get());
  auto account = const_cast<store::Account*>(draft_pkg->GetOrAttachAccountInfo()->Latest());
  account->account_status = 1;
  auto user_setting = draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  user_setting->open_pm_spot_hedge = true;
  user_setting->account_mode = EAccountMode::Portfolio;

  auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
  usdt_wallet.wallet_balance = bbase::decimal::Decimal("5000");
  usdt_wallet.equity = bbase::decimal::Decimal("5000");

  auto& btc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest());
  btc_wallet.wallet_balance = bbase::decimal::Decimal("-0.5");
  btc_wallet.liability = bbase::decimal::Decimal("0.5");
  auto btc_cow_pm_risk_unit = draft_pkg->CreateOrAttachPmRiskUnit(biz::coin_name_t("BTC"));
  auto btc_draft_pm_risk_unit = btc_cow_pm_risk_unit->LazyDraftPRU();
  draft_pkg->SetUserRecalculateInited();
  for (auto& pm_calc_unit : btc_draft_pm_risk_unit->pm_calc_unit_array) {
    for (std::size_t price_idx = 0; price_idx < 11; ++price_idx) {
      pm_calc_unit.total_future_loss_matrix_in_usd[price_idx] = -224;
    }
  }

  auto& eth_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::ETH)->Latest());
  eth_wallet.wallet_balance = bbase::decimal::Decimal("-5");
  eth_wallet.liability = bbase::decimal::Decimal("5");
  auto eth_cow_pm_risk_unit = draft_pkg->CreateOrAttachPmRiskUnit(biz::coin_name_t("ETH"));
  auto eth_draft_pm_risk_unit = eth_cow_pm_risk_unit->LazyDraftPRU();
  draft_pkg->SetUserRecalculateInited();
  for (auto& pm_calc_unit : eth_draft_pm_risk_unit->pm_calc_unit_array) {
    for (std::size_t price_idx = 0; price_idx < 11; ++price_idx) {
      pm_calc_unit.total_future_loss_matrix_in_usd[price_idx] = -128;
    }
  }

  auto ret = biz::LiabilitySettlementService::Process(draft_pkg.get());
  ASSERT_EQ(ret, 0);
  auto spot_clear_context = draft_pkg->CreateOrAttachSpotClearContext();
  ASSERT_NE(spot_clear_context, nullptr);
  auto btc_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::BTC);
  /* auto usdc_exchange_index = */ draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDC);
  auto usdt_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDT);
  auto eth_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::ETH);
  auto expected_usable_usd_value = usdt_exchange_index.Mul(5000);
  ASSERT_EQ(spot_clear_context.get()->usable_positive_usd_value.ToDouble(), expected_usable_usd_value.ToDouble());

  auto expected_liability_usd_value = btc_exchange_index.Mul(0.1) + eth_exchange_index.Mul(1);
  ASSERT_LE(spot_clear_context->target_liability_usd_value.Sub(expected_liability_usd_value).Abs().ToDouble(), 0.001);
  ASSERT_EQ(spot_clear_context->exchange_records_.size(), 2);
  ASSERT_EQ(spot_clear_context->liability_asset_.size(), 2);
  ASSERT_EQ(spot_clear_context->positive_asset_.size(), 1);

  auto usdt_positive_asset = spot_clear_context->positive_asset_[0];
  ASSERT_EQ(usdt_positive_asset->coin_, ECoin::USDT);
  EXPECT_DOUBLE_EQ(usdt_positive_asset->asset_balance_.ToDouble(), 516.48381810189812);

  auto btc_liability_asset = spot_clear_context->liability_asset_[0];
  ASSERT_EQ(btc_liability_asset->coin_, ECoin::BTC);
  ASSERT_DOUBLE_EQ(btc_liability_asset->asset_balance_.ToDouble(), 0);
  btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
  EXPECT_DOUBLE_EQ(btc_wallet.wallet_balance.ToDouble(), -0.40000001000000002);
  EXPECT_DOUBLE_EQ(btc_wallet.liability.ToDouble(), 0.40000001000000002);

  auto eth_liability_asset = spot_clear_context->liability_asset_[1];
  ASSERT_EQ(eth_liability_asset->coin_, ECoin::ETH);
  ASSERT_DOUBLE_EQ(eth_liability_asset->asset_balance_.ToDouble(), 0);
  eth_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::ETH)->Latest();
  EXPECT_DOUBLE_EQ(eth_wallet.wallet_balance.ToDouble(), -4.0000000099999999);
  EXPECT_DOUBLE_EQ(eth_wallet.liability.ToDouble(), 4.0000000099999999);

  auto exchange_order_1 = spot_clear_context->exchange_records_[0];
  ASSERT_EQ(exchange_order_1->sell_coin_id_, ECoin::USDT);
  ASSERT_EQ(exchange_order_1->buy_coin_id_, ECoin::BTC);
  EXPECT_DOUBLE_EQ(exchange_order_1->sell_money_.ToDouble(), 2853.1465678321679);
  EXPECT_DOUBLE_EQ(exchange_order_1->buy_money_.ToDouble(), 0.099999989999999997);
  EXPECT_DOUBLE_EQ(exchange_order_1->exchange_price_.ToDouble(), 27972.027972027972);
  EXPECT_DOUBLE_EQ(exchange_order_1->fee_rate_.ToDouble(), 0.02);
  EXPECT_DOUBLE_EQ(exchange_order_1->fee_money_.ToDouble(), 55.944050349650347);
  auto exchange_order_2 = spot_clear_context->exchange_records_[1];
  ASSERT_EQ(exchange_order_2->sell_coin_id_, ECoin::USDT);
  ASSERT_EQ(exchange_order_2->buy_coin_id_, ECoin::ETH);
  EXPECT_DOUBLE_EQ(exchange_order_2->sell_money_.ToDouble(), 1630.369614065934);
  EXPECT_DOUBLE_EQ(exchange_order_2->buy_money_.ToDouble(), 0.99999998999999995);
  EXPECT_DOUBLE_EQ(exchange_order_2->exchange_price_.ToDouble(), 1598.4015984015984);
  EXPECT_DOUBLE_EQ(exchange_order_2->fee_rate_.ToDouble(), 0.02);
  EXPECT_DOUBLE_EQ(exchange_order_2->fee_money_.ToDouble(), 31.968031648351648);
}

// PM模式下开启现货对冲，正资产被拿来做现货对冲,
// 正资产剔除对冲部分，足以偿还负债，并且正常偿还。最终兑币会将对冲部分拿来偿还:
// - BTC:  wb: 1.5,  hedged balance: 1, clearing order: 2
// - USDC: wb: 1000, hedged balance: 0, clearing order: 4
// 负债:
// - USDT: wb: -14500,hedged balance: 0, clearing order: 1
//
// 执行结果：卖出 0.5287425 个BTC(包含对冲部分), 买入14500个USDT
TEST_F(WalletTest, SpotClearPositiveHedgedSpot3) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();
  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(header->uid) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(event, header, workerStore.get(), per_user_store.get());
  auto account = const_cast<store::Account*>(draft_pkg->GetOrAttachAccountInfo()->Latest());
  account->account_status = 1;
  auto user_setting = draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  user_setting->open_pm_spot_hedge = true;
  user_setting->account_mode = EAccountMode::Portfolio;

  auto& usdc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->Latest());
  usdc_wallet.wallet_balance = bbase::decimal::Decimal("1000");
  usdc_wallet.equity = bbase::decimal::Decimal("1000");

  auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
  usdt_wallet.wallet_balance = bbase::decimal::Decimal("-14500");
  usdt_wallet.liability = bbase::decimal::Decimal("14500");

  auto& btc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest());
  btc_wallet.wallet_balance = bbase::decimal::Decimal("1.5");

  auto cow_pm_risk_unit = draft_pkg->CreateOrAttachPmRiskUnit(biz::coin_name_t("BTC"));
  auto draft_pm_risk_unit = cow_pm_risk_unit->LazyDraftPRU();
  draft_pkg->SetUserRecalculateInited();
  for (auto& pm_calc_unit : draft_pm_risk_unit->pm_calc_unit_array) {
    for (std::size_t price_idx = 0; price_idx < 11; ++price_idx) {
      pm_calc_unit.total_future_loss_matrix_in_usd[price_idx] = -560;
    }
  }

  auto ret = biz::LiabilitySettlementService::Process(draft_pkg.get(), true);
  ASSERT_EQ(ret, 0);

  auto spot_clear_context = draft_pkg->CreateOrAttachSpotClearContext();
  ASSERT_NE(spot_clear_context, nullptr);
  auto btc_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::BTC);
  auto usdc_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDC);
  auto usdt_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDT);
  auto expected_usable_usd_value = btc_exchange_index.Mul(1.5) + usdc_exchange_index.Mul(1000);
  ASSERT_EQ(spot_clear_context.get()->usable_positive_usd_value.ToDouble(), expected_usable_usd_value.ToDouble());
  auto expected_liability_usd_value = usdt_exchange_index.Mul(14500);
  ASSERT_EQ(spot_clear_context->target_liability_usd_value.ToDouble(), expected_liability_usd_value.ToDouble());
  ASSERT_EQ(spot_clear_context->exchange_records_.size(), 1);
  ASSERT_EQ(spot_clear_context->liability_asset_.size(), 1);
  ASSERT_EQ(spot_clear_context->positive_asset_.size(), 2);
  auto btc_positive_asset = spot_clear_context->positive_asset_[0];
  ASSERT_EQ(btc_positive_asset->coin_, ECoin::BTC);
  ASSERT_DOUBLE_EQ(btc_positive_asset->asset_balance_.ToDouble(), 0.97125749999999999);
  auto usdc_positive_asset = spot_clear_context->positive_asset_[1];
  ASSERT_EQ(usdc_positive_asset->coin_, ECoin::USDC);
  ASSERT_DOUBLE_EQ(usdc_positive_asset->asset_balance_.ToDouble(), 1000);
  auto exchange_order_1 = spot_clear_context->exchange_records_[0];

  ASSERT_EQ(exchange_order_1->sell_coin_id_, ECoin::BTC);
  ASSERT_EQ(exchange_order_1->buy_coin_id_, ECoin::USDT);
  ASSERT_DOUBLE_EQ(exchange_order_1->sell_money_.ToDouble(), 0.5287425);
  ASSERT_DOUBLE_EQ(exchange_order_1->buy_money_.ToDouble(), 14500);
  ASSERT_DOUBLE_EQ(exchange_order_1->exchange_price_.ToDouble(), 0.000035750000000000002);
  ASSERT_DOUBLE_EQ(exchange_order_1->fee_rate_.ToDouble(), 0.02);
  ASSERT_DOUBLE_EQ(exchange_order_1->fee_money_.ToDouble(), 0.0103675);
}

// PM模式下开启现货对冲，正资产，多个负债被拿来做现货对冲, 正资产和负债的对冲部分在兑币中被保留
// 正资产:
// - USDT: wb: 9000, hedged balance: 4000,    clearing order: 1
// - USDC: wb: 1000, hedged balance: 0, clearing order: 4
// 负债:
// - BTC:  wb: -0.5, hedged balance: -0.4, clearing order: 2
// - ETH:  wb: -5,   hedged balance: -4,   clearing order: 3
// 执行结果: 对冲负债被保留，卖出2853.1468531468531个USDT，买入0.1个BTC, 卖出 1630.3696303696304个USDC，买入1个ETH
TEST_F(WalletTest, SpotClearNegativeHedgedSpot4) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();
  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  std::int64_t sharding_key =
      utils::hash(static_cast<std::uint64_t>(header->uid) /
                  static_cast<int64_t>(application::GlobalVarManager::Instance().orig_pre_threads())) %
      application::GlobalConfig::re_calc_total_user_sharding();
  auto event = std::make_shared<event::ReCalcEvent>(sharding_key);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(event, header, workerStore.get(), per_user_store.get());
  auto account = const_cast<store::Account*>(draft_pkg->GetOrAttachAccountInfo()->Latest());
  account->account_status = 1;
  auto user_setting = draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  user_setting->open_pm_spot_hedge = true;
  user_setting->account_mode = EAccountMode::Portfolio;

  auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
  usdt_wallet.wallet_balance = bbase::decimal::Decimal("9000");
  usdt_wallet.equity = bbase::decimal::Decimal("9000");

  auto& usdc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDC)->Latest());
  usdc_wallet.wallet_balance = bbase::decimal::Decimal("1000");
  usdc_wallet.equity = bbase::decimal::Decimal("1000");

  auto usdt_cow_pm_risk_unit = draft_pkg->CreateOrAttachPmRiskUnit(biz::coin_name_t("USDT"));
  auto usdt_draft_pm_risk_unit = usdt_cow_pm_risk_unit->LazyDraftPRU();
  for (auto& pm_calc_unit : usdt_draft_pm_risk_unit->pm_calc_unit_array) {
    for (std::size_t price_idx = 0; price_idx < 11; ++price_idx) {
      pm_calc_unit.total_future_loss_matrix_in_usd[price_idx] = -80.08;
    }
  }

  auto& btc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest());
  btc_wallet.wallet_balance = bbase::decimal::Decimal("-0.5");
  btc_wallet.liability = bbase::decimal::Decimal("0.5");
  auto btc_cow_pm_risk_unit = draft_pkg->CreateOrAttachPmRiskUnit(biz::coin_name_t("BTC"));
  auto btc_draft_pm_risk_unit = btc_cow_pm_risk_unit->LazyDraftPRU();
  for (auto& pm_calc_unit : btc_draft_pm_risk_unit->pm_calc_unit_array) {
    for (std::size_t price_idx = 0; price_idx < 11; ++price_idx) {
      pm_calc_unit.total_future_loss_matrix_in_usd[price_idx] = -224;
    }
  }

  auto& eth_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::ETH)->Latest());
  eth_wallet.wallet_balance = bbase::decimal::Decimal("-5");
  eth_wallet.liability = bbase::decimal::Decimal("5");
  auto eth_cow_pm_risk_unit = draft_pkg->CreateOrAttachPmRiskUnit(biz::coin_name_t("ETH"));
  auto eth_draft_pm_risk_unit = eth_cow_pm_risk_unit->LazyDraftPRU();
  draft_pkg->SetUserRecalculateInited();
  for (auto& pm_calc_unit : eth_draft_pm_risk_unit->pm_calc_unit_array) {
    for (std::size_t price_idx = 0; price_idx < 11; ++price_idx) {
      pm_calc_unit.total_future_loss_matrix_in_usd[price_idx] = -128;
    }
  }

  auto ret = biz::LiabilitySettlementService::Process(draft_pkg.get());
  ASSERT_EQ(ret, 0);
  auto spot_clear_context = draft_pkg->CreateOrAttachSpotClearContext();
  ASSERT_NE(spot_clear_context, nullptr);
  auto btc_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::BTC);
  auto usdc_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDC);
  auto usdt_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::USDT);
  auto eth_exchange_index = draft_pkg->w_store->GetUsdExchangeRateByCoinId(ECoin::ETH);
  auto expected_usable_usd_value = usdt_exchange_index.Mul(5000) + usdc_exchange_index.Mul(1000);
  ASSERT_LE(spot_clear_context.get()->usable_positive_usd_value.Sub(expected_usable_usd_value).Abs().ToDouble(), 0.001);

  auto expected_liability_usd_value = btc_exchange_index.Mul(0.1) + eth_exchange_index.Mul(1);
  ASSERT_LE(spot_clear_context->target_liability_usd_value.Sub(expected_liability_usd_value).Abs().ToDouble(), 0.001);
  ASSERT_EQ(spot_clear_context->exchange_records_.size(), 2);
  ASSERT_EQ(spot_clear_context->liability_asset_.size(), 2);
  ASSERT_EQ(spot_clear_context->positive_asset_.size(), 2);

  auto usdt_positive_asset = spot_clear_context->positive_asset_[0];
  ASSERT_EQ(usdt_positive_asset->coin_, ECoin::USDT);
  EXPECT_DOUBLE_EQ(usdt_positive_asset->asset_balance_.ToDouble(), 516.48381809189812);
  auto usdc_positive_asset = spot_clear_context->positive_asset_[1];
  ASSERT_EQ(usdc_positive_asset->coin_, ECoin::USDC);
  EXPECT_DOUBLE_EQ(usdc_positive_asset->asset_balance_.ToDouble(), 1000);

  auto btc_liability_asset = spot_clear_context->liability_asset_[0];
  ASSERT_EQ(btc_liability_asset->coin_, ECoin::BTC);
  ASSERT_DOUBLE_EQ(btc_liability_asset->asset_balance_.ToDouble(), 0);
  btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
  EXPECT_DOUBLE_EQ(btc_wallet.wallet_balance.ToDouble(), -0.40000001000000002);
  EXPECT_DOUBLE_EQ(btc_wallet.liability.ToDouble(), 0.40000001000000002);

  auto eth_liability_asset = spot_clear_context->liability_asset_[1];
  ASSERT_EQ(eth_liability_asset->coin_, ECoin::ETH);
  ASSERT_DOUBLE_EQ(eth_liability_asset->asset_balance_.ToDouble(), 0);
  eth_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::ETH)->Latest();
  EXPECT_DOUBLE_EQ(eth_wallet.wallet_balance.ToDouble(), -4.0000000099999999);
  EXPECT_DOUBLE_EQ(eth_wallet.liability.ToDouble(), 4.0000000099999999);

  auto exchange_order_1 = spot_clear_context->exchange_records_[0];
  ASSERT_EQ(exchange_order_1->sell_coin_id_, ECoin::USDT);
  ASSERT_EQ(exchange_order_1->buy_coin_id_, ECoin::BTC);
  EXPECT_DOUBLE_EQ(exchange_order_1->sell_money_.ToDouble(), 2853.1465678321679);
  EXPECT_DOUBLE_EQ(exchange_order_1->buy_money_.ToDouble(), 0.099999989999999997);
  EXPECT_DOUBLE_EQ(exchange_order_1->exchange_price_.ToDouble(), 27972.027972027972);
  EXPECT_DOUBLE_EQ(exchange_order_1->fee_rate_.ToDouble(), 0.02);
  EXPECT_DOUBLE_EQ(exchange_order_1->fee_money_.ToDouble(), 55.944050349650347);
  auto exchange_order_2 = spot_clear_context->exchange_records_[1];
  ASSERT_EQ(exchange_order_2->sell_coin_id_, ECoin::USDT);
  ASSERT_EQ(exchange_order_2->buy_coin_id_, ECoin::ETH);
  EXPECT_DOUBLE_EQ(exchange_order_2->sell_money_.ToDouble(), 1630.369614065934);
  EXPECT_DOUBLE_EQ(exchange_order_2->buy_money_.ToDouble(), 0.99999998999999995);
  EXPECT_DOUBLE_EQ(exchange_order_2->exchange_price_.ToDouble(), 1598.4015984015984);
  EXPECT_DOUBLE_EQ(exchange_order_2->fee_rate_.ToDouble(), 0.02);
  EXPECT_DOUBLE_EQ(exchange_order_2->fee_money_.ToDouble(), 31.968031648351648);
}
