//
// Created by SH00728ML on 2023/3/24.
//

#include "test/biz/wallet_service_ut/label_core/label_code_service_test.hpp"

#include <gtest/gtest.h>

#include "biz_worker/service/wallet/biz/account/label_code_service.hpp"
#include "biz_worker/service/wallet/wallet_service.hpp"

TEST_F(WalletTest, LabelCodeService) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  req_header->set_req_id("LabelCodeSyn_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::LabelCodeSyn);
  auto* req_body = request->mutable_settingsreqgroupbody()->mutable_labelcodesyncreq();
  req_body->set_label_code("xxx");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());
  ASSERT_EQ(ret, 10001);

  req_body->set_label_code("MANUAL_LIQ_MODE");
  req_body->set_label_value("true");
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());
  ASSERT_EQ(ret, 10001);

  req_body->set_label_code("MANUAL_LIQ_MODE");
  req_body->set_label_value("false");
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());
  ASSERT_EQ(ret, 10001);

  req_body->set_label_code("NXXX");
  req_body->set_label_value("false");
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());
  ASSERT_EQ(ret, 10001);
}

TEST_F(WalletTest, LabelCodeService_BAN_TRADE_true) {
  const std::shared_ptr<biz::LabelCodeService> service = std::make_shared<biz::LabelCodeService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  per_user_store->user_setting_->cur_setting = (std::make_shared<store::Setting>());

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("label_code_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::LabelCodeSyn);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  auto& req =
      const_cast<svc::unified_v2::req::settings::LabelCodeSyncReq&>(request->settingsreqgroupbody().labelcodesyncreq());

  req.set_label_code("BAN_TRADE");
  req.set_label_value("true");

  EXPECT_FALSE(per_user_store->user_setting_->cur_setting->IsFutureBanTradeOpen());
  EXPECT_FALSE(per_user_store->user_setting_->cur_setting->IsOptionBanTradeOpen());
  EXPECT_FALSE(per_user_store->user_setting_->cur_setting->IsSpotBanTradeAll());

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();

  EXPECT_TRUE(draft_pkg->affected_setting_.need_to_persist);
  EXPECT_TRUE(per_user_store->user_setting_->cur_setting->IsFutureBanTradeOpen());
  EXPECT_TRUE(per_user_store->user_setting_->cur_setting->IsOptionBanTradeOpen());
  EXPECT_TRUE(per_user_store->user_setting_->cur_setting->IsSpotBanTradeAll());

  auto resp = std::make_shared<svc::unified_v2::UnifiedV2ResultDTO>();
  convertor::ToPB::ConvertToUnifiedV2ResultDTO(draft_pkg.get(), resp.get(), EProductType::Margin);
  std::string msg{};
  LOG_INFO("MarginResult, {}", utils::PbMessageToJsonString(*resp, &msg));

  LOG_INFO("LabelCodeService_RM_BAN_LIQUIDATION_true Done");
}

// TEST_F(WalletTest, LabelCodeServiceUpgrading) {
//   auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
//   auto resource = std::make_shared<store::UnicastResource>();
//   resource->per_user_store = per_user_store.get();
//   resource->config_manager = config::getTlsCfgMgrRaw();
//   resource->per_worker_store = workerStore.get();
//
//   auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
//   auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
//   auto *req_header = request->mutable_req_header();
//   store::Header header{.uid = 1001};
//   resource->header = &header;
//   req_header->set_req_id("LabelCodeSyn_1001_1");
//   req_header->set_user_id(1001);
//   req_header->set_coin(ECoin::USDT);
//   req_header->set_action(EAction::LabelCodeSyn);
//   auto *req_body = request->mutable_settingsreqgroupbody()->mutable_labelcodesyncreq();
//   req_body->set_label_code("xxx");
//
//   auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
//       1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
//       event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
//   auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
//
//   auto app_impl = std::make_shared<application::App>();
//   bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, app_impl);
//   app_impl->io_thread_.Start();
//
//   req_body->set_label_code("UPGRADING");
//   req_body->set_label_value("false");
//   auto ret = wallet_service->RunBySingleUser(biz_event, resource.get());
//   ASSERT_EQ(ret, 0);
// }
