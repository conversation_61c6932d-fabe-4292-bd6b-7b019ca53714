#include "test/biz/wallet_service_ut/balance_convert/balance_convert_test.hpp"

#include <gtest/gtest.h>

#include "biz_worker/service/trade/futures/modules/commonbiz/lazy_init_biz.hpp"
#include "biz_worker/service/wallet/wallet_service.hpp"
#include "test/biz/trade/lib/msg_builder.hpp"
#include "test/biz/trade/lib/stub.hpp"

// 闪兑失败 参数错误
TEST_F(WalletTest, BalanceConvert_Invalid_Params) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("balance_convert_1001_1");
  req_header->set_user_id(1001);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("dc2b4dcf-dc3e-4ab9-936f-130f2a6175ea");
  balance_convert_body->set_from_coin(123112);
  balance_convert_body->set_to_coin(ECoin::BTC);
  balance_convert_body->set_from_amount("1");
  balance_convert_body->set_to_amount("1");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kEcWalletCoinNotSupport);
}

// 闪兑成功，全仓 imr未溢出，兑换后未溢出，幂等
TEST_F(WalletTest, BalanceConvert_Cross) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("balance_convert_1001_1");
  req_header->set_user_id(1001);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("078e9dcf-d6de-41b9-9a69-130f9a617590");
  balance_convert_body->set_from_coin(ECoin::USDT);
  balance_convert_body->set_to_coin(ECoin::BTC);
  balance_convert_body->set_from_amount("20000");
  balance_convert_body->set_to_amount("1");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = enums::eaccountstatus::Normal;

  // 全仓
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Cross;

  // ----------- 兑换成功 ------------
  {
    // 手动加钱
    auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
    usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(30000);
    //  auto &unified_wallet = *draft_pkg->GetOrAttachUnifyWallet()->Latest();
    //  unified_wallet.available_balance = bbase::decimal::Decimal<>(30000);
  }

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  {
    auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
    ASSERT_EQ(decimal::kZero, btc_wallet.wallet_balance);
    ASSERT_EQ(decimal::kZero, btc_wallet.available_balance);
  }

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
  {
    auto const& usdt_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->LazyDraftW();
    ASSERT_EQ(bbase::decimal::Decimal<>(10000), usdt_wallet.wallet_balance);
    ASSERT_EQ(bbase::decimal::Decimal<>(10000), usdt_wallet.available_balance);
    auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->LazyDraftW();
    ASSERT_EQ(decimal::kOne, btc_wallet.wallet_balance);
    ASSERT_EQ(decimal::kOne, btc_wallet.available_balance);
  }

  // ----------- 幂等场景 ------------
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 3200102);
}

// 闪兑成功 （全仓 imr未溢出， USDT换不可质押币， 精度截取测试 ）
TEST_F(WalletTest, BalanceConvert_Cross_fix_scale) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("balance_convert_1001_122");
  req_header->set_user_id(1001);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("078e9ecf-d6fe-41b9-9a69-130f9a617590");
  balance_convert_body->set_from_coin(ECoin::USDT);
  balance_convert_body->set_to_coin(ECoin::BTC);
  balance_convert_body->set_from_amount("150.**********");
  balance_convert_body->set_to_amount("1");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = enums::eaccountstatus::Normal;

  // 全仓
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Cross;

  // ----------- 兑换成功 ------------
  {
    // 手动加钱
    auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
    usdt_wallet.wallet_balance = bbase::decimal::Decimal<>("150.**********");
    //  auto &unified_wallet = *draft_pkg->GetOrAttachUnifyWallet()->Latest();
    //  unified_wallet.available_balance = bbase::decimal::Decimal<>(30000);
  }

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  {
    auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
    ASSERT_EQ(decimal::kZero, btc_wallet.wallet_balance);
    ASSERT_EQ(decimal::kZero, btc_wallet.available_balance);
  }

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
  {
    auto const& usdt_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->LazyDraftW();
    ASSERT_EQ(decimal::kZero, usdt_wallet.wallet_balance);
    ASSERT_EQ(decimal::kZero, usdt_wallet.available_balance);
    auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->LazyDraftW();
    ASSERT_EQ(decimal::kOne, btc_wallet.wallet_balance);
    ASSERT_EQ(decimal::kOne, btc_wallet.available_balance);
  }

  // ----------- 幂等场景 ------------
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 3200102);
}

// 闪兑成功，组合 imr未溢出，兑换后未溢出，幂等
TEST_F(WalletTest, BalanceConvert_Portfolio) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("balance_convert_1001_1");
  req_header->set_user_id(1001);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("078e9dcf-d6de-41b9-9a69-130f9a617590");
  balance_convert_body->set_from_coin(ECoin::USDT);
  balance_convert_body->set_to_coin(ECoin::BTC);
  balance_convert_body->set_from_amount("20000");
  balance_convert_body->set_to_amount("1");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = enums::eaccountstatus::Normal;

  // 组合
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Portfolio;

  // ----------- 兑换成功 ------------
  {
    // 手动加钱
    auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
    usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(30000);
    //  auto &unified_wallet = *draft_pkg->GetOrAttachUnifyWallet()->Latest();
    //  unified_wallet.available_balance = bbase::decimal::Decimal<>(30000);
  }

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  {
    auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
    ASSERT_EQ(decimal::kZero, btc_wallet.wallet_balance);
    ASSERT_EQ(decimal::kZero, btc_wallet.available_balance);
  }

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);
  {
    auto const& usdt_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->LazyDraftW();
    ASSERT_EQ(bbase::decimal::Decimal<>(10000), usdt_wallet.wallet_balance);
    ASSERT_EQ(bbase::decimal::Decimal<>(10000), usdt_wallet.available_balance);
    auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->LazyDraftW();
    ASSERT_EQ(decimal::kOne, btc_wallet.wallet_balance);
    ASSERT_EQ(decimal::kOne, btc_wallet.available_balance);
  }

  // ----------- 幂等场景 ------------
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 3200102);
}

// 闪兑失败，全仓兑出大于ab
TEST_F(WalletTest, BalanceConvert_Cross_exchange_out_over_ab) {
  // 兑出大于ab
  int deposit_amount = 1000;
  int convert_out_amount = 2000;

  int uid = 1011;
  auto per_user_store = std::make_shared<store::PerUserStore>(uid, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id(fmt::format("balance_convert_{}_1", uid));
  req_header->set_user_id(uid);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("3fa423aa-56ef-4891-86a4-918511f0dde8");
  balance_convert_body->set_from_coin(ECoin::USDT);
  balance_convert_body->set_to_coin(ECoin::BTC);
  balance_convert_body->set_from_amount(std::to_string(convert_out_amount));
  balance_convert_body->set_to_amount("1");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      uid, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = uid;
  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  // ------------- 全仓 -------------
  // 全仓
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Cross;

  // 正常账户状态
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = const_cast<store::Account*>(cow_account->Latest());
  arc_account->account_status = enums::eaccountstatus::Normal;

  {
    // 手动加钱
    auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
    usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(deposit_amount);
  }

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  {
    auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
    ASSERT_EQ(decimal::kZero, btc_wallet.wallet_balance);
    ASSERT_EQ(decimal::kZero, btc_wallet.available_balance);
  }

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kEcInsufficientAB);
}

// 闪兑失败，逐仓兑出大于ab
TEST_F(WalletTest, BalanceConvert_Isolated_exchange_out_over_ab) {
  // 兑出大于ab
  int deposit_amount = 1000;
  int convert_out_amount = 2000;

  int uid = 1011;
  auto per_user_store = std::make_shared<store::PerUserStore>(uid, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id(fmt::format("balance_convert_{}_1", uid));
  req_header->set_user_id(uid);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("3fa423aa-56ef-4891-86a4-918511f0dde8");
  balance_convert_body->set_from_coin(ECoin::USDT);
  balance_convert_body->set_to_coin(ECoin::BTC);
  balance_convert_body->set_from_amount(std::to_string(convert_out_amount));
  balance_convert_body->set_to_amount("1");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      uid, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = uid;
  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  // 正常账户状态
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = const_cast<store::Account*>(cow_account->Latest());
  arc_account->account_status = enums::eaccountstatus::Normal;

  // ------------- 逐仓 -------------
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Isolated;

  {
    // 手动加钱
    auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
    usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(deposit_amount);
  }

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  {
    auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
    ASSERT_EQ(decimal::kZero, btc_wallet.wallet_balance);
    ASSERT_EQ(decimal::kZero, btc_wallet.available_balance);
  }

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kEcInsufficientAB);
}

// 闪兑失败，组合保证金模式 兑出大于ab
TEST_F(WalletTest, BalanceConvert_Portfolio_exchange_out_over_ab) {
  // 兑出大于ab
  int deposit_amount = 1000;
  int convert_out_amount = 2000;

  int uid = 1011;
  auto per_user_store = std::make_shared<store::PerUserStore>(uid, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id(fmt::format("balance_convert_{}_1", uid));
  req_header->set_user_id(uid);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("3fa423aa-56ef-4891-86a4-918511f0dde8");
  balance_convert_body->set_from_coin(ECoin::USDT);
  balance_convert_body->set_to_coin(ECoin::BTC);
  balance_convert_body->set_from_amount(std::to_string(convert_out_amount));
  balance_convert_body->set_to_amount("1");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      uid, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = uid;
  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  // 正常账户状态
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = const_cast<store::Account*>(cow_account->Latest());
  arc_account->account_status = enums::eaccountstatus::Normal;

  // ------------- 组合 -------------
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Portfolio;

  // 手动加钱
  auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
  usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(deposit_amount);

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
  ASSERT_EQ(decimal::kZero, btc_wallet.wallet_balance);
  ASSERT_EQ(decimal::kZero, btc_wallet.available_balance);

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kEcInsufficientAB);
}

// 闪兑失败 （超额: 充值1w，兑换2w）
TEST_F(WalletTest, BalanceConvert_over_balance) {
  int deposit_amount = 10000;
  int convert_out_amount = 20000;

  int uid = 1002;
  auto per_user_store = std::make_shared<store::PerUserStore>(uid, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id(fmt::format("balance_convert_{}_2", uid));
  req_header->set_user_id(uid);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("9c020964-4953-497a-a32c-23b99e98873f");
  balance_convert_body->set_from_coin(ECoin::USDT);
  balance_convert_body->set_to_coin(ECoin::BTC);
  balance_convert_body->set_from_amount(std::to_string(convert_out_amount));
  balance_convert_body->set_to_amount("1");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      uid, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = uid;
  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = enums::eaccountstatus::Normal;

  // ----------- 兑换资金不足 ------------
  // 手动加钱 1w USDT
  auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
  usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(deposit_amount);

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
  ASSERT_EQ(decimal::kZero, btc_wallet.wallet_balance);
  ASSERT_EQ(decimal::kZero, btc_wallet.available_balance);

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kEcInsufficientAB);
}

// 闪兑失败 （全仓强平）
TEST_F(WalletTest, BalanceConvert_liqing) {
  int deposit_amount = 30000;
  int convert_out_amount = 20000;
  auto account_status = enums::eaccountstatus::Liq;

  int uid = 1003;
  auto per_user_store = std::make_shared<store::PerUserStore>(uid, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id(fmt::format("balance_convert_{}_2", uid));
  req_header->set_user_id(uid);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("9c020964-4953-497a-a32c-23b99e98873f");
  balance_convert_body->set_from_coin(ECoin::USDT);
  balance_convert_body->set_to_coin(ECoin::BTC);
  balance_convert_body->set_from_amount(std::to_string(convert_out_amount));
  balance_convert_body->set_to_amount("1");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      uid, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = uid;
  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  // 全仓
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Cross;

  // ----------- 模拟强平中 ------------
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = account_status;

  // 手动加钱
  auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
  usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(deposit_amount);

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
  ASSERT_EQ(decimal::kZero, btc_wallet.wallet_balance);
  ASSERT_EQ(decimal::kZero, btc_wallet.available_balance);

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kAccountIsInLiq);
}

// 闪兑失败 （逐仓强平）
TEST_F(WalletTest, BalanceConvert_Isolate_liqing) {
  int deposit_amount = 30000;
  int convert_out_amount = 20000;
  auto coin = ECoin::USDT;

  int uid = 1008;
  auto per_user_store = std::make_shared<store::PerUserStore>(uid, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id(fmt::format("balance_convert_{}_2", uid));
  req_header->set_user_id(uid);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("9c020964-4953-497a-a32c-23b99e98873f");
  balance_convert_body->set_from_coin(coin);
  balance_convert_body->set_to_coin(ECoin::BTC);
  balance_convert_body->set_from_amount(std::to_string(convert_out_amount));
  balance_convert_body->set_to_amount("1");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      uid, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = uid;
  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = const_cast<store::Account*>(cow_account->Latest());
  arc_account->account_status = enums::eaccountstatus::Normal;

  // 逐仓
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Isolated;

  // ----------- 模拟强平中 ------------
  auto symbol = ESymbol::BTCUSDT;
  auto pz_mode = EPositionMode::MergedSingle;
  auto pz_idx = EPositionIndex::Single;
  auto pz_status = EPositionStatus::LiqInProcessing;
  auto coin_store = std::make_shared<store::PerCoinStore>(uid, coin);
  draft_pkg->u_store->working_coins_[coin] = coin_store;

  auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, pz_mode);
  draft_pkg->u_store->working_coins_[coin]->working_future_symbols_[symbol] = symbol_store;

  auto pz = biz::commonbiz::LazyInitBiz::InitPzIdx(draft_pkg.get(), coin, symbol, pz_idx, pz_status);

  // EPositionIndex::Single
  coin_store->futures_positions_.emplace(store::PositionKey{.symbol_ = symbol, .position_index = 0}, pz.get());
  symbol_store->all_positions_[EPositionIndex::Single] = pz;
  symbol_store->all_positions_[EPositionIndex::Single]->LazyDraftP()->coin = coin;
  symbol_store->all_positions_[EPositionIndex::Single]->LazyDraftP()->size = 1;

  // 手动加钱
  auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
  usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(deposit_amount);

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
  ASSERT_EQ(decimal::kZero, btc_wallet.wallet_balance);
  ASSERT_EQ(decimal::kZero, btc_wallet.available_balance);

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kAccountIsInLiq);
}

// 闪兑失败 （mmr溢出）
TEST_F(WalletTest, BalanceConvert_mmr_overflow) {
  int deposit_amount = 30000;
  int convert_out_amount = 20000;
  auto mmr = 1.5;

  int uid = 1004;
  auto per_user_store = std::make_shared<store::PerUserStore>(uid, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id(fmt::format("balance_convert_{}_1", uid));
  req_header->set_user_id(uid);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("9c020964-4953-497a-a32c-23b99e98873f");
  balance_convert_body->set_from_coin(ECoin::USDT);
  balance_convert_body->set_to_coin(ECoin::BTC);
  balance_convert_body->set_from_amount(std::to_string(convert_out_amount));
  balance_convert_body->set_to_amount("1");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      uid, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = uid;
  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  // 正常账户状态
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = enums::eaccountstatus::Normal;

  // 手动加钱
  auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
  usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(deposit_amount);

  // ----------- 模拟MMR溢出 ------------
  auto& wallet = *draft_pkg->GetOrAttachUnifyWallet()->LazyDraftW();
  wallet.im_ratio = 1;
  wallet.im_ratio_without_bonus = 1.5;
  wallet.mm_ratio = mmr;
  wallet.mm_ratio_without_bonus = mmr;

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
  ASSERT_EQ(decimal::kZero, btc_wallet.wallet_balance);
  ASSERT_EQ(decimal::kZero, btc_wallet.available_balance);

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kAccountIsInLiq);
}

// 闪兑成功 （全仓 imr未溢出，闪兑后imr=1, BTC换不可质押币）
TEST_F(WalletTest, BalanceConvert_Cross_imr_after_bc_overflow) {
  auto coin = ECoin::USDT;

  int uid = 100512;
  auto per_user_store = std::make_shared<store::PerUserStore>(uid, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id(fmt::format("balance_convert_{}_1", uid));
  req_header->set_user_id(uid);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("8e5d9de7-79c4-4340-adf3-c08a44438aeb");
  balance_convert_body->set_from_coin(ECoin::BTC);
  balance_convert_body->set_to_coin(ECoin::MKR);
  balance_convert_body->set_from_amount("0.3172986842105263150000000000000000000000000000000004");
  balance_convert_body->set_to_amount("500");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      uid, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = uid;

  // 更新BTC行情 1w
  const auto& future_mark_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
  future_mark_price_map->emplace(ESymbol::BTCUSDT, biz::price_d_t("10000"));

  workerStore->SyncFutureMarkPrice(future_mark_price_map);
  ASSERT_EQ(biz::price_d_t("10000"), *workerStore->GetFutureMarkPrice(ESymbol::BTCUSDT));

  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  // 全仓
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Cross;

  // 正常账户状态
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = enums::eaccountstatus::Normal;

  // ----------- 模拟IMR溢出 ------------
  auto symbol = ESymbol::BTCUSDT;
  auto pz_mode = EPositionMode::MergedSingle;
  auto pz_idx = EPositionIndex::Single;
  auto pz_status = EPositionStatus::Normal;
  auto coin_store = std::make_shared<store::PerCoinStore>(uid, coin);
  draft_pkg->u_store->working_coins_[coin] = coin_store;

  auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, pz_mode);
  draft_pkg->u_store->working_coins_[coin]->working_future_symbols_[symbol] = symbol_store;

  auto pz = biz::commonbiz::LazyInitBiz::InitPzIdx(draft_pkg.get(), coin, symbol, pz_idx, pz_status);

  // EPositionIndex::Single
  coin_store->futures_positions_.emplace(store::PositionKey{.symbol_ = symbol, .position_index = 0}, pz.get());
  symbol_store->all_positions_[EPositionIndex::Single] = pz;
  symbol_store->pz_not_zero_bitmap_ = 1;
  auto& raw_pz = *pz->LazyDraftP();
  raw_pz.coin = coin;
  raw_pz.symbol = symbol;
  raw_pz.side = ESide::Buy;
  raw_pz.size = 1e8;
  // 模拟困损
  raw_pz.entry_price = 1400000000000;       // 1.4w开仓价格
  raw_pz.session_value_e8 = 1400000000000;  // 1.4w开仓价值， 全仓/PM用
  raw_pz.position_balance = 1400000000000;  // 1.4w开仓价值， Isolate用
  raw_pz.order_cost = 50000000000;          // 500成本
  raw_pz.min_position_cost = 50000000000;   // 500成本
  raw_pz.risk_id = 10;
  raw_pz.leverage = 1;

  {
    // 手动加钱
    // 持仓upl占4k, cost占1k, 可用余额剩下100， 闪兑100后ab=0
    int deposit_amount = 500;
    auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(coin)->Latest());
    usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(deposit_amount);

    auto& btc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest());
    btc_wallet.wallet_balance = bbase::decimal::Decimal<>("0.5");
  }

  {
    // imr溢出
    auto& wallet = *const_cast<store::UnifyWallet*>(draft_pkg->GetOrAttachUnifyWallet()->Latest());
    wallet.im_ratio = 1;
    wallet.im_ratio_without_bonus = 1;
  }

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  {
    auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
    ASSERT_EQ(bbase::decimal::Decimal("0.5"), btc_wallet.wallet_balance);

    auto const& mkr_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::MKR)->Latest();
    ASSERT_EQ(decimal::kZero, mkr_wallet.wallet_balance);
    ASSERT_EQ(decimal::kZero, mkr_wallet.available_balance);
  }

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);

  {
    auto const& wallet = *draft_pkg->GetOrAttachUnifyWallet()->Latest();
    EXPECT_EQ(wallet.im_ratio, 0.9963);
    EXPECT_EQ(wallet.im_ratio_without_bonus, 0.9963);

    auto const& mkr_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::MKR)->Latest();
    EXPECT_EQ(bbase::decimal::Decimal("500"), mkr_wallet.wallet_balance);
    EXPECT_EQ(bbase::decimal::Decimal("500"), mkr_wallet.available_balance);

    auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
    EXPECT_EQ(bbase::decimal::Decimal("0.1827013157894736849999999999999999999999999999999996"),
              btc_wallet.wallet_balance);
    EXPECT_EQ(bbase::decimal::Decimal("0.1827013157894736849999999999999999999999999999999996"),
              btc_wallet.available_balance);
  }
}

// 闪兑失败 （全仓 imr溢出，闪兑后未增加）
TEST_F(WalletTest, BalanceConvert_Cross_imr_overflow_bc_success) {
  int convert_out_amount = 1000;
  auto coin = ECoin::USDT;

  int uid = 1005;
  auto per_user_store = std::make_shared<store::PerUserStore>(uid, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id(fmt::format("balance_convert_{}_1", uid));
  req_header->set_user_id(uid);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("8e5d9de7-79c4-4340-adf3-c08a44438aeb");
  balance_convert_body->set_from_coin(ECoin::OMG);
  balance_convert_body->set_to_coin(ECoin::MKR);
  balance_convert_body->set_from_amount(std::to_string(convert_out_amount));
  balance_convert_body->set_to_amount("100");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      uid, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = uid;

  // 更新BTC行情 1w
  const auto& future_mark_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
  future_mark_price_map->emplace(ESymbol::BTCUSDT, biz::price_d_t("10000"));

  workerStore->SyncFutureMarkPrice(future_mark_price_map);
  ASSERT_EQ(biz::price_d_t("10000"), *workerStore->GetFutureMarkPrice(ESymbol::BTCUSDT));

  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  // 全仓
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Cross;

  // 正常账户状态
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = enums::eaccountstatus::Normal;

  // ----------- 模拟IMR溢出 ------------
  auto symbol = ESymbol::BTCUSDT;
  auto pz_mode = EPositionMode::MergedSingle;
  auto pz_idx = EPositionIndex::Single;
  auto pz_status = EPositionStatus::Normal;
  auto coin_store = std::make_shared<store::PerCoinStore>(uid, coin);
  draft_pkg->u_store->working_coins_[coin] = coin_store;

  auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, pz_mode);
  draft_pkg->u_store->working_coins_[coin]->working_future_symbols_[symbol] = symbol_store;

  auto pz = biz::commonbiz::LazyInitBiz::InitPzIdx(draft_pkg.get(), coin, symbol, pz_idx, pz_status);

  // EPositionIndex::Single
  coin_store->futures_positions_.emplace(store::PositionKey{.symbol_ = symbol, .position_index = 0}, pz.get());
  symbol_store->all_positions_[EPositionIndex::Single] = pz;
  auto& raw_pz = *pz->LazyDraftP();
  raw_pz.coin = coin;
  raw_pz.symbol = symbol;
  raw_pz.side = ESide::Buy;
  raw_pz.size = 1;
  // 模拟困损
  raw_pz.entry_price = 1400000000000;       // 1.4w开仓价格
  raw_pz.session_value_e8 = 1400000000000;  // 1.4w开仓价值， 全仓/PM用
  raw_pz.position_balance = 1400000000000;  // 1.4w开仓价值， Isolate用
  raw_pz.order_cost = 60000000000;          // 600成本
  raw_pz.min_position_cost = 50000000000;   // 500成本
  raw_pz.risk_id = 10;
  raw_pz.leverage = 1;

  {
    // 手动加钱
    // 持仓upl占4k, cost占1k, imr溢出=1.1%
    int deposit_amount = 5000;
    auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(coin)->Latest());
    usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(deposit_amount);

    auto& omg_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::OMG)->Latest());
    omg_wallet.wallet_balance = bbase::decimal::Decimal<>(1000);
  }

  // imr溢出
  auto& wallet = *const_cast<store::UnifyWallet*>(draft_pkg->GetOrAttachUnifyWallet()->Latest());
  wallet.im_ratio = 1.1578947368421053;
  wallet.im_ratio_without_bonus = 1.1578947368421053;

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);

  {
    auto const& omg_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::OMG)->Latest();
    ASSERT_EQ(decimal::kZero, omg_wallet.wallet_balance);
    ASSERT_EQ(decimal::kZero, omg_wallet.available_balance);

    auto const& mkr_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::MKR)->Latest();
    ASSERT_EQ(bbase::decimal::Decimal<>(100), mkr_wallet.wallet_balance);
    ASSERT_EQ(bbase::decimal::Decimal<>(100), mkr_wallet.available_balance);
  }
}

// 闪兑失败 （全仓 imr溢出，闪兑后下降）
TEST_F(WalletTest, BalanceConvert_Cross_imr_overflow_after_bc_imr_down_success) {
  int convert_out_amount = 1000;
  auto coin = ECoin::USDT;

  int uid = 1005;
  auto per_user_store = std::make_shared<store::PerUserStore>(uid, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id(fmt::format("balance_convert_{}_1", uid));
  req_header->set_user_id(uid);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("8e5d9de7-79c4-4340-adf3-c08a44438aeb");
  balance_convert_body->set_from_coin(ECoin::MKR);
  balance_convert_body->set_to_coin(ECoin::USDT);
  balance_convert_body->set_from_amount(std::to_string(convert_out_amount));
  balance_convert_body->set_to_amount("10000");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      uid, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = uid;

  // 更新BTC行情 1w
  const auto& future_mark_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
  future_mark_price_map->emplace(ESymbol::BTCUSDT, biz::price_d_t("10000"));

  workerStore->SyncFutureMarkPrice(future_mark_price_map);
  ASSERT_EQ(biz::price_d_t("10000"), *workerStore->GetFutureMarkPrice(ESymbol::BTCUSDT));

  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  // 全仓
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Cross;

  // 正常账户状态
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = enums::eaccountstatus::Normal;

  // ----------- 模拟IMR溢出 ------------
  auto symbol = ESymbol::BTCUSDT;
  auto pz_mode = EPositionMode::MergedSingle;
  auto pz_idx = EPositionIndex::Single;
  auto pz_status = EPositionStatus::Normal;
  auto coin_store = std::make_shared<store::PerCoinStore>(uid, coin);
  draft_pkg->u_store->working_coins_[coin] = coin_store;

  auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, pz_mode);
  draft_pkg->u_store->working_coins_[coin]->working_future_symbols_[symbol] = symbol_store;

  auto pz = biz::commonbiz::LazyInitBiz::InitPzIdx(draft_pkg.get(), coin, symbol, pz_idx, pz_status);

  // EPositionIndex::Single
  coin_store->futures_positions_.emplace(store::PositionKey{.symbol_ = symbol, .position_index = 0}, pz.get());
  symbol_store->all_positions_[EPositionIndex::Single] = pz;
  symbol_store->pz_not_zero_bitmap_ = 1;
  auto& raw_pz = *pz->LazyDraftP();
  raw_pz.coin = coin;
  raw_pz.symbol = symbol;
  raw_pz.side = ESide::Buy;
  raw_pz.size = 1e8;
  // 模拟困损
  raw_pz.entry_price = 1400000000000;       // 1.4w开仓价格
  raw_pz.session_value_e8 = 1400000000000;  // 1.4w开仓价值， 全仓/PM用
  raw_pz.position_balance = 1400000000000;  // 1.4w开仓价值， Isolate用
  raw_pz.order_cost = 60000000000;          // 500成本
  raw_pz.min_position_cost = 50000000000;   // 500成本
  raw_pz.risk_id = 10;
  raw_pz.leverage = 1;

  {
    // 手动加钱
    // 持仓upl占4k, cost占1k, imr溢出
    int deposit_amount = 5000;
    auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(coin)->Latest());
    usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(deposit_amount);

    auto& mkr_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::MKR)->Latest());
    mkr_wallet.wallet_balance = bbase::decimal::Decimal<>(1000);
  }

  {
    // imr溢出
    auto& wallet = *const_cast<store::UnifyWallet*>(draft_pkg->GetOrAttachUnifyWallet()->Latest());
    wallet.im_ratio = 1.1578947368421053;
    wallet.im_ratio_without_bonus = 1.1578947368421053;
  }

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);

  {
    auto const& mkr_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::MKR)->Latest();
    ASSERT_EQ(decimal::kZero, mkr_wallet.wallet_balance);
    ASSERT_EQ(decimal::kZero, mkr_wallet.available_balance);

    auto const& usdt_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest();
    ASSERT_EQ(bbase::decimal::Decimal<>(15000), usdt_wallet.wallet_balance);
    ASSERT_EQ(bbase::decimal::Decimal<>(9900), usdt_wallet.available_balance);

    auto const& wallet = *draft_pkg->GetOrAttachUnifyWallet()->Latest();
    ASSERT_EQ(wallet.im_ratio, 0.101);
  }
}

// 闪兑成功 （组合 imr未溢出，闪兑后imr=1, BTC换不可质押币）
TEST_F(WalletTest, BalanceConvert_Portfolio_imr_after_bc_overflow) {
  auto coin = ECoin::USDT;

  int uid = 100512;
  auto per_user_store = std::make_shared<store::PerUserStore>(uid, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id(fmt::format("balance_convert_{}_1", uid));
  req_header->set_user_id(uid);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("8e5d9de7-79c4-4340-adf3-c08a44438aeb");
  balance_convert_body->set_from_coin(ECoin::BTC);
  balance_convert_body->set_to_coin(ECoin::MKR);
  balance_convert_body->set_from_amount("0.3060092105263157889999999999999999999999999999999994");
  balance_convert_body->set_to_amount("500");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      uid, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = uid;

  // 更新BTC行情 1w
  const auto& future_mark_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
  future_mark_price_map->emplace(ESymbol::BTCUSDT, biz::price_d_t("10000"));

  workerStore->SyncFutureMarkPrice(future_mark_price_map);
  ASSERT_EQ(biz::price_d_t("10000"), *workerStore->GetFutureMarkPrice(ESymbol::BTCUSDT));

  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  // 组合
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Portfolio;

  // 正常账户状态
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = enums::eaccountstatus::Normal;

  // ----------- 模拟IMR溢出 ------------
  auto symbol = ESymbol::BTCUSDT;
  auto pz_mode = EPositionMode::MergedSingle;
  auto pz_idx = EPositionIndex::Single;
  auto pz_status = EPositionStatus::Normal;
  auto coin_store = std::make_shared<store::PerCoinStore>(uid, coin);
  draft_pkg->u_store->working_coins_[coin] = coin_store;

  auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, pz_mode);
  draft_pkg->u_store->working_coins_[coin]->working_future_symbols_[symbol] = symbol_store;

  auto pz = biz::commonbiz::LazyInitBiz::InitPzIdx(draft_pkg.get(), coin, symbol, pz_idx, pz_status);

  // EPositionIndex::Single
  coin_store->futures_positions_.emplace(store::PositionKey{.symbol_ = symbol, .position_index = 0}, pz.get());
  symbol_store->all_positions_[EPositionIndex::Single] = pz;
  symbol_store->pz_not_zero_bitmap_ = 1;
  auto& raw_pz = *pz->LazyDraftP();
  raw_pz.coin = coin;
  raw_pz.symbol = symbol;
  raw_pz.side = ESide::Buy;
  raw_pz.size = 1e8;
  // 模拟困损
  raw_pz.entry_price = 1400000000000;       // 1.4w开仓价格
  raw_pz.session_value_e8 = 1400000000000;  // 1.4w开仓价值， 全仓/PM用
  raw_pz.position_balance = 1400000000000;  // 1.4w开仓价值， Isolate用
  raw_pz.order_cost = 50000000000;          // 500成本
  raw_pz.min_position_cost = 50000000000;   // 500成本
  raw_pz.risk_id = 10;
  raw_pz.leverage = 1;

  {
    // 手动加钱
    // 持仓upl占4k, cost占1k, 可用余额剩下100， 闪兑100后ab=0
    int deposit_amount = 500;
    auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(coin)->Latest());
    usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(deposit_amount);

    auto& btc_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest());
    btc_wallet.wallet_balance = bbase::decimal::Decimal<>("0.5");
  }

  {
    // imr溢出
    auto& wallet = *const_cast<store::UnifyWallet*>(draft_pkg->GetOrAttachUnifyWallet()->Latest());
    wallet.im_ratio = 1;
    wallet.im_ratio_without_bonus = 1;
  }

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  {
    auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
    ASSERT_EQ(bbase::decimal::Decimal("0.5"), btc_wallet.wallet_balance);

    auto const& mkr_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::MKR)->Latest();
    ASSERT_EQ(decimal::kZero, mkr_wallet.wallet_balance);
    ASSERT_EQ(decimal::kZero, mkr_wallet.available_balance);
  }

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);

  {
    auto const& wallet = *draft_pkg->GetOrAttachUnifyWallet()->Latest();
    ASSERT_EQ(wallet.im_ratio, 0.9969);
    ASSERT_EQ(wallet.im_ratio_without_bonus, 0.9969);

    auto const& mkr_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::MKR)->Latest();
    ASSERT_EQ(bbase::decimal::Decimal("500"), mkr_wallet.wallet_balance);
    ASSERT_EQ(bbase::decimal::Decimal("500"), mkr_wallet.available_balance);

    auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
    ASSERT_EQ(bbase::decimal::Decimal("0.1939907894736842110000000000000000000000000000000006"),
              btc_wallet.wallet_balance);
    ASSERT_EQ(bbase::decimal::Decimal("0.1939907894736842110000000000000000000000000000000006"),
              btc_wallet.available_balance);
  }
}

// 闪兑失败 （组合 imr溢出，闪兑后未增加）
TEST_F(WalletTest, BalanceConvert_Portfolio_imr_overflow_bc_success) {
  auto coin = ECoin::USDT;

  int uid = 1005;
  auto per_user_store = std::make_shared<store::PerUserStore>(uid, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id(fmt::format("balance_convert_{}_1", uid));
  req_header->set_user_id(uid);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("8e5d9de7-79c4-4340-adf3-c08a44438aeb");
  balance_convert_body->set_from_coin(ECoin::OMG);
  balance_convert_body->set_to_coin(ECoin::MKR);
  balance_convert_body->set_from_amount("1000");
  balance_convert_body->set_to_amount("100");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      uid, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = uid;

  // 更新BTC行情 1w
  const auto& future_mark_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
  future_mark_price_map->emplace(ESymbol::BTCUSDT, biz::price_d_t("10000"));

  workerStore->SyncFutureMarkPrice(future_mark_price_map);
  ASSERT_EQ(biz::price_d_t("10000"), *workerStore->GetFutureMarkPrice(ESymbol::BTCUSDT));

  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  // 组合
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Portfolio;

  // 正常账户状态
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = enums::eaccountstatus::Normal;

  // ----------- 模拟IMR溢出 ------------
  auto symbol = ESymbol::BTCUSDT;
  auto pz_mode = EPositionMode::MergedSingle;
  auto pz_idx = EPositionIndex::Single;
  auto pz_status = EPositionStatus::Normal;
  auto coin_store = std::make_shared<store::PerCoinStore>(uid, coin);
  draft_pkg->u_store->working_coins_[coin] = coin_store;

  auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, pz_mode);
  draft_pkg->u_store->working_coins_[coin]->working_future_symbols_[symbol] = symbol_store;

  auto pz = biz::commonbiz::LazyInitBiz::InitPzIdx(draft_pkg.get(), coin, symbol, pz_idx, pz_status);

  // EPositionIndex::Single
  coin_store->futures_positions_.emplace(store::PositionKey{.symbol_ = symbol, .position_index = 0}, pz.get());
  symbol_store->all_positions_[EPositionIndex::Single] = pz;
  auto& raw_pz = *pz->LazyDraftP();
  raw_pz.coin = coin;
  raw_pz.symbol = symbol;
  raw_pz.side = ESide::Buy;
  raw_pz.size = 1;
  // 模拟困损
  raw_pz.entry_price = 1400000000000;       // 1.4w开仓价格
  raw_pz.session_value_e8 = 1400000000000;  // 1.4w开仓价值， 全仓/PM用
  raw_pz.position_balance = 1400000000000;  // 1.4w开仓价值， Isolate用
  raw_pz.order_cost = 50000000000;          // 500成本
  raw_pz.min_position_cost = 50000000000;   // 500成本
  raw_pz.risk_id = 10;
  raw_pz.leverage = 1;

  {
    // 手动加钱
    // 持仓upl占4k, cost占1k, imr溢出
    int deposit_amount = 5051;
    auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(coin)->Latest());
    usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(deposit_amount);

    auto& omg_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::OMG)->Latest());
    omg_wallet.wallet_balance = bbase::decimal::Decimal<>(1000);

    // imr溢出
    auto& wallet = *const_cast<store::UnifyWallet*>(draft_pkg->GetOrAttachUnifyWallet()->Latest());
    wallet.im_ratio = 1.2993633119771313;
    wallet.im_ratio_without_bonus = 1.2993633119771313;
  }

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kErrorCodeSuccess);

  {
    auto const& omg_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::OMG)->Latest();
    ASSERT_EQ(decimal::kZero, omg_wallet.wallet_balance);
    ASSERT_EQ(decimal::kZero, omg_wallet.available_balance);

    auto const& mkr_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::MKR)->Latest();
    ASSERT_EQ(bbase::decimal::Decimal<>(100), mkr_wallet.wallet_balance);
    ASSERT_EQ(bbase::decimal::Decimal<>(100), mkr_wallet.available_balance);
  }
}

// 闪兑失败 机构强平
TEST_F(WalletTest, BalanceConvert_InstLiq) {
  auto coin = ECoin::USDT;

  int uid = 100245;
  auto per_user_store = std::make_shared<store::PerUserStore>(uid, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id(fmt::format("balance_convert_{}_1", uid));
  req_header->set_user_id(uid);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("8e5d9de7-79c4-4340-adf3-c08a44438aeb");
  balance_convert_body->set_from_coin(ECoin::USDT);
  balance_convert_body->set_to_coin(ECoin::BTC);
  balance_convert_body->set_from_amount("10000");
  balance_convert_body->set_to_amount("1");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      uid, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = uid;

  // 更新BTC行情 1w
  const auto& future_mark_price_map = std::make_shared<std::unordered_map<biz::symbol_t, biz::price_d_t>>();
  future_mark_price_map->emplace(ESymbol::BTCUSDT, biz::price_d_t("10000"));

  workerStore->SyncFutureMarkPrice(future_mark_price_map);
  ASSERT_EQ(biz::price_d_t("10000"), *workerStore->GetFutureMarkPrice(ESymbol::BTCUSDT));

  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  // 全仓
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Cross;

  // 正常账户状态
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = enums::eaccountstatus::Normal;

  auto setting = const_cast<store::Setting*>(draft_pkg->GetOrAttachUserSetting()->Latest());
  setting->all_trade_disabled = true;

  {
    // 手动加钱
    int deposit_amount = 20000;
    auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(coin)->Latest());
    usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(deposit_amount);
  }

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kInstAllTradeDisabled);
}

// 持仓升级，不允许闪兑
TEST_F(WalletTest, BalanceConvert_Upgrading) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);

  // req_header
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("balance_convert_1001_1");
  req_header->set_user_id(1001);
  req_header->set_action(EAction::BalanceConvert);

  // req_body
  auto* balance_convert_body = request->mutable_walletreqgroupbody()->mutable_balanceconvertreq();
  balance_convert_body->set_trans_id("078e9dcf-d6de-41b9-9a69-130f9a617590");
  balance_convert_body->set_from_coin(ECoin::USDT);
  balance_convert_body->set_to_coin(ECoin::BTC);
  balance_convert_body->set_from_amount("20000");
  balance_convert_body->set_to_amount("1");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  // draft_pkg
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_account = draft_pkg->GetOrAttachAccountInfo();
  auto arc_account = cow_account->LazyDraftA();
  arc_account->account_status = enums::eaccountstatus::Normal;

  // 组合
  auto cow_setting = draft_pkg->GetOrAttachUserSetting();
  auto arc_setting = const_cast<store::Setting*>(cow_setting->Latest());
  arc_setting->account_mode = EAccountMode::Portfolio;
  arc_setting->upgrading_with_position = true;

  // ----------- 兑换成功 ------------
  // 手动加钱
  auto& usdt_wallet = *const_cast<store::Wallet*>(draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT)->Latest());
  usdt_wallet.wallet_balance = bbase::decimal::Decimal<>(30000);
  //  auto &unified_wallet = *draft_pkg->GetOrAttachUnifyWallet()->Latest();
  //  unified_wallet.available_balance = bbase::decimal::Decimal<>(30000);

  auto resource = std::make_shared<store::UnicastResource>();
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto const& btc_wallet = *draft_pkg->GetOrAttachSettleCoinWallet(ECoin::BTC)->Latest();
  ASSERT_EQ(decimal::kZero, btc_wallet.wallet_balance);
  ASSERT_EQ(decimal::kZero, btc_wallet.available_balance);

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  // 检查结果
  ASSERT_EQ(ret, error::ErrorCode::kUserUpgrading);
}
