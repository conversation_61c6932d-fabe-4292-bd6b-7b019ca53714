#include "test/biz/wallet_service_ut/per_worker_store/config_apis_test.hpp"

#include <gtest/gtest.h>

#include "data/const.hpp"
#include "src/biz_worker/service/trade/store/per_worker_store.hpp"
#include "src/config/config_proxy.hpp"
#include "src/data/event/biz_event.hpp"

using store::PerWorkerStore;
TEST_F(ConfigApisTest, GetTradekeyValue) {
  std::string const option_trade_content =
      R"({"data":{"BTC":{"orderPriceFromLastPrice":"0.05","takerFee":"0.0003","priceRangeInterval":"0.1212",
"maxProportionOfTransactionInOrderPrice":"1.003","maxImFactor":"0.15","minImFactor":"0.05"}}})";
  config::ConfigProxy::Instance().OptionTradeConfigReceiveHandler(option_trade_content);
  config::ConfigProxy::Instance().PmConfigReceiveHandler(option_trade_content);
  waitRecvHandler();

  auto worker_store = std::make_shared<store::PerWorkerStore>();

  auto taker_fee = worker_store->GetTradeKeyValue("BTC", "takerFee");
  ASSERT_STREQ(taker_fee.c_str(), "0.0003");

  ASSERT_EQ(worker_store->PriceRangeInterval("BTC"), bbase::decimal::Decimal<>("0.1212"));
  ASSERT_EQ(worker_store->PriceRangeInterval("ETH"), bbase::decimal::Decimal<>("0.02"));

  auto orderPriceFromLastPrice = worker_store->GetTradeKeyValue("BTC", "orderPriceFromLastPrice");
  ASSERT_EQ(worker_store->GetDeviationOfLiqOrderPriceFromLastPrice("BTC"), bbase::decimal::Decimal<>("0.05"));
}

TEST_F(ConfigApisTest, GetCreditLimitByUidAndCoin) {
  config::getTlsCfgMgrRaw()->coin_client()->InitMockData();
  static constexpr const char* kUmUserConfigContent =
      R"({"data":[{"coinLimitMap":{"USDT":10000},"gmtModified":"2023-09-07T03:49:43.710","status":"ACTIVE","uid":"6353623","umCoinWhiteListDefaultModel":"DEFAULT_VIP_LEVEL"},
      {"coinLimitMap":{"USDT":20000,"USDC":10000},"gmtModified":"2023-08-31T04:18:34.065","status":"ACTIVE","uid":"6355032","umCoinWhiteListDefaultModel":"DEFAULT_VIP_LEVEL"}],
      "version":"113886a0-7ee6-4109-a668-376fc6ccaa60"})";

  config::ConfigProxy::Instance().UmUserWhiteConfigReceiveHandler(kUmUserConfigContent);
  waitRecvHandler();

  auto worker_store = std::make_shared<store::PerWorkerStore>();

  std::shared_ptr<biz::BigDecimal> credit_limit = worker_store->GetCreditLimitByUidAndCoin(6353623, ECoin::USDT);
  ASSERT_NE(credit_limit, nullptr);
  ASSERT_EQ(*credit_limit.get(), bbase::decimal::Decimal<>(10000));

  static constexpr const char* kUmLadderContent =
      R"({"data":{"BTC":{"0":{"hourlyBorrowRate":"0.00000114200000000000","interestFreeCreditAmount":"2222","creditLimit":"22223","baseCoin":"BTC"},
      "1003":{"hourlyBorrowRate":"0.00000114200000000000","interestFreeCreditAmount":"30000000","creditLimit":"30000000"}},
      "SOL":{"2001":{"interestFreeCreditAmount":"2000","creditLimit":"5000000"}}},"version":"e8b8e218-7897-4e30-aea4-2f4ab7ee5758"})";
  config::ConfigProxy::Instance().UmLadderConfigReceiveHandler(kUmLadderContent);
  static constexpr const char* userLevelContent_1 =
      R"({"data":{"123":0,"456":1001},"lastUpdate":0,"version":"********************************"})";
  static constexpr const char* userLevelContent_2 =
      R"({"data":{"321":0,"456":1003},"lastUpdate":0,"version":"********************************"})";
  config::ConfigProxy::Instance().VipUserLevelReceiveHandler(userLevelContent_1);
  config::ConfigProxy::Instance().VipUserLevelReceiveHandler(userLevelContent_2);
  waitRecvHandler();

  credit_limit = worker_store->GetCreditLimitByUidAndCoin(123, ECoin::BTC);
  ASSERT_NE(credit_limit, nullptr);
  ASSERT_EQ(*credit_limit.get(), bbase::decimal::Decimal<>(22223));

  credit_limit = worker_store->GetCreditLimitByUidAndCoin(123, ECoin::SOL);
  ASSERT_EQ(credit_limit, nullptr);
}

TEST_F(ConfigApisTest, GetFundPoolMaxAvailableBalance) {
  static constexpr const char* kFundPoolBalanceContent =
      R"({"content":"{\"SUSHI\":{\"availableBalance\":\"600\",\"usedBorrowSizeBalance\":\"0\",\"totalDepositBalance\":\"600\",\"minAvailableBalance\":\"0\"},\"USDC\":{\"availableBalance\":\"300\",\"usedBorrowSizeBalance\":\"0\",\"totalDepositBalance\":\"0\",\"minAvailableBalance\":\"100\"}}"})";

  config::ConfigProxy::Instance().FundPoolBalanceReceiveHandler(kFundPoolBalanceContent);
  waitRecvHandler();
  auto usdc = std::make_shared<config::CoinInfoDTO>();
  usdc->coin_id = 16;
  usdc->coin = "USDC";
  usdc->biz_type = 1;
  usdc->coin_plate = 1;
  usdc->accuracy_length = 6;

  auto data = std::make_shared<config::CoinInfoData>();
  data->coin_id_map[16] = usdc;
  data->coin_map["USDC"] = usdc;
  config::getTlsCfgMgrRaw()->coin_client()->set_coin_info_data(data);

  auto worker_store = std::make_shared<store::PerWorkerStore>();

  auto ret = worker_store->GetFundPoolMaxAvailableBalance(static_cast<int32_t>(ECoin::USDC));
  ASSERT_EQ(*ret.get(), bbase::decimal::Decimal<>("200"));

  ret = worker_store->GetFundPoolMaxAvailableBalance(static_cast<int32_t>(ECoin::AMP));
  ASSERT_TRUE(ret == nullptr);
}

TEST_F(ConfigApisTest, GetOptionLiquidationFeeRate) {
  const std::shared_ptr<biz::sc::SymbolConfigManager> symbol_config_mgr =
      std::make_shared<biz::sc::SymbolConfigManager>();
  config::ConfigProxy::Instance().config_mgr()->set_symbol_config_mgr(symbol_config_mgr);

  static constexpr const char* kTradeContent =
      R"({"data":{"BTC":{"orderPriceFromLastPrice":"0.03","liquidationFeeRate":"0.0035","bidAskMinIv":"0",
      "maxProportionOfDeliveryInOptionValue":"0.125","insuranceFeeRate":"0.000175","tickSize":"0.5","deviationOfMarkIV":"0.02",
      "entryId":"47","markMinIv":"0.1","bidAskMaxIv":"7.5","makerFee":"0.00025","minSellBasis":"0.0005","minImFactor":"0.1",
      "markMaxIv":"4.5","liquidationFeeRatePerp":"0.0008","baseCoin":"BTC","maxBuyFactor":"10","minSellFactor":"10",
      "liquidationTarget":"0.1","stepLiqSheet":"0-20:5;20-100:10;100-200:20;200-:1000","takerFee":"0.00073","minOrderSize":"0.01",
      "liquidationFeeRateFutures":"9.998","minOrderSizeIncrement":"0.01","liquidationFeeRateOption":"9.999","orderPriceFromMarkPrice":"0.1",
      "priceDeviationPercentage":"99.99","maxImFactor":"0.15","maxProportionOfTransactionInOrderPrice":"0.1251","mmFactor":"0.03",
      "minOrderPrice":"0.5","basicDeliveryFeeRate":"0.00015","maxOrderSize":"50000","takeoverTrigger":"1.6","stepLiqMaxOrderSize":"5",
      "maxOrderPrice":"10000000"}},"version":"32798883-f752-4222-b171-71a8ff43531f"})";
  static constexpr const char* kTradeUSDTContent =
      R"({"data":{"DEFAULT_COIN":{"liquidationFeeRateUsdt":"0.00075","insuranceFeeRateUsdt":"0.000513"}},
      "version":"437eebfe-91dd-4e03-a173-6a215d2d0863"})";
  static constexpr const char* kOptionSymbolContentSnippet =
      R"({"data":[{"assetType":"OPTION","baseCoin":"BTC","coinpairId":1,"contractSize":1,"contractType":"LinearOption",
      "crossId":10005,"deliveryTime":1841937600,"expiry":"CURRENT_WEEK","id":102311,"multiplier":1,"onlineTime":1640687236,
      "quoteCoin":"USD","settleCoin":"USDC","standard":"U","status":"OFFLINE","strikePrice":40000.000000002,
      "symbolName":"BTC-31DEC21-40000-C","symbolType":"C"}, {"assetType":"OPTION","baseCoin":"BTC","coinpairId":1,
      "contractSize":1,"contractType":"LinearOption","crossId":10005,"deliveryTime":1841937600,"expiry":"CURRENT_WEEK","id":102311,
      "multiplier":1,"onlineTime":1640687236,"quoteCoin":"USD","settleCoin":"USDC","standard":"U","status":"ONLINE","strikePrice":40000.000000002,
      "symbolName":"BTC-31DEC21-40000-C","symbolType":"C"}],"version":"aa6d74a6-77af-4409-b09c-fe902f72683a"})";

  config::ConfigProxy::Instance().OptionTradeConfigReceiveHandler(kTradeContent);
  config::ConfigProxy::Instance().OptionTradeUSDTConfigReceiveHandler(kTradeUSDTContent);
  config::ConfigProxy::Instance().SymbolFullReceiveHandler(kOptionSymbolContentSnippet);
  waitRecvHandler();

  std::shared_ptr<biz::SymbolConfig> const symbol_config = std::make_shared<biz::SymbolConfig>();
  symbol_config->symbol_ = 100100;
  symbol_config->symbol_name_ = "BTCUSDT";
  symbol_config->coin_name_ = "USDT";
  symbol_config->coin_ = ECoin::USDT;
  symbol_config->base_currency_ = "BTC";
  symbol_config->contract_type_ = EContractType::LinearPerpetual;
  biz::sc::SymbolConfigMap symbol_config_map;
  symbol_config_map.insert({symbol_config->symbol_, symbol_config});
  config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr()->set_symbol_config_map(symbol_config_map);

  auto worker_store = std::make_shared<store::PerWorkerStore>();

  ASSERT_EQ(worker_store->GetOptionLiquidationFeeRate(102311), bbase::decimal::Decimal<>("0.0035"));
  ASSERT_EQ(worker_store->GetOptionLiquidationFeeRate(ESymbol::UNKNOWN), bbase::decimal::Decimal<>("0.0002"));
}

TEST_F(ConfigApisTest, GetStepLiqOneTimeLimit) {
  const std::shared_ptr<biz::sc::SymbolConfigManager> symbol_config_mgr =
      std::make_shared<biz::sc::SymbolConfigManager>();
  config::ConfigProxy::Instance().config_mgr()->set_symbol_config_mgr(symbol_config_mgr);

  static constexpr const char* kTradeContent =
      R"({"data":{"BTC":{"orderPriceFromLastPrice":"0.03","liquidationFeeRate":"0.0035","bidAskMinIv":"0",
      "maxProportionOfDeliveryInOptionValue":"0.125","insuranceFeeRate":"0.000175","tickSize":"0.5","deviationOfMarkIV":"0.02",
      "entryId":"47","markMinIv":"0.1","bidAskMaxIv":"7.5","makerFee":"0.00025","minSellBasis":"0.0005","minImFactor":"0.1",
      "markMaxIv":"4.5","liquidationFeeRatePerp":"0.0008","baseCoin":"BTC","maxBuyFactor":"10","minSellFactor":"10",
      "liquidationTarget":"0.1","stepLiqSheet":"0-20:5;20-100:10;100-200:20;200-:1000","takerFee":"0.00073","minOrderSize":"0.01",
      "liquidationFeeRateFutures":"9.998","minOrderSizeIncrement":"0.01","liquidationFeeRateOption":"9.999","orderPriceFromMarkPrice":"0.1",
      "priceDeviationPercentage":"99.99","maxImFactor":"0.15","maxProportionOfTransactionInOrderPrice":"0.1251","mmFactor":"0.03",
      "minOrderPrice":"0.5","basicDeliveryFeeRate":"0.00015","maxOrderSize":"50000","takeoverTrigger":"1.6","stepLiqMaxOrderSize":"5",
      "maxOrderPrice":"10000000"}},"version":"32798883-f752-4222-b171-71a8ff43531f"})";

  config::ConfigProxy::Instance().OptionTradeConfigReceiveHandler(kTradeContent);
  waitRecvHandler();

  std::shared_ptr<biz::SymbolConfig> const symbol_config = std::make_shared<biz::SymbolConfig>();
  symbol_config->symbol_ = ESymbol::BTCUSD;
  symbol_config->symbol_name_ = "BTCUSD";
  symbol_config->coin_name_ = "BTC";
  symbol_config->coin_ = ECoin::BTC;
  symbol_config->base_currency_ = "BTC";
  symbol_config->contract_type_ = EContractType::InversePerpetual;
  symbol_config->max_new_order_qty_x_ = 500000000;
  symbol_config->qty_scale_ = 4;

  std::shared_ptr<biz::SymbolConfig> const symbol_config_eth = std::make_shared<biz::SymbolConfig>();
  symbol_config_eth->symbol_ = ESymbol::ETHUSDT;
  symbol_config_eth->symbol_name_ = "ETHUSDT";
  symbol_config_eth->coin_name_ = "USDT";
  symbol_config_eth->coin_ = ECoin::USDT;
  symbol_config_eth->base_currency_ = "BTC";
  symbol_config_eth->contract_type_ = EContractType::LinearPerpetual;
  symbol_config_eth->max_new_order_qty_x_ = 100000;
  symbol_config_eth->qty_scale_ = 4;

  biz::sc::SymbolConfigMap symbol_config_map;
  symbol_config_map.insert({symbol_config->symbol_, symbol_config});
  symbol_config_map.insert({symbol_config_eth->symbol_, symbol_config_eth});
  config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr()->set_symbol_config_map(symbol_config_map);

  auto worker_store = std::make_shared<store::PerWorkerStore>();

  store::Position::Ptr draft_position = std::make_shared<store::Position>();
  draft_position->contract_type = EContractType::LinearPerpetual;
  draft_position->symbol = ESymbol::BTCUSDT;
  biz::BigDecimal limit = worker_store->GetStepLiqOneTimeLimit(decimal::kTen, "BTC", *draft_position);
  ASSERT_EQ(limit, bbase::decimal::Decimal<>("5"));

  limit = worker_store->GetStepLiqOneTimeLimit(bbase::decimal::Decimal<>(20), "BTC", *draft_position);
  ASSERT_EQ(limit, bbase::decimal::Decimal<>("10"));

  limit = worker_store->GetStepLiqOneTimeLimit(bbase::decimal::Decimal<>(99), "BTC", *draft_position);
  ASSERT_EQ(limit, bbase::decimal::Decimal<>("10"));

  limit = worker_store->GetStepLiqOneTimeLimit(bbase::decimal::Decimal<>(100), "BTC", *draft_position);
  ASSERT_EQ(limit, bbase::decimal::Decimal<>("20"));

  limit = worker_store->GetStepLiqOneTimeLimit(decimal::kMaxAmount, "BTC", *draft_position);
  ASSERT_EQ(limit, bbase::decimal::Decimal<>("1000"));

  limit = worker_store->GetStepLiqOneTimeLimit(decimal::kNegativeOne, "BTC", *draft_position);
  ASSERT_EQ(limit, bbase::decimal::Decimal<>("5"));

  store::Position::Ptr draft_position_ETH = std::make_shared<store::Position>();
  draft_position_ETH->contract_type = EContractType::LinearPerpetual;
  draft_position_ETH->symbol = ESymbol::ETHUSDT;
  limit = worker_store->GetStepLiqOneTimeLimit(decimal::kNegativeOne, "ETH", *draft_position_ETH);
  ASSERT_EQ(limit, bbase::decimal::Decimal<>("2.5"));

  store::Position::Ptr draft_position_inverse_BTC = std::make_shared<store::Position>();
  draft_position_inverse_BTC->contract_type = EContractType::InversePerpetual;
  draft_position_inverse_BTC->symbol = ESymbol::BTCUSD;
  draft_position_inverse_BTC->session_average_price_x = 500000000;
  draft_position_inverse_BTC->price_scale = 4;

  limit = worker_store->GetStepLiqOneTimeLimit(decimal::kTen, "BTC", *draft_position_inverse_BTC);
  ASSERT_EQ(limit, bbase::decimal::Decimal<>("250000"));
}
