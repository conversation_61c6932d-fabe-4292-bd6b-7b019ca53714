//
// Created by SH00674ML on 2023/3/20.
//
#include "test/biz/wallet_service_ut/deposit/deposit_test.hpp"

#include "biz_worker/service/trade/futures/modules/commonbiz/lazy_init_biz.hpp"
#include "biz_worker/service/wallet/biz/account/balance_convert_service.hpp"
#include "biz_worker/service/wallet/biz/account/custome_spot_hedge_service.hpp"
#include "biz_worker/service/wallet/biz/account/deposit_service.hpp"
#include "biz_worker/service/wallet/biz/account/set_trigger_liq_level_service.hpp"
#include "biz_worker/service/wallet/biz/account/withdraw_service.hpp"
#include "biz_worker/service/wallet/wallet_service.hpp"
#include "data/type/biz_type.hpp"

TEST_F(WalletTest, ban_trade_InitBanTradeWhiteListUser) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  per_user_store->user_setting_->cur_setting = (std::make_shared<store::Setting>());

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::deposit);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  EXPECT_FALSE(draft_pkg->w_store->IsBanTradeWhiteListUser(111));
  EXPECT_FALSE(draft_pkg->w_store->IsBanTradeWhiteListUser(222));
  EXPECT_FALSE(draft_pkg->w_store->IsBanTradeWhiteListUser(333));

  config::ConfigProxy::Instance().BanTradeWhiteListUidConfigReceiveHandler("111,222");

  usleep(10 * 1000);
  EXPECT_TRUE(draft_pkg->w_store->IsBanTradeWhiteListUser(111));
  EXPECT_TRUE(draft_pkg->w_store->IsBanTradeWhiteListUser(222));
  EXPECT_FALSE(draft_pkg->w_store->IsBanTradeWhiteListUser(333));
  LOG_INFO("InitBanTradeWhiteListUser Done");
}

TEST_F(WalletTest, ban_trade_InitBanTradeWhiteListUser_ERROR_PARSE) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  per_user_store->user_setting_->cur_setting = (std::make_shared<store::Setting>());

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::deposit);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  config::ConfigProxy::Instance().BanTradeWhiteListUidConfigReceiveHandler("111,xxx");

  usleep(10 * 1000);
  EXPECT_TRUE(draft_pkg->w_store->IsBanTradeWhiteListUser(111));
  EXPECT_FALSE(draft_pkg->w_store->IsBanTradeWhiteListUser(222));
  EXPECT_FALSE(draft_pkg->w_store->IsBanTradeWhiteListUser(333));

  config::ConfigProxy::Instance().BanTradeWhiteListUidConfigReceiveHandler("222,333");
  usleep(10 * 1000);
  EXPECT_TRUE(draft_pkg->w_store->IsBanTradeWhiteListUser(222));
  EXPECT_TRUE(draft_pkg->w_store->IsBanTradeWhiteListUser(333));
  LOG_INFO("InitBanTradeWhiteListUser_ERROR_PARSE Done");
}

TEST_F(WalletTest, ban_trade_uid_list) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  per_user_store->user_setting_->cur_setting = (std::make_shared<store::Setting>());

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::deposit);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  EXPECT_FALSE(draft_pkg->ReadOnlyUserSetting()->cur_setting->IsSpotBanTradeAll());
  EXPECT_FALSE(draft_pkg->ReadOnlyUserSetting()->cur_setting->IsSpotBanWithdraw());

  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->SetDepositBanTrade();

  EXPECT_TRUE(draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->IsSpotBanTradeAll());
  EXPECT_TRUE(draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->IsSpotBanTradeAll());
  EXPECT_TRUE(draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->IsSpotBanWithdraw());

  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->SetDepositBanTrade();

  EXPECT_TRUE(draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->IsSpotBanTradeAll());
  EXPECT_TRUE(draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->IsSpotBanWithdraw());

  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->ClearDepositBanTrade();
  EXPECT_FALSE(draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->IsSpotBanTradeAll());
  EXPECT_FALSE(draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->IsSpotBanTradeAll());
  EXPECT_FALSE(draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->IsSpotBanWithdraw());

  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->ClearDepositBanTrade();
  EXPECT_FALSE(draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->IsSpotBanTradeAll());
  EXPECT_FALSE(draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->IsSpotBanTradeAll());
  EXPECT_FALSE(draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->IsSpotBanWithdraw());
  EXPECT_FALSE(draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->IsSpotBanWithdraw());

  LOG_INFO("ban_trade_uid_list Done");
}

TEST_F(WalletTest, ban_trade_InitBanTradeWhiteListUser_whitespace) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  per_user_store->user_setting_->cur_setting = (std::make_shared<store::Setting>());

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::deposit);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  config::ConfigProxy::Instance().BanTradeWhiteListUidConfigReceiveHandler(" 111, 222, 333 ,444");
  usleep(10 * 1000);
  EXPECT_TRUE(draft_pkg->w_store->IsBanTradeWhiteListUser(111));
  EXPECT_TRUE(draft_pkg->w_store->IsBanTradeWhiteListUser(222));
  EXPECT_TRUE(draft_pkg->w_store->IsBanTradeWhiteListUser(333));
  EXPECT_TRUE(draft_pkg->w_store->IsBanTradeWhiteListUser(444));
  LOG_INFO("InitBanTradeWhiteListUser_whitespace Done");
}

TEST_F(WalletTest, ban_trade_DepositService) {
  const std::shared_ptr<biz::DepositService> service = std::make_shared<biz::DepositService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = setting;
  setting->recalculate_initiated_ = true;
  setting->user_enabled_collateral_coin_set.insert(1);
  setting->user_enabled_collateral_coin_set.insert(2);
  setting->user_enabled_collateral_coin_set.insert(3);

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::deposit);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::DepositReq&>(request->walletreqgroupbody().depositreq());

  req.set_coin(ECoin::USDT);
  req.set_amount("***********");
  req.set_bonuschange("10");
  req.set_wallet_record_type(99999);
  req.set_transid("trans_id_1");

  EXPECT_FALSE(draft_pkg->ReadOnlyUserSetting()->cur_setting->IsSpotBanTradeAll());

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();

  auto wallet_commit = draft_pkg->affect_wallets_.front();
  auto wallet_ptr = wallet_commit.current_wallet;
  auto tran_log = draft_pkg->ref_asset_trans_logs_.front();

  EXPECT_EQ(tran_log->pre_cash_balance_, bbase::decimal::Decimal<>("1000"));
  EXPECT_EQ(tran_log->settle_coin_, ECoin::USDT);
  EXPECT_EQ(tran_log->req_id_, "deposit_1");
  EXPECT_EQ(tran_log->trans_id_, "trans_id_1");
  EXPECT_EQ(tran_log->cash_balance_, bbase::decimal::Decimal<>("***********"));
  EXPECT_EQ(tran_log->cash_flow_, bbase::decimal::Decimal<>("***********"));
  EXPECT_EQ(tran_log->change_, bbase::decimal::Decimal<>("***********"));
  EXPECT_EQ(tran_log->cross_seq_, 999);
  EXPECT_EQ(tran_log->trans_type_, 99999);
  EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_balance_), bbase::decimal::Decimal<>("510"));
  EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_change_), bbase::decimal::Decimal<>("10"));
  EXPECT_EQ(tran_log->account_mode_, EAccountMode::UnKnown);

  EXPECT_TRUE(draft_pkg->affected_setting_.need_to_persist);
  EXPECT_TRUE(draft_pkg->ReadOnlyUserSetting()->cur_setting->IsSpotBanTradeAll());

  auto resp = std::make_shared<svc::unified_v2::UnifiedV2ResultDTO>();
  convertor::ToPB::ConvertToUnifiedV2ResultDTO(draft_pkg.get(), resp.get(), EProductType::Margin);
  EXPECT_TRUE(resp->asset_margin_result().per_user_setting_data().default_disable_collateral_coin());
  std::string msg{};
  LOG_INFO("MarginResult, {}", utils::PbMessageToJsonString(*resp, &msg));

  LOG_INFO("DepositService_ban_trade Done");
}

TEST_F(WalletTest, ban_trade_WithdrawService) {
  const std::shared_ptr<biz::WithdrawService> service = std::make_shared<biz::WithdrawService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = setting;
  setting->recalculate_initiated_ = true;
  setting->user_enabled_collateral_coin_set.insert(1);
  setting->user_enabled_collateral_coin_set.insert(2);
  setting->user_enabled_collateral_coin_set.insert(3);
  setting->SetDepositBanTrade();

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("withdraw_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::withdraw);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::WithdrawReq&>(request->walletreqgroupbody().withdrawreq());

  req.set_coin(ECoin::USDT);
  req.set_amount("10");
  req.set_wallet_record_type(99999);
  req.set_trans_id("trans_id_1");

  EXPECT_TRUE(draft_pkg->ReadOnlyUserSetting()->cur_setting->IsSpotBanWithdraw());

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, error::kCashBalanceInsufficient);

  LOG_INFO("ban_trade_WithdrawService Done");
}

TEST_F(WalletTest, ban_trade_DepositService_300w) {
  auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "ban_trade_deposit_limit", "3000000");

  const std::shared_ptr<biz::DepositService> service = std::make_shared<biz::DepositService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = setting;
  setting->recalculate_initiated_ = true;
  setting->user_enabled_collateral_coin_set.insert(1);
  setting->user_enabled_collateral_coin_set.insert(2);
  setting->user_enabled_collateral_coin_set.insert(3);

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::deposit);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::DepositReq&>(request->walletreqgroupbody().depositreq());

  req.set_coin(ECoin::USDT);
  req.set_amount("********");
  req.set_bonuschange("10");
  req.set_wallet_record_type(99999);
  req.set_transid("trans_id_1");

  EXPECT_FALSE(draft_pkg->ReadOnlyUserSetting()->cur_setting->IsSpotBanTradeAll());

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();

  auto wallet_commit = draft_pkg->affect_wallets_.front();
  auto wallet_ptr = wallet_commit.current_wallet;
  auto tran_log = draft_pkg->ref_asset_trans_logs_.front();

  EXPECT_EQ(tran_log->pre_cash_balance_, bbase::decimal::Decimal<>("1000"));
  EXPECT_EQ(tran_log->settle_coin_, ECoin::USDT);
  EXPECT_EQ(tran_log->req_id_, "deposit_1");
  EXPECT_EQ(tran_log->trans_id_, "trans_id_1");
  EXPECT_EQ(tran_log->cash_balance_, bbase::decimal::Decimal<>("********"));
  EXPECT_EQ(tran_log->cash_flow_, bbase::decimal::Decimal<>("********"));
  EXPECT_EQ(tran_log->change_, bbase::decimal::Decimal<>("********"));
  EXPECT_EQ(tran_log->cross_seq_, 999);
  EXPECT_EQ(tran_log->trans_type_, 99999);
  EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_balance_), bbase::decimal::Decimal<>("510"));
  EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_change_), bbase::decimal::Decimal<>("10"));
  EXPECT_EQ(tran_log->account_mode_, EAccountMode::UnKnown);

  EXPECT_FALSE(draft_pkg->affected_setting_.need_to_persist);
  EXPECT_FALSE(draft_pkg->ReadOnlyUserSetting()->cur_setting->IsSpotBanTradeAll());

  auto resp = std::make_shared<svc::unified_v2::UnifiedV2ResultDTO>();
  convertor::ToPB::ConvertToUnifiedV2ResultDTO(draft_pkg.get(), resp.get(), EProductType::Margin);
  std::string msg{};
  LOG_INFO("MarginResult, {}", utils::PbMessageToJsonString(*resp, &msg));

  LOG_INFO("DepositService_ban_trade_300w Done");
}

TEST_F(WalletTest, ban_trade_DepositService_1000w) {
  auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "ban_trade_deposit_limit", "********");

  const std::shared_ptr<biz::DepositService> service = std::make_shared<biz::DepositService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = setting;
  setting->recalculate_initiated_ = true;
  setting->user_enabled_collateral_coin_set.insert(1);
  setting->user_enabled_collateral_coin_set.insert(2);
  setting->user_enabled_collateral_coin_set.insert(3);

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::deposit);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::DepositReq&>(request->walletreqgroupbody().depositreq());

  req.set_coin(ECoin::USDT);
  req.set_amount("********0");
  req.set_bonuschange("10");
  req.set_wallet_record_type(99999);
  req.set_transid("trans_id_1");

  EXPECT_FALSE(draft_pkg->ReadOnlyUserSetting()->cur_setting->IsSpotBanTradeAll());

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();

  auto wallet_commit = draft_pkg->affect_wallets_.front();
  auto wallet_ptr = wallet_commit.current_wallet;
  auto tran_log = draft_pkg->ref_asset_trans_logs_.front();

  EXPECT_EQ(tran_log->pre_cash_balance_, bbase::decimal::Decimal<>("1000"));
  EXPECT_EQ(tran_log->settle_coin_, ECoin::USDT);
  EXPECT_EQ(tran_log->req_id_, "deposit_1");
  EXPECT_EQ(tran_log->trans_id_, "trans_id_1");
  EXPECT_EQ(tran_log->cash_balance_, bbase::decimal::Decimal<>("*********"));
  EXPECT_EQ(tran_log->cash_flow_, bbase::decimal::Decimal<>("********0"));
  EXPECT_EQ(tran_log->change_, bbase::decimal::Decimal<>("********0"));
  EXPECT_EQ(tran_log->cross_seq_, 999);
  EXPECT_EQ(tran_log->trans_type_, 99999);
  EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_balance_), bbase::decimal::Decimal<>("510"));
  EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_change_), bbase::decimal::Decimal<>("10"));
  EXPECT_EQ(tran_log->account_mode_, EAccountMode::UnKnown);

  EXPECT_TRUE(draft_pkg->affected_setting_.need_to_persist);
  EXPECT_TRUE(draft_pkg->ReadOnlyUserSetting()->cur_setting->IsSpotBanTradeAll());

  auto resp = std::make_shared<svc::unified_v2::UnifiedV2ResultDTO>();
  convertor::ToPB::ConvertToUnifiedV2ResultDTO(draft_pkg.get(), resp.get(), EProductType::Margin);
  std::string msg{};
  LOG_INFO("MarginResult, {}", utils::PbMessageToJsonString(*resp, &msg));

  LOG_INFO("DepositService_ban_trade_1000w Done");
}

TEST_F(WalletTest, ban_trade_DepositService_white_list) {
  // 用户在白名单中， 不会触发 ban trade
  config::ConfigProxy::Instance().BanTradeWhiteListUidConfigReceiveHandler("1001");
  usleep(10 * 1000);

  const std::shared_ptr<biz::DepositService> service = std::make_shared<biz::DepositService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = setting;
  setting->recalculate_initiated_ = true;
  setting->user_enabled_collateral_coin_set.insert(1);
  setting->user_enabled_collateral_coin_set.insert(2);
  setting->user_enabled_collateral_coin_set.insert(3);

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::deposit);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::DepositReq&>(request->walletreqgroupbody().depositreq());

  req.set_coin(ECoin::USDT);
  req.set_amount("********");
  req.set_bonuschange("10");
  req.set_wallet_record_type(99999);
  req.set_transid("trans_id_1");

  EXPECT_FALSE(draft_pkg->ReadOnlyUserSetting()->cur_setting->IsSpotBanTradeAll());

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();

  auto wallet_commit = draft_pkg->affect_wallets_.front();
  auto wallet_ptr = wallet_commit.current_wallet;
  auto tran_log = draft_pkg->ref_asset_trans_logs_.front();

  EXPECT_EQ(tran_log->pre_cash_balance_, bbase::decimal::Decimal<>("1000"));
  EXPECT_EQ(tran_log->settle_coin_, ECoin::USDT);
  EXPECT_EQ(tran_log->req_id_, "deposit_1");
  EXPECT_EQ(tran_log->trans_id_, "trans_id_1");
  EXPECT_EQ(tran_log->cash_balance_, bbase::decimal::Decimal<>("********"));
  EXPECT_EQ(tran_log->cash_flow_, bbase::decimal::Decimal<>("********"));
  EXPECT_EQ(tran_log->change_, bbase::decimal::Decimal<>("********"));
  EXPECT_EQ(tran_log->cross_seq_, 999);
  EXPECT_EQ(tran_log->trans_type_, 99999);
  EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_balance_), bbase::decimal::Decimal<>("510"));
  EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_change_), bbase::decimal::Decimal<>("10"));
  EXPECT_EQ(tran_log->account_mode_, EAccountMode::UnKnown);

  EXPECT_FALSE(draft_pkg->affected_setting_.need_to_persist);
  EXPECT_FALSE(draft_pkg->ReadOnlyUserSetting()->cur_setting->IsSpotBanTradeAll());

  auto resp = std::make_shared<svc::unified_v2::UnifiedV2ResultDTO>();
  convertor::ToPB::ConvertToUnifiedV2ResultDTO(draft_pkg.get(), resp.get(), EProductType::Margin);
  EXPECT_TRUE(resp->asset_margin_result().per_user_setting_data().default_disable_collateral_coin());
  std::string msg{};
  LOG_INFO("MarginResult, {}", utils::PbMessageToJsonString(*resp, &msg));

  LOG_INFO("DepositService_ban_trade_white_list Done");
}

TEST_F(WalletTest, WalletDeposit) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::Deposit);
  auto* deposit_body = request->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("1000");
  deposit_body->set_bonuschange("201");
  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 0);
  EXPECT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(5)->Latest()->wallet_balance.ToDouble(), 1000);

  // 再冲500后校验
  req_header->set_req_id("deposit_1001_2");
  deposit_body->set_amount("500");
  draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());
  EXPECT_DOUBLE_EQ(draft_pkg->GetOrAttachSettleCoinWallet(5)->Latest()->wallet_balance.ToDouble(), 1500);
  EXPECT_DOUBLE_EQ(draft_pkg->GetOrAttachUnifyWallet()->Latest()->wallet_balance.ToDouble(), 1501.********00002);
  ASSERT_EQ(ret, 0);

  // 幂等场景
  std::vector<biz::req_id_t> req_id_list;
  req_id_list.push_back(biz::req_id_t("deposit_1001_2"));
  ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 3200102);

  // bbase::decimal::Decimal<> amount = decimal::kTen;
  // biz::WalletServiceFactory::BuildService()->Deposit(draft_pkg.get(), 5, amount);

  std::cout << "Wallet Deposit Test done" << std::endl;
}

// 非法金额
TEST_F(WalletTest, WalletDepositForAmountIllegal) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::Deposit);
  auto* deposit_body = request->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("none");
  deposit_body->set_bonuschange("def");
  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 10001);
}

// bouns > amount
TEST_F(WalletTest, WalletDepositError1) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::Deposit);
  auto* deposit_body = request->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("100000");
  deposit_body->set_bonuschange("800000");
  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 10001);
  std::cout << "Wallet Deposit Test done" << std::endl;
}

// amount<=0
TEST_F(WalletTest, WalletDepositError2) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::Deposit);
  auto* deposit_body = request->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("0");
  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 10001);
  std::cout << "Wallet Deposit Test done" << std::endl;
}

// bonus>50000,拦截
TEST_F(WalletTest, WalletDepositError3) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::Deposit);
  auto* deposit_body = request->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("800000");
  deposit_body->set_bonuschange("800000");
  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 10001);
  std::cout << "Wallet Deposit Test done" << std::endl;
}

// bonus<50000且bonus>10000
TEST_F(WalletTest, WalletDepositWithBonusSuccess) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::Deposit);
  auto* deposit_body = request->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("40000");
  deposit_body->set_bonuschange("40000");
  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 0);
  std::cout << "Wallet Deposit Test done" << std::endl;
}

// bonus=-100
TEST_F(WalletTest, WalletDepositWithBonus) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::Deposit);
  auto* deposit_body = request->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("40000");
  deposit_body->set_bonuschange("-100");
  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 10001);
  std::cout << "Wallet Deposit Test done" << std::endl;
}

// 不传bonus,bonus= 0,正常划转成功
TEST_F(WalletTest, WalletDepositWithOutBonus) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::Deposit);
  auto* deposit_body = request->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("40000");
  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 0);
  std::cout << "Wallet Deposit Test done" << std::endl;
}

// bonus>0 , 充值USDC
TEST_F(WalletTest, BonusDepositCoinUsdc) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDC);
  req_header->set_action(EAction::Deposit);
  auto* deposit_body = request->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDC);
  deposit_body->set_amount("40000");
  deposit_body->set_bonuschange("40000");

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 0);
  std::cout << "Wallet Deposit Test done" << std::endl;
}

// bonus > 0 ,充值BTC，失败
TEST_F(WalletTest, BonusDepositCoinBtc) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::BTC);
  req_header->set_action(EAction::Deposit);
  auto* deposit_body = request->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::BTC);
  deposit_body->set_amount("40000");
  deposit_body->set_bonuschange("40000");
  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 10001);
  std::cout << "Wallet Deposit Test done" << std::endl;
}

TEST_F(WalletTest, DepositService) {
  const std::shared_ptr<biz::DepositService> service = std::make_shared<biz::DepositService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = setting;
  setting->recalculate_initiated_ = true;
  setting->user_enabled_collateral_coin_set.insert(1);
  setting->user_enabled_collateral_coin_set.insert(2);
  setting->user_enabled_collateral_coin_set.insert(3);

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::deposit);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::DepositReq&>(request->walletreqgroupbody().depositreq());

  req.set_coin(ECoin::USDT);
  req.set_amount("100");
  req.set_bonuschange("10");
  req.set_wallet_record_type(99999);
  req.set_transid("trans_id_1");

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();

  auto wallet_commit = draft_pkg->affect_wallets_.front();
  auto wallet_ptr = wallet_commit.current_wallet;
  auto tran_log = draft_pkg->ref_asset_trans_logs_.front();

  EXPECT_EQ(tran_log->pre_cash_balance_, bbase::decimal::Decimal<>("1000"));
  EXPECT_EQ(tran_log->settle_coin_, ECoin::USDT);
  EXPECT_EQ(tran_log->req_id_, "deposit_1");
  EXPECT_EQ(tran_log->trans_id_, "trans_id_1");
  EXPECT_EQ(tran_log->cash_balance_, bbase::decimal::Decimal<>("1100"));
  EXPECT_EQ(tran_log->cash_flow_, bbase::decimal::Decimal<>("100"));
  EXPECT_EQ(tran_log->change_, bbase::decimal::Decimal<>("100"));
  EXPECT_EQ(tran_log->cross_seq_, 999);
  EXPECT_EQ(tran_log->trans_type_, 99999);
  EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_balance_), bbase::decimal::Decimal<>("510"));
  EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_change_), bbase::decimal::Decimal<>("10"));
  EXPECT_EQ(tran_log->account_mode_, EAccountMode::UnKnown);

  EXPECT_FALSE(draft_pkg->affected_setting_.need_to_persist);
  EXPECT_FALSE(draft_pkg->ReadOnlyUserSetting()->cur_setting->IsSpotBanTradeAll());

  auto resp = std::make_shared<svc::unified_v2::UnifiedV2ResultDTO>();
  convertor::ToPB::ConvertToUnifiedV2ResultDTO(draft_pkg.get(), resp.get(), EProductType::Margin);
  EXPECT_TRUE(resp->asset_margin_result().per_user_setting_data().default_disable_collateral_coin());
  std::string msg{};
  LOG_INFO("MarginResult, {}", utils::PbMessageToJsonString(*resp, &msg));

  LOG_INFO("DepositService Done");
}

TEST_F(WalletTest, DepositService_big_money) {
  const std::shared_ptr<biz::DepositService> service = std::make_shared<biz::DepositService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  per_user_store->user_setting_ = std::make_shared<store::CowSetting>();
  auto setting = std::make_shared<store::Setting>();
  per_user_store->user_setting_->cur_setting = setting;
  setting->recalculate_initiated_ = true;
  setting->user_enabled_collateral_coin_set.insert(1);
  setting->user_enabled_collateral_coin_set.insert(2);
  setting->user_enabled_collateral_coin_set.insert(3);

  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::deposit);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::DepositReq&>(request->walletreqgroupbody().depositreq());

  req.set_coin(ECoin::USDT);
  req.set_amount("12345678901234567890123456789010");
  req.set_bonuschange("10");
  req.set_wallet_record_type(99999);
  req.set_transid("trans_id_1");

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, error::kRequestCheckException);

  LOG_INFO("DepositService_big_money Done");
}

TEST_F(WalletTest, WithdrawService) {
  const std::shared_ptr<biz::WithdrawService> service = std::make_shared<biz::WithdrawService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("withdraw_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::withdraw);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::WithdrawReq&>(request->walletreqgroupbody().withdrawreq());

  req.set_coin(ECoin::USDT);
  req.set_amount("100");
  req.set_bonuschange("100");
  req.set_wallet_record_type(99999);
  req.set_trans_id("trans_id_1");

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();

  auto wallet_commit = draft_pkg->affect_wallets_.front();
  auto wallet_ptr = wallet_commit.current_wallet;
  auto tran_log = draft_pkg->ref_asset_trans_logs_.front();

  EXPECT_EQ(tran_log->pre_cash_balance_, bbase::decimal::Decimal<>("1000"));
  EXPECT_EQ(tran_log->settle_coin_, ECoin::USDT);
  EXPECT_EQ(tran_log->req_id_, "withdraw_1");
  EXPECT_EQ(tran_log->trans_id_, "trans_id_1");
  EXPECT_EQ(tran_log->cash_balance_, bbase::decimal::Decimal<>("900"));
  EXPECT_EQ(tran_log->cash_flow_, bbase::decimal::Decimal<>("-100"));
  EXPECT_EQ(tran_log->change_, bbase::decimal::Decimal<>("-100"));
  EXPECT_EQ(tran_log->cross_seq_, 999);
  EXPECT_EQ(tran_log->trans_type_, 99999);
  EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_balance_), bbase::decimal::Decimal<>("400"));
  EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_change_), bbase::decimal::Decimal<>("-100"));
  EXPECT_EQ(tran_log->account_mode_, EAccountMode::UnKnown);

  LOG_INFO("WithdrawService Done");
}

TEST_F(WalletTest, WithdrawService_big_money) {
  const std::shared_ptr<biz::WithdrawService> service = std::make_shared<biz::WithdrawService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("withdraw_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::withdraw);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::WithdrawReq&>(request->walletreqgroupbody().withdrawreq());

  req.set_coin(ECoin::USDT);
  req.set_amount("12345678901234567890123456789010");
  req.set_bonuschange("12345678901234567890123456789010");
  req.set_wallet_record_type(99999);
  req.set_trans_id("trans_id_1");

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, error::kRequestCheckException);

  LOG_INFO("WithdrawService_big_money Done");
}

TEST_F(WalletTest, WithdrawService_without_bonus) {
  const std::shared_ptr<biz::WithdrawService> service = std::make_shared<biz::WithdrawService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("withdraw_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::withdraw);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");
  draft_raw_wallet.base_margin_balance = bbase::decimal::Decimal<>("1000");

  auto& req = const_cast<svc::unified_v2::req::wallet::WithdrawReq&>(request->walletreqgroupbody().withdrawreq());

  req.set_coin(ECoin::USDT);
  req.set_amount("100");
  req.set_wallet_record_type(99999);
  req.set_trans_id("trans_id_1");

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();

  auto wallet_commit = draft_pkg->affect_wallets_.front();
  auto wallet_ptr = wallet_commit.current_wallet;
  auto tran_log = draft_pkg->ref_asset_trans_logs_.front();

  EXPECT_EQ(tran_log->pre_cash_balance_, bbase::decimal::Decimal<>("1000"));
  EXPECT_EQ(tran_log->settle_coin_, ECoin::USDT);
  EXPECT_EQ(tran_log->req_id_, "withdraw_1");
  EXPECT_EQ(tran_log->trans_id_, "trans_id_1");
  EXPECT_EQ(tran_log->cash_balance_, bbase::decimal::Decimal<>("900"));
  EXPECT_EQ(tran_log->cash_flow_, bbase::decimal::Decimal<>("-100"));
  EXPECT_EQ(tran_log->change_, bbase::decimal::Decimal<>("-100"));
  EXPECT_EQ(tran_log->cross_seq_, 999);
  EXPECT_EQ(tran_log->trans_type_, 99999);
  EXPECT_EQ(tran_log->bonus_balance_, "");
  EXPECT_EQ(tran_log->bonus_change_, "");
  EXPECT_EQ(tran_log->account_mode_, EAccountMode::UnKnown);
  EXPECT_EQ(tran_log->transfer_extra_info_, "");

  LOG_INFO("WithdrawService_without_bonus Done");
}

TEST_F(WalletTest, WithdrawService_pre_market) {
  const std::shared_ptr<biz::WithdrawService> service = std::make_shared<biz::WithdrawService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  // 设置强平中
  auto latest_acc = const_cast<store::Account*>(per_user_store->account_info_->Latest());
  latest_acc->account_status = enums::eaccountstatus::Liq;
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("withdraw_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::OMG);
  req_header->set_action(EAction::withdraw);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::OMG);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req = const_cast<svc::unified_v2::req::wallet::WithdrawReq&>(request->walletreqgroupbody().withdrawreq());
  // 非保证金币种 ， 强平中 也可以顺利 withdraw
  req.set_coin(ECoin::OMG);
  req.set_amount("100");
  req.set_bonuschange("100");
  req.set_wallet_record_type(99999);
  req.set_trans_id("trans_id_1");
  req.set_transfer_extra_info("transfer_extra_info1");

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();

  auto wallet_commit = draft_pkg->affect_wallets_.front();
  auto wallet_ptr = wallet_commit.current_wallet;
  auto tran_log = draft_pkg->ref_asset_trans_logs_.front();

  EXPECT_EQ(tran_log->pre_cash_balance_, bbase::decimal::Decimal<>("1000"));
  EXPECT_EQ(tran_log->settle_coin_, ECoin::OMG);
  EXPECT_EQ(tran_log->req_id_, "withdraw_1");
  EXPECT_EQ(tran_log->trans_id_, "trans_id_1");
  EXPECT_EQ(tran_log->cash_balance_, bbase::decimal::Decimal<>("900"));
  EXPECT_EQ(tran_log->cash_flow_, bbase::decimal::Decimal<>("-100"));
  EXPECT_EQ(tran_log->change_, bbase::decimal::Decimal<>("-100"));
  EXPECT_EQ(tran_log->cross_seq_, 999);
  EXPECT_EQ(tran_log->trans_type_, 99999);
  EXPECT_EQ(tran_log->transfer_extra_info_, req.transfer_extra_info());
  EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_balance_), bbase::decimal::Decimal<>("400"));
  EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_change_), bbase::decimal::Decimal<>("-100"));
  EXPECT_EQ(tran_log->account_mode_, EAccountMode::UnKnown);

  LOG_INFO("WithdrawService_pre_market Done");
}

// 币种不支持
TEST_F(WalletTest, coin_not_support_deposit) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::Coin_INT_MAX_SENTINEL_DO_NOT_USE_);
  req_header->set_action(EAction::Deposit);
  auto* deposit_body = request->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::Coin_INT_MAX_SENTINEL_DO_NOT_USE_);
  deposit_body->set_amount("1000");
  deposit_body->set_bonuschange("201");
  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());

  ASSERT_EQ(ret, 3200227);

  std::cout << "Wallet Deposit Test done" << std::endl;
}

int32_t test_rate_limit(const std::shared_ptr<biz::DepositService>& service,
                        std::shared_ptr<store::PerWorkerStore> workerStore, std::int64_t user_id) {
  auto per_user_store = std::make_shared<store::PerUserStore>(user_id, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = user_id;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1");
  req_header->set_user_id(user_id);
  req_header->set_coin(enums::ecoin::USDT);
  req_header->set_action(enums::eaction::deposit);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      user_id, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(enums::ecoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");

  auto& req = const_cast<svc::unified_v2::req::wallet::DepositReq&>(request->walletreqgroupbody().depositreq());

  req.set_coin(enums::ecoin::USDT);
  req.set_amount("100");
  req.set_wallet_record_type(1);

  return service->process(draft_pkg.get(), request);
}

int32_t test_withdraw_rate_limit(const std::shared_ptr<biz::WithdrawService>& service,
                                 std::shared_ptr<store::PerWorkerStore> workerStore, std::int64_t user_id) {
  auto per_user_store = std::make_shared<store::PerUserStore>(user_id, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  header->uid = user_id;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("withdraw_1");
  req_header->set_user_id(user_id);
  req_header->set_coin(enums::ecoin::USDT);
  req_header->set_action(enums::eaction::withdraw);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      user_id, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(enums::ecoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");

  auto& req = const_cast<svc::unified_v2::req::wallet::WithdrawReq&>(request->walletreqgroupbody().withdrawreq());

  req.set_coin(enums::ecoin::USDT);
  req.set_amount("1");
  req.set_wallet_record_type(2);

  return service->process(draft_pkg.get(), request);
}

TEST_F(WalletTest, DepositService_ratelimit) {
  const std::shared_ptr<biz::DepositService> service = std::make_shared<biz::DepositService>();

  const std::shared_ptr<biz::WithdrawService> withdraw_service = std::make_shared<biz::WithdrawService>();

  application::GlobalVarManager::Instance().set_pre_threads(100);
  auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "wallet_rate_limit_config_Deposit", "1");
  nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "wallet_rate_limit_config_Withdraw", "3");
  nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "turn_on_wallet_rate_limit", "TRUE");
  // 第一次 rate limiter null
  EXPECT_EQ(test_rate_limit(service, workerStore, 1001), 0);
  // 第二次 rate limiter pass
  EXPECT_EQ(test_rate_limit(service, workerStore, 1001), 0);
  // 第三次 rate limiter trigger
  EXPECT_EQ(test_rate_limit(service, workerStore, 1001), error::kTriggerRateLimit);

  // 2个 service 之间不会互相影响
  EXPECT_EQ(test_withdraw_rate_limit(withdraw_service, workerStore, 1001), 0);
  EXPECT_EQ(test_withdraw_rate_limit(withdraw_service, workerStore, 1001), 0);
  EXPECT_EQ(test_withdraw_rate_limit(withdraw_service, workerStore, 1001), 0);
  EXPECT_EQ(test_withdraw_rate_limit(withdraw_service, workerStore, 1001), 0);
  EXPECT_EQ(test_withdraw_rate_limit(withdraw_service, workerStore, 1001), error::kTriggerRateLimit);

  usleep(1000 * 1000);
  EXPECT_EQ(test_rate_limit(service, workerStore, 1001), 0);
  EXPECT_EQ(test_withdraw_rate_limit(withdraw_service, workerStore, 1001), 0);

  LOG_INFO("DepositService_ratelimit Done");
}

TEST_F(WalletTest, DepositService_ratelimit_5) {
  const std::shared_ptr<biz::DepositService> service = std::make_shared<biz::DepositService>();

  application::GlobalVarManager::Instance().set_pre_threads(100);
  auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);

  nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "wallet_rate_limit_config_Deposit", "5");
  nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "turn_on_wallet_rate_limit", "TRUE");

  EXPECT_EQ(test_rate_limit(service, workerStore, 1001), 0);
  for (int i = 0; i < 5; i++) {
    EXPECT_EQ(test_rate_limit(service, workerStore, 1001), 0);
  }

  EXPECT_EQ(test_rate_limit(service, workerStore, 1001), error::kTriggerRateLimit);

  LOG_INFO("DepositService_ratelimit_5 Done");
}

TEST_F(WalletTest, DepositService_ratelimit_0) {
  const std::shared_ptr<biz::DepositService> service = std::make_shared<biz::DepositService>();

  application::GlobalVarManager::Instance().set_pre_threads(100);
  auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);

  nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "wallet_rate_limit_config_Deposit", "0");
  nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "turn_on_wallet_rate_limit", "TRUE");

  EXPECT_EQ(test_rate_limit(service, workerStore, 1001), 0);
  for (int i = 0; i < 500; i++) {
    EXPECT_EQ(test_rate_limit(service, workerStore, 1001), 0);
  }

  EXPECT_EQ(test_rate_limit(service, workerStore, 1001), 0);

  LOG_INFO("DepositService_ratelimit_0 Done");
}

TEST_F(WalletTest, DepositService_RequestIdempotent) {
  const std::shared_ptr<biz::DepositService> service = std::make_shared<biz::DepositService>();

  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  {
    auto resource = std::make_shared<store::UnicastResource>();
    auto header = std::make_shared<store::Header>();
    header->uid = 1001;
    resource->header = header;
    resource->per_user_store = per_user_store.get();
    resource->config_manager = config::getTlsCfgMgrRaw();
    resource->per_worker_store = workerStore.get();

    auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
    auto* req_header = request->mutable_req_header();
    req_header->set_req_id("deposit_1");
    req_header->set_user_id(1001);
    req_header->set_coin(enums::ecoin::USDT);
    req_header->set_action(enums::eaction::deposit);
    req_header->set_cross_seq(999);

    auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
        1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
        event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
    auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
    auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(enums::ecoin::USDT);
    auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
    draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
    draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

    auto& req = const_cast<svc::unified_v2::req::wallet::DepositReq&>(request->walletreqgroupbody().depositreq());

    req.set_coin(enums::ecoin::USDT);
    req.set_amount("100");
    req.set_bonuschange("10");
    req.set_wallet_record_type(99999);
    req.set_transid("trans_id_1");

    int32_t ret = service->process(draft_pkg.get(), request);
    ASSERT_EQ(ret, 0);
    draft_pkg->CommitAll();

    auto wallet_commit = draft_pkg->affect_wallets_.front();
    auto wallet_ptr = wallet_commit.current_wallet;
    auto tran_log = draft_pkg->ref_asset_trans_logs_.front();

    EXPECT_EQ(tran_log->pre_cash_balance_, bbase::decimal::Decimal<>("1000"));
    EXPECT_EQ(tran_log->settle_coin_, enums::ecoin::USDT);
    EXPECT_EQ(tran_log->req_id_, "deposit_1");
    EXPECT_EQ(tran_log->trans_id_, "trans_id_1");
    EXPECT_EQ(tran_log->cash_balance_, bbase::decimal::Decimal<>("1100"));
    EXPECT_EQ(tran_log->cash_flow_, bbase::decimal::Decimal<>("100"));
    EXPECT_EQ(tran_log->change_, bbase::decimal::Decimal<>("100"));
    EXPECT_EQ(tran_log->cross_seq_, 999);
    EXPECT_EQ(tran_log->trans_type_, 99999);
    EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_balance_), bbase::decimal::Decimal<>("510"));
    EXPECT_EQ(bbase::decimal::Decimal<>(tran_log->bonus_change_), bbase::decimal::Decimal<>("10"));
    EXPECT_EQ(tran_log->account_mode_, EAccountMode::UnKnown);
  }

  {
    auto resource = std::make_shared<store::UnicastResource>();
    auto header = std::make_shared<store::Header>();
    header->uid = 1001;
    resource->header = header;
    resource->per_user_store = per_user_store.get();
    resource->config_manager = config::getTlsCfgMgrRaw();
    resource->per_worker_store = workerStore.get();

    auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
    auto* req_header = request->mutable_req_header();
    req_header->set_req_id("deposit_1");
    req_header->set_user_id(1001);
    req_header->set_coin(enums::ecoin::USDT);
    req_header->set_action(enums::eaction::deposit);
    req_header->set_cross_seq(999);

    auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
        1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
        event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
    auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
    auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(enums::ecoin::USDT);
    auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
    draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
    draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

    auto& req = const_cast<svc::unified_v2::req::wallet::DepositReq&>(request->walletreqgroupbody().depositreq());

    req.set_coin(enums::ecoin::USDT);
    req.set_amount("100");
    req.set_bonuschange("10");
    req.set_wallet_record_type(99999);
    req.set_transid("trans_id_1");

    int32_t ret = service->process(draft_pkg.get(), request);
    ASSERT_EQ(ret, error::kRequestIdempotent);
  }

  LOG_INFO("DepositService_RequestIdempotent Done");
}

// 限流开关 turn_on_wallet_rate_limiter 关闭的case
TEST_F(WalletTest, DepositService_ratelimit_switch_off) {
  const std::shared_ptr<biz::DepositService> service = std::make_shared<biz::DepositService>();

  const std::shared_ptr<biz::WithdrawService> withdraw_service = std::make_shared<biz::WithdrawService>();

  application::GlobalVarManager::Instance().set_pre_threads(100);
  auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "wallet_rate_limit_config_Deposit", "1");
  nacos_client->SetStringVar(config::ConstConfig::MARGIN_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "wallet_rate_limit_config_Withdraw", "3");
  // 第一次 rate limiter null
  EXPECT_EQ(test_rate_limit(service, workerStore, 1001), 0);
  // 第二次 rate limiter pass
  EXPECT_EQ(test_rate_limit(service, workerStore, 1001), 0);
  // 第三次 rate limiter pass
  EXPECT_EQ(test_rate_limit(service, workerStore, 1001), 0);

  // 2个 service 之间不会互相影响
  EXPECT_EQ(test_withdraw_rate_limit(withdraw_service, workerStore, 1001), 0);
  EXPECT_EQ(test_withdraw_rate_limit(withdraw_service, workerStore, 1001), 0);
  EXPECT_EQ(test_withdraw_rate_limit(withdraw_service, workerStore, 1001), 0);
  EXPECT_EQ(test_withdraw_rate_limit(withdraw_service, workerStore, 1001), 0);
  EXPECT_EQ(test_withdraw_rate_limit(withdraw_service, workerStore, 1001), 0);

  usleep(1000 * 1000);
  EXPECT_EQ(test_rate_limit(service, workerStore, 1001), 0);
  EXPECT_EQ(test_withdraw_rate_limit(withdraw_service, workerStore, 1001), 0);

  LOG_INFO("DepositService_ratelimit_switch_off Done");
}

TEST_F(WalletTest, check_default_field_value) {
  const std::shared_ptr<biz::DepositService> service = std::make_shared<biz::DepositService>();

  store::TransLog* trans_log = new store::TransLog;
  trans_log->trans_time_ = 3;
  EXPECT_EQ(trans_log->trans_time_, 3);

  auto* trans_log2 = new (trans_log) store::TransLog;
  EXPECT_EQ(trans_log2->trans_time_, 3);

  LOG_INFO("check_default_field_value Done2");
}

TEST_F(WalletTest, big_money_value) {
  EXPECT_TRUE(biz::WalletService::is_money_overflow("123456789012345678901234567890.12345678901234567890"));
  EXPECT_TRUE(biz::WalletService::is_money_overflow("-123456789012345678901234567890.12345678901234567890"));

  EXPECT_FALSE(biz::WalletService::is_money_overflow("123456789012345678901.12345678901234567890"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("-123456789012345678901.12345678901234567890"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("12345678901234567890.12345678901234567890"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("12345678901234567890.123456789012345678901234567890"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("12345678901234567890.1234567890123456789012345678901234567890"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("-12345678901234567890.12345678901234567890"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("99999999999999999"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("0.12345678901234567890"));

  EXPECT_FALSE(biz::WalletService::is_money_overflow("2500.000000000000000000"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("72.819800000000000000"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("3982.733700000000000000"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("4000.000000000000000000"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("1.000000000000000000"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("0.***********0000000"));

  EXPECT_FALSE(biz::WalletService::is_money_overflow("-2500.000000000000000000"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("-72.819800000000000000"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("-3982.733700000000000000"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("-4000.000000000000000000"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("-1.000000000000000000"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("-0.***********0000000"));

  EXPECT_FALSE(biz::WalletService::is_money_overflow("1"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("-1"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("00"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("0.1"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("-0.1"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("0.123456789012345678901234567890"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("0.123456789012345678901234567890"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("0.99999999999999999999999"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("-0.99999999999999999999999"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("12345678901234567890.99999999999999999999999"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("-12345678901234567890.99999999999999999999999"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("11111.99999999999999999999999"));
  EXPECT_FALSE(biz::WalletService::is_money_overflow("-11111.99999999999999999999999"));
}

TEST_F(WalletTest, BalanceConvert) {
  const std::shared_ptr<biz::BalanceConvertService> service = std::make_shared<biz::BalanceConvertService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("balanceconvertreq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::BalanceConvert);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.account_mode = EAccountMode::Isolated;
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req =
      const_cast<svc::unified_v2::req::wallet::BalanceConvertReq&>(request->walletreqgroupbody().balanceconvertreq());

  req.set_from_coin(ECoin::USDT);
  req.set_from_amount("100");
  req.set_to_coin(ECoin::USDC);
  req.set_to_amount("101");
  req.set_trans_id("trans_id_1");
  req.set_convert_type(0);

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();

  auto wallet_commit = draft_pkg->affect_wallets_.front();
  auto wallet_ptr = wallet_commit.current_wallet;
  auto tran_log = draft_pkg->ref_asset_trans_logs_[0];

  EXPECT_EQ(tran_log->pre_cash_balance_, bbase::decimal::Decimal<>("1000"));
  EXPECT_EQ(tran_log->settle_coin_, ECoin::USDT);
  EXPECT_EQ(tran_log->index_, 0);
  EXPECT_EQ(tran_log->req_id_, "balanceconvertreq_1");
  EXPECT_EQ(tran_log->trans_id_, "trans_id_10");
  EXPECT_EQ(tran_log->cash_balance_, bbase::decimal::Decimal<>("900"));
  EXPECT_EQ(tran_log->cash_flow_, bbase::decimal::Decimal<>("-100"));
  EXPECT_EQ(tran_log->change_, bbase::decimal::Decimal<>("-100"));
  EXPECT_EQ(tran_log->cross_seq_, 999);
  EXPECT_EQ(tran_log->trans_type_, store::TransLog::TransTypeEnum::kTransTypeExchangeOut);
  EXPECT_EQ(tran_log->account_mode_, EAccountMode::Isolated);

  auto tran_log_1 = draft_pkg->ref_asset_trans_logs_[1];

  EXPECT_EQ(tran_log_1->pre_cash_balance_, bbase::decimal::Decimal<>("0"));
  EXPECT_EQ(tran_log_1->settle_coin_, ECoin::USDC);
  EXPECT_EQ(tran_log_1->index_, 1);
  EXPECT_EQ(tran_log_1->req_id_, "balanceconvertreq_1");
  EXPECT_EQ(tran_log_1->trans_id_, "trans_id_11");
  EXPECT_EQ(tran_log_1->cash_balance_, bbase::decimal::Decimal<>("101"));
  EXPECT_EQ(tran_log_1->cash_flow_, bbase::decimal::Decimal<>("101"));
  EXPECT_EQ(tran_log_1->change_, bbase::decimal::Decimal<>("101"));
  EXPECT_EQ(tran_log_1->cross_seq_, 999);
  EXPECT_EQ(tran_log_1->trans_type_, store::TransLog::TransTypeEnum::kTransTypeExchangeIn);
  EXPECT_EQ(tran_log_1->account_mode_, EAccountMode::Isolated);

  LOG_INFO("BalanceConvert Done");
}

TEST_F(WalletTest, BalanceConvert_big_money) {
  const std::shared_ptr<biz::BalanceConvertService> service = std::make_shared<biz::BalanceConvertService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("balanceconvertreq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::BalanceConvert);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.account_mode = EAccountMode::Isolated;
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req =
      const_cast<svc::unified_v2::req::wallet::BalanceConvertReq&>(request->walletreqgroupbody().balanceconvertreq());

  req.set_from_coin(ECoin::USDT);
  req.set_from_amount("12345678901234567890123456789010");
  req.set_to_coin(ECoin::USDC);
  req.set_to_amount("101");
  req.set_trans_id("trans_id_1");
  req.set_convert_type(0);

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, error::kRequestCheckException);

  LOG_INFO("BalanceConvert_big_money Done");
}

TEST_F(WalletTest, BalanceConvert_OTC) {
  const std::shared_ptr<biz::BalanceConvertService> service = std::make_shared<biz::BalanceConvertService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("balanceconvertreq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::BalanceConvert);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.account_mode = EAccountMode::Isolated;
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req =
      const_cast<svc::unified_v2::req::wallet::BalanceConvertReq&>(request->walletreqgroupbody().balanceconvertreq());

  req.set_from_coin(ECoin::USDT);
  req.set_from_amount("100");
  req.set_to_coin(ECoin::USDC);
  req.set_to_amount("101");
  req.set_trans_id("trans_id_1");
  req.set_convert_type(1);

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();

  auto wallet_commit = draft_pkg->affect_wallets_.front();
  auto wallet_ptr = wallet_commit.current_wallet;
  auto tran_log = draft_pkg->ref_asset_trans_logs_[0];

  EXPECT_EQ(tran_log->pre_cash_balance_, bbase::decimal::Decimal<>("1000"));
  EXPECT_EQ(tran_log->settle_coin_, ECoin::USDT);
  EXPECT_EQ(tran_log->index_, 0);
  EXPECT_EQ(tran_log->req_id_, "balanceconvertreq_1");
  EXPECT_EQ(tran_log->trans_id_, "trans_id_10");
  EXPECT_EQ(tran_log->cash_balance_, bbase::decimal::Decimal<>("900"));
  EXPECT_EQ(tran_log->cash_flow_, bbase::decimal::Decimal<>("-100"));
  EXPECT_EQ(tran_log->change_, bbase::decimal::Decimal<>("-100"));
  EXPECT_EQ(tran_log->cross_seq_, 999);
  EXPECT_EQ(tran_log->trans_type_, store::TransLog::TransTypeEnum::kTransTypeOTCExchangeOut);
  EXPECT_EQ(tran_log->account_mode_, EAccountMode::Isolated);

  auto tran_log_1 = draft_pkg->ref_asset_trans_logs_[1];

  EXPECT_EQ(tran_log_1->pre_cash_balance_, bbase::decimal::Decimal<>("0"));
  EXPECT_EQ(tran_log_1->settle_coin_, ECoin::USDC);
  EXPECT_EQ(tran_log_1->index_, 1);
  EXPECT_EQ(tran_log_1->req_id_, "balanceconvertreq_1");
  EXPECT_EQ(tran_log_1->trans_id_, "trans_id_11");
  EXPECT_EQ(tran_log_1->cash_balance_, bbase::decimal::Decimal<>("101"));
  EXPECT_EQ(tran_log_1->cash_flow_, bbase::decimal::Decimal<>("101"));
  EXPECT_EQ(tran_log_1->change_, bbase::decimal::Decimal<>("101"));
  EXPECT_EQ(tran_log_1->cross_seq_, 999);
  EXPECT_EQ(tran_log_1->trans_type_, store::TransLog::TransTypeEnum::kTransTypeOTCExchangeIn);
  EXPECT_EQ(tran_log_1->account_mode_, EAccountMode::Isolated);

  LOG_INFO("BalanceConvert_OTC Done");
}

TEST_F(WalletTest, BalanceConvert_kRequestIdempotent) {
  const std::shared_ptr<biz::BalanceConvertService> service = std::make_shared<biz::BalanceConvertService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("balanceconvertreq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::BalanceConvert);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.account_mode = EAccountMode::Isolated;
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req =
      const_cast<svc::unified_v2::req::wallet::BalanceConvertReq&>(request->walletreqgroupbody().balanceconvertreq());

  req.set_from_coin(ECoin::USDT);
  req.set_from_amount("100");
  req.set_to_coin(ECoin::USDC);
  req.set_to_amount("101");
  req.set_trans_id("trans_id_1");
  req.set_convert_type(0);

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();

  auto wallet_commit = draft_pkg->affect_wallets_.front();
  auto wallet_ptr = wallet_commit.current_wallet;
  auto tran_log = draft_pkg->ref_asset_trans_logs_[0];

  EXPECT_EQ(tran_log->pre_cash_balance_, bbase::decimal::Decimal<>("1000"));
  EXPECT_EQ(tran_log->settle_coin_, ECoin::USDT);
  EXPECT_EQ(tran_log->index_, 0);
  EXPECT_EQ(tran_log->req_id_, "balanceconvertreq_1");
  EXPECT_EQ(tran_log->trans_id_, "trans_id_10");
  EXPECT_EQ(tran_log->cash_balance_, bbase::decimal::Decimal<>("900"));
  EXPECT_EQ(tran_log->cash_flow_, bbase::decimal::Decimal<>("-100"));
  EXPECT_EQ(tran_log->change_, bbase::decimal::Decimal<>("-100"));
  EXPECT_EQ(tran_log->cross_seq_, 999);
  EXPECT_EQ(tran_log->trans_type_, store::TransLog::TransTypeEnum::kTransTypeExchangeOut);
  EXPECT_EQ(tran_log->account_mode_, EAccountMode::Isolated);

  auto tran_log_1 = draft_pkg->ref_asset_trans_logs_[1];

  EXPECT_EQ(tran_log_1->pre_cash_balance_, bbase::decimal::Decimal<>("0"));
  EXPECT_EQ(tran_log_1->settle_coin_, ECoin::USDC);
  EXPECT_EQ(tran_log_1->index_, 1);
  EXPECT_EQ(tran_log_1->req_id_, "balanceconvertreq_1");
  EXPECT_EQ(tran_log_1->trans_id_, "trans_id_11");
  EXPECT_EQ(tran_log_1->cash_balance_, bbase::decimal::Decimal<>("101"));
  EXPECT_EQ(tran_log_1->cash_flow_, bbase::decimal::Decimal<>("101"));
  EXPECT_EQ(tran_log_1->change_, bbase::decimal::Decimal<>("101"));
  EXPECT_EQ(tran_log_1->cross_seq_, 999);
  EXPECT_EQ(tran_log_1->trans_type_, store::TransLog::TransTypeEnum::kTransTypeExchangeIn);
  EXPECT_EQ(tran_log_1->account_mode_, EAccountMode::Isolated);

  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kRequestIdempotent);

  LOG_INFO("BalanceConvert_kRequestIdempotent Done");
}

TEST_F(WalletTest, BalanceConvert_validation) {
  const std::shared_ptr<biz::BalanceConvertService> service = std::make_shared<biz::BalanceConvertService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("balanceconvertreq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::BalanceConvert);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.account_mode = EAccountMode::Isolated;
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req =
      const_cast<svc::unified_v2::req::wallet::BalanceConvertReq&>(request->walletreqgroupbody().balanceconvertreq());

  req.set_from_coin(99999);
  req.set_from_amount("100");
  req.set_to_coin(ECoin::USDC);
  req.set_to_amount("101");
  req.set_trans_id("trans_id_1");
  req.set_convert_type(0);

  int32_t ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kEcWalletCoinNotSupport);

  req.set_from_coin(ECoin::USDT);
  req.set_to_coin(-1);

  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kEcWalletCoinNotSupport);

  req.set_to_coin(ECoin::USDC);

  req.set_from_amount("0");

  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kRequestCheckException);

  req.set_from_amount("100");
  req.set_to_amount("0");

  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kRequestCheckException);

  req.set_to_amount("101");
  req.set_to_coin(ECoin::USDT);
  req.set_from_coin(ECoin::USDT);
  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kRequestCheckException);

  LOG_INFO("BalanceConvert_validation Done");
}

TEST_F(WalletTest, BalanceConvert_validation_cm_liq) {
  const std::shared_ptr<biz::BalanceConvertService> service = std::make_shared<biz::BalanceConvertService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("balanceconvertreq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::BalanceConvert);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.account_mode = EAccountMode::Isolated;
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req =
      const_cast<svc::unified_v2::req::wallet::BalanceConvertReq&>(request->walletreqgroupbody().balanceconvertreq());

  req.set_from_coin(ECoin::USDT);
  req.set_from_amount("100");
  req.set_to_coin(ECoin::USDC);
  req.set_to_amount("101");
  req.set_trans_id("trans_id_1");
  req.set_convert_type(0);

  setting.all_trade_disabled = true;
  draft_pkg->GetOrAttachUserSetting()->CommitS(nullptr);
  int32_t ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kInstAllTradeDisabled);

  setting.all_trade_disabled = false;
  setting.account_mode = EAccountMode::Cross;
  per_user_store->account_info_->LazyDraftA()->account_status = enums::eaccountstatus::PendingAdl;
  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kAccountIsInLiq);

  per_user_store->account_info_->LazyDraftA()->account_status = enums::eaccountstatus::Normal;
  draft_pkg->GetOrAttachUnifyWallet()->LazyDraftW()->mm_ratio = 2.0;
  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kAccountIsInLiq);

  LOG_INFO("BalanceConvert_validation_cm_liq Done");
}

TEST_F(WalletTest, BalanceConvert_validation_im_liq) {
  const std::shared_ptr<biz::BalanceConvertService> service = std::make_shared<biz::BalanceConvertService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("balanceconvertreq_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::BalanceConvert);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.account_mode = EAccountMode::Isolated;
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  auto& req =
      const_cast<svc::unified_v2::req::wallet::BalanceConvertReq&>(request->walletreqgroupbody().balanceconvertreq());

  req.set_from_coin(ECoin::USDT);
  req.set_from_amount("100");
  req.set_to_coin(ECoin::USDC);
  req.set_to_amount("101");
  req.set_trans_id("trans_id_1");
  req.set_convert_type(0);

  auto coin = ECoin::USDT;
  auto symbol = ESymbol::BTCUSDT;
  auto pz_mode = EPositionMode::MergedSingle;
  auto pz_idx = EPositionIndex::Single;
  auto pz_status = EPositionStatus::LiqInProcessing;
  auto coin_store = std::make_shared<store::PerCoinStore>(1001, coin);
  draft_pkg->u_store->working_coins_[coin] = coin_store;

  auto symbol_store = std::make_shared<store::PerSymbolStore>(symbol, pz_mode);
  draft_pkg->u_store->working_coins_[coin]->working_future_symbols_[symbol] = symbol_store;

  auto pz = biz::commonbiz::LazyInitBiz::InitPzIdx(draft_pkg.get(), coin, symbol, pz_idx, pz_status);

  // EPositionIndex::Single
  coin_store->futures_positions_.emplace(store::PositionKey{.symbol_ = symbol, .position_index = 0}, pz.get());
  symbol_store->all_positions_[EPositionIndex::Single] = pz;
  symbol_store->all_positions_[EPositionIndex::Single]->LazyDraftP()->coin = coin;
  symbol_store->all_positions_[EPositionIndex::Single]->LazyDraftP()->size = 1;

  int32_t ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kAccountIsInLiq);

  LOG_INFO("BalanceConvert_validation_im_liq Done");
}

TEST_F(WalletTest, CustomeSpotHedge) {
  const std::shared_ptr<biz::CustomeSpotHedgeService> service = std::make_shared<biz::CustomeSpotHedgeService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("CustomeSpotHedge_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetPmSpotHedge);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.account_mode = EAccountMode::Isolated;
  EXPECT_EQ(setting.open_pm_spot_hedge, false);
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  request->mutable_walletreqgroupbody()->mutable_setpmspothedgereq()->set_open_spot_hedge(true);

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();

  auto affected_setting_ = draft_pkg->affected_setting_;
  EXPECT_EQ(affected_setting_.current_setting->open_pm_spot_hedge, true);

  LOG_INFO("CustomeSpotHedge Done");
}

TEST_F(WalletTest, CustomeSpotHedge_validation) {
  const std::shared_ptr<biz::CustomeSpotHedgeService> service = std::make_shared<biz::CustomeSpotHedgeService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("CustomeSpotHedge_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetPmSpotHedge);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.account_mode = EAccountMode::Isolated;
  EXPECT_EQ(setting.open_pm_spot_hedge, false);
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  EXPECT_EQ(service->process(draft_pkg.get(), request), error::kErrorCodeDefault);

  request->mutable_walletreqgroupbody()->mutable_setpmspothedgereq()->set_open_spot_hedge(true);

  setting.all_trade_disabled = true;
  draft_pkg->GetOrAttachUserSetting()->CommitS(nullptr);
  int32_t ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kInstAllTradeDisabled);

  setting.all_trade_disabled = false;
  setting.account_mode = EAccountMode::Cross;
  per_user_store->account_info_->LazyDraftA()->account_status = enums::eaccountstatus::PendingAdl;
  ret = service->process(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kAccountIsInLiq);

  LOG_INFO("CustomeSpotHedge_validation Done");
}

TEST_F(WalletTest, SetTriggerLiqLevel) {
  const std::shared_ptr<biz::SetTriggerLiqLevelService> service = std::make_shared<biz::SetTriggerLiqLevelService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  per_user_store->account_info_ = std::make_shared<store::CowAccount>();
  per_user_store->account_info_->cur_account = (std::make_shared<store::Account>());
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("SetTriggerLiqLevel_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::UpdateSettingLiqLevel);
  req_header->set_cross_seq(999);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.account_mode = EAccountMode::Isolated;
  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(ECoin::USDT);
  auto& draft_raw_wallet = *cow_wallet->LazyDraftW();
  draft_raw_wallet.wallet_balance = bbase::decimal::Decimal<>("1000");
  draft_raw_wallet.bonus = bbase::decimal::Decimal<>("500");

  request->mutable_walletreqgroupbody()->mutable_settriggerliqlevelreq()->set_trigger_liq_level(1);

  int32_t ret = service->processUpdateTriggerLiqLevel(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kErrorCodeIsolatedNotModified);

  setting.account_mode = EAccountMode::Cross;
  per_user_store->account_info_->LazyDraftA()->account_status = enums::eaccountstatus::PendingAdl;
  ret = service->processUpdateTriggerLiqLevel(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kAccountIsInLiq);

  per_user_store->account_info_->LazyDraftA()->account_status = enums::eaccountstatus::Normal;
  ret = service->processUpdateTriggerLiqLevel(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kErrorCodeParamsError);

  request->mutable_walletreqgroupbody()->mutable_settriggerliqlevelreq()->set_trigger_liq_level(100);

  ret = service->processUpdateTriggerLiqLevel(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kErrorCodeSuccess);
  EXPECT_EQ(setting.trigger_liq_level, 100);

  ret = service->processUpdateTriggerLiqLevel(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kErrorCodeSuccess);

  ret = service->processQueryTriggerLiqLevel(draft_pkg.get(), request);
  EXPECT_EQ(ret, error::kErrorCodeSuccess);

  LOG_INFO("SetTriggerLiqLevel Done");
}
