#include "test/biz/wallet_service_ut/spot_calculator/spot_buy_bankruptcy_price_test.hpp"

#include "biz_worker/service/trade/store/spot_order/cow_spot_order.hpp"
#include "src/biz_worker/service/wallet/rebalance/refresh_wallet_unit_service.hpp"
#include "src/biz_worker/service/wallet/wallet_service.hpp"

TEST_F(WalletTest, SpotBuyBankruptcyPriceV2) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("deposit_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::Deposit);
  auto* deposit_body = request->mutable_walletreqgroupbody()->mutable_depositreq();
  deposit_body->set_coin(ECoin::USDT);
  deposit_body->set_amount("1000");
  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());
  //  int32_t ret = biz::WalletServiceFactory::BuildService()->ProcessDeposit(draft_pkg.get(), nullptr);
  //  draft_pkg->CommitAll();

  ASSERT_EQ(ret, 0);

  // check settle wallet
  //  ASSERT_NE(draft_pkg->ref_cow_wallet_map_[5], nullptr);
  //  ASSERT_EQ(draft_pkg->ref_cow_wallet_map_[5]->LatestRef()->wallet().wallet_balance.ToDouble(), 1000);
  auto& margin_cost = *draft_pkg->CreateOrAttachSpotSymbolMarginCost(5, 101)->LazyDraftSMC();
  margin_cost.spot_order_digest.limit_order_amount = bbase::decimal::Decimal<>(1000);
  margin_cost.spot_order_digest.limit_order_qty = bbase::decimal::Decimal<>("0.035");
  //  biz::WalletServiceFactory::BuildService()->ProcessMarginRequest(biz_event, resource.get());
  biz::RefreshWalletUnitService::SyncMarginCostDelta(draft_pkg.get());

  auto spot_order = std::make_shared<store::SpotOrder>();
  spot_order->side = ESide::Buy;
  spot_order->symbol = 101;
  spot_order->exchange_symbol = 101;
  spot_order->amount = bbase::decimal::Decimal<>(1000);
  spot_order->order_status = EOrderStatus::Created;
  spot_order->base_coin = 1;
  spot_order->settle_coin = 5;
  auto order = std::make_shared<store::CowSpotOrder>();
  order->draft_order = (spot_order);

  ret = biz::SpotOrderCalculator::OrderBankruptcyPriceV2(draft_pkg.get(), spot_order.get());
  ASSERT_EQ(ret, 0);
  EXPECT_DOUBLE_EQ(draft_pkg->u_store->unify_wallet_unit_->Latest()->haircut.ToDouble(), 40.03************9);
  EXPECT_DOUBLE_EQ(spot_order->price.ToDouble(), ************);
  draft_pkg->CommitAll();
  std::cout << "Process spot BP Test done" << std::endl;
}
