//
// Created by SH00728ML on 2023/3/24.
//

#include "test/biz/wallet_service_ut/set_collateral_coin_mode/set_collateral_coin_mode_service_test.hpp"

#include <gtest/gtest.h>

#include <memory>

#include "biz_worker/service/wallet/wallet_service.hpp"
#include "models/usersettingdto/user_setting.pb.h"
#include "src/biz_worker/service/wallet/biz/account/custome_colleteral_coin_service.hpp"
#include "src/biz_worker/service/wallet/biz/account/set_collateral_coin_mode_service.hpp"
#include "src/biz_worker/service/wallet/biz/account/switch_special_node_service.hpp"
#include "src/biz_worker/service/wallet/rebalance/refresh_wallet_unit_service.hpp"
#include "src/biz_worker/utils/pb_convertor/from_pb.hpp"
#include "src/biz_worker/utils/pb_convertor/to_pb.hpp"

TEST_F(SetCollateralCoinModeServiceTest, wallet_service_RunBySingleUser) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetCollateralCoinMode);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->collateral_coin_mode = ECollateralCoinMode::Normal;

  auto& req = const_cast<svc::unified_v2::req::settings::SetCollateralCoinModeReq&>(
      request->settingsreqgroupbody().set_collateral_coin_mode_req());

  req.set_collateral_coin_mode(ECollateralCoinMode::InstLoan);
  int32_t ret = wallet_service->RunBySingleUser(biz_event, resource.get());
  ASSERT_EQ(ret, 0);
}

TEST_F(SetCollateralCoinModeServiceTest, normal_to_instloan) {
  const std::shared_ptr<biz::SetCollateralCoinModeService> set_collateral_coin_mode_service =
      std::make_shared<biz::SetCollateralCoinModeService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetCollateralCoinMode);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->collateral_coin_mode = ECollateralCoinMode::Normal;
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->user_enabled_collateral_coin_set.insert(ECoin::USDT);
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->user_enabled_collateral_coin_set.insert(ECoin::USDC);

  auto& req = const_cast<svc::unified_v2::req::settings::SetCollateralCoinModeReq&>(
      request->settingsreqgroupbody().set_collateral_coin_mode_req());

  req.set_collateral_coin_mode(ECollateralCoinMode::InstLoan);

  int32_t ret = set_collateral_coin_mode_service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();
  ASSERT_EQ(draft_pkg->affected_setting_.current_setting->user_enabled_collateral_coin_set.size(), 2);
  ASSERT_EQ(draft_pkg->affected_setting_.current_setting->collateral_coin_mode,
            static_cast<ECollateralCoinMode>(req.collateral_coin_mode()));

  LOG_INFO("normal_to_instloan test done");
}

TEST_F(SetCollateralCoinModeServiceTest, normal_to_instloan_Isolated) {
  const std::shared_ptr<biz::SetCollateralCoinModeService> set_collateral_coin_mode_service =
      std::make_shared<biz::SetCollateralCoinModeService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetCollateralCoinMode);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->collateral_coin_mode = ECollateralCoinMode::Normal;
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->user_enabled_collateral_coin_set.insert(ECoin::USDT);
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->user_enabled_collateral_coin_set.insert(ECoin::USDC);
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->account_mode = EAccountMode::Isolated;

  auto& req = const_cast<svc::unified_v2::req::settings::SetCollateralCoinModeReq&>(
      request->settingsreqgroupbody().set_collateral_coin_mode_req());

  req.set_collateral_coin_mode(ECollateralCoinMode::InstLoan);

  int32_t ret = set_collateral_coin_mode_service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();
  ASSERT_EQ(draft_pkg->affected_setting_.current_setting->user_enabled_collateral_coin_set.size(), 2);
  ASSERT_EQ(draft_pkg->affected_setting_.current_setting->collateral_coin_mode,
            static_cast<ECollateralCoinMode>(req.collateral_coin_mode()));

  LOG_INFO("normal_to_instloan test done");
}

TEST_F(SetCollateralCoinModeServiceTest, instloan_to_normal) {
  const std::shared_ptr<biz::SetCollateralCoinModeService> set_collateral_coin_mode_service =
      std::make_shared<biz::SetCollateralCoinModeService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetCollateralCoinMode);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->collateral_coin_mode = ECollateralCoinMode::InstLoan;

  auto& req = const_cast<svc::unified_v2::req::settings::SetCollateralCoinModeReq&>(
      request->settingsreqgroupbody().set_collateral_coin_mode_req());

  req.set_collateral_coin_mode(ECollateralCoinMode::Normal);

  int32_t ret = set_collateral_coin_mode_service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  draft_pkg->CommitAll();
  ASSERT_EQ(draft_pkg->affected_setting_.current_setting->user_enabled_collateral_coin_set.size(), 2);
  ASSERT_TRUE(draft_pkg->affected_setting_.current_setting->user_enabled_collateral_coin_set.contains(ECoin::USDC));
  ASSERT_TRUE(draft_pkg->affected_setting_.current_setting->user_enabled_collateral_coin_set.contains(ECoin::USDT));
  ASSERT_EQ(draft_pkg->affected_setting_.current_setting->collateral_coin_mode,
            static_cast<ECollateralCoinMode>(req.collateral_coin_mode()));

  LOG_INFO("instloan_to_normal test done");
}

TEST_F(SetCollateralCoinModeServiceTest, normal_to_normal) {
  const std::shared_ptr<biz::SetCollateralCoinModeService> set_collateral_coin_mode_service =
      std::make_shared<biz::SetCollateralCoinModeService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetCollateralCoinMode);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->collateral_coin_mode = ECollateralCoinMode::Normal;
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->user_enabled_collateral_coin_set.insert(ECoin::USDT);
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->user_enabled_collateral_coin_set.insert(ECoin::USDC);

  auto& req = const_cast<svc::unified_v2::req::settings::SetCollateralCoinModeReq&>(
      request->settingsreqgroupbody().set_collateral_coin_mode_req());

  req.set_collateral_coin_mode(ECollateralCoinMode::Normal);

  int32_t ret = set_collateral_coin_mode_service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, error::kRequestIdempotent);
  LOG_INFO("normal_to_normal test done");
}

TEST_F(SetCollateralCoinModeServiceTest, instloan_to_instloan) {
  const std::shared_ptr<biz::SetCollateralCoinModeService> set_collateral_coin_mode_service =
      std::make_shared<biz::SetCollateralCoinModeService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetCollateralCoinMode);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->collateral_coin_mode = ECollateralCoinMode::InstLoan;
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->user_enabled_collateral_coin_set.insert(ECoin::USDT);
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->user_enabled_collateral_coin_set.insert(ECoin::USDC);

  auto& req = const_cast<svc::unified_v2::req::settings::SetCollateralCoinModeReq&>(
      request->settingsreqgroupbody().set_collateral_coin_mode_req());

  req.set_collateral_coin_mode(ECollateralCoinMode::InstLoan);

  int32_t ret = set_collateral_coin_mode_service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, error::kRequestIdempotent);
  LOG_INFO("instloan_to_instloan test done");
}

TEST_F(SetCollateralCoinModeServiceTest, instloan_to_unknown) {
  const std::shared_ptr<biz::SetCollateralCoinModeService> set_collateral_coin_mode_service =
      std::make_shared<biz::SetCollateralCoinModeService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetCollateralCoinMode);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->collateral_coin_mode = ECollateralCoinMode::InstLoan;
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->user_enabled_collateral_coin_set.insert(ECoin::USDT);
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->user_enabled_collateral_coin_set.insert(ECoin::USDC);

  auto& req = const_cast<svc::unified_v2::req::settings::SetCollateralCoinModeReq&>(
      request->settingsreqgroupbody().set_collateral_coin_mode_req());

  req.set_collateral_coin_mode(ECollateralCoinMode::UnKnown);

  int32_t ret = set_collateral_coin_mode_service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, error::kRequestCheckException);
  LOG_INFO("instloan_to_unknown test done");
}

TEST_F(SetCollateralCoinModeServiceTest, kAccountInstStatusNotNormal) {
  const std::shared_ptr<biz::SetCollateralCoinModeService> set_collateral_coin_mode_service =
      std::make_shared<biz::SetCollateralCoinModeService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetCollateralCoinMode);

  auto ref_account_info_ = std::make_shared<store::CowAccount>();
  ref_account_info_->cur_account = (std::make_shared<store::Account>());
  per_user_store->account_info_ = ref_account_info_;
  auto latest_acc = const_cast<store::Account*>(per_user_store->account_info_->Latest());
  latest_acc->inst_status = 1;

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->collateral_coin_mode = ECollateralCoinMode::InstLoan;
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->user_enabled_collateral_coin_set.insert(ECoin::USDT);
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->user_enabled_collateral_coin_set.insert(ECoin::USDC);

  auto& req = const_cast<svc::unified_v2::req::settings::SetCollateralCoinModeReq&>(
      request->settingsreqgroupbody().set_collateral_coin_mode_req());

  req.set_collateral_coin_mode(ECollateralCoinMode::Normal);

  int32_t ret = set_collateral_coin_mode_service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, error::kAccountInstStatusNotNormal);
  LOG_INFO("kAccountInstStatusNotNormal test done");
}

TEST_F(SetCollateralCoinModeServiceTest, kAccountIsInLiq) {
  const std::shared_ptr<biz::SetCollateralCoinModeService> set_collateral_coin_mode_service =
      std::make_shared<biz::SetCollateralCoinModeService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetCollateralCoinMode);

  auto ref_account_info_ = std::make_shared<store::CowAccount>();
  ref_account_info_->cur_account = (std::make_shared<store::Account>());
  per_user_store->account_info_ = ref_account_info_;
  auto latest_acc = const_cast<store::Account*>(per_user_store->account_info_->Latest());
  latest_acc->account_status = enums::eaccountstatus::Liq;

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->collateral_coin_mode = ECollateralCoinMode::InstLoan;
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->user_enabled_collateral_coin_set.insert(ECoin::USDT);
  draft_pkg->GetOrAttachUserSetting()->LazyDraftS()->user_enabled_collateral_coin_set.insert(ECoin::USDC);

  auto& req = const_cast<svc::unified_v2::req::settings::SetCollateralCoinModeReq&>(
      request->settingsreqgroupbody().set_collateral_coin_mode_req());

  req.set_collateral_coin_mode(ECollateralCoinMode::Normal);

  int32_t ret = set_collateral_coin_mode_service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, error::kAccountIsInLiq);
  LOG_INFO("kAccountIsInLiq test done");
}

TEST_F(SetCollateralCoinModeServiceTest, SetSingleCoinColleteral_new) {
  const std::shared_ptr<biz::CustomeColleteralCoinService> service =
      std::make_shared<biz::CustomeColleteralCoinService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetSpotCollateralCoin);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.collateral_coin_mode = ECollateralCoinMode::Normal;

  auto& req = const_cast<svc::unified_v2::req::settings::SetSpotCollateralCoinReq&>(
      request->settingsreqgroupbody().set_spot_collateral_coin());
  auto* btc = req.add_coinlist();
  btc->set_coin(1);
  btc->set_enable(true);

  auto* eth = req.add_coinlist();
  eth->set_coin(2);
  eth->set_enable(true);

  int32_t ret = service->SetSingleCoinColleteral(draft_pkg.get(), req);
  ASSERT_EQ(ret, error::kErrorCodeSuccess);
  ASSERT_EQ(setting.user_enabled_collateral_coin_set.size(), 2);
  ASSERT_TRUE(setting.user_enabled_collateral_coin_set.contains(btc->coin()));
  ASSERT_TRUE(setting.user_enabled_collateral_coin_set.contains(eth->coin()));
  ASSERT_EQ(setting.collateral_coin_mode, ECollateralCoinMode::Normal);
  ASSERT_EQ(setting.default_disable_collateral_coin, true);

  btc->set_enable(false);
  eth->set_enable(false);

  ret = service->SetSingleCoinColleteral(draft_pkg.get(), req);
  ASSERT_EQ(ret, error::kErrorCodeSuccess);
  ASSERT_EQ(setting.user_enabled_collateral_coin_set.size(), 0);
  ASSERT_EQ(setting.collateral_coin_mode, ECollateralCoinMode::Normal);
  ASSERT_EQ(setting.default_disable_collateral_coin, true);

  LOG_INFO("SetSingleCoinColleteral_new test done");
}

TEST_F(SetCollateralCoinModeServiceTest, SetSingleCoinColleteral_spot_order_check_new) {
  const std::shared_ptr<biz::CustomeColleteralCoinService> service =
      std::make_shared<biz::CustomeColleteralCoinService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetSpotCollateralCoin);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.collateral_coin_mode = ECollateralCoinMode::Normal;

  draft_pkg->CreateOrAttachSpotSymbolMarginCost(1, 1);

  auto spotOrder = std::make_shared<store::CowSpotOrder>();
  auto ao = std::make_shared<::store::SpotOrder>();
  spotOrder->cur_order = (ao);
  ao->leverage_place_type = ELeveragePlaceType::AUTO_LOAN_AND_SPOT;
  resource->per_user_store->working_coins_[1]->working_spot_symbols_[1]->all_spot_orders_[biz::order_id_t("123")] =
      spotOrder.get();

  auto& req = const_cast<svc::unified_v2::req::settings::SetSpotCollateralCoinReq&>(
      request->settingsreqgroupbody().set_spot_collateral_coin());
  auto* btc = req.add_coinlist();
  btc->set_coin(1);
  btc->set_enable(true);

  auto* eth = req.add_coinlist();
  eth->set_coin(2);
  eth->set_enable(true);

  int32_t ret = service->SetSingleCoinColleteral(draft_pkg.get(), req);
  ASSERT_EQ(ret, error::kErrorCodeSuccess);
  ASSERT_EQ(setting.user_enabled_collateral_coin_set.size(), 2);
  ASSERT_TRUE(setting.user_enabled_collateral_coin_set.contains(btc->coin()));
  ASSERT_TRUE(setting.user_enabled_collateral_coin_set.contains(eth->coin()));
  ASSERT_EQ(setting.collateral_coin_mode, ECollateralCoinMode::Normal);
  ASSERT_EQ(setting.default_disable_collateral_coin, true);

  btc->set_enable(false);
  eth->set_enable(false);

  ret = service->SetSingleCoinColleteral(draft_pkg.get(), req);
  ASSERT_EQ(ret, error::kSpotOrderNotEmpty);

  LOG_INFO("SetSingleCoinColleteral_spot_order_check_new test done");
}

TEST_F(SetCollateralCoinModeServiceTest, CustomeColleteralCoinService_forceRemoveAllUserConfig) {
  const std::shared_ptr<biz::CustomeColleteralCoinService> service =
      std::make_shared<biz::CustomeColleteralCoinService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetSpotCollateralCoin);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.collateral_coin_mode = ECollateralCoinMode::Normal;

  auto req = request->mutable_settingsreqgroupbody()->mutable_set_spot_collateral_coin();
  req->set_forceremovealluserconfig(true);
  auto* btc = req->add_coinlist();
  btc->set_coin(1);
  btc->set_enable(true);

  auto* eth = req->add_coinlist();
  eth->set_coin(2);
  eth->set_enable(true);

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);
  ASSERT_GT(setting.user_enabled_collateral_coin_set.size(), 2);

  LOG_INFO("CustomeColleteralCoinService_forceRemoveAllUserConfig test done");
}

TEST_F(SetCollateralCoinModeServiceTest, CustomeColleteralCoinService_InstLoanAllowed) {
  const std::shared_ptr<biz::CustomeColleteralCoinService> service =
      std::make_shared<biz::CustomeColleteralCoinService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetSpotCollateralCoin);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.collateral_coin_mode = ECollateralCoinMode::InstLoan;

  auto req = request->mutable_settingsreqgroupbody()->mutable_set_spot_collateral_coin();
  auto* btc = req->add_coinlist();
  btc->set_coin(1);
  btc->set_enable(true);

  auto* eth = req->add_coinlist();
  eth->set_coin(2);
  eth->set_enable(true);

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, 0);

  LOG_INFO("CustomeColleteralCoinService_InstLoanNotAllowed test done");
}

TEST_F(SetCollateralCoinModeServiceTest, CustomeColleteralCoinService_kAccountInstStatusNotNormal) {
  const std::shared_ptr<biz::CustomeColleteralCoinService> service =
      std::make_shared<biz::CustomeColleteralCoinService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetSpotCollateralCoin);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.collateral_coin_mode = ECollateralCoinMode::Normal;
  auto ref_account_info_ = std::make_shared<store::CowAccount>();
  ref_account_info_->cur_account = (std::make_shared<store::Account>());
  per_user_store->account_info_ = ref_account_info_;
  auto latest_acc = const_cast<store::Account*>(per_user_store->account_info_->Latest());
  latest_acc->inst_status = 1;

  auto req = request->mutable_settingsreqgroupbody()->mutable_set_spot_collateral_coin();
  auto* btc = req->add_coinlist();
  btc->set_coin(1);
  btc->set_enable(true);

  auto* eth = req->add_coinlist();
  eth->set_coin(2);
  eth->set_enable(true);

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, error::kAccountInstStatusNotNormal);

  LOG_INFO("CustomeColleteralCoinService_kAccountInstStatusNotNormal test done");
}

TEST_F(SetCollateralCoinModeServiceTest, CustomeColleteralCoinService_kAccountIsInLiq) {
  const std::shared_ptr<biz::CustomeColleteralCoinService> service =
      std::make_shared<biz::CustomeColleteralCoinService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetSpotCollateralCoin);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.collateral_coin_mode = ECollateralCoinMode::Normal;
  auto ref_account_info_ = std::make_shared<store::CowAccount>();
  ref_account_info_->cur_account = (std::make_shared<store::Account>());
  per_user_store->account_info_ = ref_account_info_;
  auto latest_acc = const_cast<store::Account*>(per_user_store->account_info_->Latest());
  latest_acc->account_status = enums::eaccountstatus::Liq;

  auto req = request->mutable_settingsreqgroupbody()->mutable_set_spot_collateral_coin();
  auto* btc = req->add_coinlist();
  btc->set_coin(1);
  btc->set_enable(true);

  auto* eth = req->add_coinlist();
  eth->set_coin(2);
  eth->set_enable(true);

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, error::kAccountIsInLiq);

  LOG_INFO("CustomeColleteralCoinService_kAccountIsInLiq test done");
}

TEST_F(SetCollateralCoinModeServiceTest, ConvertToPerUserSettingData) {
  store::Setting raw_setting{};
  raw_setting.collateral_coin_mode = ECollateralCoinMode::InstLoan;
  raw_setting.user_enabled_collateral_coin_set.insert(ECoin::USDT);

  models::usersettingdto::PerUserSettingData setting_data{};

  convertor::ToPB::ConvertToPerUserSettingData(&raw_setting, &setting_data);

  ASSERT_TRUE(setting_data.default_disable_collateral_coin());
  ASSERT_EQ(setting_data.collateral_coin_mode(), ECollateralCoinMode::InstLoan);
  ASSERT_EQ(setting_data.user_enabled_collateral_coin_list().size(), 1);

  store::Setting setting{};
  bizutils::FromPB::ConvertToUserSetting(&setting_data, &setting, false);
  ASSERT_TRUE(setting.default_disable_collateral_coin);
  ASSERT_EQ(setting.collateral_coin_mode, ECollateralCoinMode::InstLoan);
  ASSERT_EQ(setting.user_enabled_collateral_coin_set.size(), 1);

  LOG_INFO("ConvertToPerUserSettingData test done");
}

TEST_F(SetCollateralCoinModeServiceTest, test_im_ratio_without_bonus) {
  double imr = 0.807;

  ASSERT_FALSE(static_cast<int32_t>(imr * 100) > 80);

  ASSERT_GT((imr * 100), 80);

  ASSERT_GT(imr * 100, 80);

  imr = 0.812;

  ASSERT_TRUE(static_cast<int32_t>(imr * 100) > 80);

  ASSERT_GT((imr * 100), 80);

  ASSERT_GT(imr * 100, 80);

  LOG_INFO("test_im_ratio_without_bonus test done");
}

TEST_F(SetCollateralCoinModeServiceTest, CustomeColleteralCoinService_UserRecalculateInited) {
  const std::shared_ptr<biz::CustomeColleteralCoinService> service =
      std::make_shared<biz::CustomeColleteralCoinService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetSpotCollateralCoin);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.collateral_coin_mode = ECollateralCoinMode::Normal;
  setting.user_enabled_collateral_coin_set.insert(2);

  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(2);
  auto none_const_w = const_cast<store::Wallet*>(cow_wallet->Latest());
  none_const_w->wallet_balance = biz::money_t("-100");
  EXPECT_TRUE(cow_wallet->Latest()->liability == biz::money_t("0"));

  auto* settingsreqgroupbody = request->mutable_settingsreqgroupbody();
  auto req = settingsreqgroupbody->mutable_set_spot_collateral_coin();

  auto* eth = req->add_coinlist();
  eth->set_coin(2);
  eth->set_enable(false);

  biz::RefreshWalletUnitService::RecalculateAllWallets(draft_pkg.get());

  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, error::kSettleCoinHaveLiability);
  cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(2);
  EXPECT_TRUE(cow_wallet->Latest()->liability == biz::money_t("100"));

  LOG_INFO("CustomeColleteralCoinService_UserRecalculateInited test done");
}

TEST_F(SetCollateralCoinModeServiceTest, CustomeColleteralCoinService_kRequestIdempotent) {
  const std::shared_ptr<biz::CustomeColleteralCoinService> service =
      std::make_shared<biz::CustomeColleteralCoinService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  // 第一次enable ETH， 成功
  {
    auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
    auto* req_header = request->mutable_req_header();
    req_header->set_req_id("set_collateral_coin_mode_1001_1");
    req_header->set_user_id(1001);
    req_header->set_coin(ECoin::USDT);
    req_header->set_action(EAction::SetSpotCollateralCoin);

    auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
        1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
        event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
    auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
    auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
    setting.collateral_coin_mode = ECollateralCoinMode::Normal;

    auto* settingsreqgroupbody = request->mutable_settingsreqgroupbody();
    auto req = settingsreqgroupbody->mutable_set_spot_collateral_coin();

    auto* eth = req->add_coinlist();
    eth->set_coin(2);
    eth->set_enable(true);

    int32_t ret = service->process(draft_pkg.get(), request);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);
  }

  // 第2次enable ETH， 幂等
  {
    auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
    auto* req_header = request->mutable_req_header();
    req_header->set_req_id("set_collateral_coin_mode_1001_1");
    req_header->set_user_id(1001);
    req_header->set_coin(ECoin::USDT);
    req_header->set_action(EAction::SetSpotCollateralCoin);

    auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
        1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
        event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
    auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
    auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
    setting.collateral_coin_mode = ECollateralCoinMode::Normal;

    auto* settingsreqgroupbody = request->mutable_settingsreqgroupbody();
    auto req = settingsreqgroupbody->mutable_set_spot_collateral_coin();

    auto* eth = req->add_coinlist();
    eth->set_coin(2);
    eth->set_enable(true);

    int32_t ret = service->process(draft_pkg.get(), request);
    // ASSERT_EQ(ret, error::kRequestIdempotent);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);  // CR讨论后改成 success
  }

  // 第1次enable ETH + BTC 成功
  {
    auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
    auto* req_header = request->mutable_req_header();
    req_header->set_req_id("set_collateral_coin_mode_1001_1");
    req_header->set_user_id(1001);
    req_header->set_coin(ECoin::USDT);
    req_header->set_action(EAction::SetSpotCollateralCoin);

    auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
        1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
        event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
    auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
    auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
    setting.collateral_coin_mode = ECollateralCoinMode::Normal;

    auto* settingsreqgroupbody = request->mutable_settingsreqgroupbody();
    auto req = settingsreqgroupbody->mutable_set_spot_collateral_coin();

    auto* eth = req->add_coinlist();
    eth->set_coin(2);
    eth->set_enable(true);

    auto* btc = req->add_coinlist();
    btc->set_coin(1);
    btc->set_enable(true);

    int32_t ret = service->process(draft_pkg.get(), request);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);
  }

  // 第2次enable ETH + BTC 幂等
  {
    auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
    auto* req_header = request->mutable_req_header();
    req_header->set_req_id("set_collateral_coin_mode_1001_1");
    req_header->set_user_id(1001);
    req_header->set_coin(ECoin::USDT);
    req_header->set_action(EAction::SetSpotCollateralCoin);

    auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
        1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
        event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
    auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
    auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
    setting.collateral_coin_mode = ECollateralCoinMode::Normal;

    auto* settingsreqgroupbody = request->mutable_settingsreqgroupbody();
    auto req = settingsreqgroupbody->mutable_set_spot_collateral_coin();

    auto* eth = req->add_coinlist();
    eth->set_coin(2);
    eth->set_enable(true);

    auto* btc = req->add_coinlist();
    btc->set_coin(1);
    btc->set_enable(true);

    int32_t ret = service->process(draft_pkg.get(), request);
    // ASSERT_EQ(ret, error::kRequestIdempotent);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);  // CR讨论后改成 success
  }

  // 第一次disable ETH， 成功
  {
    auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
    auto* req_header = request->mutable_req_header();
    req_header->set_req_id("set_collateral_coin_mode_1001_1");
    req_header->set_user_id(1001);
    req_header->set_coin(ECoin::USDT);
    req_header->set_action(EAction::SetSpotCollateralCoin);

    auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
        1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
        event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
    auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
    auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
    setting.collateral_coin_mode = ECollateralCoinMode::Normal;

    auto* settingsreqgroupbody = request->mutable_settingsreqgroupbody();
    auto req = settingsreqgroupbody->mutable_set_spot_collateral_coin();

    auto* eth = req->add_coinlist();
    eth->set_coin(2);
    eth->set_enable(false);

    int32_t ret = service->process(draft_pkg.get(), request);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);
  }

  // 第2次disable ETH， 幂等
  {
    auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
    auto* req_header = request->mutable_req_header();
    req_header->set_req_id("set_collateral_coin_mode_1001_1");
    req_header->set_user_id(1001);
    req_header->set_coin(ECoin::USDT);
    req_header->set_action(EAction::SetSpotCollateralCoin);

    auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
        1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
        event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
    auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
    auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
    setting.collateral_coin_mode = ECollateralCoinMode::Normal;

    auto* settingsreqgroupbody = request->mutable_settingsreqgroupbody();
    auto req = settingsreqgroupbody->mutable_set_spot_collateral_coin();

    auto* eth = req->add_coinlist();
    eth->set_coin(2);
    eth->set_enable(false);

    int32_t ret = service->process(draft_pkg.get(), request);
    // ASSERT_EQ(ret, error::kRequestIdempotent);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);  // CR讨论后改成 success
  }

  // 第1次disable ETH + BTC 成功
  {
    auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
    auto* req_header = request->mutable_req_header();
    req_header->set_req_id("set_collateral_coin_mode_1001_1");
    req_header->set_user_id(1001);
    req_header->set_coin(ECoin::USDT);
    req_header->set_action(EAction::SetSpotCollateralCoin);

    auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
        1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
        event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
    auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
    auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
    setting.collateral_coin_mode = ECollateralCoinMode::Normal;

    auto* settingsreqgroupbody = request->mutable_settingsreqgroupbody();
    auto req = settingsreqgroupbody->mutable_set_spot_collateral_coin();

    auto* eth = req->add_coinlist();
    eth->set_coin(2);
    eth->set_enable(false);

    auto* btc = req->add_coinlist();
    btc->set_coin(1);
    btc->set_enable(false);

    int32_t ret = service->process(draft_pkg.get(), request);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);
  }

  // 第2次disable ETH + BTC 幂等
  {
    auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
    auto* req_header = request->mutable_req_header();
    req_header->set_req_id("set_collateral_coin_mode_1001_1");
    req_header->set_user_id(1001);
    req_header->set_coin(ECoin::USDT);
    req_header->set_action(EAction::SetSpotCollateralCoin);

    auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
        1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
        event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
    auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
    auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
    setting.collateral_coin_mode = ECollateralCoinMode::Normal;

    auto* settingsreqgroupbody = request->mutable_settingsreqgroupbody();
    auto req = settingsreqgroupbody->mutable_set_spot_collateral_coin();

    auto* eth = req->add_coinlist();
    eth->set_coin(2);
    eth->set_enable(false);

    auto* btc = req->add_coinlist();
    btc->set_coin(1);
    btc->set_enable(false);

    int32_t ret = service->process(draft_pkg.get(), request);
    // ASSERT_EQ(ret, error::kRequestIdempotent);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);  // CR讨论后改成 success
  }

  // 第1次 enable 1  disable 2 成功
  {
    auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
    auto* req_header = request->mutable_req_header();
    req_header->set_req_id("set_collateral_coin_mode_1001_1");
    req_header->set_user_id(1001);
    req_header->set_coin(ECoin::USDT);
    req_header->set_action(EAction::SetSpotCollateralCoin);

    auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
        1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
        event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
    auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
    auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
    setting.collateral_coin_mode = ECollateralCoinMode::Normal;

    auto* settingsreqgroupbody = request->mutable_settingsreqgroupbody();
    auto req = settingsreqgroupbody->mutable_set_spot_collateral_coin();

    auto* eth = req->add_coinlist();
    eth->set_coin(1);
    eth->set_enable(true);

    auto* btc = req->add_coinlist();
    btc->set_coin(2);
    btc->set_enable(false);

    int32_t ret = service->process(draft_pkg.get(), request);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);
  }

  // 第2次 enable 1  disable 2 幂等
  {
    auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
    auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
    auto* req_header = request->mutable_req_header();
    req_header->set_req_id("set_collateral_coin_mode_1001_1");
    req_header->set_user_id(1001);
    req_header->set_coin(ECoin::USDT);
    req_header->set_action(EAction::SetSpotCollateralCoin);

    auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
        1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
        event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
    auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
    auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
    setting.collateral_coin_mode = ECollateralCoinMode::Normal;

    auto* settingsreqgroupbody = request->mutable_settingsreqgroupbody();
    auto req = settingsreqgroupbody->mutable_set_spot_collateral_coin();

    auto* eth = req->add_coinlist();
    eth->set_coin(1);
    eth->set_enable(true);

    auto* btc = req->add_coinlist();
    btc->set_coin(2);
    btc->set_enable(false);

    int32_t ret = service->process(draft_pkg.get(), request);
    ASSERT_EQ(ret, error::kErrorCodeSuccess);  // CR讨论后改成 success
  }

  LOG_INFO("CustomeColleteralCoinService_kRequestIdempotent test done");
}

TEST_F(SetCollateralCoinModeServiceTest, SwitchSpecialNodeService) {
  const std::shared_ptr<biz::SwitchSpecialNodeService> service = std::make_shared<biz::SwitchSpecialNodeService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetSpotCollateralCoin);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto& setting = *draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting.collateral_coin_mode = ECollateralCoinMode::Normal;
  setting.user_enabled_collateral_coin_set.insert(2);
  // 这里假装已经跑过大计算
  setting.recalculate_initiated_ = true;

  auto cow_wallet = draft_pkg->GetOrAttachSettleCoinWallet(2);
  auto none_const_w = const_cast<store::Wallet*>(cow_wallet->Latest());
  none_const_w->wallet_balance = biz::money_t("-100");

  auto* reqgroupbody = request->mutable_walletreqgroupbody();
  auto req = reqgroupbody->mutable_switchspecialnodereq();
  req->set_spnode("g9i0");

  application::GlobalVarManager::Instance().set_group_name("");
  application::GlobalVarManager::Instance().set_node_name("g0p0");

  LOG_INFO("SwitchSpecialNode uid:{}, checked symbol {}", draft_pkg->uid(), 1);

  // 有抵押品资产为负的情况下， 没跑过大计算， 可以关闭抵押品成功
  int32_t ret = service->process(draft_pkg.get(), request);
  ASSERT_EQ(ret, error::kErrorCodeSuccess);

  LOG_INFO("SwitchSpecialNodeService test done");
}

TEST_F(SetCollateralCoinModeServiceTest, IsCollateralCoin) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(enums::ecoin::USDT);
  req_header->set_action(enums::eaction::SetSpotCollateralCoin);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());

  EXPECT_TRUE(draft_pkg->IsCollateralCoin(enums::ecoin::USDT));
  EXPECT_TRUE(draft_pkg->IsCollateralCoin(enums::ecoin::USDC));

  EXPECT_TRUE(draft_pkg->IsLoan(enums::ecoin::USDT));
  EXPECT_TRUE(draft_pkg->IsLoan(enums::ecoin::USDC));

  EXPECT_TRUE(draft_pkg->IsCollateralCoin(enums::ecoin::BTC));
  EXPECT_TRUE(draft_pkg->IsLoan(enums::ecoin::BTC));

  EXPECT_TRUE(draft_pkg->IsCollateralCoin(enums::ecoin::ETH));
  // is_loan = "N";
  EXPECT_FALSE(draft_pkg->IsLoan(enums::ecoin::ETH));

  // omg_config->is_margin = "N";
  EXPECT_FALSE(draft_pkg->IsCollateralCoin(enums::ecoin::OMG));
  // lfw_config->margin_value_rate = bbase::decimal::Decimal<>("0");
  EXPECT_FALSE(draft_pkg->IsCollateralCoin(enums::ecoin::LFW));

  // marginCoinConfigDTO == nullptr
  EXPECT_FALSE(draft_pkg->IsCollateralCoin(enums::ecoin::STETH));
  EXPECT_FALSE(draft_pkg->IsLoan(enums::ecoin::STETH));

  // marginCoinConfigDTO->status 0
  EXPECT_FALSE(draft_pkg->IsCollateralCoin(enums::ecoin::ENS));
  EXPECT_FALSE(draft_pkg->IsLoan(enums::ecoin::ENS));

  // marginCoinConfigDTO->status 2
  EXPECT_FALSE(draft_pkg->IsCollateralCoin(enums::ecoin::AVA));
  EXPECT_FALSE(draft_pkg->IsLoan(enums::ecoin::AVA));

  LOG_INFO("IsCollateralCoin test done");
}

// DC DT  平台已预下架（运营已配置预下架+uta 系统已配置可以下架）+用户关闭时，不是抵押品
TEST_F(SetCollateralCoinModeServiceTest, IsCollateralCoin_stable_coin_1) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(enums::ecoin::USDT);
  req_header->set_action(enums::eaction::SetSpotCollateralCoin);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto setting = draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting->collateral_coin_mode = ECollateralCoinMode::Normal;

  auto usdt_cfg = draft_pkg->w_store->QueryMarginCoinConfigByCoinId(enums::ecoin::USDT);
  usdt_cfg->is_collateral_pre_offline = "Y";
  // 用户级别没开 不是抵押品
  EXPECT_FALSE(setting->CollateralCoinEnable(enums::ecoin::USDT, true));
  // EXPECT_FALSE(draft_pkg->IsCollateralCoin(enums::ecoin::USDT));

  // 用户级别开了， 即使预下架， 还是抵押品
  setting->user_enabled_collateral_coin_set.insert(enums::ecoin::USDT);
  EXPECT_TRUE(draft_pkg->IsCollateralCoin(enums::ecoin::USDT));

  LOG_INFO("IsCollateralCoin_stable_coin_1 test done");
}

// DC DT  平台已下架，不是抵押品,  用户级别开了
TEST_F(SetCollateralCoinModeServiceTest, IsCollateralCoin_stable_coin_2) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(enums::ecoin::USDT);
  req_header->set_action(enums::eaction::SetSpotCollateralCoin);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto setting = draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting->collateral_coin_mode = ECollateralCoinMode::Normal;
  setting->user_enabled_collateral_coin_set.insert(enums::ecoin::USDT);

  auto usdt_cfg = draft_pkg->w_store->QueryMarginCoinConfigByCoinId(enums::ecoin::USDT);
  //  平台已经下架
  usdt_cfg->is_margin = "N";
  EXPECT_TRUE(setting->CollateralCoinEnable(enums::ecoin::USDT));
  // EXPECT_FALSE(draft_pkg->IsCollateralCoin(enums::ecoin::USDT));

  LOG_INFO("IsCollateralCoin_stable_coin_2 test done");
}

// DC DT  平台没下架，也不是预下架， 是抵押品
TEST_F(SetCollateralCoinModeServiceTest, IsCollateralCoin_stable_coin_3) {
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(enums::ecoin::USDT);
  req_header->set_action(enums::eaction::SetSpotCollateralCoin);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto setting = draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting->collateral_coin_mode = ECollateralCoinMode::Normal;

  // 用户级别开
  setting->user_enabled_collateral_coin_set.insert(enums::ecoin::USDT);
  EXPECT_TRUE(draft_pkg->IsCollateralCoin(enums::ecoin::USDT));
  // 用户级别关
  setting->user_enabled_collateral_coin_set.clear();
  // EXPECT_FALSE(draft_pkg->IsCollateralCoin(enums::ecoin::USDT));

  LOG_INFO("IsCollateralCoin_stable_coin_3 test done");
}

// DC DT 抵押品设置
TEST_F(SetCollateralCoinModeServiceTest, SetSingleCoinColleteral_stable_coin_1) {
  const std::shared_ptr<biz::CustomeColleteralCoinService> service =
      std::make_shared<biz::CustomeColleteralCoinService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetSpotCollateralCoin);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto setting = draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting->collateral_coin_mode = ECollateralCoinMode::Normal;

  auto& req = const_cast<svc::unified_v2::req::settings::SetSpotCollateralCoinReq&>(
      request->settingsreqgroupbody().set_spot_collateral_coin());
  auto* usdt = req.add_coinlist();
  usdt->set_coin(5);
  usdt->set_enable(true);

  // DT DC 不预下架，关闭时，可以开启
  int32_t ret = service->SetSingleCoinColleteral(draft_pkg.get(), req);
  ASSERT_EQ(ret, error::kErrorCodeSuccess);
  // 第二次会幂等， 啥也不做 打印  SetSpotCollateralCoin, uid:1001, noCoinStateChange
  ret = service->SetSingleCoinColleteral(draft_pkg.get(), req);
  ASSERT_EQ(ret, error::kErrorCodeSuccess);

  //  不预下架，抵押品开启时不能关闭
  usdt->set_enable(false);
  ret = service->SetSingleCoinColleteral(draft_pkg.get(), req);
  ASSERT_EQ(ret, error::kEcWalletCoinNotSupport);

  LOG_INFO("SetSingleCoinColleteral_stable_coin_1 test done");
}

// 其他 抵押品设置
TEST_F(SetCollateralCoinModeServiceTest, SetSingleCoinColleteral_stable_coin_2) {
  const std::shared_ptr<biz::CustomeColleteralCoinService> service =
      std::make_shared<biz::CustomeColleteralCoinService>();
  auto per_user_store = std::make_shared<store::PerUserStore>(1001, ELoadDumpStatus::Loaded);
  auto resource = std::make_shared<store::UnicastResource>();
  auto header = std::make_shared<store::Header>();
  header->uid = 1001;
  resource->header = header;
  resource->per_user_store = per_user_store.get();
  resource->config_manager = config::getTlsCfgMgrRaw();
  resource->per_worker_store = workerStore.get();

  auto unified_v_2_request_dto = svc::unified_v2::UnifiedV2RequestDTO::default_instance();
  auto request = std::make_shared<svc::unified_v2::UnifiedV2RequestDTO>(unified_v_2_request_dto);
  auto* req_header = request->mutable_req_header();
  req_header->set_req_id("set_collateral_coin_mode_1001_1");
  req_header->set_user_id(1001);
  req_header->set_coin(ECoin::USDT);
  req_header->set_action(EAction::SetSpotCollateralCoin);

  auto biz_event = std::make_shared<event::MarginHdtsBizEvent>(
      1001, event::EventType::kEventMarginHdtsRequest, event::EventSourceType::kMarginHdts,
      event::EventDispatchType::kUnicastToUser, event::EventBizType::kMarginRequest, request);
  auto draft_pkg = std::make_shared<biz::DraftPkg>(biz_event, header, workerStore.get(), per_user_store.get());
  auto setting = draft_pkg->GetOrAttachUserSetting()->LazyDraftS();
  setting->collateral_coin_mode = ECollateralCoinMode::Normal;
  setting->user_enabled_collateral_coin_set.insert(enums::ecoin::BTC);

  auto& req = const_cast<svc::unified_v2::req::settings::SetSpotCollateralCoinReq&>(
      request->settingsreqgroupbody().set_spot_collateral_coin());
  auto* btc = req.add_coinlist();
  btc->set_coin(1);
  btc->set_enable(false);

  auto btc_cfg = draft_pkg->w_store->QueryMarginCoinConfigByCoinId(enums::ecoin::BTC);
  //  平台预下架
  btc_cfg->is_collateral_pre_offline = "Y";

  // 运营已配置预下架
  // 可以手工关闭抵押品
  int32_t ret = service->SetSingleCoinColleteral(draft_pkg.get(), req);
  ASSERT_EQ(ret, error::kErrorCodeSuccess);

  btc->set_enable(true);
  ret = service->SetSingleCoinColleteral(draft_pkg.get(), req);
  // 不可以开启
  ASSERT_EQ(ret, error::kEcWalletCoinNotSupport);

  LOG_INFO("SetSingleCoinColleteral_stable_coin_2 test done");
}
