#ifndef TEST_BIZ_WALLET_SERVICE_UT_PMUNIT_REFRESH_PM_RISK_UNIT_SERVICE_TEST_HPP_
#define TEST_BIZ_WALLET_SERVICE_UT_PMUNIT_REFRESH_PM_RISK_UNIT_SERVICE_TEST_HPP_

#include <memory>
#include <string>

#include "biz_worker/service/wallet/algo/pmunit/refresh_pm_risk_unit_service.hpp"
#include "test/biz/wallet_service_ut/wallet_test.hpp"

#define DEFINE_TEST_CASE(result, arg_list) \
  struct TestCase {                        \
    struct Args {                          \
      arg_list;                            \
    } args;                                \
    result;                                \
  }

using TestClass = biz::RefreshPmRiskUnitService;

class RefreshPmRiskUnitServiceTest : public WalletTest {
 public:
  void SetUp() override { WalletTest::SetUp(); }
};

#endif  // TEST_BIZ_WALLET_SERVICE_UT_PMUNIT_REFRESH_PM_RISK_UNIT_SERVICE_TEST_HPP_
