#include "test/biz/wallet_service_ut/pmunit/refresh_pm_risk_unit_service_test.hpp"

TEST_F(WalletTest, CalcNextDeliveryTimeStampInSec) {
  DEFINE_TEST_CASE(biz::time_sec_t result, biz::time_sec_t now_sec; int delivery_hour);

  TestCase test_cases[] = {{{1680062113, 8}, 1680076800}, {{1680076800, 8}, 1680076800}, {{1680076801, 8}, 1680163200}};

  for (const auto& test_case : test_cases) {
    auto next = TestClass::CalcNextDeliveryTimeStampInSec(test_case.args.now_sec, test_case.args.delivery_hour);
    ASSERT_EQ(next, test_case.result);
  }
}

TEST_F(WalletTest, CalcRemainExpireDays) {
  DEFINE_TEST_CASE(int result, biz::time_sec_t now_sec; biz::time_sec_t expire_sec);

  TestCase test_cases[] = {{{1680062113, 1680062110}, 1},
                           {{1680062113, 1680062113}, 1},
                           {{1680062113, 1680062114}, 1},
                           {{1680062113, 1680062113 + 24 * 60 * 60}, 1},
                           {{1680062113, 1680062113 + 24 * 60 * 60 + 1}, 2}};

  for (const auto& test_case : test_cases) {
    auto next = TestClass::CalcRemainExpireDays(test_case.args.now_sec, test_case.args.expire_sec);
    ASSERT_EQ(next, test_case.result);
  }
}
