#include "test/biz/wallet_service_ut/pmunit/refresh_pm_risk_unit_service_test.hpp"

TEST_F(WalletTest, ReCalcPmFuturePnlMatrixInUsd) {
  DEFINE_TEST_CASE(biz::BigDecimal result[11], biz::BigDecimal pnl_matrix[11]; biz::BigDecimal mark_price_usd;
                   biz::BigDecimal price_range_interval; biz::time_sec_t delivert_time; biz::time_sec_t now;
                   bool need_decay);

  TestCase test_cases[] = {
      {{.pnl_matrix = {},
        .mark_price_usd{biz::BigDecimal("10000")},
        .price_range_interval{biz::BigDecimal("0.01")},
        .delivert_time = 900,
        .now = 0,
        .need_decay = false},
       {biz::BigDecimal("-500"), biz::BigDecimal("-400"), biz::BigDecimal("-300"), biz::BigDecimal("-200"),
        biz::BigDecimal("-100"), biz::BigDecimal("0"), biz::BigDecimal("100"), biz::BigDecimal("200"),
        biz::BigDecimal("300"), biz::BigDecimal("400"), biz::BigDecimal("500")}},
      {{.pnl_matrix = {},
        .mark_price_usd{biz::BigDecimal("10000")},
        .price_range_interval{biz::BigDecimal("0.5")},
        .delivert_time = 900,
        .now = 1000,
        .need_decay = false},
       {biz::BigDecimal("-10000"), biz::BigDecimal("-10000"), biz::BigDecimal("-10000"), biz::BigDecimal("-10000"),
        biz::BigDecimal("-5000"), biz::BigDecimal("0"), biz::BigDecimal("5000"), biz::BigDecimal("10000"),
        biz::BigDecimal("15000"), biz::BigDecimal("20000"), biz::BigDecimal("25000")}},
      {{.pnl_matrix = {},
        .mark_price_usd{biz::BigDecimal("10000")},
        .price_range_interval{biz::BigDecimal("0.01")},
        .delivert_time = 900,
        .now = 0,
        .need_decay = true},
       {biz::BigDecimal("-250"), biz::BigDecimal("-200"), biz::BigDecimal("-150"), biz::BigDecimal("-100"),
        biz::BigDecimal("-50"), biz::BigDecimal("0"), biz::BigDecimal("50"), biz::BigDecimal("100"),
        biz::BigDecimal("150"), biz::BigDecimal("200"), biz::BigDecimal("250")}},
      {{.pnl_matrix = {},
        .mark_price_usd{biz::BigDecimal("10000")},
        .price_range_interval{biz::BigDecimal("0.01")},
        .delivert_time = 900,
        .now = 900,
        .need_decay = true},
       {biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"),
        biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"),
        biz::BigDecimal("0")}},
      {{.pnl_matrix = {},
        .mark_price_usd{biz::BigDecimal("10000")},
        .price_range_interval{biz::BigDecimal("0.01")},
        .delivert_time = 900,
        .now = 1000,
        .need_decay = true},
       {biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"),
        biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"),
        biz::BigDecimal("0")}}};

  for (const auto& test_case : test_cases) {
    std::array<double, 11> input;
    TestClass::ReCalcPmFuturePnlMatrixInUsd(
        input, test_case.args.mark_price_usd.ToDouble(), test_case.args.price_range_interval.ToDouble(),
        test_case.args.delivert_time, test_case.args.now, test_case.args.need_decay);
    for (int i = 0; i < 11; ++i) {
      ASSERT_LE(std::abs(input[i] - test_case.result[i].ToDouble()), 0.01);
    }
  }
}

TEST_F(WalletTest, ReCalcPmOptionPnlMatrixInUsd) {
  DEFINE_TEST_CASE(biz::BigDecimal result[3][11], biz::BigDecimal pnl_matrix[3][11];
                   biz::BigDecimal underlying_price_usd; biz::BigDecimal mark_price_usd; double ivs[3]; double iv;
                   double iv_range_up; double iv_range_down; double price_interval[11]; double price_range;
                   biz::BigDecimal option_interest_rate; biz::BigDecimal strike_price_usd; std::string option_type;
                   biz::time_sec_t delivery_time_in_sec; biz::time_sec_t now_in_sec);

  TestCase test_cases[] = {
      {{
           .pnl_matrix = {},
           .underlying_price_usd{biz::BigDecimal("18000")},
           .mark_price_usd{biz::BigDecimal("500")},
           .ivs = {},
           .iv = 0.28,
           .iv_range_up = 0.34,
           .iv_range_down = 0.12,
           .price_interval = {},
           .price_range = 0.01,
           .option_interest_rate{biz::BigDecimal("0.025")},
           .strike_price_usd{biz::BigDecimal("18500")},
           .option_type{"P"},
           .delivery_time_in_sec = 1680249600,
           .now_in_sec = 1680082593,
       },
       {{biz::BigDecimal("900.83"), biz::BigDecimal("724.81"), biz::BigDecimal("552.44"), biz::BigDecimal("385.964"),
         biz::BigDecimal("228.257"), biz::BigDecimal("82.538"), biz::BigDecimal("-48.054"), biz::BigDecimal("-160.992"),
         biz::BigDecimal("-254.83"), biz::BigDecimal("-329.474"), biz::BigDecimal("-386.162")},
        {biz::BigDecimal("897.56"), biz::BigDecimal("717.59"), biz::BigDecimal("537.8"), biz::BigDecimal("358.786"),
         biz::BigDecimal("182.429"), biz::BigDecimal("13.15"), biz::BigDecimal("-141.346"), biz::BigDecimal("-271.482"),
         biz::BigDecimal("-369.738"), biz::BigDecimal("-434.6493"), biz::BigDecimal("-471.5193")},
        {biz::BigDecimal("897.55"), biz::BigDecimal("717.55"), biz::BigDecimal("537.55"), biz::BigDecimal("357.602"),
         biz::BigDecimal("178.075"), biz::BigDecimal("1.103"), biz::BigDecimal("-166.017"), biz::BigDecimal("-308.435"),
         biz::BigDecimal("-410.0907"), biz::BigDecimal("-466.9861"), biz::BigDecimal("-490.83412")}}}};

  for (const auto& test_case : test_cases) {
    TestClass::BuildIvs(
        const_cast<double*>(test_case.args.ivs), test_case.args.iv, test_case.args.iv_range_up,
        test_case.args.iv_range_down,
        TestClass::CalcRemainExpireDays(test_case.args.now_in_sec, test_case.args.delivery_time_in_sec));

    TestClass::BuildOptPriceInterval(const_cast<double*>(test_case.args.price_interval), test_case.args.price_range,
                                     test_case.args.now_in_sec, test_case.args.delivery_time_in_sec);

    std::array<std::array<double, 11>, 3> input{};
    TestClass::ReCalcPmOptionPnlMatrixInUsd(
        input, test_case.args.underlying_price_usd.ToDouble(), test_case.args.mark_price_usd.ToDouble(),
        test_case.args.ivs, test_case.args.price_interval, test_case.args.option_interest_rate.ToDouble(),
        test_case.args.strike_price_usd.ToDouble(), test_case.args.option_type, test_case.args.delivery_time_in_sec,
        test_case.args.now_in_sec);

    for (int i = 0; i < 3; ++i) {
      for (int j = 0; j < 11; ++j) {
        EXPECT_LE(std::abs(input[i][j] - test_case.result[i][j].ToDouble()), 0.01);
      }
    }
  }
}

TEST_F(WalletTest, BuildIvs) {
  DEFINE_TEST_CASE(biz::BigDecimal result[3], double ivs[3]; double cur_iv; double iv_range_up; double iv_range_down;
                   int remain_days);

  TestCase test_cases[] = {
      {{.ivs = {}, .cur_iv = 0.12, .iv_range_up = 0.28, .iv_range_down = 0.34, .remain_days = 5},
       {biz::BigDecimal("0.1775154672761661"), biz::BigDecimal("0.12"), biz::BigDecimal("0.05015978973608403")}},
      {{.ivs = {}, .cur_iv = 0.12, .iv_range_up = 0.28, .iv_range_down = 2, .remain_days = 5},
       {biz::BigDecimal("0.1775154672761661"), biz::BigDecimal("0.12"), biz::BigDecimal("0.0001")}}};

  for (const auto& test_case : test_cases) {
    TestClass::BuildIvs(const_cast<double*>(test_case.args.ivs), test_case.args.cur_iv, test_case.args.iv_range_up,
                        test_case.args.iv_range_down, test_case.args.remain_days);
    for (int i = 0; i < 3; ++i) {
      ASSERT_DOUBLE_EQ(test_case.args.ivs[i], test_case.result[i].ToDouble());
    }
  }
}

TEST_F(WalletTest, BuildOptPriceInterval) {
  DEFINE_TEST_CASE(biz::BigDecimal result[11], double price_interval[11]; biz::BigDecimal price_range_interval;
                   biz::time_sec_t now_in_sec; biz::time_sec_t expire_time_in_sec);

  TestCase test_cases[] = {
      {{.price_interval = {},
        .price_range_interval{biz::BigDecimal("0.12")},
        .now_in_sec = 1,
        .expire_time_in_sec = 3600},
       {biz::BigDecimal("-0.6"), biz::BigDecimal("-0.48"), biz::BigDecimal("-0.36"), biz::BigDecimal("-0.24"),
        biz::BigDecimal("-0.12"), biz::BigDecimal("0"), biz::BigDecimal("0.12"), biz::BigDecimal("0.24"),
        biz::BigDecimal("0.36"), biz::BigDecimal("0.48"), biz::BigDecimal("0.6")}},
      {{.price_interval = {},
        .price_range_interval{biz::BigDecimal("0.12")},
        .now_in_sec = 1,
        .expire_time_in_sec = 10},
       {biz::BigDecimal("-0.003"), biz::BigDecimal("-0.0024"), biz::BigDecimal("-0.0018"), biz::BigDecimal("-0.0012"),
        biz::BigDecimal("-0.0006"), biz::BigDecimal("0"), biz::BigDecimal("0.0006"), biz::BigDecimal("0.0012"),
        biz::BigDecimal("0.0018"), biz::BigDecimal("0.0024"), biz::BigDecimal("0.003")}},
      {{.price_interval = {}, .price_range_interval{biz::BigDecimal("0.12")}, .now_in_sec = 1, .expire_time_in_sec = 1},
       {biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"),
        biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"),
        biz::BigDecimal("0")}},
      {{.price_interval = {}, .price_range_interval{biz::BigDecimal("0.12")}, .now_in_sec = 1, .expire_time_in_sec = 0},
       {biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"),
        biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"), biz::BigDecimal("0"),
        biz::BigDecimal("0")}}};

  for (const auto& test_case : test_cases) {
    TestClass::BuildOptPriceInterval(const_cast<double*>(test_case.args.price_interval),
                                     test_case.args.price_range_interval.ToDouble(), test_case.args.now_in_sec,
                                     test_case.args.expire_time_in_sec);
    for (int i = 0; i < 11; ++i) {
      ASSERT_DOUBLE_EQ(test_case.args.price_interval[i], test_case.result[i].ToDouble());
    }
  }
}
