#include "test/biz/wallet_service_ut/pmunit/refresh_pm_risk_unit_service_test.hpp"

TEST_F(WalletTest, RefreshOptionNetShortContingency) {
  auto header = std::make_shared<store::Header>();
  auto user_store = std::make_shared<store::PerUserStore>(12345678, ELoadDumpStatus::Loaded);
  biz::DraftPkg::Ptr draft_pkg = std::make_shared<biz::DraftPkg>(nullptr, header, workerStore.get(), user_store.get());

  auto opt_cfg_1 = config::SymbolDTO();
  opt_cfg_1.symbol_name = "BTC-12000-C";
  opt_cfg_1.symbol_type = "C";
  opt_cfg_1.id = 1;
  opt_cfg_1.settle_coin = "USDC";
  opt_cfg_1.base_coin = "TEST1";
  opt_cfg_1.quote_coin = "USD";
  opt_cfg_1.strike_price = biz::BigDecimal(12000);
  opt_cfg_1.delivery_time = 12;

  ASSERT_EQ(workerStore->NetShortPositionsFactor(opt_cfg_1.base_coin), biz::BigDecimal("0.009"));

  // 刷入期权1
  auto cow_pm_risk_unit = draft_pkg->CreateOrAttachPmRiskUnit(biz::coin_name_t("TEST1"));
  auto lazy_ru = cow_pm_risk_unit->LazyDraftPRU();
  lazy_ru->base_coin_usd_index = 10;

  MockOptionSymbol(opt_cfg_1);

  const biz::str16_t index_key(fmt::format("{}-{}", opt_cfg_1.base_coin, opt_cfg_1.quote_coin));
  ASSERT_EQ(*workerStore->GetOptionIndexPrice(index_key), biz::BigDecimal("20000"));

  auto cow_margin_cost_opt_c1 =
      draft_pkg->CreateOrAttachDerivativesSymbolMarginCost(ECoin::USDC, opt_cfg_1.id, EContractType::Option);
  auto& draft_margin_cost = *cow_margin_cost_opt_c1->LazyDraftMC();
  draft_margin_cost.pm_basecoin_size[0] = -1;
  draft_margin_cost.pm_basecoin_size[1] = -2;
  draft_margin_cost.pm_basecoin_size[2] = 1;
  draft_margin_cost.base_coin_name = "TEST1";
  draft_margin_cost.expire_time_in_sec = opt_cfg_1.delivery_time;

  TestClass::RefreshOptionNetShortContingency(cow_pm_risk_unit, cow_margin_cost_opt_c1->cur_margin_cost,
                                              cow_margin_cost_opt_c1->draft_margin_cost, draft_pkg.get());

  biz::time_sec_t now = 0;

  TestClass::ReCalcOptionNetShortContingency(cow_pm_risk_unit, draft_pkg.get(), now);

  auto pm_calc_unit_array = cow_pm_risk_unit->LazyDraftPRU()->pm_calc_unit_array[0];
  ASSERT_EQ(pm_calc_unit_array.total_net_short, -1);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_1.delivery_time].net_short_call, -1);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_1.delivery_time].net_short_put, 0);
  ASSERT_EQ(pm_calc_unit_array.option_net_short_contingency, 0.09);  // abs(-1)*0.009*10=0.09

  pm_calc_unit_array = cow_pm_risk_unit->LazyDraftPRU()->pm_calc_unit_array[1];
  ASSERT_EQ(pm_calc_unit_array.total_net_short, -2);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_1.delivery_time].net_short_call, -2);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_1.delivery_time].net_short_put, 0);
  ASSERT_EQ(pm_calc_unit_array.option_net_short_contingency, 0.18);  // abs(-2)*0.009*10=0.18

  pm_calc_unit_array = cow_pm_risk_unit->LazyDraftPRU()->pm_calc_unit_array[2];
  ASSERT_EQ(pm_calc_unit_array.total_net_short, 0);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_1.delivery_time].net_short_call, 0);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_1.delivery_time].net_short_put, 0);
  ASSERT_EQ(pm_calc_unit_array.option_net_short_contingency, 0);  // abs(0)*0.009*10=0

  // 重刷期权1不变
  TestClass::RefreshOptionNetShortContingency(cow_pm_risk_unit, cow_margin_cost_opt_c1->cur_margin_cost,
                                              cow_margin_cost_opt_c1->draft_margin_cost, draft_pkg.get());
  TestClass::ReCalcOptionNetShortContingency(cow_pm_risk_unit, draft_pkg.get(), now);

  pm_calc_unit_array = cow_pm_risk_unit->LazyDraftPRU()->pm_calc_unit_array[0];
  ASSERT_EQ(pm_calc_unit_array.total_net_short, -1);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_1.delivery_time].net_short_call, -1);
  ASSERT_EQ(pm_calc_unit_array.option_net_short_contingency, 0.09);  // abs(-1)*0.009*10=0.09

  // 刷入期权2
  auto opt_cfg_2 = config::SymbolDTO();
  opt_cfg_2.symbol_name = "BTC-13000-C";
  opt_cfg_2.symbol_type = "C";
  opt_cfg_2.id = 2;
  opt_cfg_2.settle_coin = "USDC";
  opt_cfg_2.base_coin = "TEST1";
  opt_cfg_2.strike_price = biz::BigDecimal(13000);
  opt_cfg_2.delivery_time = 12;

  MockOptionSymbol(opt_cfg_2);

  auto cow_margin_cost_opt_c2 =
      draft_pkg->CreateOrAttachDerivativesSymbolMarginCost(ECoin::USDC, opt_cfg_2.id, EContractType::Option);
  auto& draft_margin_cost_2 = *cow_margin_cost_opt_c2->LazyDraftMC();
  draft_margin_cost_2.pm_basecoin_size[0] = 2;
  draft_margin_cost_2.base_coin_name = "TEST1";
  draft_margin_cost_2.expire_time_in_sec = opt_cfg_2.delivery_time;

  TestClass::RefreshOptionNetShortContingency(cow_pm_risk_unit, cow_margin_cost_opt_c2->cur_margin_cost,
                                              cow_margin_cost_opt_c2->draft_margin_cost, draft_pkg.get());
  TestClass::ReCalcOptionNetShortContingency(cow_pm_risk_unit, draft_pkg.get(), now);

  pm_calc_unit_array = cow_pm_risk_unit->LazyDraftPRU()->pm_calc_unit_array[0];
  // call合约按照行权价从小到达排序
  // BTC-12000-C:-1 BTC-13000-C:2 => -1 低行权价的正size可以抵扣比它高价格的size，13000行权价期权以后无其他期权
  ASSERT_EQ(pm_calc_unit_array.total_net_short, -1);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_2.delivery_time].net_short_call, -1);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_2.delivery_time].net_short_put, 0);

  // 刷入期权3
  auto opt_cfg_3 = config::SymbolDTO();
  opt_cfg_3.symbol_name = "BTC-14000-C";
  opt_cfg_3.symbol_type = "C";
  opt_cfg_3.id = 3;
  opt_cfg_3.settle_coin = "USDC";
  opt_cfg_3.base_coin = "TEST1";
  opt_cfg_3.strike_price = biz::BigDecimal(14000);
  opt_cfg_3.delivery_time = 12;

  MockOptionSymbol(opt_cfg_3);

  auto cow_margin_cost_opt_c3 =
      draft_pkg->CreateOrAttachDerivativesSymbolMarginCost(ECoin::USDC, opt_cfg_3.id, EContractType::Option);
  auto& draft_margin_cost_3 = *cow_margin_cost_opt_c3->LazyDraftMC();
  draft_margin_cost_3.pm_basecoin_size[0] = -4;
  draft_margin_cost_3.base_coin_name = "TEST1";
  draft_margin_cost_3.expire_time_in_sec = opt_cfg_3.delivery_time;

  TestClass::RefreshOptionNetShortContingency(cow_pm_risk_unit, cow_margin_cost_opt_c3->cur_margin_cost,
                                              cow_margin_cost_opt_c3->draft_margin_cost, draft_pkg.get());
  TestClass::ReCalcOptionNetShortContingency(cow_pm_risk_unit, draft_pkg.get(), now);

  pm_calc_unit_array = cow_pm_risk_unit->LazyDraftPRU()->pm_calc_unit_array[0];
  // call合约按照行权价从小到达排序
  // BTC-12000-C:-1 BTC-13000-C:2 BTC-14000-C:-4 => -1+2+-4=-3
  ASSERT_EQ(pm_calc_unit_array.total_net_short, -3);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_2.delivery_time].net_short_call, -3);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_2.delivery_time].net_short_put, 0);

  // 刷入期权4
  auto opt_cfg_4 = config::SymbolDTO();
  opt_cfg_4.symbol_name = "BTC-14000-P";
  opt_cfg_4.symbol_type = "P";
  opt_cfg_4.id = 4;
  opt_cfg_4.settle_coin = "USDC";
  opt_cfg_4.base_coin = "TEST1";
  opt_cfg_4.strike_price = biz::BigDecimal(14000);
  opt_cfg_4.delivery_time = 12;

  MockOptionSymbol(opt_cfg_4);

  auto cow_margin_cost_opt_c4 =
      draft_pkg->CreateOrAttachDerivativesSymbolMarginCost(ECoin::USDC, opt_cfg_4.id, EContractType::Option);
  auto& draft_margin_cost_4 = *cow_margin_cost_opt_c4->LazyDraftMC();
  draft_margin_cost_4.pm_basecoin_size[0] = -4;
  draft_margin_cost_4.base_coin_name = "TEST1";
  draft_margin_cost_4.expire_time_in_sec = opt_cfg_4.delivery_time;

  TestClass::RefreshOptionNetShortContingency(cow_pm_risk_unit, cow_margin_cost_opt_c4->cur_margin_cost,
                                              cow_margin_cost_opt_c4->draft_margin_cost, draft_pkg.get());
  TestClass::ReCalcOptionNetShortContingency(cow_pm_risk_unit, draft_pkg.get(), now);

  pm_calc_unit_array = cow_pm_risk_unit->LazyDraftPRU()->pm_calc_unit_array[0];
  ASSERT_EQ(pm_calc_unit_array.total_net_short, -7);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_2.delivery_time].net_short_call, -3);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_2.delivery_time].net_short_put, -4);

  // 刷入期权5
  auto opt_cfg_5 = config::SymbolDTO();
  opt_cfg_5.symbol_name = "BTC-13000-P";
  opt_cfg_5.symbol_type = "P";
  opt_cfg_5.id = 5;
  opt_cfg_5.settle_coin = "USDC";
  opt_cfg_5.base_coin = "TEST1";
  opt_cfg_5.strike_price = biz::BigDecimal(13000);
  opt_cfg_5.delivery_time = 12;
  MockOptionSymbol(opt_cfg_5);
  auto cow_margin_cost_opt_c5 =
      draft_pkg->CreateOrAttachDerivativesSymbolMarginCost(ECoin::USDC, opt_cfg_5.id, EContractType::Option);
  auto& draft_margin_cost_5 = *cow_margin_cost_opt_c5->LazyDraftMC();
  draft_margin_cost_5.pm_basecoin_size[0] = 10;
  draft_margin_cost_5.base_coin_name = "TEST1";
  draft_margin_cost_5.expire_time_in_sec = opt_cfg_5.delivery_time;

  TestClass::RefreshOptionNetShortContingency(cow_pm_risk_unit, cow_margin_cost_opt_c5->cur_margin_cost,
                                              cow_margin_cost_opt_c5->draft_margin_cost, draft_pkg.get());
  TestClass::ReCalcOptionNetShortContingency(cow_pm_risk_unit, draft_pkg.get(), now);

  pm_calc_unit_array = cow_pm_risk_unit->LazyDraftPRU()->pm_calc_unit_array[0];
  ASSERT_EQ(pm_calc_unit_array.total_net_short, -7);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_2.delivery_time].net_short_call, -3);
  // put合约按照行权价从高到低遍历
  // BTC-14000-P:-4 BTC-13000-P:10 => -4 高行权价size只能对冲低行权价size
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_2.delivery_time].net_short_put, -4);

  // 刷入期权6
  auto opt_cfg_6 = config::SymbolDTO();
  opt_cfg_6.symbol_name = "BTC-12000-P";
  opt_cfg_6.symbol_type = "P";
  opt_cfg_6.id = 6;
  opt_cfg_6.settle_coin = "USDC";
  opt_cfg_6.base_coin = "TEST1";
  opt_cfg_6.strike_price = biz::BigDecimal(12000);
  opt_cfg_6.delivery_time = 12;
  MockOptionSymbol(opt_cfg_6);
  auto cow_margin_cost_opt_c6 =
      draft_pkg->CreateOrAttachDerivativesSymbolMarginCost(ECoin::USDC, opt_cfg_6.id, EContractType::Option);
  auto& draft_margin_cost_6 = *cow_margin_cost_opt_c6->LazyDraftMC();
  draft_margin_cost_6.pm_basecoin_size[0] = -8;
  draft_margin_cost_6.base_coin_name = "TEST1";
  draft_margin_cost_6.expire_time_in_sec = opt_cfg_6.delivery_time;

  TestClass::RefreshOptionNetShortContingency(cow_pm_risk_unit, cow_margin_cost_opt_c6->cur_margin_cost,
                                              cow_margin_cost_opt_c6->draft_margin_cost, draft_pkg.get());
  TestClass::ReCalcOptionNetShortContingency(cow_pm_risk_unit, draft_pkg.get(), now);

  pm_calc_unit_array = cow_pm_risk_unit->LazyDraftPRU()->pm_calc_unit_array[0];
  ASSERT_EQ(pm_calc_unit_array.total_net_short, -7);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_2.delivery_time].net_short_call, -3);
  // put合约按照行权价从高到低遍历
  // BTC-14000-P:-4 BTC-13000-P:10 BTC-12000-P：-8 => -4+min(10-8,0) 高行权价size只能对冲低行权价size
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_2.delivery_time].net_short_put, -4);

  // 刷入期权7
  auto opt_cfg_7 = config::SymbolDTO();
  opt_cfg_7.symbol_name = "BTC-11000-P";
  opt_cfg_7.symbol_type = "P";
  opt_cfg_7.id = 7;
  opt_cfg_7.settle_coin = "USDC";
  opt_cfg_7.base_coin = "TEST1";
  opt_cfg_7.strike_price = biz::BigDecimal(11000);
  opt_cfg_7.delivery_time = 12;
  MockOptionSymbol(opt_cfg_7);
  auto cow_margin_cost_opt_c7 =
      draft_pkg->CreateOrAttachDerivativesSymbolMarginCost(ECoin::USDC, opt_cfg_7.id, EContractType::Option);
  auto& draft_margin_cost_7 = *cow_margin_cost_opt_c7->LazyDraftMC();
  draft_margin_cost_7.pm_basecoin_size[0] = -8;
  draft_margin_cost_7.base_coin_name = "TEST1";
  draft_margin_cost_7.expire_time_in_sec = opt_cfg_7.delivery_time;

  TestClass::RefreshOptionNetShortContingency(cow_pm_risk_unit, cow_margin_cost_opt_c7->cur_margin_cost,
                                              cow_margin_cost_opt_c7->draft_margin_cost, draft_pkg.get());
  TestClass::ReCalcOptionNetShortContingency(cow_pm_risk_unit, draft_pkg.get(), now);

  pm_calc_unit_array = cow_pm_risk_unit->LazyDraftPRU()->pm_calc_unit_array[0];
  ASSERT_EQ(pm_calc_unit_array.total_net_short, -13);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_2.delivery_time].net_short_call, -3);
  // put合约按照行权价从高到低遍历
  // BTC-14000-P:-4 BTC-13000-P:10 BTC-12000-P：-8 => -4+min(10-8-8,0)=-10 高行权价size只能对冲低行权价size
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_2.delivery_time].net_short_put, -10);

  // 刷入期权8
  auto opt_cfg_8 = config::SymbolDTO();
  opt_cfg_8.symbol_name = "BTC-10000-P";
  opt_cfg_8.symbol_type = "P";
  opt_cfg_8.id = 8;
  opt_cfg_8.settle_coin = "USDC";
  opt_cfg_8.base_coin = "TEST1";
  opt_cfg_8.strike_price = biz::BigDecimal(10000);
  opt_cfg_8.delivery_time = 11;
  MockOptionSymbol(opt_cfg_8);
  auto cow_margin_cost_opt_c8 =
      draft_pkg->CreateOrAttachDerivativesSymbolMarginCost(ECoin::USDC, opt_cfg_8.id, EContractType::Option);
  auto& draft_margin_cost_8 = *cow_margin_cost_opt_c8->LazyDraftMC();
  draft_margin_cost_8.pm_basecoin_size[0] = -8;
  draft_margin_cost_8.base_coin_name = "TEST1";
  draft_margin_cost_8.expire_time_in_sec = opt_cfg_8.delivery_time;

  TestClass::RefreshOptionNetShortContingency(cow_pm_risk_unit, cow_margin_cost_opt_c8->cur_margin_cost,
                                              cow_margin_cost_opt_c8->draft_margin_cost, draft_pkg.get());
  TestClass::ReCalcOptionNetShortContingency(cow_pm_risk_unit, draft_pkg.get(), now);

  pm_calc_unit_array = cow_pm_risk_unit->LazyDraftPRU()->pm_calc_unit_array[0];
  ASSERT_EQ(pm_calc_unit_array.total_net_short, -21);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_2.delivery_time].net_short_call, -3);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_2.delivery_time].net_short_put, -10);
  ASSERT_EQ(pm_calc_unit_array.pm_expire_time_in_sec_digest_map[opt_cfg_8.delivery_time].net_short_put, -8);
}

TEST_F(WalletTest, ReCalcOptionNetShortContingency) {
  auto header = std::make_shared<store::Header>();
  auto user_store = std::make_shared<store::PerUserStore>(12345678, ELoadDumpStatus::Loaded);
  biz::DraftPkg::Ptr draft_pkg = std::make_shared<biz::DraftPkg>(nullptr, header, workerStore.get(), user_store.get());

  auto cow_pm_risk_unit = std::make_shared<store::CowPmRiskUnit>();
  auto pm_risk_unit_cur = std::make_shared<store::PmRiskUnit>();
  pm_risk_unit_cur->base_coin_name = "TEST1";
  pm_risk_unit_cur->base_coin_usd_index = 10;
  pm_risk_unit_cur->pm_calc_unit_array[0].pm_expire_time_in_sec_digest_map[1].net_short_call = -3;
  pm_risk_unit_cur->pm_calc_unit_array[0].pm_expire_time_in_sec_digest_map[1].net_short_put = -3;
  pm_risk_unit_cur->pm_calc_unit_array[0].pm_expire_time_in_sec_digest_map[2].net_short_put = -3;
  cow_pm_risk_unit->cur_pm_risk_unit = (pm_risk_unit_cur);
  auto draft_pm_risk_unit = cow_pm_risk_unit->LazyDraftPRU();

  workerStore->exchange_rate_price_map_->insert_or_assign(biz::str32_t("USDC-USD"), biz::BigDecimal("1.1"));
  ASSERT_EQ(workerStore->NetShortPositionsFactor("TEST1"), biz::BigDecimal("0.009"));

  TestClass::ReCalcOptionNetShortContingency(cow_pm_risk_unit, draft_pkg.get(), 0);
  ASSERT_EQ(draft_pm_risk_unit->pm_calc_unit_array[0].total_net_short, -9);
  ASSERT_DOUBLE_EQ(draft_pm_risk_unit->pm_calc_unit_array[0].option_net_short_contingency, 0.81);  // 9*0.009*10=0.81

  TestClass::ReCalcOptionNetShortContingency(cow_pm_risk_unit, draft_pkg.get(), 1);
  ASSERT_EQ(draft_pm_risk_unit->pm_calc_unit_array[0].total_net_short, -3);
  ASSERT_DOUBLE_EQ(draft_pm_risk_unit->pm_calc_unit_array[0].option_net_short_contingency, 0.27);  // 3*0.009*10=0.27

  TestClass::ReCalcOptionNetShortContingency(cow_pm_risk_unit, draft_pkg.get(), 2);
  ASSERT_EQ(draft_pm_risk_unit->pm_calc_unit_array[0].total_net_short, 0);
  ASSERT_DOUBLE_EQ(draft_pm_risk_unit->pm_calc_unit_array[0].option_net_short_contingency, 0);  // 0*0.009*10=0
}
