//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/3/22.
//

#ifndef TEST_BIZ_WALLET_SERVICE_UT_PROCESS_TRADING_RESULT_TRANS_LOG_SPOT_HPP_
#define TEST_BIZ_WALLET_SERVICE_UT_PROCESS_TRADING_RESULT_TRANS_LOG_SPOT_HPP_

#include <iostream>
#include <memory>

#include "test/biz/wallet_service_ut/wallet_test.hpp"

class TransLogSpotTest : public WalletTest {
  static void MockSpotMatchingResultForBuy(biz::DraftPkg* draft_pkg, int64_t amount = 10000, int64_t fee = 2000);
  static void MockSpotMatchingResultForSell(biz::DraftPkg* draft_pkg);
  static void ClearWalletAndTransLogs(biz::DraftPkg* draft_pkg, int coin);
};

#endif  // TEST_BIZ_WALLET_SERVICE_UT_PROCESS_TRADING_RESULT_TRANS_LOG_SPOT_HPP_
