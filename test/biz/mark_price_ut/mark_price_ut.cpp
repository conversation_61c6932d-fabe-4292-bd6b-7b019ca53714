#include <fmt/format.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include <stdlib.h>

#include <bbase/common/blogger/blogger.hpp>
#include <bbase/common/hdts/hdts.hpp>
#include <bbase/common/nacos_client/nacos_client_impl.hpp>

#include "data/type/biz_type.hpp"
#include "models/quote/market_price.pb.h"
#include "src/application/application.hpp"
#include "src/application/market_data/mark_price/mark_price_metrics.hpp"
#include "src/application/market_data/mark_price/mark_price_synchronizer.hpp"
#include "src/common/worker/type.hpp"
#include "src/config/config_proxy.hpp"
#include "src/monitor/monitor.hpp"
#include "test/mocks/hdts/dtssdk.hpp"

static auto mp_ut_app = std::make_shared<application::App>();

class MpMockHdtsMessage : public dtssdk::Message {
  friend class HdtsTopic;

 public:
  MpMockHdtsMessage() : error_(dtssdk::kMsg_Confirmed) {}
  ~MpMockHdtsMessage() override {}

 public:
  dtssdk::SysEventCode State() override { return error_; }

  std::string TopicName() const override { return topic_; }

  void* PayLoad() const override { return const_cast<char*>(pay_load_.c_str()); }

  std::int32_t Len() const override { return pay_load_.size(); }

  std::int64_t Offset() const override { return offset_; }

  void* GetHeadersPtr() override { return const_cast<char*>(headers_.c_str()); }

  std::int32_t GetHeadersLen() override { return headers_.size(); }

  std::int64_t CommitTimeStamp() override { return commit_time_; }

  std::int64_t ConfirmTimeStamp() override { return confirm_time_; }

  void* MsgOpaque() const override { return opaque_; }

  bool IsEndMsg() override { return false; }

  std::uint64_t GetBitmapTags() const override { return 0; }

  bool GetIsReliable() const override { return true; }

 private:
  std::string topic_;
  dtssdk::SysEventCode error_;
  std::int64_t offset_;
  std::string pay_load_;
  std::string headers_;
  void* opaque_{nullptr};
  std::int64_t commit_time_;
  std::int64_t confirm_time_;
};

class MarkPriceTest : public testing::Test {
 public:
  void SetUp() override {
    synchronizer_ = std::make_shared<biz::MarkPriceSynchronizer>(mp_ut_app.get(), mp_ut_app->pipeline());
  }

  void TearDown() override {}
  static void SetUpTestSuite() {}
  static void TearDownTestSuite() {}
  static void waitRecvHandler() {
    auto& io_service = config::ConfigProxy::Instance().io_service_;
    bool io_service_arrived = false;
    int i = 0;
    io_service.post([&] { io_service_arrived = true; });
    usleep(1000);
    while (++i < 1000) {
      if (io_service_arrived) {
        break;
      }
      usleep(10000);
    }
    if (i >= 1000) {
      LOG_ERROR("io service timeout, recv handler may not take effect");
    }
  }

 protected:
  // std::shared_ptr<application::App> app_;
  std::shared_ptr<biz::MarkPriceSynchronizer> synchronizer_;
};

TEST_F(MarkPriceTest, mark_price_synchronizer_start) {
  bbase::hdts::Offset low_offset_temp = -1, high_offset_temp = -1;
  bbase::hdts::Hdts::GetLowWaterOffset("aaa", &low_offset_temp);
  bbase::hdts::Hdts::GetHighWaterOffset("aaa", &high_offset_temp);

  synchronizer_->Start();
  EXPECT_EQ(synchronizer_->consumers_.size(), 3);
  synchronizer_->NotifyPriceSynced(biz::QuoteType::kUnknown);
  synchronizer_->NotifyPriceSynced(biz::QuoteType::kFuturePrice);
  synchronizer_->NotifyPriceSynced(biz::QuoteType::kOptionPrice);
  synchronizer_->NotifyPriceSynced(biz::QuoteType::kExchangeRate);
  synchronizer_->Stop();
  // 每个用例执行完就清空一下
  dtssdk::IDtsSdk::Instance()->NewConsumer()->Close();  // 关闭所有consumer线程
  dtssdk::IDtsSdk::Destroy();
}

TEST_F(MarkPriceTest, mark_price_synchronizer_start_metrics) {
  bbase::hdts::Offset low_offset_temp = -1, high_offset_temp = -1;
  bbase::hdts::Hdts::GetLowWaterOffset("aaa", &low_offset_temp);
  bbase::hdts::Hdts::GetHighWaterOffset("aaa", &high_offset_temp);

  synchronizer_->Start(true);
  EXPECT_EQ(synchronizer_->consumers_.size(), 3);
  synchronizer_->Stop();
  // 每个用例执行完就清空一下
  dtssdk::IDtsSdk::Instance()->NewConsumer()->Close();  // 关闭所有consumer线程
  dtssdk::IDtsSdk::Destroy();
}

TEST_F(MarkPriceTest, mark_price_synchronizer_mock) { synchronizer_->OnMarkPriceMock(); }

TEST_F(MarkPriceTest, test_future_price) {
  models::quote::FuturePriceResponse future_price_resp;
  future_price_resp.set_reqid("100");
  models::quote::FuturePrice* fpbtcusdt = future_price_resp.add_futureprice();
  fpbtcusdt->set_symbolname("BTCUSDT");
  fpbtcusdt->set_basecoin("USDT");
  fpbtcusdt->set_quotecoin("USDT");
  com::bybit::option::common::PrecisionDecimal* price = new com::bybit::option::common::PrecisionDecimal();
  price->set_unscaledvalue(66);
  price->set_scale(7);
  fpbtcusdt->set_allocated_indexprice(price);
  com::bybit::option::common::PrecisionDecimal* mprice = new com::bybit::option::common::PrecisionDecimal();
  mprice->set_unscaledvalue(55);
  mprice->set_scale(4);
  fpbtcusdt->set_allocated_markprice(mprice);
  fpbtcusdt->set_timestamp(9999);
  fpbtcusdt->set_originaltimestamp(9999);
  com::bybit::option::common::PrecisionDecimal* lprice = new com::bybit::option::common::PrecisionDecimal();
  lprice->set_unscaledvalue(33);
  lprice->set_scale(2);
  fpbtcusdt->set_allocated_lastprice(lprice);
  fpbtcusdt->set_symbolid(5);

  models::quote::FuturePrice* fpbtcusd = future_price_resp.add_futureprice();
  fpbtcusd->set_symbolname("BTCUSD");
  fpbtcusd->set_basecoin("BTC");
  fpbtcusd->set_quotecoin("BTC");
  com::bybit::option::common::PrecisionDecimal* price0 = new com::bybit::option::common::PrecisionDecimal();
  price->set_unscaledvalue(66);
  price->set_scale(7);
  fpbtcusd->set_allocated_indexprice(price0);
  com::bybit::option::common::PrecisionDecimal* mprice0 = new com::bybit::option::common::PrecisionDecimal();
  mprice->set_unscaledvalue(55);
  mprice->set_scale(4);
  fpbtcusd->set_allocated_markprice(mprice0);
  fpbtcusd->set_timestamp(9999);
  fpbtcusd->set_originaltimestamp(9999);
  com::bybit::option::common::PrecisionDecimal* lprice0 = new com::bybit::option::common::PrecisionDecimal();
  lprice->set_unscaledvalue(33);
  lprice->set_scale(2);
  fpbtcusd->set_allocated_lastprice(lprice0);
  fpbtcusd->set_symbolid(1);

  models::quote::FuturePrice* fp_perp = future_price_resp.add_futureprice();
  fp_perp->set_symbolname("BTCPERP");
  fp_perp->set_basecoin("USDC");
  fp_perp->set_quotecoin("USDC");
  fp_perp->set_timestamp(9999);
  fp_perp->set_originaltimestamp(9999);
  fp_perp->set_symbolid(45);

  com::bybit::option::common::PrecisionDecimal* price1 = new com::bybit::option::common::PrecisionDecimal();
  price1->set_unscaledvalue(66);
  price1->set_scale(7);
  fp_perp->set_allocated_indexprice(price1);
  com::bybit::option::common::PrecisionDecimal* mprice1 = new com::bybit::option::common::PrecisionDecimal();
  mprice1->set_unscaledvalue(55);
  mprice1->set_scale(4);
  fp_perp->set_allocated_markprice(mprice1);

  models::quote::FuturePrice* fp_xrpusdt = future_price_resp.add_futureprice();
  fp_xrpusdt->set_symbolname("XRPUSDT");
  fp_xrpusdt->set_basecoin("USDT");
  fp_xrpusdt->set_quotecoin("USDT");
  fp_xrpusdt->set_timestamp(9999);
  fp_xrpusdt->set_originaltimestamp(9999);
  fp_xrpusdt->set_symbolid(8);

  com::bybit::option::common::PrecisionDecimal* price2 = new com::bybit::option::common::PrecisionDecimal();
  price2->set_unscaledvalue(77);
  price2->set_scale(7);
  fp_xrpusdt->set_allocated_indexprice(price2);
  com::bybit::option::common::PrecisionDecimal* mprice2 = new com::bybit::option::common::PrecisionDecimal();
  mprice2->set_unscaledvalue(55);
  mprice2->set_scale(4);
  fp_xrpusdt->set_allocated_markprice(mprice2);

  models::quote::FuturePrice* fp_ethusd = future_price_resp.add_futureprice();
  fp_ethusd->set_symbolname("ETHUSD");
  fp_ethusd->set_basecoin("ETH");
  fp_ethusd->set_quotecoin("USD");
  fp_ethusd->set_timestamp(9999);
  fp_ethusd->set_originaltimestamp(9999);
  fp_ethusd->set_symbolid(2);

  com::bybit::option::common::PrecisionDecimal* price_eth_usd = new com::bybit::option::common::PrecisionDecimal();
  price_eth_usd->set_unscaledvalue(77);
  price_eth_usd->set_scale(7);
  fp_ethusd->set_allocated_indexprice(price_eth_usd);
  com::bybit::option::common::PrecisionDecimal* mprice_eth_usd = new com::bybit::option::common::PrecisionDecimal();
  mprice_eth_usd->set_unscaledvalue(55);
  mprice_eth_usd->set_scale(4);
  fp_ethusd->set_allocated_markprice(mprice_eth_usd);

  models::quote::FuturePrice* fp_btcusdz20 = future_price_resp.add_futureprice();
  fp_btcusdz20->set_symbolname("BTCUSDZ20");
  fp_btcusdz20->set_basecoin("BTC");
  fp_btcusdz20->set_quotecoin("USD");
  fp_btcusdz20->set_timestamp(9999);
  fp_btcusdz20->set_originaltimestamp(9999);
  fp_btcusdz20->set_symbolid(11);

  com::bybit::option::common::PrecisionDecimal* price_btcusdz20 = new com::bybit::option::common::PrecisionDecimal();
  price_btcusdz20->set_unscaledvalue(77);
  price_btcusdz20->set_scale(7);
  fp_btcusdz20->set_allocated_indexprice(price_btcusdz20);
  com::bybit::option::common::PrecisionDecimal* mprice_btcusdz20 = new com::bybit::option::common::PrecisionDecimal();
  mprice_btcusdz20->set_unscaledvalue(55);
  mprice_btcusdz20->set_scale(4);
  fp_btcusdz20->set_allocated_markprice(mprice_btcusdz20);

  models::quote::FuturePrice* fp_btc = future_price_resp.add_futureprice();
  fp_btc->set_symbolname("BTC-26MAY23");
  fp_btc->set_basecoin("BTC");
  fp_btc->set_quotecoin("USDT");
  fp_btc->set_timestamp(9999);
  fp_btc->set_originaltimestamp(9999);
  fp_btc->set_symbolid(284);

  com::bybit::option::common::PrecisionDecimal* price_fp_btc1 = new com::bybit::option::common::PrecisionDecimal();
  price_fp_btc1->set_unscaledvalue(77);
  price_fp_btc1->set_scale(4);
  fp_btc->set_allocated_indexprice(price_fp_btc1);
  com::bybit::option::common::PrecisionDecimal* price_fp_btc2 = new com::bybit::option::common::PrecisionDecimal();
  price_fp_btc2->set_unscaledvalue(55);
  price_fp_btc2->set_scale(4);
  fp_btc->set_allocated_markprice(price_fp_btc2);

  models::quote::FuturePrice* fp_eth = future_price_resp.add_futureprice();
  fp_eth->set_symbolname("ETH-26MAY23");
  fp_eth->set_basecoin("ETH");
  fp_eth->set_quotecoin("USDT");
  fp_eth->set_timestamp(9999);
  fp_eth->set_originaltimestamp(9999);
  fp_eth->set_symbolid(289);

  com::bybit::option::common::PrecisionDecimal* price_fp_eth1 = new com::bybit::option::common::PrecisionDecimal();
  price_fp_eth1->set_unscaledvalue(77);
  price_fp_eth1->set_scale(4);
  fp_eth->set_allocated_indexprice(price_fp_eth1);
  com::bybit::option::common::PrecisionDecimal* price_fp_eth2 = new com::bybit::option::common::PrecisionDecimal();
  price_fp_eth2->set_unscaledvalue(55);
  price_fp_eth2->set_scale(4);
  fp_eth->set_allocated_markprice(price_fp_eth2);
  // EXPECT_EQ(fp->symbolid(), 5);

  models::quote::FuturePrice* fp_alpha = future_price_resp.add_futureprice();
  fp_alpha->set_symbolname("ALPHAUSDT");
  fp_alpha->set_basecoin("ALPHA");
  fp_alpha->set_quotecoin("USDT");
  fp_alpha->set_timestamp(9999);
  fp_alpha->set_originaltimestamp(9999);
  fp_alpha->set_symbolid(180);

  com::bybit::option::common::PrecisionDecimal* price_fp_alpha1 = new com::bybit::option::common::PrecisionDecimal();
  price_fp_eth1->set_unscaledvalue(77);
  price_fp_eth1->set_scale(4);
  fp_alpha->set_allocated_indexprice(price_fp_alpha1);
  com::bybit::option::common::PrecisionDecimal* price_fp_alpha2 = new com::bybit::option::common::PrecisionDecimal();
  price_fp_eth2->set_unscaledvalue(55);
  price_fp_eth2->set_scale(4);
  fp_alpha->set_allocated_markprice(price_fp_alpha2);
  // EXPECT_EQ(fp->symbolid(), 5);

  models::quote::FuturePrice* future_fp_btc = future_price_resp.add_futureprice();
  future_fp_btc->set_symbolname("BTC-USDT-26MAY23");
  future_fp_btc->set_basecoin("BTC");
  future_fp_btc->set_quotecoin("USDT");
  future_fp_btc->set_timestamp(9999);
  future_fp_btc->set_originaltimestamp(9999);
  future_fp_btc->set_symbolid(300);

  com::bybit::option::common::PrecisionDecimal* price_fp_btc_index = new com::bybit::option::common::PrecisionDecimal();
  price_fp_btc_index->set_unscaledvalue(77);
  price_fp_btc_index->set_scale(4);
  future_fp_btc->set_allocated_indexprice(price_fp_btc_index);
  com::bybit::option::common::PrecisionDecimal* price_fp_btc_mark = new com::bybit::option::common::PrecisionDecimal();
  price_fp_btc_mark->set_unscaledvalue(55);
  price_fp_btc_mark->set_scale(4);
  future_fp_btc->set_allocated_markprice(price_fp_btc_mark);

  models::quote::FuturePrice* fp_sol = future_price_resp.add_futureprice();
  fp_sol->set_symbolname("SOLUSDT");
  fp_sol->set_basecoin("SOL");
  fp_sol->set_quotecoin("USDT");
  fp_sol->set_timestamp(9999);
  fp_sol->set_originaltimestamp(9999);
  fp_sol->set_symbolid(10);

  com::bybit::option::common::PrecisionDecimal* price_fp_sol_index = new com::bybit::option::common::PrecisionDecimal();
  price_fp_sol_index->set_unscaledvalue(77);
  price_fp_sol_index->set_scale(4);
  fp_sol->set_allocated_indexprice(price_fp_sol_index);
  com::bybit::option::common::PrecisionDecimal* price_fp_sol_mark = new com::bybit::option::common::PrecisionDecimal();
  price_fp_sol_mark->set_unscaledvalue(55);
  price_fp_sol_mark->set_scale(4);
  fp_sol->set_allocated_markprice(price_fp_sol_mark);

  models::quote::FuturePrice* fp_sol_future = future_price_resp.add_futureprice();
  fp_sol_future->set_symbolname("SOL-USDT-26MAY23");
  fp_sol_future->set_basecoin("SOL");
  fp_sol_future->set_quotecoin("USDT");
  fp_sol_future->set_timestamp(9999);
  fp_sol_future->set_originaltimestamp(9999);
  fp_sol_future->set_symbolid(199);

  com::bybit::option::common::PrecisionDecimal* price_fp_sol_future_index =
      new com::bybit::option::common::PrecisionDecimal();
  price_fp_sol_future_index->set_unscaledvalue(77);
  price_fp_sol_future_index->set_scale(4);
  fp_sol_future->set_allocated_indexprice(price_fp_sol_future_index);
  com::bybit::option::common::PrecisionDecimal* price_fp_sol_future_mark =
      new com::bybit::option::common::PrecisionDecimal();
  price_fp_sol_future_mark->set_unscaledvalue(55);
  price_fp_sol_future_mark->set_scale(4);
  fp_sol_future->set_allocated_markprice(price_fp_sol_future_mark);

  MpMockHdtsMessage msg;
  msg.topic_ = "aaa";
  msg.pay_load_ = future_price_resp.SerializeAsString();
  // // new consumer handle
  auto handler = std::make_shared<biz::FuturePriceHandle>(synchronizer_.get());
  handler->ProcessMessage(msg);

  // 跟future data对齐
  auto& mk_map = *handler->future_price()->symbol_mark_price_map();
  auto& idx_map = *handler->future_price()->symbol_index_price_map();

  EXPECT_EQ(mk_map[5], bbase::decimal::Decimal<>(55, -4));
  EXPECT_EQ(idx_map[5], bbase::decimal::Decimal<>(66, -7));

  EXPECT_EQ(synchronizer_->future_price_synced_, true);
  EXPECT_EQ(synchronizer_->option_price_synced_, false);
  EXPECT_EQ(synchronizer_->exchange_rate_synced_, false);
}

TEST_F(MarkPriceTest, test_future_price_update) {
  auto init_future_price = std::make_shared<biz::FuturePriceData>();
  init_future_price->InitMockData();

  auto handler = std::make_shared<biz::FuturePriceHandle>(synchronizer_.get());
  handler->set_future_price(init_future_price);

  // EXPECT_EQ(handler->symbol_price_received_map_.size(), 2);

  models::quote::FuturePriceResponse future_price_resp;
  future_price_resp.set_reqid("100");
  models::quote::FuturePrice* fp = future_price_resp.add_futureprice();
  fp->set_symbolname("BTCUSDT");
  fp->set_basecoin("USDT");
  fp->set_quotecoin("USDT");
  com::bybit::option::common::PrecisionDecimal* price = new com::bybit::option::common::PrecisionDecimal();
  price->set_unscaledvalue(66);
  price->set_scale(7);
  fp->set_allocated_indexprice(price);
  com::bybit::option::common::PrecisionDecimal* mprice = new com::bybit::option::common::PrecisionDecimal();
  mprice->set_unscaledvalue(55);
  mprice->set_scale(4);
  fp->set_allocated_markprice(mprice);
  fp->set_timestamp(9999);
  fp->set_originaltimestamp(9999);
  com::bybit::option::common::PrecisionDecimal* lprice = new com::bybit::option::common::PrecisionDecimal();
  lprice->set_unscaledvalue(33);
  lprice->set_scale(2);
  fp->set_allocated_lastprice(lprice);
  fp->set_symbolid(5);

  // EXPECT_EQ(fp->symbolid(), 5);
  MpMockHdtsMessage msg;
  msg.topic_ = "aaa";
  msg.pay_load_ = future_price_resp.SerializeAsString();

  // process
  handler->ProcessMessage(msg);

  models::quote::FuturePriceResponse future_price_resp1;
  future_price_resp1.set_reqid("101");
  models::quote::FuturePrice* fp1 = future_price_resp1.add_futureprice();
  fp1->set_symbolname("BTCUSDT");
  fp1->set_basecoin("USDT");
  fp1->set_quotecoin("USDT");
  com::bybit::option::common::PrecisionDecimal* price1 = new com::bybit::option::common::PrecisionDecimal();
  price1->set_unscaledvalue(66);
  price1->set_scale(7);
  fp1->set_allocated_indexprice(price1);
  com::bybit::option::common::PrecisionDecimal* mprice1 = new com::bybit::option::common::PrecisionDecimal();
  mprice1->set_unscaledvalue(55);
  mprice1->set_scale(4);
  fp1->set_allocated_markprice(mprice1);
  fp1->set_timestamp(10000);
  fp1->set_originaltimestamp(10000);
  com::bybit::option::common::PrecisionDecimal* lprice1 = new com::bybit::option::common::PrecisionDecimal();
  lprice1->set_unscaledvalue(33);
  lprice1->set_scale(2);
  fp1->set_allocated_lastprice(lprice1);
  fp1->set_symbolid(5);

  MpMockHdtsMessage msg1;
  msg1.topic_ = "aaa";
  msg1.pay_load_ = future_price_resp1.SerializeAsString();

  // process
  handler->ProcessMessage(msg1);

  auto& mk_map = *handler->future_price()->symbol_mark_price_map();
  auto& idx_map = *handler->future_price()->symbol_index_price_map();
  // 跟future data对齐
  EXPECT_EQ(mk_map[5], bbase::decimal::Decimal<>(55, -4));
  EXPECT_EQ(idx_map[5], bbase::decimal::Decimal<>(66, -7));
  // EXPECT_EQ(last_map[5], bbase::decimal::Decimal<>(33, 11));
  auto& up_idx = *handler->future_price()->updated_symbol_index_price();
  EXPECT_EQ(up_idx[0], 5);
  EXPECT_EQ(handler->future_price()->updated_symbol_mark_price()->back(), 5);

  EXPECT_EQ(synchronizer_->future_price_synced_, false);
  EXPECT_EQ(synchronizer_->option_price_synced_, false);
  EXPECT_EQ(synchronizer_->exchange_rate_synced_, false);
}

TEST_F(MarkPriceTest, test_option_price) {
  // 期权base coin
  auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  nacos_client->SetStringVar(config::ConstConfig::OPTION_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "usdc_base_coin_list", "BTC");
  nacos_client->SetStringVar(config::ConstConfig::OPTION_KV_DATA_ID, config::ConstConfig::UTA_GROUP,
                             "usdt_base_coin_list", "BTC");
  models::quote::QuotePrice quote_price;
  /*ob价格数据*/
  // message OrderBookPrice{
  //   com.bybit.option.common.PrecisionDecimal bestAsk = 1;
  //   com.bybit.option.common.PrecisionDecimal bestAskSize = 2;
  //   com.bybit.option.common.PrecisionDecimal bestBid = 3;
  //   com.bybit.option.common.PrecisionDecimal bestBidSize = 4;
  // }
  // // 表示数量相关的
  // message PrecisionDecimal {
  //     int64 unscaledValue = 1;
  //     int32 scale = 2;
  // }
  // // 用于表示价格，金额相关的
  // message Money {
  //     int64 unscaledValue = 1;
  //     int32 scale = 2;
  //     string coin = 3;
  // }
  //   int64 timestamp = 1;                                //UNIX时间戳，秒级
  //   string base_coin = 2;                               //标的币种
  //   string quote_coin = 3;                              //结算币种
  quote_price.set_timestamp(1679911434);
  quote_price.set_base_coin("BTC");
  quote_price.set_quote_coin("USD");

  // com.bybit.option.common.Money indexPrice = 4;
  com::bybit::option::common::Money* index_price = new com::bybit::option::common::Money();
  index_price->set_unscaledvalue(2839727000000);
  index_price->set_scale(8);
  index_price->set_coin("BTC");
  quote_price.set_allocated_indexprice(index_price);

  //   map<string, com.bybit.option.common.Money> markPriceMap = 5;
  auto& mp_map = *quote_price.mutable_markpricemap();
  com::bybit::option::common::Money mark_price1;
  mark_price1.set_unscaledvalue(30367871968);
  mark_price1.set_scale(8);
  mark_price1.set_coin("BTC");
  mp_map["BTC-31MAR23-20000-C"] = mark_price1;
  com::bybit::option::common::Money mark_price2;
  mark_price2.set_unscaledvalue(286439871968);
  mark_price2.set_scale(8);
  mark_price2.set_coin("BTC");
  mp_map["BTC-7APR23-31000-P"] = mark_price2;

  // map<int64, com.bybit.option.common.Money> underlyingPriceMap = 6;
  auto& up_map = *quote_price.mutable_underlyingpricemap();
  com::bybit::option::common::Money underlying_price;
  underlying_price.set_unscaledvalue(2851580000000);
  underlying_price.set_scale(8);
  underlying_price.set_coin("BTC");
  up_map[1682668800] = underlying_price;

  //   map<int64, com.bybit.option.common.Money> estimatedDeliveryPriceMap = 7;
  //   map<int64, com.bybit.option.common.Money> underlyingOriginPriceMap = 8;
  auto& ed_map = *quote_price.mutable_underlyingoriginpricemap();
  com::bybit::option::common::Money underlyingorigin_price;
  underlyingorigin_price.set_unscaledvalue(2851580000000);
  underlyingorigin_price.set_scale(8);
  underlyingorigin_price.set_coin("BTC");
  ed_map[1682668800] = underlyingorigin_price;

  //   map<string, com.bybit.option.common.PrecisionDecimal> markIVMap = 9;
  auto& mi_map = *quote_price.mutable_markivmap();
  com::bybit::option::common::PrecisionDecimal mark_iv;
  mark_iv.set_unscaledvalue(6527);
  mark_iv.set_scale(4);
  mi_map["BTC-31MAR23-20000-C"] = mark_iv;

  //   map<string, Greeks> greeksMap = 10;
  auto& gk_map = *quote_price.mutable_greeksmap();
  models::quote::Greeks gks;
  com::bybit::option::common::PrecisionDecimal* gks_delta = new com::bybit::option::common::PrecisionDecimal();
  gks_delta->set_unscaledvalue(99999994);
  gks_delta->set_scale(8);
  gks.set_allocated_delta(gks_delta);
  com::bybit::option::common::PrecisionDecimal* gks_vega = new com::bybit::option::common::PrecisionDecimal();
  gks_vega->set_unscaledvalue(535);
  gks_vega->set_scale(8);
  gks.set_allocated_vega(gks_vega);
  gk_map["BTC-31MAR23-20000-C"] = gks;

  //   map<string, com.bybit.option.common.PrecisionDecimal> unSmoothMarkIVMap = 11;//未平滑markiv
  auto& us_mark_map = *quote_price.mutable_unsmoothmarkivmap();
  com::bybit::option::common::PrecisionDecimal us_mark;
  us_mark.set_unscaledvalue(6527);
  us_mark.set_scale(4);
  us_mark_map["BTC-31MAR23-20000-C"] = us_mark;

  // map<string, OrderBookPrice> orderBookPriceMap = 12;
  auto& ob_map = *quote_price.mutable_orderbookpricemap();
  models::quote::OrderBookPrice ob;
  com::bybit::option::common::PrecisionDecimal* pd_bestask = new com::bybit::option::common::PrecisionDecimal();
  pd_bestask->set_unscaledvalue(1100000000);
  pd_bestask->set_scale(8);
  com::bybit::option::common::PrecisionDecimal* best_asksize = new com::bybit::option::common::PrecisionDecimal();
  best_asksize->set_unscaledvalue(10000);
  best_asksize->set_scale(4);
  com::bybit::option::common::PrecisionDecimal* best_bid = new com::bybit::option::common::PrecisionDecimal();
  best_bid->set_unscaledvalue(900000000);
  best_bid->set_scale(8);
  com::bybit::option::common::PrecisionDecimal* best_bid_size = new com::bybit::option::common::PrecisionDecimal();
  best_bid_size->set_unscaledvalue(10000);
  best_bid_size->set_scale(4);
  ob.set_allocated_bestask(pd_bestask);
  ob.set_allocated_bestbid(best_bid);
  ob.set_allocated_bestasksize(best_asksize);
  ob.set_allocated_bestbidsize(best_bid_size);
  ob_map["BTC-31MAR23-20000-C"] = ob;

  //   string reqId = 100;
  quote_price.set_reqid("Dovi5lE2yo-130");

  MpMockHdtsMessage msg;
  msg.topic_ = "aaa";
  msg.pay_load_ = quote_price.SerializeAsString();
  // // new consumer handle
  auto handler = std::make_shared<biz::OptionPriceHandle>(synchronizer_.get());
  handler->ProcessMessage(msg);

  auto& bid_map = *handler->option_price()->bid_price_map();
  auto& ask_map = *handler->option_price()->ask_price_map();
  auto& bid_size_map = *handler->option_price()->bid_size_map();
  auto& ask_size_map = *handler->option_price()->ask_size_map();
  auto& index_map = *handler->option_price()->index_price_map();
  auto& mark_map = *handler->option_price()->mark_price_map();
  auto& mark_iv_map = *handler->option_price()->mark_iv_map();
  auto& uns_mark_iv_map = *handler->option_price()->un_smooth_mark_iv_map();
  auto& underlying_map = *handler->option_price()->underlying_price_map();
  // 跟future data对齐
  EXPECT_EQ(bid_map[102311], bbase::decimal::Decimal<>(900000000, -8));
  EXPECT_EQ(ask_map[102311], bbase::decimal::Decimal<>(1100000000, -8));
  EXPECT_EQ(bid_size_map[102311], bbase::decimal::Decimal<>(10000, -4));
  EXPECT_EQ(ask_size_map[102311], bbase::decimal::Decimal<>(10000, -4));
  EXPECT_EQ(index_map[biz::str16_t("BTC-USD")], bbase::decimal::Decimal<>(2839727000000, -8));
  EXPECT_EQ(mark_map[102311], bbase::decimal::Decimal<>(30367871968, -8));
  EXPECT_EQ(mark_iv_map[102311], bbase::decimal::Decimal<>(6527, -4));
  EXPECT_EQ(uns_mark_iv_map[102311], bbase::decimal::Decimal<>(6527, -4));
  EXPECT_EQ(underlying_map[biz::str64_t("BTC-USD-1682668800")], bbase::decimal::Decimal<>(2851580000000, -8));

  models::quote::QuotePrice usdt_quote_price;
  usdt_quote_price.set_timestamp(1679911434);
  usdt_quote_price.set_base_coin("BTC");
  usdt_quote_price.set_quote_coin("USDT");

  com::bybit::option::common::Money* usdt_index_price = new com::bybit::option::common::Money();
  usdt_index_price->set_unscaledvalue(2839727000000);
  usdt_index_price->set_scale(8);
  usdt_index_price->set_coin("BTC");
  usdt_quote_price.set_allocated_indexprice(usdt_index_price);

  //   map<string, com.bybit.option.common.Money> markPriceMap = 5;
  auto& usdt_mp_map = *usdt_quote_price.mutable_markpricemap();
  com::bybit::option::common::Money usdt_mark_price1;
  usdt_mark_price1.set_unscaledvalue(30367871968);
  usdt_mark_price1.set_scale(8);
  usdt_mark_price1.set_coin("BTC");
  usdt_mp_map["BTC-31MAR23-20000-C-USDT"] = usdt_mark_price1;
  com::bybit::option::common::Money usdt_mark_price2;
  usdt_mark_price2.set_unscaledvalue(286439871968);
  usdt_mark_price2.set_scale(8);
  usdt_mark_price2.set_coin("BTC");
  usdt_mp_map["BTC-7APR23-31000-P-USDT"] = usdt_mark_price2;

  MpMockHdtsMessage usdt_msg;
  usdt_msg.topic_ = "aaa";
  usdt_msg.pay_load_ = usdt_quote_price.SerializeAsString();
  // // new consumer handle
  handler->ProcessMessage(usdt_msg);
  sleep(1);

  EXPECT_EQ(synchronizer_->option_price_synced_, true);

  // 设置更小的时间戳，此数据不会被处理
  quote_price.set_timestamp(1679910434);
  msg.topic_ = "aaa";
  msg.pay_load_ = quote_price.SerializeAsString();
  handler->ProcessMessage(msg);
}

TEST_F(MarkPriceTest, test_option_price_false) {
  // 期权base coin
  auto nacos_client = bbase::object_manager::ObjectManager::GetObjectRaw<bbase::nacos_client::NacosClientImpl>(
      bbase::object_manager::ObjectType::kObjectConfigCenter);
  nacos_client->SetStringVar(config::ConstConfig::OPTION_KV_DATA_ID, config::ConstConfig::UTA_GROUP, "base_coin_list",
                             "BTC,ETH");
  models::quote::QuotePrice quote_price;
  /*ob价格数据*/
  // message OrderBookPrice{
  //   com.bybit.option.common.PrecisionDecimal bestAsk = 1;
  //   com.bybit.option.common.PrecisionDecimal bestAskSize = 2;
  //   com.bybit.option.common.PrecisionDecimal bestBid = 3;
  //   com.bybit.option.common.PrecisionDecimal bestBidSize = 4;
  // }
  // // 表示数量相关的
  // message PrecisionDecimal {
  //     int64 unscaledValue = 1;
  //     int32 scale = 2;
  // }
  // // 用于表示价格，金额相关的
  // message Money {
  //     int64 unscaledValue = 1;
  //     int32 scale = 2;
  //     string coin = 3;
  // }
  //   int64 timestamp = 1;                                //UNIX时间戳，秒级
  //   string base_coin = 2;                               //标的币种
  //   string quote_coin = 3;                              //结算币种
  quote_price.set_timestamp(1679911434);
  quote_price.set_base_coin("BTC");
  quote_price.set_quote_coin("USD");

  // com.bybit.option.common.Money indexPrice = 4;
  com::bybit::option::common::Money* index_price = new com::bybit::option::common::Money();
  index_price->set_unscaledvalue(2839727000000);
  index_price->set_scale(8);
  index_price->set_coin("BTC");
  quote_price.set_allocated_indexprice(index_price);

  //   map<string, com.bybit.option.common.Money> markPriceMap = 5;
  auto& mp_map = *quote_price.mutable_markpricemap();
  com::bybit::option::common::Money mark_price1;
  mark_price1.set_unscaledvalue(30367871968);
  mark_price1.set_scale(8);
  mark_price1.set_coin("BTC");
  mp_map["BTC-31MAR23-20000-C"] = mark_price1;
  com::bybit::option::common::Money mark_price2;
  mark_price2.set_unscaledvalue(286439871968);
  mark_price2.set_scale(8);
  mark_price2.set_coin("BTC");
  mp_map["BTC-7APR23-31000-P"] = mark_price2;

  // map<int64, com.bybit.option.common.Money> underlyingPriceMap = 6;
  auto& up_map = *quote_price.mutable_underlyingpricemap();
  com::bybit::option::common::Money underlying_price;
  underlying_price.set_unscaledvalue(2851580000000);
  underlying_price.set_scale(8);
  underlying_price.set_coin("BTC");
  up_map[1682668800] = underlying_price;

  //   map<int64, com.bybit.option.common.Money> estimatedDeliveryPriceMap = 7;
  //   map<int64, com.bybit.option.common.Money> underlyingOriginPriceMap = 8;
  auto& ed_map = *quote_price.mutable_underlyingoriginpricemap();
  com::bybit::option::common::Money underlyingorigin_price;
  underlyingorigin_price.set_unscaledvalue(2851580000000);
  underlyingorigin_price.set_scale(8);
  underlyingorigin_price.set_coin("BTC");
  ed_map[1682668800] = underlyingorigin_price;

  //   map<string, com.bybit.option.common.PrecisionDecimal> markIVMap = 9;
  auto& mi_map = *quote_price.mutable_markivmap();
  com::bybit::option::common::PrecisionDecimal mark_iv;
  mark_iv.set_unscaledvalue(6527);
  mark_iv.set_scale(4);
  mi_map["BTC-31MAR23-20000-C"] = mark_iv;

  //   map<string, Greeks> greeksMap = 10;
  auto& gk_map = *quote_price.mutable_greeksmap();
  models::quote::Greeks gks;
  com::bybit::option::common::PrecisionDecimal* gks_delta = new com::bybit::option::common::PrecisionDecimal();
  gks_delta->set_unscaledvalue(99999994);
  gks_delta->set_scale(8);
  gks.set_allocated_delta(gks_delta);
  com::bybit::option::common::PrecisionDecimal* gks_vega = new com::bybit::option::common::PrecisionDecimal();
  gks_vega->set_unscaledvalue(535);
  gks_vega->set_scale(8);
  gks.set_allocated_vega(gks_vega);
  gk_map["BTC-31MAR23-20000-C"] = gks;

  //   map<string, com.bybit.option.common.PrecisionDecimal> unSmoothMarkIVMap = 11;//未平滑markiv
  auto& us_mark_map = *quote_price.mutable_unsmoothmarkivmap();
  com::bybit::option::common::PrecisionDecimal us_mark;
  us_mark.set_unscaledvalue(6527);
  us_mark.set_scale(4);
  us_mark_map["BTC-31MAR23-20000-C"] = us_mark;

  // map<string, OrderBookPrice> orderBookPriceMap = 12;
  auto& ob_map = *quote_price.mutable_orderbookpricemap();
  models::quote::OrderBookPrice ob;
  com::bybit::option::common::PrecisionDecimal* pd_bestask = new com::bybit::option::common::PrecisionDecimal();
  pd_bestask->set_unscaledvalue(1100000000);
  pd_bestask->set_scale(8);
  com::bybit::option::common::PrecisionDecimal* best_asksize = new com::bybit::option::common::PrecisionDecimal();
  best_asksize->set_unscaledvalue(10000);
  best_asksize->set_scale(4);
  com::bybit::option::common::PrecisionDecimal* best_bid = new com::bybit::option::common::PrecisionDecimal();
  best_bid->set_unscaledvalue(900000000);
  best_bid->set_scale(8);
  com::bybit::option::common::PrecisionDecimal* best_bid_size = new com::bybit::option::common::PrecisionDecimal();
  best_bid_size->set_unscaledvalue(10000);
  best_bid_size->set_scale(4);
  ob.set_allocated_bestask(pd_bestask);
  ob.set_allocated_bestbid(best_bid);
  ob.set_allocated_bestasksize(best_asksize);
  ob.set_allocated_bestbidsize(best_bid_size);
  ob_map["BTC-31MAR23-20000-C"] = ob;

  //   string reqId = 100;
  quote_price.set_reqid("Dovi5lE2yo-130");

  MpMockHdtsMessage msg;
  msg.topic_ = "aaa";
  msg.pay_load_ = quote_price.SerializeAsString();
  // // new consumer handle
  auto handler = std::make_shared<biz::OptionPriceHandle>(synchronizer_.get());
  handler->ProcessMessage(msg);

  auto& bid_map = *handler->option_price()->bid_price_map();
  auto& ask_map = *handler->option_price()->ask_price_map();
  auto& bid_size_map = *handler->option_price()->bid_size_map();
  auto& ask_size_map = *handler->option_price()->ask_size_map();
  auto& index_map = *handler->option_price()->index_price_map();
  auto& mark_map = *handler->option_price()->mark_price_map();
  auto& mark_iv_map = *handler->option_price()->mark_iv_map();
  auto& uns_mark_iv_map = *handler->option_price()->un_smooth_mark_iv_map();
  auto& underlying_map = *handler->option_price()->underlying_price_map();
  // 跟future data对齐
  EXPECT_EQ(bid_map[102311], bbase::decimal::Decimal<>(900000000, -8));
  EXPECT_EQ(ask_map[102311], bbase::decimal::Decimal<>(1100000000, -8));
  EXPECT_EQ(bid_size_map[102311], bbase::decimal::Decimal<>(10000, -4));
  EXPECT_EQ(ask_size_map[102311], bbase::decimal::Decimal<>(10000, -4));
  EXPECT_EQ(index_map[biz::str16_t("BTC-USD")], bbase::decimal::Decimal<>(2839727000000, -8));
  EXPECT_EQ(mark_map[102311], bbase::decimal::Decimal<>(30367871968, -8));
  EXPECT_EQ(mark_iv_map[102311], bbase::decimal::Decimal<>(6527, -4));
  EXPECT_EQ(uns_mark_iv_map[102311], bbase::decimal::Decimal<>(6527, -4));
  EXPECT_EQ(underlying_map[biz::str64_t("BTC-USD-1682668800")], bbase::decimal::Decimal<>(2851580000000, -8));

  EXPECT_EQ(synchronizer_->option_price_synced_, false);
}

TEST_F(MarkPriceTest, test_exchange_rate) {
  models::quote::ExchangeRateResponse exchange_rate_resp;

  //   int64 timestamp = 1;
  exchange_rate_resp.set_timestamp(1679625003);
  //   repeated ExchangeRate exchangeRate = 2;
  models::quote::ExchangeRate* er = exchange_rate_resp.add_exchangerate();
  er->set_fromcoin("USDT");
  er->set_tocoin("USD");
  com::bybit::option::common::PrecisionDecimal* rate = new com::bybit::option::common::PrecisionDecimal();
  rate->set_unscaledvalue(85842069);
  rate->set_scale(8);
  er->set_allocated_rate(rate);
  er->set_originaltimestamp(1679625003139);
  //   string reqId = 100;
  exchange_rate_resp.set_reqid("EqxWDmzcON");

  MpMockHdtsMessage msg;
  msg.topic_ = "aaa";
  msg.pay_load_ = exchange_rate_resp.SerializeAsString();
  // // new consumer handle
  auto handler = std::make_shared<biz::ExchangeRateHandle>(synchronizer_.get());
  handler->ProcessMessage(msg);

  // 这里很难检查；
  EXPECT_EQ(synchronizer_->exchange_rate_synced_, true);
}

TEST_F(MarkPriceTest, test_metrics_true) {
  synchronizer_->metrics_flag_ = true;
  // 耗时
  {
    biz::MetricsFunctionCost metircs_function_cost(true, monitor::kLabelFutureMp);
    usleep(2000);
  }
  {
    biz::MetricsFunctionCost metircs_function_cost(true, monitor::kLabelOptionMp);
    usleep(2000);
  }
  {
    biz::MetricsFunctionCost metircs_function_cost(true, monitor::kLabelErMp);
    usleep(2000);
  }
  {
    biz::MetricsFunctionCost metircs_function_cost(true, monitor::kLabelQuoteMp);
    usleep(2000);
  }
  // monitor::mark_price_latency_h[0]->v

  // future counter
  auto v00 = monitor::mark_price_counter_g[0][0]->Value();
  auto v01 = monitor::mark_price_counter_g[0][1]->Value();
  biz::MetricsMessageCount metrics_future_incoming1(true, monitor::kLabelFutureMp, 0);
  EXPECT_EQ(monitor::mark_price_counter_g[0][0]->Value(), v00 + 1);
  biz::MetricsMessageCount metrics_future_incoming2(true, monitor::kLabelFutureMp, 0);
  EXPECT_EQ(monitor::mark_price_counter_g[0][0]->Value(), v00 + 2);
  biz::MetricsMessageCount metrics_future_outgoing1(true, monitor::kLabelFutureMp, 1);
  EXPECT_EQ(monitor::mark_price_counter_g[0][1]->Value(), v01 + 1);
  biz::MetricsMessageCount metrics_future_outgoing2(true, monitor::kLabelFutureMp, 1);
  EXPECT_EQ(monitor::mark_price_counter_g[0][1]->Value(), v01 + 2);

  // option counter
  auto v10 = monitor::mark_price_counter_g[1][0]->Value();
  auto v11 = monitor::mark_price_counter_g[1][1]->Value();
  biz::MetricsMessageCount metrics_option_incoming1(true, monitor::kLabelOptionMp, 0);
  EXPECT_EQ(monitor::mark_price_counter_g[1][0]->Value(), v10 + 1);
  biz::MetricsMessageCount metrics_option_incoming2(true, monitor::kLabelOptionMp, 0);
  EXPECT_EQ(monitor::mark_price_counter_g[1][0]->Value(), v10 + 2);
  biz::MetricsMessageCount metrics_option_outgoing1(true, monitor::kLabelOptionMp, 1);
  EXPECT_EQ(monitor::mark_price_counter_g[1][1]->Value(), v11 + 1);
  biz::MetricsMessageCount metrics_option_outgoing2(true, monitor::kLabelOptionMp, 1);
  EXPECT_EQ(monitor::mark_price_counter_g[1][1]->Value(), v11 + 2);

  // exchange rate counter
  auto v20 = monitor::mark_price_counter_g[2][0]->Value();
  auto v21 = monitor::mark_price_counter_g[2][1]->Value();
  biz::MetricsMessageCount metrics_exchange_incoming1(true, monitor::kLabelErMp, 0);
  EXPECT_EQ(monitor::mark_price_counter_g[2][0]->Value(), v20 + 1);
  biz::MetricsMessageCount metrics_exchange_incoming2(true, monitor::kLabelErMp, 0);
  EXPECT_EQ(monitor::mark_price_counter_g[2][0]->Value(), v20 + 2);
  biz::MetricsMessageCount metrics_exchange_outgoing1(true, monitor::kLabelErMp, 1);
  EXPECT_EQ(monitor::mark_price_counter_g[2][1]->Value(), v21 + 1);
  biz::MetricsMessageCount metrics_exchange_outgoing2(true, monitor::kLabelErMp, 1);
  EXPECT_EQ(monitor::mark_price_counter_g[2][1]->Value(), v21 + 2);

  // future mark price
  biz::MetricsFuturePrice fp1(true, "mark_price", "BTCUSDT", bbase::decimal::Decimal<>(10000, -4));
  biz::MetricsFuturePrice fp2(true, "mark_price", "ETHUSDT", bbase::decimal::Decimal<>(20000, -4));
  biz::MetricsFuturePrice fp3(true, "mark_price", "ABCUSDT", bbase::decimal::Decimal<>(30000, -4));
  biz::MetricsFuturePrice fp4(true, "mark_price", "DEFUSDT", bbase::decimal::Decimal<>(40000, -4));
  auto mpg1 = monitor::mark_price_gauge("future", "mark_price", "BTCUSDT");
  EXPECT_EQ(mpg1->Value(), 1);
  auto mpg2 = monitor::mark_price_gauge("future", "mark_price", "ETHUSDT");
  EXPECT_EQ(mpg2->Value(), 2);
  auto mpg3 = monitor::mark_price_gauge("future", "mark_price", "ABCUSDT");
  EXPECT_EQ(mpg3->Value(), 3);
  auto mpg4 = monitor::mark_price_gauge("future", "mark_price", "DEFUSDT");
  EXPECT_EQ(mpg4->Value(), 4);

  // future index price
  biz::MetricsFuturePrice fi1(true, "index_price", "BTCUSDT", bbase::decimal::Decimal<>(10000, -4));
  biz::MetricsFuturePrice fi2(true, "index_price", "ETHUSDT", bbase::decimal::Decimal<>(20000, -4));
  biz::MetricsFuturePrice fi3(true, "index_price", "ABCUSDT", bbase::decimal::Decimal<>(30000, -4));
  biz::MetricsFuturePrice fi4(true, "index_price", "DEFUSDT", bbase::decimal::Decimal<>(40000, -4));
  auto mpg5 = monitor::mark_price_gauge("future", "index_price", "BTCUSDT");
  EXPECT_EQ(mpg5->Value(), 1);
  auto mpg6 = monitor::mark_price_gauge("future", "index_price", "ETHUSDT");
  EXPECT_EQ(mpg6->Value(), 2);
  auto mpg7 = monitor::mark_price_gauge("future", "index_price", "ABCUSDT");
  EXPECT_EQ(mpg7->Value(), 3);
  auto mpg8 = monitor::mark_price_gauge("future", "index_price", "DEFUSDT");
  EXPECT_EQ(mpg8->Value(), 4);

  // option mark price
  biz::MetricsOptionPrice op1(true, "mark_price", "XXX", bbase::decimal::Decimal<>(10000, -4));
  biz::MetricsOptionPrice op2(true, "mark_price", "YYY", bbase::decimal::Decimal<>(20000, -4));
  biz::MetricsOptionPrice op3(true, "mark_price", "AAA", bbase::decimal::Decimal<>(30000, -4));
  biz::MetricsOptionPrice op4(true, "mark_price", "BBB", bbase::decimal::Decimal<>(40000, -4));
  auto mop1 = monitor::mark_price_gauge("option", "mark_price", "XXX");
  EXPECT_EQ(mop1->Value(), 1);
  auto mop2 = monitor::mark_price_gauge("option", "mark_price", "YYY");
  EXPECT_EQ(mop2->Value(), 2);
  auto mop3 = monitor::mark_price_gauge("option", "mark_price", "AAA");
  EXPECT_EQ(mop3->Value(), 3);
  auto mop4 = monitor::mark_price_gauge("option", "mark_price", "BBB");
  EXPECT_EQ(mop4->Value(), 4);

  // exchange rate
  biz::MetricsExchangeRate mer1(true, "USDT-USDC", bbase::decimal::Decimal<>(10000, -4));
  biz::MetricsExchangeRate mer2(true, "USDC-USDT", bbase::decimal::Decimal<>(20000, -4));
  auto er1 = monitor::exchange_rate_gauge("USDT-USDC");
  EXPECT_EQ(er1->Value(), 1);
  auto er2 = monitor::exchange_rate_gauge("USDC-USDT");
  EXPECT_EQ(er2->Value(), 2);

  // last price
  biz::MetricsLastPrice mlp1(true, EProductType::Futures, 5, 5, "20000.1");      // BTCUSDT
  biz::MetricsLastPrice mlp2(true, EProductType::Options, 6, 102311, "0.0091");  // BTC-31MAR23-20000-C
  biz::MetricsLastPrice mlp3(true, EProductType::Spot, 7, 1, "0.0091");          // BTCUSDT
  auto mlp1_v = monitor::mark_price_gauge("future", "last_price", "BTCUSDT", "5");
  EXPECT_EQ(mlp1_v->Value(), 20000.1);
  auto mlp2_v = monitor::mark_price_gauge("option", "last_price", "BTC-31MAR23-20000-C", "6");
  EXPECT_EQ(mlp2_v->Value(), 0.0091);
  auto mlp3_v = monitor::mark_price_gauge("spot", "last_price", "BTCUSDT", "7");
  EXPECT_EQ(mlp3_v->Value(), 0.0091);
}

TEST_F(MarkPriceTest, test_metrics_false) {
  // 耗时
  {
    biz::MetricsFunctionCost metircs_function_cost(false, monitor::kLabelFutureMp);
    usleep(2000);
  }
  {
    biz::MetricsFunctionCost metircs_function_cost(false, monitor::kLabelOptionMp);
    usleep(2000);
  }
  {
    biz::MetricsFunctionCost metircs_function_cost(false, monitor::kLabelErMp);
    usleep(2000);
  }
  {
    biz::MetricsFunctionCost metircs_function_cost(false, monitor::kLabelQuoteMp);
    usleep(2000);
  }
  // monitor::mark_price_latency_h[0]->v

  // future counter
  auto v00 = monitor::mark_price_counter_g[0][0]->Value();
  auto v01 = monitor::mark_price_counter_g[0][1]->Value();
  biz::MetricsMessageCount metrics_future_incoming1(false, monitor::kLabelFutureMp, 0);
  EXPECT_EQ(monitor::mark_price_counter_g[0][0]->Value(), v00);
  biz::MetricsMessageCount metrics_future_outgoing1(false, monitor::kLabelFutureMp, 1);
  EXPECT_EQ(monitor::mark_price_counter_g[0][1]->Value(), v01);

  // option counter
  auto v10 = monitor::mark_price_counter_g[1][0]->Value();
  auto v11 = monitor::mark_price_counter_g[1][1]->Value();
  biz::MetricsMessageCount metrics_option_incoming1(false, monitor::kLabelOptionMp, 0);
  EXPECT_EQ(monitor::mark_price_counter_g[1][0]->Value(), v10);
  biz::MetricsMessageCount metrics_option_outgoing1(false, monitor::kLabelOptionMp, 1);
  EXPECT_EQ(monitor::mark_price_counter_g[1][1]->Value(), v11);

  // exchange rate counter
  auto v20 = monitor::mark_price_counter_g[2][0]->Value();
  auto v21 = monitor::mark_price_counter_g[2][1]->Value();
  biz::MetricsMessageCount metrics_exchange_incoming1(false, monitor::kLabelErMp, 0);
  EXPECT_EQ(monitor::mark_price_counter_g[2][0]->Value(), v20);
  biz::MetricsMessageCount metrics_exchange_outgoing1(false, monitor::kLabelErMp, 1);
  EXPECT_EQ(monitor::mark_price_counter_g[2][1]->Value(), v21);

  // future mark price
  auto mpg1 = monitor::mark_price_gauge("future", "mark_price", "BTCUSDT");
  auto mpg_v1 = mpg1->Value();
  auto mpg2 = monitor::mark_price_gauge("future", "mark_price", "ETHUSDT");
  auto mpg_v2 = mpg2->Value();
  biz::MetricsFuturePrice fp1(false, "mark_price", "BTCUSDT", bbase::decimal::Decimal<>(90000, -4));
  biz::MetricsFuturePrice fp2(false, "mark_price", "ETHUSDT", bbase::decimal::Decimal<>(10000, -4));
  EXPECT_EQ(mpg1->Value(), mpg_v1);
  EXPECT_EQ(mpg2->Value(), mpg_v2);

  // future index price
  auto mpg5 = monitor::mark_price_gauge("future", "index_price", "BTCUSDT");
  auto mpg5_v = mpg5->Value();
  auto mpg6 = monitor::mark_price_gauge("future", "index_price", "ETHUSDT");
  auto mpg6_v = mpg6->Value();
  biz::MetricsFuturePrice fi1(false, "index_price", "BTCUSDT", bbase::decimal::Decimal<>(70000, -4));
  biz::MetricsFuturePrice fi2(false, "index_price", "ETHUSDT", bbase::decimal::Decimal<>(80000, -4));
  EXPECT_EQ(mpg5->Value(), mpg5_v);
  EXPECT_EQ(mpg6->Value(), mpg6_v);

  // option mark price
  auto mop1 = monitor::mark_price_gauge("option", "mark_price", "XXX");
  auto mop1_v = mop1->Value();
  auto mop2 = monitor::mark_price_gauge("option", "mark_price", "YYY");
  auto mop2_v = mop2->Value();
  biz::MetricsOptionPrice op1(false, "mark_price", "XXX", bbase::decimal::Decimal<>(50000, -4));
  biz::MetricsOptionPrice op2(false, "mark_price", "YYY", bbase::decimal::Decimal<>(60000, -4));
  EXPECT_EQ(mop1->Value(), mop1_v);
  EXPECT_EQ(mop2->Value(), mop2_v);

  // exchange rate
  auto er1 = monitor::exchange_rate_gauge("USDT-USDC");
  auto er1_v = er1->Value();
  auto er2 = monitor::exchange_rate_gauge("USDC-USDT");
  auto er2_v = er2->Value();
  biz::MetricsExchangeRate mer1(false, "USDT-USDC", bbase::decimal::Decimal<>(10000, -4));
  biz::MetricsExchangeRate mer2(false, "USDC-USDT", bbase::decimal::Decimal<>(20000, -4));
  EXPECT_EQ(er1->Value(), er1_v);
  EXPECT_EQ(er2->Value(), er2_v);

  // last price
  auto mlp1_v = monitor::mark_price_gauge("future", "last_price", "BTCUSDT", "5");
  auto mlp1_tv = mlp1_v->Value();
  auto mlp2_v = monitor::mark_price_gauge("option", "last_price", "BTC-31MAR23-20000-C", "6");
  auto mlp2_tv = mlp2_v->Value();
  auto mlp3_v = monitor::mark_price_gauge("spot", "last_price", "BTCUSDT", "7");
  auto mlp3_tv = mlp3_v->Value();
  biz::MetricsLastPrice mlp1(false, EProductType::Futures, 5, 5, "20001.1");      // BTCUSDT
  biz::MetricsLastPrice mlp2(false, EProductType::Options, 6, 102311, "0.0092");  // BTC-31MAR23-20000-C
  biz::MetricsLastPrice mlp3(false, EProductType::Spot, 7, 1, "0.0098");          // BTCUSDT
  EXPECT_EQ(mlp1_v->Value(), mlp1_tv);
  EXPECT_EQ(mlp2_v->Value(), mlp2_tv);
  EXPECT_EQ(mlp3_v->Value(), mlp3_tv);
}

TEST_F(MarkPriceTest, test_base_coin_check) {
  bbase::hdts::Offset low_offset_temp = -1, high_offset_temp = -1;
  bbase::hdts::Hdts::GetLowWaterOffset("aaa", &low_offset_temp);
  bbase::hdts::Hdts::GetHighWaterOffset("aaa", &high_offset_temp);

  synchronizer_->Start();
  EXPECT_EQ(synchronizer_->consumers_.size(), 3);
  // 第一次检查
  synchronizer_->procBaseCoinCompare();
  usleep(500);

  // 第二次检查，增加一个base coin
  const char* kCoinPairContent1 =
      "{\"data\":[{\"quoteCoin\":\"USD\",\"baseCoin\":\"BTC\",\"settleCoin\":\"USDC\",\"contractSize\":1,"
      "\"multiplier\":1},{\"quoteCoin\":\"USD\",\"baseCoin\":\"ETH\",\"settleCoin\":\"USDC\",\"contractSize\":1,"
      "\"multiplier\":1}],\"version\":\"1\"}";
  auto coin_pair_data1 = std::make_shared<config::CoinPairData>();
  std::string msg;
  coin_pair_data1->parse(kCoinPairContent1, msg);
  config::getTlsCfgMgrRaw()->coin_client()->set_coin_pair_data(coin_pair_data1);

  synchronizer_->procBaseCoinCompare();
  usleep(500);

  // 第三次检查，减少一个base coin
  const char* kCoinPairContent2 =
      "{\"data\":[{\"quoteCoin\":\"USD\",\"baseCoin\":\"BTC\",\"settleCoin\":\"USDC\",\"contractSize\":1,"
      "\"multiplier\":1}],\"version\":\"1\"}";
  auto coin_pair_data2 = std::make_shared<config::CoinPairData>();
  coin_pair_data2->parse(kCoinPairContent2, msg);
  config::getTlsCfgMgrRaw()->coin_client()->set_coin_pair_data(coin_pair_data2);

  synchronizer_->procBaseCoinCompare();
  usleep(500);

  // 第四次检查，删除base coin
  synchronizer_->procBaseCoinCompare();
  usleep(500);

  synchronizer_->Stop();
  // 每个用例执行完就清空一下
  dtssdk::IDtsSdk::Instance()->NewConsumer()->Close();  // 关闭所有consumer线程
  dtssdk::IDtsSdk::Destroy();
}

TEST_F(MarkPriceTest, test_mark_price_consume_error) {
  // // new consumer handle
  auto handler = std::make_shared<biz::ExchangeRateHandle>(synchronizer_.get());
  handler->OnConsumeError(dtssdk::SysEventCode::kConsumeFailed);

  models::quote::ExchangeRateResponse exchange_rate_resp;

  //   int64 timestamp = 1;
  exchange_rate_resp.set_timestamp(1679625003);
  //   repeated ExchangeRate exchangeRate = 2;
  models::quote::ExchangeRate* er = exchange_rate_resp.add_exchangerate();
  er->set_fromcoin("USDT");
  er->set_tocoin("USD");
  com::bybit::option::common::PrecisionDecimal* rate = new com::bybit::option::common::PrecisionDecimal();
  rate->set_unscaledvalue(85842069);
  rate->set_scale(8);
  er->set_allocated_rate(rate);
  er->set_originaltimestamp(1679625003139);
  //   string reqId = 100;
  exchange_rate_resp.set_reqid("EqxWDmzcON");

  MpMockHdtsMessage msg;
  msg.topic_ = "aaa";
  msg.pay_load_ = exchange_rate_resp.SerializeAsString();

  handler->OnConsumeCallback(msg);
}

TEST_F(MarkPriceTest, test_mark_price_consume_get_highwater_error) {
  // // new consumer handle
  auto handler = std::make_shared<biz::ExchangeRateHandle>(synchronizer_.get());
  handler->OnConsumeError(dtssdk::SysEventCode::kConsumeFailed);

  models::quote::ExchangeRateResponse exchange_rate_resp;

  //   int64 timestamp = 1;
  exchange_rate_resp.set_timestamp(1679625003);
  //   repeated ExchangeRate exchangeRate = 2;
  models::quote::ExchangeRate* er = exchange_rate_resp.add_exchangerate();
  er->set_fromcoin("USDT");
  er->set_tocoin("USD");
  com::bybit::option::common::PrecisionDecimal* rate = new com::bybit::option::common::PrecisionDecimal();
  rate->set_unscaledvalue(85842069);
  rate->set_scale(8);
  er->set_allocated_rate(rate);
  er->set_originaltimestamp(1679625003139);
  //   string reqId = 100;
  exchange_rate_resp.set_reqid("EqxWDmzcON");

  MpMockHdtsMessage msg;
  msg.topic_ = "aaa";
  msg.pay_load_ = exchange_rate_resp.SerializeAsString();

  bbase::hdts::Hdts::Fini();
  handler->OnConsumeCallback(msg);
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
}

int main(int argc, char* argv[]) {
  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");
  auto client = std::make_shared<bbase::nacos_client::NacosClientImpl>("FUTURE-CONFIG", "uta", "");
  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::ObjectType::kObjectConfigCenter, client);
  // cache kv dataids
  config::ConfigProxy::Instance().InitConfigCenter();

  config::ConfigProxy::Instance().config_mgr()->set_symbol_config_mgr(std::make_shared<biz::sc::SymbolConfigManager>());
  config::getTlsCfgMgrRaw()->symbol_config_mgr_sptr()->InitMockData(false, 0);
  config::getTlsCfgMgrRaw()->spot_client_sptr()->InitMockData();

  const char* kOptionSymbolContent =
      "{\"data\":[{\"assetType\":\"OPTION\",\"baseCoin\":\"BTC\",\"coinpairId\":1,\"contractSize\":1,"
      "\"contractType\":"
      "\"LinearOption\",\"crossId\":10007,\"deliveryTime\":1841937600,\"expiry\":\"CURRENT_WEEK\",\"id\":102311,"
      "\"multiplier\":1,\"onlineTime\":1640687236,\"quoteCoin\":\"USD\",\"settleCoin\":\"USDC\",\"standard\":\"U\","
      "\"status\":\"ONLINE\",\"strikePrice\":40000.000000002,\"symbolName\":\"BTC-31MAR23-20000-C\",\"symbolType\":"
      "\"C\"}],\"version\":\"aa6d74a6-77af-4409-b09c-fe902f72683a\"}";

  config::ConfigProxy::Instance().SymbolFullReceiveHandler(kOptionSymbolContent);
  MarkPriceTest::waitRecvHandler();

  const char* kCoinPairContent =
      "{\"data\":[{\"quoteCoin\":\"USD\",\"baseCoin\":\"BTC\",\"settleCoin\":\"USDC\",\"contractSize\":1,"
      "\"multiplier\":1}],\"version\":\"1\"}";
  auto coin_pair_data = std::make_shared<config::CoinPairData>();
  std::string msg;
  coin_pair_data->parse(kCoinPairContent, msg);
  config::getTlsCfgMgrRaw()->coin_client()->set_coin_pair_data(coin_pair_data);

  bbase::object_manager::ObjectManager::RegisterObject(bbase::object_manager::kObjectApp, mp_ut_app);
  mp_ut_app->pipeline()->Init();
  auto time_now = bbase::utils::Time::GetTimeNs();
  std::srand(static_cast<std::uint32_t>(time_now));
  int http_port = (rand() + time_now) % 65530;  // NOLINT

  if (http_port <= 10240) {
    http_port += 10240;
  }
  monitor::Monitor::Instance().Init();
  monitor::Monitor::Instance().Start(fmt::format("localhost:{}", http_port));

  testing::InitGoogleTest(&argc, argv);
  testing::InitGoogleMock(&argc, argv);

  std::int32_t ret = RUN_ALL_TESTS();

  bbase::hdts::Hdts::Fini();
  bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::ObjectType::kObjectConfigCenter);
  monitor::Monitor::Instance().Stop();
  monitor::Monitor::Instance().Fini();
  config::ConfigProxy::Instance().Fini();
  bbase::object_manager::ObjectManager::UnregisterObject(bbase::object_manager::kObjectApp);

  return ret;
}
