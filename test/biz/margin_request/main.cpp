
#include <idtssdk.h>

#include <bbase/common/hdts/hdts.hpp>
#include <iostream>
#include <thread>

#include "src/application/application.hpp"
#include "src/margin_request/margin_request_consumer.hpp"

void TestConsume() {
  std::string topic = "cht_margin_unify_request_RZ01";
  bbase::hdts::Offset start_offset = 11177;

  application::App app;
  worker::Pipeline::Ptr pipeline = std::make_shared<worker::Pipeline>(&app);
  pipeline->Init();

  bbase::hdts::Hdts::Init({}, "uta_hdts_normal_user", "uta_hdts_normal_user");

  auto margin_request = std::make_shared<margin_request::MarginRequestConsumer>(topic, pipeline);
  margin_request->Start(start_offset, -1);

  std::this_thread::sleep_for(std::chrono::seconds(5));

  margin_request->Stop();
  margin_request.reset();

  bbase::hdts::Hdts::Fini();
}

int main() {
  TestConsume();
  return 0;
}
