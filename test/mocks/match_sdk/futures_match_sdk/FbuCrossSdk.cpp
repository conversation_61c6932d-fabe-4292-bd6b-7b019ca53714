#include "FbuCrossSdk.h"

#include <chrono>
#include <cstdio>
#include <iostream>
#include <string>

#include "src/application/global_var_manager.hpp"

namespace fbu {

FbuCrossSdk::FbuCrossSdk() {
  callback_.traceLog = false;  // true写sdk日志，false不写sdk日志
  callback_.logCb = std::bind(&FbuCrossSdk::logFunc, this, std::placeholders::_1, std::placeholders::_2);
  callback_.rejectResponseCb = std::bind(&FbuCrossSdk::rejectResponseFunc, this, std::placeholders::_1,
                                         std::placeholders::_2, std::placeholders::_3, std::placeholders::_4);
  callback_.executionResponseCb =
      std::bind(&FbuCrossSdk::executionResponseFunc, this, std::placeholders::_1, std::placeholders::_2,
                std::placeholders::_3, std::placeholders::_4, std::placeholders::_5);
  callback_.executionResponseFromReqCb =
      std::bind(&FbuCrossSdk::executionResponseFromReq, this, std::placeholders::_1, std::placeholders::_2,
                std::placeholders::_3, std::placeholders::_4, std::placeholders::_5, std::placeholders::_6);
  callback_.dealExecutionResponseCb =
      std::bind(&FbuCrossSdk::dealExecutionResponseFunc, this, std::placeholders::_1, std::placeholders::_2);
  bool bRet = matcher_.Init(callback_);
  if (!bRet) {
    std::cout << "match sdk init failed" << std::endl;
    exit(-1);
  }

  callback_eu_.traceLog = false;  // true写sdk日志，false不写sdk日志
  callback_eu_.logCb = std::bind(&FbuCrossSdk::logFunc, this, std::placeholders::_1, std::placeholders::_2);
  callback_eu_.rejectResponseCb = std::bind(&FbuCrossSdk::rejectResponseFunc_eu, this, std::placeholders::_1,
                                            std::placeholders::_2, std::placeholders::_3, std::placeholders::_4);
  callback_eu_.executionResponseCb =
      std::bind(&FbuCrossSdk::executionResponseFunc_eu, this, std::placeholders::_1, std::placeholders::_2,
                std::placeholders::_3, std::placeholders::_4, std::placeholders::_5);
  callback_eu_.executionResponseFromReqCb =
      std::bind(&FbuCrossSdk::executionResponseFromReq_eu, this, std::placeholders::_1, std::placeholders::_2,
                std::placeholders::_3, std::placeholders::_4, std::placeholders::_5);
  callback_eu_.dealExecutionResponseCb =
      std::bind(&FbuCrossSdk::dealExecutionResponseFunc_eu, this, std::placeholders::_1, std::placeholders::_2);
  callback_eu_.mirrorDealExecutionResponseCb =
      std::bind(&FbuCrossSdk::mirrorDealExecutionResponseFunc_eu, this, std::placeholders::_1, std::placeholders::_2,
                std::placeholders::_3, std::placeholders::_4);

  bRet = matcher_eu_.Init(callback_eu_);
  if (!bRet) {
    std::cout << "match sdk init failed" << std::endl;
    exit(-1);
  }
}

FbuCrossSdk::~FbuCrossSdk() {}

bool FbuCrossSdk::DoCross(uint64_t offset, std::string req, std::string& rsp) {
  begin_time_ =
      std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch())
          .count();
  bool bRet = false;
  int64_t nTimestamp = 0;

  if ((last_offset_ + 1) != offset && (last_offset_ != 0)) {
    std::cout << "offset skip,last_offset=" << last_offset_ << ",offset=" << offset << std::endl;
  }

  // 3，解析kafka包头
  char* data = static_cast<char*>(req.data());  // kafka包头+包体数据
  Header* pHead = reinterpret_cast<Header*>(data);
  const uint64_t length = req.size();  // kafka包头+包体数据长度
  if (length != (sizeof(Header) + pHead->_length)) {
    std::cout << "offset #" << offset << " length " << length << " is wrong" << std::endl;
    last_offset_ = offset;
    return false;
  }

  if (!Header::validate(*pHead)) {
    printf("header check failed\n");
    last_offset_ = offset;
    return false;
  }
  if (application::GlobalVarManager::Instance().is_eu_mode()) {
    if (pHead->_count != 1) {
      printf("head count check failed\n");
      last_offset_ = offset;
      return false;
    }
    if (pHead->_passThrough && (pHead->_length != sizeof(yijin::MatchSdk::order::Request))) {
      printf("head _length check failed\n");
      last_offset_ = offset;
      return false;
    } else if (!pHead->_passThrough && (pHead->_length != sizeof(eu_fbu::yijin::MatchSdk::order::Request))) {
      printf("head _length check failed\n");
      last_offset_ = offset;
      return false;
    }
    if (pHead->_passThrough) {  // 欧洲站透传包
      // 将请求包体data转为结构体
      eu_fbu::yijin::MatchSdk::order::GlobalRequest* request =
          reinterpret_cast<eu_fbu::yijin::MatchSdk::order::GlobalRequest*>(data + sizeof(Header));
      if (!request->isChecksumMatched()) {
        printf("checksum failed\n");
        last_offset_ = offset;
        return false;
      }
      rspCache_.ReSet();

      bRet = matcher_eu_.DoPassThroughRequest(*request, offset, nTimestamp);
      if (!bRet) {
        last_offset_ = offset;
        return false;
      }
    } else {  // 欧洲站非透传包
      // 将请求包体data转为结构体
      eu_fbu::yijin::MatchSdk::order::Request* request =
          reinterpret_cast<eu_fbu::yijin::MatchSdk::order::Request*>(data + sizeof(Header));
      if (!request->isChecksumMatched()) {
        printf("checksum failed\n");
        last_offset_ = offset;
        return false;
      }
      rspCache_.ReSet();

      bRet = matcher_eu_.DoRequest(*request, offset, nTimestamp);
      if (!bRet) {
        last_offset_ = offset;
        return false;
      }
    }
  } else {  // 主站请求
    if (pHead->_count != 1 || pHead->_length != sizeof(yijin::MatchSdk::order::Request)) {
      printf("length check failed\n");
      last_offset_ = offset;
      return false;
    }

    // 将请求包体data转为结构体
    yijin::MatchSdk::order::Request* request =
        reinterpret_cast<yijin::MatchSdk::order::Request*>(data + sizeof(Header));
    if (!request->isChecksumMatched()) {
      printf("checksum failed\n");
      last_offset_ = offset;
      return false;
    }

    rspCache_.ReSet();

    bRet = matcher_.DoRequest(*request, offset, pHead->_passThrough, nTimestamp);
    if (!bRet) {
      last_offset_ = offset;
      return false;
    }
  }

  if (pHead->_passThrough) {
    pHead->_offset = offset;
    rsp = std::string(data, length);
  } else {
    if (rspCache_.GetLen() > 0) {
      bRet = rspCache_.AddHead(offset);

      if (!bRet) {
        std::cout << "response cache AddHead failed,new failed or already AddHead" << std::endl;
        return false;
      }
      end_time_ =
          std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch())
              .count();
      rspCache_.UpdateTime(begin_time_, begin_time_);
      begin_time_ = 0;
      begin_time_ = 0;
      rsp = std::string(rspCache_.GetData(), rspCache_.GetLen());
      rspCache_.ReSet();
    }
  }
  last_offset_ = offset;

  return true;
}

// 回调日志处理
void FbuCrossSdk::logFunc(int level, char* buf) { printf("cross sdk,level=%d,log=%s\n", level, buf); }

void FbuCrossSdk::rejectResponseFunc(const yijin::MatchSdk::order::Request& request_,
                                     yijin::MatchSdk::order::ResponseMsgType msgType_, uint64_t& transactTime_,
                                     const yijin::MatchSdk::order::ErrorCode ec_) {
  switch (msgType_) {
    case yijin::MatchSdk::order::ExecutionReport:  // 下单拒单回报
    {
      Response* response = rspCache_.GetNewRspPtr();
      response->_msgType = yijin::MatchSdk::order::ExecutionReport;
      response->_senderCompID = request_._targetCompID;  // 是反的
      response->_targetCompID = request_._senderCompID;  // 是反的
      response->_clOrdID = request_._orderID;            // 请求订单的orderid
      response->_execType = yijin::MatchSdk::order::ExecType::ET_Rejected;
      response->_orderStatus = yijin::MatchSdk::order::OrderStatus::OS_Rejected;
      response->_rejectReason = ec_;
      response->_createdBy = request_._createdBy;
      response->_canceledBy = request_._canceledBy;
      response->_orderType = request_._orderType;
      response->_symbol = request_._symbol;
      response->_side = request_._side;
      response->_orderQty = request_._qty;
      response->_price = request_._price;
      response->_priceScale = request_._priceScale;
      response->_smpType = request_._smpType;
      response->_smpGroup = request_._smpGroup;
      response->_transactTime[0] = transactTime_ / 1000000;
      response->_transactTime[1] = transactTime_ % 1000000;
      response->_timeInForce = request_._timeInForce;
      struct timeval tv;
      struct timezone tz {
        0, 0
      };
      gettimeofday(&tv, &tz);
      response->_sendingTime[0] = tv.tv_sec;
      response->_sendingTime[1] = tv.tv_usec;
      response->_checksum = CHECKSUM_CONST;
    } break;
    case yijin::MatchSdk::order::CancelRejectResponse:  // 撤单拒单回报
    {
      Response* response = rspCache_.GetNewRspPtr();
      response->_msgType = yijin::MatchSdk::order::CancelRejectResponse;
      response->_senderCompID = request_._targetCompID;  // 是反的
      response->_targetCompID = request_._senderCompID;  // 是反的
      response->_clOrdID = request_._orderID;
      response->_origClOrdID = request_._origOrderID;
      response->_execType = yijin::MatchSdk::order::ExecType::ET_Rejected;
      response->_rejectReason = ec_;
      response->_createdBy = request_._createdBy;
      response->_canceledBy = request_._canceledBy;
      response->_orderType = request_._orderType;
      response->_symbol = request_._symbol;
      response->_side = request_._side;
      response->_orderQty = request_._qty;
      response->_price = request_._price;
      response->_priceScale = request_._priceScale;
      response->_smpType = request_._smpType;
      response->_smpGroup = request_._smpGroup;
      response->_transactTime[0] = transactTime_ / 1000000;
      response->_transactTime[1] = transactTime_ % 1000000;
      response->_timeInForce = request_._timeInForce;
      struct timeval tv;
      struct timezone tz {
        0, 0
      };
      gettimeofday(&tv, &tz);
      response->_sendingTime[0] = tv.tv_sec;
      response->_sendingTime[1] = tv.tv_usec;
      response->_checksum = CHECKSUM_CONST;
    } break;
    case yijin::MatchSdk::order::ReplaceRejectResponse:  // 改价下单拒单回报；现货没有:是的，只有期货才有
    {
      Response* response = rspCache_.GetNewRspPtr();
      response->_msgType = yijin::MatchSdk::order::ReplaceRejectResponse;
      response->_senderCompID = request_._targetCompID;  // 是反的
      response->_targetCompID = request_._senderCompID;  // 是反的
      response->_clOrdID = request_._orderID;
      response->_origClOrdID = request_._origOrderID;
      response->_execType = yijin::MatchSdk::order::ExecType::ET_Rejected;
      response->_rejectReason = ec_;

      response->_createdBy = request_._createdBy;
      response->_canceledBy = request_._canceledBy;
      response->_orderType = request_._orderType;
      response->_symbol = request_._symbol;
      response->_side = request_._side;
      response->_orderQty = request_._qty;
      response->_price = request_._price;
      response->_priceScale = request_._priceScale;
      response->_smpType = request_._smpType;
      response->_smpGroup = request_._smpGroup;
      response->_transactTime[0] = transactTime_ / 1000000;
      response->_transactTime[1] = transactTime_ % 1000000;
      response->_timeInForce = request_._timeInForce;
      struct timeval tv;
      struct timezone tz {
        0, 0
      };
      gettimeofday(&tv, &tz);
      response->_sendingTime[0] = tv.tv_sec;
      response->_sendingTime[1] = tv.tv_usec;
      response->_checksum = CHECKSUM_CONST;
    } break;
    default:
      std::cout << "unkown msgtype:" << msgType_ << std::endl;
      break;
  }
}

// 请求执行报告
void FbuCrossSdk::executionResponseFunc(const yijin::MatchSdk::order::Order* order_,
                                        yijin::MatchSdk::order::ExecType et_, uint64_t& transactTime_,
                                        const yijin::MatchSdk::order::ErrorCode ec_, const uint8_t checkType_) {
  switch (et_) {
    case yijin::MatchSdk::order::ET_New:  // 创建
    {
      Response* response = rspCache_.GetNewRspPtr();
      response->_msgType = yijin::MatchSdk::order::ExecutionReport;
      response->_senderCompID = order_->_targetCompID;
      response->_targetCompID = order_->_senderCompID;
      response->_clOrdID = order_->_clOrdID;
      response->_orderID = order_->_orderID;
      response->_execType = yijin::MatchSdk::order::ExecType::ET_New;
      response->_orderStatus = order_->_orderStatus;
      response->_leavesQty = order_->_leavesQty;
      response->_createdBy = order_->_createdBy;
      response->_canceledBy = order_->_canceledBy;
      response->_orderType = order_->_orderType;
      response->_symbol = order_->_symbol;
      response->_side = order_->_side;
      response->_orderQty = order_->_qty;
      response->_price = order_->_price;
      response->_priceScale = order_->_priceScale;
      response->_smpType = order_->_smpType;
      response->_smpGroup = order_->_smpGroup;
      response->_transactTime[0] = transactTime_ / 1000000;
      response->_transactTime[1] = transactTime_ % 1000000;
      response->_timeInForce = order_->_timeInForce;
      struct timeval tv;
      struct timezone tz {
        0, 0
      };
      gettimeofday(&tv, &tz);
      response->_sendingTime[0] = tv.tv_sec;
      response->_sendingTime[1] = tv.tv_usec;
      response->_checksum = CHECKSUM_CONST;
    } break;
    case yijin::MatchSdk::order::ET_Canceled:  // 撤销
    {
      Response* response = rspCache_.GetNewRspPtr();
      response->_msgType = yijin::MatchSdk::order::ExecutionReport;
      response->_senderCompID = order_->_targetCompID;
      response->_targetCompID = order_->_senderCompID;
      response->_clOrdID =
          order_->_clOrdID;  // TODO(cater) : this is challenged by Latrel, should thsi be originClOrdID?
      response->_origClOrdID = order_->_clOrdID;
      response->_orderID = order_->_orderID;
      response->_execType = yijin::MatchSdk::order::ExecType::ET_Canceled;
      response->_rejectReason = ec_;
      response->_orderStatus = yijin::MatchSdk::order::OrderStatus::OS_Canceled;
      response->_cumQty = order_->GetMatchedQty();  // 已成交量
      response->_lastQty = order_->_leavesQty;      // 跟老错保持一致
      response->_lastPx = 0;
      response->_leavesQty = 0;
      response->_createdBy = order_->_createdBy;
      response->_canceledBy = order_->_canceledBy;
      response->_orderType = order_->_orderType;
      response->_symbol = order_->_symbol;
      response->_side = order_->_side;
      response->_orderQty = order_->_qty;
      response->_price = order_->_price;
      response->_priceScale = order_->_priceScale;
      response->_smpType = order_->_smpType;
      response->_smpGroup = order_->_smpGroup;
      response->_timeInForce = order_->_timeInForce;
      response->_transactTime[0] = transactTime_ / 1000000;
      response->_transactTime[1] = transactTime_ % 1000000;
      struct timeval tv;
      struct timezone tz {
        0, 0
      };
      gettimeofday(&tv, &tz);
      response->_sendingTime[0] = tv.tv_sec;
      response->_sendingTime[1] = tv.tv_usec;
      response->_checksum = CHECKSUM_CONST;
    } break;
    case yijin::MatchSdk::order::ET_Modify:  // 改价下单
    {
      Response* response = rspCache_.GetNewRspPtr();
      response->_msgType = yijin::MatchSdk::order::ExecutionReport;
      response->_senderCompID = order_->_targetCompID;
      response->_targetCompID = order_->_senderCompID;
      response->_clOrdID = order_->_clOrdID;
      response->_orderID = order_->_orderID;
      response->_execType = yijin::MatchSdk::order::ExecType::ET_Modify;
      response->_orderStatus = order_->_orderStatus;
      response->_leavesQty = order_->_leavesQty;
      response->_createdBy = order_->_createdBy;
      response->_canceledBy = order_->_canceledBy;
      response->_orderType = order_->_orderType;
      response->_symbol = order_->_symbol;
      response->_side = order_->_side;
      response->_orderQty = order_->_qty;
      response->_price = order_->_price;
      response->_priceScale = order_->_priceScale;
      response->_smpType = order_->_smpType;
      response->_smpGroup = order_->_smpGroup;
      response->_timeInForce = order_->_timeInForce;
      response->_keepPriority = (checkType_ == 1) ? yijin::MatchSdk::order::KeepPriority::Keep
                                                  : yijin::MatchSdk::order::KeepPriority::Non_Keep;

      response->_transactTime[0] = transactTime_ / 1000000;
      response->_transactTime[1] = transactTime_ % 1000000;
      struct timeval tv;
      struct timezone tz {
        0, 0
      };
      gettimeofday(&tv, &tz);
      response->_sendingTime[0] = tv.tv_sec;
      response->_sendingTime[1] = tv.tv_usec;
      response->_checksum = CHECKSUM_CONST;
    } break;
    default:
      std::cout << "unkown exectype:" << et_ << std::endl;
      break;
  }
}

// 成交回报
void FbuCrossSdk::dealExecutionResponseFunc(const yijin::MatchSdk::order::Order* order_, uint64_t& transactTime_) {
  Response* response = rspCache_.GetNewRspPtr();
  response->_msgType = yijin::MatchSdk::order::ExecutionReport;
  response->_senderCompID = order_->_targetCompID;
  response->_targetCompID = order_->_senderCompID;
  response->_clOrdID = order_->_clOrdID;
  response->_execID = order_->_execID;  // TODO(cater) : need to log this?
  response->_orderID = order_->_orderID;
  // response->_origClOrdID          = order_->_origClOrdID;
  response->_execType = yijin::MatchSdk::order::ExecType::ET_Trade;
  response->_orderStatus = order_->_orderStatus;
  response->_lastQty = order_->_lastQty;
  response->_lastPx = order_->_lastPx;
  response->_cumQty = order_->GetMatchedQty();
  response->_leavesQty = order_->_leavesQty;
  response->_lastLiquidityInd = order_->_lastLiquidityInd;
  response->_createdBy = order_->_createdBy;
  response->_canceledBy = order_->_canceledBy;
  response->_orderType = order_->_orderType;
  response->_symbol = order_->_symbol;
  response->_side = order_->_side;
  response->_orderQty = order_->_qty;
  response->_price = order_->_price;
  response->_priceScale = order_->_priceScale;
  response->_smpType = order_->_smpType;
  response->_smpGroup = order_->_smpGroup;
  response->_timeInForce = order_->_timeInForce;
  response->_transactTime[0] = transactTime_ / 1000000;
  response->_transactTime[1] = transactTime_ % 1000000;
  struct timeval tv;
  struct timezone tz {
    0, 0
  };
  gettimeofday(&tv, &tz);
  response->_sendingTime[0] = tv.tv_sec;
  response->_sendingTime[1] = tv.tv_usec;
  response->_checksum = CHECKSUM_CONST;
}

void FbuCrossSdk::executionResponseFromReq(const yijin::MatchSdk::order::Request& request_,
                                           const yijin::MatchSdk::order::Order* order_,
                                           yijin::MatchSdk::order::ExecType et_, uint64_t& transactTime_,
                                           const yijin::MatchSdk::order::ErrorCode ec_, const uint8_t checkType_) {
  Response* response = rspCache_.GetNewRspPtr();
  response->_msgType = yijin::MatchSdk::order::ExecutionReport;
  response->_senderCompID = request_._targetCompID;
  response->_targetCompID = request_._senderCompID;
  response->_orderID = order_->_orderID;
  response->_clOrdID = request_._orderID;
  response->_origClOrdID = request_._origOrderID;
  response->_execType = et_;  // yijin::MatchSdk::order::ExecType::ET_Rejected;
  response->_orderStatus = order_->_orderStatus;
  response->_rejectReason = ec_;
  response->_cumQty = order_->GetMatchedQty();  // 已成交量
  response->_lastQty = order_->_leavesQty;      // 保持跟旧撮合一致
  response->_lastPx = 0;
  response->_leavesQty = 0;
  response->_createdBy = order_->_createdBy;
  response->_canceledBy = request_._canceledBy;
  response->_orderType = order_->_orderType;
  response->_symbol = order_->_symbol;
  response->_side = order_->_side;
  response->_orderQty = order_->_qty;
  response->_price = order_->_price;
  response->_priceScale = order_->_priceScale;
  response->_smpType = order_->_smpType;
  response->_smpGroup = order_->_smpGroup;
  response->_timeInForce = order_->_timeInForce;
  // response->_lastLiquidityInd     = order_->_lastLiquidityInd;
  response->_transactTime[0] = transactTime_ / 1000000;
  response->_transactTime[1] = transactTime_ % 1000000;
  // response->_keepPriority = (checkType_ == 1) ? yijin::MatchSdk::order::KeepPriority::Keep :
  // yijin::MatchSdk::order::KeepPriority::Non_Keep;
  struct timeval tv;
  struct timezone tz {
    0, 0
  };
  gettimeofday(&tv, &tz);
  response->_sendingTime[0] = tv.tv_sec;
  response->_sendingTime[1] = tv.tv_usec;
  // Cancel时，用_keepPriority来标识是否是replaceCancel：Keep 为replaceCancel；否则不是
  if (et_ == yijin::MatchSdk::order::ET_Canceled) {
    response->_keepPriority =
        (checkType_ == 1) ? yijin::MatchSdk::order::KeepPriority::Keep : yijin::MatchSdk::order::KeepPriority::Non_Keep;
  }
  response->_checksum = CHECKSUM_CONST;
}

void FbuCrossSdk::rejectResponseFunc_eu(const eu_fbu::yijin::MatchSdk::order::Request& request_,
                                        eu_fbu::yijin::MatchSdk::order::ResponseMsgType msgType_,
                                        uint64_t& transactTime_, const eu_fbu::yijin::MatchSdk::order::ErrorCode ec_) {
  switch (static_cast<yijin::MatchSdk::order::ResponseMsgType>(msgType_)) {
    case yijin::MatchSdk::order::ExecutionReport:  // 下单拒单回报
    {
      Response* response = rspCache_.GetNewRspPtr();
      response->_msgType = yijin::MatchSdk::order::ExecutionReport;
      response->_senderCompID = request_._targetCompID;  // 是反的
      response->_targetCompID = request_._senderCompID;  // 是反的
      response->_clOrdID = request_._orderID;            // 请求订单的orderid
      response->_execType = yijin::MatchSdk::order::ExecType::ET_Rejected;
      response->_orderStatus = yijin::MatchSdk::order::OrderStatus::OS_Rejected;
      response->_rejectReason = static_cast<yijin::MatchSdk::order::ErrorCode>(ec_);
      response->_createdBy = static_cast<yijin::MatchSdk::order::CreatedBy>(request_._createdBy);
      response->_canceledBy = static_cast<yijin::MatchSdk::order::CanceledBy>(request_._canceledBy);
      response->_orderType = static_cast<yijin::MatchSdk::order::OrderType>(request_._orderType);
      response->_symbol = request_._symbol;
      response->_side = static_cast<yijin::MatchSdk::order::Side>(request_._side);
      response->_orderQty = request_._qty;
      response->_price = request_._price;
      response->_priceScale = request_._priceScale;
      response->_smpType = request_._smpType;
      response->_smpGroup = request_._smpGroup;
      response->_timeInForce = static_cast<yijin::MatchSdk::order::TimeInForce>(request_._timeInForce);
      response->_transactTime[0] = transactTime_ / 1000000;
      response->_transactTime[1] = transactTime_ % 1000000;
      struct timeval tv;
      struct timezone tz {
        0, 0
      };
      gettimeofday(&tv, &tz);
      response->_sendingTime[0] = tv.tv_sec;
      response->_sendingTime[1] = tv.tv_usec;
      response->_submitType = eu_fbu::yijin::MatchSdk::order::OrderSubmitType::RealOrder;
      response->_checksum = CHECKSUM_CONST;
    } break;
    case yijin::MatchSdk::order::CancelRejectResponse:  // 撤单拒单回报
    {
      Response* response = rspCache_.GetNewRspPtr();
      response->_msgType = yijin::MatchSdk::order::CancelRejectResponse;
      response->_senderCompID = request_._targetCompID;  // 是反的
      response->_targetCompID = request_._senderCompID;  // 是反的
      response->_clOrdID = request_._orderID;
      response->_origClOrdID = request_._origOrderID;
      response->_execType = yijin::MatchSdk::order::ExecType::ET_Rejected;
      response->_rejectReason = static_cast<yijin::MatchSdk::order::ErrorCode>(ec_);
      response->_createdBy = static_cast<yijin::MatchSdk::order::CreatedBy>(request_._createdBy);
      response->_canceledBy = static_cast<yijin::MatchSdk::order::CanceledBy>(request_._canceledBy);
      response->_orderType = static_cast<yijin::MatchSdk::order::OrderType>(request_._orderType);
      response->_symbol = request_._symbol;
      response->_side = static_cast<yijin::MatchSdk::order::Side>(request_._side);
      response->_orderQty = request_._qty;
      response->_price = request_._price;
      response->_priceScale = request_._priceScale;
      response->_smpType = request_._smpType;
      response->_smpGroup = request_._smpGroup;
      response->_timeInForce = static_cast<yijin::MatchSdk::order::TimeInForce>(request_._timeInForce);
      response->_transactTime[0] = transactTime_ / 1000000;
      response->_transactTime[1] = transactTime_ % 1000000;
      struct timeval tv;
      struct timezone tz {
        0, 0
      };
      gettimeofday(&tv, &tz);
      response->_sendingTime[0] = tv.tv_sec;
      response->_sendingTime[1] = tv.tv_usec;
      response->_submitType = eu_fbu::yijin::MatchSdk::order::OrderSubmitType::RealOrder;
      response->_checksum = CHECKSUM_CONST;
    } break;
    case yijin::MatchSdk::order::ReplaceRejectResponse:  // 改价下单拒单回报；现货没有:是的，只有期货才有
    {
      Response* response = rspCache_.GetNewRspPtr();
      response->_msgType = yijin::MatchSdk::order::ReplaceRejectResponse;
      response->_senderCompID = request_._targetCompID;  // 是反的
      response->_targetCompID = request_._senderCompID;  // 是反的
      response->_clOrdID = request_._orderID;
      response->_origClOrdID = request_._origOrderID;
      response->_execType = yijin::MatchSdk::order::ExecType::ET_Rejected;
      response->_rejectReason = static_cast<yijin::MatchSdk::order::ErrorCode>(ec_);

      response->_createdBy = static_cast<yijin::MatchSdk::order::CreatedBy>(request_._createdBy);
      response->_canceledBy = static_cast<yijin::MatchSdk::order::CanceledBy>(request_._canceledBy);
      response->_orderType = static_cast<yijin::MatchSdk::order::OrderType>(request_._orderType);
      response->_symbol = request_._symbol;
      response->_side = static_cast<yijin::MatchSdk::order::Side>(request_._side);
      response->_orderQty = request_._qty;
      response->_price = request_._price;
      response->_priceScale = request_._priceScale;
      response->_smpType = request_._smpType;
      response->_smpGroup = request_._smpGroup;
      response->_timeInForce = static_cast<yijin::MatchSdk::order::TimeInForce>(request_._timeInForce);
      response->_transactTime[0] = transactTime_ / 1000000;
      response->_transactTime[1] = transactTime_ % 1000000;
      struct timeval tv;
      struct timezone tz {
        0, 0
      };
      gettimeofday(&tv, &tz);
      response->_sendingTime[0] = tv.tv_sec;
      response->_sendingTime[1] = tv.tv_usec;
      response->_submitType = eu_fbu::yijin::MatchSdk::order::OrderSubmitType::RealOrder;
      response->_checksum = CHECKSUM_CONST;
    } break;
    default:
      std::cout << "unkown msgtype:" << msgType_ << std::endl;
      break;
  }
}

// 请求执行报告
void FbuCrossSdk::executionResponseFunc_eu(const eu_fbu::yijin::MatchSdk::order::Order* order_,
                                           eu_fbu::yijin::MatchSdk::order::ExecType et_, uint64_t& transactTime_,
                                           const eu_fbu::yijin::MatchSdk::order::ErrorCode ec_,
                                           const uint8_t checkType_) {
  switch (static_cast<yijin::MatchSdk::order::ExecType>(et_)) {
    case yijin::MatchSdk::order::ET_New:  // 创建
    {
      Response* response = rspCache_.GetNewRspPtr();
      response->_msgType = yijin::MatchSdk::order::ExecutionReport;
      response->_senderCompID = order_->_targetCompID;
      response->_targetCompID = order_->_senderCompID;
      response->_clOrdID = order_->_clOrdID;
      response->_orderID = order_->_orderID;
      response->_execType = yijin::MatchSdk::order::ExecType::ET_New;
      response->_orderStatus = static_cast<yijin::MatchSdk::order::OrderStatus>(order_->_orderStatus);
      response->_leavesQty = order_->_leavesQty;
      response->_createdBy = static_cast<yijin::MatchSdk::order::CreatedBy>(order_->_createdBy);
      response->_canceledBy = static_cast<yijin::MatchSdk::order::CanceledBy>(order_->_canceledBy);
      response->_orderType = static_cast<yijin::MatchSdk::order::OrderType>(order_->_orderType);
      response->_symbol = order_->_symbol;
      response->_side = static_cast<yijin::MatchSdk::order::Side>(order_->_side);
      response->_orderQty = order_->_qty;
      response->_price = order_->_price;
      response->_priceScale = order_->_priceScale;
      response->_smpType = order_->_smpType;
      response->_smpGroup = order_->_smpGroup;
      response->_timeInForce = static_cast<yijin::MatchSdk::order::TimeInForce>(order_->_timeInForce);
      response->_transactTime[0] = transactTime_ / 1000000;
      response->_transactTime[1] = transactTime_ % 1000000;
      struct timeval tv;
      struct timezone tz {
        0, 0
      };
      gettimeofday(&tv, &tz);
      response->_sendingTime[0] = tv.tv_sec;
      response->_sendingTime[1] = tv.tv_usec;
      response->_submitType = eu_fbu::yijin::MatchSdk::order::OrderSubmitType::RealOrder;
      response->_checksum = CHECKSUM_CONST;
    } break;
    case yijin::MatchSdk::order::ET_Canceled:  // 撤销
    {
      Response* response = rspCache_.GetNewRspPtr();
      response->_msgType = yijin::MatchSdk::order::ExecutionReport;
      response->_senderCompID = order_->_targetCompID;
      response->_targetCompID = order_->_senderCompID;
      response->_clOrdID =
          order_->_clOrdID;  // TODO(cater) : this is challenged by Latrel, should thsi be originClOrdID?
      response->_origClOrdID = order_->_clOrdID;
      response->_orderID = order_->_orderID;
      response->_execType = yijin::MatchSdk::order::ExecType::ET_Canceled;
      response->_rejectReason = static_cast<yijin::MatchSdk::order::ErrorCode>(ec_);
      response->_orderStatus = yijin::MatchSdk::order::OrderStatus::OS_Canceled;
      response->_cumQty = order_->GetMatchedQty();  // 已成交量
      response->_lastQty = order_->_leavesQty;      // 跟老错保持一致
      response->_lastPx = 0;
      response->_leavesQty = 0;
      response->_createdBy = static_cast<yijin::MatchSdk::order::CreatedBy>(order_->_createdBy);
      response->_canceledBy = static_cast<yijin::MatchSdk::order::CanceledBy>(order_->_canceledBy);
      response->_orderType = static_cast<yijin::MatchSdk::order::OrderType>(order_->_orderType);
      response->_symbol = order_->_symbol;
      response->_side = static_cast<yijin::MatchSdk::order::Side>(order_->_side);
      response->_orderQty = order_->_qty;
      response->_price = order_->_price;
      response->_priceScale = order_->_priceScale;
      response->_smpType = order_->_smpType;
      response->_smpGroup = order_->_smpGroup;
      response->_timeInForce = static_cast<yijin::MatchSdk::order::TimeInForce>(order_->_timeInForce);
      response->_transactTime[0] = transactTime_ / 1000000;
      response->_transactTime[1] = transactTime_ % 1000000;
      struct timeval tv;
      struct timezone tz {
        0, 0
      };
      gettimeofday(&tv, &tz);
      response->_sendingTime[0] = tv.tv_sec;
      response->_sendingTime[1] = tv.tv_usec;
      response->_submitType = eu_fbu::yijin::MatchSdk::order::OrderSubmitType::RealOrder;
      response->_checksum = CHECKSUM_CONST;
    } break;
    case yijin::MatchSdk::order::ET_Modify:  // 改价下单
    {
      Response* response = rspCache_.GetNewRspPtr();
      response->_msgType = yijin::MatchSdk::order::ExecutionReport;
      response->_senderCompID = order_->_targetCompID;
      response->_targetCompID = order_->_senderCompID;
      response->_clOrdID = order_->_clOrdID;
      response->_orderID = order_->_orderID;
      response->_execType = yijin::MatchSdk::order::ExecType::ET_Modify;
      response->_orderStatus = static_cast<yijin::MatchSdk::order::OrderStatus>(order_->_orderStatus);
      response->_leavesQty = order_->_leavesQty;
      response->_createdBy = static_cast<yijin::MatchSdk::order::CreatedBy>(order_->_createdBy);
      response->_canceledBy = static_cast<yijin::MatchSdk::order::CanceledBy>(order_->_canceledBy);
      response->_orderType = static_cast<yijin::MatchSdk::order::OrderType>(order_->_orderType);
      response->_symbol = order_->_symbol;
      response->_side = static_cast<yijin::MatchSdk::order::Side>(order_->_side);
      response->_orderQty = order_->_qty;
      response->_price = order_->_price;
      response->_priceScale = order_->_priceScale;
      response->_smpType = order_->_smpType;
      response->_smpGroup = order_->_smpGroup;
      response->_timeInForce = static_cast<yijin::MatchSdk::order::TimeInForce>(order_->_timeInForce);
      response->_keepPriority = (checkType_ == 1) ? yijin::MatchSdk::order::KeepPriority::Keep
                                                  : yijin::MatchSdk::order::KeepPriority::Non_Keep;

      response->_transactTime[0] = transactTime_ / 1000000;
      response->_transactTime[1] = transactTime_ % 1000000;
      struct timeval tv;
      struct timezone tz {
        0, 0
      };
      gettimeofday(&tv, &tz);
      response->_sendingTime[0] = tv.tv_sec;
      response->_sendingTime[1] = tv.tv_usec;
      response->_submitType = eu_fbu::yijin::MatchSdk::order::OrderSubmitType::RealOrder;
      response->_checksum = CHECKSUM_CONST;
    } break;
    default:
      std::cout << "unkown exectype:" << et_ << std::endl;
      break;
  }
}

// 成交回报
void FbuCrossSdk::dealExecutionResponseFunc_eu(const eu_fbu::yijin::MatchSdk::order::Order* order_,
                                               uint64_t& transactTime_) {
  Response* response = rspCache_.GetNewRspPtr();
  response->_msgType = yijin::MatchSdk::order::ExecutionReport;
  response->_senderCompID = order_->_targetCompID;
  response->_targetCompID = order_->_senderCompID;
  response->_clOrdID = order_->_clOrdID;
  response->_execID = order_->_execID;  // TODO(cater) : need to log this?
  response->_orderID = order_->_orderID;
  // response->_origClOrdID          = order_->_origClOrdID;
  response->_execType = yijin::MatchSdk::order::ExecType::ET_Trade;
  response->_orderStatus = static_cast<yijin::MatchSdk::order::OrderStatus>(order_->_orderStatus);
  response->_lastQty = order_->_lastQty;
  response->_lastPx = order_->_lastPx;
  response->_cumQty = order_->GetMatchedQty();
  response->_leavesQty = order_->_leavesQty;
  response->_lastLiquidityInd = static_cast<yijin::MatchSdk::order::LiquidityInd>(order_->_lastLiquidityInd);
  response->_createdBy = static_cast<yijin::MatchSdk::order::CreatedBy>(order_->_createdBy);
  response->_canceledBy = static_cast<yijin::MatchSdk::order::CanceledBy>(order_->_canceledBy);
  response->_orderType = static_cast<yijin::MatchSdk::order::OrderType>(order_->_orderType);
  response->_symbol = order_->_symbol;
  response->_side = static_cast<yijin::MatchSdk::order::Side>(order_->_side);
  response->_orderQty = order_->_qty;
  response->_price = order_->_price;
  response->_priceScale = order_->_priceScale;
  response->_smpType = order_->_smpType;
  response->_smpGroup = order_->_smpGroup;
  response->_timeInForce = static_cast<yijin::MatchSdk::order::TimeInForce>(order_->_timeInForce);
  response->_submitType = eu_fbu::yijin::MatchSdk::order::OrderSubmitType::RealOrder;
  response->_transactTime[0] = transactTime_ / 1000000;
  response->_transactTime[1] = transactTime_ % 1000000;
  struct timeval tv;
  struct timezone tz {
    0, 0
  };
  gettimeofday(&tv, &tz);
  response->_sendingTime[0] = tv.tv_sec;
  response->_sendingTime[1] = tv.tv_usec;
  response->_checksum = CHECKSUM_CONST;
}

void FbuCrossSdk::executionResponseFromReq_eu(const eu_fbu::yijin::MatchSdk::order::Request& request_,
                                              const eu_fbu::yijin::MatchSdk::order::Order* order_,
                                              eu_fbu::yijin::MatchSdk::order::ExecType et_, uint64_t& transactTime_,
                                              const eu_fbu::yijin::MatchSdk::order::ErrorCode ec_) {
  Response* response = rspCache_.GetNewRspPtr();
  response->_msgType = yijin::MatchSdk::order::ExecutionReport;
  response->_senderCompID = request_._targetCompID;
  response->_targetCompID = request_._senderCompID;
  response->_orderID = order_->_orderID;
  response->_clOrdID = request_._orderID;
  response->_origClOrdID = request_._origOrderID;
  response->_execType =
      static_cast<yijin::MatchSdk::order::ExecType>(et_);  // yijin::MatchSdk::order::ExecType::ET_Rejected;
  response->_orderStatus = static_cast<yijin::MatchSdk::order::OrderStatus>(order_->_orderStatus);
  response->_rejectReason = static_cast<yijin::MatchSdk::order::ErrorCode>(ec_);
  response->_cumQty = order_->GetMatchedQty();  // 已成交量
  response->_lastQty = order_->_leavesQty;      // 保持跟旧撮合一致
  response->_lastPx = 0;
  response->_leavesQty = 0;
  response->_createdBy = static_cast<yijin::MatchSdk::order::CreatedBy>(order_->_createdBy);
  response->_canceledBy = static_cast<yijin::MatchSdk::order::CanceledBy>(request_._canceledBy);
  response->_orderType = static_cast<yijin::MatchSdk::order::OrderType>(order_->_orderType);
  response->_symbol = order_->_symbol;
  response->_side = static_cast<yijin::MatchSdk::order::Side>(order_->_side);
  response->_orderQty = order_->_qty;
  response->_price = order_->_price;
  response->_priceScale = order_->_priceScale;
  response->_smpType = order_->_smpType;
  response->_smpGroup = order_->_smpGroup;
  response->_timeInForce = static_cast<yijin::MatchSdk::order::TimeInForce>(order_->_timeInForce);
  // response->_lastLiquidityInd     = order_->_lastLiquidityInd;
  response->_transactTime[0] = transactTime_ / 1000000;
  response->_transactTime[1] = transactTime_ % 1000000;
  // response->_keepPriority = (checkType_ == 1) ? yijin::MatchSdk::order::KeepPriority::Keep :
  // yijin::MatchSdk::order::KeepPriority::Non_Keep;
  struct timeval tv;
  struct timezone tz {
    0, 0
  };
  gettimeofday(&tv, &tz);
  response->_sendingTime[0] = tv.tv_sec;
  response->_sendingTime[1] = tv.tv_usec;
  response->_submitType = eu_fbu::yijin::MatchSdk::order::OrderSubmitType::RealOrder;
  response->_checksum = CHECKSUM_CONST;
}

// 虚拟成交回报
void FbuCrossSdk::mirrorDealExecutionResponseFunc_eu(const eu_fbu::yijin::MatchSdk::order::Order* order_,
                                                     uint64_t& transactTime_,
                                                     eu_fbu::yijin::MatchSdk::utility::UUID4& mExecId_,
                                                     uint64_t& mUid) {
  Response* response = rspCache_.GetNewRspPtr();
  response->_msgType = yijin::MatchSdk::order::ExecutionReport;
  response->_senderCompID = order_->_targetCompID;
  response->_targetCompID = mUid;
  response->_clOrdID = order_->_mirrorOrdID;
  response->_execID = mExecId_;
  response->_submitType = eu_fbu::yijin::MatchSdk::order::OrderSubmitType::MirrorOrder;
  response->_orderID = order_->_orderID;
  response->_execType = yijin::MatchSdk::order::ExecType::ET_Trade;
  response->_orderStatus = yijin::MatchSdk::order::OrderStatus::OS_Fill;  // eu 特殊处理
  response->_lastQty = order_->_lastQty;
  response->_lastPx = order_->_lastPx;
  response->_cumQty = order_->_lastQty;  // eu 特殊处理
  response->_leavesQty = 0;              // eu 特殊处理
  response->_lastLiquidityInd = static_cast<yijin::MatchSdk::order::LiquidityInd>(order_->_lastLiquidityInd);
  response->_createdBy = static_cast<yijin::MatchSdk::order::CreatedBy>(order_->_createdBy);
  response->_canceledBy = static_cast<yijin::MatchSdk::order::CanceledBy>(order_->_canceledBy);
  response->_orderType = static_cast<yijin::MatchSdk::order::OrderType>(order_->_orderType);
  response->_symbol = order_->_symbol;
  response->_side = static_cast<yijin::MatchSdk::order::Side>(order_->_side);
  response->_orderQty = order_->_lastQty;  // eu 特殊处理
  response->_price = order_->_price;
  response->_priceScale = order_->_priceScale;
  response->_smpGroup = order_->_smpGroup;
  response->_smpType = order_->_smpType;
  response->_timeInForce = static_cast<yijin::MatchSdk::order::TimeInForce>(order_->_timeInForce);
  response->_transactTime[0] = transactTime_ / 1000000;
  response->_transactTime[1] = transactTime_ % 1000000;
  struct timeval tv;
  struct timezone tz {
    0, 0
  };
  gettimeofday(&tv, &tz);
  response->_sendingTime[0] = tv.tv_sec;
  response->_sendingTime[1] = tv.tv_usec;
  response->_checksum = CHECKSUM_CONST;
}
}  // namespace fbu
