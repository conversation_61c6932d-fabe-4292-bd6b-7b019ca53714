/* 期货撮合引擎sdk接口 */
#pragma once

#include <functional>
#include <list>
#include <memory>
#include <string>

#include "FbuReqHeader.h"
#include "FbuResponse.h"
#include "FbuRspCache.h"
#include "FbuRspHeader.h"
#include "futures_match_sdk/inc/MatchSdkIf.h"
#include "futures_match_sdk/inc/Request.h"
#include "futures_match_sdk/inc_eu/MatchSdkIf.h"

namespace fbu {
static constexpr uint32_t CHECKSUM_CONST = 0xFF000100;
class FbuCrossSdk {
 public:  // 给外部调用的接口
  FbuCrossSdk();
  ~FbuCrossSdk();
  // req: 80字节header + 480字节req.body
  // rsp: 80字节header + 动态长度rsp.body1 + 动态长度rsp.body2 + ... + 动态长度rsp.bodyN
  bool DoCross(uint64_t cross_seq, std::string req, std::string& rsp);

 private:  // 内部接口
  void logFunc(int level, char* buf);
  void rejectResponseFunc(const yijin::MatchSdk::order::Request& request_,
                          yijin::MatchSdk::order::ResponseMsgType msgType_, uint64_t& transactTime_,
                          const yijin::MatchSdk::order::ErrorCode ec_);
  void executionResponseFunc(const yijin::MatchSdk::order::Order* order_, yijin::MatchSdk::order::ExecType et_,
                             uint64_t& transactTime_, const yijin::MatchSdk::order::ErrorCode ec_,
                             const uint8_t checkType_);
  void dealExecutionResponseFunc(const yijin::MatchSdk::order::Order* order_, uint64_t& transactTime_);
  void executionResponseFromReq(const yijin::MatchSdk::order::Request& request_,
                                const yijin::MatchSdk::order::Order* order_, yijin::MatchSdk::order::ExecType et_,
                                uint64_t& transactTime_, const yijin::MatchSdk::order::ErrorCode ec_,
                                const uint8_t checkType_);
  // 欧洲站
  void rejectResponseFunc_eu(const eu_fbu::yijin::MatchSdk::order::Request& request_,
                             eu_fbu::yijin::MatchSdk::order::ResponseMsgType msgType_, uint64_t& transactTime_,
                             const eu_fbu::yijin::MatchSdk::order::ErrorCode ec_);
  void executionResponseFunc_eu(const eu_fbu::yijin::MatchSdk::order::Order* order_,
                                eu_fbu::yijin::MatchSdk::order::ExecType et_, uint64_t& transactTime_,
                                const eu_fbu::yijin::MatchSdk::order::ErrorCode ec_, const uint8_t checkType_);
  void dealExecutionResponseFunc_eu(const eu_fbu::yijin::MatchSdk::order::Order* order_, uint64_t& transactTime_);
  void executionResponseFromReq_eu(const eu_fbu::yijin::MatchSdk::order::Request& request_,
                                   const eu_fbu::yijin::MatchSdk::order::Order* order_,
                                   eu_fbu::yijin::MatchSdk::order::ExecType et_, uint64_t& transactTime_,
                                   const eu_fbu::yijin::MatchSdk::order::ErrorCode ec_);
  void mirrorDealExecutionResponseFunc_eu(const eu_fbu::yijin::MatchSdk::order::Order* order_, uint64_t& transactTime_,
                                          eu_fbu::yijin::MatchSdk::utility::UUID4& mExecId_, uint64_t& mUid);

 private:
  yijin::MatchSdk::MatchSdkIf matcher_;
  yijin::MatchSdk::InitCallback callback_;
  RspCache rspCache_;
  uint64_t last_offset_ = 0;
  uint64_t begin_time_ = 0;
  uint64_t end_time_ = 0;
  eu_fbu::yijin::MatchSdk::MatchSdkIf matcher_eu_;
  eu_fbu::yijin::MatchSdk::InitCallback callback_eu_;
};

}  // namespace fbu
