#pragma once
#include <cstdint>
#include <string>

#include "ErrorCode.h"  // NOLINT

namespace eu_fbu::yijin {
namespace MatchSdk {
namespace order {

enum RequestMsgType : uint16_t {
  NewOrder = 0x4400,               // "D\0" in chars
  CancelOrder = 0x4600,            // "F\0" in chars
  ModifyOrder = 0x4800,            // "H\0" in chars
  MarkPriceRequest = 0x6400,       // "d\0" in chars
  SecurityStatusRequest = 0x6500,  // "e\0" in chars 合约状态消息
};

enum ResponseMsgType : uint16_t {
  ExecutionReport = 0x3800,        // "8\0" in chars
                                   //  The execution report message is used to:
                                   //  1. confirm the receipt of an order
                                   //  2. confirm changes to an existing order
                                   //  3. relay order status information
                                   //  4. relay fill information on working orders
                                   //  5. reject orders (35=D)
  CancelRejectResponse = 0x3900,   // "9\0" in chars
                                   //  1. reject cancel request (35=F)
  ReplaceRejectResponse = 0x4100,  // "A\0" in chars
                                   //  1. reject cancel request (35=F)
};

enum OrderType : char {
  MarketOrder = '1',  // 期货市价单
  LimitOrder = '2',   // 期货/现货限价单
};

enum Side : char { Buy = '1', Sell = '2' };

// Specified how long the order remains in effect
enum TimeInForce : char {
  GoodTillCancel = '1',         // GTC
  PostOnly = '2',               //
  ImmediateOrCancel = '3',      // IOC
  FillOrKill = '4',             // FOK
  RetailPriceImprovement = '5'  // RPI
};

enum CreatedBy : char { CreatedByUser = 'U', CreatedByCondition = 'C', CreatedByStop = 'S' };

enum CanceledBy : char { CanceledByUser = 'U', CanceledByLiquidation = 'L', CanceledByPosition = 'P' };

// 订单状态
enum OrderStatus : char {
  OS_New = '0',          // 未成交有效
  OS_PartialFill = '1',  // 部分成交
  OS_Fill = '2',         // 完全成交
  OS_Canceled = '4',     // 撤销
  OS_Rejected = '8'      // 拒绝
};

enum ExecType : char {
  ET_New = '0',       // 创建
  ET_Canceled = '4',  // 撤销
  ET_Rejected = '7',  // 拒绝
  ET_Trade = 'F',     // 成交
  ET_Modify = 'R'     // 变更
};

// 流动性
enum LiquidityInd : char {
  LiquidityIndNA = 0,  // 无效
  AddLiquidity = '1',  // 提供流动性，maker单提供流动性
  TakeLiquidity = '2'  // 消耗流动性，taker单消耗流动性
};

// 用于modify response，是否保留原订单
enum KeepPriority : char { Non_Keep = 0, Keep = 1 };

// selfTradePreventionMode
enum SmpType : uint8_t { SMPT_NONE = 0, SMPT_TAKER = 1, SMPT_MAKER = 2, SMPT_BOTH = 3 };

enum UserType : char {
  LpUser = 'L',       // Liquidity provider
  CustomerUser = 'C'  // Customer
};

// response
enum OrderSubmitType : char {
  RealOrder = 'R',   // real order
  MirrorOrder = 'M'  // mirror order，下游仅需要关注成交本身。订单可忽略
};
enum forbidMatchRpiType : int8_t {
  MatchWithRpi = 0,   // can match with RPI
  NoMatchWithRpi = 1  // cannot match with RPI
};
const std::string& toString(OrderType orderType_) noexcept;
const std::string& toString(Side side_) noexcept;
const std::string& toString(TimeInForce timeInForce_) noexcept;
const std::string& toString(OrderStatus orderStatus_) noexcept;
const std::string& toString(ExecType execType_) noexcept;
const std::string& toString(LiquidityInd liquidityInd_) noexcept;
const std::string& toString(RequestMsgType msgType_) noexcept;
const std::string& toString(ResponseMsgType msgType_) noexcept;
const std::string& toString(CreatedBy createdBy_) noexcept;
const std::string& toString(CanceledBy canceledBy_) noexcept;
const std::string& toString(SmpType smpType_) noexcept;
const std::string& toString(UserType userType_) noexcept;
const std::string& toString(OrderSubmitType submitType_) noexcept;
}  // namespace order
}  // namespace MatchSdk
}  // namespace eu_fbu::yijin
