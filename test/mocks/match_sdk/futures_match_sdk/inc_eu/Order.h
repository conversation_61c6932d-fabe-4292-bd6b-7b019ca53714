#pragma once

#include <boost/functional/hash.hpp>
#include <memory>
#include <unordered_map>

#include "ComDef.h"  // NOLINT
#include "UUID4.h"   // NOLINT

namespace eu_fbu::yijin {
namespace MatchSdk {
namespace order {

class PriceNode;
class Request;

class Order {
 public:
  Order() noexcept { reset(); }

  utility::UUID4 _orderID;  // 撮合内部的订单ID
  uint64_t _offset;         // Kafka offset of the request
  utility::UUID4 _clOrdID;  // orderId of request，业务订单ID
  uint64_t _senderCompID;   // UNIQUE User ID in USER/ACCOUNT DB
  uint64_t _targetCompID;
  utility::UUID4 _execID;
  uint64_t _qty;        // orderQty
  uint64_t _price;      // price
  uint64_t _lastQty;    // 最新（上次）成交量
  uint64_t _leavesQty;  // 剩余订单量
  uint64_t _lastPx;     // 最新成交价
  uint64_t _avgPx;      // 该字段废弃了
  uint64_t _transactTime;

  uint32_t _symbol;
  OrderType _orderType;
  Side _side;
  TimeInForce _timeInForce;
  LiquidityInd _lastLiquidityInd;
  OrderStatus _orderStatus;
  CreatedBy _createdBy;
  CanceledBy _canceledBy;
  char _priceScale;
  char _reserve[4];  // 8字节对齐
  int32_t _smpGroup{0};
  SmpType _smpType;
  char _reserve1[2];
  UserType _userType;
  uint64_t _mirrorUID;
  utility::UUID4 _mirrorOrdID;  // mirror orderId of request，业务镜像订单ID

  /** ANYTHING AFTER THIS CANNOT BE SAVED TO AND LOADED FROM FILE */
  /*  _prevOrder is the first, donot insert anything in front of _prevOrder */

  Order* _prevOrder;      // 在priceNode里的上一跳订单
  Order* _nextOrder;      // 在priceNode里的下一跳订单
  PriceNode* _priceNode;  // 本订单所在的PriceNode

  void reset() noexcept;
  bool IsOrderQtyAvailable();
  void UpdateMatchOrder(uint64_t matchedQty_, uint64_t matchedPrice_, LiquidityInd liqInd_, utility::UUID4& execID_,
                        uint64_t& transactTime_);
  void InitOrderFromRequest(Request& request_, uint64_t offset_) noexcept;
  // 获得已成交量
  uint64_t GetMatchedQty() const { return (_qty - _leavesQty); }
  SmpType GetSmpType(int32_t& makerSmpGroup_, uint64_t& makerUid_);
};

using OrdersMap = std::unordered_map<utility::UUID4, order::Order*, boost::hash<utility::UUID4>>;  // <orderId, order>

}  // namespace order
}  // namespace MatchSdk
}  // namespace eu_fbu::yijin
