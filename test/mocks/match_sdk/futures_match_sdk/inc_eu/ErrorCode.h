#pragma once

#include <cstdint>
#include <string>

namespace eu_fbu::yijin {
namespace MatchSdk {
namespace order {

enum ErrorCode : uint16_t {
  EC_NoError = 0,
  EC_Others = 1,
  EC_UnknownMessageType,
  EC_MissingClOrdID,  // 未携带订单id
  EC_MissingOrigClOrdID,
  EC_ClOrdIDOrigClOrdIDAreTheSame,  // 5
  EC_DuplicatedClOrdID,             // 重复的订单id
  EC_OrigClOrdIDDoesNotExist,
  EC_TooLateToCancel,
  EC_UnknownOrderType,
  EC_UnknownSide,  // 10
  EC_UnknownTimeInForce,
  EC_WronglyRouted,
  EC_MarketOrderPriceIsNotZero,
  EC_LimitOrderInvalidPrice,
  EC_NoEnoughQtyToFill,  // 15
  EC_NoImmediateQtyToFill,
  EC_QtyCannotBeZero,
  EC_PerCancelRequest,  // 因为撤销订单请求而撤销原订单
  EC_MarketOrderCannotBePostOnly,
  EC_PostOnlyWillTakeLiquidity,  // 20 postonly单将会消耗流动性
  EC_CancelReplaceOrder,         // 因为修改订单请求而撤销原订单
  EC_InvalidSymbolStatus,
  EC_MarketOrderNoSupportTIF,  // 市价单不支持除IOC/FOK外的其他TimeInForce
  EC_ReachMaxTradeNum,         // 达到成交最大数量
  EC_InvalidPriceScale,        // 25 PriceScale不在0-18范围内
  EC_BySelfMatch = 28,         // 28 触发stp被撤单
  EC_InvalidSmpType,           // 29 非法smpType
  EC_CancelByMMP,              // 30 因为MMP被撤单, used in option
  EC_InCallAuctionStatus,  // 31 集合竞价状态，不支持改单；集合竞价撮合状态，不支持下撤改单
  EC_InvalidUserType = 34,   // 非法UserType
  EC_InvalidMirrorOid = 35,  // 非法mirrorOrderId
  EC_InvalidMirrorUid = 36,  // 非法mirrorUid
  EC_Max                     // 最大的错误值
};

const std::string& toString(ErrorCode ec_) noexcept;
}  // namespace order
}  // namespace MatchSdk
}  // namespace eu_fbu::yijin
