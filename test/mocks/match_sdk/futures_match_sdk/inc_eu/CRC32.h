#pragma once

#include <boost/crc.hpp>
#include <utility>

namespace eu_fbu::yijin {
namespace MatchSdk {
namespace utility {

/* return pair of success and checksum */
inline std::pair<bool, uint32_t> crc32(const char* buf_, const size_t len_) noexcept {
  static_assert(sizeof(boost::crc_32_type) == 4);

  boost::crc_32_type crc32;
  try {
    crc32.process_bytes(buf_, len_);
  } catch (const std::exception& e) {
    // LogError << "failed to calc checksum because " << e.what() << LogSend;
    return std::make_pair(false, ~0x0UL);
  }

  return std::make_pair(true, crc32.checksum());
}

}  // namespace utility
}  // namespace MatchSdk
}  // namespace eu_fbu::yijin
