#pragma once

#include <bitset>
#include <string>

#include "ObuResponse.h"
#include "ObuRspHeader.h"
namespace obu {
// bitset有效值范围
#define BitIndex_Max 63
#define BitIndex_Min -1

#define BLOCK_SIZE 1024 * 10  // 每次内存不足扩展长度

class RspCache {
 public:
  //************************************
  // 方法:    RspCache
  // 日期:    2021/11/02 21:38:32
  // 说明:    构造应答缓冲区，默认缓冲区大小1024*10，以后不够用了每次扩展BLOCK_SIZE
  //************************************
  explicit RspCache(int32_t size = 1024 * 10) {
    size = size / 1024 * 1024;  // 保证size是1024的倍数
    if (size < 1024 * 10) {     // 保证初始大小不会太小
      size = 1024 * 10;
    }

    init_len = size;
    rsp_count = 0;
    data = new char[size];
    len = sizeof(RspHeader);  // sizeof编译时确定值，不耗时
    max_len = size;
    isAddHead = false;
    symbol = 0;
    isMultiSymbol = false;
    memset(data, 0, size);
  }

  //************************************
  // 方法:    ~RspCache
  // 日期:    2021/11/02 21:38:16
  // 说明:    析构对象释放资源
  //************************************
  ~RspCache() {
    if (data) {
      delete[] data;
    }
    data = nullptr;
  }

  //************************************
  // 方法:    ReSet
  // 日期:    2021/11/02 21:37:52
  // 说明:    重置缓冲区，清空原数据
  //************************************
  int ReSet() {
    // bit_set.reset();  // 全部置0
    int ret = 0;
    if ((max_len / init_len) > 5) {  // 如果自动扩展长度超过原初始化的5倍了，则释放空间，重新申请init_len大小的空间使用
      char* new_data = new char[init_len];
      delete[] data;
      data = new_data;
      new_data = nullptr;
      memset(data, 0, init_len);  // 新申请内存初始化
      len = sizeof(RspHeader);    // 新内存重置长度
      max_len = init_len;         // 新内存重置长度
      ret = 1;
    }
    rsp_count = 0;
    isAddHead = false;
    symbol = 0;
    isMultiSymbol = false;
    memset(data, 0, len);     // 把上次已用过的长度初始化
    len = sizeof(RspHeader);  // 重置有效数据len长度，只要new成功，前n个字节就是包头。
    return ret;
  }

  //************************************
  // 方法:    AddHead
  // 日期:    2021/11/02 21:37:20
  // 说明:    给当前缓冲区添加业务包头
  //************************************
  bool AddHead(uint64_t offset) {
    if (len <=
        0) {  // 如果添加包头时，当前长度为零，说明new失败了,不可以重新new，因为调用addhead时，item都已经执行过回调了
      return false;
    }
    if (!isAddHead) {
      isAddHead = true;
      RspHeader* pHead = reinterpret_cast<RspHeader*>(data);
      // 初始化head
      pHead->_magicCookie = RspHeader::MAGIC;
      pHead->_hdrVersion = RspHeader::VERSION;
      pHead->_hdrLength = sizeof(RspHeader);
      pHead->_action = RspHeader::ActionCode::Msg;
      pHead->_action = !isMultiSymbol ? RspHeader::ActionCode::Msg : RspHeader::ActionCode::MultiSymbolMsg;
      pHead->_offset = offset;
      pHead->_length = rsp_count * sizeof(Response);
      pHead->_count = rsp_count;
      pHead->_passThrough = false;
      return true;
    }
    return false;
    // 重复调用不做任何事
  }
  bool UpdateTime(int64_t start_, int64_t end_) {
    if (len <=
        0) {  // 如果添加包头时，当前长度为零，说明new失败了,不可以重新new，因为调用addhead时，item都已经执行过回调了
      return false;
    }
    if (isAddHead) {
      RspHeader* pHead = reinterpret_cast<RspHeader*>(data);
      pHead->_startCross = start_;
      pHead->_endCross = end_;
      return true;
    }
    return false;
  }
  //************************************
  // 方法:    GetNewRspPtr
  // 日期:    2021/11/02 21:36:24
  // 说明:    获取下一个response的指针首地址
  //************************************
  Response* GetNewRspPtr() {
    if ((len + sizeof(Response)) > static_cast<std::size_t>(max_len)) {  // 内存不足，需要扩展长度BLOCK_SIZE
      char* new_data = new char[len + BLOCK_SIZE];
      // memset(new_data,0,len + BLOCK_SIZE);
      memcpy(new_data, data, len);
      delete[] data;
      data = new_data;
      new_data = nullptr;
      max_len = len + BLOCK_SIZE;  // 新内存重置长度
    }
    Response* pRsp = reinterpret_cast<Response*>(data + len);
    new (pRsp) Response();

    len = len + sizeof(Response);  // 更新最新长度
    // 初始化response
    pRsp->_magicCookie = Response::MAGIC;
    pRsp->_msgVersion = Response::VERSION;
    pRsp->_msgLength = sizeof(Response);  // sizeof编译时确定值，不耗时
    rsp_count++;
    return pRsp;
  }

  //************************************
  // 方法:    GetData
  // 日期:    2021/11/02 21:35:54
  // 说明:    获取当前数据首地址
  //************************************
  char* GetData() const { return data; }

  //************************************
  // 方法:    GetLen
  // 日期:    2021/11/02 21:36:09
  // 说明:    获取当前有效数据的长度
  //************************************
  int32_t GetLen() { return len; }

  //************************************
  // 方法:    PrintHead
  // 日期:    2021/11/02 21:49:12
  // 说明:    打印包头内容
  //************************************
  void PrintHead() {
    if (isAddHead) {  // 包头初始化过之后才能打印
      RspHeader* pHead = reinterpret_cast<RspHeader*>(data);
      pHead->toString();
    }
  }

  //************************************
  // 方法:    GetRspCount
  // 日期:    2021/11/02 21:56:39
  // 说明:    获取rsponse的个数
  //************************************
  int32_t GetRspCount() { return rsp_count; }

  void SetSymbol(uint32_t symbol_) {
    if (symbol == 0) {
      symbol = symbol_;
    } else if (symbol != symbol_) {  // set multi symbol
      isMultiSymbol = true;
    }
  }

 private:
  char* data{nullptr};
  int32_t len;
  int32_t max_len;
  bool isAddHead;
  int32_t init_len;
  int32_t rsp_count;
  uint32_t symbol{0};
  bool isMultiSymbol{false};
};
}  // namespace obu
