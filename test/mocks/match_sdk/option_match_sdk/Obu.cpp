/*
  用于测试封装
*/
#include <iostream>

#include "ObuCrossSdk.h"
#include "ObuResponse.h"
#include "ObuRspHeader.h"

int main() {
  obu::ObuCrossSdk* sdk1 = new obu::ObuCrossSdk();
  // 包头
  obu::Header header;
  header._magicCookie = obu::Header::MAGIC;
  header._hdrVersion = obu::Header::VERSION;
  header._hdrLength = sizeof(obu::Header);
  header._action = obu::Header::ActionCode::Msg;
  header._passThrough = 0;
  header._count = 1;
  header._offset = 0;
  header._length = sizeof(obu::yijin::MatchSdk::order::Request);
  std::string sHeader(reinterpret_cast<char*>(&header), sizeof(obu::Header));
  // 包体
  obu::yijin::MatchSdk::order::Request request;
  request._orderID = obu::yijin::MatchSdk::utility::uuid4();
  request._origOrderID = obu::yijin::MatchSdk::utility::uuid4();
  request._side = obu::yijin::MatchSdk::order::Side::Buy;
  request._timeInForce = obu::yijin::MatchSdk::order::TimeInForce::GoodTillCancel;
  request._qty = 1;
  request._price = 5000;
  request._bitIndex = 3;    // 期权特有字段
  request._bitVersion = 0;  // 期权特有字段
  struct timeval tv;
  gettimeofday(&tv, nullptr);
  request._transactTime[0] = tv.tv_sec;
  request._transactTime[1] = tv.tv_usec;
  request._msgType = obu::yijin::MatchSdk::order::RequestMsgType::NewOrder;
  request._msgSeqNum = 1;
  request._symbol = 0x01;
  request._orderType = obu::yijin::MatchSdk::order::OrderType::LimitOrder;
  request._createdBy = obu::yijin::MatchSdk::order::CreatedBy::CreatedByUser;
  request._canceledBy = obu::yijin::MatchSdk::order::CanceledBy::CanceledByUser;
  auto random_seed = static_cast<std::uint32_t>(time(nullptr));
  request._senderCompID = rand_r(&random_seed) % 1000;
  request._targetCompID = rand_r(&random_seed) % 1000;  // 期权禁止自成交所以UID这里测试时给随机值
  request.seal2();
  std::string sRequest(reinterpret_cast<char*>(&request), sizeof(obu::yijin::MatchSdk::order::Request));
  std::string rsp;
  // case 1
  sdk1->DoCross(0, sHeader + sRequest, rsp);

  // 解包rsp
  int pos = 0;
  obu::RspHeader* rspHead = (obu::RspHeader*)rsp.data();
  rspHead->toString();
  pos += sizeof(obu::RspHeader);
  for (size_t i = 0; i < rspHead->_count; i++) {
    obu::Response* item = (obu::Response*)(rsp.data() + pos);
    pos += sizeof(obu::Response);
    item->toString("item+" + std::to_string(i + 1));
  }
  delete sdk1;
  printf("***********************\n");
  // case 2
  sdk1 = new obu::ObuCrossSdk();
  request._side = obu::yijin::MatchSdk::order::Side::Sell;
  request._orderID = obu::yijin::MatchSdk::utility::uuid4();
  request.seal2();
  std::string sRequest1(reinterpret_cast<char*>(&request), sizeof(obu::yijin::MatchSdk::order::Request));
  sdk1->DoCross(0, sHeader + sRequest1, rsp);
  // 解包rsp
  pos = 0;
  rspHead = (obu::RspHeader*)rsp.data();
  rspHead->toString();
  pos += sizeof(obu::RspHeader);
  for (size_t i = 0; i < rspHead->_count; i++) {
    obu::Response* item = (obu::Response*)(rsp.data() + pos);
    pos += sizeof(obu::Response);
    item->toString("item+" + std::to_string(i + 1));
  }
  delete sdk1;
  return 0;
}
