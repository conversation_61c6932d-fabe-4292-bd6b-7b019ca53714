#pragma once

#include <cstdint>
#include <string>

namespace obu::yijin {
namespace MatchSdk {
namespace order {
enum ErrorCode : uint16_t {
  EC_NoError = 0,
  EC_Others = 1,
  EC_UnknownMessageType,
  EC_MissingClOrdID,  // 未携带订单id
  EC_MissingOrigClOrdID,
  EC_ClOrdIDOrigClOrdIDAreTheSame,
  EC_DuplicatedClOrdID,  // 重复的订单id
  EC_OrigClOrdIDDoesNotExist,
  EC_TooLateToCancel,
  EC_UnknownOrderType,
  EC_UnknownSide,
  EC_UnknownTimeInForce,
  EC_WronglyRouted,
  EC_MarketOrderPriceIsNotZero,
  EC_LimitOrderInvalidPrice,
  EC_NoEnoughQtyToFill,
  EC_NoImmediateQtyToFill,
  EC_QtyCannotBeZero,
  EC_PerCancelRequest,  // 因为撤销订单请求而撤销原订单
  EC_MarketOrderCannotBePostOnly,
  EC_PostOnlyWillTakeLiquidity,  // postonly单将会消耗流动性
  EC_CancelReplaceOrder,         // 因为修改订单请求而撤销原订单
  EC_InvalidSymbolStatus,
  EC_MarketOrderNoSupportTIF,  // 市价单不支持除IOC/FOK外的其他TimeInForce
  EC_ReachMaxTradeNum,         // (期货)一次委托达到成交最大次数
  EC_InvalidPriceScale,        // (期货)25 PriceScale不在0-18范围内
  EC_BitIndexInvalid,          // bitindex的值超过最大有效值63，是无效值
  EC_BySelfMatch = 28,         // 因禁止自成交而停止撮合，剩余的量撤单返回码
  EC_InvalidSmpType,           // 29 非法smpType
  EC_CancelByMMP,              // 30 因为MMP被撤单
  EC_Max                       // 最大的错误值
};
const std::string& toString(ErrorCode ec_) noexcept;
}  // namespace order
}  // namespace MatchSdk
}  // namespace obu::yijin
