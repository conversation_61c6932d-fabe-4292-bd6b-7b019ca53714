#pragma once

#include <boost/functional/hash.hpp>
#include <memory>
#include <unordered_map>

#include "ComDef.h"
#include "UUID4.h"

namespace obu::yijin {
namespace MatchSdk {
namespace order {
class PriceNode;
class Request;
class Order {
 public:
  Order() noexcept { reset(); }
  utility::UUID4 _orderID;  // 撮合内部的订单ID，在InitOrderFromRequest里初始化。_orderID需要通过response传给下游
  uint64_t _offset;         // Kafka offset of the request
  utility::UUID4 _clOrdID;  // orderId of request，业务订单ID
  uint64_t _senderCompID;   // UNIQUE User ID in USER/ACCOUNT DB
  uint64_t _targetCompID;
  utility::UUID4 _execID;
  uint64_t _qty;        // orderQty
  uint64_t _price;      // price
  uint64_t _lastQty;    // 最新（上次）成交量
  uint64_t _leavesQty;  // 剩余订单量
  uint64_t _lastPx;     // 最新成交价
  uint64_t _avgPx{0};
  uint64_t _transactTime;
  uint32_t _symbol;
  OrderType _orderType;
  Side _side;
  TimeInForce _timeInForce;
  LiquidityInd _lastLiquidityInd;
  OrderStatus _orderStatus;
  CreatedBy _createdBy;
  CanceledBy _canceledBy;
  uint8_t _featureBit;
  int8_t _bitIndex;    // 比特位索引信息
  int8_t _bitVersion;  // bit位版本信息暂时不使用，全部放上送的默认值
  char _reserve1[2];   // 8字节对齐
  int32_t _smpGroup{0};
  SmpType _smpType;
  char _reserve2[3];      // 占位
  uint32_t _coin{0};      // 期权symbol对应的coin
  uint32_t _reserve3{0};  // 8字节对齐占位
  uint64_t _reserve4{0};  // 备用
  uint64_t _reserve5{0};  // 备用
  /** ANYTHING AFTER THIS CANNOT BE SAVED TO AND LOADED FROM FILE */
  /*  _prevOrder is the first, donot insert anything in front of _prevOrder */

  Order* _prevOrder{nullptr};      // 在priceNode里的上一跳订单
  Order* _nextOrder{nullptr};      // 在priceNode里的下一跳订单
  PriceNode* _priceNode{nullptr};  // 本订单所在的PriceNode

  void reset() noexcept;
  bool IsOrderQtyAvailable();
  void UpdateMatchOrder(uint64_t matchedQty_, uint64_t matchedPrice_, LiquidityInd liqInd_, utility::UUID4& execID_,
                        uint64_t& transactTime_);
  void InitOrderFromRequest(Request& request_, uint64_t offset_, int32_t coin) noexcept;
  // 获得已成交量
  uint64_t GetMatchedQty() const { return (_qty - _leavesQty); }
  SmpType GetSmpType(int32_t makerSmpGroup_, uint64_t makerUid_);
  bool IsMmp();
};
using OrdersMap = std::unordered_map<utility::UUID4, order::Order*, boost::hash<utility::UUID4>>;
}  // namespace order
}  // namespace MatchSdk
}  // namespace obu::yijin
