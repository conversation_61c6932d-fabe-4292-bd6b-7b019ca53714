#pragma once
#include <string>

#include "ComDef.h"  // NOLINT
#include "UUID4.h"   // NOLINT

namespace eu_obu::yijin {
namespace MatchSdk {
namespace order {

/**
 * NOTICE : NEVER INHERIT FROM ANY OTHER TYPES
 * because we will reinterpret_cast message buffer directly to this object type!
 */
// 期货request
class Request {
 public:
  static constexpr uint64_t MAGIC = 0xABCDABEFFEBADCBA;
  static constexpr uint32_t VERSION = 0xFF000100;

  Request() noexcept : _magicCookie{MAGIC}, _msgVersion{VERSION}, _msgLength{sizeof(Request)} { reset(); }

  void reset() noexcept;
  bool seal2() noexcept;
  bool isChecksumMatched() const noexcept;
  const std::string toString() const noexcept;

  ErrorCode validateNewRequest() noexcept;
  ErrorCode validateCancelRequest() noexcept;
  ErrorCode ValidateModifyRequest() noexcept;

  uint64_t GetSendTime() { return _sendingTime[0] * 1000000 + _sendingTime[1]; }
  uint64_t GetTransTime() { return _transactTime[0] * 1000000 + _transactTime[1]; }
  void SetTransacTime(uint64_t lastTSMicroSec) {
    _transactTime[0] = lastTSMicroSec / 1000000;
    _transactTime[1] = lastTSMicroSec % 1000000;

    seal2();
  }

  bool CheckQtyValid() { return _qty > 0; }

 public:  // Member, public.. don't wanna add setter/getter
  // >>>>>>>>>> HEADER <<<<<<<<<
  uint64_t _magicCookie;
  uint32_t _msgVersion;  // [0]-reserved(0xFF), [1]-major, [2]-minor, [3]-patch
  uint16_t _msgLength;   // sizeof(Request)
  RequestMsgType _msgType;
  uint8_t _featureBit;  // 特性标识位，bit1 表示MMP订单
  UserType _userType;   // 用户类型，1 byte
  char _padding[2];
  int32_t _smpGroup;         // smp交易组ID
  uint64_t _senderCompID;    // accountId
  uint64_t _targetCompID;    // UID
  uint64_t _sendingTime[2];  // UTC timestamp from gettimeofday()
                             // represent the time of sending this message
                             // [0] utc time in second since Epoc,
                             // [1] microsconds of this second
                             // Epoc is 1970-01-01 00:00:00 +0000 (UTC)
  // >>>>>>>>>> BODY <<<<<<<<<
  utility::UUID4 _orderID;  // unique client order id (real type is uint8_t[16])
                            // uuid4, boost's native 128bit data
  OrderType _orderType;
  Side _side;
  TimeInForce _timeInForce;
  SmpType _smpType;             // selfTradePreventionMode 禁止自成交模式
  uint32_t _symbol;             // UNIQUE product ID in PRODUCT DB
                                // e.g. 0x1 - Bitcoin / USD contract
                                // e.g. 0x2 - Eth / USD contract
  uint64_t _qty;                // order quantity
  uint64_t _price;              // order price
  utility::UUID4 _origOrderID;  // original _clOrdID to cancel or replace (uint8_t[16])
  uint64_t _transactTime[2];    // UTC timestamp from gettimefoday()
                                // represent the time of making the business decision
                                // [0] utc time in second since Epoc,
                                // [1] microsconds of this second
                                // Epoc is 1970-01-01 00:00:00 +0000 (UTC)
                                // 当带MMP标识时，_transactTime[0]用来传coin
  CreatedBy _createdBy;
  CanceledBy _canceledBy;
  int8_t _padding1;  // 占位
                     // 当request为MmpPassThrough MMP透传消息时，用于MMP是否开启关闭开关--原_bitIndex
  char _padding2[5];              // 占位
  utility::UUID4 _mirrorOrderID;  // 虚拟orderid 16 byte
  uint64_t _mirrorUid;            // 虚拟uid
  char _padding3[4];              // 占位
  // >>>>>>>>>> TRAILER<<<<<<<
  uint32_t _checksum;  // CRC-32 for header+body
                       // https://en.wikipedia.org/wiki/Cyclic_redundancy_check
};

static_assert(sizeof(Request) == 168);

class GlobalRequest {
 public:
  bool seal2() noexcept;
  bool isChecksumMatched() const noexcept;
  const std::string toString() const noexcept;

  uint64_t GetSendTime() { return _sendingTime[0] * 1000000 + _sendingTime[1]; }
  void SetTransacTime(uint64_t lastTSMicroSec) {
    _transactTime[0] = lastTSMicroSec / 1000000;
    _transactTime[1] = lastTSMicroSec % 1000000;

    seal2();
  }

 public:  // Member, public.. don't wanna add setter/getter
  uint64_t _magicCookie;
  uint32_t _msgVersion;  // [0]-reserved(0xFF), [1]-major, [2]-minor, [3]-patch
  uint16_t _msgLength;   // sizeof(Request)
  RequestMsgType _msgType;
  uint8_t _featureBit;  // 特性标识位，bit1 表示MMP订单
  char _padding[3];
  int32_t _smpGroup;         // smp交易组ID
  uint64_t _senderCompID;    // accountId
  uint64_t _targetCompID;    // UID
  uint64_t _sendingTime[2];  // UTC timestamp from gettimeofday()
                             // represent the time of sending this message
                             // [0] utc time in second since Epoc,
                             // [1] microsconds of this second
                             // Epoc is 1970-01-01 00:00:00 +0000 (UTC)
  // >>>>>>>>>> BODY <<<<<<<<<
  utility::UUID4 _orderID;  // unique client order id (real type is uint8_t[16])
                            // uuid4, boost's native 128bit data
  OrderType _orderType;
  Side _side;
  TimeInForce _timeInForce;
  SmpType _smpType;             // selfTradePreventionMode 禁止自成交模式
  uint32_t _symbol;             // UNIQUE product ID in PRODUCT DB
                                // e.g. 0x1 - Bitcoin / USD contract
                                // e.g. 0x2 - Eth / USD contract
  uint64_t _qty;                // order quantity
  uint64_t _price;              // order price
  utility::UUID4 _origOrderID;  // original _clOrdID to cancel or replace (uint8_t[16])
  uint64_t _transactTime[2];    // UTC timestamp from gettimefoday()
                                // represent the time of making the business decision
                                // [0] utc time in second since Epoc,
                                // [1] microsconds of this second
                                // Epoc is 1970-01-01 00:00:00 +0000 (UTC)
                                // 当带MMP标识时，_transactTime[0]用来传coin
  CreatedBy _createdBy;
  CanceledBy _canceledBy;
  int8_t _bitIndex;    // 比特位索引信息
  int8_t _bitVersion;  // bit位版本信息暂时不使用，全部放0
  uint32_t _checksum;
};

static_assert(sizeof(GlobalRequest) == 136);
}  // namespace order
}  // namespace MatchSdk
}  // namespace eu_obu::yijin
