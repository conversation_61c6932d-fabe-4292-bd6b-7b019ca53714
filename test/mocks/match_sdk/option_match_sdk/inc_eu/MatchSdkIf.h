/* 期货撮合引擎sdk接口 */
#pragma once

#include <functional>
#include <list>
#include <memory>

#include "Order.h"    // NOLINT
#include "Request.h"  // NOLINT

namespace eu_obu::yijin::MatchSdk {

// 日志回调函数，如果注册了该回调函数，本模块所有的日志信息都会使用该回调函数处理。否则将不输出日志
using LogCallback = std::function<void(int level, char* buf)>;

// taker 请求拒绝结果回调函数, app需实现处理请求拒绝结果回调函数，并在init时把回调函数传入sdk
using RejectResponseCallback = std::function<void(const order::Request& request_, order::ResponseMsgType msgType_,
                                                  uint64_t& transactTime_, const order::ErrorCode ec_)>;

// taker or maker 请求执行结果(非成交结果)回调函数, app需实现处理请求执行结果回调函数，并在init时把回调函数传入sdk
using ExecutionResponseCallback =
    std::function<void(const order::Order* order_, order::ExecType et_, uint64_t& transactTime_,
                       const order::ErrorCode ec_, const uint8_t checkType_, utility::UUID4 peerOrderId_)>;

// taker or maker 请求执行结果(非成交结果)回调函数, app需实现处理请求执行结果回调函数，并在init时把回调函数传入sdk
// 和ExecutionResponseCallback的区别，用于cancel和modify请求时执行结果，携带请求订单ID，而不是被取消订单ID
// 注意：期货的cancel携带请求订单ID，modify携带被取消订单ID
using ExecutionResponseFromReqCallback =
    std::function<void(const order::Request& request_, const order::Order* order_, order::ExecType et_,
                       uint64_t& transactTime_, const order::ErrorCode ec_)>;

// taker or maker 成交执行结果回调函数, app需实现处理请求执行结果回调函数，并在init时把回调函数传入sdk
using DealExecutionResponseCallback = std::function<void(const order::Order* order_, uint64_t& transactTime_)>;

// taker or maker 虚拟成交执行结果回调函数, app需实现处理请求执行结果回调函数，并在init时把回调函数传入sdk
using MirrorDealExecutionResponseCallback =
    std::function<void(const order::Order* order_, uint64_t& transactTime_, utility::UUID4& mExecId_, uint64_t& mUid)>;

struct InitCallback {
  InitCallback()
      : traceLog(false),
        logCb(nullptr),
        rejectResponseCb(nullptr),
        executionResponseCb(nullptr),
        executionResponseFromReqCb(nullptr),
        dealExecutionResponseCb(nullptr),
        mirrorDealExecutionResponseCb(nullptr) {}

  bool traceLog;  // 是否输出交易log。一般，主撮合为了性能，不输出log；备撮合为了跟踪排障，需要输出log；
  LogCallback logCb;
  RejectResponseCallback rejectResponseCb;
  ExecutionResponseCallback executionResponseCb;
  ExecutionResponseFromReqCallback executionResponseFromReqCb;
  DealExecutionResponseCallback dealExecutionResponseCb;
  MirrorDealExecutionResponseCallback mirrorDealExecutionResponseCb;
};

class MatchSdkIf {
 public:
  MatchSdkIf();
  virtual ~MatchSdkIf();

  /*初始化MatchSdk，并设置回调函数
   * false：失败；true：成功
   */
  bool Init(InitCallback& initCb);

  bool DoRequest(order::Request& request, uint64_t crossSeq, int64_t& timestamp);
  bool DoPassThroughRequest(order::GlobalRequest& request, uint64_t crossSeq, int64_t& timestamp);
  /* 撮合服务依赖dump文件作为高可用性策略：
   * 1、Init后调用LoadFile加载dump文件
   * 2、退出进程时先调用DumpLocalFile dump文件到本地
   * 3、定期调用DumpShareFile dump文件到共享磁盘（只有dump服务才调用）*/

  // 加载dump文件，在Init后调用
  // false，表示load失败；true，表示load成功，返回dumpfile的lastOffset
  bool LoadFile(char* filePath, uint64_t& lastOffset_);

  // 输出dump文件到本地磁盘或共享磁盘，注意：dump过程会带来时延，只有在退出进程或dump服务才调用
  bool DumpFile(char* filePath);

  void SetBeginCrossSeq(int64_t crossSeq);

  // 为quote和convert_for_broker生成dump文件
  bool DumpFileForQuote(char* filePath);
};
}  // namespace eu_obu::yijin::MatchSdk
