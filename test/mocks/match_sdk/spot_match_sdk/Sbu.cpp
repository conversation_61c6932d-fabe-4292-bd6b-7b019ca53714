/*
  用于测试封装
*/
#include <stdio.h>
#include <stdlib.h>
#include <sys/time.h>
#include <unistd.h>

#include <iostream>
#include <string>

#include "SbuCrossSdk.h"
#include "inc/Request.h"
#include "utility.h"  // NOLINT

#define POW_C1_N18 1000000000000000000ULL

std::vector<std::string> split(std::string str, std::string pattern) {
  std::string::size_type pos;
  std::vector<std::string> result;
  str += pattern;  // 扩展字符串以方便操作
  int size = str.size();
  for (int i = 0; i < size; i++) {
    pos = str.find(pattern, i);
    if (pos < size) {
      std::string s = str.substr(i, pos - i);
      result.push_back(s);
      i = pos + pattern.size() - 1;
    }
  }
  return result;
}

sbu::uint128_t string2int128(const std::string& s) {
  auto v = split(s, ".");
  if (v.size() == 1) {
    return std::stoll(v[0]) * (sbu::uint128_t)POW_C1_N18;
  } else {
    sbu::uint128_t t = 0;
    auto x = (sbu::uint128_t)std::stoll(v[0]);
    t = x * (sbu::uint128_t)POW_C1_N18 +
        std::stoll(v[1]) * (sbu::uint128_t)POW_C1_N18 / (sbu::uint128_t)(pow(10, v[1].size()));
    return t;
  }
}

int main() {
  sbu::SbuCrossSdk* sdk = new sbu::SbuCrossSdk();
  // 包头
  sbu::yijin::MatchSdk::order::Header header;
  header._magicCookie = sbu::yijin::MatchSdk::order::Header::MAGIC;
  header._hdrVersion = sbu::yijin::MatchSdk::order::Header::VERSION;
  header._hdrLength = sizeof(sbu::yijin::MatchSdk::order::Header);
  header._msgType = 0;  // 大宗使用
  header._offset = 0;
  header._length = sizeof(sbu::yijin::MatchSdk::order::Request);
  header._count = 1;
  header._symbol = 1;
  header._exchangeId = 0;
  header._startCross = 0;
  header._endCross = 0;
  header._sendingTime = 0;
  // header._orderType = 0;//透传字段

  std::string sHeader(reinterpret_cast<char*>(&header), sizeof(sbu::yijin::MatchSdk::order::Header));
  // 包体
  struct timeval tv;
  gettimeofday(&tv, nullptr);
  sbu::yijin::MatchSdk::order::Request request;
  request._magicCookie = sbu::yijin::MatchSdk::order::Request::MAGIC;
  request._msgVersion = sbu::yijin::MatchSdk::order::Request::VERSION;
  request._msgLength = sizeof(sbu::yijin::MatchSdk::order::Request);
  request._msgType = sbu::yijin::MatchSdk::order::NewOrder;
  request._smpGroup = 0;
  request._senderCompID = 123;
  request._targetCompID = 123;
  request._sendingTime = tv.tv_sec * 1000000 + tv.tv_usec;
  request._accountId = 123;
  request._brokerID = 123;
  request._bizCrossSeq = 123;
  request._orderID = 1;
  request._orderType = sbu::yijin::MatchSdk::order::OrderType::LimitOrder;
  request._side = sbu::yijin::MatchSdk::order::Side::Buy;
  request._timeInForce = sbu::yijin::MatchSdk::order::TimeInForce::GoodTillCancel;
  request._symbol = 1;
  // int128_t qty128 = string2int128(std::to_string(1 + rand() % 11));
  // int64_t qty = 1 + rand() % 11;
  int64_t qty = 10;
  sbu::uint128_t qty128 = string2int128(std::to_string(qty));
  request._qty[0] = (int64_t)(qty128 / POW_C1_N18);
  request._qty[1] = (int64_t)(qty128 % POW_C1_N18);

  sbu::uint128_t minQty = string2int128("0.1");
  request._minQty[0] = (int64_t)(minQty / POW_C1_N18);
  request._minQty[1] = (int64_t)(minQty % POW_C1_N18);

  int64_t amount = 0;
  request._amount[0] = 0;
  request._amount[1] = 0;

  int64_t minAmount = 1;
  request._minAmount[0] = 1;
  request._minAmount[1] = 0;

  int64_t price = 5000;
  sbu::uint128_t price128 = string2int128(std::to_string(price));
  request._price[0] = (int64_t)(price128 / POW_C1_N18);
  request._price[1] = (int64_t)(price128 % POW_C1_N18);

  request._reserve1 = 456;
  request._reserve2 = 456;
  request._reserve3 = 456;
  request._reserve4 = 456;
  request._reserve5 = 456;
  request._reserve6 = 456;
  std::string sRequest(reinterpret_cast<char*>(&request), sizeof(sbu::yijin::MatchSdk::order::Request));
  std::string rsp;
  // 撮合
  sdk->DoCross(0, sHeader + sRequest, rsp);
  // 解包
  sbu::yijin::MatchSdk::order::Header* head = (sbu::yijin::MatchSdk::order::Header*)rsp.data();
  // head->RspToString();
  int pos = sizeof(sbu::yijin::MatchSdk::order::Header);
  for (size_t i = 0; i < head->_count; i++) {
    sbu::Response* item = (sbu::Response*)(rsp.data() + pos);
    pos += sizeof(sbu::Response);
    pos += item->_clientOrderIDLen;
    item->toString("item+" + std::to_string(i));
  }
  delete sdk;
  printf("*****************\n");
  // case 2
  sdk = new sbu::SbuCrossSdk();
  request._orderID = 2;
  request._side = sbu::yijin::MatchSdk::order::Side::Sell;
  std::string sRequest1(reinterpret_cast<char*>(&request), sizeof(sbu::yijin::MatchSdk::order::Request));
  rsp.clear();
  sdk->DoCross(0, sHeader + sRequest1, rsp);
  // 解包
  head = (sbu::yijin::MatchSdk::order::Header*)rsp.data();
  // head->toString();
  pos = sizeof(sbu::yijin::MatchSdk::order::Header);
  for (size_t i = 0; i < head->_count; i++) {
    sbu::Response* item = (sbu::Response*)(rsp.data() + pos);
    pos += sizeof(sbu::Response);
    pos += item->_clientOrderIDLen;
    item->toString("item+" + std::to_string(i));
  }
  delete sdk;

  return 0;
}
