# Linux version

.SUFFIXES:
.SUFFIXES: .cpp .obj .o 

CPP = g++ -m64

CPPDEFS = -g -fPIC -std=c++17

LIB_DIR = -L./ \
-L./lib/linux/

LIB_DIR_MAC_ARM = -L./ \
-L./lib/mac_arm/

LIB_DIR_MAC_x64 = -L./ \
-L./lib/mac_x64/

INC_DIR = -I./ \
-I./inc/ 

#注意动态库的顺序，否则可能出现链接错误
LIBS = -ldl -lspot_match_sdk


.cpp.o:
	$(CPP) -c $< -o $@ $(INC_DIR) $(CPPDEFS)

	
OBJS =  \
Sbu.o \
SbuCrossSdk.o 



ALL : sbu_cross


sbu_cross : $(OBJS)
	$(CPP) -o $@ $(LIB_DIR) $(CPPDEFS) $(OBJS) $(LIBS)

sbu_cross_mac_arm : $(OBJS)
	$(CPP) -o $@ $(LIB_DIR_MAC_ARM) $(CPPDEFS) $(OBJS) $(LIBS)

sbu_cross_mac_x64 : $(OBJS)
	$(CPP) -o $@ $(LIB_DIR_MAC_x64) $(CPPDEFS) $(OBJS) $(LIBS)
	
clean:
	rm -f *.o
