#pragma once

#include <cstdint>
#include <string>

#include "ErrorCode.h"  // NOLINT

namespace eu_sbu::yijin {
namespace MatchSdk {
namespace order {

// msgtype在(100,200)之间的值都要补成交id
enum RequestMsgType : uint16_t {
  NewOrder = 1,
  CancelOrder = 2,
  SecurityStatusRequest = 3,  // 设置合约状态
  LoadOrderRequest = 4,  // 现货旧币灰度切换过程中，处理load旧币订单薄请求，过了灰度期，可以删除
  ModifyOrder = 5,  // 修改订单消息
  // 以下为透传消息，当HeadMsgType = PassThrough_Msg时上下游使用，撮合不使用
  RecoverPassThrough = 100,       // 透传消息，启动恢复
  BlockTradePassThrough = 101,    // 大宗交易透传消息,补execid
  TakeOverPassThrough = 102,      // 现货强平透传消息,补execid
  MaxAddExecidPassThrough = 200,  // 没有实际业务含义,标识需要补成交id的最大透传消息ID
};

enum ResponseMsgType : uint16_t {
  ExecutionReport = 1,         //
                               //  The execution report message is used to:
                               //  1. confirm the receipt of an order
                               //  2. confirm changes to an existing order
                               //  3. relay order status information
                               //  4. relay fill information on working orders
                               //  5. reject orders (35=D)
  CancelRejectResponse = 2,    //
                               //  1. reject cancel request (35=F)
  SecurityStatusResponse = 3,  // 设置合约状态应答
  ReplaceRejectResponse = 4,   // 改单拒单应答
};

enum OrderType : char {
  LimitOrder = '0',        // 限价单
  MarketBaseOrder = '2',   // 现货市价卖单
  MarketQuoteOrder = '3',  // 现货市价买单
  LimitMakerOrder = '4',
  ComOrder = '5',        // 同账号订单
  StopLimitOrder = '6',  // STOP_LIMIT
  LocalOnlyOrder = '7',  // LOCAL_ONLY
  LimitBaseOrder = '8',  // 市价单转限价单 按qty买
  LimitQuoteOrder = '9'  // 市价单转限价单 按amount卖
};

enum Side : char { Buy = '0', Sell = '1' };

// Specified how long the order remains in effect
enum TimeInForce : char {
  GoodTillCancel = '0',    // GTC
  FillOrKill = '1',        // FOK
  ImmediateOrCancel = '2'  // IOC
};

// 订单状态
enum OrderStatus : char {
  OS_New = '0',          // 未成交有效
  OS_PartialFill = '1',  // 部分成交
  OS_Fill = '2',         // 完全成交
  OS_Canceled = '4',     // 撤销
  OS_Rejected = '8'      // 拒绝
};

enum ExecType : char {
  ET_New = '0',             // 创建
  ET_Canceled = '4',        // 撤销
  ET_Rejected = '7',        // 拒绝
  ET_Trade = 'F',           // 成交
  ET_SecurityStatus = 'E',  // 处理SecurityStatusRequest结果，成功或失败
  ET_Modify = 'R'           // 变更
};

// 流动性
enum LiquidityInd : char {
  LiquidityIndNA = '0',  // 无效
  AddLiquidity = '1',    // 提供流动性，maker单提供流动性
  TakeLiquidity = '2'    // 消耗流动性，taker单消耗流动性
};

enum HeadMsgType : int16_t {
  Normal_Order = 0,      // 普通报单消息
  Taker_req = 100,       // 大宗透传成交信息
  Maker_req = 101,       // 大宗透传补单
  PassThrough_Msg = 102  // 透传消息（payload为request）
};

enum HeadOrderType : int16_t {
  E_NewOrder = 1,    // 大宗成交信息
  E_CancelOrder = 2  // 大宗补单
};

// selfTradePreventionMode
enum SmpType : uint8_t { SMPT_NONE = 0, SMPT_TAKER = 1, SMPT_MAKER = 2, SMPT_BOTH = 3 };
// 用于modify response，是否保留原订单
enum KeepPriority : char { Non_Keep = 0, Keep = 1 };

enum UserType : char {
  LpUser = 'L',       // Liquidity provider
  CustomerUser = 'C'  // Customer
};

// response
enum OrderSubmitType : char {
  RealOrder = 'R',   // real order
  MirrorOrder = 'M'  // mirror order，下游仅需要关注成交本身。订单可忽略
};

const std::string& toString(OrderType orderType_) noexcept;
const std::string& toString(Side side_) noexcept;
const std::string& toString(TimeInForce timeInForce_) noexcept;
const std::string& toString(OrderStatus orderStatus_) noexcept;
const std::string& toString(ExecType execType_) noexcept;
const std::string& toString(LiquidityInd liquidityInd_) noexcept;
const std::string& toString(RequestMsgType msgType_) noexcept;
const std::string& toString(ResponseMsgType msgType_) noexcept;
const std::string& toString(SmpType smpType_) noexcept;
const std::string& toString(UserType userType_) noexcept;
const std::string& toString(OrderSubmitType submitType_) noexcept;
}  // namespace order
}  // namespace MatchSdk
}  // namespace eu_sbu::yijin
