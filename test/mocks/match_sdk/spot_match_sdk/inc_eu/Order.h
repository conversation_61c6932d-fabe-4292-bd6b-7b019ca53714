#pragma once
#include <memory>
#include <string>
#include <unordered_map>

#include "ComDef.h"   // NOLINT
#include "utility.h"  // NOLINT

namespace eu_sbu::yijin {
namespace MatchSdk {
namespace order {

class PriceNode;
class Request;
class RequestForLoad;

class Order {
 public:
  Order() noexcept { reset(); }
  // 16字节对齐
  int64_t _orderID;       // orderId of request
  int64_t _offset;        // Kafka offset of the request
  int64_t _senderCompID;  // UNIQUE User ID in USER/ACCOUNT DB
  int64_t _targetCompID;
  int64_t _execID;
  int64_t _transactTime;
  int64_t _sendingTime;     // 透传字段
  int64_t _accountId;       // 透传字段
  int64_t _brokerID;        // 劵商ID，透传字段
  int64_t _bizCrossSeq;     // 业务trading的request序列号，透传字段
  int64_t _makerFeeRate;    // 透传字段:预收手续费费率(maker)
  int64_t _takerFeeRate;    // 透传字段:预收手续费费率(taker)
  int64_t _makerBonusRate;  // 透传字段:maker返佣比率
  int32_t _symbol;
  OrderType _orderType;
  Side _side;
  TimeInForce _timeInForce;
  LiquidityInd _lastLiquidityInd;
  OrderStatus _orderStatus;
  int8_t _limitPriceRatio;  // 风控限价比例
  UserType _userType;
  SmpType _smpType;
  int32_t _smpGroup;
  int64_t _mirrorOrdID;  // mirror orderId of request，业务镜像订单ID
  int64_t _mirrorUID;
  int8_t _forbidMatchRpi;  // match with rpi;
  uint8_t _siteId;         // 站点ID
  char _reserve[6];        // 预留16字节

  int64_t _reserve1;  // 预留透传字段
  int64_t _reserve2;  // 预留透传字段
  int32_t _reserve3;  // 预留透传字段
  int32_t _reserve4;  // 预留透传字段
  int32_t _reserve5;  // 预留透传字段
  int32_t _reserve6;  // 预留透传字段

  // 为了方便dump_quote，将128字段放在最后
  uint128_t _qty;           // orderQty
  uint128_t _price;         // price
  uint128_t _lastQty;       // 最新成交量
  uint128_t _minQty;        // 平台要求的最小成交量
  uint128_t _leavesQty;     // 剩余订单量
  uint128_t _lastPx;        // 最新成交价
  uint128_t _lastAmount;    // 最新成交金额
  uint128_t _leavesAmount;  // 剩余订单金额
  uint128_t _minAmount;     // 平台要求的最小订单金额
  uint128_t _amount;        // 订单金额

  /** ANYTHING AFTER THIS CANNOT BE SAVED TO AND LOADED FROM FILE */
  /*  _prevOrder is the first, donot insert anything in front of _prevOrder */

  Order* _prevOrder;      // 在priceNode里的上一跳订单
  Order* _nextOrder;      // 在priceNode里的下一跳订单
  PriceNode* _priceNode;  // 本订单所在的PriceNode

  void reset() noexcept;
  bool IsOrderQtyAmountAvailable();
  bool IsOrderQtyAvailable();
  bool IsOrderAmountAvailable();
  bool IsOrderMultiplyAmountAvailable();
  void UpdateMatchOrder(uint128_t matchedQty_, uint128_t& matchedPrice_, uint128_t matchedAmount_, LiquidityInd liqInd_,
                        int64_t& execID_, int64_t& transactTime_);
  void UpdateMakerMatchOrder(uint128_t matchedQty_, uint128_t& matchedPrice_, uint128_t matchedAmount_,
                             LiquidityInd liqInd_, int64_t& execID_, int64_t& transactTime_);
  std::string toString() const noexcept;
  void InitOrderFromRequest(Request& request_, int64_t offset_, int64_t& lastTranscTime_) noexcept;
  bool RiskLimitPrice(const uint128_t& lastMatchPrice, const uint128_t& makerPrice);
  ErrorCode MarketLimitPrice(const uint128_t& lastMatchPrice, const uint128_t& makerPrice);
  SmpType GetSmpType(int32_t& makerSmpGroup_, int64_t& makerUid_);
  bool IsOrderStatusFill() { return _orderStatus == OS_Fill; }
};
// static_assert(sizeof(Order) == 320);
using OrdersMap = std::unordered_map<int64_t, order::Order*>;  // <orderId, order>
}  // namespace order
}  // namespace MatchSdk
}  // namespace eu_sbu::yijin
