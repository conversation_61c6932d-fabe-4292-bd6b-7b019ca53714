/* 期货撮合引擎sdk接口 */
#pragma once

#include <functional>
#include <list>
#include <memory>

#include "Order.h"    // NOLINT
#include "Request.h"  // NOLINT

namespace eu_sbu::yijin {
namespace MatchSdk {

// 日志回调函数，如果注册了该回调函数，本模块所有的日志信息都会使用该回调函数处理。若未设置，使用sdk默认日志输出
using LogCallback = std::function<void(int level, char* buf)>;

// taker 请求拒绝结果回调函数, app需实现处理请求拒绝结果回调函数，并在init时把回调函数传入sdk
using RejectResponseCallback = std::function<void(const order::Request& request_, order::ResponseMsgType msgType_,
                                                  int64_t& transactTime_, const order::ErrorCode ec_)>;

// taker or maker 请求执行结果(非成交结果)回调函数, app需实现处理请求执行结果回调函数，并在init时把回调函数传入sdk
using ExecutionResponseCallback =
    std::function<void(const order::Order* order_, order::ExecType et_, int64_t& transactTime_,
                       const order::ErrorCode ec_, int64_t peerOrderId_, const uint8_t checkType_)>;

// taker or maker 请求执行结果(非成交结果)回调函数, app需实现处理请求执行结果回调函数，并在init时把回调函数传入sdk
// 和ExecutionResponseCallback的区别，用于cancel和modify请求时执行结果，携带请求订单ID，而不是被取消订单ID
using ExecutionResponseFromReqCallback =
    std::function<void(const order::Request& request_, const order::Order* order_, order::ExecType et_,
                       int64_t& transactTime_, const order::ErrorCode ec_)>;

// taker or maker 成交执行结果回调函数, app需实现处理请求执行结果回调函数，并在init时把回调函数传入sdk
using DealExecutionResponseCallback = std::function<void(const order::Order* order_, int64_t& transactTime_)>;

// taker or maker 虚拟成交执行结果回调函数, app需实现处理请求执行结果回调函数，并在init时把回调函数传入sdk
using MirrorDealExecutionResponseCallback = std::function<void(const order::Order* order_, int64_t& transactTime_,
                                                               int64_t& mExecId_, int64_t& mUid, int64_t& mAid)>;

// taker 成交执行结果回调函数, app需实现处理请求执行结果回调函数，并在init时把回调函数传入sdk
using UpdateDealExecutionResponseCallback = std::function<bool(const order::Order* order_)>;

struct InitCallback {
  InitCallback()
      : traceLog(false),
        logCb(nullptr),
        rejectResponseCb(nullptr),
        executionResponseCb(nullptr),
        executionResponseFromReqCb(nullptr),
        dealExecutionResponseCb(nullptr),
        updateDealExecutionResponseCb(nullptr),
        mirrorDealExecutionResponseCb(nullptr) {}

  bool traceLog;  // 是否输出交易log。一般，主撮合为了性能，不输出log；备撮合为了跟踪排障，需要输出log；
  int64_t execIDBase;
  LogCallback logCb;
  RejectResponseCallback rejectResponseCb;
  ExecutionResponseCallback executionResponseCb;
  ExecutionResponseFromReqCallback executionResponseFromReqCb;
  DealExecutionResponseCallback dealExecutionResponseCb;
  UpdateDealExecutionResponseCallback updateDealExecutionResponseCb;
  MirrorDealExecutionResponseCallback mirrorDealExecutionResponseCb;
};

class MatchSdkIf {
 public:
  MatchSdkIf();
  virtual ~MatchSdkIf();

  /*初始化MatchSdk，并设置回调函数
   * false：失败；true：成功
   */
  bool Init(InitCallback& initCb);

  /*处理request消息，返回responses
   * 输入：request;crossSeq;passThrough-是否透传；timestamp-发送时间戳，用来修正撮合处理时间戳
   * 输出：responses-response list
   * 调用DoRequest，app需完成：
   *    1、外层消息接收；
   *    2、外层消息头校验；
   *    3、解析出request；
   *    4、若passThrough，需在调用DoRequest后再将request透传，因为DoRequest可能会对request进行处理；
   *    5、主撮合服务和行情撮合服务需将返回的responses发送出去；
   * false：失败；true：成功
   */
  bool DoRequest(order::Request& request, int64_t crossSeq, int64_t& timestamp);
  bool DoPassThroughRequest(order::GlobalRequest& request, uint64_t crossSeq, int64_t& timestamp);
  /*撮合服务依赖dump文件作为高可用性策略：
   * 1、Init后调用LoadFile加载dump文件
   * 2、退出进程时先调用DumpLocalFile dump文件到本地
   * 3、定期调用DumpShareFile dump文件到共享磁盘（只有dump服务才调用）*/

  // 加载dump文件，在Init后调用
  // false，表示load失败；true，表示load成功，返回dumpfile的lastOffset
  bool LoadFile(char* filePath, int64_t& lastOffset_);

  // 输出dump文件到本地磁盘或共享磁盘，注意：dump过程会带来时延，只有在退出进程或dump服务才调用
  bool DumpFile(char* filePath);

  // 为quote和convert_for_broker生成dump文件
  bool DumpFileForQuote(char* filePath);

  /*
  函    数： DoPassthoughRequest
  说    明： 透传请求消息处理
  */
  bool DoPassthoughReq(order::Header& header, int64_t crossSeq, int64_t& timestamp);

  void SetBeginCrossSeq(int64_t crossSeq);
};
}  // namespace MatchSdk
}  // namespace eu_sbu::yijin
