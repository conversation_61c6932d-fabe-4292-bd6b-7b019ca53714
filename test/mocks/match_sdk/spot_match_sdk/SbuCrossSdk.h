/* 期货撮合引擎sdk接口 */
#pragma once

#include <functional>
#include <list>
#include <memory>
#include <string>

#include "SbuResponse.h"
#include "SbuRspCache.h"
#include "inc/Header.h"
#include "spot_match_sdk/inc/MatchSdkIf.h"
#include "spot_match_sdk/inc_eu/MatchSdkIf.h"

namespace sbu {

#define CV128TOARR2(arr2, v128)                                                          \
  arr2[0] = (uint64_t)(v128 / (boost::multiprecision::uint128_t)1000000000000000000ull); \
  arr2[1] = (uint64_t)(v128 % (boost::multiprecision::uint128_t)1000000000000000000ull)

class SbuCrossSdk {
 public:  // 给外部调用的接口
  explicit SbuCrossSdk(uint64_t base_id = 100);
  ~SbuCrossSdk();
  // req: 80字节header + 480字节req.body
  // rsp: 80字节header + 动态长度rsp.body1 + 动态长度rsp.body2 + ... + 动态长度rsp.bodyN
  bool DoCross(uint64_t cross_seq, std::string req, std::string& rsp);

 private:  // 内部接口
  void logFunc(int level, char* buf);
  // 主站回调函数
  void rejectResponseFunc(const yijin::MatchSdk::order::Request& request_,
                          yijin::MatchSdk::order::ResponseMsgType msgType_, int64_t& transactTime_,
                          const yijin::MatchSdk::order::ErrorCode ec_);
  // 主动撤单或修改失败时回调的函数
  void executionResponseFunc(const yijin::MatchSdk::order::Order* order_, yijin::MatchSdk::order::ExecType et_,
                             int64_t& transactTime_, const yijin::MatchSdk::order::ErrorCode ec_, int64_t peerOrderId_,
                             const uint8_t checkType_);
  void dealExecutionResponseFunc(const yijin::MatchSdk::order::Order* order_, int64_t& transactTime_);
  // 主动撤单或修改成功时回调的函数
  void executionResponseFromReq(const yijin::MatchSdk::order::Request& request_,
                                const yijin::MatchSdk::order::Order* order_, yijin::MatchSdk::order::ExecType et_,
                                int64_t& transactTime_, const yijin::MatchSdk::order::ErrorCode ec_,
                                const uint8_t checkType_);
  bool UpdateDealExecutionResponseFunc(const yijin::MatchSdk::order::Order* order_);
  // 欧洲站撮合回调
  void rejectResponseFunc_eu(const eu_sbu::yijin::MatchSdk::order::Request& request_,
                             eu_sbu::yijin::MatchSdk::order::ResponseMsgType msgType_, int64_t& transactTime_,
                             const eu_sbu::yijin::MatchSdk::order::ErrorCode ec_);
  // 主动撤单或修改失败时回调的函数
  void executionResponseFunc_eu(const eu_sbu::yijin::MatchSdk::order::Order* order_,
                                eu_sbu::yijin::MatchSdk::order::ExecType et_, int64_t& transactTime_,
                                const eu_sbu::yijin::MatchSdk::order::ErrorCode ec_, int64_t peerOrderId_,
                                const uint8_t checkType_);

  // 主动撤单或修改成功时回调的函数
  void executionResponseFromReq_eu(const eu_sbu::yijin::MatchSdk::order::Request& request_,
                                   const eu_sbu::yijin::MatchSdk::order::Order* order_,
                                   eu_sbu::yijin::MatchSdk::order::ExecType et_, int64_t& transactTime_,
                                   const eu_sbu::yijin::MatchSdk::order::ErrorCode ec_);
  void dealExecutionResponseFunc_eu(const eu_sbu::yijin::MatchSdk::order::Order* order_, int64_t& transactTime_);
  void mirrorDealExecutionResponseFunc_eu(const eu_sbu::yijin::MatchSdk::order::Order* order_, int64_t& transactTime_,
                                          int64_t& mExecId_, int64_t& mUid, int64_t& mAid);
  bool UpdateDealExecutionResponseFunc_eu(const eu_sbu::yijin::MatchSdk::order::Order* order_);

 private:
  yijin::MatchSdk::MatchSdkIf matcher_;
  yijin::MatchSdk::InitCallback callback_;
  RspCache rspCache_;
  uint64_t last_offset_ = 0;
  uint64_t begin_time_ = 0;
  uint64_t end_time_ = 0;
  // 欧洲站
  eu_sbu::yijin::MatchSdk::MatchSdkIf matcher_eu_;
  eu_sbu::yijin::MatchSdk::InitCallback callback_eu_;
};

}  // namespace sbu
