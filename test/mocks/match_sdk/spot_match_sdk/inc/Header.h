#ifndef TEST_MOCKS_MATCH_SDK_SPOT_MATCH_SDK_INC_HEADER_H_
#define TEST_MOCKS_MATCH_SDK_SPOT_MATCH_SDK_INC_HEADER_H_

#include <cstdint>
#include <string>

#include "ComDef.h"  // NOLINT

namespace sbu::yijin {
namespace MatchSdk {
namespace order {
class Header {
 public:
  static constexpr int64_t MAGIC = 0x0EFF1357EEFF1357;
  static constexpr int32_t VERSION = 0x0F010100;
  // 构造方法

  Header() : _startCross(0), _endCross(0) {}
  static Header createMsg(int64_t exchangeID_, int64_t length_, uint32_t count_, int64_t offset_ = 0x7FFFFFFFFFFFFFFF,
                          bool passThrough_ = false);
  static Header createDumpStateReq();
  static bool validate(const Header& hdr_) {
    return hdr_._magicCookie == MAGIC && hdr_._hdrVersion == VERSION && hdr_._hdrLength == sizeof(Header);
  }
  const std::string reqToString() const noexcept;
  const std::string rspToString() const noexcept;

 public:
  int64_t _magicCookie = MAGIC;
  int32_t _hdrVersion = VERSION;
  int16_t _hdrLength = sizeof(Header);  // head自身的长度
  int16_t _msgType;  // 0x0001:标识place消息，旧币迁移到新撮合从数据库load订单使用该消息
  int64_t _offset;  // 在请求里此字段传零即可，不使用；在应答里是把本次请求的kafka时topic的offset
  int64_t _length;  // head后面紧跟的多个response总长度，不包含head长度；_length=sizeof(Response)*_count
  int32_t _count;   // head后面紧跟的多个response的个数
  int32_t _symbol;           // 透传字段
  int64_t _exchangeId;       // 透传字段，目前trading上送的都是同一个值
  int64_t _startCross;       // 撮合开始处理req时间戳
  int64_t _endCross;         // 撮合发送response时间戳
  int64_t _sendingTime;      // 透传字段，请求发送时间，毫秒
  HeadOrderType _orderType;  // 透传字段:订单类型
  int16_t _reserve1;         // 透传字段:预留
  int32_t _reserve2;         // 透传字段:预留
};

// header size is 80
static_assert(sizeof(Header) == 80);

}  // namespace order
}  // namespace MatchSdk
}  // namespace sbu::yijin
#endif  // TEST_MOCKS_MATCH_SDK_SPOT_MATCH_SDK_INC_HEADER_H_
