#pragma once
#include <math.h>

#include <boost/multiprecision/cpp_int.hpp>
#include <sstream>
#include <string>
#include <utility>

namespace sbu {

typedef boost::multiprecision::uint256_t uint256_t;
typedef boost::multiprecision::uint128_t uint128_t;

namespace yijin {
namespace MatchSdk {
namespace utility {

// 1000000000000000000ull
static const int64_t basePrecision = (int64_t)pow(10, 18);  // 需要显式将double转化成整型，否则会损失精度
static const uint128_t maxPrecision = (uint128_t)basePrecision * basePrecision;

// 入参in必须为int64_t[2]数组
inline uint128_t GenUint128(int64_t* in) {
  uint128_t value = (uint128_t)(*in) * basePrecision + *(in + 1);

  return value;
}

inline void SplitUint128(uint128_t& in, int64_t& out0, int64_t& out1) {
  out0 = int64_t(in / basePrecision);
  out1 = int64_t(in % basePrecision);
}

inline void SplitUint128N(uint128_t in, int64_t& out0, int64_t& out1) {
  out0 = int64_t(in / basePrecision);
  out1 = int64_t(in % basePrecision);
}

// 入参value必须为int64_t[2]数组
inline bool ValueValid(int64_t* value) {  // 0=<_price/_amount/_qty<10^18
  return *value >= 0 && *value < basePrecision && *(value + 1) >= 0 && *(value + 1) < basePrecision;
}

// 价*量得到的金额（18位精度）必须小于max of uint128
inline bool AmountValid(int64_t* qty_, int64_t* price_) {
  uint128_t qty = GenUint128(qty_);
  uint128_t price = GenUint128(price_);

  uint256_t result = (uint256_t)qty * price / basePrecision;
  return result < maxPrecision;
}

// 转换18*18乘积的精度到18，直接截断，类似BigDecimal.ROUND_DOWN，会有精度损失
inline uint128_t MultiplyConvertPrecision(uint128_t& in1, uint128_t& in2) {
  uint256_t result = (uint256_t)in1 * in2;
  return uint128_t(result / basePrecision);
}

// 转换18*18乘积的精度到18，直接截断，类似BigDecimal.ROUND_DOWN，会有精度损失
inline uint256_t MultiplyConvertPrecision256(uint128_t& in1, uint128_t& in2) {
  uint256_t result = (uint256_t)in1 * in2;
  return result / basePrecision;
}

// 得到18精度的除数
inline uint128_t DivideConvertPrecision(uint128_t& divisor, uint128_t& Dividend) {
  uint256_t result = (uint256_t)divisor * basePrecision;
  return uint128_t(result / Dividend);
}

// 先得到18精度的除数，再将除数按最小精度ROUND_DOWN
inline uint128_t DivideConvertMinPrecision(uint128_t& divisor, uint128_t& Dividend, uint128_t& minPrecision) {
  uint128_t result = DivideConvertPrecision(divisor, Dividend);

  return uint128_t(result / minPrecision * minPrecision);
}

inline bool CompareUpPriceRatio(const uint128_t& makerPrice, const uint128_t& lastMatchPrice, int8_t priceRatio) {
  uint256_t makerPrice256 = (uint256_t)makerPrice * 100;
  uint256_t limitPrice256 = (uint256_t)lastMatchPrice * (100 + priceRatio);
  return (makerPrice256 > limitPrice256);
}

inline bool CompareDownPriceRatio(const uint128_t& makerPrice, const uint128_t& lastMatchPrice, int8_t priceRatio) {
  uint256_t makerPrice256 = (uint256_t)makerPrice * 100;
  uint256_t limitPrice256 = (uint256_t)lastMatchPrice * (100 - priceRatio);
  return (makerPrice256 < limitPrice256);
}

inline std::string Uint128ToString(uint128_t& in) { return in.str(); }

inline std::string Uint128ToStringN(uint128_t in) { return in.str(); }

}  // namespace utility
}  // namespace MatchSdk
}  // namespace yijin
}  // namespace sbu
