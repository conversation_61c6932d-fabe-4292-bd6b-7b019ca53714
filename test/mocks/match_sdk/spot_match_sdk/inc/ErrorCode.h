#ifndef TEST_MOCKS_MATCH_SDK_SPOT_MATCH_SDK_INC_ERRORCODE_H_
#define TEST_MOCKS_MATCH_SDK_SPOT_MATCH_SDK_INC_ERRORCODE_H_

#include <cstdint>
#include <string>

namespace sbu::yijin {
namespace MatchSdk {
namespace order {

enum ErrorCode : uint16_t {
  // add more
  EC_NoError = 0,
  EC_SystemErr,
  EC_MissingClOrdID,                // 未携带订单id
  EC_DuplicatedClOrdID,             // 重复的订单id
  EC_OrderNotExist,                 // 找不到请求的订单
  EC_UnknownMessageType,            // 5
  EC_UnknownOrderType,              // 非法订单类型
  EC_InvalidSide,                   // 非法side
  EC_UnknownTimeInForce,            // 非法TimeInForce
  EC_WronglyRouted,                 // symbolID不匹配
  EC_InvalidPrice,                  // 10 价格非法
  EC_NoEnoughQtyToFill,             // FOK单，对手盘没有足够的量或金额导致撤单
  EC_NoImmediateQtyToFill,          // IOC单，对手盘没有足够的量导致未成交的单被撤单
  EC_CancelRequest,                 // 因为撤销订单请求而撤销原订单
  EC_LimitMakerWillTakeLiquidity,   // LimitMaker单被撤单因为将会消耗流动性
  EC_InvalidSymbolStatus,           // 15 合约交易状态异常
  EC_InvalidQty,                    // 订单量非法
  EC_InvalidAmount,                 // 订单金额非法
  EC_MarketOrderNoSuppTimeInForce,  // 市价单不支持该TimeInForce类型
  EC_LoadOrderCancel,               // load订单导致的cancel maker单
  EC_CancelForNoFullFill,           // 20 未完全成交的单被撤单
  EC_MarketQuoteNoSuppSell,         // MarketQuoteOrder不支持Sell单
  EC_DisorderOrderID,               // 乱序订单，比如cancel单比new单先到
  EC_InvalidBaseValue,              // qty/price/amout超过最大精度限制，或minQty/minAmout<=0
  EC_LoadOrderCanMatch,             // load订单可以成交，错误
  EC_SecurityStatusFail,            // 25 SecurityStatus设置交易对状态失败
  EC_ReachMaxTradeNum,              // 26 达到最大吃单笔数或者最大价格档数量限制而撤单
  EC_ReachMarketPriceLimit,         // 27 达到统保市价单破产价限价
  EC_BySelfMatch = 28,              // 28 触发stp被撤单
  EC_InvalidSmpType,                // 29 非法smpType
  EC_ReachRiskPriceLimit,           // 30 达到市价单风控限价
  EC_CancelReplaceOrder = 31,       // 31 因为改单导致的撤原订单
  EC_CancelByOrderValueZero = 32,   // 32 因为订单价值为0导致的撤原订单
  EC_CancelByMatchValueZero = 33,   // 33 因为成交价值为0导致的撤原订单
  EC_Max                            // 最大的错误值
};

const std::string& toString(ErrorCode ec_) noexcept;

}  // namespace order
}  // namespace MatchSdk
}  // namespace sbu::yijin

#endif  // TEST_MOCKS_MATCH_SDK_SPOT_MATCH_SDK_INC_ERRORCODE_H_
