/* 期货撮合引擎sdk接口 */
#pragma once

#include <functional>
#include <list>
#include <memory>
#include <string>

#include "FuturesSpreadReqHeader.h"
#include "FuturesSpreadResponse.h"
#include "FuturesSpreadRspCache.h"
#include "FuturesSpreadRspHeader.h"
#include "inc/MatchSdkIf.h"
#include "inc/Order.h"
#include "inc/Request.h"

namespace futures_spread {
static constexpr uint32_t CHECKSUM_CONST = 0xFF000100;
class FuturesSpreadCrossSdk {
 public:  // 给外部调用的接口
  FuturesSpreadCrossSdk();
  ~FuturesSpreadCrossSdk();
  // req: 80字节header + 480字节req.body
  // rsp: 80字节header + 动态长度rsp.body1 + 动态长度rsp.body2 + ... + 动态长度rsp.bodyN
  bool DoCross(uint64_t cross_seq, std::string req, std::string& rsp);

 private:  // 内部接口
  void logFunc(int level, char* buf);
  void rejectResponseFunc(const yijin::MatchSdk::order::Request& request_,
                          yijin::MatchSdk::order::ResponseMsgType msgType_, uint64_t& transactTime_,
                          const yijin::MatchSdk::order::ErrorCode ec_);
  void executionResponseFunc(const yijin::MatchSdk::order::Order* order_, yijin::MatchSdk::order::ExecType et_,
                             uint64_t& transactTime_, const yijin::MatchSdk::order::ErrorCode ec_,
                             const uint8_t checkType_);
  void dealExecutionResponseFunc(const yijin::MatchSdk::order::Order* order_, uint64_t& transactTime_);
  void executionResponseFromReq(const yijin::MatchSdk::order::Request& request_,
                                const yijin::MatchSdk::order::Order* order_, yijin::MatchSdk::order::ExecType et_,
                                uint64_t& transactTime_, const yijin::MatchSdk::order::ErrorCode ec_,
                                const uint8_t checkType_);

 private:
  yijin::MatchSdk::MatchSdkIf matcher_;
  yijin::MatchSdk::InitCallback callback_;
  RspCache rspCache_;
  uint64_t last_offset_ = 0;
  uint64_t begin_time_ = 0;
  uint64_t end_time_ = 0;
};

}  // namespace futures_spread
