#pragma once

#include <cstdint>
#include <iostream>
#include <string>

namespace futures_spread {
class Header {
 public:
  static constexpr uint64_t MAGIC = 0xEEFF1357EEFF1357;
  static constexpr uint32_t VERSION = 0xFF000100;
  // 类型定义
  enum ActionCode : uint16_t {
    Msg = 0,           // this is a new message
    DumpStateReq = 1,  // request the processor to save its states to a file
    NumOfActions = 2   // MUST BE ALWAYS THE LAST
  };
  // 方法
  Header() {}
  explicit Header(ActionCode a_) noexcept : _action(a_) {}
  static bool validate(const Header& hdr_) {
    return hdr_._magicCookie == MAGIC && hdr_._hdrVersion == VERSION && hdr_._hdrLength == sizeof(Header) &&
           static_cast<uint16_t>(hdr_._action) < static_cast<uint16_t>(NumOfActions);
  }
  void toString() const {
    std::cout << std::hex << "_magicCookie=0x" << _magicCookie << ";_hdrVersion=0x" << _hdrVersion << std::dec
              << ";_hdrLength=" << _hdrLength << ";_action=" << _action << ";_offset=" << _offset
              << ";_length=" << _length << ";_count=" << _count << ";_passThrough=" << _passThrough << std::endl;
  }

 public:
  uint64_t _magicCookie = MAGIC;
  uint32_t _hdrVersion = VERSION;
  uint16_t _hdrLength = sizeof(Header);  // size of MessageHeader
  ActionCode _action;                    // what is this message?
  uint64_t _offset;                      // copy kafka message's offset to here
  uint64_t _length;                      // overall size of all business related messages in this package
  uint32_t _count;                       // number of actual business related messages in this package
  bool _passThrough;                     // upstream just wants to pass through us with an offset attached in this hop
  char _padding[3];
  // uint64_t _startCross;  // 撮合开始处理req时间戳
  // uint64_t _endCross;    // 撮合发送response时间戳
};

static_assert(sizeof(Header) == 40);

}  // namespace futures_spread
