#pragma once
#include <string>
#ifdef __APPLE__
#else
#ifdef BOOST_UUID_NO_SIMD
#error "we don't expect to not use simd"
#endif

#ifndef BOOST_UUID_USE_SSE2
#define BOOST_UUID_USE_SSE2
#endif
#endif
#ifdef BOOST_UUID_USE_SSE3
#error "we don't expect to use sse3"
#endif

#ifdef BOOST_UUID_USE_SSE41

#error "we don't expect to use sse41"
#endif

#include <boost/uuid/name_generator.hpp>
#include <boost/uuid/random_generator.hpp>
#include <boost/uuid/uuid.hpp>
#include <boost/uuid/uuid_io.hpp>

namespace futures_spread::yijin {
namespace MatchSdk {
namespace utility {

using UUID4 = boost::uuids::uuid;

inline UUID4 uuid4() {
  static thread_local boost::uuids::random_generator uuid4gen;
#if NDEBUG
  return uuid4gen();
#else
  auto uid = uuid4gen();
  assert(uid.version() == boost::uuids::uuid::version_random_number_based);
  return uid;
#endif
}

// no new type, continue to use UUID4
// using UUID5 = boost::uuids::uuid;

inline UUID4 uuid5(void const* buffer, std::size_t byte_count) {
  static thread_local boost::uuids::name_generator_sha1 uuid5gen(boost::uuids::ns::url());
#if NDEBUG
  return uuid5gen(buffer, byte_count);
#else
  auto uid = uuid5gen(buffer, byte_count);
  assert(uid.version() == boost::uuids::uuid::version_name_based_sha1);
  return uid;
#endif
}

inline std::string toString(const UUID4& uuid4_) { return boost::uuids::to_string(uuid4_); }

}  // namespace utility
}  // namespace MatchSdk
}  // namespace futures_spread::yijin
