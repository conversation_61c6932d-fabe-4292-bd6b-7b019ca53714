#ifndef TEST_MOCKS_HDTS_TOPIC_HPP_
#define TEST_MOCKS_HDTS_TOPIC_HPP_

#include <idtssdk.h>

#include <bbase/common/blogger/blogger.hpp>
#include <deque>
#include <map>
#include <memory>
#include <mutex>
#include <string>

#include "dtssdk.hpp"   // NOLINT
#include "message.hpp"  // NOLINT

namespace mock {

class HdtsTopic {
 public:
  HdtsTopic() : message_offset_(0) {}

 public:
  void PushMessage(const std::shared_ptr<dtssdk::Message>& message_p) {
    std::lock_guard<std::mutex> guard(mutex_);

    auto mockMsg = std::dynamic_pointer_cast<HdtsMessage>(message_p);
    mockMsg->offset_ = message_offset_.fetch_add(1);

    message_[mockMsg->offset_] = mockMsg;
  }

  std::shared_ptr<dtssdk::Message> PopMessage() {
    std::lock_guard<std::mutex> guard(mutex_);

    if (message_.size() == 0) {
      return nullptr;
    } else {
      auto msg_iter = message_.begin();
      auto msg = msg_iter->second;
      message_.erase(msg_iter);

      return msg;
    }
  }

  std::int64_t GetMessageFromOffset(std::int64_t offset_p, std::shared_ptr<dtssdk::Message>& message_p) {
    std::lock_guard<std::mutex> guard(mutex_);

    message_p = nullptr;

    // 如果offset_p < 0, message返回nullptr, offset返回message_offset_
    if (offset_p < 0) {
      return message_offset_;
    }

    // 如果offset_p >= message_offset_, message返回nullptr, offset返回offset_p
    if (offset_p >= message_offset_) {
      return offset_p;
    }

    // 如果offset_p < message_offset_, message返回找到的值, offset返回offset_p + 1
    auto message_iter = message_.find(offset_p);

    if (message_iter != message_.end()) {
      message_p = message_iter->second;
    }

    return offset_p + 1;
  }
  std::int64_t MinOffset() {
    if (message_.size() <= 0) {
      return -1;
    }

    return message_.begin()->second->Offset();
  }
  std::int64_t MaxOffset() {
    if (message_.size() <= 0) {
      return -1;
    }

    return message_.rbegin()->second->Offset();
  }

 private:
  std::mutex mutex_;
  std::map<std::int64_t, std::shared_ptr<dtssdk::Message>> message_;
  std::atomic<std::int64_t> message_offset_;
};

class HdtsTopicStorage {
 public:
  static HdtsTopicStorage& Instance() {
    static HdtsTopicStorage instance;

    return instance;
  }
  // 清理干净所有的数据
  void Destroy() {
    std::lock_guard<std::mutex> guard(mutex_);

    topic_.clear();
  }

  HdtsTopic* GetTopic(const std::string& topic_name_p) {
    std::lock_guard<std::mutex> guard(mutex_);

    auto it = topic_.find(topic_name_p);

    if (it != topic_.end()) {
      return it->second.get();
    }

    return nullptr;
  }

  HdtsTopic* GetAndNewTopicIfNotExist(const std::string& topic_name_p) {
    std::lock_guard<std::mutex> guard(mutex_);

    auto it = topic_.find(topic_name_p);

    if (it != topic_.end()) {
      return it->second.get();
    }

    auto mt = std::make_shared<HdtsTopic>();
    topic_[topic_name_p] = mt;

    return mt.get();
  }

 private:
  HdtsTopicStorage() {
    std::lock_guard<std::mutex> guard(mutex_);

    std::string log_content = fmt::format("topic manager construct");

    if (log_callback != nullptr) {
      log_callback->OnLogCallback(yijin::log::FATAL, log_content);
    } else {
      LOG_INFO(log_content);
    }
  }
  ~HdtsTopicStorage() = default;

 public:
  std::mutex mutex_;
  std::map<std::string, std::shared_ptr<HdtsTopic>> topic_;
};
}  // namespace mock

#endif  // TEST_MOCKS_HDTS_TOPIC_HPP_
