#ifndef TEST_MOCKS_HDTS_HEADERS_HPP_
#define TEST_MOCKS_HDTS_HEADERS_HPP_

#include <idtssdk.h>
#include <string.h>

#include <bbase/common/utils/crypto.hpp>
#include <memory>
#include <sstream>
#include <string>
#include <vector>

namespace mock {

class HdtsHeaders : public dtssdk::Headers {
 public:
  HdtsHeaders() {}
  ~HdtsHeaders() override {}

 public:
  bool Add(const std::string& key_p, const std::string& value_p) override {
    for (auto iter : headers_) {
      if (iter.key == key_p) {
        iter.value = value_p;

        return true;
      }
    }

    dtssdk::Header head(key_p, value_p);
    headers_.push_back(head);

    return true;
  }

  std::vector<dtssdk::Header>& Get() override { return headers_; }

  void* Cptr() override {
    if (headers_.size() == 0) {
      return nullptr;
    }

    // serialize
    serialized_.clear();
    if (serialized_.size() == 0) {
      for (auto iter : headers_) {
        int32_t len = iter.key.size();
        std::string str;
        str.resize(sizeof(int32_t));
        memcpy(str.data(), &len, sizeof(int32_t));
        serialized_ += str;
        serialized_ += iter.key;

        len = iter.value.size();
        str.clear();
        str.resize(sizeof(int32_t));
        memcpy(str.data(), &len, sizeof(int32_t));
        serialized_ += str;
        serialized_ += iter.value;
      }
    }
    return const_cast<char*>(serialized_.c_str());
  }

  std::int32_t Size() override {
    Cptr();
    return serialized_.size();
  }

  void Destroy() override { delete this; }

 private:
  std::string serialized_;
  std::vector<dtssdk::Header> headers_;
};
}  // namespace mock

#endif  // TEST_MOCKS_HDTS_HEADERS_HPP_
