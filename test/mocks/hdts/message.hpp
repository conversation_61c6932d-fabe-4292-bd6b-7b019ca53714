//
// Created by SG00174ML on 6/2/23.
//

#ifndef TEST_MOCKS_HDTS_MESSAGE_HPP_
#define TEST_MOCKS_HDTS_MESSAGE_HPP_

#include <idtssdk.h>

#include <string>

namespace mock {
class HdtsTopic;
class HdtsAsyncProducer;

class HdtsMessage : public dtssdk::Message {
  friend class HdtsTopic;
  friend class HdtsAsyncProducer;

 public:
  HdtsMessage() : error_(dtssdk::kMsg_Confirmed) {}
  ~HdtsMessage() override {}

 public:
  void SetState(dtssdk::SysEventCode code_p) { error_ = code_p; }
  dtssdk::SysEventCode State() override { return error_; }

  std::string TopicName() const override { return topic_; }

  void* PayLoad() const override { return const_cast<char*>(pay_load_.c_str()); }

  std::int32_t Len() const override { return pay_load_.size(); }

  std::int64_t Offset() const override { return offset_; }

  void* GetHeadersPtr() override { return const_cast<char*>(headers_.c_str()); }

  std::int32_t GetHeadersLen() override { return headers_.size(); }

  std::int64_t CommitTimeStamp() override { return commit_time_; }

  std::int64_t ConfirmTimeStamp() override { return confirm_time_; }

  void SetMsgOpaque(void* msg_opaque_p) { opaque_ = msg_opaque_p; }
  void* MsgOpaque() const override { return opaque_; }

  bool IsEndMsg() override { return false; }

  std::uint64_t GetBitmapTags() const override { return 0; }

  bool GetIsReliable() const override { return true; }

 private:
  std::string topic_;
  dtssdk::SysEventCode error_;
  std::int64_t offset_;
  std::string pay_load_;
  std::string headers_;
  void* opaque_{nullptr};
  std::int64_t commit_time_;
  std::int64_t confirm_time_;
};
};  // namespace mock

#endif  // TEST_MOCKS_HDTS_MESSAGE_HPP_
