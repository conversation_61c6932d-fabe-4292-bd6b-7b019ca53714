#ifndef TEST_MOCKS_HDTS_SDK_HPP_
#define TEST_MOCKS_HDTS_SDK_HPP_

#include <idtssdk.h>

#include <memory>
#include <string>

#include "async_producer.hpp"  // NOLINT
#include "consumer.hpp"        // NOLINT
#include "topic.hpp"           // NOLINT

namespace mock {
class HdtsSdk : public dtssdk::IDtsSdk {
 public:
  HdtsSdk() = default;
  ~HdtsSdk() override {}

 public:
  dtssdk::IAsyncProducer* GetAsyncProducer() override {
    // 单例在不同的testsuite之间会被反复使用， 需要确保线程还在工作
    // producer_.checkAndStart();

    return &producer_;
  }

  dtssdk::IConsumer* NewConsumer() override { return &consumer_; }

  dtssdk::OffsetResult GetOffSet(std::string& topic_name_p, dtssdk::LogicOffset offset_p, std::int32_t) override {
    dtssdk::OffsetResult result;
    HdtsTopic* topic = HdtsTopicStorage::Instance().GetTopic(topic_name_p);

    if (topic == nullptr) {
      result.offset = -1;
      result.error = dtssdk::kTopicNotExist;

      return result;
    }

    switch (offset_p) {
      case dtssdk::LogicOffset::kMinOffset: {
        result.offset = topic->MinOffset();

        if (result.offset == -1) {
          result.error = dtssdk::kTopicNotExistData;
        } else {
          result.error = dtssdk::kNoError;
        }

        break;
      }
      case dtssdk::LogicOffset::kMaxOffset: {
        result.offset = topic->MaxOffset();

        if (result.offset == -1) {
          result.error = dtssdk::kTopicNotExistData;
        } else {
          result.error = dtssdk::kNoError;
        }

        break;
      }
      default: {
        result.offset = -1;
        result.error = dtssdk::kTopicNotExistData;

        break;
      }
    }

    return result;
  }

  std::int32_t SetParameters(std::string&, std::string&) override { return 0; }
  std::int32_t SetParameters(const std::string&, const std::string&) override { return 0; }

  dtssdk::MessageResult GetMaxMessage(std::string& topic, std::int32_t) override {
    dtssdk::MessageResult result;
    HdtsTopic* t_p = HdtsTopicStorage::Instance().GetTopic(topic);
    if (t_p == nullptr) {
      result.offset = -1;
      result.error = dtssdk::kTopicNotExist;

      return result;
    }
    std::shared_ptr<dtssdk::Message> msg = std::make_shared<HdtsMessage>();
    int64_t max_offset = t_p->MaxOffset();
    t_p->GetMessageFromOffset(max_offset, msg);
    if (msg != nullptr) {
      result.offset = max_offset;
      result.pay_load = std::string(static_cast<char*>(msg->PayLoad()), msg->Len());
      result.error = dtssdk::kNoError;
      result.pay_load_len = msg->Len();
    }
    return result;
  }
  dtssdk::MessageResult GetOneMessage(std::string& topic, int64_t offset, int32_t timeout) override {
    dtssdk::MessageResult result;
    int64_t start =
        std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
            .count();
    while (true) {
      std::this_thread::sleep_for(std::chrono::milliseconds(100));  // 先等3秒然后开始
      int64_t now =
          std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
              .count();
      if (now - start > timeout) {
        result.offset = -1;
        result.error = dtssdk::kTopicNotExist;

        break;
      }
      HdtsTopic* t_p = HdtsTopicStorage::Instance().GetTopic(topic);
      if (t_p == nullptr) {
        result.offset = -1;
        result.error = dtssdk::kTopicNotExist;

        continue;
      }
      std::shared_ptr<dtssdk::Message> msg = std::make_shared<HdtsMessage>();
      t_p->GetMessageFromOffset(offset, msg);
      if (msg != nullptr) {
        result.offset = offset;
        result.pay_load = std::string(static_cast<char*>(msg->PayLoad()), msg->Len());
        result.error = dtssdk::kNoError;
        result.pay_load_len = msg->Len();
        result.headers_len = 0;  // 暂时不提供header返回
        return result;
      }
    }

    return result;
  }
  dtssdk::IConsumeGroup* NewConsumeGroup() override { return nullptr; }

  bool GetParameters(std::string&, std::string&) override { return true; }

  dtssdk::TopicOriginResult SetTopicOffsetOrigin(std::string&, std::int64_t, std::int32_t) override {
    return dtssdk::TopicOriginResult();
  }

  dtssdk::TopicOriginResult GetTopicOffsetOrigin(std::string&, std::int32_t) override {
    return dtssdk::TopicOriginResult();
  }

  void SetAuthInfo(std::string&, std::string&) override {}
  int QuickStopSend() override { return 0; }
  void TriggerMrtWorkerRebalance() override {}

 private:
  HdtsAsyncProducer producer_;
  HdtsConsumer consumer_;
};
};  // namespace mock

#endif  // TEST_MOCKS_HDTS_SDK_HPP_
