#include <benchmark/benchmark.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <algorithm>
#include <bbase/common/decimal/decimal.hpp>
#include <bbase/common/decimal/decimal2.hpp>
#include <boost/multiprecision/cpp_int.hpp>
#include <boost/multiprecision/detail/integer_ops.hpp>
#include <boost/multiprecision/gmp.hpp>
#include <boost/multiprecision/mpfr.hpp>
#include <cmath>
#include <cstdio>
#include <iostream>

#include "src/common/utils.hpp"

class TestInt : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
  static void SetUpTestCase() {}
  static void TearDownTestCase() {}
};

int64_t Func6_5(bool, int n, int64_t a, int64_t b, int64_t c, int64_t d, int64_t e) {
  bbase::decimal::Decimal res(0), tmp1, t_a(a), t_b(b), t_c(c), t_d(d), t_e(e);

  for (int i = 0; i < n; ++i) {
    tmp1 = t_a / t_e;
    tmp1 = tmp1 * t_d;
    res = res + tmp1;
    res = res + t_b * bbase::decimal::Decimal("0.01") * t_c * bbase::decimal::Decimal("0.0001") -
          bbase::decimal::Decimal(123456789);
  }

  return 0;  // int64_t(res);
}

int64_t Func6_6(bool, int n, int64_t a, int64_t b, int64_t c, int64_t d, int64_t e) {
  bbase::decimal::Decimal2 res(0), tmp1, t_a(a), t_b(b), t_c(c), t_d(d), t_e(e);

  for (int i = 0; i < n; ++i) {
    tmp1 = t_a / t_e;
    tmp1 = tmp1 * t_d;
    res = res + tmp1;
    res = res + t_b * bbase::decimal::Decimal2("0.01") * t_c * bbase::decimal::Decimal2("0.0001") -
          bbase::decimal::Decimal2(123456789);
  }

  return 0;  // int64_t(res);
}

std::int64_t Func6_7(bool, int n, int64_t a, int64_t b, int64_t c, int64_t d, int64_t e) {
  boost::multiprecision::number<boost::multiprecision::gmp_float<57>> res(0), tmp1, t_a(a), t_b(b), t_c(c), t_d(d),
      t_e(e);

  for (int i = 0; i < n; ++i) {
    tmp1 = t_a / t_e;
    tmp1 = tmp1 * t_d;
    res = res + tmp1;
    res = res +
          t_b * boost::multiprecision::number<boost::multiprecision::gmp_float<57>>("0.01") * t_c *
              boost::multiprecision::number<boost::multiprecision::gmp_float<57>>("0.0001") -
          boost::multiprecision::number<boost::multiprecision::gmp_float<57>>(123456789);
  }

  return 0;  // int64_t(res);
}

std::int64_t Func6_8(bool, int n, int64_t a, int64_t b, int64_t c, int64_t d, int64_t e) {
  boost::multiprecision::number<boost::multiprecision::mpfr_float_backend<57, boost::multiprecision::allocate_stack>>
      res(0), tmp1, t_a(a), t_b(b), t_c(c), t_d(d), t_e(e);

  for (int i = 0; i < n; ++i) {
    tmp1 = t_a / t_e;
    tmp1 = tmp1 * t_d;
    res = res + tmp1;
    res = res +
          t_b *
              boost::multiprecision::number<
                  boost::multiprecision::mpfr_float_backend<57, boost::multiprecision::allocate_stack>>("0.01") *
              t_c *
              boost::multiprecision::number<
                  boost::multiprecision::mpfr_float_backend<57, boost::multiprecision::allocate_stack>>("0.0001") -
          boost::multiprecision::number<
              boost::multiprecision::mpfr_float_backend<57, boost::multiprecision::allocate_stack>>(123456789);
  }

  return 0;  // int64_t(res);
}
std::int64_t Hash() {
  for (std::uint64_t i = 0; i < 1000000; i++) {
    (void)utils::hash(i);
  }
  return 0;
}

std::int64_t Insert_map_no_reserve() {
  folly::ConcurrentHashMap<std::uint64_t, bool> hash_map;
  for (std::uint64_t i = 0; i < 3'000'000ULL; i++) {
    hash_map.insert_or_assign(i, true);
  }
  return 0;
}

std::int64_t Insert_map_with_reserve() {
  folly::ConcurrentHashMap<std::uint64_t, bool> hash_map_new(3'000'000ULL);
  folly::ConcurrentHashMap<std::uint64_t, bool> hash_map;
  hash_map = std::move(hash_map_new);
  for (std::uint64_t i = 0; i < 3'000'000ULL; i++) {
    hash_map.insert_or_assign(i, true);
  }
  return 0;
}

// static void BM_Func6_5(benchmark::State& state) {
//   for (auto _ : state) {
//     Func6_5(false, 300, 12345678902 + 3, 250800 + 3, 820048, 1700, 500860);
//   }
// }
// BENCHMARK(BM_Func6_5);  // NOLINT

// static void BM_Func6_6(benchmark::State& state) {
//   for (auto _ : state) {
//     Func6_6(false, 300, 12345678902 + 3, 250800 + 3, 820048, 1700, 500860);
//   }
// }
// BENCHMARK(BM_Func6_6);  // NOLINT

// static void BM_Func6_7(benchmark::State& state) {
//   for (auto _ : state) {
//     Func6_7(false, 300, 12345678902 + 3, 250800 + 3, 820048, 1700, 500860);
//   }
// }
// BENCHMARK(BM_Func6_7);  // NOLINT

// static void BM_Func6_8(benchmark::State& state) {
//   for (auto _ : state) {
//     Func6_7(false, 300, 12345678902 + 3, 250800 + 3, 820048, 1700, 500860);
//   }
// }
// BENCHMARK(BM_Func6_8);  // NOLINT

static void BM_insert_map_no_reserve(benchmark::State& state) {
  for (auto _ : state) {
    Insert_map_no_reserve();
  }
}

BENCHMARK(BM_insert_map_no_reserve);  // NOLINT

static void BM_insert_map_with_reserve(benchmark::State& state) {
  for (auto _ : state) {
    Insert_map_with_reserve();
  }
}

BENCHMARK(BM_insert_map_with_reserve);  // NOLINT

static void BM_Hash(benchmark::State& state) {
  for (auto _ : state) {
    Hash();
  }
}

BENCHMARK(BM_Hash);  // NOLINT

int main(int argc, char** argv) {
  ::benchmark::Initialize(&argc, argv);
  if (::benchmark::ReportUnrecognizedArguments(argc, argv)) return 1;
  ::benchmark::RunSpecifiedBenchmarks();
  // testing::InitGoogleTest(&argc, argv);
  // testing::InitGoogleMock(&argc, argv);
  // return RUN_ALL_TESTS();
}
