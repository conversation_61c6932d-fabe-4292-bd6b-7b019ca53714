cmake_minimum_required(VERSION 3.17)

if (NOT CMAKE_TOOLCHAIN_FILE)
    set(VCPKG_INSTALLED_DIR ${CMAKE_CURRENT_SOURCE_DIR}/deps/installed)
    set(VCPKG_MANIFEST_DIR ${CMAKE_CURRENT_SOURCE_DIR}/deps)
    set(VCPKG_TARGET_TRIPLET arm64-osx)
    set(VCPKG_HOST_TRIPLET arm64-osx)
    set(CMAKE_TOOLCHAIN_FILE ${CMAKE_CURRENT_SOURCE_DIR}/deps/vcpkg/scripts/buildsystems/vcpkg.cmake)
endif ()

project("uta_engine" C CXX)

list(APPEND CMAKE_MODULE_PATH ${CMAKE_SOURCE_DIR}/cmake)

include(CMakeMacros)
include(GeneralCMakeOptions)
include(GeneralCMake)
include(GeneralCompiler)
include(IncludeWhatYouUse)
include(Ccache)
include(GitHooks)
include(GitInfo)
include(Sanitizer)
include(TimeTrace)
include(Gperftools)
include(PrintVariable)
include(Je<PERSON>loc)

find_package(bbase CONFIG REQUIRED)

add_subdirectory(${CMAKE_SOURCE_DIR}/proto)
add_subdirectory(${CMAKE_SOURCE_DIR}/src)

if (BUILD_TESTING)
    add_subdirectory(${CMAKE_SOURCE_DIR}/test/benchmark/bigint)
    add_subdirectory(${CMAKE_SOURCE_DIR}/test/benchmark/decimal)
    add_subdirectory(${CMAKE_SOURCE_DIR}/test/benchmark/from_float_str)
    add_subdirectory(${CMAKE_SOURCE_DIR}/test/benchmark/rate_limit)
    add_subdirectory(${CMAKE_SOURCE_DIR}/test/biz)
endif ()

add_subdirectory(${CMAKE_SOURCE_DIR}/tools/tool)
add_subdirectory(${CMAKE_SOURCE_DIR}/tools/nacos_verify)
# add_subdirectory(${CMAKE_SOURCE_DIR}/tools/test/nacos_verify_ut)
